<?xml version="1.0"?>
<phpunit
  backupGlobals="false"
  beStrictAboutOutputDuringTests="true"
  beStrictAboutTestsThatDoNotTestAnything="true"
  bootstrap="tests/phpunit/bootstrap.php"
  colors="true"
  verbose="true"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd">

  <coverage processUncoveredFiles="true">
    <include>
      <directory suffix=".php">legacy/</directory>
      <directory suffix=".php">models/</directory>
    </include>
  </coverage>

  <testsuites>
    <testsuite name="Test Suite">
      <directory>tests/phpunit</directory>
    </testsuite>
  </testsuites>

</phpunit>
