.docStatus {
    text-align: center;
    padding: 0.25em;
}

.chkLigntPink {
    background-color: #fdbd69 !important;
}

.chkLightRed, .chkLightRed td {
    background-color: #fbb5b5 !important;
}

.chkRed, .chkRed td {
    background-color: #fa3434 !important;
    color: #ffffff !important;
}

.chkDarkGrn, .chkDarkGrn td {
    background-color: #a8f9c5 !important;
}

.chkLightGrn {
    background-color: #90ee90 !important;
}

.chkYellow {
    background-color: #fae8c1 !important;
}

.chkWhite {
    background-color: #bff0f7 !important;
}

.chk<PERSON>rey {
    background-color: #cccccc !important;
}

.chkPurp, .chkPurp td {
    background-color: #c11bc1 !important;
    color: #ffffff !important;
}

.box-shadow-css {
    /* box-shadow: 0 1rem 1rem 0rem rgba(0, 0, 0, .3) !important; */
    box-shadow: 0 1rem 1rem -1rem rgba(0, 0, 0, .4) !important;
    /* -webkit-transition: all .3s ease-in-out; */
    transition: all .3s ease-in-out;
}

.hide {
    display: none;
}

.addAsFav {
    /* color: #e6e605 !important;
font-size: 2.5em !important;
    text-decoration: none !important;*/
}

.trhoverNoColor:hover {
    /*background-color: white !important;*/
}

.carousel-multi-item .carousel-indicators .active {
    height: 1.56rem;
    width: 1.56rem;
    max-width: 1.56rem;
    background-color: #4285f4;
    -webkit-border-radius: 50%;
    border-radius: 50%;
}

.popOverManual {
    max-width: 600px !important;
}

.popOverManual > .popover-body {
    /*  max-height: 400px !important;
    overflow-y: scroll !important;*/
}

.popOverManual > .popover-body::-webkit-scrollbar {
    width: 5px;
    border-radius: 16px;
}

/* Track */
.popOverManual > .popover-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

/* Handle */
.popOverManual > .popover-body::-webkit-scrollbar-thumb {
    background: #2a8fc866;
    border-radius: 10px;
}

/* Handle on hover */
.popOverManual > .popover-body::-webkit-scrollbar-thumb:hover {
    background: #0082BB;
}

.scroll.ps > .ps__rail-y {
    width: 10px;
}

.scroll.ps > .ps__rail-y > .ps__thumb-y {
    width: 10px;
}

.scroll.ps > .ps__rail-y > .ps__thumb-y:focus, .scroll.ps > .ps__rail-y > .ps__thumb-y:hover {
    width: 10px;
}

.scrollDiv {
    overflow-y: auto;
    overflow-x: hidden;
}


.bootstrap-select .dropdown-menu.inner > li.selected > a .check-mark {
    top: 40%;
    position: absolute;
    margin-top: -.4rem;
    font-size: .7rem;
    left: 12px;
}

.bootstrap-select.show-tick .dropdown-menu li a span.text {
    margin-left: 24px;
}

.disabledCursor {
    cursor: not-allowed !important;
}

.bootstrap-select li.disabled {
    cursor: not-allowed !important;
}

.upl_doc_file {
    display: -webkit-inline-box;
}

.carousel-indicators li {
    height: 6px;
    background-color: #a8a2a2 !important;
}

.carousel-indicators .active {
    opacity: 20;
}


.offcanvas {
    width: 440px !important;
}

.offcanvas.offcanvas-right {
    right: -440px;
}

.my-4top {
    margin-top: 1rem !important;
}

.card.card-custom > .card-header {
    min-height: 50px !important;
}

.card.card-custom > .card-header .card-title, .card.card-custom > .card-header .card-title .card-label {
    /*font-size: 14px !important;*/
}


/*.datepicker-days .day {
    color: #3f4254 !important;
}*/

.datepicker {
    border: 1px solid #cfcfcf;
}

.datepicker-days td.disabled.day, .datepicker table tr td span.disabled {
    cursor: not-allowed !important;
}


.datepicker tbody tr > td.day.old {
    color: #989797 !important;
}

.datepicker tbody tr > td.day.new {
    color: #989797 !important;
}

.datepicker tbody tr > td.day, .datepicker table tr td span.month {
    color: #3c3f4f !important;
    font-weight: 400;
}

.datepicker table tr td.disabled, .datepicker table tr td.disabled:hover, .datepicker table tr td span.disabled {
    background: none;
    color: #dadbdc !important;
}

/*.pipelineNavigation li.nav-item {
    border-right: 2px solid #f1f1f1;
    border-radius: 5px;
}*/

.LWcustomTable.table-vertical-center th {
    vertical-align: center !important;
}

.LWcustomTable .fa-stack {
    display: inline-block;
    height: 3.2em;
    position: relative;
    vertical-align: middle;
    width: 1.5em;
}

.LWcustomTable th {
    text-transform: uppercase !important;
    /*padding: 2px 4px !important;*/
}

.LWcustomTable td {
    padding: 0px 4px !important;
}

.table-disable-hover.table tbody tr:hover {
    background-color: transparent;
}

.optnGrp {
    display: list-item !important;
    cursor: default;
    padding-left: .55rem !important;
    color: #99999a !important;
    /* font-weight: normal !important; */
    text-transform: uppercase;
    background-color: #f6f6f6;
    font-weight: 500 !important;
}

.table.table-hover-td td:hover {
    background-color: #f2f2f2 !important;
}

.aside-menu .menu-nav > .menu-item.menu-item-active > .menu-heading, .aside-menu .menu-nav > .menu-item.menu-item-active > .menu-link {
    background-color: #3699ff !important
}

.aside-menu .menu-nav > .menu-item.menu-item-open > .menu-link > .menu-icon > i, .aside-menu .menu-nav > .menu-item.menu-item-active > .menu-link > .menu-icon > i {
    color: white;
}

.aside-menu .menu-nav > .menu-item:not(.menu-item-active).menu-item-open > .menu-link {
    /*   background-color: #3699ff !important*/
    /*   background-color: #3699ff !important*/
    transition: background-color .9s;
}

.aside-menu .menu-nav > .menu-item .menu-submenu .menu-item.menu-item-active > .menu-link {
    background-color: rgba(54, 153, 255, .3) !important;
}

/*.aside-menu .menu-nav > .menu-item:not(.menu-item-active).menu-item-open > .menu-link:hover {
    !*   background-color: #3699ff !important*!
    !*   background-color: #3699ff !important*!
    background-color:  red !important;
}*/


@media (min-width: 992px) {
    .aside-minimize:not(.aside-minimize-hover) .aside-menu .menu-nav > .menu-item.menu-item-active {
        background-color: #3699ff !important;
    }
}

@media only screen and (max-width: 600px) {
    .d-mobile-none {
        display: none;
    }
}

.pipelineNavigation .nav-pills .nav-link {
    border: 1px solid #ebedf3;
    /*border-color: #ebedf3 #ebedf3 #e4e6ef;*/
}

.pipelineNavigation .nav.nav-pills .nav-item {
    margin-right: 0px !important;
    cursor: pointer;
    /*border-color: #ebedf3 #ebedf3 #e4e6ef;*/
}

.wizard-wrapper {
    cursor: pointer;
}

.nav.nav-pills .nav-link {
    color: #3f4254;
    font-weight: 500;
}

.dataTables_scrollBody {
    height: auto !important;
}

.dropdown-menu-arrow {
    position: absolute;
    Right: 6px;
    top: calc(50% - 2px);
}

.dropdown-menu-arrow:before,
.dropdown-menu-arrow:after {
    content: "";
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-width: 7px 8px;
    border-style: solid;
    border-color: transparent;
    z-index: 1001;
}

.dropdown-menu-arrow:after {
    bottom: -18px;
    right: -8px;
    border-bottom-color: #fff;
}

.dropdown-menu-arrow:before {
    bottom: -17px;
    right: -8px;
    border-bottom-color: rgba(0, 0, 0, .15);
}


#status-bar {
    /*  line-height: 1.167em;*/
    -webkit-background-size: 100% 100%;
    -moz-background-size: 100% 100%;
    -o-background-size: 100% 100%;
    background-size: 100% 100%;
    text-align: right;
    color: #7b7b7b;
}

.status-infos {
    margin-bottom: 0;
}

.status-infos > .lireplace {
    position: relative;
}

.status-infos .result-block {
    display: none;
}

.result-block {
    width: 800px;
    /* overflow: auto;
     margin-top: 20px;*/
}


/*.result-block .result-info {
    !*   background: #333333;*!
    !*    color: white;*!
    padding: 0.417em 0.75em 0.583em;
    margin: -1em -1em -1em -1em;
    -moz-border-radius: 0.8em 0.8em 0 0;
    -webkit-border-bottom-right-radius: 0.8em;
    -webkit-border-bottom-left-radius: 0.8em;
    border-radius: 0.8em 0.8em 0 0;
    white-space: nowrap;
}*/

.result-block {
    position: absolute;
    z-index: 99990;
    min-width: 20em;
    /*   background: white;*/
    /*    -moz-border-radius: 1em;
        -webkit-border-radius: 1em;
        -webkit-background-clip: padding-box;
        border-radius: 1em;
        -moz-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
        -webkit-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);*/
    padding: 1em;
    /* line-height: 1em;*/
    text-align: left;
    /*    color: #333333;*/
    display: inline-block;
    /*  border: 1px solid #333333;*/

    font-family: Poppins, Helvetica, sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: .9rem;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #fff;
    border-radius: .42rem;
    -webkit-box-shadow: 0 0 20px 0 rgba(0, 0, 0, .15);
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, .15);

}


.result-block .arrow {
    font-size: 0;
    line-height: 0;
    width: 0;
    position: absolute;
    z-index: 89;
    /*    right: 0px;*/
    top: -15px;
    border-bottom: 18px solid white;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;

}

.form-control[readonly] {
    background-color: #f7f5f5;
    cursor: not-allowed;
}

.form-control:disabled {
    cursor: not-allowed;
}

a.clicktocall {
    display: table;
}

.dataTables_scrollBody::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    border-radius: 16px;
}

/* Track */
.dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

/* Handle */
.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: #2a8fc866;
    border-radius: 10px;
}

/* Handle on hover */
.dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background: #888;
}

/* Card Scroll Bar */
.card-scroll::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    border-radius: 16px;
}

/* Track */
.card-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

/* Handle */
.card-scroll::-webkit-scrollbar-thumb {
    background: #2a8fc866;
    border-radius: 10px;
}

/* Handle on hover */
.card-scroll::-webkit-scrollbar-thumb:hover {
    background: #888;
}

/* end of Scroll Bar */


.brand-logo > img {
    max-height: 65px;
    max-width: 235px;
}


/* Copied from newhtml.css */
.columnHeadingNew, .bbr {
    border-right: 1px solid #ffffff;
}

.tCenter {
    text-align: center;
}

.subSecAc {
    /* display: block;*/
}

.subSecDa {
    display: none !important;
}

.secHide {
    display: none !important;
}

/*.secShow {
    display: block;
}*/

.actDisp {
    display: none;
}

.card.card-custom {
    border: 1px solid #ebedf3;
    box-shadow: 0 1rem 1rem -1.1rem rgba(0, 0, 0, .2) !important;
}

.mandatory, .brokerMandatoryField {
    background-color: #ffa80024 !important
}

.hidden {
    display: none;
}

/*form#loanModForm > div.card > div.card-body {
    display: none;
}*/

/*.toggleClass {
    display: none !important;
}*/

/* End of Copied from newhtml.css */


/*
.chosen-container .chosen-results li.group-result {
    padding-left: 0.95rem !important;
}

.checkbox > span, .checkbox.checkbox-outline > span {
    background-color: #ffffff !important;
    border: 1px solid #cdcbcb !important;
}
*/
.datepicker > div {
    display: inherit;
}

div.dataTables_wrapper div.dataTables_info {
    padding-top: 0 !important;
}

.btn.btn-text {
    cursor: text;
    padding: 0.40rem .75rem !IMPORTANT;
}

.dropzone {
    border: 0px !important;
}

.divMandatory {
    width: 20px;
    height: 20px;
    background-color: #ffcc99;
    float: left;
    border: 1px solid #ffcc99;
}

.scrollSave {
    /* display: none;*/
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: fixed;
    bottom: 140px;
    right: 20px;
    cursor: pointer;
    z-index: 100;
    -webkit-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, .075);
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, .075);
    -webkit-transition: color .15s ease, background-color .15s ease, border-color .15s ease, -webkit-box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, -webkit-box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, box-shadow .15s ease, -webkit-box-shadow .15s ease;
    border-radius: .42rem !important;
}


.scrollBottom {
    /* display: none;*/
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 36px;
    height: 36px;
    position: fixed;
    bottom: 73px;
    right: 20px;
    cursor: pointer;
    z-index: 100;
    background-color: #3699ff;
    -webkit-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, .075);
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, .075);
    opacity: 0;
    -webkit-transition: color .15s ease, background-color .15s ease, border-color .15s ease, -webkit-box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, -webkit-box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, box-shadow .15s ease, -webkit-box-shadow .15s ease;
    border-radius: .42rem !important;
}

[data-scrollBottom=on] .scrollBottom {
    opacity: .3;
    -webkit-animation: animation-scrolltop .4s ease-out 1;
    animation: animation-scrolltop .4s ease-out 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.scrollBottom:hover {
    -webkit-transition: color .15s ease, background-color .15s ease, border-color .15s ease, -webkit-box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, -webkit-box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, box-shadow .15s ease;
    transition: color .15s ease, background-color .15s ease, border-color .15s ease, box-shadow .15s ease, -webkit-box-shadow .15s ease;
    opacity: 1;
}

.scrolltop {
    bottom: 116px !important;
}

.scrollBottom .svg-icon svg {
    height: 24px;
    width: 24px;
}

@media (max-width: 991.98px) {
    .scrollBottom {
        width: 30px;
        height: 30px;
        right: 15px;
    }
}

tr[class*="WorkFlowhideForAdditionalLogic"] {
    display: none !important;
}

.tableAlternative tr:nth-child(even) {
    background-color: #f2f2f2;
}

/*Loan Terms*/
.bg-light-green {
    background-color: #C6D9B7 !important;
}

.bg-light-green i {
    color: #3f4254 !important;
}

/* radio mandatory */
.radio > input.mandatory ~ span {
    background: #ffa80024;
}

.radio > input:checked ~ span {
    background: #3699FF !important;;
}

/* checkbox mandatory */
.checkbox > input.mandatory ~ span {
    border: 1px solid #f64e60 !important;
}

/* select multiple mandatory */
.js-example-basic-multiple.mandatory ~ span .select2-selection.select2-selection--multiple {
    background-color: antiquewhite;
}

/*webforms hide icon*/

.navbar-toggler {
    display: none;
}

/* Mobile Responsive code */
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) {
    #showHidColumnsDropdown1, #showHidColumnsDropdown2 {
        width: 390px !important;
    }
}

/* Medium Devices, Desktops */
@media only screen and (min-width: 992px) {

    #showHidColumnsDropdown1, #showHidColumnsDropdown2 {
        width: 650px !important;
    }
}

div.dataTables_wrapper div.dataTables_filter input {
    /*    width: 100% !important; -- this makes the search box for data tables go off the page */
}

.picker-dialog {
    z-index: 1051 !important;
}

.loanFileButton {
    border: 0px solid transparent;
    padding: 0;
}

.loanFileButtonLink {
    padding: 3px 9px !important;
}

.radio-inline {
    padding-top: 8px;
}


/* jquery chosen fix for default text in multiselects.  you need <option value=""></option> as first option to use properly */
li.search-field input.default {
    width: 100% !important;
}

.mandatory > a.chosen-single {
    background-color: #ffa80000 !important;
}

.mandatory > a.chosen-single > div {
    background: none;
}


.alternativeColor:nth-of-type(odd) {
    background-color: #f3f6f9;
}

.alternativeColor:nth-of-type(even) {
    background-color: #fff;
}

#daveSocketContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #3699ff;
}

#daveSocketContainer [data-letters]:before {
    content: attr(data-letters);
    display: inline-block;
    font-size: 1em;
    width: 2.5em;
    height: 2.5em;
    line-height: 2.5em;
    text-align: center;
    border-radius: 50%;
    background: #3699ff;
    vertical-align: middle;
    margin-right: 1em;
    color: white;
    animation: pulsate 1s ease-out infinite;
}


@-webkit-keyframes pulsate {
    0% {
        box-shadow: 0 0 0 black;
    }
    50% {
        box-shadow: 0 0 10px black;
    }
    100% {
        box-shadow: 0 0 0 black;
    }
}

/*.select2-selection__clear {*/
/*    display: none !important;*/
/*}*/

.select2-search__field {
    width: 100% !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove:before {
    color: red;
}

.highlightField {
    background: url("/assetsNew/media/20x20small.gif") no-repeat 80% 50%;
    border: 1px solid #ee2d41;
    background-color: rgba(246, 78, 96, .12) !important;
}

.showDisabledNotification {
    color: red;
    font-weight: bolder;
    background-color: rgba(246, 78, 96, .08) !important;
}

.align-right {
    text-align: right;
}

.not-allowed {
    cursor: not-allowed;
}

.bootstrap-select > .dropdown-toggle {
    padding: 6px 8px !important;
}

.bootstrap-select > .dropdown-toggle.btn-light .filter-option, .bootstrap-select > .dropdown-toggle.btn-secondary .filter-option {
    color: #c3c5d1;
}

.bootstrap-select .dropdown-menu.inner li a {
    white-space: normal !important; /* Allow text wrapping */
    word-wrap: break-word; /* Ensure long words break */
    display: block;
    overflow-wrap: break-word;
    text-overflow: clip;
    padding: 5px; /* Adjust padding for better spacing */
}

.bootstrap-select .dropdown-menu.inner > li.dropdown-header {
    font-weight: bold;
    color: black;
}
.bootstrap-select .dropdown-toggle:not(.bs-placeholder) .filter-option-inner-inner {
    color: #35353c;
}
