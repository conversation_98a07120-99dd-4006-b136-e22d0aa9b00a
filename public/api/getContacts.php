<?php


use models\composite\oContacts\getContactList;
use models\Request;
use models\standard\HTTP;

session_start();
require __DIR__ . '/../includes/util.php';

$result = null;
$searchTerm = $_REQUEST['query'] ? Request::GetClean('query') : null;
$CTypeID = $_REQUEST['CTypeID'] ? Request::GetClean('CTypeID') : null;
$PCID = ($_REQUEST['PCID'] ? Request::GetClean('PCID') : null);
if (!$PCID) {
    HTTP::ExitJSON(['success' => 'No PCID']);
}
$contactList = getContactList::getReport([
    'searchTerm' => $searchTerm,
    'PCID' => $PCID,
    'CTypeID' => $CTypeID,
   // 'userNumber' => '1',
]);
$nameStr = [];
$data = [];
foreach ($contactList['contacts'] ?? [] as $contact) {
    $result[] = [
        'label' => $contact['contactName'] . ' ' . $contact['contactLName'] . ' (' . $contact['email'] . ')',
        'value' => $contact['CID'],
    ];
    $labels[] = $contact['contactName'] . ' ' . $contact['contactLName'] . ' (' . $contact['email'] . ')';
    $data[] = $contact['CID'];
}
HTTP::ExitJSON([
    'query' => $searchTerm,
    'data' => $data,
    'suggestions' => $labels,
]);
