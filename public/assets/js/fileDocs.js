function getSelectedPKGID() {
    console.log({
        func: 'getSelectedPKGID',
    });

    let _loanModForm = $('#loanModForm');

    let selectedPKGIDs = [];
    _loanModForm.find('.signable').each(function () {
        if ($(this).is(':checked')) {
            selectedPKGIDs.push($(this).val());
        }
    });
    _loanModForm.find('.unsignable').each(function () {
        if ($(this).is(':checked')) {
            selectedPKGIDs.push($(this).val());
        }
    });
    _loanModForm.find('#selectedPkg').val(selectedPKGIDs.join(','));
}

function getSelectedAttachedPKGID() {
    console.log({
        func: 'getSelectedAttachedPKGID',
    });

    let _loanModForm = $('#loanModForm');

    let pkgIds = [];
    _loanModForm.find('.attachable').each(function () {
        if ($(this).is(':checked')) {
            pkgIds.push($(this).val());
        }
    });
    _loanModForm.find('#selectedAttachedPkg').val(pkgIds.join(','));
}

/* Creditor Docs */

function getCreditorEsignPKGID(x, chkAction, pkgValue, CRID, LMRId) {
    console.log({
        func: 'getCreditorEsignPKGID',
    });

    var pkgCnt = 0, pkgId = '', j = 0;
    pkgCnt = document.loanModForm.pkgCnt.value;
    var s = 0, i = 0, crPkgId = '';
    eval("obj = document.getElementsByName('creditorPKG[]')");
    for (var i = 0; i < obj.length; i++) {
        if (obj[i].checked) {
            if (s > 0) {
                crPkgId += ", ";
            }
            crPkgId += obj[i].value;
            s++;
        }
    }
    if (i == 0) {
        eval("obj = document.getElementsByName('creditorPKG[]')");
        if (obj.checked) {
            crPkgId = obj.value;
        }
    }
    document.loanModForm.creditorEsignPkg.value = crPkgId;

    if (chkAction) {
        HTTP.Post('/backoffice/api_v2/checkPkgEsigned', {
            pkgValue: pkgValue,
            LMRId: LMRId,
            CRID: CRID,
        }, function (data) {
            let pkgEsigned = parseInt(data.pkgEsigned);
            let msg = data.msg;

            if (pkgEsigned === 1) {
                $.confirm({
                    icon: 'flaticon-file icon-2x text-danger',
                    closeIcon: true,
                    title: 'Confirm',
                    content: msg,
                    type: 'red',
                    backgroundDismiss: false,
                    buttons: {
                        yes: {
                            btnClass: 'btn-green',
                            action: function () {

                            }
                        },
                        cancel: {
                            text: 'No',
                            action: function () {
                                x.checked = false;
                                getCreditorEsignPKGID(false, pkgValue, CRID, LMRId);
                            }
                        },
                    }
                });
            }
        });
    }
}


function getCreditorAttachedPKGID(chk) {
    console.log({
        func: 'getCreditorAttachedPKGID',
    });

    var pkgCnt = 0, pkgId = '', j = 0;
    pkgCnt = document.loanModForm.pkgCnt.value;
    var k = 0, a = 0, crePkgId = '';
    eval("obj = document.getElementsByName('creditorAttachPKG[]')");
    for (var a = 0; a < obj.length; a++) {
        if (obj[a].checked) {
            if (k > 0) {
                crePkgId += ", ";
            }
            crePkgId += obj[a].value;
            k++;
        }
    }
    if (a == 0) {
        eval("obj = document.getElementsByName('creditorAttachPKG[]')");
        if (obj.checked) {
            crePkgId = obj.value;
        }
    }
    document.loanModForm.creditorAttachedPkg.value = crePkgId;
}


/*
 * Get selected branch docs id
 */
function getBranchSelectedPKGID() {
    console.log({
        func: 'getBranchSelectedPKGID',
    });

    let _loanModForm = $('#loanModForm');

    let pkgIds = [];
    _loanModForm.find('.branchAttachable').each(function () {
        let checked = $(this).is(':checked');
        let filesize = $(this).data('filesize');
        GetFileSizeDoc(filesize, checked);
        if (checked) {
            pkgIds.push($(this).val());
        }
    });
    _loanModForm.find('#branchSelectedPkg').val(pkgIds.join(','));
}

/*
 * Get selected PC docs id
 */
function getPCSelectedPKGID() {
    console.log({
        func: 'getPCSelectedPKGID',
    });

    let _loanModForm = $('#loanModForm');

    let pkgIds = [];
    _loanModForm.find('.pcdocAttachable').each(function () {
        let checked = $(this).is(':checked');
        let filesize = $(this).data('filesize');
        GetFileSizeDoc(filesize, checked);
        if (checked) {
            pkgIds.push($(this).val());
        }
    });
    console.log(pkgIds);
    _loanModForm.find('#PCSelectedPkg').val(pkgIds.join(','));
}

function getEsignSelectedPKGID(newDocId, fileSize, checkVal) {
    console.log({
        func: 'getEsignSelectedPKGID',
    });

    if (GetFileSizeDoc(fileSize, checkVal)) {
        var eSignDocCnt = 0, j = 0, i = 0, ePkgId = '';
        eval("obj = document.getElementsByName('esignDoc[]')");

        for (var k = 0; k < obj.length; k++) {

            if (obj[k].checked) {
                if (j > 0) {
                    ePkgId += ", ";
                }
                ePkgId += obj[k].value;
                j++;
            }
        }
        if (k == 0) {
            eval("obj = document.getElementsByName('esignDoc[]')");
            if (obj.checked) {
                ePkgId = obj.value;
            }
        }
        document.loanModForm.esignSelectedPkg.value = ePkgId;
    } else {
        return false;
    }
}

function getGoogleDocumentsSelectedPKGID() {
    console.log({
        func: 'getGoogleDocumentsSelectedPKGID',
    });

    let _loanModForm = $('#loanModForm');

    let pkgIds = [];
    _loanModForm.find('.googleDocument').each(function () {
        if ($(this).is(':checked')) {
            pkgIds.push($(this).val());
        }
    });
    _loanModForm.find('#customSelectedPkg').val(pkgIds.join(','));
}

function getCustomGoogleSheetSelectedPKGID() {
    console.log({
        func: 'getCustomGoogleSheetSelectedPKGID',
    });

    let _loanModForm = $('#loanModForm');

    let pkgIds = [];
    _loanModForm.find('.googleSheet').each(function () {
        if ($(this).is(':checked')) {
            pkgIds.push($(this).val());
        }
    });
    _loanModForm.find('#customWordSelectedPkg').val(pkgIds.join(','));
}


function GetFileSizeDoc(fileSize, checkVal) {
    console.log({
        func: 'GetFileSizeDoc',
    });

    var totalFileSize = 0, tot = 0;

    totalFileSize = document.loanModForm.totFileSize.value;

    if (checkVal) {
        tot = parseFloat(totalFileSize) + parseFloat(fileSize);
    } else {
        tot = parseFloat(totalFileSize) - parseFloat(fileSize);
    }

    document.loanModForm.totFileSize.value = tot;
    return true;
}

function getSelectedUpDoc(newDocId, fileSize, checkVal) {
    console.log({
        func: 'getSelectedUpDoc',
    });

    if (GetFileSizeDoc(fileSize, checkVal)) {
        var s = 0, d = 0, upDocId = '';
        eval("obj = document.getElementsByName('upDoc[]')");

        for (var s = 0; s < obj.length; s++) {

            if (obj[s].checked) {
                if (d > 0) {
                    upDocId += ", ";
                }
                upDocId += obj[s].value;
                d++;
            }
        }
        if (s == 0) {
            eval("obj = document.getElementsByName('upDoc[]')");

            if (obj.checked) {
                upDocId = obj.value;
            }
        }
        document.loanModForm.selectedUpDoc.value = upDocId;
    } else {
        return false;
    }

}


function getBinderSelectedPKGID(newDocId, fileSize, checkVal) {
    console.log({
        func: 'getBinderSelectedPKGID',
    });

    if (GetFileSizeDoc(fileSize, checkVal)) {
        var eSignDocCnt = 0, j = 0, h = 0, biPkgId = '';
        eval("obj = document.getElementsByName('binderDoc[]')");

        for (var h = 0; h < obj.length; h++) {

            if (obj[h].checked) {
                if (j > 0) {
                    biPkgId += ", ";
                }
                biPkgId += obj[h].value;
                j++;
            }
        }
        if (h == 0) {
            eval("obj = document.getElementsByName('binderDoc[]')");
            if (obj.checked) {
                biPkgId = obj.value;
            }
        }
        document.loanModForm.binderSelectedPkg.value = biPkgId;
    } else {
        return false;
    }
}

class fileDocs {

    static buildCheckBoxForSignUsers(signUsers, LMRId, pkgID) {

        console.log({
            func: 'buildCheckBoxForSignUsers'
        });
        console.log(signUsers);

        $('.docsSignUsers' + pkgID).html('');

        $.each(signUsers, function (index, user) {
            let checkbox = $('<input>', {
                type: 'checkbox',
                name: 'signUsers[' + pkgID + '][]',
                value: user,
                class: 'form-control signUsers signUsers_' + pkgID,
                id: 'checkbox_' + pkgID + '_' + index
            });
            let span = $('<span>');
            // Create the label element
            let label = $('<label>', {
                for: 'checkbox_' + pkgID + '_' + index,
                class: 'checkbox',
            }).append(checkbox, span, user);
            // Create the container div
            $('.docsSignUsers' + pkgID).append(label);
        });
    }

    static checkIfESigned(chkAction, pkgValue, pkgID, LMRId) {
        console.log({
            func: 'checkIfESigned',
        });

        let msg = '', pkgEsigned = 0;

        if (chkAction) {

            $('.docsSignUsers' + pkgValue).html('<img src="/assets/images/ajax-loader.gif" alt="">');

            HTTP.Post("/backoffice/api_v2/checkPkgEsigned", {
                pkgValue: pkgValue,
                LMRId: LMRId,
                json: 1,
            }, function (data) {

                let pkgEsigned = data.hasOwnProperty('pkgEsigned') ? data.pkgEsigned : null;
                let msg = data.hasOwnProperty('msg') ? data.msg : null;
                let docsSignUsers = data.hasOwnProperty('signUsers') ? data.signUsers : null;

                if (parseInt(pkgEsigned) === 1) {
                    $.confirm({
                        icon: 'flaticon-file icon-2x text-danger',
                        closeIcon: true,
                        title: 'Confirm',
                        content: msg,
                        type: 'red',
                        backgroundDismiss: false,
                        buttons: {
                            yes: {
                                btnClass: 'btn-green',
                                action: function () {
                                    fileDocs.buildCheckBoxForSignUsers(docsSignUsers, LMRId, pkgValue);
                                }
                            },
                            cancel: {
                                text: 'No',
                                action: function () {
                                    cancelDocSend(pkgID);
                                }
                            },
                        }
                    });
                } else {
                    fileDocs.buildCheckBoxForSignUsers(docsSignUsers, LMRId, pkgValue);
                }
            });

/*


            var url = "../backoffice/checkPkgEsigned.php";
            var qstr1 = "pkgValue=" + pkgValue + "&LMRId=" + LMRId;
            try {
                xmlDoc = getXMLDoc(url, qstr1);
            } catch (e) {
            }
            try {
                pkgEsigned = xmlDoc.getElementsByTagName("pkgEsigned")[0].firstChild.nodeValue;
            } catch (e) {
            }
            try {
                msg = xmlDoc.getElementsByTagName("msg")[0].firstChild.nodeValue;
            } catch (e) {
            }
            if (pkgEsigned == 1) {
                $.confirm({
                    icon: 'flaticon-file icon-2x text-danger',
                    closeIcon: true,
                    title: 'Confirm',
                    content: msg,
                    type: 'red',
                    backgroundDismiss: false,
                    buttons: {
                        yes: {
                            btnClass: 'btn-green',
                            action: function () {

                            }
                        },
                        cancel: {
                            text: 'No',
                            action: function () {
                                cancelDocSend(pkgID);
                            }
                        },
                    }
                });

            }*/
        }
    }

}

function checkIfESigned(chkAction, pkgValue, pkgID, LMRId) {
    console.log({
        func: 'checkIfESigned',
    });

    if (chkAction) {
        HTTP.Post('/backoffice/api_v2/checkPkgEsigned', {
            pkgValue: pkgValue,
            LMRId: LMRId,
        }, function (data) {

            let pkgEsigned = parseInt(data.pkgEsigned);
            let msg = data.msg;

            if (pkgEsigned === 1) {
                $.confirm({
                    icon: 'flaticon-file icon-2x text-danger',
                    closeIcon: true,
                    title: 'Confirm',
                    content: msg,
                    type: 'red',
                    backgroundDismiss: false,
                    buttons: {
                        yes: {
                            btnClass: 'btn-green',
                            action: function () {

                            }
                        },
                        cancel: {
                            text: 'No',
                            action: function () {
                                cancelDocSend(pkgID);
                            }
                        },
                    }
                });

            }
        });
    }
}

function cancelDocSend(pkgValueId) {
    console.log({
        func: 'cancelDocSend',
    });

    eval("document.loanModForm." + pkgValueId + ".checked = false");
    getSelectedPKGID();
}

function resendEsignLink(txnID, LMRId, selPKGID, packageName) {
    console.log({
        func: 'resendEsignLink',
    });

    var encryptedPCID = '', encryptedEId = '';

    if (confirm("Would you like to resend the link for the \"" + packageName + "\" document?")) {

        encryptedPCID = document.loanModForm.encryptedPCID.value;
        encryptedEId = document.loanModForm.encryptedEId.value;

        qstr = 'LMRId=' + LMRId + '&resendTxnID=' + txnID + '&PCID=' + encryptedPCID + '&executiveId=' + encryptedEId + '&selRPKGID=' + selPKGID + '&faxPop=0';

        eval("ContactPop.showOverlay('" + POPSURL + "docsSend.php')"); /** Open Popup **/
    }


}

function getAllPackages(opt) {
    console.log({
        func: 'getAllPackages',
    });

    if (!opt) opt = '';
    var AEUserType = "", pkgCnt = 0, empCnt = 0, pkg = 0, pkg1 = 0, selectedPkg = "", selectedAttPkg = '';
    pkg_lien1 = false;
    var docCnt = 0, dd = 0, selectedDoc = "", chkLienOpt = false, pkg_profit = false;
    var branchDoc = 0, branchDocLen = 0, docVal = "", docValues = "", doc1 = 0, ePkgCnt = 0;
    var epkg = 0, eselectedPkg = "";
    var docValues2 = "", userDocLen = 0, doc3 = 0;
    var docValues3 = "", custDocLen = 0, doc5 = 0;
    creditorDoc = '';
    var docValues4 = "", eCustDocLen = 0, doc7 = 0, eDocLen = 0, upDocLen = 0, brDocLen = 0, binderDocLen = 0,
        cusDocLen = 0, PCDocLen = 0;
    var binderDocLen = "", docLen = "", chk = "", binDoc = 0, cusDoc = '', upDoc = '', eDoc = '', brDoc = '',
        PCDocCnt = 0, PCDoc = '', creditorsDoc = '';

    try {
        branchDocCnt = document.loanModForm.branchDocCnt.value;
        branchDocCnt = parseFloat(branchDocCnt);
    } catch (e) {
    }
    try {
        pkgCnt = document.loanModForm.pkgCnt.value;
        pkgCnt = parseFloat(pkgCnt);
    } catch (e) {
    }

    try {
        binderCnt = document.loanModForm.binderCnt.value;
        binderCnt = parseFloat(binderCnt);
    } catch (e) {
    }

    try {
        epkgCnt = document.loanModForm.epkgCnt.value;
        epkgCnt = parseFloat(epkgCnt);
    } catch (e) {
    }
    try {
        esignCnt = document.loanModForm.esignCnt.value;
        esignCnt = parseFloat(esignCnt);
    } catch (e) {
    }

    try {
        custDocCnt = document.loanModForm.custDocCnt.value;
        custDocCnt = parseFloat(custDocCnt);
    } catch (e) {
    }

    try {
        faxPkgCnt = document.loanModForm.faxPkgCnt.value;
        faxPkgCnt = parseFloat(faxPkgCnt);
    } catch (e) {
    }
    try {
        PCDocCnt = document.loanModForm.PCDocCnt.value;
        PCDocCnt = parseFloat(PCDocCnt);
    } catch (e) {
    }


    for (var p = 0; p < pkgCnt; p++) {
        var chk = false, pkgId = 0, pkg_title = "";
        try {
            eval("chk = document.loanModForm.pkg_" + p + ".checked");
        } catch (e) {
        }
        if (chk) {
            try {
                eval("pkgId = document.loanModForm.pkg_" + p + ".value");
            } catch (e) {
            }
            try {
                eval("pkg_title = document.loanModForm.pkg_" + p + ".title");
            } catch (e) {
            }
            if (pkg > 0) {
                selectedPkg += ",";
            }
            selectedPkg += pkgId;
            pkg++;
        }
    }


    for (var p = 0; p < pkgCnt; p++) {
        var chk = false, pkgId1 = 0, pkg_title = "";
        try {
            eval("chk = document.loanModForm.pkg1_" + p + ".checked");
        } catch (e) {
        }
        if (chk) {
            try {
                eval("pkgId1 = document.loanModForm.pkg1_" + p + ".value");
            } catch (e) {
            }
            if (pkg > 0) {
                selectedAttPkg += ",";
            }
            selectedAttPkg += pkgId1;
            pkg++;
        }
    }


    try {
        eval("docLen1 = document.loanModForm['customDoc[]']");
        cusDocLen = docLen1.length;
    } catch (e) {
    }
    if (cusDocLen > 0) {
        for (var j1 = 0; j1 < docLen1.length; j1++) {
            if (docLen1[j1].checked) {
                cusDoc++;
            }
        }
    } else {
        try {
            chk = docLen1.checked;
        } catch (e) {
        }
        if (chk) {
            cusDoc++;
        }
    }
    var docLen11, cusWordDocLen = 0, cusWordDoc = 0;
    try {
        eval("docLen11 = document.loanModForm['customWordDoc[]']");
        cusWordDocLen = docLen11.length;
    } catch (e) {
    }
    if (cusWordDocLen > 0) {
        for (var j1 = 0; j1 < docLen11.length; j1++) {
            if (docLen11[j1].checked) {
                cusWordDoc++;
            }
        }
    } else {
        try {
            chk = docLen11.checked;
        } catch (e) {
        }
        if (chk) {
            cusWordDoc++;
        }
    }
    try {
        eval("docLen2 = document.loanModForm['esignDoc[]']");
        eDocLen = docLen2.length;
    } catch (e) {
    }
    if (eDocLen > 0) {
        for (var j1 = 0; j1 < docLen2.length; j1++) {
            if (docLen2[j1].checked) {
                eDoc++;
            }
        }
    } else {
        try {
            chk = docLen2.checked;
        } catch (e) {
        }
        if (chk) {
            eDoc++;
        }
    }


    try {
        eval("docLen3 = document.loanModForm['upDoc[]']");
        upDocLen = docLen3.length;
    } catch (e) {
    }
    if (upDocLen > 0) {
        for (var j1 = 0; j1 < docLen3.length; j1++) {
            if (docLen3[j1].checked) {
                upDoc++;
            }
        }
    } else {
        try {
            chk = docLen3.checked;
        } catch (e) {
        }
        if (chk) {
            upDoc++;
        }
    }

    try {
        eval("docLen4 = document.loanModForm['branchDoc[]']");
        brDocLen = docLen4.length;
    } catch (e) {
    }
    if (brDocLen > 0) {
        for (var j1 = 0; j1 < docLen4.length; j1++) {
            if (docLen4[j1].checked) {
                brDoc++;
            }
        }
    } else {
        try {
            chk = docLen4.checked;
        } catch (e) {
        }
        if (chk) {
            brDoc++;
        }
    }

    try {
        eval("docLen5 = document.loanModForm['PCDoc[]']");
        PCDocLen = docLen5.length;
    } catch (e) {
    }
    if (PCDocLen > 0) {
        for (var j1 = 0; j1 < docLen5.length; j1++) {
            if (docLen5[j1].checked) {
                PCDoc++;
            }
        }
    } else {
        try {
            chk = docLen5.checked;
        } catch (e) {
        }
        if (chk) {
            PCDoc++;
        }
    }


    try {
        eval("docLen = document.loanModForm['binderDoc[]']");
        binderDocLen = docLen.length;
    } catch (e) {
    }
    if (binderDocLen > 0) {
        for (var j1 = 0; j1 < docLen.length; j1++) {
            if (docLen[j1].checked) {
                binDoc++;
            }
        }
    } else {
        try {
            chk = docLen.checked;
        } catch (e) {
        }
        if (chk) {
            binDoc++;
        }
    }

    var creditorDocs = 0;
    try {
        eval("creditor = document.loanModForm['creditorPKG[]']");
        creditorDocs = creditor.length;
    } catch (e) {
    }
    if (creditorDocs > 0) {
        for (var j = 0; j < creditor.length; j++) {
            if (creditor[j].checked) {
                creditorDoc++;
            }
        }
    } else {
        try {
            chk = creditor.checked;
        } catch (e) {
        }
        if (chk) {
            creditorDoc++;
        }
    }

    try {
        eval("creditors = document.loanModForm['creditorAttachPKG[]']");
        creditorDocs = creditors.length;
    } catch (e) {
    }
    if (creditorDocs > 0) {
        for (var j = 0; j < creditors.length; j++) {
            if (creditors[j].checked) {
                creditorsDoc++;
            }
        }
    } else {
        try {
            chk = creditors.checked;
        } catch (e) {
        }
        if (chk) {
            creditorsDoc++;
        }
    }
    if (((selectedPkg == 0) || (selectedPkg == "")) &&

        ((selectedAttPkg == 0) || (selectedAttPkg == "")) &&
        ((brDoc == 0) || (brDoc == "")) &&
        ((PCDoc == 0) || (PCDoc == "")) &&
        ((upDoc == 0) || (upDoc == "")) &&
        ((eDoc == 0) || (eDoc == "")) &&
        ((cusDoc == 0) || (cusDoc == "")) &&
        ((cusWordDoc == 0) || (cusWordDoc == "")) &&
        ((binDoc == 0) || (binDoc == "")) &&
        ((creditorDoc == 0) || (creditorDoc == "")) &&
        ((creditorsDoc == 0) || (creditorsDoc == ""))

    ) {
        //alert("Please select any package");
        if (opt == 'Binder') toastrNotification("Please select any package", 'error');
        return false;
    } else {
        return true;
    }

}

function openSendDocPopup(LMRId, actionOpt) {
    console.log({
        func: 'openSendDocPopup',
    });

    var encryptedPCID = '', encryptedEId = '', clientName = '', oldFPCID = 0, popsTitle = '';

    encryptedPCID = document.loanModForm.encryptedPCID.value;
    encryptedEId = document.loanModForm.encryptedEId.value;
    clientName = document.loanModForm.clientName.value;
    oldFPCID = document.loanModForm.oldFPCID.value;

    var selPkg = false;
    if (oldFPCID == 854) { /* As per Daniel request, remove the restriction for the law offices PC to select the documents while sending emails on Sep 4, 2015. */
        selPkg = true;
        //popsTitle = 'Send Email';
    } else {
        selPkg = getAllPackages();
        popsTitle = 'Send Docs';
    }
    //if(selPkg) {
    var selPKGID = 0, selBPKGID = 0, selCPKGID = 0, selEPKGID = 0, selBinPKGID = 0, selUpDocID = 0, selAttachedPkg = 0,
        totFileSize = 0, selPCPKGID = 0, selCreditorEsignPKGID = 0, selCreditorAttachedPKGID = 0;
    var selCWPKGID = 0;

    selPKGID = document.getElementById("selectedPkg").value;
    selBPKGID = document.getElementById("branchSelectedPkg").value;
    selCPKGID = document.getElementById("customSelectedPkg").value;
    selCWPKGID = document.getElementById("customWordSelectedPkg").value;
    selEPKGID = document.getElementById("esignSelectedPkg").value;
    selBinPKGID = document.getElementById("binderSelectedPkg").value;
    selUpDocID = document.getElementById("selectedUpDoc").value;
    selAttachedPkg = document.getElementById("selectedAttachedPkg").value;
    selPCPKGID = document.getElementById("PCSelectedPkg").value;

    selCreditorEsignPKGID = document.getElementById("creditorEsignPkg").value;
    selCreditorAttachedPKGID = document.getElementById("creditorAttachedPkg").value;

    totFileSize = document.getElementById("totFileSize").value;
    qstr = "LMRId=" + LMRId + "&assignedPCID=" + encryptedPCID + "&executiveId=" + encryptedEId + "&fax=0";
    qstr += "&selPKGID=" + selPKGID + "&selBPKGID=" + selBPKGID + "&selCPKGID=" + selCPKGID + "&selCWPKGID=" + selCWPKGID + "&selEPKGID=" + selEPKGID;
    qstr += "&selBinPKGID=" + selBinPKGID + "&selUpDocID=" + selUpDocID + "&selAttachedPkg=" + selAttachedPkg;
    qstr += "&totFileSize=" + totFileSize + "&selPCPKGID=" + selPCPKGID;
    qstr += "&selCreditorEsignPKGID=" + selCreditorEsignPKGID + "&selCreditorAttachedPKGID=" + selCreditorAttachedPKGID;
    qstr += "&actionOpt=" + actionOpt + "&showSaveBtn=1";

    clientName = htmlentities(clientName, "ENT_QUOTES");

    eval("popupArray['" + POPSURL + "docsSend.php'][1]	= 'File : " + clientName + " > " + popsTitle + "'");
    eval("ContactPop.showOverlay('" + POPSURL + "docsSend.php')"); /** Open Popup **/
    //}
}

function getNoOfFax(LMRId, assignedPCID, executiveId, fax) {
    console.log({
        func: 'getNoOfFax',
    });

    getSelectedPKGID();
    var pkgCnt = 0, j = 0, totalCnt = 0, clientName = '';
    var customDoc = $('#customSelectedPkg').val();

    clientName = document.loanModForm.clientName.value;
    pkgCnt = document.loanModForm.pkgCnt.value;

    var selPkg = false;
    selPkg = getAllPackages();
    var selPKGID = 0, selBPKGID = 0, selCPKGID = 0, selEPKGID = 0, selBinPKGID = 0, selUpDocID = 0,
        selAttachedPkg = 0, totFileSize = 0, selCreditorPKGID = 0, selCreditorAttachedPKGID = 0;
    var selCWPKGID = 0;
    selPKGID = document.getElementById("selectedPkg").value;
    selBPKGID = document.getElementById("branchSelectedPkg").value;
    selCPKGID = document.getElementById("customSelectedPkg").value;
    selCWPKGID = document.getElementById("customWordSelectedPkg").value;
    selEPKGID = document.getElementById("esignSelectedPkg").value;
    selBinPKGID = document.getElementById("binderSelectedPkg").value;
    selUpDocID = document.getElementById("selectedUpDoc").value;
    selAttachedPkg = document.getElementById("selectedAttachedPkg").value;
    totFileSize = document.getElementById("totFileSize").value;
    try {
        selCreditorPKGID = document.getElementById("creditorSelectedPkg").value;
    } catch (e) {
    }
    try {
        selCreditorAttachedPKGID = document.getElementById("creditorAttachedPkg").value;
    } catch (e) {
    }
    qstr = "LMRId=" + LMRId + "&assignedPCID=" + assignedPCID + "&executiveId=" + executiveId + "&fax=1";
    qstr += "&selPKGID=" + selPKGID + "&selBPKGID=" + selBPKGID + "&selCPKGID=" + selCPKGID + "&selCWPKGID=" + selCWPKGID + "&selEPKGID=" + selEPKGID;
    qstr += "&selBinPKGID=" + selBinPKGID + "&selUpDocID=" + selUpDocID + "&selAttachedPkg=" + selAttachedPkg;
    qstr += "&totFileSize=" + totFileSize;
    qstr += "&selCreditorPKGID=" + selCreditorPKGID + "&selCreditorAttachedPKGID=" + selCreditorAttachedPKGID + "&showSaveBtn=1";

    clientName = htmlentities(clientName, "ENT_QUOTES");

    $('#faxButton').data('id', qstr);
    $('#faxButton').data('name', 'File : ' + clientName + ' > Send Fax');
    $('#faxButton').click();
    // eval("popupArray['" + POPSURL + "docsSend.php'][1]	= 'File : " + clientName + " > Send Fax'");
    //eval("ContactPop.showOverlay('" + POPSURL + "docsSend.php')"); /** Open Popup **/
    return true;
    // }
}

function toggleESignDocStatus() {
    console.log({
        func: 'toggleESignDocStatus',
    });

    let eSignDocStatus = $('#eSignDocStatus');
    let eSignDocStatusVal = eSignDocStatus.val();
    eSignDocStatus.val(eSignDocStatusVal === '0' ? '1' : '0');
}

function deleteESignedDoc(txnID, LMRId, docId, packageName, isSysNotesPrivate) {
    console.log({
        func: 'deleteESignedDoc - used for esign doc list to delete, not to be confused with same name function in mailFileDocs.js',
    });
    $.confirm({
        icon: 'fa fa-warning',
        closeIcon: true,
        title: 'Confirm',
        content: "Are you sure to delete this document?",
        type: 'red',
        backgroundDismiss: true,
        buttons: {
            cancel: {
                text: 'Close',
                action: function () {

                }
            },
            yes: {
                action: function () {
                    HTTP.Post('/backoffice/deleteEsignedDoc.php', {
                        txnID: txnID,
                        LMRId: LMRId,
                        docId: docId,
                        packageName: packageName,
                        isSysNotesPrivate: isSysNotesPrivate,
                    }, function (data) {
                        toastrNotification(data.success, 'success');
                        $('tr#' + txnID).hide();
                    });
                }
            }
        },
        // onClose: function () {
        // },
        // this onclose was causing scroll to top, so i commented it
    });
}

function showDeletedESignDoc(LMRId) {
    console.log({
        func: 'showDeletedESignDoc',
    });

    let dStatus = $('#eSignDocStatus').val();
    $.ajax({
        url: '/backoffice/getFileESignDocsList.php',
        type: 'POST',
        data: {
            LMRId: LMRId,
            dStatus: dStatus
        },
        beforeSend: function () {

        },
        success: function (data) {
            $('#restoreESignDocList').html(data);
        }
    });
}

function restoreDeletedESignDoc(LMRId, txnId, docId) {
    console.log({
        func: 'restoreDeletedESignDoc',
    });

    $.confirm({
        icon: 'fa fa-exclamation-circle text-warning',
        closeIcon: true,
        title: 'Confirm',
        content: "Are you sure to restore this document?",
        type: 'orange',
        backgroundDismiss: false,
        buttons: {
            no: {
                action: function () {

                }
            },
            yes: {
                btnClass: 'btn-blue',
                action: function () {
                    $.ajax({
                        url: siteSSLUrl + "backoffice/restoreDeletedESignDoc.php",
                        dataType: 'json',
                        method: 'POST',
                        data: {
                            LMRId: LMRId,
                            txnId: txnId,
                            docId: docId,
                        },
                        beforeSend: function () {

                        },
                        success: function (resp) {
                            let msg;
                            let msgType;
                            if (resp) {
                                msg = 'Document restored successfully.';
                                msgType = 'success';
                                toastrNotification(msg, msgType);
                                toggleESignDocStatus();
                                showDeletedESignDoc(LMRId);
                            } else {
                                msg = 'Error!! Document can not be restored. Please try again.';
                                msgType = 'error';
                                toastrNotification(msg, msgType);
                            }

                        }
                    });
                }
            }
        },
        onClose: function () {
        },
    });
}

function deleteUploadFileDoc(docId, userId, opt, LMRId, divId, tableId) {
    console.log({
        func: 'deleteUploadFileDoc',
    });


    //$('.with-children-tip > *').hideTip();
    let lid = $('#fLMRId').val();
    if (LMRId != '') {
        encryptedLMRId = LMRId;
    } else {
        encryptedLMRId = document.loanModForm.encryptedLId.value;
    }

    $.confirm({
        icon: 'fa fa-exclamation-triangle text-danger',
        closeIcon: true,
        title: 'Confirm',
        content: "Are you sure want to delete this document?",
        type: 'red',
        backgroundDismiss: false,
        buttons: {
            cancel: {
                text: 'Close',
                action: function () {
                    //  location.reload();
                }
            },
            yes: {
                action: function () {
                    var url = siteSSLUrl + "backoffice/deleteFileDoc.php";
                    var qstr = "dId=" + docId + "&userId=" + userId + "&LMRId=" + LMRId;
                    try {
                        xmlDoc = getXMLDoc(url, qstr);
                    } catch (e) {
                    }
                    var delCnt = '';
                    try {
                        delCnt = xmlDoc.getElementsByTagName("delCnt")[0].firstChild.nodeValue;
                    } catch (e) {
                    }
                    if (delCnt > 0) {
                        var rowNumbIndex = 0;
                        eval("rowNumbIndex = document.getElementById('" + divId + "').rowIndex");
                        eval("document.getElementById('" + tableId + "').deleteRow(" + rowNumbIndex + ")");
                        if (tableId == 'UploadChkFileDocList') {
                            var divId2 = '';
                            var rowNumbIndex = 0;
                            divId2 = divId.replace('chk_', '');
                            eval("rowNumbIndex = document.getElementById('" + divId2 + "').rowIndex");
                            eval("document.getElementById('UploadFileDocList').deleteRow(" + rowNumbIndex + ")");
                        }
                        toastrNotification("Deleted successfully", 'success');
                        getFileNotes(lid, '');
                    }
                }
            }
        },
        onClose: function () {
        },
    });

}

function restoreDeletedUploadFileDoc(docId, userId, opt, LMRId, divId, tableId) {
    console.log({
        func: 'restoreDeletedUploadFileDoc',
    });

    let encryptedLMRId;
    if (LMRId != '') {
        encryptedLMRId = LMRId;
    } else {
        encryptedLMRId = $('#encryptedLId').val();
    }
    $.confirm({
        icon: 'fa fa-exclamation-circle text-warning',
        closeIcon: true,
        title: 'Confirm',
        content: "Are you sure to restore this document?",
        type: 'orange',
        backgroundDismiss: false,
        buttons: {
            no: {
                action: function () {

                }
            },
            yes: {
                btnClass: 'btn-blue',
                action: function () {
                    $.ajax({
                        url: siteSSLUrl + "backoffice/restoreDeletedUploadFileDoc.php",
                        dataType: 'json',
                        method: 'POST',
                        data: {
                            LMRId: encryptedLMRId,
                            docId: docId,
                        },
                        beforeSend: function () {

                        },
                        success: function (resp) {
                            let msg;
                            let msgType;
                            if (resp) {
                                msg = 'Document restored successfully.';
                                msgType = 'success';
                                toastrNotification(msg, msgType);
                                location.reload();
                            } else {
                                msg = 'Error!! Document can not be restored. Please try again.';
                                msgType = 'error';
                                toastrNotification(msg, msgType);
                            }

                        }
                    });
                }
            }
        },
        onClose: function () {
        },
    });
}


function showAndHideFaxCoverInfo(fldValue, divCls) {
    console.log({
        func: 'showAndHideFaxCoverInfo',
    });

    console.log('showAndHideFaxCoverInfo');
    if (fldValue == '1') {
        //document.getElementsByClassName(divCls).style.display = 'block';
        $("." + divCls).css("display", "block");
    } else {
        //document.getElementsByClassName(divCls).style.display = 'none';
        $("." + divCls).css("display", "none");
    }
}


/**

 Description    : Validation of the docs Name and save functionality
 Developer    : Viji & Venkatesh,Suresh
 Date        : Feb 24, 2017

 **/

function validatePCFileDocuments() {
    console.log({
        func: 'validatePCFileDocuments',
    });

    return !!(chkIsBlank('PCFileDocumentsForm', 'docName', 'Please enter the file documents')
        && isMultiCheckboxSelected('PCFileDocumentsForm', 'docsModuleName', 'Please Select any Modules'));
}

function savePCFileDocuments() {
    console.log({
        func: 'savePCFileDocuments',
    });

    return !!validatePCFileDocuments();
}

function showSortableList(sortOpt, orderBy, LMRId, PCID, allowToEdit, recordDate, docId, displayDocName, category, userId) {
    console.log({
        func: 'showSortableList',
    });

    showLoader();
    setTimeout("showFileDocsList('" + sortOpt + "', '" + orderBy + "', '" + LMRId + "', '" + PCID + "', '" + allowToEdit + "', '" + recordDate + "', '" + docId + "', '" + displayDocName + "', '" + category + "', '" + userId + "')", 500);
}

function showFileDocsList(sortOpt, orderBy, LMRId, PCID, allowToEdit, recordDate, docId, displayDocName, category, userId) {
    console.log({
        func: 'showFileDocsList',
    });

    showLoader();
    var url = "", qstr = "", displayList = "", activeTab = '', clientName = '';
    try {
        activeTab = document.loanModForm.activeTab.value;
    } catch (e) {
    }

    try {
        clientName = document.loanModForm.borrowerName.value;
    } catch (e) {
    }
    //activeTab = 'LI';
    url = siteUrl + "backoffice/getFileDocsList.php";
    qstr = "LMRId=" + LMRId + "&sortOpt=" + sortOpt + "&orderBy=" + orderBy + "&PCID=" + PCID + "&allowToEdit=" + allowToEdit + "&recordDate=" + recordDate + "&activeTab=" + activeTab + "&category=" + category;

    try {
        displayDocsList = getResponse(url, qstr);
    } catch (e) {
    }
    try {
        if (category == 'HMLO CMA Report') {
            document.getElementById("HMLOUploadFileDocList").innerHTML = displayDocsList;
        } else {
            document.getElementById("UploadFileDocList").innerHTML = displayDocsList;
        }
    } catch (e) {
    }

    if (activeTab == 'PI') {
        $('.propertyPictures').click(function () {
            $("#propertyPictures").dialog({
                modal: true,
                resizable: false,
                draggable: true,
                width: '90%',
                title: 'Pictures of property: ' + $(this).attr('data-title'),
                autoOpen: true,
                closeOnEscape: true,
                buttons: {
                    Close: function () {
                        $(this).dialog("close");
                    }
                },
                resizable: false,
                open: function () {   // open event handler
                    $('#propertyPictures').html('Please wait');
                }
            });

            $('.ui-widget-header').css('display', 'block');
            $('.ui-widget-header a').css('display', 'block');

            var img = "<div class='pad5'><img src='" + $(this).attr('data-url') + "'></div><div class='pad10'></div>"
            $('#propertyPictures').html(img);
            // do other stuff.
        });
    }

    qr = "LMRId=" + LMRId + "&docId=" + docId + "&displayDocName=" + displayDocName + "&docCategory=" + category + "&userId=" + userId + "&edit=y";
    $("#" + docId + "_editFile").html("<a class=\"btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass\" style=\"text-decoration:none;\" data-wsize=\"modal-lg\" data-toggle=\"modal\" data-target=\"#exampleModal1\" data-href=\"" + POPSURL + "editFileDocInfo.php\" data-id='" + qr + "' name='File:" + clientName + " > Doc Info'  title=\"Click to edit File Doc Info\"><i class=\"fa fa-edit\"></i></a>");

    //eval("ContactPop.init('" + POPSURL + "editFileDocInfo.php', 'editFileDocInfo.php', 'File Doc Info', '" + POPSURL + "','fileDocSave.php' , 400,'200')");
    try {
        //  ContactPop.hideOverlay();
    } catch (e) {
    }

    setTimeout("hideLoader()", 200);
}

function toggleActiveStatus() {
    console.log({
        func: 'toggleActiveStatus',
    });


    let uploadDocStatus = $('#uploadDocStatus');
    let uploadDocStatusVal = uploadDocStatus.val();
    uploadDocStatus.val(uploadDocStatusVal === '0' ? '1' : '0');
}

function showSortableListRefresh(
    sortOpt,
    orderBy,
    LMRId,
    PCID,
    allowToEdit,
    recordDate,
    docId,
    displayDocName,
    category,
    userId,
    op,
    activeStatus
) {
    console.log({
        func: 'showSortableListRefresh',
        sortOpt: sortOpt,
        orderBy: orderBy,
        LMRId: LMRId,
        PCID: PCID,
        allowToEdit: allowToEdit,
        recordDate: recordDate,
        docId: docId,
        displayDocName: displayDocName,
        category: category,
        userId: userId,
        op: op,
        activeStatus: activeStatus
    });


    BlockDiv('UploadFileDocList_div');
    let url = "";
    let qstr = "";
    let displayList = "";
    let activeTab = '';
    let clientName = '';

    activeTab = document.loanModForm.activeTab.value;
    clientName = document.loanModForm.borrowerName.value;

    activeStatus = $('#uploadDocStatus').val();
    url = "/backoffice/getFileDocsList.php";

    HTTP.PostRaw(url + '?' + qstr, {
        LMRId: LMRId,
        sortOpt: sortOpt,
        orderBy: orderBy,
        PCID: PCID,
        allowToEdit: allowToEdit,
        recordDate: recordDate,
        activeTab: activeTab,
        category: category,
        op: op,
        activeStatus: activeStatus,
    }, function (data) {

        console.log({
            data: data,
        })

        if (category === 'HMLO CMA Report') {
            document.getElementById("HMLOUploadFileDocList").innerHTML = data;
        } else {
            document.getElementById("UploadFileDocList_div").innerHTML = data;
        }
        let qr = "LMRId=" + LMRId + "&docId=" + docId + "&displayDocName=" + displayDocName + "&docCategory=" + category + "&userId=" + userId + "&edit=y";
        $("#" + docId + "_editFile").html("<a class=\"fa fa-edit fa-2x icon-green tip-bottom\" style=\"text-decoration:none;\" href=\"" + POPSURL + "editFileDocInfo.php\" id='" + qr + "' name='File:" + clientName + " > Doc Info'  title=\"Click to edit File Doc Info\"></a>");
        initialiseDatatable('dataTbl_' + LMRId, true);
        setTimeout("hideLoader()", 200);

        let rowCountLmrid = $('.dataTbl_' + LMRId + ' tr').length;
        if (rowCountLmrid > 0) {
            $('#checkallshow').show();
        }
        $('#select_allAllUploadClass').on('click', function () {
            let upDocId = '';
            let dCnt = '';
            let totFileSizeUpload = '0';
            if (this.checked) {
                $('.selectAllUploadClass').each(function () {
                    this.checked = true;
                    if (dCnt > 0) {
                        upDocId += ", ";
                    }
                    upDocId += this.value;
                    dCnt++;
                    totFileSizeUpload = parseFloat(totFileSizeUpload) + parseFloat($(this).attr('data-file-size'));
                });
                document.loanModForm.selectedUpDoc.value = upDocId;
                document.loanModForm.totFileSize.value = totFileSizeUpload;

            } else {
                $('.selectAllUploadClass').each(function () {
                    this.checked = false;
                });
                document.loanModForm.selectedUpDoc.value = '';
                document.loanModForm.totFileSize.value = '0';
            }
        });
        $('.selectAllUploadClass').on('click', function () {
            if ($('.selectAllUploadClass:checked').length === $('.selectAllUploadClass').length) {
                $('#select_allAllUploadClass').prop('checked', true);
            } else {
                $('#select_allAllUploadClass').prop('checked', false);
            }
        });
    });
}

function initialiseDatatable(tableClass, reinit) {
    console.log({
        func: 'initialiseDatatable',
    });


    if (reinit) {
        $("." + tableClass).dataTable().fnDestroy()
    }
    $('.' + tableClass).DataTable({
        language: {search: "", searchPlaceholder: "Search",},
        /* "sDom": 'ifr<"toolbar">tp',*/
        "sDom": '<"d-flex justify-content-between align-self-center"i<"#' + tableClass + 'Title">f">rt',
        /*  "sDom": 'Bfrtip',*/
        "scrollX": true,
        /* "processing": true,*/
        /*"stateSave": true,*/
        //scrollY: '50vh',
        "scrollY": 500,
        "paging": false,
        "info": true,
        "searching": true,
        /*"retrieve": true,*/
        /*"select":true,*/
        "order": [],
        "columns": [
            {"width": "2%", "className": "text-center"}, //Sno
            {"width": "2%", "className": "text-center"}, //Checkbox
            {"width": "5%", "className": "text-left"}, //Operation
            {"width": "22%", "className": "text-left"}, //FileName
            {"width": "10%", "className": "text-left"}, //Category
            {"width": "15%", "className": "text-left"}, //Doc Type ,
            {"width": "10%"}, //Expiry Date
            {"width": "15%", "className": "text-center"},//Uploaded Bu
            {"width": "10%", "className": "text-center"}, //File type/size
            {"width": "5%", "className": "text-center"}, //Delete
        ],
        "aoColumnDefs": [{
            'bSortable': false,
            'aTargets': [1, 2, 6, 9]
        }],
    });

    $('#' + tableClass + "Title").html('<span class="font-weight-bolder"><u>Uploaded Files & Documents</u></span>');
}

function deleteFileUploadedDoc(docId, userId, LMRId, div) {
    console.log({
        func: 'deleteFileUploadedDoc',
    });


    //$('.with-children-tip > *').hideTip();
    var ks = 0;
    var delCnt = '';
    var dID = '';
    var uploadedBy = '';
    var LMRId = '';
    var tArray = [];
    if (docId != '') {
        var confrim = confirm("Are you sure want to delete this document?");
        encryptedLMRId = document.loanModForm.encryptedLId.value;
        if (confrim) {
            var url = siteSSLUrl + "backoffice/deleteFileDoc.php";
            var qstr = "dId=" + docId + "&userId=" + userId + "&LMRId=" + LMRId;
            try {
                xmlDoc = getXMLDoc(url, qstr);
            } catch (e) {
            }
            try {
                delCnt = xmlDoc.getElementsByTagName("delCnt")[0].firstChild.nodeValue;
            } catch (e) {
            }
        }
    }

    if (delCnt > 0 || docId == '') {
        $(div).parent().parent().remove();
        var cnt = $('.proInsCoverage').length;
        $('#proInsCnt').val(cnt);
    }

    jQuery('.proInsCoverage').each(function (i) {
        $(this).attr("id", "proInsSec_" + i);
    });

    jQuery('.delTxt').each(function (i) {
        $(this).attr("id", "delTxt_" + i);
        $(this).attr("name", "delTxt_" + i);
    });

    return false;
}

/* remove Draw Request Documents... -- is used */
function deleteDrawRequestDocs(docId, userId, LMRId, div) {
    console.log({
        func: 'deleteDrawRequestDocs',
    });


    // $('.with-children-tip > *').hideTip();
    var ks = 0;
    var delCnt = '';
    var dID = '';
    var uploadedBy = '';
    var LMRId = '';
    var tArray = [];
    if (docId != '') {
        var confrim = confirm("Are you sure want to delete this document?");
        encryptedLMRId = document.loanModForm.encryptedLId.value;
        if (confrim) {
            var url = siteSSLUrl + "backoffice/deleteFileDoc.php";
            var qstr = "dId=" + docId + "&userId=" + userId + "&LMRId=" + LMRId;
            try {
                xmlDoc = getXMLDoc(url, qstr);
            } catch (e) {
            }
            try {
                delCnt = xmlDoc.getElementsByTagName("delCnt")[0].firstChild.nodeValue;
            } catch (e) {
            }
        }
    }

    if (delCnt > 0 || docId == '') {
        $(div).parent().parent().remove();
    }

    return false;
}


