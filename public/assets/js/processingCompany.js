/* eslint-disable no-empty */
/* eslint-disable no-undef */

/* eslint-disable no-unused-vars */
function validateProcessingCompanyForm() {
    console.log({
        func: 'validateProcessingCompanyForm',
    });
    var pcId = 0, userRole = '';
    pcId = document.processingCompanyForm.pcId.value;
    userRole = document.processingCompanyForm.userRole.value;

    if (pcId > 0) {
        if (chkIsBlank('processingCompanyForm', 'processingCompanyName', 'Please Enter Processing Company Name') &&
            checkValidEmailId('processingCompanyForm', 'attorneyEmail', 'Please Enter the Email') &&
            validatePCLogo() &&
            chkPhoneNumberExtension('processingCompanyForm', 'phoneNumber') &&
            isMultiCheckboxSelected('processingCompanyForm', 'moduleType', 'Please Select the Modules') &&
            chkPCModuleServices()) {
            if (document.processingCompanyForm.publicUser.value == true) {
                if (checkPCEmailExists()) {
                    return true;
                } else {
                    return false;
                }
            } else if (userRole == 'Super' || userRole == 'Sales') {
                if (chkIsBlank('processingCompanyForm', 'salesRep', 'Please select sales rep') &&
                    chkIsBlank('processingCompanyForm', 'nUsers', 'Please select no of users') &&
                    checkSMTPServer('processingCompanyForm', 'serverInfoUserSetting')
                ) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return true;
            }
        } else {
            return false;
        }

    } else {
        if (chkIsBlank('processingCompanyForm', 'processingCompanyName', 'Please Enter Processing Company Name') &&
            chkIsBlank('processingCompanyForm', 'attorneyFName', 'Please Enter Owner First Name') &&
            chkIsBlank('processingCompanyForm', 'attorneyLName', 'Please Enter Owner Last Name') &&
            isEmailOk('processingCompanyForm', 'attorneyEmail', 'Please Enter the Email') &&
            validatePCLogo() &&
            chkPhoneNumber('processingCompanyForm', 'phone1', 'phone2', 'phone3') &&
            chkIsBlank('processingCompanyForm', 'nUsers', 'Please select no of users') &&
            chkIsBlank('processingCompanyForm', 'salesRep', 'Please select sales rep')) {
            if (document.processingCompanyForm.publicUser.value == true) {
                if (checkPCEmailExists()) {
                    return true;
                } else {
                    return false;
                }
            } else {
                if (isMultiCheckboxSelected('processingCompanyForm', 'moduleType', 'Please Select the Modules') &&
                    chkPCModuleServices()
                ) {
                    return true;
                } else {
                    return false;
                }
            }
        } else {
            return false;
        }
    }
}

function validatePCLogo() {
    console.log({
        func: 'validatePCLogo',
    });
    var path = document.processingCompanyForm.logoName.value;
    if (path == '') return true;

    var path_len = path.length;
    var file_length = path.lastIndexOf('\\');
    var fileName = path.substring(file_length + 1, path_len);

    if (path != "") {
        path_len = path.length;
        file_extension = path.lastIndexOf('.');
        file_ext_string = path.substring(file_extension + 1, path_len);
        if ((file_ext_string == "jpg") || (file_ext_string == "jpeg") ||
            (file_ext_string == "JPG") || (file_ext_string == "JPEG") ||
            (file_ext_string == "png") || (file_ext_string == "PNG") ||
            (file_ext_string == "gif") || (file_ext_string == "GIF")
        ) {
            return true;
        } else {
            toastrNotification('File types allowed are  gif, jpeg, png.', 'error');
            document.processingCompanyForm.logoName.focus();
            return false;
        }
    }
}

function checkSMTPServer(formName, fieldName) {
    console.log({
        func: 'checkSMTPServer',
    });
    var fld = "", fldCnt = 0, fldVal = 0;

    eval("var obj = document." + formName + "." + fieldName);
    for (var i = 0; i < obj.length; i++) {
        eval("var fld = document." + formName + "." + fieldName + "[" + i + "]" + ".checked");
        if (fld) {
            eval("var fldVal = document." + formName + "." + fieldName + "[" + i + "]" + ".value");
        }
    }
    if (fldVal == 1) {
        if (
            chkIsBlank(formName, 'hostName', 'Please enter smtp host name') &&
            chkIsBlank(formName, 'userName', 'Please enter smtp user name') &&
            chkIsBlank(formName, 'pwd', 'Please enter smtp password') &&
            chkIsBlank(formName, 'portNo', 'Please enter smtp port #')
        ) {
            return true;
        } else {
            return false;
        }
    } else {
        return true;
    }
}

function checkPCEmailExists() {
    console.log({
        func: 'checkPCEmailExists',
    });
    var PCEmail = "", userEmailExists = 0, pcId = 0, attorneyEmail = '';
    PCEmail = document.processingCompanyForm.attorneyEmail.value;
    pcId = document.processingCompanyForm.pcId.value;
    attorneyEmail = document.processingCompanyForm.PCEmailChk.value;
    if (PCEmail != "" && trim(PCEmail) != trim(attorneyEmail)) {
        var status = 0, updMsg = 0;
        var url = "../backoffice/checkPCEmailExists.php";
        var qstr = "PCEmail=" + PCEmail + "&pcId=" + pcId;
        var xmlDoc = "";
        try {
            xmlDoc = getXMLDoc(url, qstr);
        } catch (e) {
        }
        try {
            userEmailExists = xmlDoc.getElementsByTagName("userEmailExists")[0].firstChild.nodeValue;
        } catch (e) {
        }

        if (userEmailExists > 0) {
            statusMsg = "The email address already exists. Please try with some other email address.";
            //alert(statusMsg);

            toastrNotification(statusMsg, 'error');
            document.processingCompanyForm.attorneyEmail.value = '';
            document.processingCompanyForm.attorneyEmail.focus();
            return false;
        } else {
            return true;
        }
    }
    return true;
}

function showPCUsers(formName, fieldName, targetName) {
    console.log({
        func: 'showPCUsers',
    });
    var selectedUsers = 0;
    eval("selectedUsers = document." + formName + "." + fieldName + ".value");
    eval("document." + formName + "." + targetName + ".value = selectedUsers");
}

function procCompLogoDelete(PCID) {
    console.log({
        func: 'procCompLogoDelete',
    });
    var confirmMsg = confirm("Are you sure you want to delete this logo?");
    if (confirmMsg) {
        window.location.href = "PCLogoDelete.php?pcId=" + PCID;
    }
}

function procCompFooterLogoDelete(PCID) {
    console.log({
        func: 'procCompFooterLogoDelete',
    });
    var confirmMsg = confirm("Are you sure you want to delete this logo?");
    if (confirmMsg) {
        window.location.href = "PCFooterLogoDelete.php?pcId=" + PCID;
    }
}

function availableToBranch(PKGID) {
    console.log({
        func: 'availableToBranch',
    });

    let PCID = document.pkgForm.pcId.value;

    HTTP.Post('/backoffice/api_v2/jqfiles/makePCPkgToBranch', {
        PKGID: PKGID,
        PCID: PCID,
    }, function (data) {

        let status = parseInt(data.hasOwnProperty('status') ? data.status : 0);
        let msg = status > 0 ? 'Made Available to All Branches' : 'Already Available to All Branches';

        toastrNotification(msg, 'success');
    });
}

function shareDocToPC(PCID) {
    console.log({
        func: 'shareDocToPC',
    });

    $.ajax({
        type: 'POST',
        url: "../backoffice/makePCtoAllStdPkg.php",
        data: jQuery.param({'PCID': PCID}),
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        success: function (response) {
            res = JSON.parse(response);
            if (res.code == 100) {
                toastrNotification(res.msg, 'success');
                setTimeout(function () {
                    window.location.reload();
                }, 1000);
            } else {
                toastrNotification(res.msg, 'error');
            }
        }
    });

}

function deleteChecklist(checklistId, rowCnt, moduleCode) {
    console.log({
        func: 'deleteChecklist',
    });
    let rowNumbIndex = "", rsCnt = 0;
    rowNumbIndex = document.getElementById(rowCnt).rowIndex;
    document.getElementById("updMsgTop").innerHTML = "";

    $.confirm({
        icon: 'fa fa-warning',
        closeIcon: true,
        title: 'Confirm',
        content: "Would you like to delete this checklist?",
        type: 'red',
        backgroundDismiss: true,
        buttons: {
            Yes: function () {
                try {
                    xmlDoc = getXMLDoc("../backoffice/deleteChecklist.php", "checklistId=" + checklistId);
                } catch (e) {
                }
                eval("var table = document.getElementById('checklistTable_" + moduleCode + "').deleteRow(rowNumbIndex)");
                try {
                    rsCnt = xmlDoc.getElementsByTagName("rsCnt")[0].firstChild.nodeValue;
                } catch (e) {
                }
                if (rsCnt == 1) {
                    document.getElementById("updMsgTop").innerHTML = "<h4>Item Deleted</h4>";
                }
            },
            Cancel: function () {

            }
        },
    });

    //$('.with-children-tip > *').hideTip();

}

function deletebrokerReqDoc(docId, pcId) {
    console.log({
        func: 'deletebrokerReqDoc',
    });


    $.confirm({
        icon: 'fa fa-warning',
        closeIcon: true,
        title: 'Confirm',
        content: "Would you like to delete this document details ?",
        type: 'red',
        backgroundDismiss: true,
        buttons: {
            yes: {
                btnClass: 'btn-green',
                action: function () {
                    var url = "../backoffice/deleteOrUpdateBrokerDocs.php?docId=" + docId;
                    $.ajax({
                        type: 'POST',
                        url: '../backoffice/deleteBrokerDocs.php',
                        data: jQuery.param({'docId': docId, 'type': 'delete'}),
                        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                        success: function (status) {
                            if (status > 0) {
                                toastrNotification('Document Deleted Successfully', 'success');
                                location.reload();
                            }
                        }
                    });

                }
            },
            cancel: {
                text: 'Cancel',
                action: function () {
                    //  location.reload();
                }
            },
        }
    });


}


function validateBorkerRequiredDocs() {
    console.log({
        func: 'validateBorkerRequiredDocs',
    });

    if (chkIsBlank('brokerRequiredDocForm', 'docName', 'Please Enter Required Doc Name')) {
        // if( chkIsBlank('brokerRequiredDocForm', 'docCategory', 'Please Enter Required Doc Category')) {
        return true;
        // }
        // else return false;
    } else {
        return false;
    }
}

function validateChecklistItem(txt) {
    console.log({
        func: 'validateChecklistItem',
    });
    var checklistId = '';
    checklistId = document.checklistForm.checklistId.value;
    if (checklistId == 0) checklistId = '';

    if (chkIsBlank('checklistForm', 'checklistItem', 'Please enter the required docs name') &&
        chkIsBlank('checklistForm', 'moduleName', 'Please Select the File Type')) {
        if (chkModuleAndServices(txt)) {
            $('#checklistForm').find(".button").attr('disabled', true);
            return true;
        } else {
            return false;
        }
    } else {
        return false;
    }
}

function chkModuleAndServices(txt) {
    console.log({
        func: 'chkModuleAndServices',
    });
    var moduleNameArray = new Array();
    var moduleName = '';
    var moduleCodeArray = new Array();
    var moduleCode = '';

    var objLen = 0, t = 0, fld = false, fldVal = '';
    try {
        eval("moduleCode = document.checklistForm.moduleName.value");
    } catch (e) {
    }

    if (moduleCode != '') {
        moduleCodeArray = moduleCode.split(", ");

    }

    for (var i = 0; i < moduleCodeArray.length; i++) {
        var moduleCode = '', moduleName = '';
        var Cnt = 0;
        var servicesCnt = 0;
        moduleCode = moduleCodeArray[i];

        servicesCnt = eval("document.getElementById('" + moduleCode + "_Cnt').value");
        for (var j = 0; j < servicesCnt; j++) {
            var chk = false;
            chk = eval("document.getElementById('services_" + moduleCode + "_" + j + "').checked");
            if (chk) {
                Cnt++;
            }
        }
        if (Cnt == 0) {
            // alert(" Please Select Any "+txt);
            toastrNotification(" Please Select Any " + txt, 'error');
            return false;
        }
    }
    return true;
}

/* PC CheckList Modules and Services */

/*
function chkModuleServices() {
	var moduleNameArray = new Array();   var moduleName = '';
	var moduleCodeArray = new Array();  var moduleCode = '';

    var objLen = 0, t = 0, fld = false, fldVal = '';
    try {
        eval("objLen = document.checklistForm.moduleName.length");
    } catch(e) {}

    if(objLen > 0) {
        for(var i=0; i<objLen; i++) {
			fld = false, fldVal = '';
				eval("fld = document.checklistForm.moduleName.options["+i+"].selected");
				if (fld) {
					eval("fldVal = document.checklistForm.moduleName.options["+i+"]" +".value");
					if(t == 0) {
						eval("moduleName = document.checklistForm.moduleName.options["+i+"]" +".text");
						moduleCode = fldVal;
					} else {
						moduleName += ", ";
						eval("moduleName += document.checklistForm.moduleName.options["+i+"]" +".text");
						moduleCode += ", "+ fldVal;
					}
					t++;
				}
        }
    } else {
        eval("var fld = document.checklistForm.moduleName.selected");
        if(fld) {
			eval("moduleCode = document.checklistForm.moduleName.value");
			eval("moduleName = document.checklistForm.moduleName.value");
        }
    }
	if (moduleCode !='')
	{
		moduleCodeArray = moduleCode.split(", ");
		moduleNameArray = moduleName.split(", ");

	}
	
    for(var i=0;i<moduleCodeArray.length;i++) {
		var moduleCode = '', moduleName = ''; var Cnt = 0; var servicesCnt = 0; 
		moduleCode = moduleCodeArray[i];
		moduleName = moduleNameArray[i];

		servicesCnt = eval("document.getElementById('"+moduleCode+"_Cnt').value");
		for(var j=0; j<servicesCnt; j++) {
			var chk = false;
			chk = eval("document.getElementById('services_"+moduleCode+"_"+j+"').checked");
			if (chk){
				Cnt++;
			}
		}
		if (Cnt == 0) { 
			 alert(" Please Select Any Services for the module "+ moduleName);
			 return false;
		} 
	}
	return true;
}
*/

function clickToAllFields(chk, moduleCode) {
    console.log({
        func: 'clickToAllFields',
    });
    var opt = false;
    var len = 0;
    try {
        len = eval("document.getElementById('" + moduleCode + "_Cnt').value");
    } catch (e) {
    }

    if (chk) {
        opt = true;
    }
    for (var j = 0; j < len; j++) {
        eval("document.getElementById('services_" + moduleCode + "_" + j + "').checked = " + opt);
    }
}

/* PC CheckList Modules and Services */

/* PC Profile Modules and Services */

function clickToAllServiceFields(chk, moduleCode, nOPT, defaultLoanPgm) {
    console.log({
        func: 'clickToAllServiceFields',
    });
    var opt = false;
    var len = 0;
    try {
        len = eval("document.getElementById('" + moduleCode + "_Cnt').value");
    } catch (e) {
    }

    if (chk) {
        opt = true;
    }
    if (moduleCode == 'HMLO' && nOPT == 'LW') { /* Lending Wise Default Service - HMLO) April 27, 2017 */
        //var serviceTypeArray = ['PC', 'BRL', 'C', 'CONS', 'FAF', 'L', 'LC', 'MF', 'R', 'RBH', 'REF', 'REPC', 'TRFU'];
        //var serviceTypeArray = ['PC', 'BRL', 'C', 'CONS', 'FAF', 'RBH', 'REF', 'TRFU', 'BPP', 'COR', 'JVR'];
        var serviceTypeArray = defaultLoanPgm;
        for (var k = 0; k < len; k++) {
            eval("var serviceTypeName = document.getElementById('services_" + moduleCode + "_" + k + "').value");
            for (var i = 0; i < serviceTypeArray.length; i++) {
                if (serviceTypeName == serviceTypeArray[i]) {
                    eval("document.getElementById('services_" + moduleCode + "_" + k + "').checked = true");
                }
            }
        }
    } else {
        for (var k = 0; k < len; k++) {
            eval("document.getElementById('services_" + moduleCode + "_" + k + "').checked = " + opt);
        }
    }
}

function chkPCModuleServices() {
    console.log({
        func: 'chkPCModuleServices',
    });
    var moduleNameArray = new Array();
    var moduleName = '';
    var moduleCodeArray = new Array();
    var moduleCode = '';

    var objLen = 0, t = 0, fld = false, fldVal = '';
    try {
        eval("objLen = document.processingCompanyForm.moduleType.length");
    } catch (e) {
    }

    if (objLen > 0) {
        for (var i = 0; i < objLen; i++) {
            fld = false, fldVal = '';
            eval("fld = document.processingCompanyForm.moduleType.options[" + i + "].selected");
            if (fld) {
                eval("fldVal = document.processingCompanyForm.moduleType.options[" + i + "]" + ".value");
                if (t == 0) {
                    eval("moduleName = document.processingCompanyForm.moduleType.options[" + i + "]" + ".text");
                    moduleCode = fldVal;
                } else {
                    moduleName += ", ";
                    eval("moduleName += document.processingCompanyForm.moduleType.options[" + i + "]" + ".text");
                    moduleCode += ", " + fldVal;
                }
                t++;
            }
        }
    } else {
        eval("var fld = document.processingCompanyForm.moduleType.selected");
        if (fld) {
            eval("moduleCode = document.processingCompanyForm.moduleType.value");
            eval("moduleName = document.processingCompanyForm.moduleType.value");
        }
    }
    if (moduleCode != '') {
        moduleCodeArray = moduleCode.split(", ");
        moduleNameArray = moduleName.split(", ");

    }

    for (var i = 0; i < moduleCodeArray.length; i++) {
        var moduleCode = '', moduleName = '';
        var Cnt = 0;
        var servicesCnt = 0;
        moduleCode = moduleCodeArray[i];
        moduleName = moduleNameArray[i];

        servicesCnt = eval("document.getElementById('" + moduleCode + "_Cnt').value");
        for (var j = 0; j < servicesCnt; j++) {
            var chk = false;
            chk = eval("document.getElementById('services_" + moduleCode + "_" + j + "').checked");
            if (chk) {
                Cnt++;
            }
        }
        if (Cnt == 0) {
            // alert(" Please Select Any Services for the module "+ moduleName);
            toastrNotification(" Please Select Any Services for the module " + moduleName, 'error');
            return false;
        }
    }
    return true;
}

/* PC Profile Modules and Services */

function savePCChecklistItems() {
    console.log({
        func: 'savePCChecklistItems',
    });
    if (validateChecklist()) {
        $('#loaderDiv').show();
        saveNewPCChecklistItems();
        return true;
    } else {
        return false;
    }
}

function saveNewPCChecklistItems() {
    console.log({
        func: 'saveNewPCChecklistItems',
    });
    var statPopupArray = new Array();
    var data = $("#checklistForm").serialize();
    var PCID = 0;
    PCID = $("#PCID").val();
    $.post("../pops/saveCheckListInfo.php", data, function (theResponse) {
        $("#sessMsg").html('<h4>Primary File Substatus Saved<h4>');

        try {
            document.getElementById('PCFileChecklistDiv').innerHTML = theResponse;
        } catch (e) {
        }
        $("#tabledivbody").sortable({
            items: "tr",
            cursor: 'move',
            opacity: 0.6,
            placeholder: "ui-state-highlight",
            update: function () {
                $('.saveDispOrder').css("display", "block");
            }
        });

        //$("#PCFileStatusDiv").html(theResponse);
        qstr = "PCID=" + PCID + "&showSaveBtn=1";
        /** Popup start **/

        eval("statPopupArray['" + POPSURL + "addChecklistItem.php'] = new Array('addChecklistItem.php', 'Add / Edit Checklist', '" + POPSURL + "','saveChecklistInfo.php' ,900,'500')");

        for (key in statPopupArray) {
            ContactPop.init(key, statPopupArray[key][0], statPopupArray[key][1], statPopupArray[key][2], statPopupArray[key][3], statPopupArray[key][4], statPopupArray[key][5]);
        }
        /** Popup end **/
    });

    try {
        ContactPop.hideOverlay(); /** Close- Popup **/
    } catch (e) {
    }
    $('.with-tip, .with-children-tip > *').tip();
}

function validatePCUploadForm() {
    console.log({
        func: 'validatePCUploadForm',
    });

    var path = document.pkgForm.fileSrc.value;
    var path_len = path.length;
    var file_length = path.lastIndexOf('\\');
    var fileName = path.substring(file_length + 1, path_len);
    if (path != "") {

        path_len = path.length;
        file_extension = path.lastIndexOf('.');
        file_ext_string = path.substring(file_extension + 1, path_len);
        if ((file_ext_string == "pdf") || (file_ext_string == "PDF") ||
            (file_ext_string == "doc") || (file_ext_string == "DOC") ||
            (file_ext_string == "docx") || (file_ext_string == "DOCX") ||
            (file_ext_string == "xls") || (file_ext_string == "XLS") ||
            (file_ext_string == "xlsx") || (file_ext_string == "XLSX")
        ) {
            if (!chkIsBlank('pkgForm', 'categoryID', 'Please select category')) {
                return false;
            }
        } else {
            //alert ("File types  allowed are pdf, doc, docx, xls, xlsx.");
            toastrNotification("File types  allowed are pdf, doc, docx, xls, xlsx.", 'error');
            return false;
        }
    }
    return true;
}

function deleteUploadDoc(docId, PCID) {
    console.log({
        func: 'deleteUploadDoc',
    });
    $.confirm({
        icon: 'fa fa-warning',
        closeIcon: true,
        title: 'Confirm',
        content: "Are you sure want to delete this document?",
        type: 'red',
        backgroundDismiss: true,
        buttons: {
            yes: function () {
                window.location.href = "deletePCDoc.php?dId=" + docId + "&PCId=" + PCID;
            },
            cancel: function () {

            },
        },
    });
}

// function validatePCNotes() {
//     if (chkIsBlank('PCNotesForm', 'PCNotes', 'Please Enter the Notes')) {
//         return true;
//     } else {
//         return false;
//     }
// }

// function validatePCFeeForm() {
//     var feeVal = document.PCFeeForm.payment.value;
//     if (chkIsBlank('PCFeeForm', 'payment', 'Please enter amount owed') && chkIsBlank('PCFeeForm', 'licensePayment', 'Please enter monthly license fee') &&
//         chkIsBlank('PCFeeForm', 'paidDate', 'Please enter due date') &&
//         chkIsBlank('PCFeeForm', 'expiryDate', 'Please enter expiry date')
//     ) {
//         if (alertToNumericValue(feeVal)) {
//             return true;
//         } else {
//             return false;
//         }
//     } else {
//         return false;
//     }
// }

// function alertToNumericValue(currentvalue) {
//     currentvalue1 = trim(currentvalue);
//     currentvalue = replaceCommaValues(currentvalue);
//     if ((currentvalue1 != "") && (currentvalue == "")) {
//         // alert('Please make sure you have entered only numeric values in the fields');
//         toastrNotification('Please make sure you have entered only numeric values in the fields', 'error');
//         return false;
//     } else if ((currentvalue1 > 0) && (currentvalue == 0)) {
//         // alert('Please make sure you have entered only numeric values in the fields');
//         toastrNotification('Please make sure you have entered only numeric values in the fields', 'error');
//         return false;
//     } else {
//         return true;
//     }
// }

// function updateOwedFlag(pcId, owedFlag) {
//     if (owedFlag == 1) {
//         var cfmMsg = "Are you sure to mark as paid";
//     } else {
//         var cfmMsg = "Are you sure to mark as due";
//     }
//     $.confirm({
//         icon: 'fa fa-warning',
//         closeIcon: true,
//         title: 'Confirm',
//         content: cfmMsg,
//         type: 'red',
//         backgroundDismiss: true,
//         buttons: {
//             yes: {
//                 btnClass: 'btn-green',
//                 action: function () {
//                     var xmlDoc = "", url = "", qstr = "", updateCount = 0;
//
//                     url = "../backoffice/updatePCOwedFlag.php";
//                     qstr = "pcId=" + pcId + "&owedFlag=" + owedFlag;
//                     try {
//                         xmlDoc = getXMLDoc(url, qstr);
//                     } catch (e) {
//                     }
//                     window.location.reload();
//                 }
//             },
//             cancel: {
//                 text: 'Cancel',
//                 action: function () {
//                     //  location.reload();
//                 }
//             },
//         }
//     });
//
//
// }

// function processingCompanyDelete(processingCompanyId, searchTerm, PCStatus) {
//     if (PCStatus == 0) {
//         var confirmMsg = confirm("Are sure to deactivate this company?");
//     } else {
//         var confirmMsg = confirm("Are sure to activate this company?");
//     }
//     if (confirmMsg) {
//         sortOpt = document.adminProcessingForm.sortOpt.value;
//         procCompanyId = document.adminProcessingForm.procCompanyId.value;
//         srchOwedFlag = document.adminProcessingForm.srchOwedFlag.value;
//         activeStatus = document.adminProcessingForm.activeStatus.value;
//         window.location.href = "processingCompanyDelete.php?PCID=" + processingCompanyId + "&procCompanyId=" + procCompanyId + "&sortBy=" + sortOpt + "&searchTerm=" + searchTerm + "&PCStatus=" + PCStatus + "&srchOwedFlag=" + srchOwedFlag + "&activeStatus=" + activeStatus;
//     }
// }
//
// function totalDeletePC(processingCompanyId, searchTerm, activeStatus) {
//     var confirmMsg = confirm("Are you sure you want to delete this company?");
//     if (confirmMsg) {
//         pageNumber = document.adminProcessingForm.pageNumber.value;
//         sortOpt = document.adminProcessingForm.sortOpt.value;
//         procCompanyId = document.adminProcessingForm.procCompanyId.value;
//         srchOwedFlag = document.adminProcessingForm.srchOwedFlag.value;
//         activeStatus = document.adminProcessingForm.activeStatus.value;
//         window.location.href = "totalProcessingCompanyDelete.php?PCID=" + processingCompanyId + "&page=" + pageNumber + "&sortBy=" + sortOpt + "&searchTerm=" + searchTerm + "&activeStatus=" + activeStatus + "&srchOwedFlag=" + srchOwedFlag + "&procCompanyId=" + procCompanyId;
//     }
// }
//
// function enableMarketPlacePC(processingCompanyId, marketPlaceStatus, msg) {
//
//     if (marketPlaceStatus == 1) {
//         var confirmMsg = "You want to activate Marketplace For This Company?";
//     } else {
//         var confirmMsg = "You want to deactivate Marketplace For This Company?";
//     }
//
//     $.confirm({
//         icon: 'fa fa-warning',
//         closeIcon: true,
//         title: 'Confirm',
//         content: confirmMsg,
//         type: 'red',
//         backgroundDismiss: true,
//         buttons: {
//             yes: {
//                 btnClass: 'btn-green',
//                 action: function () {
//                     $.ajax({
//                         type: 'POST',
//                         url: siteSSLUrl + "backoffice/changeStatusMarketplace.php",
//                         data: jQuery.param({
//                             'marketPlaceStatus': marketPlaceStatus,
//                             'PCID': processingCompanyId,
//                             'marketPlus': 'marketPlus'
//                         }),
//                         contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
//                         success: function (response) {
//                             if (response > 0) {
//
//                                 if (marketPlaceStatus == 1) {
//                                     $('#' + processingCompanyId + '_0').show();
//                                     $('#' + processingCompanyId + '_1').hide();
//                                 } else {
//
//                                     $('#' + processingCompanyId + '_0').hide();
//                                     $('#' + processingCompanyId + '_1').show();
//                                 }
//                                 toastrNotification(msg + " Successfully", 'success');
//                             } else {
//                                 toastrNotification("Error In Updating", 'warning');
//                             }
//                         }
//                     });
//                 }
//             },
//             cancel: {
//                 text: 'Cancel',
//                 action: function () {
//                     //  location.reload();
//                 }
//             },
//         }
//     });
//
//
// }
//
// function enableMarketPlacePCPublic(processingCompanyId, marketPlaceStatus, msg) {
//     if (marketPlaceStatus == 1) {
//         var confirmMsg = "you want to make Marketplace public For This Company ?";
//     } else {
//         var confirmMsg = "you want to remove Marketplace public Place For This Company ?";
//     }
//
//     $.confirm({
//         icon: 'fa fa-warning',
//         closeIcon: true,
//         title: 'Confirm',
//         content: confirmMsg,
//         type: 'red',
//         backgroundDismiss: true,
//         buttons: {
//             yes: {
//                 btnClass: 'btn-green',
//                 action: function () {
//                     $.ajax({
//                         type: 'POST',
//                         url: siteSSLUrl + "backoffice/changeStatusMarketplace.php",
//                         data: jQuery.param({
//                             'marketPlaceStatus': marketPlaceStatus,
//                             'PCID': processingCompanyId,
//                             'marketPlus': 'marketPlusPublic'
//                         }),
//                         contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
//                         success: function (response) {
//                             if (response > 0) {
//
//                                 if (marketPlaceStatus == 1) {
//                                     $('#' + processingCompanyId + 'b_0').show();
//                                     $('#' + processingCompanyId + 'b_1').hide();
//                                 } else {
//
//                                     $('#' + processingCompanyId + 'b_0').hide();
//                                     $('#' + processingCompanyId + 'b_1').show();
//                                 }
//                                 toastrNotification(msg + " Successfully", 'success');
//                             } else {
//                                 toastrNotification("Error In Updating", 'warning');
//                             }
//                         }
//                     });
//                 }
//             },
//             cancel: {
//                 text: 'Cancel',
//                 action: function () {
//                     //  location.reload();
//                 }
//             },
//         }
//     });
//
//
// }


// function showSortableList(sortOpt, orderBy) {
//     try {
//         document.adminProcessingForm.pageNumber.value = 1;
//     } catch (e) {
//     }
//     try {
//         document.adminProcessingForm.sortOpt.value = sortOpt;
//     } catch (e) {
//     }
//     try {
//         document.adminProcessingForm.orderBy.value = orderBy;
//     } catch (e) {
//     }
//     document.adminProcessingForm.submit();
// }

function selectBelowFilesDocs(docGroup, val) {
    console.log({
        func: 'selectBelowFilesDocs',
    });
    var i = 0, cnt = 0, chkVal = 1, chkOpt = false, obj = "";
    eval("cnt = document.pkgForm.pkg_" + docGroup + "Cnt.value");
    eval("chkVal = document.pkgForm.chk_" + docGroup + ".value");
    eval("obj = document.pkgForm['PKGID_" + docGroup + "[]']");
    for (var i = 0; i < obj.length; i++) {
        var obj1 = '';
        if (val == 1) {
            eval("obj[" + i + "].checked = true");
        } else {
            eval("obj[" + i + "].checked = false");
        }
    }
    if (i == 0) {
        if (val == 1) {
            eval("obj.checked = true");
        } else {
            eval("obj.checked = false");
        }
    }

    if (val == 1) {
        eval("document.pkgForm.chk_" + docGroup + ".value = 1");
    } else {
        eval("document.pkgForm.chk_" + docGroup + ".value = 0");
    }
}

function selectBelowFiles(docGroup) {
    console.log({
        func: 'selectBelowFiles',
    });
    var i = 0, cnt = 0, chkVal = 1, chkOpt = false, obj = "";
    eval("cnt = document.pkgForm.pkg_" + docGroup + "Cnt.value");
    eval("chkVal = document.pkgForm.chk_" + docGroup + ".value");
    eval("obj = document.pkgForm['PKGID_" + docGroup + "[]']");
    for (var i = 0; i < obj.length; i++) {
        var obj1 = '';
        if (chkVal == 1) {
            eval("obj[" + i + "].checked = true");
        } else {
            eval("obj[" + i + "].checked = false");
        }
    }
    if (i == 0) {
        if (chkVal == 1) {
            eval("obj.checked = true");
        } else {
            eval("obj.checked = false");
        }
    }

    if (chkVal == 1) {
        eval("document.pkgForm.chk_" + docGroup + ".value = 0");
    } else {
        eval("document.pkgForm.chk_" + docGroup + ".value = 1");
    }
}

function testSMTPSetting() {
    console.log({
        func: 'testSMTPSetting',
    });
    document.getElementById("smtpSetting").innerHTML = '<div>Please wait... <img src="' + progressBarImg + '"></div>';
    setTimeout("sendAndTestSMTPSetting()", 100);
}

function sendAndTestSMTPSetting() {
    console.log({
        func: 'sendAndTestSMTPSetting',
    });
    var hostName = "", userName = "", pwd = "";
    var replyTo = "", bounceMail = "", mailStatus = 0, msg = "";
    port = "";
    em = "", pcId = 0;
    try {
        hostName = document.processingCompanyForm.hostName.value;
    } catch (e) {
    }
    try {
        userName = document.processingCompanyForm.userName.value;
    } catch (e) {
    }
    try {
        pwd = document.processingCompanyForm.pwd.value;
    } catch (e) {
    }
    try {
        replyTo = document.processingCompanyForm.replyTo.value;
    } catch (e) {
    }
    try {
        port = document.processingCompanyForm.portNo.value;
    } catch (e) {
    }
    try {
        bounceMail = document.processingCompanyForm.bounceMail.value;
    } catch (e) {
    }
    try {
        PCID = document.processingCompanyForm.pcId.value;
    } catch (e) {
    }

    var url = "../backoffice/testSMTPSetting.php";
    var qstr = "hostName=" + hostName + "&UID=" + PCID + "&UGroup=Employee&userName=" + userName + "&pwd=" + pwd + "&replyTo=" + replyTo + "&port=" + port + "&bounceMail=" + bounceMail;
    xmlDoc = getXMLDoc(url, qstr);

    try {
        mailStatus = xmlDoc.getElementsByTagName("mailStatus")[0].firstChild.nodeValue;
    } catch (e) {
    }
    try {
        em = xmlDoc.getElementsByTagName("errorMsg")[0].firstChild.nodeValue;
    } catch (e) {
    }

    if (mailStatus == 1) {
        msg = "SMTP Server Verified";
    } else if (em != '') {
        msg = em;
    } else {
        msg = "SMTP Server Failed. Check setting or contact your e-mail host for permission.";
    }

    toastrNotification(msg, 'error');
    //document.getElementById("smtpSetting").innerHTML = msg;
    setTimeout("clearTestSMTPSetting()", 4500);
}

function clearTestSMTPSetting() {
    console.log({
        func: 'clearTestSMTPSetting',
    });
    document.getElementById("smtpSetting").innerHTML = '';
}

function showHRFeeDiv(fldName, divId) {
    console.log({
        func: 'showHRFeeDiv',
    });
    eval("fldValue = document.processingCompanyForm." + fldName + ".value");
    if (fldValue == 0) {
        document.getElementById("homeReportFee").disabled = true;
    } else {
        document.getElementById("homeReportFee").disabled = false;
    }
}

function showHidePCSettingsDiv(fieldID, divId) {
    console.log({
        func: 'showHidePCSettingsDiv',
    });
    var fValue = $('#' + fieldID).val();
    if (fValue == 0) {
        $('.' + divId).hide();
        $('#loanNumberPrefix').val('');
    } else {
        $('.' + divId).show();
        $('#loanNumberPrefix').val('10000');
    }
}

// function deletePCNotes(PNID) {
//
//     var rsCnt = 0;
//     var confirmMsg = "Are you sure to delete this comment?";
//
//
//     $.confirm({
//         icon: 'fa fa-warning',
//         closeIcon: true,
//         title: 'Confirm',
//         content: confirmMsg,
//         type: 'red',
//         backgroundDismiss: true,
//         buttons: {
//             yes: {
//                 btnClass: 'btn-green',
//                 action: function () {
//                     var url = "../backoffice/deletePCComments.php";
//                     var qstr = "PNID=" + PNID;
//                     try {
//                         xmlDoc = getXMLDoc(url, qstr);
//                     } catch (e) {
//                     }
//                     try {
//                         rsCnt = xmlDoc.getElementsByTagName("rsCnt")[0].firstChild.nodeValue;
//                     } catch (e) {
//                     }
//                     if (rsCnt > 0) {
//                         document.getElementById('PCNotesTable').deleteRow(document.getElementById(PNID).rowIndex);
//                     }
//                 }
//             },
//             cancel: {
//                 text: 'Cancel',
//                 action: function () {
//                     //  location.reload();
//                 }
//             },
//         }
//     });
//
//
// }
//
// function editPCNotes(PNID) {
//     var PCRemarks = '';
//     var PCNotesID = 0;
//     var url = "../backoffice/editPCComments.php";
//     var qstr = "PNID=" + PNID;
//     try {
//         xmlDoc = getXMLDoc(url, qstr);
//     } catch (e) {
//     }
//     try {
//         PCRemarks = xmlDoc.getElementsByTagName("PCRemarks")[0].firstChild.nodeValue;
//     } catch (e) {
//     }
//     try {
//         PCNotesID = xmlDoc.getElementsByTagName("PCNotesID")[0].firstChild.nodeValue;
//     } catch (e) {
//     }
//
//     document.PCNotesForm.PCNotes.value = PCRemarks;
//     document.PCNotesForm.PNID.value = PCNotesID;
// }

function checkEFaxServiceProvider(serviceProvider) {
    console.log({
        func: 'checkEFaxServiceProvider',
    });
    if (serviceProvider == 'Vitelity') {
        $('#serviceProviderDiv').show();
        $('#serviceProviderDiv1').show();
        $("#serviceProviderDiv").html("DID Number");
    } else if (serviceProvider == 'Faxage') {
        $('#serviceProviderDiv').show();
        $('#serviceProviderDiv1').show();
        $("#serviceProviderDiv").html("E-Fax Company");
    } else {
        $('#serviceProviderDiv').hide();
        $('#serviceProviderDiv1').hide();
    }
}

/*
function showAddServicesPopUp(PCID, moduleCode, isHMLO) {

    popupArray = new Array();
    qstr = "PCID=" + PCID + "&PCProfile=PCProfile&MC=" + moduleCode + "&isHM=" + isHMLO;
    if (isHMLO == 1) {
        eval("popupArray['" + POPSURL + "editService.php'] = new Array('editService.php', 'Add / Edit Loan Programs', '" + POPSURL + "','saveServiceType.php', 600, '550')");
    } else {
        eval("popupArray['" + POPSURL + "editService.php'] = new Array('editService.php', 'Add / Edit Service Type', '" + POPSURL + "','saveServiceType.php', 600, '550')");
    }

    for (key in popupArray) {
        ContactPop.init(key, popupArray[key][0], popupArray[key][1], popupArray[key][2], popupArray[key][3], popupArray[key][4], popupArray[key][5]);
    }

    eval("ContactPop.showOverlay('" + POPSURL + "editService.php')");
}*/

function showAddServicesPopUp(PCID, moduleCode, isHMLO) {
    console.log({
        func: 'showAddServicesPopUp',
    });
    var qstr = "PCID=" + PCID + "&PCProfile=PCProfile&MC=" + moduleCode + "&isHM=" + isHMLO;
    if (isHMLO == 1) {
        var _title = "Add Loan Programs";
    } else {
        var _title = "Add Service Type";
    }
    var _url = POPSURL + "editService.php?" + qstr;

    modalCall(_title, 'modal-xl', _url);
}

function addRAMVendorKey(encPCID, encSID) {
    console.log({
        func: 'addRAMVendorKey',
    });
    /** Popup start **/

    qstr = "encPCID=" + encPCID + "&encSID=" + encSID;
    eval("ContactPop.showOverlay('" + POPSURL + "addRAMVendorKey.php')"); /** Open Popup **/
    /** Popup end **/

}

function saveRAMVendorKeyForm() {
    console.log({
        func: 'saveRAMVendorKeyForm',
    });
    if (validateRAMVendorKeyForm()
    ) {
        $('#loaderDiv').show();
        saveRAMVendorKey();
        return true;
    } else {
        return false;
    }
}

function validateRAMVendorKeyForm() {
    console.log({
        func: 'validateRAMVendorKeyForm',
    });
    if (chkIsBlank('RAMVendorKeyForm', 'vendorName', 'Please enter name') &&
        chkIsBlank('RAMVendorKeyForm', 'vendorKey', 'Please enter vendor key') &&
        chkIsBlank('RAMVendorKeyForm', 'vendorKey2', 'Please enter vendor key') &&
        chkIsBlank('RAMVendorKeyForm', 'vendorKey3', 'Please enter vendor key') &&
        chkIsBlank('RAMVendorKeyForm', 'vendorKey4', 'Please enter vendor key') &&
        chkIsBlank('RAMVendorKeyForm', 'vendorKey5', 'Please enter vendor key') &&
        chkIsBlank('RAMVendorKeyForm', 'feeGroup', 'Please enter fee group id') &&
        checkValidNumber('RAMVendorKeyForm', 'feeGroup', 'Fee Group ID') &&
        chkIsBlank('RAMVendorKeyForm', 'affiliateID', 'Please enter affiliate id') &&
        checkValidNumber('RAMVendorKeyForm', 'affiliateID', 'Affiliate ID')
    ) {
        return true;
    } else {
        return false;
    }
}

function saveRAMVendorKey() {
    console.log({
        func: 'saveRAMVendorKey',
    });
    var statPopupArray = new Array();
    var data = $("#RAMVendorKeyForm").serialize();
    var PCID = 0;
    PCID = $("#PCID").val();
    $.post("../pops/saveRAMVendorKey.php", data, function (theResponse) {

        $("#sessMsg").html('<h4>Vendor Keys Saved<h4>');
        try {
            document.getElementById('PCVendorKeyDiv').innerHTML = theResponse;
        } catch (e) {
        }
        qstr = "PCID=" + PCID;

        /** Popup start **/

        eval("statPopupArray['" + POPSURL + "addRAMVendorKey.php']	= new Array('addRAMVendorKey.php', 'Add Vendor Key', '" + POPSURL + "','saveRAMVendorKey.php' , 500, '200')");

        for (key in statPopupArray) {
            ContactPop.init(key, statPopupArray[key][0], statPopupArray[key][1], statPopupArray[key][2], statPopupArray[key][3], statPopupArray[key][4], statPopupArray[key][5]);
        }
        /** Popup end **/

    });
    try {
        ContactPop.hideOverlay(); /** Close- Popup **/
    } catch (e) {
    }
    $('.with-tip, .with-children-tip > *').tip();
}

function deleteRAMVendorKey(encSID, rowNumb) {
    console.log({
        func: 'deleteRAMVendorKey',
    });
    $('.with-children-tip > *').hideTip();
    var rowNumbIndex = "";
    rowNumbIndex = document.getElementById(rowNumb).rowIndex;
    var url = "../backoffice/deleteRAMVendorKey.php";
    var qstr = "SID=" + encSID;
    try {
        xmlDoc = getXMLDoc(url, qstr);
    } catch (e) {
    }
    var table = document.getElementById('PCRAMVendorKeyTable').deleteRow(rowNumbIndex);
    try {
        var rsCnt = xmlDoc.getElementsByTagName("rsCnt")[0].firstChild.nodeValue;
    } catch (e) {
    }
    if (rsCnt == 1) {
        document.getElementById("sessMsg").innerHTML = "<h4>Item Deleted<h4>";
    }
}

function addRAMAffiliateKey(encPCID, encSID) {
    console.log({
        func: 'addRAMAffiliateKey',
    });
    /** Popup start **/

    qstr = "encPCID=" + encPCID + "&encSID=" + encSID;
    eval("ContactPop.showOverlay('" + POPSURL + "addRAMAffiliateKey.php')"); /** Open Popup **/
    /** Popup end **/

}

function saveRAMAffiliateKeyForm() {
    console.log({
        func: 'saveRAMAffiliateKeyForm',
    });
    if (validateRAMAffiliateKeyForm()
    ) {
        $('#loaderDiv').show();
        saveRAMAffiliateKey();
        return true;
    } else {
        return false;
    }
}

function validateRAMAffiliateKeyForm() {
    console.log({
        func: 'validateRAMAffiliateKeyForm',
    });
    if (chkIsBlank('RAMAffiliateKeyForm', 'affiliateName', 'Please enter name') &&
        //      chkIsBlank('RAMAffiliateKeyForm','feeGroup','Please enter fee group id')&&
        //       checkValidNumber('RAMAffiliateKeyForm','feeGroup','Fee Group ID')&&
        chkIsBlank('RAMAffiliateKeyForm', 'affiliateID', 'Please enter affiliate id') &&
        checkValidNumber('RAMAffiliateKeyForm', 'affiliateID', 'Affiliate ID')
    ) {
        return true;
    } else {
        return false;
    }
}

function saveRAMAffiliateKey() {
    console.log({
        func: 'saveRAMAffiliateKey',
    });
    var statPopupArray = new Array();
    var data = $("#RAMAffiliateKeyForm").serialize();
    var PCID = 0;
    PCID = $("#PCID").val();
    $.post("../pops/saveRAMAffiliateKey.php", data, function (theResponse) {

        $("#sessMsg").html('<h4>Affiliate Keys Saved<h4>');
        try {
            document.getElementById('PCAffiliateKeyDiv').innerHTML = theResponse;
        } catch (e) {
        }
        qstr = "PCID=" + PCID;

        /** Popup start **/

        eval("statPopupArray['" + POPSURL + "addRAMAffiliateKey.php']	= new Array('addRAMAffiliateKey.php', 'Add RAM Affiliate(s)', '" + POPSURL + "','saveRAMAffiliateKey.php' , 500, '200')");

        for (key in statPopupArray) {
            ContactPop.init(key, statPopupArray[key][0], statPopupArray[key][1], statPopupArray[key][2], statPopupArray[key][3], statPopupArray[key][4], statPopupArray[key][5]);
        }
        /** Popup end **/

    });
    try {
        ContactPop.hideOverlay(); /** Close- Popup **/
    } catch (e) {
    }
    $('.with-tip, .with-children-tip > *').tip();
}

function deleteRAMAffiliateKey(encSID, rowNumb) {
    console.log({
        func: 'deleteRAMAffiliateKey',
    });
    $('.with-children-tip > *').hideTip();
    var rowNumbIndex = "";
    rowNumbIndex = document.getElementById(rowNumb).rowIndex;
    var url = "../backoffice/deleteRAMAffiliateKey.php";
    var qstr = "SID=" + encSID;
    try {
        xmlDoc = getXMLDoc(url, qstr);
    } catch (e) {
    }
    var table = document.getElementById('PCRAMAffiliateKeyTable').deleteRow(rowNumbIndex);
    try {
        var rsCnt = xmlDoc.getElementsByTagName("rsCnt")[0].firstChild.nodeValue;
    } catch (e) {
    }
    if (rsCnt == 1) {
        document.getElementById("sessMsg").innerHTML = "<h4>Item Deleted<h4>";
    }
}

function addRAMAttorneyKey(encPCID, encSID) {
    console.log({
        func: 'addRAMAttorneyKey',
    });
    /** Popup start **/

    qstr = "encPCID=" + encPCID + "&encSID=" + encSID;
    eval("ContactPop.showOverlay('" + POPSURL + "addRAMAttorneyKey.php')"); /** Open Popup **/
    /** Popup end **/

}

function saveRAMAttorneyKeyForm() {
    console.log({
        func: 'saveRAMAttorneyKeyForm',
    });
    if (validateRAMAttorneyKeyForm()
    ) {
        $('#loaderDiv').show();
        saveRAMAttorneyKey();
        return true;
    } else {
        return false;
    }
}

function validateRAMAttorneyKeyForm() {
    console.log({
        func: 'validateRAMAttorneyKeyForm',
    });
    if (chkIsBlank('RAMAttorneyKeyForm', 'attorneyName', 'Please enter name') &&
        chkIsBlank('RAMAttorneyKeyForm', 'attorneyKey', 'Please enter vendor key') &&
        chkIsBlank('RAMAttorneyKeyForm', 'attorneyKey2', 'Please enter vendor key') &&
        chkIsBlank('RAMAttorneyKeyForm', 'attorneyKey3', 'Please enter vendor key') &&
        chkIsBlank('RAMAttorneyKeyForm', 'attorneyKey4', 'Please enter vendor key') &&
        chkIsBlank('RAMAttorneyKeyForm', 'attorneyKey5', 'Please enter vendor key') &&
        chkIsBlank('RAMAttorneyKeyForm', 'feeGroup', 'Please enter fee group id') &&
        checkValidNumber('RAMAttorneyKeyForm', 'feeGroup', 'Fee Group ID') &&
        chkIsBlank('RAMAttorneyKeyForm', 'feeSplit', 'Please enter fee Split') &&
        chkIsBlank('RAMAttorneyKeyForm', 'RAMAttorneyID', 'Please enter Attorney id') &&
        checkValidNumber('RAMAttorneyKeyForm', 'RAMAttorneyID', 'Attorney ID')
    ) {
        return true;
    } else {
        return false;
    }
}

function saveRAMAttorneyKey() {
    console.log({
        func: 'saveRAMAttorneyKey',
    });
    var statPopupArray = new Array();
    var data = $("#RAMAttorneyKeyForm").serialize();
    var PCID = 0;
    PCID = $("#PCID").val();
    $.post("../pops/saveRAMAttorneyKey.php", data, function (theResponse) {

        $("#sessMsg").html('<h4>Attorney Keys Saved<h4>');
        try {
            document.getElementById('PCAttorneyKeyDiv').innerHTML = theResponse;
        } catch (e) {
        }
        qstr = "PCID=" + PCID;

        /** Popup start **/

        eval("statPopupArray['" + POPSURL + "addRAMAttorneyKey.php']	= new Array('addRAMAttorneyKey.php', 'Add RAM Attorney(s)', '" + POPSURL + "','saveRAMAttorneyKey.php' , 500, '200')");

        for (key in statPopupArray) {
            ContactPop.init(key, statPopupArray[key][0], statPopupArray[key][1], statPopupArray[key][2], statPopupArray[key][3], statPopupArray[key][4], statPopupArray[key][5]);
        }
        /** Popup end **/

    });
    try {
        ContactPop.hideOverlay(); /** Close- Popup **/
    } catch (e) {
    }
    $('.with-tip, .with-children-tip > *').tip();
}

function deleteRAMAttorneyKey(encSID, rowNumb) {
    console.log({
        func: 'deleteRAMAttorneyKey',
    });
    $('.with-children-tip > *').hideTip();
    var rowNumbIndex = "";
    rowNumbIndex = document.getElementById(rowNumb).rowIndex;
    var url = "../backoffice/deleteRAMAttorneyKey.php";
    var qstr = "SID=" + encSID;
    try {
        xmlDoc = getXMLDoc(url, qstr);
    } catch (e) {
    }
    var table = document.getElementById('PCRAMAttorneyKeyTable').deleteRow(rowNumbIndex);
    try {
        var rsCnt = xmlDoc.getElementsByTagName("rsCnt")[0].firstChild.nodeValue;
    } catch (e) {
    }
    if (rsCnt == 1) {
        document.getElementById("sessMsg").innerHTML = "<h4>Item Deleted<h4>";
    }
}

function addRAMFeeGroups(encPCID, encSID) {
    console.log({
        func: 'addRAMFeeGroups',
    });
    /** Popup start **/

    qstr = "encPCID=" + encPCID + "&encSID=" + encSID;
    eval("ContactPop.showOverlay('" + POPSURL + "addRAMFeeGroups.php')"); /** Open Popup **/
    /** Popup end **/

}

function saveRAMFeeGroupsForm() {
    console.log({
        func: 'saveRAMFeeGroupsForm',
    });
    if (validateRAMFeeGroupsForm()
    ) {
        $('#loaderDiv').show();
        saveRAMFeeGroups();
        return true;
    } else {
        return false;
    }
}

function validateRAMFeeGroupsForm() {
    console.log({
        func: 'validateRAMFeeGroupsForm',
    });
    if (chkIsBlank('RAMFeeGroupsForm', 'programName', 'Please enter Program') &&
        chkIsBlank('RAMFeeGroupsForm', 'retainerPCFeeSplit', 'Please enter Fee Split for PC') &&
        checkValidNumber('RAMFeeGroupsForm', 'retainerPCFeeSplit', 'Fee Split for PC') &&
        chkIsBlank('RAMFeeGroupsForm', 'retainerAttorneyFeeSplit', 'Please enter Fee Split for Attorney') &&
        checkValidNumber('RAMFeeGroupsForm', 'retainerAttorneyFeeSplit', 'Fee Split for Attorney') &&
        chkIsBlank('RAMFeeGroupsForm', 'retainerAffiliateFeeSplit', 'Please enter Fee Split for Affiliate') &&
        checkValidNumber('RAMFeeGroupsForm', 'retainerAffiliateFeeSplit', 'Fee Split for Affiliate') &&
        chkIsBlank('RAMFeeGroupsForm', 'recurringPCFeeSplit', 'Please enter Fee Split for PC') &&
        checkValidNumber('RAMFeeGroupsForm', 'recurringPCFeeSplit', 'Fee Split for PC') &&
        chkIsBlank('RAMFeeGroupsForm', 'recurringAttorneyFeeSplit', 'Please enter Fee Split for Attorney') &&
        checkValidNumber('RAMFeeGroupsForm', 'recurringAttorneyFeeSplit', 'Fee Split for Attorney') &&
        chkIsBlank('RAMFeeGroupsForm', 'recurringAffiliateFeeSplit', 'Please enter Fee Split for Affiliate') &&
        checkValidNumber('RAMFeeGroupsForm', 'recurringAffiliateFeeSplit', 'Fee Split for Affiliate')
    ) {
        return true;
    } else {
        return false;
    }
}

function saveRAMFeeGroups() {
    console.log({
        func: 'saveRAMFeeGroups',
    });
    var statPopupArray = new Array();
    var data = $("#RAMFeeGroupsForm").serialize();
    var PCID = 0;
    PCID = $("#PCID").val();
    $.post("../pops/saveRAMFeeGroupInfo.php", data, function (theResponse) {

        $("#sessMsg").html('<h4>Fee Group Saved<h4>');
        try {
            document.getElementById('PCRAMFeeGroupKeyTable').innerHTML = theResponse;
        } catch (e) {
        }
        qstr = "PCID=" + PCID;

        /** Popup start **/

        eval("statPopupArray['" + POPSURL + "addRAMFeeGroups.php']	= new Array('addRAMFeeGroups.php', 'Add RAM Calculators (Programs)', '" + POPSURL + "','saveRAMFeeGroupInfo.php' , 500, '200')");

        for (key in statPopupArray) {
            ContactPop.init(key, statPopupArray[key][0], statPopupArray[key][1], statPopupArray[key][2], statPopupArray[key][3], statPopupArray[key][4], statPopupArray[key][5]);
        }
        /** Popup end **/

    });
    try {
        ContactPop.hideOverlay(); /** Close- Popup **/
    } catch (e) {
    }
    $('.with-tip, .with-children-tip > *').tip();
}

function deleteRAMFeeGroupInfo(encSID, rowNumb) {
    console.log({
        func: 'deleteRAMFeeGroupInfo',
    });
    $('.with-children-tip > *').hideTip();
    var rowNumbIndex = "";
    rowNumbIndex = document.getElementById(rowNumb).rowIndex;
    var url = "../backoffice/deleteRAMFeeGroupInfo.php";
    var qstr = "SID=" + encSID;
    try {
        xmlDoc = getXMLDoc(url, qstr);
    } catch (e) {
    }
    var table = document.getElementById('PCRAMFeeGroupKeyTable').deleteRow(rowNumbIndex);
    try {
        var rsCnt = xmlDoc.getElementsByTagName("rsCnt")[0].firstChild.nodeValue;
    } catch (e) {
    }
    if (rsCnt == 1) {
        document.getElementById("sessMsg").innerHTML = "<h4>Item Deleted<h4>";
    }
}

function clickToSelectAllModuleServices(chk) {
    console.log({
        func: 'clickToSelectAllModuleServices',
    });

    var moduleCodeArray = new Array();
    var moduleCode = '';
    if (chk) {
        var confirmMsg = confirm("Are you sure you want to activate all possible services for your files?");
    } else {
        var confirmMsg = confirm("Are you sure you want to deactivate all possible services for your files?");
    }
    if (confirmMsg) {

        var objLen = 0, t = 0, fld = false, fldVal = '';
        try {
            eval("objLen = document.processingCompanyForm.moduleType.length");
        } catch (e) {
        }

        if (objLen > 0) {
            for (var i = 0; i < objLen; i++) {
                fld = false, fldVal = '';
                eval("fld = document.processingCompanyForm.moduleType.options[" + i + "].selected");
                if (fld) {
                    eval("fldVal = document.processingCompanyForm.moduleType.options[" + i + "]" + ".value");
                    if (t == 0) {
                        moduleCode = fldVal;
                    } else {
                        moduleCode += ", " + fldVal;
                    }
                    t++;
                }
            }
        } else {
            eval("var fld = document.processingCompanyForm.moduleType.selected");
            if (fld) {
                eval("moduleCode = document.processingCompanyForm.moduleType.value");
            }
        }

        if (moduleCode != '') {
            moduleCodeArray = moduleCode.split(", ");
        }
        for (var i = 0; i < moduleCodeArray.length; i++) {
            var moduleCode = '', moduleName = '';
            var servicesCnt = 0;
            var opt = false;
            moduleCode = moduleCodeArray[i];
            if (chk) {
                opt = true;
            }
            servicesCnt = eval("document.getElementById('" + moduleCode + "_Cnt').value");
            for (var j = 0; j < servicesCnt; j++) {
                eval("document.getElementById('services_" + moduleCode + "_" + j + "').checked = " + opt);
            }
        }
    } else {
        eval("document.getElementById('checkAllFields').checked = false");
    }
}

/* PC Checklist Module Services */

function checklistModuleServices(formName, srcName, targetName) {
    console.log({
        func: 'checklistModuleServices',
    });
    var checklistModule = '', xmlDoc = '', serviceDataArray = new Array();
    var checklistServiceArray = new Array();
    var PCID = 0;

    PCID = $("#assignedPCID").val();
    var checklistServiceArray = getNewObject(targetName);
    checklistServiceArray.options[0] = new Option("- All -", "", false);

    try {
        eval("checklistModule = document." + formName + "." + srcName + ".value");
    } catch (e) {
    }


    if (checklistModule != "") {
        var url = "../backoffice/getChecklistModuleServices.php";
        var qstr = "moduleCode=" + checklistModule + "&PCID=" + PCID;
        try {
            xmlDoc = getXMLDoc(url, qstr);
        } catch (e) {
        }
        try {
            serviceDataArray = xmlDoc.getElementsByTagName("serviceData");
        } catch (e) {
        }

        for (var c = 0; c < serviceDataArray.length; c++) {
            var serviceCode = "", serviceType = '';
            try {
                serviceCode = serviceDataArray[c].getElementsByTagName("serviceCode")[0].firstChild.nodeValue;
            } catch (e) {
            }
            try {
                serviceType = serviceDataArray[c].getElementsByTagName("serviceType")[0].firstChild.nodeValue;
            } catch (e) {
            }

            checklistServiceArray.options[c + 1] = new Option(serviceType, serviceCode, false, false);
        }
    }
}

/**

 Description : edit the custom service Type
 Date        : May 03, 2017
 Author        : Viji & Venkatesh

 **/

/*
function editService(PCID, STID, moduleCode) {

    popupArray = new Array();
    qstr = "STID=" + STID + "&PCProfile=PCProfile&MC=" + moduleCode + "&PCID=" + PCID;
    eval("popupArray['" + POPSURL + "editService.php'] = new Array('editService.php', 'Add / Edit Service Type', '" + POPSURL + "','saveServiceType.php', 600, '550')");

    for (key in popupArray) {
        ContactPop.init(key, popupArray[key][0], popupArray[key][1], popupArray[key][2], popupArray[key][3], popupArray[key][4], popupArray[key][5]);
    }

    eval("ContactPop.showOverlay('" + POPSURL + "editService.php')");
}
*/

/**

 Description : edit the custom service Type New UI update
 Date        : May 12, 2021
 Author      : Murali Krishna

 **/
function editService(PCID, STID, moduleCode) {
    console.log({
        func: 'editService',
    });
    var qstr = "STID=" + STID + "&PCProfile=PCProfile&MC=" + moduleCode + "&PCID=" + PCID;
    var _title = "Edit Service Type";
    var _url = POPSURL + "editService.php?" + qstr;

    modalCall(_title, 'modal-xl', _url);
}

/**

 Description : edit the custom service Type
 Date        : May 04, 2017
 Author        : Viji & Venkatesh

 **/
function deleteCustomServiceType(serviceCode, PCID, STID) {
    console.log({
        func: 'deleteCustomServiceType',
    });

    if (serviceCode != "") {
        var url = "../backoffice/getCustomServicesTypeCnt.php";
        var qstr = "serviceCode=" + serviceCode + "&PCID=" + PCID;
        try {
            xmlDoc = getXMLDoc(url, qstr);
        } catch (e) {
        }
        try {
            serviceDataArray = xmlDoc.getElementsByTagName("customServices");
        } catch (e) {
        }

        for (var c = 0; c < serviceDataArray.length; c++) {
            var NoOfServiceCnt = 0;
            try {
                NoOfServiceCnt = serviceDataArray[c].getElementsByTagName("NoOfServiceCnt")[0].firstChild.nodeValue;
            } catch (e) {
            }
        }
    }
    if (NoOfServiceCnt > 0) {
        var confirmMsg = "You currently have " + (NoOfServiceCnt) + " files using this service. We highly recommend replacing the service on these files before deleting. Are you sure you wish to proceed?";
    } else {
        var confirmMsg = "Are you sure to delete this service type.";
    }

    $.confirm({
        icon: 'fa fa-warning',
        closeIcon: true,
        title: 'Confirm',
        content: confirmMsg,
        type: 'red',
        backgroundDismiss: true,
        buttons: {
            Yes: function () {
                var url = "../backoffice/deleteServiceType.php";
                var qstr = "serviceCode=" + serviceCode + "&opt=customST";

                try {
                    xmlDoc = getXMLDoc(url, qstr);
                } catch (e) {
                }

                try {
                    rsCnt = xmlDoc.getElementsByTagName("delCnt")[0].firstChild.nodeValue;
                } catch (e) {
                }
                if (rsCnt > 0) {
                    toastrNotification('Successfully deleted', 'success');
                    $(".cls_" + STID).remove();
                }
            },
            Cancel: function () {

            }
        },
    });
}

/**

 ** Description    : Validate the Merchant Funding Loan Terms Tab and also save functionality
 ** Developer    : Viji & Venkatesh
 ** Date            : May 26, 2017

 **/

function validatePCBasicLoanTerms() {
    console.log({
        func: 'validatePCBasicLoanTerms',
    });

    if (
        isMultiCheckboxSelected('addBasicLoanTermForm', 'loanPgm', 'Please select any one Loan Program') &&
        marketPlaceCheck() &&
        validatedMaxMinRange('LoanAmount') && validatedMaxMinRange('Rate') &&
        //validatedMaxMinRange('Fico') &&
        // validatedMaxMinRange('FixFlop') &&
        //  validatedMaxMinRange('GrndConst') &&
        validateLoanProgramDescription('loanPgmDetails') &&
        validateAdditionalTermsDescription('reqForLoanProUnderwriting')
    ) {

        return true;
    } else {
        return false;
    }
}

function marketPlaceCheck() {
    console.log({
        func: 'marketPlaceCheck',
    });
    var selValue = $('input[name=enableMarketPlaceForGuidelines]:checked').val();

    if (selValue == '1') {

        if (isMultiCheckboxSelected('addBasicLoanTermForm', 'marketLoanProgram', 'Please select any one Marketplace Loan category') &&

            validateCustomLoanValue('minLoanAmount', "Please Enter Minimun Loan Amount") &&
            validateCustomLoanValue('maxLoanAmount', "Please Enter Maximum Loan Amount") &&
            validateCustomLoanValueRate('minRate', "Please Enter Minimun Rate") &&
            validateCustomLoanValueRate('maxRate', "Please Enter Maximum Rate") &&
            validateCustomLoanValueRateAllowZero('minFico', "Please Enter Minimum Fico") &&
            validateCustomLoanValueRateAllowZero('minFixFlop', "Please Enter Min # of properties completed for fix and flip") &&
            //validateCustomLoanValueRate('maxFixFlop', "Please Enter Max # of properties completed for fix and flip\t") &&
            validateCustomLoanValueRateAllowZero('minGrndConst', "Please Enter Min # of properties completed for ground up construction\t") &&
            //validateCustomLoanValueRate('maxGrndConst', "Please Enter Max # of properties completed for ground up construction\t") &&

            validateCustomLoanValueRateAllowZero('minPoints', "Please Enter Min Origination Points") &&
            validateCustomLoanValueRate('maxPoints', "Please Enter Max Origination Points") &&

            isMultiCheckboxSelected('addBasicLoanTermForm', 'propertyType', 'Please select any one Property Type') &&
            isMultiCheckboxSelected('addBasicLoanTermForm', 'loanTerm', 'Please select any one Loan Term Options') &&
            isMultiCheckboxSelected('addBasicLoanTermForm', 'occupancy', 'Please select any one Borrower Occupancy Options') &&

            isMultiCheckboxSelected('addBasicLoanTermForm', 'state', 'Please select any one State') &&
            validateCustomLoanValueRate('totalLTC', "Please Enter % Total Loan to Cost") &&
            isMultiCheckboxSelectedCount('addBasicLoanTermForm', 'branchesId,brokersId,employeesId,contactsId', 'Please Select Atleast One Users To Notify')
        ) {

            return true;
        } else {
            return false;
        }
    } else {
        if (validateCustomLoanValueRate('minPoints', "Please Enter Min Origination Points")) {
            return true;
        } else {
            return false;
        }
    }
}

function validateCustomLoanValue(inputId, errorMsg) {
    console.log({
        func: 'validateCustomLoanValue',
    });

    if ($('#' + inputId).prop('disabled') === false) {
        tmValue = $('#' + inputId).val().trim();
        if (tmValue == '' || tmValue == '0.00') {
            $('#' + inputId).focus();
            toastrNotification(errorMsg, 'error');
            return false;
        } else {
            return true;
        }
    } else {
        return true
    }

}

function validateCustomLoanValueRate(inputId, errorMsg) {
    console.log({
        func: 'validateCustomLoanValueRate',
    });

    if ($('#' + inputId).prop('disabled') === false) {
        tmValue = $('#' + inputId).val().trim();
        if (tmValue == '' || tmValue == '0' || tmValue == '0.00') {
            $('#' + inputId).focus();
            toastrNotification(errorMsg, 'error');
            return false;
        } else {
            return true;
        }
    } else {
        return true
    }
}

function validateCustomLoanValueRateAllowZero(inputId, errorMsg) {
    console.log({
        func: 'validateCustomLoanValueRateAllowZero',
    });
    tmValue = $('#' + inputId).val().trim();
    if (tmValue == '') {
        if ($('#' + inputId).is(":visible")) {
            $('#' + inputId).focus();
            toastrNotification(errorMsg, 'error');
            return false;
        } else {
            return true;
        }
    } else {
        return true;
    }
}


function validateLoanProgramDescription(loanPgmDetailsId) {
    console.log({
        func: 'validateLoanProgramDescription',
    });

    //loanPgmDetailstxt = $('#' + loanPgmDetailsId).val().trim();
    loanPgmDetailstxt = tinyMCE.get(loanPgmDetailsId).getContent();
    if (loanPgmDetailstxt == '') {
        toastrNotification("Please Enter Loan Program Description", 'error');
        tinyMCE.get(loanPgmDetailsId).focus();
        tinyMCE.execCommand('mceInsertContent', false, '');

        return false;
    } else {
        return true;
    }
}


function validatedMaxMinRange(inputId) {
    console.log({
        func: 'validatedMaxMinRange',
    });
    var min = '', max = '', eMsg = '', TLC = '';
    min = replaceCommaValues($('#min' + inputId).val());
    max = replaceCommaValues($('#max' + inputId).val());
    TLC = replaceCommaValues($('#totalLTC').val());

    if (inputId == 'LoanAmount') {
        eMsg = 'Max Loan Amount should be greater than from Min Loan Amount';
    } else if (inputId == 'Rate') {
        eMsg = 'Max Rate should be greater than from Min Rate';
    }

    toastr.options = {
        "positionClass": "toast-center-center",
        "closeButton": true,
        "showDuration": "5000",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "5000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut",
        "allowHtml": true,
    }

    if (parseFloat(min) > parseFloat(max)) {
        $('#max' + inputId).focus();
        toastr.error(eMsg);
        return false;
    } else if ((TLC < 1 || TLC > 100) && TLC != '') {
        if ($('#totalLTC').prop('disabled') === false) {
            $('#totalLTC').focus();
            toastr.error('Total Loan to Cost should be 1 to 100 percent');
            return false;
        } else {
            return true;
        }

    } else {
        return true;
    }
}

function savePCBasicLoanTerm() {
    console.log({
        func: 'savePCBasicLoanTerm',
    });
    document.getElementById('loaderDiv').style.display = "none";
    if (validatePCBasicLoanTerms()
    ) {
        document.getElementById('loaderDiv').style.display = "block";
        return true;
    } else {
        return false;
    }
}

function deleteBasicLoanTerm(BLID, PCID) {
    console.log({
        func: 'deleteBasicLoanTerm',
    });

    $.confirm({
        icon: 'fa fa-warning',
        closeIcon: true,
        title: 'Confirm',
        content: "Are you sure you want to delete?",
        type: 'red',
        backgroundDismiss: true,
        buttons: {
            Yes: function () {
                var rsCnt = 0;
                var url = "../backoffice/deletePCBasicLoanTerm.php";
                var qstr = "BLID=" + BLID + "&PCID=" + PCID;
                try {
                    xmlDoc = getXMLDoc(url, qstr);
                } catch (e) {
                }
                window.location.reload();
            },
            Cancel: function () {

            }
        },
    });
}

function savePCCustomNiches(obj, newVal) {
    console.log({
        func: 'savePCCustomNiches',
    });

    document.getElementById('loaderImg1').style.display = "block";

    setTimeout(saveHMLONiches(obj, newVal), 500);
}

function saveHMLONiches(obj, newVal) {
    console.log({
        func: 'saveHMLONiches',
    });
    var newNicheID = 0;

    PCID = document.addBasicLoanTermForm.encryptedPCID.value;

    var url = "../backoffice/savePCCustomNiches.php";
    var qstr = "niches=" + newVal + "&PCID=" + PCID;

    try {
        xmlDoc = getXMLDoc(url, qstr);
    } catch (e) {
    }

    try {
        newNicheID = xmlDoc.getElementsByTagName("nichesID")[0].firstChild.nodeValue;
    } catch (e) {
    }

    if (newNicheID > 0) {
        obj.select_append_option({
            value: newNicheID,
            text: newVal
        });
        setTimeout(function () {
            document.getElementById('loaderImg1').style.display = "none";
        }, 500);
    }


}

function validateBranchUploadDocumentForm() {
    console.log({
        func: 'validateBranchUploadDocumentForm',
    });
    returnOpt = true;
    for (var i = 1; i < 2; i++) {
        var path = $("#fileSrc_" + i).val();
        var path_len = path.length;
        var file_length = path.lastIndexOf('\\');
        var fileName = path.substring(file_length + 1, path_len);

        if (path == "") {
            //alert("Select the document");

            toastrNotification('Select the document', 'error');
            $("form").submit(function (e) {
                e.preventDefault();
            });
            try {
                $("#fileSrc_" + i).focus();
            } catch (e) {
            }
            $("#fileSrc_" + i).addClass('highlights');
            return false;
        }
    }
    var i = 1;
    for (var i = 1; i <= 5; i++) {
        var path = $("#fileSrc_" + i).val();
        var path_len = path.length;
        var file_length = path.lastIndexOf('\\');
        var fileName = path.substring(file_length + 1, path_len);

        if (path != '') {
            file_extension = path.lastIndexOf('.');
            file_ext_string = path.substring(file_extension + 1, path_len);
            if ((file_ext_string == "pdf") || (file_ext_string == "PDF") || (file_ext_string == "doc") || (file_ext_string == "DOC") ||
                (file_ext_string == "docx") || (file_ext_string == "DOCX") || (file_ext_string == "xls") || (file_ext_string == "XLS") ||
                (file_ext_string == "xlsx") || (file_ext_string == "XLSX")) {
            } else {
                //alert ("File types  allowed are pdf, doc, docx, xls, xlsx.");
                toastrNotification('File types  allowed are pdf, doc, docx, xls, xlsx.', 'error');
                $("form").submit(function (e) {
                    e.preventDefault();
                });
                return false;
            }
        }
    }
    if (i > 1) {
        $('#PCDocSubmit').val('PCDocSubmit');
        $("#pkgForm")[0].submit();
    }
    return true;
}


/**
 * Description: Branch Mandatory Checklist Items
 * Date      : Dec 11, 2017
 * PT #      : 153211423
 * Functions : checklistMandatoryForBranchWebForms(executiveId, checklistId, allowStatus, opt).
 * JQFiles   : checklistMandatoryForBranchWebForms.php.
 * Added By  : Suresh K
 **/

function checklistMandatoryForBranchWebForms(executiveId, checklistId, allowStatus, opt) {
    console.log({
        func: 'checklistMandatoryForBranchWebForms',
    });
    var displayText = '';
    $.ajax({
        type: 'POST',
        url: '/JQFiles/checklistMandatoryForBranchWebForms.php',
        data: jQuery.param({
            'executiveId': executiveId,
            'checklistId': checklistId,
            'allowStatus': allowStatus,
            'opt': opt
        }),
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        success: function (status) {
            if (status > 0) {
                if (allowStatus == 1) {
                    displayText = "<a class=\"fa fa-check icon-green fa-2x tip-bottom\" title=\"Click to make it not Mandatory\" onclick=\"checklistMandatoryForBranchWebForms('" + executiveId + "', '" + checklistId + "', '0', '" + opt + "');\" style=\"text-decoration:none; cursor: pointer;\"></a>";
                } else {
                    displayText = "<a class=\"icon-red fa fa-times fa-2x tip-bottom\" title=\"Click to make it Mandatory.\" onclick=\"checklistMandatoryForBranchWebForms('" + executiveId + "', '" + checklistId + "', '1', '" + opt + "');\" style=\"text-decoration:none; cursor: pointer;\"></a>";
                }
                $('.with-children-tip > *').hideTip();
                var id = opt + '_' + checklistId;
                document.getElementById(id).innerHTML = displayText;
            }
        }
    });
}

function savePCCustomFormFields(fieldID, sectionId, opt) {
    console.log({
        func: 'savePCCustomFormFields',
    });

    $("#sessMsg").html('');

    var pcId = '';
    var tabName = '';
    var fieldLabel = '';
    var fieldName = '';
    var fieldIds = [];
    var fieldLabels = [];
    var fieldNames = [];
    var option = 0;
    var Mandatory = 0;

    pcId = $('#pcId').val();
    tabName = $('#tabName').val();

    if (opt == 'CheckAll') {

        if ($("#" + sectionId).prop('checked')) {
            option = 1;
            Mandatory = 1;
        }

        jQuery(".check" + sectionId).each(function () {
            var tFieldId = 0;
            var tFieldLabel = '';
            var tFieldName = '';
            tFieldId = $(this).val();                                                          //  | Get Field Id.
            tFieldLabel = $("#fieldLabel_" + tFieldId).html();                                      //  | Get Field Label.
            tFieldName = $(this).attr('name');                                                   //  | Get Field Name.
            fieldIds.push(tFieldId);                                                                //  | Push Field Id.
            fieldLabels.push(tFieldLabel);                                                          //  | Push Field Label.
            fieldNames.push(tFieldName);                                                            //  | Push Field Name.
        });

    } else {

        if ($("#" + fieldID).prop('checked')) {
            option = 1;
        }
        tFieldLabel = $("#fieldLabel_" + fieldID).html();                                       //  | Get Field Label for Single.
        tFieldName = $("#" + fieldID).attr('name');                                            //  | Get Field Name for Single.
        fieldIds.push(fieldID);                                                                 //  | Push Field Id for Single.
        fieldLabels.push(tFieldLabel);                                                          //  | Push Field Label for Single.
        fieldNames.push(tFieldName);
        if ($("#" + fieldID + "Mandatory").prop('checked')) {
            Mandatory = 1;
        }

    }

    $.ajax({
        type: 'POST',
        url: '/JQFiles/savePCCustomFormFileds.php',
        data: jQuery.param({
            'pcId': pcId, 'fieldID': fieldIds, 'sectionID': sectionId, 'fieldLabel': fieldLabels,
            'fieldName': fieldNames, 'tabName': tabName, 'Mandatory': Mandatory, 'opt': option
        }),
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        success: function (fieldItems) {
            $("#sessMsg").html('<h4>Updated Successfully<h4>');
            $('.with-tip, .with-children-tip > *').tip();
        }
    });
    // alert(msg);
    toastrNotification(msg, 'error');
}

function saveQuickAppCustomFormFields(fieldID, sectionId, opt) {

    console.log({
        func: 'saveQuickAppCustomFormFields',
    });
    $("#sessMsg").html('');

    var pcId = '';
    var tabName = '';
    var fieldLabel = '';
    var fieldName = ftValues = UG = UN = '';
    var fieldIds = [];
    var fieldLabels = [];
    var fieldNames = [];
    var option = 0;
    var Mandatory = 0;
    var fullApp = backoffice = BOMandatory = 0;
    var fullAppMandatory = 0;
    var quickApp = 0;
    var subFieldIds = [];
    var fullAppSel = quickAppSel = backofficeSel = 0;
    var chkAllArray = [];
    var hideBorrowerInfo = 0;

    pcId = $('#pcId').val();
    UG = $('#userGroup').val();
    UN = $('#userName').val();
    pcId = $('#pcId').val();
    tabName = $('#tabName').val();
    ftValues = $("#fileModule_" + fieldID).chosen().val();

    if (opt == 'resetAll') {
        option = 2;
    }

    if (opt == 'HBInfo') {
        option = 4;
        hideBorrowerInfo = sectionId;
    }

    if (opt == 'fileType') {
        option = 5;
        ftValues = $("#fileModule_" + fieldID).chosen().val();
        fieldIds.push(fieldID);
    } else if (opt == 'resetSection') {
        option = 3;
    } else if (opt == 'CheckAllFA' || opt == 'CheckAllQA' || opt == 'CheckAllBO') {

        option = 1;
        if ($("#" + sectionId + "FA").prop('checked')) fullAppSel = 1;
        if ($("#" + sectionId + "BO").prop('checked')) backofficeSel = 1;

        if ($("#" + sectionId).prop('checked')) quickAppSel = 1;
        jQuery(".check" + sectionId).each(function () {
            var tFieldId = 0;
            var tFieldLabel = '';
            var tFieldName = ftv = '';
            var tArr = [];
            var fD = qD = bD = fM = qM = bM = 0;
            tFieldId = $(this).val();                                                          //  | Get Field Id.
            tFieldLabel = $("#fieldLabel_" + tFieldId).html();                                      //  | Get Field Label.
            tFieldName = $(this).attr('name');

            if ($("#" + tFieldId + "FA").prop('checked')) fD = 1;                                         // | Current Display State
            if ($("#" + tFieldId + "QA").prop('checked')) qD = 1;                                         // | Current Display State
            if ($("#" + tFieldId + "BO").prop('checked')) bD = 1;                                         // | Current Display State
            if ($("#" + tFieldId + "FAMandatory").prop('checked')) fM = 1;                                // | Current Mandatory State
            if ($("#" + tFieldId + "QAMandatory").prop('checked')) qM = 1;                                // | Current Mandatory State
            if ($("#" + tFieldId + "BOMandatory").prop('checked')) bM = 1;                                // | Current Mandatory State
            ftv = $("#fileModule_" + tFieldId).chosen().val();

            if (opt == 'CheckAllFA') {
                if (fullAppSel == 1) {
                    var tArr = {
                        fId: tFieldId,
                        FAD: 1,
                        QAD: qD,
                        BOD: bD,
                        FAM: fM,
                        QAM: qM,
                        BOM: bM,
                        fldN: tFieldName,
                        FTV: ftv
                    };
                } else {
                    var tArr = {
                        fId: tFieldId,
                        FAD: 0,
                        QAD: qD,
                        BOD: bD,
                        FAM: 0,
                        QAM: qM,
                        BOM: bM,
                        fldN: tFieldName,
                        FTV: ftv
                    };
                }
            }
            if (opt == 'CheckAllQA') {
                if (quickAppSel == 1) {
                    var tArr = {
                        fId: tFieldId,
                        FAD: fD,
                        QAD: 1,
                        BOD: bD,
                        FAM: fM,
                        QAM: qM,
                        BOM: bM,
                        fldN: tFieldName,
                        FTV: ftv
                    };
                } else {
                    var tArr = {
                        fId: tFieldId,
                        FAD: fD,
                        QAD: 0,
                        BOD: bD,
                        FAM: fM,
                        QAM: 0,
                        BOM: bM,
                        fldN: tFieldName,
                        FTV: ftv
                    };
                }
            }
            if (opt == 'CheckAllBO') {
                if (backofficeSel == 1) {
                    var tArr = {
                        fId: tFieldId,
                        FAD: fD,
                        QAD: qD,
                        BOD: 1,
                        FAM: fM,
                        QAM: qM,
                        BOM: bM,
                        fldN: tFieldName,
                        FTV: ftv
                    };
                } else {
                    var tArr = {
                        fId: tFieldId,
                        FAD: fD,
                        QAD: qD,
                        BOD: 0,
                        FAM: fM,
                        QAM: qM,
                        BOM: 0,
                        fldN: tFieldName,
                        FTV: ftv
                    };
                }
            }
            chkAllArray.push(tArr);                                                                 //  | Check All Array.

            fieldIds.push(tFieldId);                                                                //  | Push Field Id.
            fieldLabels.push(tFieldLabel);                                                          //  | Push Field Label.
            fieldNames.push(tFieldName);                                                            //  | Push Field Name.
        });

    } else {

        if ($("#" + fieldID + "FA").prop('checked')) option = 1;
        if ($("#" + fieldID + "QA").prop('checked')) option = 1;
        if ($("#" + fieldID + "BO").prop('checked')) option = 1;
        if ($("#" + fieldID + "FAMandatory").prop('checked')) option = 1;
        if ($("#" + fieldID + "QAMandatory").prop('checked')) option = 1;
        if ($("#" + fieldID + "BOMandatory").prop('checked')) option = 1;

        tFieldLabel = $("#fieldLabel_" + fieldID).html();                                       //  | Get Field Label for Single.
        tFieldName = $("#" + fieldID + "FA").attr('name');                                            //  | Get Field Name for Single.
        fieldIds.push(fieldID);                                                                 //  | Push Field Id for Single.
        fieldLabels.push(tFieldLabel);                                                          //  | Push Field Label for Single.
        fieldNames.push(tFieldName);

        if ($("#" + fieldID + "FA").prop('checked')) fullApp = 1;
        if ($("#" + fieldID + "QA").prop('checked')) quickApp = 1;
        if ($("#" + fieldID + "BO").prop('checked')) backoffice = 1;
        if ($("#" + fieldID + "FAMandatory").prop('checked')) {
            fullAppMandatory = 1;
            option = 1;
            fullApp = 1;
            if ($("#" + fieldID + "FA").prop('checked', 'true')) ;
        }
        if ($("#" + fieldID + "QAMandatory").prop('checked')) {
            Mandatory = 1;
            option = 1;
            quickApp = 1;
            if ($("#" + fieldID + "QA").prop('checked', 'true')) ;
        }
        if ($("#" + fieldID + "BOMandatory").prop('checked')) {
            BOMandatory = 1;
            option = 1;
            backoffice = 1;
            if ($("#" + fieldID + "BO").prop('checked', 'true')) ;
        }

        if (fullApp == 0) {
            jQuery(".PR_" + fieldID + "_FA").each(function () {
                var tFieldId = 0;
                var tFieldLabel = '';
                var tFieldName = '';
                tFieldId = $(this).val();                                                          //  | Get Field Id.
                subFieldIds.push(tFieldId);                                                                //  | Push Field Id.
                $(this).prop("checked", false);                                                          //  | Unchecked field Val
                $("#" + tFieldId + "FAMandatory").prop("checked", false);                                    //  | Unchecked field Val
            });
        }

        if (quickApp == 0) {
            jQuery(".PR_" + fieldID + "_QA").each(function () {
                var tFieldId = 0;
                tFieldId = $(this).val();                                                          //  | Get Field Id.
                subFieldIds.push(tFieldId);                                                                //  | Push Field Id.
                $(this).prop("checked", false);                                                          //  | Unchecked field Val
                $("#" + tFieldId + "QAMandatory").prop("checked", false);                                    //  | Unchecked field Val
            });
        }

        if (backoffice == 0) {
            jQuery(".PR_" + fieldID + "_BO").each(function () {
                var tFieldId = 0;
                tFieldId = $(this).val();                                                          //  | Get Field Id.
                subFieldIds.push(tFieldId);                                                                //  | Push Field Id.
                $(this).prop("checked", false);                                                          //  | Unchecked field Val
                $("#" + tFieldId + "BOMandatory").prop("checked", false);                                    //  | Unchecked field Val
            });
        }

    }

    $.ajax({
        type: 'POST',
        url: '/JQFiles/saveQuickAppCustomFormFields.php',
        data: jQuery.param({
            'pcId': pcId,
            'fieldID': fieldIds,
            'sectionID': sectionId,
            'fieldLabel': fieldLabels,
            'fieldName': fieldNames,
            'tabName': tabName,
            'fullApp': fullApp,
            'quickApp': quickApp,
            'backoffice': backoffice,
            'fullAppMandatory': fullAppMandatory,
            'Mandatory': Mandatory,
            'BOMandatory': BOMandatory,
            'opt': option,
            'subFieldIds': subFieldIds,
            'fileModuleTypes': ftValues,
            'chkAllArray': chkAllArray,
            'hideBorrowerInfo': hideBorrowerInfo,
            'UG': UG,
            'UN': UN
        }),
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        success: function (fieldItems) {
            $("#sessMsg").html('Updated Successfully');
            //$('.with-tip, .with-children-tip > *').tip();
            if (option == 2 || option == 3) {
                location.reload(true);
            }
        }
    });
    try {
        toastrNotification(msg, 'error');
    } catch (e) {
    }
}

function saveQuickAppCustomFormFieldsNew(fieldID, sectionId, opt) {
    console.log({
        func: 'saveQuickAppCustomFormFieldsNew',
    });
    var pcId = $('#pcId').val();
    var UG = $('#userGroup').val();
    var UN = $('#userName').val();
    var chkAllArray = [];

    if (opt == 'resetSection') {  /*   on section wise reset to default button click */
        option = 3;
    } else if (opt == 'resetAll') {  /* on clicking the reset all button in the page*/
        option = 2;
    } else if (opt == 'HBInfo') {
        option = 4;
        hideBorrowerInfo = sectionId;
    } else {  /*   on section wise save button click */
        option = 1;
        jQuery(".check" + sectionId).each(function () {
            var tFieldId = 0;
            var tFieldLabel = '';
            var tFieldName = '';
            var tArr = [];
            var fD = qD = bD = fM = qM = bM = 0;
            let enableQAUniqueLoanPrograms = 0;
            let enableFAUniqueLoanPrograms = 0;
            let enableAllLoanPrograms = 0;
            tFieldId = $(this).val();
            // console.log(tFieldId);
            if ($("#" + tFieldId + "FA").prop('checked')) fD = 1;                                         // | Current Display State
            if ($("#" + tFieldId + "QA").prop('checked')) qD = 1;                                         // | Current Display State
            if ($("#" + tFieldId + "BO").prop('checked')) bD = 1;                                         // | Current Display State
            if ($("#" + tFieldId + "FAMandatory").prop('checked')) fM = 1;                                // | Current Mandatory State
            if ($("#" + tFieldId + "QAMandatory").prop('checked')) qM = 1;                                // | Current Mandatory State
            if ($("#" + tFieldId + "BOMandatory").prop('checked')) bM = 1;                                // | Current Mandatory State
            var ftv = $("#fileModule_" + tFieldId).val();
            var flp = $('#fileLoanPrg_' + tFieldId).val();
            var QAlp = $('#fileQALoanPrg_' + tFieldId).val();
            var FAlp = $('#fileFALoanPrg_' + tFieldId).val();
            if ($("#QA_" + tFieldId + "_enableQAUniqueLoanPrograms").prop('checked') && QAlp.length) enableQAUniqueLoanPrograms = 1;                                         // | Current Display State
            if ($("#FA_" + tFieldId + "_enableFAUniqueLoanPrograms").prop('checked') && FAlp.length) enableFAUniqueLoanPrograms = 1;                                         // | Current Display State
            if ($("#" + tFieldId + "_LP").prop('checked')) enableAllLoanPrograms = 1;                                         // | Current Display State


            var tArr = {
                fId: tFieldId,
                FAD: fD,
                QAD: qD,
                BOD: bD,
                FAM: fM,
                QAM: qM,
                BOM: bM,
                FTV: (typeof (ftv) === 'undefined') ? '' : ftv.toString(),
                FLP: (typeof (flp) === 'undefined') ? '' : flp.toString(),
                QALP: (typeof (QAlp) === 'undefined') ? '' : QAlp.toString(),
                FALP: (typeof (FAlp) === 'undefined') ? '' : FAlp.toString(),
                EQAULP: enableQAUniqueLoanPrograms,
                EFAULP: enableFAUniqueLoanPrograms,
                EFALLLP: enableAllLoanPrograms,
            };
            if (tFieldId == '1187') {
                console.log(tArr);
            }
            chkAllArray.push(tArr);
        });

    }
    $.ajax({
        type: 'POST',
        url: '/JQFiles/saveQuickAppCustomFormFields.php',
        data: jQuery.param({
            'pcId': pcId,
            'sectionID': sectionId,
            'opt': option,
            'chkAllArray': chkAllArray,
            'UG': UG,
            'UN': UN
        }),
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        success: function (fieldItems) {
            $("#sessMsg").html('Updated Successfully');
            //$('.with-tip, .with-children-tip > *').tip();
            if (option == 1) {
                msg = 'Updated Successfully';
            }
            if (option == 2 || option == 3) {
                msg = 'Reset Successfully';
                location.reload(true);
            }
            toastrNotification(msg, 'success');
        }
    });

}

function getSectionWiseCustomFormFields(pcid, sectionid, fieldNameSearch) {
    console.log({
        func: 'getSectionWiseCustomFormFields',
    });
    const POPOVER_HIDE_DELAY = 300;
    $('.collapse_img_' + sectionid).html("<img src=\"/assets/images/large-loading.gif\" alt=\"\">");

    HTTP.Post('/backoffice/api_v2/jqfiles/getSectionWiseFormFieldData', {
        sectionid: sectionid,
        pcid: pcid,
        fieldNameSearch: fieldNameSearch,
    }, function (data) {
        console.log({
            data: data,
        });

        $.each(data, function (key, value) {
            $('#collapse_' + key).html(value);

            let _checkBO = $(".checkBO" + key);

            let fieldCnt = 0;
            _checkBO.each(function () {
                if (this.checked) fieldCnt++;
            });
            if (_checkBO.length === fieldCnt) {
                $("#" + key + "BO").prop('checked', true);
            } else {
                $("#" + key + "BO").prop("checked", false);
            }

            let _checkQA = $(".checkQA" + key);
            fieldCnt = 0;
            _checkQA.each(function () {
                if (this.checked) fieldCnt++;
            });
            if (_checkQA.length === fieldCnt) {
                $("#" + key + "QA").prop('checked', true);
            } else {
                $("#" + key + "QA").prop("checked", false);
            }

            let _checkFA = $(".checkFA" + key);
            fieldCnt = 0;
            _checkFA.each(function () {
                if (this.checked) fieldCnt++;
            });
            if (_checkFA.length === fieldCnt) {
                $("#" + key + "FA").prop('checked', true);
            } else {
                $("#" + key + "FA").prop("checked", false);
            }

            $(".chzn-select" + key).chosen();
            $(".chzn-select_" + key).chosen();
            $(".chzn-select_QA_" + key).chosen();
            $(".chzn-select_FA_" + key).chosen();
            // $("#" + $(".chzn-select_" + key).attr('id') + "_chosen").hide();
        });

        $('.fileLoanPrg').hide();

        $('.tacBackofficeDisabled').prop('disabled', true);

        $('.updateFieldLabel').editable({
            url: '/backoffice/api_v2/form/update_form_field_label',
            response: function (upStatus) {
            }
        });

        $('.updateSubSectionToolTip').editable({
            display: function (value, sourceData) {
                $(this).html($(this).attr('data-icon'));
            },
            url: '/backoffice/api_v2/form/update_form_field_subsection_tooltip',
            success: function (response) {
                // It's recommended to have the server send clean data.
                // Using global regex for replacement is safer.
                const toolTipText = response.subSectionToolTip;

                // Cache $(this) for efficiency and readability.
                const $element = $('#' + this.id);
                $element.find('i').attr('data-content', toolTipText);
                $element.find('span').attr('data-value', toolTipText);

                $('.popoverClass').popover('hide');
            }
        });
        $(".popoverClass").popover({
            trigger: "manual",
            html: true,
            animation: true,
            boundary: 'window',
            content: function () {
                const $i = $(this).find('i');
                return $i.attr('data-content') || '';
            },
            template: '<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header text-center "></h3><div class="popover-body text-center"></div></div>'
        }).on("mouseenter", function () {
            $(this).popover("show");
            $(".popover").on("mouseleave", function () {
                $(this).popover('hide');
            });
        }).on("mouseleave", function () {
            const self = this;
            setTimeout(function () {
                if (!$(".popover:hover").length) {
                    $(self).popover("hide");
                }
            }, POPOVER_HIDE_DELAY);
        });

        $('#divLoaderAll').hide();
    });
}

$(document).ready(function () {
    $(".search_services").on("input", function () {
        $(this).next('img').show();
        filter = $(this).val().toUpperCase();

        tableClassNameClass = $(this).attr('data-table-class');
        //tableTemp = $(this).parent('div').next('table');
        tableClassName = $("." + tableClassNameClass);

        $.each(tableClassName, function (index, tableTemps) {
            trTemp = tableTemps.getElementsByClassName("serviceTypeValue");
            $.each(trTemp, function (index, eachServicType) {
                console.log($(eachServicType).data('service-name'));

                txtValue = $(eachServicType).data('service-name');
                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    //TdTxt.style.display = "";
                    $(this).removeClass('hide');
                } else {
                    //TdTxt.style.display = "none";
                    //TdTxt.addClass('hide');
                    $(this).addClass('hide');
                }


            });
            //trTemp = tableTemp.find("tr");
            /*return false;
                        $.each(trTemp, function (index, valueTd) {
                            tdTemp = valueTd.getElementsByTagName("td");
                            $.each(tdTemp, function (index, TdTxt) {
                                if (TdTxt) {
                                    txtValue = TdTxt.textContent || TdTxt.innerText;
                                    if (txtValue.toUpperCase().indexOf(filter) > -1) {
                                        //TdTxt.style.display = "";
                                        $(this).removeClass('hide');
                                    } else {
                                        //TdTxt.style.display = "none";
                                        //TdTxt.addClass('hide');
                                        $(this).addClass('hide');
                                    }
                                }
                            });
                        });*/
        });
        $(this).next('img').hide();
    });
    $('input:radio[name="useMyNameAndEmail"]').change(
        function () {
            if ($(this).is(':checked')) {
                if (($(this).val() == '1' || $(this).val() == 'branch') && $('#userRole').val() != 'Super') {
                    toastrNotification('Contact us for this feature', 'error');
                    $(this).prop('checked', false);
                    $("input[name=useMyNameAndEmail][value=0]").prop('checked', true);
                }
            }
        });


});

function checkLoanProgramAssigned(id, value, loanProgramName, pcid) {
    console.log({
        func: 'checkLoanProgramAssigned',
    });
    toastr.clear();
    if (!($('#' + id).is(":checked"))) {
        $.ajax({
            type: 'POST',
            url: siteSSLUrl + "backoffice/getFileData.php",
            data: jQuery.param({
                'PCID': pcid,
                'loanProgram': value,
            }),
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            success: function (response) {
                var result = JSON.parse(response);
                if (result > 0) {
                    $('#' + id).prop('checked', true);
                    //var newDiv = $(document.createElement('div'));
                    //var msg = newDiv.html(' The Loan Program <b>"' + loanProgramName + '"</b> is assigned to <b> "' + result + '"</b> files. Please assign these files to a different loan program before disabling <b>"' + loanProgramName + '"</b>.');
                    var msg = ' The Loan Program <b>"' + loanProgramName + '"</b> is assigned to <b> "' + result + '"</b> files. Please assign these files to a different loan program before disabling <b>"' + loanProgramName + '"</b>.';
                    $.confirm({
                        icon: 'fa fa-warning',
                        closeIcon: true,
                        title: 'Confirm',
                        content: msg,
                        type: 'red',
                        backgroundDismiss: true,
                        buttons: {
                            somethingElse: {
                                text: 'Click here to see files',
                                btnClass: 'btn-blue',
                                keys: ['enter', 'shift'],
                                action: function () {
                                    viewFileData(value);
                                }
                            },
                            Cancel: function () {

                            }
                        },
                    });
                }
            }
        });
    }
}


function viewFileData(value) {
    console.log({
        func: 'viewFileData',
    });
    window.open(
        siteSSLUrl + "backoffice/myPipeline.php?LMRClientType=" + value, "_blank");
}

function showEditSwitch(targetName, parent) {
    console.log({
        func: 'showEditSwitch',
    });
    var pVal = $('#' + parent).val();
    if (pVal == 1) {
        $("." + targetName).css("display", "block");
    } else {
        $("." + targetName).css("display", "none");
    }
}

$(document).on('click', '.checkFATACMan', function () {
    let _fieldId = this.id;
    let _fieldName = this.name;
    let _fieldIdB = _fieldId.replace('Mandatory', '');

    console.log({
        func: 'checkFATACMan',
        _fieldId: _fieldId,
        _fieldName: _fieldName,
        _fieldIdB: _fieldIdB,
    });

    $('#' + _fieldIdB).prop('checked', $(this).is(':checked'));
});

$(document).on('click', '.checkTACMan', function () {
    let _fieldId = this.id;
    let _fieldName = this.name;
    let _fieldIdB = _fieldId.replace('Mandatory', '');

    console.log({
        func: 'checkTACMan',
        _fieldId: _fieldId,
        _fieldName: _fieldName,
        _fieldIdB: _fieldIdB,
    });

    $('#' + _fieldIdB).prop('checked', $(this).is(':checked'));
});

$(document).on('click', '.tacMandatoryAutoCheck', function () {
    let _fieldId = this.id;
    let _fieldName = this.name;
    let _fieldIdB = _fieldId + 'Mandatory';

    console.log({
        func: 'tacMandatoryAutoCheck',
        _fieldId: _fieldId,
        _fieldName: _fieldName,
        _fieldIdB: _fieldIdB,
    });

    $('#' + _fieldIdB).prop('checked', $(this).is(':checked'));
});

function populateStateCounty(formName, srcName, targetName) {
    console.log({
        func: 'populateStateCounty',
    });

    var propertyState = '', xmlDoc = '', countyDataArray = new Array();
    propertyState = $('#state').val();
    if (propertyState != "") {
        var url = siteSSLUrl + "backoffice/getCountyData.php";
        var qstr = "sc=" + propertyState;

        try {
            xmlDoc = getXMLDoc(url, qstr);
        } catch (e) {
        }
        try {
            countyDataArray = xmlDoc.getElementsByTagName("county");
        } catch (e) {
        }
        var options = ''
        for (var c = 0; c < countyDataArray.length; c++) {
            var countyName = "";
            try {
                countyName = countyDataArray[c].getElementsByTagName("countyName")[0].firstChild.nodeValue;
            } catch (e) {
            }
            options += "<option value='" + countyName + "'>" + countyName + "</option>"
        }
        $('#county').empty();
        $('#county').append("<option>- Select County - </option>");
        $('#county').append(options);
    }
}

class processingCompany {
    static showMarketplaceDiv() {
        let allowPCToMarketPlace = parseInt($('#allowPCToMarketPlace').val());
        if (allowPCToMarketPlace) {
            $('.showMarketPlaceChildDiv').show();
        } else {
            $('.showMarketPlaceChildDiv').hide();
        }
    }

    static toggleSwitch_PC() {
        let _checkbox = $('#allowNestedEntityMembersCheck');
        let _hiddenField = $('#allowNestedEntityMembers');
        let _check = _checkbox.is(':checked'); //allowNestedEntityMembers
        let PCID = $('#pcId').val();
        if (!_check) { // disable
            let msg = 'Are you sure to disable this feature?';
            $.confirm({
                icon: 'fa fa-warning',
                closeIcon: true,
                title: 'Confirm',
                content: msg,
                type: 'red',
                backgroundDismiss: false,
                draggable: false,
                buttons: {
                    yes: function () {
                        HTTP.Post('/backoffice/processingCompany/checkForNestedEntityMember',
                            {
                                PCID: PCID,
                            },
                            function (data) {
                                if (data._count > 0) { // re-confirm for file(s) with nested entity members
                                    processingCompany.checkNestedEntityMembers(_checkbox, _hiddenField, _check);
                                } else {
                                    processingCompany.disableNestedEntityMembers(_checkbox, _hiddenField, _check);
                                }
                            });
                    }, cancel: function () {
                        _checkbox.addClass('switch-on');
                        _checkbox.removeClass('switch-off');
                        _checkbox.prop('checked', true);
                        _hiddenField.val(1);
                    },
                },
                onClose: function () {

                },
            });
        } else {
            _checkbox.addClass('switch-on');
            _checkbox.removeClass('switch-off');
            _checkbox.prop('checked', true);
            _hiddenField.val(1);
        }
    }

    static checkNestedEntityMembers(_checkbox, _hiddenField, _check) {
        let msg = 'There are active loan files with nested entity members and these entity members will be deleted. You will not be able to get back the deleted nested entity members. Do you still want to disable this feature?';
        $.confirm({
            icon: 'fa fa-warning',
            closeIcon: true,
            title: 'Confirm',
            content: msg,
            type: 'red',
            backgroundDismiss: false,
            draggable: false,
            buttons: {
                yes: function () {
                    processingCompany.disableNestedEntityMembers(_checkbox, _hiddenField, _check);
                }, cancel: function () {
                    _checkbox.addClass('switch-on');
                    _checkbox.removeClass('switch-off');
                    _checkbox.prop('checked', true);
                    _hiddenField.val(1);
                },
            },
            onClose: function () {

            },
        });
    }

    static disableNestedEntityMembers(_checkbox, _hiddenField, _check) {
        let PCID = $('#pcId').val();
        HTTP.Post('/backoffice/processingCompany/disableNestedEntityMembers',
            {
                setNestedEntityMembers: _check,
                PCID: PCID,
            },
            function (data) {
                let msg = 'Nested entity members are disabled successfully.';
                toastrNotification(msg, 'success');
                _checkbox.addClass('switch-off');
                _checkbox.removeClass('switch-on');
                _checkbox.prop('checked', false);
                _hiddenField.val(0);
            });
    }

    static toggleSwitchField_PC(parent, target) {
        let isPLO = $('#' + parent);
        if (isPLO.is(':checked')) {
            $('#' + target).show();
        } else {
            $('#' + target).hide();
        }
    }

    static loadRequiredDocCategory(ele) {
        let categoryName = $(ele).data('categoryname');
        let categoryId = $(ele).data('categoryid');

        $('#categoryName').val(categoryName);
        $('#categoryId').val(categoryId);
    }

    static updateRequiredDocCategoryStatus(ele) {

        let _ele = $(ele);
        let categoryId = _ele.data('id');
        let activeStatus = parseInt(_ele.data('status'));
        if (categoryId) {
            $.confirm({
                icon: 'fa fa-warning',
                closeIcon: true,
                title: 'Confirm',
                content: "Are you sure you want to " + (activeStatus === 1 ? "Activate?" : "Deactivate?"),
                type: 'red',
                backgroundDismiss: true,
                buttons: {
                    yes: function () {
                        HTTP.Post("api_v2/processingCompany/requiredDocs/category/active_inactive", {
                            categoryId: categoryId,
                            activeStatus: activeStatus,
                        }, function (resObj) {
                            if (parseInt(resObj.code) === 100) {
                                toastrNotification(resObj.msg, 'success');
                                location.reload();
                            } else {
                                toastrNotification(resObj.error, 'error');
                            }
                        });
                    },
                    cancel: function () {

                    },
                }
            });
        }

    }

    static validateVhost() {
        let vhost = $('#vhost').val();
        //if empty return true
        if (!vhost) {
            return true;
        }
        // Convert to lowercase and trim spaces
        vhost = vhost.toLowerCase().trim();
        //remove http:// or https:// from the beginning of the url
        vhost = vhost.replace(/^https?:\/\//, '');
        // Ensure it does not start or end with a hyphen (-) or dot (.)
        vhost = vhost.replace(/^-+|-+$/g, '');
        // Validate format using regex (basic domain validation)
        if (!/^(?:[a-z0-9][a-z0-9-]{0,62}\.)+[a-z]{2,}$/.test(vhost)) {
            toastrNotification('Invalid vhost format. Please enter a valid vhost.', 'error');
            return false;
        } else {
            //assign the value back to the input field
            $('#' + vhost).val(vhost);
            return true;
        }
    }

    static makePCDocsAvailableToAllBranch(docID) {
        console.log({
            func: 'makePCDocsAvailableToAllBranch',
        });

        let PCID = $('#pkgForm [name="pcId"]').val();

        HTTP.Post('../backoffice/makePCDocsAvailableToAllBranch.php', {
            docID: docID,
            PCID: PCID,
        }, function (data) {
            let code = data.hasOwnProperty('code') ? parseInt(data.code) : null;
            let msg = data.hasOwnProperty('msg') ? (data.msg) : '';
            toastrNotification(msg, code === 100 ? 'success' : 'error');
        });
    }
}


function validateAdditionalTermsDescription(additionalTermsId) {
    console.log({
        func: 'validateAdditionalTermsDescription',
    });

    additionalTermsDetailstxt = tinyMCE.get(additionalTermsId).getContent();
    if (additionalTermsDetailstxt.length > 65535) {
        toastrNotification("Additional Terms & Requirements for Loan Processing & Underwriting: exceeds the limit of 65535 Characters", 'error');
        tinyMCE.get(additionalTermsId).focus();
        tinyMCE.execCommand('mceInsertContent', false, '');
        return false;
    } else {
        return true;
    }
}

