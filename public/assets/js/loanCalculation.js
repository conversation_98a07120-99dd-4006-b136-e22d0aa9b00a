class TransactionType {

    static COMMERCIAL_CASH_OUT_REFINANCE = 'Commercial Cash Out Refinance';

    static CASH_OUT_REFINANCE = 'Cash-Out / Refinance';

    static COMMERCIAL_RATE_TERM_REFINANCE = 'Commercial Rate / Term Refinance';

    static NEW_CONSTRUCTION_EXISTING_LAND = 'New Construction - Existing Land';

    static PURCHASE = 'Purchase';

    static REFINANCE = 'Refinance';

    static LINE_OF_CREDIT = 'Line of Credit';

    static RATE_TERM_REFINANCE = 'Rate & Term Refinance';

    static TRANSACTIONAL = 'Transactional';

    static COMMERCIAL_PURCHASE = 'Commercial Purchase';

    static EQUIPMENT_FINANCING = 'Equipment Financing';
    static DELAYED_PURCHASE = 'Delayed Purchase';
}

function updateLoanDetail(inCl) {
    console.log({
        func: 'updateLoanDetail',
    });
    loanCalculation.initLTC2();
    loanCalculation.calculateInitialLoanAmount();
    calculateCurrentLoanBalance();
    calculateAcquisitionLTV();

    let autoCalcTLAARV = $('input[name=autoCalcTLAARV]:checked').val();
    if (autoCalcTLAARV === "Yes") {
        autoCalculateTotalLoanAmountARVNew(inCl);
    } else {
        calculateHMLOFeeCostTotalLoanAmount();
    }

    updateLoanCalculation();

    /**
     * Origination Points calculations
     */
    calculateOriginationPointsValue();
    /**
     * Broker Points calculations
     */
    calculateBrokerPointsValue();
    dateDiffDiemDays();
    /**
     * Total Fees and Cost calculations
     */
    calculateTotalFeesAndCost(inCl);
    /*
     * Market LTV calculation.
     * ARV
     * Loan To Cost
     * Daily Interest Charge
     */


    getHMLOLoanInfoTotalMonthlyPayment();
    setDailyInterestCharge();

    calculatePaymentReserves();
    calculateHMLONetMonthlyPayment();
    calculateDebtServiceRatio();
    calculateDebtServiceRatioPITIA();
    calculateTotalProjectCost();
    calculateSimpleARVPercentage();
    calculateTotalCashOut();
    loanCalculation.calculateContingencyReserve();
    calculateRequiredConstruction('requiredConstructionAmt');
    calculateTotalRequiredReserves();

    calculateTotalCashToClose();

    loanCalculation.calculateNetLenderFundsToTitle()

    monthlyInterestRate(0, 0, 0);
    calculateCapRate();
    /* calculation for New TPC & LTC */
    calculationNewTPCLTC();

    //assign values to mirror fields SPCF//
    let taxes1 = replaceCommaValues($('#taxes1').val());
    let annualPremium = replaceCommaValues($('#annualPremium').val());

    $('#spcf_taxes1').val(autoNumericConverter(taxes1));
    $('#spcf_annualPremium').val(autoNumericConverter(annualPremium));
    calculateCashFlow();

    calculateExitAmount('');

    if ($('#customLenderRevenue').length) {
        getLenderRevenue();
    }
    loanCalculation.setLTCInitialLoanAmount();
    loanCalculation.setLTCMarketValue();
    loanCalculation.setLTCOriginalPurchasePriceValue();
    totalRequiredReserves.calculatePercentageTotalLoanAmount();
    loanCalculation.calculateGrossProfit();
}

function getLenderRevenue() {
    console.log({
        func: 'getLenderRevenue',
    });

    let originationPointsValue = getFieldsValue('originationPointsValue');
    let applicationFee = getFieldsValue('applicationFee');
    let processingFee = getFieldsValue('processingFee');
    let underwritingFees = getFieldsValue('underwritingFees');
    let bufferAndMessengerFee = getFieldsValue('bufferAndMessengerFee');
    let drawsSetUpFee = getFieldsValue('drawsSetUpFee');
    let drawsFee = getFieldsValue('drawsFee');
    let valuationAVMFee = getFieldsValue('valuationAVMFee');
    let wireFee = getFieldsValue('wireFee');
    let servicingSetUpFee = getFieldsValue('servicingSetUpFee');
    let floodCertificateFee = getFieldsValue('floodCertificateFee');
    let backgroundCheckFee = getFieldsValue('backgroundCheckFee');
    let creditReportFee = getFieldsValue('creditReportFee');
    let projectFeasibility = getFieldsValue('projectFeasibility');
    let appraisalFee = getFieldsValue('appraisalFee');
    let thirdPartyFees = getFieldsValue('thirdPartyFees');

    let customLenderRevenue =
        parseFloat(originationPointsValue)
        + parseFloat(applicationFee)
        + parseFloat(processingFee)
        + parseFloat(underwritingFees)
        + parseFloat(bufferAndMessengerFee)
        + parseFloat(drawsSetUpFee)
        + parseFloat(drawsFee)
        + parseFloat(valuationAVMFee)
        + parseFloat(wireFee)
        + parseFloat(servicingSetUpFee)
        + parseFloat(floodCertificateFee)
        + parseFloat(backgroundCheckFee)
        + parseFloat(creditReportFee)
        + parseFloat(projectFeasibility)
        + parseFloat(appraisalFee)
        - parseFloat(thirdPartyFees);

    $('#customLenderRevenue').html('$ ' + customLenderRevenue.toFixed(2));
}

function setTotalProjectSoftHardCostDisp() {

    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
    let needRehab = $('input[name=propertyNeedRehab]:checked').val()

    if ((typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_PURCHASE ||
            typeOfHMLOLoanRequesting === TransactionType.PURCHASE ||
            typeOfHMLOLoanRequesting === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND) &&
        needRehab === 'Yes'
    ) {
        //NewLTCDiv /NewTPCDiv
        //$("#NewLTCDiv").css("display", '');
        $("#NewTPCDiv").css("display", "block");
        $("#NewTPCToolTip").attr('data-original-title', 'Total project soft & hard cost =  (Purchase price + Rehab/const cost + Total Fees & Costs + Cost Spent) <hr> Note: Do not include Accrued Per Diem Interest');
    } else {
        //$("#NewLTCDiv").css("display", "none");
        $("#NewTPCDiv").css("display", "none");
        $("#NewTPCToolTip").attr('data-original-title', '');
    }


}

function LoanToCostWSoftHardCost() {

    let needRehab = $('input[name=propertyNeedRehab]:checked').val()
    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();

    if (needRehab === 'Yes'
        && (isTransactionTypeRefinance(typeOfHMLOLoanRequesting)
            || isTransactionTypePurchase(typeOfHMLOLoanRequesting)
            || isTransactionTypeNewConstruction(typeOfHMLOLoanRequesting)
        )
    ) {
        $("#NewLTCDiv").show();
    }
}

function showAndHideCommercialFields(tempFldValue) {
    console.log({
        func: 'showAndHideCommercialFields',
        tempFldValue: tempFldValue,
    });
    let LMRClientType;
    let _LMRClientTypeEle = $("#LMRClientType");
    if (_LMRClientTypeEle.length) {
        LMRClientType = _LMRClientTypeEle.val();
    }

    let activeTab = '';
    let tableRow = '';// 'table-row';
    let tableCell = '';//'table-cell';
    let tableCellEmpty = '';

    if (tempFldValue === TransactionType.EQUIPMENT_FINANCING) {
        // $("#equipmentdiv").removeClass("secHide");
        //$("#equipmentdiv").addClass("secShow");
        $("#tab_PI").css('display', 'none');
        //$("#tab_EF").css('display', 'block');
    } else {
        //$("#equipmentdiv").addClass("secHide");
        //$("#tab_EF").css('display', 'none');
    }
    $("#equipmentdiv").removeClass("secShow");

    activeTab = $('#activeTab').val();

    if (activeTab !== 'HMLI') {
        tableRow = 'block';
        tableCell = 'block';
        tableCellEmpty = '';
    }

    $('#isLoTxt').html('Current Loan Balance');
    $('.isBlanketLoanDiv').css("display", tableRow);
    $('.isEFBlanketLoanDiv').css("display", tableCell);
    $('.LOCTotalLoanAmtHide').css("display", tableCellEmpty);
    $('.LOCTotalLoanAmt').css("display", "none");
    //$(".HMLOTotalLoanAmt").css("background-color", "#F3F2D1");
    $('.feeSectionTotalLoanAmt').css("display", tableCell);
    $('.acquisitionLTVTD').css("display", "none");
    $('.marketLTVTD').css("display", tableCell);
    //$('.removeCls').addClass('HMLOTotalLoanAmt');
    $('.typeOfSale').css("display", "none");
    $('.landValueExtraCls').css("display", "none");
    $('.cashOutRefinanceDisp').css("display", "none");
    $('.transactionalTdFieldsNCDate').css("display", "none");
    $('.totProjectCost').html('Total Project Cost');

    let _landValueClsEle = $('.landValueCls');
    _landValueClsEle.css("display", "none");

    $("#NewLTCDiv").hide();
    $("#NewTPCDiv").hide();

    setTotalProjectSoftHardCostDisp();
    LoanToCostWSoftHardCost();

    if (tempFldValue === TransactionType.RATE_TERM_REFINANCE ||
        tempFldValue === TransactionType.CASH_OUT_REFINANCE ||
        tempFldValue === TransactionType.DELAYED_PURCHASE ||
        tempFldValue === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE ||
        tempFldValue === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
        tempFldValue === TransactionType.LINE_OF_CREDIT ||
        tempFldValue === TransactionType.REFINANCE) {

        if (tempFldValue === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE ||
            tempFldValue === TransactionType.RATE_TERM_REFINANCE) {

            $('.doesPropertyNeedRehabSection').css("display", tableCell);
            $('.contigencyCls').css("display", tableCell);

        } else {

            $('.doesPropertyNeedRehabSection').css("display", tableCell);
            $('.contigencyCls').css("display", tableCell);

        }

        if (tempFldValue === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
            tempFldValue === TransactionType.CASH_OUT_REFINANCE ||
            tempFldValue === TransactionType.DELAYED_PURCHASE ||
            tempFldValue === TransactionType.RATE_TERM_REFINANCE ||
            tempFldValue === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE ||
            tempFldValue === TransactionType.REFINANCE
        ) {

            $('.cashOutDiv').css("display", tableRow);

            $('.rehabConsCls').css("display", "");
            $('.LOCTotalLoanAmtHide').css("display", "none");
            $(".cashOutFields").css("background", "#bbe1fe");
            $('.totProjectCost').html('Total Project Value');
            // $('.marketLTVTD').css("display", "none");

            if (LMRClientType === 'CONS') {
                $('.landValueExtraCls').css("display", tableCell);
            } else {
                $('.landValueExtraCls').css("display", "none");
            }
            $('.cashOutRefinanceDisp').css("display", tableCell);

        } else {
            $('.cashOutDiv').css("display", "none");

            $(".cashOutFields").css("background", "#bbe1fe");
            $('.rehabConsCls').css("display", "none");

        }

        if (tempFldValue === TransactionType.LINE_OF_CREDIT) {
            $('.feeSectionTotalLoanAmt').css("display", "none");
            clear_form_elements('commercialTdFields');
            clear_form_elements('refinanceSection');
            $('.commercialTdFields').css("display", "none");
            $('.LOCTotalLoanAmtHide').css("display", "none");
            //$('.LOCTotalLoanAmt').css("display", tableCell);
            //$('.refinanceSection').css("display", "none");
            //$('.rehabConsCls').css("display", tableRow);
            $('.lineOfCreditProp').show();
        } else {
            $('.commercialTdFields').css("display", tableCell);
            $('.refinanceSection').css("display", "block");
        }
        clear_form_elements('downPaymentField');
        clear_form_elements('commercialFields');
        clear_form_elements('commercialFieldsTD');
        clear_form_elements('commercialTdFields');
        clear_form_elements('transactionalFields');
        clear_form_elements('blanketLoanFields');

        $('.downPaymentField').css("display", "none");
        $('.cashOutRefinanceFields').css("display", tableCell);
        $('.commercialFields').css("display", "none");
        $('.commercialFieldsTD').css("display", "none");
        $('.commercialTdFields').css("display", "none");
        $('.transactionalTdFields').css("display", "none");
        $('.transactionalFields').css("display", "none");
        $('.blanketLoanFields').css("display", "none");
        $('#taxes1').attr('readonly', false);

    } else if (tempFldValue === TransactionType.TRANSACTIONAL) {

        clear_form_elements('commercialFields');
        clear_form_elements('commercialFieldsTD');
        clear_form_elements('commercialTdFields');
        // clear_form_elements('subjectPropertySection');
        clear_form_elements('blanketLoanFields');
        $('#rehabCostFinanced').val('');
        clear_form_elements('doesPropertyNeedRehabDispDiv');
        clear_form_elements('doesPropertyNeedRehabSection');
        clear_form_elements('cashOutRefinanceFields');
        clear_form_elements('contigencyCls');
        clear_form_elements('refinanceSection');
        clear_form_elements('cashOutDiv');
        clear_form_elements('rehabConsCls');
        clear_form_elements('isBlanketLoanDiv');
        clear_form_elements('isEFBlanketLoanDiv');
        loanCalculation.setDownPaymentHtml('');
        $('.downPaymentField').css("display", tableRow);
        $('.transactionalFields').css("display", tableRow);
        $('.doesPropertyNeedRehabSection').css("display", "none");
        $('.doesPropertyNeedRehabDispDiv').css("display", "none");
        $('#propertyNeedRehabYes').prop('checked', false);
        $('.propertyNeedRehabinitialTddisp').css("display", "none");
        $('.commercialFields').css("display", "none");
        $('.commercialFieldsTD').css("display", "none");
        $('.commercialTdFields').css("display", "none");
        $('.transactionalTdFields').css("display", tableCellEmpty);
        // $('.subjectPropertySection').css("display", "none");
        $('.refinanceSection').css("display", "none");
        $('.cashOutRefinanceFields').css("display", "none");
        $('.blanketLoanFields').css("display", "none");
        $('#taxes1').attr('readonly', false);
        $('.contigencyCls').css("display", "none");
        $('.cashOutDiv').css("display", "none");
        $('.rehabConsCls').css("display", "none");
        $('.isBlanketLoanDiv').css("display", "none");
        $('.isEFBlanketLoanDiv').css("display", "none");
        $(".cashOutFields").css("background-color", "#F3F2D1");
        $('#isLoTxt').html('Initial Loan Amount');

    } else {

        if (tempFldValue === TransactionType.COMMERCIAL_PURCHASE) {
            //  $('.subjectPropertySection').css("display", "block");
        } else {
            //  clear_form_elements('subjectPropertySection');
            //  $('.subjectPropertySection').css("display", "none");
        }

        clear_form_elements('transactionalFields');
        clear_form_elements('blanketLoanFields');
        clear_form_elements('cashOutRefinanceFields');
        clear_form_elements('refinanceSection');
        clear_form_elements('cashOutDiv');
        clear_form_elements('rehabConsCls');
        $('#rehabCostFinanced').val('');
        clear_form_elements('doesPropertyNeedRehabDispDiv');
        loanCalculation.setDownPaymentHtml('');

        $('.commercialFields').css("display", tableRow);
        $('.commercialFieldsTD').css("display", tableCell);
        $('.transactionalFields').css("display", "none");
        $('.refinanceSection').css("display", "none");
        $('.blanketLoanFields').css("display", "none");
        $('.doesPropertyNeedRehabSection').css("display", tableCell);
        $('.cashOutRefinanceFields').css("display", "none");
        $('#taxes1').attr('readonly', false);
        $('.contigencyCls').css("display", tableCell);
        $('.cashOutDiv').css("display", "none");
        $('.rehabConsCls').css("display", "none");
        $('.marketToolTip').css("display", "block");
        $('.purchaseTPCCls').css("display", tableRow);
        $('.commercialTDFields').css("display", tableCell);
        $('.acquisitionLTVTD').css("display", tableCell);
        $('.downPaymentField').css("display", tableRow);
        $('.typeOfSale').css("display", tableRow);
        $(".cashOutFields").css("background-color", "#F3F2D1");
        $('.transactionalTdFields').css("display", tableCellEmpty);
        $('.commercialFieldsTDNew').css("display", ""); //needs to override the previous style fix for sc-31816
        /* Transaction Type = New Construction - Existing Land - PT=#********* */
        if (tempFldValue === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND) {

            $('#propertyNeedRehabYes').prop('checked', true);
            hideAndShowPropertyNeedRehab(1);
            clear_form_elements('transactionalTdFieldsNC');
            $('.transactionalTdFieldsNC').css("display", "none");
            $('.transactionalTdFieldsNCDate').css("display", tableRow);
            $('.acquisitionLTVTD').css("display", "none");
            // $('.marketLTVTD').css("display", "none");
            $('.totProjectCost').html('Total Project Value');
            _landValueClsEle.css("display", "");

            // sai
            clear_form_elements('transactionalTdFieldsNC');
            //$('.transactionalTdFieldsNC').css("display", "");
            //$('.acquisitionLTVTD').css("display", "");
            //$('.marketLTVTD').css("display", "");
            //$('.commercialTdFields').hide();
            $('.rehabConsCls').show();
            $('.costBasis_disp').css("display", "");
            $('.LTVPercentageDisp').css("display", "none");
            $('.totalCashOut_disp').css("display", "none");
            $('.LOCTotalLoanAmtHide').css("display", "none");

            //sai

        } else {
            let propertyNeedRehabYes = $('#propertyNeedRehabYes');
            propertyNeedRehabYes.prop('checked', false);
            let _FPCID = $('#FPCID');
            if (_FPCID.length) {
                FPCID = parseInt(_FPCID.val());
            }
            if ((LMRClientType === 'BRLGUC' && FPCID === 4975)) { //CV3
                propertyNeedRehabYes.prop('checked', true);
            }
            hideAndShowPropertyNeedRehab(1);
        }
    }

    $('.autoCalcTLAARVDisp').css("display", "none");

    if (tempFldValue === TransactionType.PURCHASE ||
        tempFldValue === TransactionType.COMMERCIAL_PURCHASE ||
        tempFldValue === TransactionType.CASH_OUT_REFINANCE ||
        tempFldValue === TransactionType.DELAYED_PURCHASE ||
        tempFldValue === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
        tempFldValue === TransactionType.REFINANCE ||
        tempFldValue === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND) {
        $('.autoCalcTLAARVDisp').css("display", "");
    }
    /**
     * update as on Jan 30, 2023
     * PT#184325968 {Make Rate and Term transaction types function just like Cash-Out transaction types do.}
     */
    if (tempFldValue === TransactionType.RATE_TERM_REFINANCE ||
        tempFldValue === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE) {
        //$('#isIntialLoanAmountDisp').hide();
        $('#isLoanPaymentAmtTLA').prop('checked', true);
        $('#isLoanPaymentAmtILA').removeAttr('checked');
    } else {
        //$('#isIntialLoanAmountDisp').show();
    }

    var temprehabCon = '';
    var tempIsBlanketLoan = '';
    temprehabCon = document.loanModForm.propertyNeedRehab.value;
    tempIsBlanketLoan = document.loanModForm.isBlanketLoan.value;

    hideAndShowBlanketLoan(tempIsBlanketLoan, 'isBlanketLoan');
    hideAndShowBlanketLoan(tempIsBlanketLoan, 'isEFBlanketLoan');
    $('#maxArvPer').hide();
    $('#rehabCostPercentageFinanced').attr('readonly', false);
    // if (tempFldValue !== TransactionType.NEW_CONSTRUCTION_EXISTING_LAND) {
    //     $('.LTCInitialLoanAmountCls').removeClass(" d-none ");
    // } else {
    //     $('.LTCInitialLoanAmountCls').addClass(" d-none ");
    // }

    if (tempFldValue === TransactionType.COMMERCIAL_PURCHASE
        || tempFldValue === TransactionType.PURCHASE
        || tempFldValue === TransactionType.CASH_OUT_REFINANCE
        || tempFldValue === TransactionType.DELAYED_PURCHASE
    ) {
        $('.grossProfit_disp').removeClass('d-none');
        $('.grossProfitMargin_disp').removeClass('d-none');
    } else {
        $('.grossProfit_disp').addClass('d-none');
        $('.grossProfitMargin_disp').addClass('d-none');
    }
    if (tempFldValue === TransactionType.CASH_OUT_REFINANCE
        || tempFldValue === TransactionType.DELAYED_PURCHASE
        || tempFldValue === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE
        || tempFldValue === TransactionType.LINE_OF_CREDIT) {
        $('.useOfFundsDiv').removeClass('d-none');
    } else {
        $('.useOfFundsDiv').addClass('d-none');
    }


    /** ST-40970 **/
    let refinanceBasedFieldsClass = $('.refinanceBasedFieldsClass');
    if ($.inArray(tempFldValue, [
        TransactionType.CASH_OUT_REFINANCE,
        TransactionType.DELAYED_PURCHASE,
        TransactionType.RATE_TERM_REFINANCE,
        TransactionType.REFINANCE,
    ]) >= 0) {
        refinanceBasedFieldsClass.removeClass('hide');
    } else {
        refinanceBasedFieldsClass.addClass('hide');
    }
    if (tempFldValue === TransactionType.DELAYED_PURCHASE) {
        $('.costBasis_disp').css("display", "block");
    }
    /** End ST-40970 **/

    let _PSAClosingDateClass = $('.PSAClosingDateClass');
    if (tempFldValue !== TransactionType.PURCHASE) {
        _PSAClosingDateClass.hide();
    } else {
        _PSAClosingDateClass.show();
    }

    updateLoanDetail();
}

function hideAndShowPropertyNeedRehab(dipOpt) {
    console.log({
        func: 'hideAndShowPropertyNeedRehab',
    });

    if (dipOpt === undefined) dipOpt = 0;   // https://www.pivotaltracker.com/story/show/161121479

    var activeTab = '';
    var tableRow = '';//'table-row';
    var tableCell = '';//table-cell';

    try {
        activeTab = $('#activeTab').val();
    } catch (e) {
    }

    if (activeTab == 'HMLI') {
    } else {
        var tableRow = 'block';
        var tableCell = 'block';
    }

    var fldValue = $('input[name=propertyNeedRehab]:checked').val();

    if (fldValue === 'Yes') {
        var rehabDefaultVal = 100;   // Set rehab default value 100 
        if ($('#setRehabDefaultVal').val() != '') {
            rehabDefaultVal = $('#setRehabDefaultVal').val();
        }
        if ($('#rehabCostPercentageFinanced').val() == '') {
            $('#rehabCostPercentageFinanced').val(rehabDefaultVal);
        }
        $('.doesPropertyNeedRehabDispDiv').show();
        try {
            $('.propertyNeedRehabinitialTddisp').show();
            $('.propertyNeedRehabFootageTddisp').show();
        } catch (e) {
        }
        LoanToCostWSoftHardCost();
    } else {
        $('.doesPropertyNeedRehabDispDiv').css("display", "none");
        $('#propertyNeedRehabYes').prop('checked', false);
        try {
            $('.propertyNeedRehabinitialTddisp').css("display", "none");
            $('.propertyNeedRehabFootageTddisp').css("display", "none");
        } catch (e) {
        }
        clear_form_elements('doesPropertyNeedRehabDispDiv');
    }
    setTotalProjectSoftHardCostDisp();

    var isGroundChecked;
    isGroundChecked = $('input[name=isThisGroundUpConstruction]:checked').val();
    if (isGroundChecked != 'Yes') {
        $('.groundUpFields').hide();
    }
    $('#rehabCostFinanced').val('');

    if (dipOpt == 0) {
        updateLoanDetail();
    }
}


/* Acquisition LTV */
function calculateAcquisitionLTV() {
    console.log({
        func: 'calculateAcquisitionLTV',
    });

    let costBasis = getFieldsValue('costBasis');
    let acquisitionPriceFinanced = getTextValue('acquisitionPriceFinanced');

    let closingCostFinanced = getFieldsValue('closingCostFinanced');                 // int - Input Fields Value

    let acquisitionLTV;
    let _acquisitionLTVTooltip = $('#acquisitionLTVTooltip');
    let acquisitionLTVTooltip = null;

    if (costBasis > 0) {
        acquisitionLTV = ((acquisitionPriceFinanced + closingCostFinanced) / costBasis) * 100.0;
        acquisitionLTV = acquisitionLTV.toFixed(2);
    }
    acquisitionLTVTooltip = `((${acquisitionPriceFinanced} + ${closingCostFinanced}) / ${costBasis}) * 100.0 = ${acquisitionLTV} % `;

    acquisitionLTVTooltip = _acquisitionLTVTooltip.data('formula') + '<hr>' + acquisitionLTVTooltip;
    _acquisitionLTVTooltip.attr('data-original-title', acquisitionLTVTooltip);

    console.log({
        costBasis: costBasis,
        acquisitionPriceFinanced: acquisitionPriceFinanced,
        closingCostFinanced: closingCostFinanced,
        acquisitionLTV: acquisitionLTV,
    });

    $('#acquisitionLTV').html(acquisitionLTV);
}

$(function () { //background-color: rgb(213, 213, 213);
    $("#typeOfHMLOLoanRequesting").on('change', function () {
        let totalLoanAmount1Ele = $("#totalLoanAmount1");
        if (this.value === TransactionType.PURCHASE ||
            this.value === TransactionType.COMMERCIAL_PURCHASE ||
            this.value === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND
        ) {
            totalLoanAmount1Ele.prop('readonly', false);
            totalLoanAmount1Ele.attr('style', '')
        } else {
            totalLoanAmount1Ele.prop('readonly', true);
            totalLoanAmount1Ele.attr('style', 'background-color: rgb(213, 213, 213);')
        }
    })
});


/* Total Loan Amount */
function calculateHMLOFeeCostTotalLoanAmount() {
    console.log({
        func: 'calculateHMLOFeeCostTotalLoanAmount',
    });

    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();

    let acquisitionPriceFinanced = getTextValue('acquisitionPriceFinanced');            // txt - Inner text Value
    let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
    let closingCostFinanced = getFieldsValue('closingCostFinanced');                 // int - Input Fields Value
    let payOffMortgage1 = getFieldsValue('payOffMortgage1');
    let payOffMortgage2 = getFieldsValue('payOffMortgage2');
    let payOffOutstandingTaxes = getFieldsValue('payOffOutstandingTaxes');
    let CORTotalLoanAmt = getFieldsValue('CORTotalLoanAmt');
    let prepaidInterestReserve = getFieldsValue('prepaidInterestReserve');
    let isCheckedVal = getFieldsValue('haveInterestreserveYes', null, true) === 'Yes';
    if (!isCheckedVal) {
        prepaidInterestReserve = 0;
    }

    let totalLoanAmount;
    let TLAFormula;
    let TLATooltip = '';
    let chkTotalLoanAmount;
    let tLAToolTipWithValues = '';

    if (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
        loanCalculation.initializeVariables();

        let totalAmountObj = loanCalculation.calculateTotalLoanAmountForLTC2();
        if ('totalLoanAmount' in totalAmountObj) {
            totalLoanAmount = autoNumericConverter(totalAmountObj.totalLoanAmount.toFixed(2));
        }
        if ('totalLoanAmountTooltip' in totalAmountObj) {
            tLAToolTipWithValues = totalAmountObj.totalLoanAmountTooltip;
        }
    } else {

        if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
            typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
            typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
            typeOfHMLOLoanRequesting === TransactionType.REFINANCE ||
            typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE ||
            typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE
        ) {
            totalLoanAmount = CORTotalLoanAmt + rehabCostFinanced - prepaidInterestReserve;
            tLAToolTipWithValues = `${CORTotalLoanAmt} + ${rehabCostFinanced} `; // Tooltip
        } else if (typeOfHMLOLoanRequesting === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND) {
            totalLoanAmount = CORTotalLoanAmt + rehabCostFinanced + closingCostFinanced;
            chkTotalLoanAmount = totalLoanAmount;
            tLAToolTipWithValues = ` ${CORTotalLoanAmt} + ${rehabCostFinanced} + ${closingCostFinanced} `; // Tooltip
        } else if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {
            totalLoanAmount = CORTotalLoanAmt + acquisitionPriceFinanced + rehabCostFinanced + closingCostFinanced;
            chkTotalLoanAmount = totalLoanAmount;
            tLAToolTipWithValues = ` ${CORTotalLoanAmt} + ${acquisitionPriceFinanced} + ${rehabCostFinanced} + ${closingCostFinanced} `; // Tooltip
        } else if (typeOfHMLOLoanRequesting === TransactionType.TRANSACTIONAL) {
            totalLoanAmount = acquisitionPriceFinanced + closingCostFinanced;
            chkTotalLoanAmount = totalLoanAmount;
            tLAToolTipWithValues = ` ${acquisitionPriceFinanced} + ${closingCostFinanced}  `; // Tooltip
        } else {
            totalLoanAmount = acquisitionPriceFinanced + rehabCostFinanced + closingCostFinanced;
            chkTotalLoanAmount = totalLoanAmount;
            tLAToolTipWithValues = ` ${acquisitionPriceFinanced} ` +
                ($('.rehabCostFinancedDiv').is(':visible') ? ` + ${rehabCostFinanced} ` : '')
                + ` + ${closingCostFinanced} `; // Tooltip
        }
        if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
            typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
            typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
            typeOfHMLOLoanRequesting === TransactionType.REFINANCE ||
            typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE ||
            typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE
        ) {
            if ($('.rehabCostFinancedDiv').is(':visible')) {
                TLATooltip = '+ Rehab / Construction Cost Financed';
            }
            TLAFormula = "Total Loan Amount = Initial Loan Amount " + TLATooltip;
        } else {
            if ($('.rehabCostFinancedDiv').is(':visible')) {
                TLATooltip = '+ Rehab / Construction Cost Financed';
            }
            if ($('.closingCostFinanced_disp').is(':visible')) {
                TLATooltip += '+ Closing Costs Financed';
            }
            if ($('input[name="haveInterestreserve"]:checked').val() === 'Yes') {
                TLATooltip += '+ Prepaid Interest Reserve Financed';
                tLAToolTipWithValues += ` + ${prepaidInterestReserve} `;
            }
            TLAFormula = "Total Loan Amount = Initial Loan Amount " + TLATooltip;
        }
        totalLoanAmount += prepaidInterestReserve;
        totalLoanAmount = autoNumericConverter(totalLoanAmount.toFixed(2));
        tLAToolTipWithValues = TLAFormula + "<hr>" + tLAToolTipWithValues + ` = $ ${totalLoanAmount} `;

        if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {
            $('#LOCTotalLoanAmt').val(totalLoanAmount);
        }
    }
    console.log({
        typeOfHMLOLoanRequesting: typeOfHMLOLoanRequesting,
        acquisitionPriceFinanced: acquisitionPriceFinanced,
        rehabCostFinanced: rehabCostFinanced,
        closingCostFinanced: closingCostFinanced,
        payOffMortgage1: payOffMortgage1,
        payOffMortgage2: payOffMortgage2,
        payOffOutstandingTaxes: payOffOutstandingTaxes,
        CORTotalLoanAmt: CORTotalLoanAmt,
        prepaidInterestReserve: prepaidInterestReserve,
        isCheckedVal: isCheckedVal,
        totalLoanAmount: totalLoanAmount,
        TLAFormula: TLAFormula,
        chkTotalLoanAmount: chkTotalLoanAmount,
    });

    $('#totalLoanAmount1').val(totalLoanAmount);
    $('.totalLoanAmount').html(totalLoanAmount);
    $('#tLAToolTip').attr('data-content', tLAToolTipWithValues);
    $('#totalLoanAmountToolTip').attr('data-content', tLAToolTipWithValues);
}


/* Calculate ARV */
function updateLoanCalculation() {
    console.log({
        func: 'updateLoanCalculation',
    });

    var totalLoanAmount = 0, netLenderFundsToBorrower = 0, homeValue = 0;
    var assessedValue = 0;
    var ARV = 0;
    var typeOfHMLOLoanRequesting = '';
    let totalDailyInterestCharge = 0;
    var diemDays = '';
    var closingCostFinanced = 0;
    let prepaidInterestReserve = 0;
    let lien1Rate = 0;
    let marketLTV = 0;

    typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
    assessedValue = getFieldsValue('assessedValue');
    homeValue = getFieldsValue('homeValue');
    lien1Rate = getFieldsValue('lien1Rate');
    diemDays = getTextValue('diemDays');
    closingCostFinanced = getFieldsValue('closingCostFinanced');

    if ($('input[name="haveInterestreserve"]:checked').val() === 'Yes') {
        prepaidInterestReserve = getFieldsValue('prepaidInterestReserve');
    }

    if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {
        totalLoanAmount = getFieldsValue('LOCTotalLoanAmt');

    } else if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
        typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.REFINANCE) {
        totalLoanAmount = getTextValue('coTotalAmt');            // txt - Inner text Value

    } else {
        totalLoanAmount = getFieldsValue('totalLoanAmount1');
    }

    /* ARV */
    if (assessedValue > 0) {
        ARV = (parseFloat(totalLoanAmount) / parseFloat(assessedValue)) * 100;
    }
    ARV = autoNumericConverter(ARV.toFixed(2));

    let _ARV = $('#ARV');
    if (_ARV.length > 0) {
        _ARV.html(ARV);
    }
    let _fullARVTooltip = $("#fullARVTooltip");
    if (_fullARVTooltip.length > 0) {
        let fullARVTooltip = ` ${totalLoanAmount} / ${assessedValue} * 100 = ${ARV} %`;
        fullARVTooltip = _fullARVTooltip.data('formula') + '<hr>' + fullARVTooltip;
        _fullARVTooltip.attr('data-original-title', fullARVTooltip);
    }

    let acquisitionPriceFinanced = getTextValue('acquisitionPriceFinanced') ?? 0;
    let _marketLTVToolTip = $("#marketLTVToolTip");
    let marketLTVFormula = ``;
    let marketLTVTooltipWithValues = ``;

    /* Market LTV Calculation */
    if (typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
        typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT ||
        typeOfHMLOLoanRequesting === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND ||
        typeOfHMLOLoanRequesting === TransactionType.REFINANCE) {
        if (homeValue > 0) {
            marketLTV = (parseFloat(totalLoanAmount) / parseFloat(homeValue)) * 100;
            marketLTV = marketLTV.toFixed(2);
        }
        marketLTVTooltipWithValues = ` ${totalLoanAmount} / ${homeValue} * 100 = ${marketLTV} %`;
        marketLTVFormula = "Market LTV = (Total Loan Amount / Property Value(As-Is)) * 100<hr>This is a percentage of your loan to values based off the market value,  not the purchase price." + "<hr>" + marketLTVTooltipWithValues;
    } else {
        if (homeValue > 0) {
            //89_CR_07_02_2019
            marketLTV = ((parseFloat(acquisitionPriceFinanced) + parseFloat(closingCostFinanced)) / parseFloat(homeValue)) * 100;
            marketLTV = marketLTV.toFixed(2);
        }
        marketLTVTooltipWithValues = ` ( ${acquisitionPriceFinanced} + ${closingCostFinanced} ) / ${homeValue} * 100 = ${marketLTV} %`;
        marketLTVFormula = "Market LTV = (Initial Loan Amount + Closing Cost Financed ) / Assessed As-is Value) * 100<hr>This is a percentage of your loan to values based off the market value, not the purchase price." + "<hr>" + marketLTVTooltipWithValues;
    }
    _marketLTVToolTip.attr('data-original-title', marketLTVFormula);

    let _marketLTV = $('#marketLTV');
    if (_marketLTV.length > 0) {
        _marketLTV.html(marketLTV);
    }

    let LTC;
    let totalProjectCost = getTextValue('totalProjectCost') ?? 0;
    /* Loan To Cost */
    if (totalProjectCost > 0) {
        LTC = (parseFloat(totalLoanAmount) / parseFloat(totalProjectCost)) * 100;
        LTC = LTC.toFixed(2);
    }
    //setDailyInterestCharge
    let _LoanToCost = $('#Loan-to-Cost');
    if (_LoanToCost.length > 0) {
        _LoanToCost.html(LTC);
    }

    let rehabCostFinanced = getFieldsValue('rehabCostFinanced') ?? 0;
    let rehabCost = getFieldsValue('rehabCost') ?? 0;
    let perRehabCostFinanced;
    let _rehabCostFinancedTooltip = $('#rehabCostFinancedTooltip');
    let rehabCostFinancedTooltip = null;
    let _perRehabCostFinanced = $('#perRehabCostFinanced');

    if (rehabCost > 0) {
        perRehabCostFinanced = (parseFloat(rehabCostFinanced) / parseFloat(rehabCost)) * 100;
        perRehabCostFinanced = perRehabCostFinanced.toFixed(2);
    }
    rehabCostFinancedTooltip = ` ${rehabCostFinanced} / ${rehabCost} * 100 = ${perRehabCostFinanced} %`;
    if (_perRehabCostFinanced.length > 0) {
        _perRehabCostFinanced.html(perRehabCostFinanced);
    }
    rehabCostFinancedTooltip = _rehabCostFinancedTooltip.data('formula') + '<hr>' + rehabCostFinancedTooltip;
    _rehabCostFinancedTooltip.attr('data-original-title', rehabCostFinancedTooltip);

}

function getDaysInMonth(year, month) {
    console.log({
        func: 'getDaysInMonth',
    });

    return new Date(year, month, 0).getDate();
}

class loanCalculation {
    static refinanceList = null;
    static nonInclusivePerDiem = null;

    static updateAccrualType(ele) {
        let accrualType = $(ele).val();
        $('.accrualTypeClass').val(accrualType);
    }

    static getaccrualTypeBaseValues() {
        console.log({
            func: 'getaccrualTypeBaseValues',
        });

        let _Y = 30;
        let _cDM;
        let _closingDateObj = $('#closingDate');
        if (_closingDateObj.is(":visible")) {
            _cDM = _closingDateObj.val(); //Closing Date Month
            if (_cDM !== '') {
                let _cDMArray = _cDM.split('/');
                _Y = getDaysInMonth(_cDMArray[2], _cDMArray[0]);
            }
        }

        let _Z = 360;
        let _X = 30;
        let _accrualTypeObj = $('#accrualType');
        if (_accrualTypeObj.is(":visible")) {
            let _accrualType = _accrualTypeObj.val();
            if (_accrualType === "Actual/360") {
                _Z = 360;
                _X = _Y;
            } else if (_accrualType === "365") {
                _Z = 365;
                _X = _Y;
            } else if (_accrualType === "360") {
                _Z = 360;
                _X = 30;
            } else if (_accrualType === "30/365") {
                _Z = 365;
                _X = 30;
            }
        }

        console.log({
            func: 'getaccrualTypeBaseValues',
            _X: _X,
            _Y: _Y,
            _Z: _Z,
            'Target Closing Date': _cDM,
            'Number Of Days': _Y,
        });

        return {
            '_X': _X,
            '_Y': _Y,
            '_Z': _Z
        }
    }

    static setLTCInitialLoanAmount() {
        console.log({
            func: 'setLTCInitialLoanAmount',
        });
        let LTCInitialLoanAmountToolTip;
        let costSpent = (getFieldsValue('costSpent') != '') ? replaceCommaValues(getFieldsValue('costSpent')) : 0;
        let costBasis;
        //= (getFieldsValue('costBasis') != '') ? replaceCommaValues(getFieldsValue('costBasis')) : 0;
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        let initialLoanAmount = 0;
        let LTCInitialLoanAmount = 0;

        if (isTransactionTypeRefinance(typeOfHMLOLoanRequesting)) {
            costBasis = replaceCommaValues($('.LTCOriginalPurchasePriceField:first').val());
        } else {
            costBasis = (getFieldsValue('costBasis') !== '') ? replaceCommaValues(getFieldsValue('costBasis')) : 0;
        }

        if (
            typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE
            || typeOfHMLOLoanRequesting === TransactionType.REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND
        ) {
            initialLoanAmount = getFieldsValue('CORTotalLoanAmt');
        } else {
            initialLoanAmount = getTextValue('acquisitionPriceFinanced');
        }
        if ($('.costSpent_disp').is(':visible')) {
            let propertyAsIsValue = costBasis + costSpent;
            if (propertyAsIsValue) {
                LTCInitialLoanAmount = (initialLoanAmount / (propertyAsIsValue)) * 100;
            }
            LTCInitialLoanAmountToolTip = `${initialLoanAmount} / ${propertyAsIsValue} * 100 `
        } else {
            if (costBasis) {
                LTCInitialLoanAmount = (initialLoanAmount / (costBasis)) * 100;
            }
            LTCInitialLoanAmountToolTip = `${initialLoanAmount} / ${costBasis} * 100 `
        }
        LTCInitialLoanAmount = autoNumericConverter(LTCInitialLoanAmount.toFixed(2));
        $('#LTCInitialLoanAmount').html(LTCInitialLoanAmount);

        let _LTCInitialLoanAmountToolTip = $('#LTCInitialLoanAmountToolTip');
        LTCInitialLoanAmountToolTip = _LTCInitialLoanAmountToolTip.data('formula') + '<hr>' + LTCInitialLoanAmountToolTip + " = " + LTCInitialLoanAmount + " %";
        _LTCInitialLoanAmountToolTip.attr('data-original-title', LTCInitialLoanAmountToolTip);
    }

    static setLTCMarketValue() {
        console.log({
            func: 'setLTCMarketValue',
        });
        let LTCMarketValueToolTip;
        let costSpent = (getFieldsValue('costSpent') != '') ? replaceCommaValues(getFieldsValue('costSpent')) : 0;
        let homeValue = (getFieldsValue('homeValue') != '') ? replaceCommaValues(getFieldsValue('homeValue')) : 0;
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        let initialLoanAmount = 0;
        let LTCMarketValue = 0;

        if (
            typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE
            || typeOfHMLOLoanRequesting === TransactionType.REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND
        ) {
            initialLoanAmount = getFieldsValue('CORTotalLoanAmt');
        } else {
            initialLoanAmount = getTextValue('acquisitionPriceFinanced');
        }
        if ($('.costSpent_disp').is(':visible')) {
            let propertyAsIsValue = homeValue + costSpent
            if (propertyAsIsValue) {
                LTCMarketValue = (initialLoanAmount / (propertyAsIsValue)) * 100;
            }
            LTCMarketValueToolTip = `${initialLoanAmount} / ${propertyAsIsValue} * 100 `;
        } else {
            if (homeValue) {
                LTCMarketValue = (initialLoanAmount / (homeValue)) * 100;
            }
            LTCMarketValueToolTip = `${initialLoanAmount} / ${homeValue} * 100 `;
        }
        LTCMarketValue = autoNumericConverter(LTCMarketValue.toFixed(2));
        $('#LTCMarketValue').html(LTCMarketValue);

        let _LTCMarketValueToolTip = $('#LTCMarketValueToolTip');
        LTCMarketValueToolTip = _LTCMarketValueToolTip.data('formula') + '<hr>' + LTCMarketValueToolTip + " = " + LTCMarketValue + " %";
        _LTCMarketValueToolTip.attr('data-original-title', LTCMarketValueToolTip);
    }

    static setLTCOriginalPurchasePriceValue() {
        console.log({
            func: 'setLTCOriginalPurchasePriceValue',
        });
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        let initialLoanAmount;
        let costSpent;
        let originalPurchasePrice;
        let LTCOriginalPurchasePriceValue;

        if ($.inArray(typeOfHMLOLoanRequesting, this.refinanceList) !== -1) {
            initialLoanAmount = getFieldsValue('CORTotalLoanAmt');
            costSpent = getFieldsValue('costSpent');
            originalPurchasePrice = replaceCommaValues($('.LTCOriginalPurchasePriceField:first').val());
            LTCOriginalPurchasePriceValue = initialLoanAmount / (costSpent + originalPurchasePrice) * 100;

            $('.LTCOriginalPurchasePriceCls').removeClass(' d-none ');
            $('#LTCOriginalPurchasePriceValue').html(LTCOriginalPurchasePriceValue.toFixed(2));
        } else {
            $('.LTCOriginalPurchasePriceCls').addClass('d-none');
        }
    }

    static init() {
        loanCalculation.refinanceList = typeof refinanceList !== 'undefined' ? refinanceList : null;
        $(document).on('change', '.LTCOriginalPurchasePriceField:first', function () {
            loanCalculation.setLTCOriginalPurchasePriceValue();
        });
    }

    static calculateGrossProfit() {
        console.log({
            func: 'calculateGrossProfit',
        });
        let grossProfit = 0;
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        let arv = replaceCommaValues(getFieldsValue('assessedValue'));
        let purchasePrice = replaceCommaValues(getFieldsValue('costBasis'));
        let originalPurchasePrice = replaceCommaValues(getFieldsValue('originalPurchasePrice_1'));
        let costSpent = replaceCommaValues(getFieldsValue('costSpent'));
        let rehabCost = replaceCommaValues(getFieldsValue('rehabCost'));
        let propertyNeedRehab = $('input[name=propertyNeedRehab]:checked').val();
        let grossProfitTooltip = '';
        let _grossProfitTooltip = $('#grossProfitTooltip');

        if (propertyNeedRehab === 'Yes') {
            if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE
                || typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE) {
                grossProfit = arv - originalPurchasePrice - costSpent - rehabCost;
                grossProfitTooltip = "ARV - Original Purchase Price - Cost Spent - Rehab/Construction Cost " + "<hr>" + `${arv} - ${originalPurchasePrice} - ${costSpent} - ${rehabCost}  `;
            } else {
                grossProfit = arv - purchasePrice - costSpent - rehabCost;
                grossProfitTooltip = "ARV - Purchase Price - Cost Spent - Rehab/Construction Cost " + "<hr>" + `${arv} - ${purchasePrice} - ${costSpent} - ${rehabCost} `;
            }
        }
        loanCalculation.calculateGrossProfitMargin(grossProfit);

        grossProfit = autoNumericConverter(grossProfit.toFixed(2));
        grossProfitTooltip = grossProfitTooltip + `= $ ${grossProfit}`;
        _grossProfitTooltip.attr('data-original-title', grossProfitTooltip);
        $('#grossProfit').html(grossProfit);
    }

    static calculateGrossProfitMargin(grossProfit) {
        console.log({
            func: 'calculateGrossProfitMargin',
        });
        let grossProfitMargin = 0;
        let grossProfitMarginTooltip = '';
        let arv = replaceCommaValues(getFieldsValue('assessedValue'));
        let propertyNeedRehab = $('input[name=propertyNeedRehab]:checked').val();
        if (propertyNeedRehab === 'Yes') {
            grossProfitMargin = ((grossProfit / arv) * 100);
        }
        grossProfitMargin = autoNumericConverter(grossProfitMargin.toFixed(2));
        grossProfitMarginTooltip = `${grossProfit} / ${arv} * 100 = ${grossProfitMargin} %`;
        $('#grossProfitMargin').html(grossProfitMargin);

        let _grossProfitMarginTooltip = $('#grossProfitMarginTooltip');
        grossProfitMarginTooltip = _grossProfitMarginTooltip.data('formula') + '<hr>' + grossProfitMarginTooltip;
        _grossProfitMarginTooltip.attr('data-original-title', grossProfitMarginTooltip);
    }

    static calculateAmortizationPayment(input) {

        let amount = (typeof (input.amount) !== "undefined" ? input.amount : 0).toString().replace(/[^\d.]/ig, ''),
            rate = (typeof (input.rate) !== "undefined" ? input.rate : 0).toString().replace(/[^\d.]/ig, ''),
            term = (typeof (input.term) !== "undefined" ? input.term : 0);

        if (term.match("y")) {
            term = parseInt(term.replace(/[^\d.]/ig, ''), 10) * 12;
        } else {
            term = parseInt(term.replace(/[^\d.]/ig, ''), 10);
        }

        let monthly_interest = rate / 100 / 12;

        // Now compute the monthly payment amount.
        let x = Math.pow(1 + monthly_interest, term),
            monthly = (amount * x * monthly_interest) / (x - 1);

        if (amount * rate * term > 0) {
            // Fill in the output fields, rounding to 2 decimal places
            return {
                original_amount: amount,
                payment_amount: monthly,
                payment_amount_formatted: monthly.toFixed(2),
                num_payments: term,
                total_payments: (monthly * term),
                total_payments_formatted: (monthly * term).toFixed(2),
                total_interest: ((monthly * term) - amount),
                total_interest_formatted: ((monthly * term) - amount).toFixed(2)
            };
        } else {
            // The numbers provided won't provide good data as results,
            return 0;
        }

    }

    static isAutoCalcTotalLoanAmountBasedOnLTC2() {
        return $('input[name=autoCalcTLAARV]:checked').val() === 'LTC2';
    }

    static isTransactionTypePurchaseCategory() {
        const purchaseTypes = [
            TransactionType.PURCHASE,
            TransactionType.DELAYED_PURCHASE,
            TransactionType.COMMERCIAL_PURCHASE,
            TransactionType.LINE_OF_CREDIT,
            TransactionType.TRANSACTIONAL,
            TransactionType.NEW_CONSTRUCTION_EXISTING_LAND,
        ];
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        return purchaseTypes.includes(typeOfHMLOLoanRequesting);
    }

    static isTransactionTypeRefinanceCategory() {
        const refinanceTypes = [
            TransactionType.CASH_OUT_REFINANCE,
            TransactionType.REFINANCE,
            TransactionType.RATE_TERM_REFINANCE,
            TransactionType.COMMERCIAL_CASH_OUT_REFINANCE,
            TransactionType.COMMERCIAL_RATE_TERM_REFINANCE,
        ];
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        return refinanceTypes.includes(typeOfHMLOLoanRequesting);
    }

    static calculateBaseAmountForLTC2() {
        console.log({
            func: 'calculateBaseAmountForLTC2',
        });
        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        let totalProjectCost;
        let TPCFormula;
        let TPCToolTipWithValues = '';

        let rehabCost = getFieldsValue('rehabCost'); //Rehab Budget
        let assessedValue = getFieldsValue('homeValue'); //property value AS-IS
        let costBasis = getFieldsValue('costBasis'); //Purchase Price
        let escrowFees = getFieldsValue('escrowFees'); //Estimated Settlement or Closing/Escrow Fee

        if (loanCalculation.isTransactionTypePurchaseCategory()) {
            totalProjectCost = costBasis + rehabCost + escrowFees;
            TPCFormula = "Total Project Value = Purchase Price " +
                "+ Rehab/Construction Cost " +
                "+ Estimated Settlement or Closing/Escrow Fee ";
            TPCToolTipWithValues = ` ${costBasis} + ${rehabCost} + ${escrowFees} `;
        } else if (loanCalculation.isTransactionTypeRefinanceCategory()) {
            totalProjectCost = assessedValue + rehabCost + escrowFees;
            TPCFormula = "Total Project Value = (Property Value(As-Is) " +
                "+ Rehab/Construction Cost " +
                "+ Estimated Settlement or Closing/Escrow Fee ";
            TPCToolTipWithValues = ` ${assessedValue} + ${rehabCost} + ${escrowFees} `;
        }
        TPCFormula += `<hr>${TPCToolTipWithValues} = $${totalProjectCost}`;
        return {
            projectCost: totalProjectCost,
            tooltip: TPCFormula,
        };
    }

    static lien1Rate;
    static noOfMonthsPrepaid;
    static haveInterestReserve;
    static prepaidInterestReserveFinanced;
    static totalLoanAmount;
    static LTCPercentage;
    static homeValue;
    static costBasis;
    static rehabCost;
    static escrowFees;
    static closingCostFinanced;
    static prepaidInterestReserve;
    static _totalLoanAmount;
    static _coTotalAmt;
    static totalLoanAmountTooltip;
    static totalLoanAmountTooltipWithValues;
    static brokerPointsRate;
    static originationPointsRate;

    static brokerPointsValue;
    static originationPointsValue;
    static _LTC2_additionalReserveInterest;
    static _LTC2_additionalOriginationInterest;
    static _LTC2_additionalReserveInterestTooltip;
    static _LTC2_additionalOriginationInterestTooltip;
    static _tLAToolTip;
    static _totalLoanAmountToolTip;
    static _maxAmtToPutDown;
    static _downPaymentPercentage;
    static _hideFieldsForLTC2;
    static _showDivForLTC2;
    static _enableFieldsForLTC2;
    static _LTC2_baseLoanAmountTooltip;
    static payOffMortgage1;
    static payOffMortgage2;
    static isFileSPREO;
    static PCID_SPREO_CAPITAL = '8e3a1284b1b58f31';
    static totalCashToCloseTooltip;
    static totalCashToCloseTooltipWithValues;
    static _totalCashToCloseTooltip;
    static totalCashOut;
    static totalCashOutToolTip;
    static _totalCashOutToolTip;
    static totalCashOutToolTipWithValues;
    static contingencyReserve;
    static contingencyReserveAmt;
    static _contingencyReserveAmt;
    static contingencyReserveAmtTooltip;
    static _contingencyReserveAmtTooltip;
    static contingencyReserveAmtTooltipWithValues;
    static currentLoanBalance;
    static currentLoanBalanceTooltip;
    static _currentLoanBalanceTooltip;

    static currentLoanBalanceTooltipWithValues;
    static closingCostNotFinancedTooltip;
    static closingCostNotFinancedTooltipWithValues;
    static downPaymentHtmlTooltip;
    static downPaymentHtmlTooltipWithValues;

    static initializeVariables() {

        this._totalLoanAmount = $('#totalLoanAmount1'); //Purchase Total Loan Amount
        this._maxAmtToPutDown = $('#maxAmtToPutDown');  //Down Payment
        this._downPaymentPercentage = $('#downPaymentPercentage');
        this._hideFieldsForLTC2 = $('.hideFieldsForLTC2');
        this._showDivForLTC2 = $('.showDivForLTC2');
        this._enableFieldsForLTC2 = $('.enableFieldsForLTC2');

        this.lien1Rate = getFieldsValue('lien1Rate');
        this.noOfMonthsPrepaid = getFieldsValue('noOfMonthsPrepaid');
        this.haveInterestReserve = $('input[name="haveInterestreserve"]:checked').val(); //Have Interest Reserve
        this.prepaidInterestReserveFinanced = (this.haveInterestReserve === 'Yes' ? getFieldsValue('prepaidInterestReserve') : 0); //Prepaid Interest Reserve (if financed)
        this.prepaidInterestReserve = getFieldsValue('prepaidInterestReserve'); //Prepaid Interest Reserve

        this.LTCPercentage = getFieldsValue('maxLTCPer'); //LTC Percentage
        this.homeValue = getFieldsValue('homeValue'); //Property As-is
        this.costBasis = getFieldsValue('costBasis'); //Purchase Price
        this.rehabCost = getFieldsValue('rehabCost'); //Rehab Budget
        this.escrowFees = getFieldsValue('escrowFees'); //Estimated Settlement or Closing/Escrow Fee
        this.closingCostFinanced = getFieldsValue('closingCostFinanced'); //Estimated Settlement or Closing/Escrow Fee

        this._coTotalAmt = $('#coTotalAmt'); //Refinance Total Loan Amount
        this.brokerPointsRate = getFieldsValue('brokerPointsRate');
        this.originationPointsRate = getFieldsValue('originationPointsRate');
        this.brokerPointsValue = getFieldsValue('brokerPointsValue');
        this.originationPointsValue = getFieldsValue('originationPointsValue');
        this.lien1Rate = getFieldsValue('lien1Rate');
        this.noOfMonthsPrepaid = getFieldsValue('noOfMonthsPrepaid');
        this._LTC2_additionalReserveInterest = $('#LTC2_additionalReserveInterest');
        this._LTC2_additionalReserveInterestTooltip = $('#LTC2_additionalReserveInterestTooltip');

        this._LTC2_additionalOriginationInterest = $('#LTC2_additionalOriginationInterest');
        this._LTC2_additionalOriginationInterestTooltip = $('#LTC2_additionalOriginationInterestTooltip');

        this._tLAToolTip = $('#tLAToolTip');
        this._totalLoanAmountToolTip = $('#totalLoanAmountToolTip');
        this._LTC2_baseLoanAmountTooltip = $('#LTC2_baseLoanAmountTooltip');

        this._CORTotalLoanAmt = $('#CORTotalLoanAmt');
        this._hideFieldsForLTC2_RefinanceCategory = $('.hideFieldsForLTC2_RefinanceCategory');
        this._CORefiLTVPercentage = $('#CORefiLTVPercentage');

        this.payOffMortgage1 = getFieldsValue('payOffMortgage1'); //Pay-Off on Mortgage 1
        this.payOffMortgage2 = getFieldsValue('payOffMortgage2'); //Pay-Off on Mortgage 2
        this.isFileSPREO = this.getEncryptedPCID() === loanCalculation.PCID_SPREO_CAPITAL;
        this._totalCashToCloseTooltip = $('#totalCashToCloseTooltip');
        this._totalCashOutToolTip = $('#totalCashOutToolTip');
        this._contingencyReserveAmtTooltip = $('#contingencyReserveAmtTooltip');
        this.contingencyReserveAmt = $('#contingencyReserveAmt');
    }

    static getEncryptedPCID() {
        return $('#encryptedPCID').val();
    }

    static initLTC2() {

        loanCalculation.initializeVariables();

        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            this._totalLoanAmount.attr('readonly', false);
            this._maxAmtToPutDown.attr('readonly', false);
            this._downPaymentPercentage.attr('readonly', false);
         /*   if (!this.isFileSPREO) {
                this._hideFieldsForLTC2.show();
            }*/
            this._hideFieldsForLTC2.show();
            this._showDivForLTC2.hide();
            this._enableFieldsForLTC2.attr('disabled', true);

            this._CORTotalLoanAmt.attr('readonly', false);
            this._hideFieldsForLTC2_RefinanceCategory.show();
            return null;
        }

        this._totalLoanAmount.attr('readonly', true);
        this._maxAmtToPutDown.attr('readonly', true);
        this._downPaymentPercentage.attr('readonly', true);
        this._hideFieldsForLTC2.hide();

        if (loanCalculation.isTransactionTypePurchaseCategory()) {
            this._showDivForLTC2.show();
            this._enableFieldsForLTC2.attr('disabled', false);
        } else if (loanCalculation.isTransactionTypeRefinanceCategory) {
            this._showDivForLTC2.hide();
            this._enableFieldsForLTC2.val('');
            this._enableFieldsForLTC2.attr('disabled', true);

            this._CORTotalLoanAmt.attr('readonly', true);
            this._CORefiLTVPercentage.val('');
            this._hideFieldsForLTC2_RefinanceCategory.hide();
        }

        loanCalculation.calculateOriginationPointValueForLTC2();
        loanCalculation.calculateBrokerPointsValueForLTC2();

        loanCalculation.calculateBaseLoanAmountForLTC2();

        loanCalculation.calculateAdditionalReserveInterestForLTC2();
        loanCalculation.calculateAdditionalOriginationInterestForLTC2();

        loanCalculation.calculateTotalLoanAmountForLTC2();
        loanCalculation.calculateDownPaymentForLTC2();
        loanCalculation.calculateDownPaymentPercentageForLTC2();

        /*
            if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
                calculateOriginationPointsValue();
                calculateBrokerPointsValue();
            } else {
                loanCalculation.calculateOriginationRateForLTC2();
                loanCalculation.calculateBrokerPointsRateForLTC2();
            }
        */

        loanCalculation.calculateTotalCashToCloseForLTC2();
        loanCalculation.calculateTotalCashOutForLTC2();
        loanCalculation.calculateTotalProjectCostForLTC2();
        loanCalculation.calculateInitialLoanAmount();
    }

    static calculateTotalProjectCostForLTC2() {
        console.log({
            func: 'calculateTotalProjectCostForLTC2',
        });
        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        let ltcBaseAmountObj = loanCalculation.calculateBaseAmountForLTC2()
        if ('projectCost' in ltcBaseAmountObj) {
            $('#totalProjectCost').html(autoNumericConverter(ltcBaseAmountObj.projectCost.toFixed(2)));
        }
        if ('tooltip' in ltcBaseAmountObj) {
            $('#TPCToolTip').attr('data-original-title', ltcBaseAmountObj.tooltip);
        }
    }

    static LTC2_baseLoanAmount = null;
    static LTC2_baseLoanAmountTooltip = null;
    static LTC2_baseLoanAmountTooltipWithValues = null;
    static LTC2_baseLoanAmountFormatted = null;

    static calculateBaseLoanAmountForLTC2() {

        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        //  if (loanCalculation.isTransactionTypePurchaseCategory()) {

        this.LTC2_baseLoanAmountTooltip =
            "((LTC% * (Purchase Price + Rehab Budget + Estimated Settlement or Closing/Escrow Fee)) + " +
            "(Closing Cost Financed + Prepaid Interest Reserve (if financed)))";

        this.LTC2_baseLoanAmount = (this.LTCPercentage / 100 * (this.costBasis + this.rehabCost + this.escrowFees)) + (this.closingCostFinanced + this.prepaidInterestReserveFinanced);

        this.LTC2_baseLoanAmountFormatted = autoNumericConverter(this.LTC2_baseLoanAmount.toFixed(2));

        this.LTC2_baseLoanAmountTooltipWithValues = `(${this.LTCPercentage} / 100 * (${this.costBasis} + ${this.rehabCost} + ${this.escrowFees})) + ${this.closingCostFinanced} + ${this.prepaidInterestReserveFinanced})`;

        let LTC2_baseLoanAmountTooltip = this.LTC2_baseLoanAmountTooltip
            + '<hr>' + this.LTC2_baseLoanAmountTooltipWithValues
            + '<hr>' + this.LTC2_baseLoanAmountFormatted
        ;
        this._LTC2_baseLoanAmountTooltip.attr('data-content', LTC2_baseLoanAmountTooltip);
        //  }
    }

    static LTC2_additionalReserveInterest = null;
    static LTC2_additionalReserveInterestTooltip = null;
    static LTC2_additionalReserveInterestTooltipWithValues = null;
    static LTC2_additionalReserveInterestFormatted = null;

    static calculateAdditionalReserveInterestForLTC2() {

        if (loanCalculation.isTransactionTypeRefinanceCategory()) {
            return;
        }
        this.LTC2_additionalReserveInterestTooltip = '<b>IF( Pre-paid Interest reserve is YES)</b> <br> Interest Rate / 1200 *  ((LTC%*(Purchase Price + Rehab Budget + Estimated Settlement or Closing/Escrow Fee) + (Closing Cost Financed + Prepaid Interest Reserve IF Financed)) * noOfMonthsPrepaid) - Prepaid Interest Reserve if Financed <br> <b>  Else  </b><br> 0 ';

        this.LTC2_additionalReserveInterest = this.haveInterestReserve === 'Yes' ? (this.lien1Rate / 12 / 100)
            * ((this.LTC2_baseLoanAmount * this.noOfMonthsPrepaid))
            - this.prepaidInterestReserveFinanced : 0;

        this.LTC2_additionalReserveInterestTooltipWithValues = this.haveInterestReserve === 'Yes' ? `(${this.lien1Rate} / 12 / 100) * (`
            + this.LTC2_baseLoanAmountTooltipWithValues + ` * ` + this.noOfMonthsPrepaid
            + `) - ${this.prepaidInterestReserveFinanced}` : `0`;

        this.LTC2_additionalReserveInterestFormatted = autoNumericConverter(this.LTC2_additionalReserveInterest.toFixed(2))

        this._LTC2_additionalReserveInterest.val(this.LTC2_additionalReserveInterestFormatted);

        let LTC2_additionalReserveInterestTooltip = this.LTC2_additionalReserveInterestTooltip
            + '<hr>'
            + this.LTC2_additionalReserveInterestTooltipWithValues
            + '<hr>'
            + this.LTC2_additionalReserveInterestFormatted
        ;

        this._LTC2_additionalReserveInterestTooltip.attr('data-content', LTC2_additionalReserveInterestTooltip);
    }

    static LTC2_additionalOriginationInterest = null;
    static LTC2_additionalOriginationInterestTooltip = null;
    static LTC2_additionalOriginationInterestTooltipWithValues = null;
    static LTC2_additionalOriginationInterestFormatted = null;

    static calculateAdditionalOriginationInterestForLTC2() {

        if (loanCalculation.isTransactionTypeRefinanceCategory()) {
            return;
        }
        this.LTC2_additionalOriginationInterest = (this.brokerPointsRate / 100 + this.originationPointsRate / 100)
            * (this.LTC2_baseLoanAmount)
            - (this.originationPointsValue + this.brokerPointsValue);

        this.LTC2_additionalOriginationInterestTooltip = '(Origination Point % + Broker Point % as decimals) * ((LTC%*(Purchase Price + Rehab Budget + Estimated Settlement or Closing/Escrow Fee)) + (Closing Cost Financed + Prepaid Interest Reserve(if financed))) - (Origination Fee + Broker Fee)';

        this.LTC2_additionalOriginationInterestTooltipWithValues = `(${this.originationPointsRate} / 100 + ${this.brokerPointsRate} / 100) * (` + this.LTC2_baseLoanAmountTooltipWithValues + `) - (${this.originationPointsValue} + ${this.brokerPointsValue})`;

        this.LTC2_additionalOriginationInterestFormatted = autoNumericConverter(this.LTC2_additionalOriginationInterest.toFixed(2))
        this._LTC2_additionalOriginationInterest.val(this.LTC2_additionalOriginationInterestFormatted);

        let LTC2_additionalOriginationInterestTooltip = this.LTC2_additionalOriginationInterestTooltip
            + '<hr>'
            + this.LTC2_additionalOriginationInterestTooltipWithValues
            + '<hr>'
            + this.LTC2_additionalOriginationInterestFormatted
        ;

        this._LTC2_additionalOriginationInterestTooltip.attr('data-content', LTC2_additionalOriginationInterestTooltip);
    }

    static calculateTotalLoanAmountForLTC2() {

        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }

        if (loanCalculation.isTransactionTypePurchaseCategory()) {

            this.totalLoanAmount = this.LTC2_baseLoanAmount
                + this.LTC2_additionalReserveInterest
                + this.LTC2_additionalOriginationInterest
            ;

            this.totalLoanAmountTooltip = this.LTC2_baseLoanAmountTooltip
                + " + Additional Reserve Interest "
                + " + Additional Origination Interest  "

            let totalCashOutFormatted = autoNumericConverter(this.totalLoanAmount.toFixed(2));
            this._totalLoanAmount.val(totalCashOutFormatted);

            this.totalLoanAmountTooltipWithValues = this.LTC2_baseLoanAmountTooltipWithValues
                + "+" + this.LTC2_additionalReserveInterestFormatted
                + "+" + this.LTC2_additionalOriginationInterestFormatted
            ;

            let totalLoanAmountTooltip =
                ' Base Amount Formula ' + '<br>' + this.LTC2_baseLoanAmountTooltip
                + '<br>' + 'Base Amount Calculation = ' + this.LTC2_baseLoanAmountTooltipWithValues
                + '<br>' + 'Base Amount = ' + this.LTC2_baseLoanAmount
                + '<hr>' + this.totalLoanAmountTooltip
                + '<hr>'
                + this.totalLoanAmountTooltipWithValues
                + '<hr>'
                + totalCashOutFormatted
            ;
            this._tLAToolTip.attr('data-content', totalLoanAmountTooltip);

        } else if (loanCalculation.isTransactionTypeRefinanceCategory()) {

            // Calculate total loan amount
            this.totalLoanAmount =
                (this.LTCPercentage / 100 * (this.homeValue + this.rehabCost + this.escrowFees)) +
                (this.closingCostFinanced + this.prepaidInterestReserveFinanced)
            ;

            // Initial tooltip description
            this.totalLoanAmountTooltip =
                "(LTC% * (Property As-Is Value + Rehab Budget + Estimated Settlement or Closing/Escrow Fee)) + (Closing Costs Financed + Prepaid Interest Reserve) ";

            let totalCashOutFormatted = autoNumericConverter(this.totalLoanAmount.toFixed(2));
            this._coTotalAmt.html(totalCashOutFormatted);

            // Append a detailed formula with actual values to the tooltip
            this.totalLoanAmountTooltipWithValues = `(${this.LTCPercentage} / 100 * (${this.homeValue} + ${this.rehabCost} + ${this.escrowFees})) + ${this.closingCostFinanced} + ${this.prepaidInterestReserveFinanced})
            `;

            let totalLoanAmountTooltip = this.totalLoanAmountTooltip
                + '<hr>'
                + this.totalLoanAmountTooltipWithValues
                + '<hr>'
                + totalCashOutFormatted
            ;
            // Update the tooltip
            this._totalLoanAmountToolTip.attr('data-content', totalLoanAmountTooltip);
        }
        console.log({
            'totalLoanAmount': this.totalLoanAmount,
            'totalLoanAmountTooltip': this.totalLoanAmountTooltip
        });
        return {
            'totalLoanAmount': this.totalLoanAmount,
            'totalLoanAmountTooltip': this.totalLoanAmountTooltip
        }
    }

    static calculateDownPaymentForLTC2() {

        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            this._maxAmtToPutDown.attr('readonly', false);
            return null;
        }
        let ltcBaseAmountObj = loanCalculation.calculateBaseAmountForLTC2()
        let downPayment; //Down Payment AMount
        let downPaymentPercentage = getFieldsValue('downPaymentPercentage'); //Down Payment Percentage
        let ltcBaseAmount = ('projectCost' in ltcBaseAmountObj ? ltcBaseAmountObj.projectCost : 0);

        downPayment = ltcBaseAmount * (downPaymentPercentage / 100);
        this._maxAmtToPutDown.val(autoNumericConverter(downPayment.toFixed(2)))
        this._maxAmtToPutDown.attr('readonly', true);

    }

    static calculateDownPaymentPercentageForLTC2() {

        let _downPaymentPercentage = $('#downPaymentPercentage'); //Down Payment
        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            _downPaymentPercentage.attr('readonly', false);
            return null;
        }
        let maxLTCPer = getFieldsValue('maxLTCPer'); //LTC-2
        let downPaymentPercentage = parseFloat((100 - maxLTCPer).toFixed(2));
        _downPaymentPercentage.val(downPaymentPercentage);
        _downPaymentPercentage.attr('readonly', true);

    }

    static calculateOriginationRateForLTC2() {

        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        let originationPointsRate; //Origination Points Value
        let _originationPointsRate = $('#originationPointsRate'); //Origination Points Rate
        let originationPointsValue = getFieldsValue('originationPointsValue'); //Origination Points Rate
        //let ltcBaseAmountObj = loanCalculation.calculateBaseAmountForLTC2()
        //let ltcBaseAmount = ('projectCost' in ltcBaseAmountObj ? ltcBaseAmountObj.projectCost : 0);
        let LTCPercentage = getFieldsValue('maxLTCPer'); //LTC Percentage
        let costBasis = getFieldsValue('costBasis'); //Purchase Price
        let rehabCost = getFieldsValue('rehabCost'); //Rehab Budget
        let escrowFees = getFieldsValue('escrowFees'); //Estimated Settlement or Closing/Escrow Fee

        let totalLoanAmount = loanCalculation.isTransactionTypePurchaseCategory() ?
            (LTCPercentage / 100 * (costBasis + rehabCost + escrowFees)) :
            (loanCalculation.isTransactionTypeRefinanceCategory() ? getFieldsValue('coTotalAmt') : 0);

        this.originationPointsRate = originationPointsValue / totalLoanAmount * 100;
        _originationPointsRate.val(this.originationPointsRate);
    }


    static calculateOriginationPointValueForLTC2() {
        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        let originationPointsValue; //Origination Points Value
        let originationPointsRate = getFieldsValue('originationPointsRate'); //Origination Points Rate
        let _originationPointsValue = $('#originationPointsValue'); //Origination Points Rate
        //let ltcBaseAmountObj = loanCalculation.calculateBaseAmountForLTC2()
        //let ltcBaseAmount = ('projectCost' in ltcBaseAmountObj ? ltcBaseAmountObj.projectCost : 0);
        let LTCPercentage = getFieldsValue('maxLTCPer'); //LTC Percentage
        let costBasis = getFieldsValue('costBasis'); //Purchase Price
        let rehabCost = getFieldsValue('rehabCost'); //Rehab Budget
        let escrowFees = getFieldsValue('escrowFees'); //Estimated Settlement or Closing/Escrow Fee


        let total = loanCalculation.isTransactionTypePurchaseCategory() ?
            (LTCPercentage / 100 * (costBasis + rehabCost + escrowFees)) :
            (loanCalculation.isTransactionTypeRefinanceCategory() ? getFieldsValue('coTotalAmt') : 0);

        this.originationPointsValue = total * (originationPointsRate / 100);
        _originationPointsValue.val(autoNumericConverter(this.originationPointsValue.toFixed(2)));

        console.log({
            'originationPointsValue': this.originationPointsValue,
            'total': total,
            'originationPointsRate': originationPointsRate,
        });
    }

    static calculateBrokerPointsRateForLTC2() {
        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        let brokerPointsRate; //Broker Points Rate
        let _brokerPointsRate = $('#brokerPointsRate'); //Broker Points Rate
        let brokerPointsValue = getFieldsValue('brokerPointsValue'); //Broker Points Value
        //let ltcBaseAmountObj = loanCalculation.calculateBaseAmountForLTC2()
        //let ltcBaseAmount = ('projectCost' in ltcBaseAmountObj ? ltcBaseAmountObj.projectCost : 0);
        let LTCPercentage = getFieldsValue('maxLTCPer'); //LTC Percentage
        let costBasis = getFieldsValue('costBasis'); //Purchase Price
        let rehabCost = getFieldsValue('rehabCost'); //Rehab Budget
        let escrowFees = getFieldsValue('escrowFees'); //Estimated Settlement or Closing/Escrow Fee

        let total = loanCalculation.isTransactionTypePurchaseCategory() ?
            (LTCPercentage / 100 * (costBasis + rehabCost + escrowFees)) :
            (loanCalculation.isTransactionTypeRefinanceCategory() ? getFieldsValue('coTotalAmt') : 0);

        this.brokerPointsRate = brokerPointsValue / total * 100;
        _brokerPointsRate.val(this.brokerPointsRate);
    }

    static calculateBrokerPointsValueForLTC2() {

        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        let brokerPointsValue; //Broker Points Value
        let brokerPointsRate = getFieldsValue('brokerPointsRate'); //Broker Points Rate
        let _brokerPointsValue = $('#brokerPointsValue'); //Broker Points Rate
        //let ltcBaseAmountObj = loanCalculation.calculateBaseAmountForLTC2()
        //let ltcBaseAmount = ('projectCost' in ltcBaseAmountObj ? ltcBaseAmountObj.projectCost : 0);
        let LTCPercentage = getFieldsValue('maxLTCPer'); //LTC Percentage
        let costBasis = getFieldsValue('costBasis'); //Purchase Price
        let rehabCost = getFieldsValue('rehabCost'); //Rehab Budget
        let escrowFees = getFieldsValue('escrowFees'); //Estimated Settlement or Closing/Escrow Fee

        let total = loanCalculation.isTransactionTypePurchaseCategory() ?
            (LTCPercentage / 100 * (costBasis + rehabCost + escrowFees)) :
            (loanCalculation.isTransactionTypeRefinanceCategory() ? getFieldsValue('coTotalAmt') : 0);

        this.brokerPointsValue = total * (brokerPointsRate / 100);
        _brokerPointsValue.val(autoNumericConverter(this.brokerPointsValue.toFixed(2)));

    }

    static calculateTotalCashToCloseForLTC2() {
        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        let totalCashToClose = 0;
        let totalCashToCloseFormatted = 0;
        if (loanCalculation.isTransactionTypePurchaseCategory()) {
            let _totalCashToClose = $('#totalCashToClose');

            //let totalLoanAmount = getFieldsValue('totalLoanAmount1'); //Purchase Total Loan Amount
            let downPayment = getFieldsValue('maxAmtToPutDown'); //Down Payment
            let originationPointsValue = getFieldsValue('originationPointsValue'); //Origination Points Value
            let brokerPointsValue = getFieldsValue('brokerPointsValue'); //Broker Points Value
            let haveInterestReserve = $('input[name="haveInterestreserve"]:checked').val(); //Have Interest Reserve
            let prepaidInterestReserveNotFinanced = haveInterestReserve === 'No' ? getFieldsValue('prepaidInterestReserve') : 0; //Prepaid Interest Reserve (if not financed)
            let closingCostFinanced = getFieldsValue('closingCostFinanced'); //Closing Cost Financed
            let prepaidInterestReserveFinanced = haveInterestReserve === 'Yes' ? getFieldsValue('prepaidInterestReserve') : 0; //Prepaid Interest Reserve (if financed)

            totalCashToClose = downPayment
                + originationPointsValue
                + brokerPointsValue
                + prepaidInterestReserveNotFinanced
                - closingCostFinanced
            //- prepaidInterestReserveFinanced
            ;
            totalCashToCloseFormatted = autoNumericConverter(totalCashToClose.toFixed(2));
            _totalCashToClose.html(totalCashToCloseFormatted);

            loanCalculation._totalCashToCloseTooltip = $('#totalCashToCloseTooltip');
            loanCalculation.totalCashToCloseTooltip = 'Down Payment + Origination Points + Broker Points + Prepaid Interest Reserve (if not financed) - Closing Costs Financed';
            loanCalculation.totalCashToCloseTooltipWithValues = `(${downPayment} 
                            + ${originationPointsValue}
                            + ${brokerPointsValue}
                            + ${prepaidInterestReserveNotFinanced}
                            - ${closingCostFinanced}
                            )`;

            let totalCashToCloseTooltip = loanCalculation.totalCashToCloseTooltip
                + '<hr>' + loanCalculation.totalCashToCloseTooltipWithValues
                + '<hr>' + totalCashToCloseFormatted
            ;
            loanCalculation._totalCashToCloseTooltip.attr('data-content', totalCashToCloseTooltip);

        }
        return totalCashToClose;
    }

    static calculateTotalCashToCloseForSPREO() {
        let totalCashToCloseForSPREO = 0;
        let totalCashToCloseForSPREOFormatted = 0;

        if (loanCalculation.isTransactionTypePurchaseCategory()) {
            let _totalCashToClose = $('#totalCashToClose');
            let initialLoanAmount = getFieldsValue('acquisitionPriceFinanced');  //initial Loan Amount
            let closingCostFinanced = getFieldsValue('closingCostFinanced');      // int - Input Fields Value

            totalCashToCloseForSPREO = initialLoanAmount - closingCostFinanced;

            totalCashToCloseForSPREOFormatted = autoNumericConverter(totalCashToCloseForSPREO.toFixed(2));
            _totalCashToClose.html(totalCashToCloseForSPREOFormatted);

            loanCalculation._totalCashToCloseTooltip = $('#totalCashToCloseTooltip');
            loanCalculation.totalCashToCloseTooltip = 'Initial Loan Amount - Closing Costs Financed';
            loanCalculation.totalCashToCloseTooltipWithValues = `(${initialLoanAmount} - ${closingCostFinanced})`;

            let totalCashToCloseTooltip = loanCalculation.totalCashToCloseTooltip
                + '<hr>' + loanCalculation.totalCashToCloseTooltipWithValues
                + '<hr>' + totalCashToCloseForSPREOFormatted
            ;
            loanCalculation._totalCashToCloseTooltip.attr('data-content', totalCashToCloseTooltip);
        }
        return totalCashToCloseForSPREO;
    }


    static calculateTotalCashOutForLTC2() {
        if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            return null;
        }
        loanCalculation.totalCashOut = 0;
        if (loanCalculation.isTransactionTypeRefinanceCategory()) {
            let totalLoanAmount = getFieldsValue('coTotalAmt'); //Refinance Total Loan Amount
            let payOffMortgage1 = getFieldsValue('payOffMortgage1'); //Pay-Off on Mortgage 1
            let payOffMortgage2 = getFieldsValue('payOffMortgage2'); //Pay-Off on Mortgage 2
            let payOffOutstandingTaxes = getFieldsValue('payOffOutstandingTaxes'); //Pay Off Outstanding Taxes
            let escrowFees = getFieldsValue('escrowFees'); //Settlement or Closing/Escrow Fee
            let originationPointsValue = getFieldsValue('originationPointsValue'); //Origination Points
            let brokerPointsValue = getFieldsValue('brokerPointsValue'); //Broker Points
            let rehabCost = getFieldsValue('rehabCost'); //Rehab Cost Financed
            let prepaidInterestReserve = getFieldsValue('prepaidInterestReserve'); //Rehab Cost Financed
            let LTCPercentage = getFieldsValue('maxLTCPer'); //LTC Percentage
            let homeValue = getFieldsValue('homeValue'); //Property As-is

            let _totalCashOut = $('#totalCashOut');

            let _totalCashToClose = $('#totalCashToClose');
            let totalCashToClose;

            /*          totalCashOut = totalLoanAmount
                          - payOffMortgage1
                          - payOffMortgage2
                          - payOffOutstandingTaxes
                          - escrowFees
                          - originationPointsValue
                          - brokerPointsValue
                          - rehabCost
                          - prepaidInterestReserve
                      ;*/

            loanCalculation.totalCashOut =
                (LTCPercentage / 100 * (homeValue + rehabCost + escrowFees))
                - payOffMortgage1
                - payOffMortgage2
                - payOffOutstandingTaxes
                - escrowFees
                - brokerPointsValue
                - originationPointsValue
                - rehabCost
                - prepaidInterestReserve
            ;


            loanCalculation.totalCashOut = parseFloat(loanCalculation.totalCashOut.toFixed(2)); // Ensure proper formatting
            const totalCashOutFormatted = autoNumericConverter(loanCalculation.totalCashOut);
            _totalCashOut.html(totalCashOutFormatted);

            totalCashToClose = loanCalculation.totalCashOut < 0 ? Math.abs(loanCalculation.totalCashOut) : -loanCalculation.totalCashOut;
            _totalCashToClose.html(autoNumericConverter(totalCashToClose.toFixed(2)));

            loanCalculation.totalCashOutToolTip = '((LTC%*(As-Is Value + Rehab Budget + Estimated Settlement or Closing/Escrow Fee)) - Pay-Off On Mortgage 1 - Pay-Off on Mortage 2 - Pay-Off Outstanding Taxes - Estimated Settlement or Closing/Escrow Fee - Broker Points - Origination Points - Rehab Cost - Prepaid interest Reserve)';

            loanCalculation.totalCashOutToolTipWithValues = `${LTCPercentage} / 100 * (${homeValue} + ${rehabCost} + ${escrowFees}))
                - ${payOffMortgage1}
                - ${payOffMortgage2}
                - ${payOffOutstandingTaxes}
                - ${escrowFees}
                - ${brokerPointsValue}
                - ${originationPointsValue}
                - ${rehabCost}
                - ${prepaidInterestReserve}`
            ;

            loanCalculation.totalCashOutToolTip = loanCalculation.totalCashOutToolTip
                + '<hr>' + loanCalculation.totalCashOutToolTipWithValues
                + '<hr>' + autoNumericConverter(loanCalculation.totalCashOut.toFixed(2))
            ;
            loanCalculation._totalCashOutToolTip.attr('data-content', loanCalculation.totalCashOutToolTip);
        }
        return loanCalculation.totalCashOut;
    }

    static calculateTotalCashOutForSPREO() {

        loanCalculation.totalCashOut = 0;
        if (loanCalculation.isTransactionTypeRefinanceCategory()) {
            let initialLoanAmountForTransactionType = getFieldsValue('CORTotalLoanAmt');  //initial Loan Amount
            let payOffMortgage1 = getFieldsValue('payOffMortgage1'); //Pay-Off on Mortgage 1
            let payOffMortgage2 = getFieldsValue('payOffMortgage2'); //Pay-Off on Mortgage 2
            let payOffOutstandingTaxes = getFieldsValue('payOffOutstandingTaxes'); //Pay Off Outstanding Taxes
            let closingCostFinanced = getFieldsValue('closingCostFinanced'); //Closing Cost Financed

            let _totalCashOut = $('#totalCashOut');

            let _totalCashToClose = $('#totalCashToClose');
            let totalCashToClose;

            loanCalculation.totalCashOut =
                initialLoanAmountForTransactionType
                - payOffMortgage1
                - payOffMortgage2
                - payOffOutstandingTaxes
                - closingCostFinanced
            ;
            loanCalculation.totalCashOut = parseFloat(loanCalculation.totalCashOut.toFixed(2)); // Ensure proper formatting
            const totalCashOutFormatted = autoNumericConverter(loanCalculation.totalCashOut);
            // Update DOM
            _totalCashOut.html(totalCashOutFormatted);

            totalCashToClose = loanCalculation.totalCashOut < 0 ? Math.abs(loanCalculation.totalCashOut) : -loanCalculation.totalCashOut;
            _totalCashToClose.html(autoNumericConverter(totalCashToClose.toFixed(2)));

            loanCalculation._totalCashOutToolTip = $('#totalCashOutToolTip');
            loanCalculation.totalCashOutToolTip = 'Initial Loan Amount - Payoffs - Closing Costs Financed';

            loanCalculation.totalCashOutToolTipWithValues = `${initialLoanAmountForTransactionType}
                - ${payOffMortgage1}
                - ${payOffMortgage2}
                - ${payOffOutstandingTaxes}
                - ${closingCostFinanced}`
            ;

            loanCalculation.totalCashOutToolTip = loanCalculation.totalCashOutToolTip
                + '<hr>' + loanCalculation.totalCashOutToolTipWithValues
                + '<hr>' + autoNumericConverter(loanCalculation.totalCashOut.toFixed(2))
            ;
            loanCalculation._totalCashOutToolTip.attr('data-content', loanCalculation.totalCashOutToolTip);

        }
        return loanCalculation.totalCashOut;
    }


    /* Initial Loan Amount */
    static calculateInitialLoanAmount() {
        console.log({
            func: 'calculateInitialLoanAmount',
        });

        let initialLoanAmount;
        let _initialLoanAmount = $('#acquisitionPriceFinanced');  //initial Loan Amount
        let _initialLoanAmountForTransactionType = $('#CORTotalLoanAmt');  //initial Loan Amount
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        let totalLoanAmount = getFieldsValue('totalLoanAmount1');
        let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
        let closingCostFinanced = getFieldsValue('closingCostFinanced');                 // int - Input Fields Value
        let prepaidInterestReserve = $('input[name="haveInterestreserve"]:checked').val() === 'Yes' ? 0 : getFieldsValue('prepaidInterestReserve');
        let loanamount = getTextValue('coTotalAmt');
        let costBasis = getFieldsValue('costBasis');
        let maxAmtToPutDown = getFieldsValue('maxAmtToPutDown');

        if (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
            let paymentBased = $('input[name="isLoanPaymentAmt"]:checked').val();
            let rehabCost = getFieldsValue('rehabCost');

            if (loanCalculation.isTransactionTypePurchaseCategory()) {
                let totalLoanAmount = getFieldsValue('totalLoanAmount1'); //Purchase Total Loan Amount;

                if (paymentBased === 'TLA') {
                    initialLoanAmount = totalLoanAmount - rehabCost;
                }
                if (paymentBased === 'ILA') {
                    initialLoanAmount = totalLoanAmount - rehabCost;
                }
                initialLoanAmount = initialLoanAmount ? autoNumericConverter(initialLoanAmount.toFixed(2)) : '';
                _initialLoanAmount.html(initialLoanAmount);
            }
            if (loanCalculation.isTransactionTypeRefinanceCategory()) {
                let totalLoanAmount = getFieldsValue('coTotalAmt'); //Purchase Total Loan Amount;

                if (paymentBased === 'TLA') {
                    initialLoanAmount = totalLoanAmount - rehabCost;
                }
                if (paymentBased === 'ILA') {
                    initialLoanAmount = totalLoanAmount - rehabCost;
                }
                initialLoanAmount = initialLoanAmount ? autoNumericConverter(initialLoanAmount.toFixed(2)) : '';
                _initialLoanAmountForTransactionType.val(initialLoanAmount);
            }
        } else {
            if ((typeOfHMLOLoanRequesting === TransactionType.PURCHASE ||
                typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_PURCHASE
            ) && (totalLoanAmount !== loanamount)) {

                initialLoanAmount = totalLoanAmount - (rehabCostFinanced + closingCostFinanced + prepaidInterestReserve);

                let aqDownPay = costBasis - initialLoanAmount;
                let aqDownPayPercentage = aqDownPay * 100.0 / costBasis;

                if (initialLoanAmount && initialLoanAmount < costBasis) {
                    _initialLoanAmount.html(autoNumericConverter(initialLoanAmount.toFixed(2)));
                    if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
                        $('#downPaymentPercentage').val(autoNumericConverter(aqDownPayPercentage));
                        $('#maxAmtToPutDown').val(autoNumericConverter(aqDownPay));
                    }
                    return true;
                } else {
                    loanCalculation.checkLoanTermViolation();
                }
            } else {
                initialLoanAmount = costBasis - maxAmtToPutDown;
            }
            initialLoanAmount = initialLoanAmount ? autoNumericConverter(initialLoanAmount.toFixed(2)) : '';
            _initialLoanAmount.html(initialLoanAmount);
        }

        console.log({
            typeOfHMLOLoanRequesting: typeOfHMLOLoanRequesting,
            totalLoanAmount: totalLoanAmount,
            rehabCostFinanced: rehabCostFinanced,
            closingCostFinanced: closingCostFinanced,
            prepaidInterestReserve: prepaidInterestReserve,
            loanamount: loanamount,
            costBasis: costBasis,
            maxAmtToPutDown: maxAmtToPutDown,
            initialLoanAmount: initialLoanAmount,
        });
    }

    /**
     * Check loan term violation
     * @returns {boolean}
     */
    static checkLoanTermViolation() {
        console.log({func: 'checkLoanTermViolation'});

        // Check if file type includes "loc"
        if ($('#fileTypesTxt').val()?.toString().split(',').includes("loc")) return true;

        const typeOfLoan = $('#typeOfHMLOLoanRequesting').val();
        const initialLoanAmount = getTextValue('acquisitionPriceFinanced');  // Initial Loan Amount
        const maxAmtToPutDown = getFieldsValue('maxAmtToPutDown');           // Acquisition Down Payment
        const costBasis = getFieldsValue('costBasis');                       // Acquisition / Purchase Price

        const isPurchaseType = [TransactionType.PURCHASE, TransactionType.COMMERCIAL_PURCHASE].includes(typeOfLoan);
        const isAutoCalcTotalLoanAmountNotBasedOnLTC2 = !loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2();

        if (isPurchaseType
            && parseFloat(maxAmtToPutDown) > parseFloat(costBasis)
            && isAutoCalcTotalLoanAmountNotBasedOnLTC2) {
            return toastrNotification('Acquisition Down Payment should be less than Acquisition / Purchase Price', 'error'), false;
        }

        if (isPurchaseType
            && parseFloat(costBasis) < parseFloat(initialLoanAmount)
            && isAutoCalcTotalLoanAmountNotBasedOnLTC2) {
            if ($('#isFilelocked').val() !== 'No') {
                return toastrNotification('Acquisition / Purchase Price should be greater than Initial Loan Amount', 'error'), false;
            }
        }
        return true;
    }

    static calculateNetLenderFundsToTitle() {
        /* Net Lender Funds To Borrower */
        let _netLenderFundsToBorrower = $('#netLenderFundsToBorrower');
        let netLenderFundsToBorrower = 0;
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        let totalFeesAndCost = getTextValue('totalFeesAndCost');
        let thirdPartyFees = getFieldsValue('thirdPartyFees');
        let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
        let prepaidInterestReserve = getFieldsValue('prepaidInterestReserve');
        let totalLoanAmount = null;

        /*  if ($('input[name="haveInterestreserve"]:checked').val() !== 'Yes') {
              prepaidInterestReserve = 0;
          }*/
        /*  if (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {

          } else {
              if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {
                  totalLoanAmount = getFieldsValue('LOCTotalLoanAmt');
              } else if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE
                  || typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE
                  || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE
                  || typeOfHMLOLoanRequesting === TransactionType.REFINANCE
              ) {
                  totalLoanAmount = getTextValue('coTotalAmt');            // txt - Inner text Value
              } else {
                  totalLoanAmount = getFieldsValue('totalLoanAmount1');
              }
          } */
        totalLoanAmount = loanCalculation.isTransactionTypePurchaseCategory() ?
            getFieldsValue('totalLoanAmount1') :
            (loanCalculation.isTransactionTypeRefinanceCategory() ? getFieldsValue('coTotalAmt') : 0);

        let totalDailyInterestCharge = getTextValue('totalDailyInterestCharge');
        let closingCostFinanced = getFieldsValue('closingCostFinanced');                 // int - Input Fields Value
        let initialAdvance = getFieldsValue('initialAdvance');   // Initial Draw Amount
        let escrowFees = getFieldsValue('escrowFees'); //Estimated Settlement or Closing/Escrow Fee
        let payOffMortgage1 = getFieldsValue('payOffMortgage1'); //Pay-Off on Mortgage 1
        let payOffMortgage2 = getFieldsValue('payOffMortgage2'); //Pay-Off on Mortgage 2
        let payOffOutstandingTaxes = getFieldsValue('payOffOutstandingTaxes'); //Pay Off Outstanding Taxes


        let netLenderFundsTooltip;
        if (loanCalculation.isTransactionTypePurchaseCategory()) {
            netLenderFundsToBorrower = parseFloat(totalLoanAmount)
                - parseFloat(totalFeesAndCost)
                - parseFloat(rehabCostFinanced)
                - parseFloat(prepaidInterestReserve)
                + initialAdvance
                + (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2() ? escrowFees - loanCalculation.LTC2_additionalReserveInterest - loanCalculation.LTC2_additionalOriginationInterest : 0)
            ;
            netLenderFundsTooltip =
                'Net Lender Funds to Title = Total Loan Amount - Total Fees & Costs - Rehab Cost Financed - Prepaid Interest Reserve + Initial Draw Amount' +
                (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2() ? ' + Settlement or Closing/Escrow Fee - Additional Reserve Interest - Additional Origination Interest' : '') +
                '<hr>' +
                `Net Lender Funds to Borrower = (${totalLoanAmount} 
                    - ${totalFeesAndCost} 
                    - ${rehabCostFinanced} 
                    - ${prepaidInterestReserve} 
                    + ${initialAdvance}` +
                (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2() ?
                    ` + ${escrowFees} - ${loanCalculation.LTC2_additionalReserveInterest} - ${loanCalculation.LTC2_additionalOriginationInterest}` : '') +
                `) ` + `<hr>` + ` ${netLenderFundsToBorrower}`;


        } else if (loanCalculation.isTransactionTypeRefinanceCategory()) {
            netLenderFundsToBorrower = parseFloat(totalLoanAmount)
                - parseFloat(totalFeesAndCost)
                - parseFloat(rehabCostFinanced)
                - parseFloat(prepaidInterestReserve)
                + initialAdvance
                + (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2() ? escrowFees : 0)
            ;

            netLenderFundsTooltip =
                'Net Lender Funds to Title = Total Loan Amount - Total Fees & Costs - Rehab Cost Financed - Prepaid Interest Reserve + Initial Draw Amount' +
                (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2() ? ' + Settlement or Closing/Escrow Fee' : '') +
                '<hr>' +
                `(${totalLoanAmount} - ${totalFeesAndCost} - ${rehabCostFinanced} - ${prepaidInterestReserve} + ${initialAdvance}` +
                (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2() ? ` + ${escrowFees} ` : '') +
                `) ` + `<hr>` + ` ${netLenderFundsToBorrower}`;

        }

        netLenderFundsToBorrower = netLenderFundsToBorrower < 0 ? 0 : autoNumericConverter(netLenderFundsToBorrower.toFixed(2));

        if (_netLenderFundsToBorrower.length > 0) {
            _netLenderFundsToBorrower.html(convertInputToAbsoluteValueWithDollar(netLenderFundsToBorrower));
        }
        $('label[for="netLenderFundsToTitle"] .fa-info-circle').attr(
            'data-original-title',
            netLenderFundsTooltip
        );
    }

    static calculateContingencyReserve() {
        console.log({
            func: 'calculateContingencyReserve',
        });

        this.rehabCost = getFieldsValue('rehabCost');
        this.rehabCostFinanced = getFieldsValue('rehabCostFinanced');
        this.contingencyReserve = getFieldsValue('contingencyReserve');
        this._contingencyReserveAmt = $('#contingencyReserveAmt');
        this.isFileSPREO = this.getEncryptedPCID() === this.PCID_SPREO_CAPITAL;
        this._contingencyReserveAmtTooltip = $('#contingencyReserveAmtTooltip');
        this.contingencyReserveAmt = 0;

        if (this.isFileSPREO) {
            this.contingencyReserveAmt = 0.1 * this.rehabCostFinanced;
            this.contingencyReserveAmtTooltip = "(.1 * Rehab/Construction Cost Financed)";
            this.contingencyReserveAmtTooltipWithValues = `(0.1 * ${this.rehabCostFinanced})`;
        } else {
            this.contingencyReserveAmtTooltip = "(Rehab/Construction Cost * Contingency Reserve / 100)";
            if (this.contingencyReserve > 0) {
                this.contingencyReserveAmt = parseFloat(this.contingencyReserve / 100);
                this.contingencyReserveAmt = parseFloat(this.rehabCost) * parseFloat(this.contingencyReserveAmt);
               // this.contingencyReserveAmt = autoNumericConverter(this.contingencyReserveAmt.toFixed(2));
            }
            this.contingencyReserveAmtTooltipWithValues = `( ${this.rehabCost} * ${this.contingencyReserve} / 100) `;
        }
        this.contingencyReserveAmt = autoNumericConverter(this.contingencyReserveAmt ? this.contingencyReserveAmt.toFixed(2) : 0)
        this.contingencyReserveAmtTooltip = this.contingencyReserveAmtTooltip
            + '<hr>' + this.contingencyReserveAmtTooltipWithValues
            + '<hr>' + this.contingencyReserveAmt;

        this._contingencyReserveAmtTooltip.attr('data-content', this.contingencyReserveAmtTooltip);

        if (this._contingencyReserveAmt.length) {
            this._contingencyReserveAmt.html(this.contingencyReserveAmt);
        }
    }
    static formatNumber(val,decimal) {
        val = parseFloat(val).toFixed(decimal); // Convert to float with max n decimals
        val = val.replace(/\.?0+$/, '');   // Remove trailing zeros and optional trailing dot

        // Ensure at least 2 decimal places
        if (!val.includes('.')) {
            return val + '.00';
        }

        let parts = val.split('.');
        if (parts[1].length === 1) {
            return val + '0';
        }
        return val;
    }

    static setDownPaymentHtml(downPayment) {
        console.log({
            func: 'setDownPaymentHtml',
        });
        let _downPayment = $('#downPayment');

        let _downPaymentHtmlTooltip  = $('#downPaymentHtmlTooltip');
        let downPaymentHtmlTooltip;

        if(_downPayment.length > 0) {
            downPayment = replaceCommaValues(downPayment);
            downPayment = autoNumericConverter(downPayment.toFixed(2));
            _downPayment.html(downPayment);

            downPaymentHtmlTooltip = `${downPayment}`;
            _downPaymentHtmlTooltip.attr('data-content', downPaymentHtmlTooltip)
        }
    }
}

$(function () {
    loanCalculation.init();
});

function setDailyInterestCharge() {
    console.log({
        func: 'setDailyInterestCharge',
    });

    /* Daily Interest Charge */
    let lien1Rate = getFieldsValue('lien1Rate');
    if (!lien1Rate) return;
    let lien1Terms = $('#lien1Terms').val();
    if (!lien1Terms) {
        lien1Terms = 'Interest Only';
    } //refer HMLOLoanInfoVars.php line #2293

    let loanPaymentBased = $('input[name=isLoanPaymentAmt]:checked').val();
    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
    let amtBasedOnPayment = 0;
    let totalDailyInterestCharge = 0;
    let totalLoanAmount;

    if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {
        totalLoanAmount = getFieldsValue('LOCTotalLoanAmt');
    } else if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
        typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.REFINANCE) {
        totalLoanAmount = getFieldsValue('coTotalAmt');            // txt - Inner text Value
    } else {
        totalLoanAmount = getFieldsValue('totalLoanAmount1');
    }

    let accrualTypeBaseValues = loanCalculation.getaccrualTypeBaseValues();
    let _Z = accrualTypeBaseValues._Z;
    let toolTipTxt;

    if (lien1Terms === 'Interest Only') {
        if (loanPaymentBased === 'ILA') {
            totalLoanAmount = getFieldsValue('currentLoanBalance');
            totalDailyInterestCharge = ((lien1Rate / 100) / _Z) * totalLoanAmount;
            toolTipTxt = "Per Diem Interest = (((Int. Rate/100)/" + _Z + ")* Current Loan Balance ) = " + totalDailyInterestCharge.toFixed(2);
        } else if (loanPaymentBased === 'TLA') {
            totalLoanAmount = getFieldsValue('totalLoanAmount1');
            totalDailyInterestCharge = ((lien1Rate / 100) / _Z) * totalLoanAmount;
            toolTipTxt = "Per Diem Interest = (((Int. Rate/100)/" + _Z + ")* Total Loan Amount ) = " + totalDailyInterestCharge.toFixed(2);
        }
    } else {
        if ($('#isLoanPaymentAmtILA').is(':checked')) {
            amtBasedOnPayment = getFieldsValue('currentLoanBalance');
        }
        if ($('#isLoanPaymentAmtTLA').is(':checked')) {
            amtBasedOnPayment = totalLoanAmount;
        }
        totalDailyInterestCharge = amtBasedOnPayment * (((lien1Rate / 100) / 12) / 30);
        toolTipTxt = "Per Diem Interest = (Total Loan Amount * (((Int. Rate/100)/12)/30)) = " + totalDailyInterestCharge;
    }

    console.log({
        func: 'setDailyInterestCharge',
        lien1Rate: lien1Rate,
        lien1Terms: lien1Terms,
        loanPaymentBased: loanPaymentBased,
        accrualTypeBaseValues: accrualTypeBaseValues,
        _Z: _Z,
        typeOfHMLOLoanRequesting: typeOfHMLOLoanRequesting,
        totalLoanAmount: totalLoanAmount,
        amtBasedOnPayment: amtBasedOnPayment,
        totalDailyInterestCharge: totalDailyInterestCharge,
        toolTipTxt: toolTipTxt
    });

    $('#totalDailyInterestCharge').html(autoNumericConverter(totalDailyInterestCharge));
    $('#totalDailyInterestChargeDummy').html(autoNumericConverter(totalDailyInterestCharge.toFixed(2)));

    if (toolTipTxt !== '') {
        $('#perDiemToolTip').tooltip('hide').attr('data-original-title', toolTipTxt);
    }


}

function getHMLOLoanInfoTotalMonthlyPayment() {
    console.log({
        func: 'getHMLOLoanInfoTotalMonthlyPayment',
    });
    loanCalculation.initLTC2();

    let totalLoanAmount = 0;
    let interestRate = 0;
    let term = 12;
    let loanPaymentBased = $('input[name=isLoanPaymentAmt]:checked').val();
    let initialLoanAmount = 0;

    let accrualTypeBaseValues = loanCalculation.getaccrualTypeBaseValues();
    let _X = accrualTypeBaseValues._X;
    let _Z = accrualTypeBaseValues._Z;
    let amt = null;
    let totalMonthlyPaymentTooltip = '';

    let _lien1Payment = $('#lien1Payment');
    let _lien1Terms = $('#lien1Terms');

    if (loanPaymentBased === 'SMP') {
        $('#lien1Terms option:selected').removeAttr('selected');
        _lien1Payment.prop('readonly', false);
        _lien1Terms.val('');
        _lien1Terms.attr('disabled', 'disabled');
    } else {
        _lien1Payment.prop('readonly', true);
        _lien1Terms.attr('disabled', false);
    }
    let lien1Terms = _lien1Terms.val();
    if (!lien1Terms) {
        return;
    }
    interestRate = getFieldsValue('lien1Rate');

    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();

    if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
        typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.REFINANCE) {
        initialLoanAmount = getFieldsValue('CORTotalLoanAmt');
    } else if (typeOfHMLOLoanRequesting === TransactionType.TRANSACTIONAL) {
        initialLoanAmount = getTextValue('acquisitionPriceFinanced');
    } else {
        initialLoanAmount = getTextValue('currentLoanBalance');
    }

    if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {
        totalLoanAmount = getFieldsValue('LOCTotalLoanAmt');
    } else if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
        typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.REFINANCE) {
        totalLoanAmount = $('.totalLoanAmount').html();
        totalLoanAmount = replaceCommaValues(totalLoanAmount);
    } else {
        totalLoanAmount = getFieldsValue('totalLoanAmount1');
    }

    if (loanPaymentBased === 'ILA') {
        totalLoanAmount = getTextValue('currentLoanBalance');
    }
    if (typeOfHMLOLoanRequesting === TransactionType.TRANSACTIONAL) {
        totalLoanAmount = initialLoanAmount;
    }

    if (lien1Terms === 'Interest Only') {
        let _monthlyPaymentPart1 = (((interestRate / 100) / _Z) * _X);
        if (loanPaymentBased === 'ILA' || loanPaymentBased === 'TLA') {
            totalLoanAmount = parseFloat(totalLoanAmount);
            amt = _monthlyPaymentPart1 * totalLoanAmount;
            totalMonthlyPaymentTooltip = `${totalLoanAmount} * (((${interestRate} / 100) / ${_Z}) * ${_X})`;
        }
    } else {
        let pos = lien1Terms.indexOf(" ");
        lien1Terms = lien1Terms.substring(0, pos);
        lien1Terms = parseInt(lien1Terms);

        if (lien1Terms) {
            if (lien1Terms > 0) {
                term = lien1Terms * 12;
            }
            if ((totalLoanAmount > 0) && (interestRate > 0)) {
                amt = calculateAmortizationValue(totalLoanAmount, interestRate, term);
                totalMonthlyPaymentTooltip = `${totalLoanAmount} * (${interestRate} / 1200) / (1 - Math.pow(1 / (1 + (${interestRate} / 1200)), ${term}))`;
            } else {
                amt = null;
            }
        } else {
            amt = null;
        }
    }
    amt = amt ? amt.toFixed(2) : 0;
    amt = autoNumericConverter(amt);
    let _totalMonthlyPaymentTooltip = $('#totalMonthlyPaymentTooltip');
    totalMonthlyPaymentTooltip = totalMonthlyPaymentTooltip ? (_totalMonthlyPaymentTooltip.data('formula') + '<hr>' + totalMonthlyPaymentTooltip + ` = $ ${amt}`) : _totalMonthlyPaymentTooltip.data('formula');
    _totalMonthlyPaymentTooltip.attr('data-original-title', totalMonthlyPaymentTooltip);

    if (loanPaymentBased !== 'SMP') {
        _lien1Payment.val(amt);
    }

}

/**

 ** Description : Required Reserves Section Calculation Methods
 ** Developer    : Venkatesh
 ** Date         : Oct 12, 2017

 **/

class paymentReserves {
    static lien1Payment = null;
    static paymentReserves = null;
    static paymentReservesAmt = null;
    static pcid = null;
    static includeTaxesInsuranceHOA = null;
    static netMonthlyPayment = null;
    static lessPrePayInterestReserve = 0;
    static totalInterestPaymentReserveRequired = 0;

    static init() {
        this.lien1Payment = getFieldsValue('lien1Payment');
        this.paymentReserves = getFieldsValue('paymentReserves');
        this.pcid = getFieldsValue('FPCID');
        this.includeTaxesInsuranceHOA = $('#includeTaxesInsuranceHOA:checked').val() ?? 0;
        this.netMonthlyPayment = getTextValue('netMonthlyPayment');

        this.lessPrePayInterestReserve = getTextValue('lessPrePayInterestReserve');
    }

    static calculate() {
        console.log({
            func: 'calculate',
        });
        this.paymentReservesAmt = 0;

        if (this.pcid === '4975') { //Exclude (Taxes, Insurance & HOA) for CV3
            if (this.paymentReserves > 0) {
                this.paymentReservesAmt = this.lien1Payment * this.paymentReserves;
            }
        } else {
            if (this.includeTaxesInsuranceHOA && this.paymentReserves > 0) { //PITIA
                this.paymentReservesAmt = (this.netMonthlyPayment * this.paymentReserves);
            } else {
                this.paymentReservesAmt = (this.lien1Payment * this.paymentReserves);
            }
        }
        try {
            $('#paymentReservesAmt').html(autoNumericConverter(this.paymentReservesAmt.toFixed(2)));
        } catch (e) {
        }
    }

    static debug() {
        console.log({
            lien1Payment: this.lien1Payment,
            netMonthlyPayment: this.netMonthlyPayment,
            paymentReserves: this.paymentReserves,
            paymentReservesAmt: this.paymentReservesAmt,
            includeTaxesInsuranceHOA: this.includeTaxesInsuranceHOA,
        })
    }


    static setLessPrePaidInterestReserve() {
        let isCheckedVal = getFieldsValue('haveInterestreserveYes', null, true) === 'Yes';
        if (isCheckedVal) {
            this.lessPrePayInterestReserve = getFieldsValue('prepaidInterestReserve');
        } else {
            this.lessPrePayInterestReserve = 0;
        }
        //update the tooltip
        let _lessPrePayInterestReserveToolTip = $('#lessPrePayInterestReserveToolTip');
        let lessPrePayInterestReserveToolTip = _lessPrePayInterestReserveToolTip.data('formula') + '<hr>' + `${this.lessPrePayInterestReserve}`;
        _lessPrePayInterestReserveToolTip.attr('data-original-title', lessPrePayInterestReserveToolTip);
        $('#lessPrePayInterestReserve').html(autoNumericConverter(this.lessPrePayInterestReserve.toFixed(2)));
    }

    static calculateTotalInterestPaymentReserveRequired() {
        let isCheckedVal = getFieldsValue('haveInterestreserveYes', null, true) === 'Yes';
        let paymentReservesAmt = this.paymentReservesAmt ? replaceCommaValues(this.paymentReservesAmt) : 0;

        let _totalInterestPaymentReserveRequiredToolTip = $('#totalInterestPaymentReserveRequiredToolTip');
        let totalInterestPaymentReserveRequiredToolTip = '';
        if (isCheckedVal && paymentReservesAmt > 0) {
            this.totalInterestPaymentReserveRequired = paymentReservesAmt - this.lessPrePayInterestReserve
            < 0 ? 0 : paymentReservesAmt - this.lessPrePayInterestReserve; //non-negative value

            totalInterestPaymentReserveRequiredToolTip = _totalInterestPaymentReserveRequiredToolTip.data('formula') + '<hr>';
            if(paymentReservesAmt - this.lessPrePayInterestReserve < 0) {
                totalInterestPaymentReserveRequiredToolTip += 0;
            } else {
                totalInterestPaymentReserveRequiredToolTip +=  `${paymentReservesAmt} - ${this.lessPrePayInterestReserve} =` + this.totalInterestPaymentReserveRequired.toFixed(2);
            }
        } else {
            this.totalInterestPaymentReserveRequired = paymentReservesAmt;
            totalInterestPaymentReserveRequiredToolTip = _totalInterestPaymentReserveRequiredToolTip.data('formula') + '<hr>'
                + `${paymentReservesAmt} - ${this.lessPrePayInterestReserve} =` + this.totalInterestPaymentReserveRequired.toFixed(2);
        }
        //update the tooltip
        _totalInterestPaymentReserveRequiredToolTip.attr('data-original-title', totalInterestPaymentReserveRequiredToolTip);
        $('#totalInterestPaymentReserveRequired').html(autoNumericConverter(this.totalInterestPaymentReserveRequired.toFixed(2)));
    }
}

function calculatePaymentReserves() {
    console.log({
        func: 'calculatePaymentReserves',
    });

    paymentReserves.init();
    paymentReserves.calculate();
    paymentReserves.setLessPrePaidInterestReserve();
    paymentReserves.calculateTotalInterestPaymentReserveRequired();
    paymentReserves.debug();
}

function calculateHMLONetMonthlyPayment() {
    console.log({
        func: 'calculateHMLONetMonthlyPayment',
    });

    // Initialize variables
    let netMonthlyPayment = 0;
    let payment = parseFloat(getFieldsValue('lien1Payment')) || 0;
    let taxes1 = parseFloat(getFieldsValue('taxes1')) || 0;
    let annualPremium = parseFloat(getFieldsValue('annualPremium')) || 0;
    let spcfHoafees = parseFloat(getFieldsValue('spcf_hoafees')) || 0;

    // Calculate net monthly payment
    netMonthlyPayment = payment + (taxes1 / 12) + (annualPremium / 12) + (spcfHoafees / 12);
    netMonthlyPayment = autoNumericConverter(netMonthlyPayment.toFixed(2));

    // Tooltip calculations
    let _netMonthlyPaymentTooltip = $('#netMonthlyPaymentTooltip');
    let netMonthlyPaymentTooltip = `${payment} + ( ${taxes1} + ${annualPremium} + ${spcfHoafees} ) / 12 = $ ${netMonthlyPayment}`;
    netMonthlyPaymentTooltip = (_netMonthlyPaymentTooltip.data('formula') || '') + '<hr>' + netMonthlyPaymentTooltip;

    // Update tooltip content
    _netMonthlyPaymentTooltip.attr('data-original-title', netMonthlyPaymentTooltip);

    // Display net monthly payment
    try {
        $('#netMonthlyPayment').html(netMonthlyPayment);
    } catch (e) {
        console.error('Error updating #netMonthlyPayment: ', e);
    }
    // Recalculate the Required Reserves based on the new Net Monthly Payment
    calculatePaymentReserves();

    // Log detailed calculation values for debugging
    console.log({
        Payment: payment,
        taxes1: taxes1,
        annualPremium: annualPremium,
        spcfHoafees: spcfHoafees,
    });
}

$(document).on('change', '#spcf_hoafees', function () {
    // calculateHMLONetMonthlyPayment();
});

class totalRequiredReserves {
    static paymentReservesAmt = null;
    static requiredConstructionAmt = null;
    static contingencyReserveAmt = null;
    static totalRequiredReserves = null;
    static percentageTotalLoanAmount = null;
    static totalInterestPaymentReserveRequired = null;
    static rehabCost = null;
    static rehabCostFinanced = null;

    static init() {
        this.totalInterestPaymentReserveRequired = getTextValue('totalInterestPaymentReserveRequired');
        this.paymentReservesAmt = getTextValue('paymentReservesAmt');
        this.requiredConstructionAmt = getTextValue('requiredConstructionAmt');
        this.contingencyReserveAmt = getTextValue('contingencyReserveAmt');
        this.percentageTotalLoanAmount = getTextValue('percentageTotalLoanAmount');
        this.rehabCost = getFieldsValue('rehabCost');
        this.rehabCostFinanced = getFieldsValue('rehabCostFinanced');
        loanCalculation.isFileSPREO = loanCalculation.getEncryptedPCID() === loanCalculation.PCID_SPREO_CAPITAL;
    }

    static calculate() {
        console.log({
            func: 'calculate',
        });

        const _totalRequiredReservesTooltip = $('#totalRequiredReservesTooltip');
        const _totalRequiredReserves = $('#totalRequiredReserves');
        let totalRequiredReservesTooltip;
        let totalRequiredReservesTooltipWithValues;
        let totalRequiredReserves;

        if (loanCalculation.isFileSPREO) {
            this.totalRequiredReserves =
                (this.rehabCost - this.rehabCostFinanced)
                + (0.1 * this.rehabCostFinanced);

            totalRequiredReserves = autoNumericConverter((this.totalRequiredReserves ?? 0).toFixed(2));

            totalRequiredReservesTooltip = "(Rehab/Construction Cost - Rehab/Construction Cost Financed) + (.1 * Rehab/Construction Cost Financed)";

            totalRequiredReservesTooltipWithValues =
                `(${this.rehabCost} - ${this.rehabCostFinanced})
                + (0.1 * ${this.rehabCostFinanced}) `
                + `<hr>` + totalRequiredReserves
            ;

        } else {
            this.totalRequiredReserves =
                (this.totalInterestPaymentReserveRequired ?? 0) +
                (this.requiredConstructionAmt ?? 0) +
                (this.contingencyReserveAmt ?? 0) +
                (this.percentageTotalLoanAmount ?? 0);

            totalRequiredReserves = autoNumericConverter((this.totalRequiredReserves ?? 0).toFixed(2));

            totalRequiredReservesTooltip = 'Total Required Reserves = Total Interest Payment Reserve Required \n' +
                '        + Required Construction /Rehab Budget Not Financed \n' +
                '        + % Contingency Reserve \n' +
                '        + % of Total Loan Amount';

            totalRequiredReservesTooltipWithValues =
                `${this.totalInterestPaymentReserveRequired ?? 0} + 
        ${this.requiredConstructionAmt ?? 0} + 
        ${this.contingencyReserveAmt ?? 0} + 
        ${this.percentageTotalLoanAmount ?? 0} `
                + `<hr>`
                + `${totalRequiredReserves}`;

        }
        totalRequiredReservesTooltip = totalRequiredReservesTooltip
            + "<hr>" + totalRequiredReservesTooltipWithValues;

        // Set tooltip with correct title
        _totalRequiredReservesTooltip.attr('data-content', totalRequiredReservesTooltip);

        // Update the total required reserves in the UI if the element exists
        if (_totalRequiredReserves.length) {
            _totalRequiredReserves.html(totalRequiredReserves);
        }
    }

    static debug() {
        console.log({
            paymentReservesAmt: this.paymentReservesAmt,
            requiredConstructionAmt: this.requiredConstructionAmt,
            contingencyReserveAmt: this.contingencyReserveAmt,
            totalRequiredReserves: this.totalRequiredReserves,
        });
    }

    static calculatePercentageTotalLoanAmount() {
        console.log({
            func: 'calculatePercentageTotalLoanAmount',
        });
        let percentageTotalLoan = 0;
        let percentageTotalLoanAmount = 0;
        let totalLoanAmount = 0;

        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
        percentageTotalLoan = getFieldsValue('percentageTotalLoan');

        if (percentageTotalLoan > 0) {
            if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {
                totalLoanAmount = getFieldsValue('LOCTotalLoanAmt');

            } else if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
                typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
                typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
                typeOfHMLOLoanRequesting === TransactionType.REFINANCE) {
                totalLoanAmount = getTextValue('coTotalAmt');            // txt - Inner text Value

            } else {
                totalLoanAmount = getFieldsValue('totalLoanAmount1');
            }
            percentageTotalLoanAmount = (percentageTotalLoan * totalLoanAmount) / 100
        }
        $('#percentageTotalLoanAmount').html(autoNumericConverter(percentageTotalLoanAmount.toFixed(2)));
    }
}

function calculateTotalRequiredReserves() {
    console.log({
        func: 'calculateTotalRequiredReserves',
    });
    totalRequiredReserves.calculatePercentageTotalLoanAmount();
    totalRequiredReserves.init();
    totalRequiredReserves.calculate();
    totalRequiredReserves.debug();
}


function calculateTotalProjectCost() {
    console.log({
        func: 'calculateTotalProjectCost',
    });

    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
    let rehabCost = getFieldsValue('rehabCost');
    let assessedValue = getFieldsValue('homeValue'); //property value AS-IS
    let costBasis = getFieldsValue('costBasis'); //Purchase Price
    let closingCostFinanced = getFieldsValue('closingCostFinanced');
    let haveInterestreserve = $('input[name="haveInterestreserve"]:checked').val();
    let costSpent = getFieldsValue('costSpent');
    let addToTotalProjectValue = $('input[name="addToTotalProjectValue"]:checked').val();
    let prepaidInterestReserve = 0;
    if (haveInterestreserve === 'Yes' && addToTotalProjectValue === 'Yes') {
        prepaidInterestReserve = getFieldsValue('prepaidInterestReserve');
    }
    console.log({
        typeOfHMLOLoanRequesting: typeOfHMLOLoanRequesting,
        rehabCost: rehabCost,
        assessedValue: assessedValue,
        costBasis: costBasis,
        closingCostFinanced: closingCostFinanced,
        prepaidInterestReserve: prepaidInterestReserve,
    });

    let totalProjectCost;
    let TPCFormula;
    let LTCFormula;
    let totalLoanAmount;
    let TPCToolTipWithValues = '';

    if (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
        loanCalculation.initLTC2();
    } else {

        if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND
            || typeOfHMLOLoanRequesting === TransactionType.REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE
        ) {
            totalProjectCost = parseFloat(assessedValue) + parseFloat(rehabCost) + parseFloat(costSpent) + parseFloat(prepaidInterestReserve);
            TPCToolTipWithValues = ` ${assessedValue} + ${rehabCost} + ${costSpent} + ${prepaidInterestReserve} `;

            TPCFormula = "Total Project Value = (Property Value(As-Is) + Rehab/Construction Cost + Cost Spent + Pre-paid Interest Reserve* <br><b>*Pre-paid interest reserve only included, if Financing Pre-paid interest reserve= Yes and Add to Total Project Cost= Yes</b> )" + "<hr>" + TPCToolTipWithValues;
            LTCFormula = "Loan-to-Cost (LTC) =  (Total Loan Amount / Project Value) * 100<hr>Loan-to-Cost Ratio (LTC) A ratio used in commercial real estate construction to compare the amount of the loan used to finance a project to the cost to build the project. If the project costs $1 million to complete and the borrower borrows $700,000, the loan-to-cost (LTC) ratio would be 70%.";

        } else if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {

            totalProjectCost = parseFloat(rehabCost) + parseFloat(closingCostFinanced) + parseFloat(costSpent) + parseFloat(prepaidInterestReserve);
            TPCToolTipWithValues = ` ${rehabCost} + ${closingCostFinanced} + ${costSpent} + ${prepaidInterestReserve} `;

            TPCFormula = "Total Project Cost = (Rehab/Construction Cost + Closing Cost Financed + Cost Spent + Pre-paid Interest Reserve* <br><b>*Pre-paid interest reserve only included, if Financing Pre-paid interest reserve= Yes and Add to Total Project Cost= Yes</b>) " + "<hr>" + TPCToolTipWithValues;
            LTCFormula = "Loan-to-Cost (LTC) =  (Total Loan Amount / Project Cost) * 100<hr>Loan-to-Cost Ratio (LTC) A ratio used in commercial real estate construction to compare the amount of the loan used to finance a project to the cost to build the project. If the project costs $1 million to complete and the borrower borrows $700,000, the loan-to-cost (LTC) ratio would be 70%.";
        } else {

            totalProjectCost = parseFloat(costBasis) + parseFloat(rehabCost) + parseFloat(costSpent) + parseFloat(prepaidInterestReserve);
            TPCToolTipWithValues = ` ${costBasis} + ${rehabCost} + ${costSpent} + ${prepaidInterestReserve} `;

            TPCFormula = "Total Project Cost = (Acquisition / Purchase Price + Rehab/Construction Cost + Cost Spent + Pre-paid Interest Reserve* <br><b>*Pre-paid interest reserve only included, if Financing Pre-paid interest reserve= Yes and Add to Total Project Cost= Yes</b>) " + "<hr>" + TPCToolTipWithValues;
            LTCFormula = "Loan-to-Cost (LTC) =  (Total Loan Amount / Project Cost) * 100<hr>Loan-to-Cost Ratio (LTC) A ratio used in commercial real estate construction to compare the amount of the loan used to finance a project to the cost to build the project. If the project costs $1 million to complete and the borrower borrows $700,000, the loan-to-cost (LTC) ratio would be 70%.";
        }

        if (typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT) {
            totalLoanAmount = getFieldsValue('LOCTotalLoanAmt');
        } else if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE
        ) {
            totalLoanAmount = getTextValue('coTotalAmt');            // txt - Inner text Value
        } else {
            totalLoanAmount = getFieldsValue('totalLoanAmount1');
        }
        /* Loan To Cost */
        let LTC = null;
        if (totalProjectCost > 0) {
            LTC = (parseFloat(totalLoanAmount) / parseFloat(totalProjectCost)) * 100;
            LTC = LTC.toFixed(2);
        }
        totalProjectCost = autoNumericConverter(totalProjectCost.toFixed(2));

        TPCFormula = TPCFormula + ` = $ ${totalProjectCost} `;
        $('#TPCToolTip').attr('data-original-title', TPCFormula);
        $('#totalProjectCost').html(totalProjectCost);

        let _LTCToolTip = $('#LTCToolTip');
        LTCFormula = LTCFormula + "<hr>" + ` ${totalLoanAmount} / ${totalProjectCost} * 100 = ${LTC} %`;
        _LTCToolTip.attr('data-original-title', LTCFormula);
        $('#Loan-to-Cost').html(LTC);
    }
}

/* Simple ARV Calculation */
function calculateSimpleARVPercentage() {
    console.log({
        func: 'calculateSimpleARVPercentage',
    });

    let totalLoanAmount = getFieldsValue('totalLoanAmount1');
    let originationPointsValue = getFieldsValue('originationPointsValue');
    let brokerPointsValue = getFieldsValue('brokerPointsValue');
    let prepaidInterestReserve = getFieldsValue('prepaidInterestReserve');
    let isCheckedVal = getFieldsValue('haveInterestreserveYes', null, true) === 'Yes';
    if (!isCheckedVal) {
        prepaidInterestReserve = 0;
    }
    let assessedValue = getFieldsValue('assessedValue');
    let _simpleARVTooltip = $('#simpleARVTooltip');
    let simpleARVTooltip = null;

    let simpleARVP = 0;
    let simpleARV = 0;
    if (assessedValue > 0) {
        simpleARVP = totalLoanAmount - (originationPointsValue + brokerPointsValue + prepaidInterestReserve);
        simpleARV = (simpleARVP / assessedValue) * 100;
    }
    simpleARV = autoNumericConverter(simpleARV.toFixed(2));

    simpleARVTooltip = `( ( ${totalLoanAmount} - ( ${originationPointsValue} + ${brokerPointsValue} + ${prepaidInterestReserve}) )  / ${assessedValue} ) * 100 = ${simpleARV} %`;

    simpleARVTooltip = _simpleARVTooltip.data('formula') + '<hr>' + simpleARVTooltip;
    _simpleARVTooltip.attr('data-original-title', simpleARVTooltip);

    console.log({
        totalLoanAmount: totalLoanAmount,
        originationPointsValue: originationPointsValue,
        brokerPointsValue: brokerPointsValue,
        prepaidInterestReserve: prepaidInterestReserve,
        isCheckedVal: isCheckedVal,
        assessedValue: assessedValue,
        simpleARVP: simpleARVP,
        simpleARV: simpleARV,
    });

    let _simpleARV = $('#simpleARV');
    if (_simpleARV.length > 0) {
        _simpleARV.html(simpleARV);
    }
}

function calculateTotalCashOut() {
    console.log({
        func: 'calculateTotalCashOut',
    });

    let payOffMortgage1 = 0;
    let payOffMortgage2 = 0;
    let payOffOutstandingTaxes = 0;
    let CORTotalLoanAmt = 0;
    let closingCostFinanced = 0;
    let totalCashOut = 0;
    let rehabCostFinanced = 0;
    let prepaidInterestReserve = 0;
    let isCheckedVal = '';
    loanCalculation._totalCashOutToolTip = $('#totalCashOutToolTip');
    let totalCashOutToolTip;
    let _totalCashOut = $('#totalCashOut');
    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
    loanCalculation.isFileSPREO = loanCalculation.getEncryptedPCID() === loanCalculation.PCID_SPREO_CAPITAL;


    if (loanCalculation.isFileSPREO) {

        totalCashOut = loanCalculation.calculateTotalCashOutForSPREO();

    } else if (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {

        totalCashOut = loanCalculation.calculateTotalCashOutForLTC2();

    } else {

        CORTotalLoanAmt = getFieldsValue('CORTotalLoanAmt');
        payOffMortgage1 = getFieldsValue('payOffMortgage1');
        payOffMortgage2 = getFieldsValue('payOffMortgage2');
        payOffOutstandingTaxes = getFieldsValue('payOffOutstandingTaxes');
        closingCostFinanced = getFieldsValue('closingCostFinanced');
        rehabCostFinanced = getFieldsValue('rehabCostFinanced');
        prepaidInterestReserve = getFieldsValue('prepaidInterestReserve');

        isCheckedVal = $('input[name="haveInterestreserve"]').is(":checked");
        if (!isCheckedVal || $('input[name="haveInterestreserve"]:checked').val() === 'No') {
            prepaidInterestReserve = 0;
            loanCalculation.totalCashOutToolTip = 'Cash to be Disbursed = (Initial Loan Amount - Pay-Off on Mortgage 1 - ' +
                'Pay-Off on Mortgage 2 - Pay Off Outstanding Taxes - Closing Costs Financed)';
        } else {
            loanCalculation.totalCashOutToolTip = 'Cash to be Disbursed = (Initial Loan Amount - Pay-Off on Mortgage 1 - ' +
                'Pay-Off on Mortgage 2 - Pay Off Outstanding Taxes - Closing Costs Financed - Pre-paid Interest Reserve Financed)';
        }

        if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE
            || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE
        ) {
            totalCashOut = parseFloat(CORTotalLoanAmt) - parseFloat(payOffMortgage1) - parseFloat(payOffMortgage2) - parseFloat(payOffOutstandingTaxes) - parseFloat(closingCostFinanced) - parseFloat(prepaidInterestReserve);

            loanCalculation.totalCashOutToolTipWithValues = `${CORTotalLoanAmt}
                - ${payOffMortgage1}
                - ${payOffMortgage2}
                - ${payOffOutstandingTaxes}
                - ${closingCostFinanced} 
                - ${prepaidInterestReserve} `;

        } else {
            totalCashOut = parseFloat(CORTotalLoanAmt) - parseFloat(payOffMortgage1) - parseFloat(payOffMortgage2) - parseFloat(payOffOutstandingTaxes) - parseFloat(closingCostFinanced) - parseFloat(rehabCostFinanced);

            loanCalculation.totalCashOutToolTipWithValues = `${CORTotalLoanAmt}
                - ${payOffMortgage1}
                - ${payOffMortgage2}
                - ${payOffOutstandingTaxes}
                - ${closingCostFinanced} 
                - ${rehabCostFinanced} 
                `;
        }

        loanCalculation.totalCashOutToolTip = loanCalculation.totalCashOutToolTip
            + '<hr>' + loanCalculation.totalCashOutToolTipWithValues
            + '<hr>' + autoNumericConverter(totalCashOut.toFixed(2))
        ;
        loanCalculation._totalCashOutToolTip.attr('data-content', loanCalculation.totalCashOutToolTip);
    }
    totalCashOut = autoNumericConverter(totalCashOut.toFixed(2));
    if (_totalCashOut.length > 0) {
        _totalCashOut.html(totalCashOut);
    }

}

function validateRehabCostFinanced() {
    console.log({
        func: 'validateRehabCostFinanced',
    });
    //let rehabCostFinanced = 0, rehabCost = 0;
    let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
    let rehabCost = getFieldsValue('rehabCost');

    if (parseFloat(rehabCostFinanced) > parseFloat(rehabCost)) {
        toastrNotification('Rehab/Construction Cost Financed should not be greater than Rehab/Construction Cost', 'error');
        $('#rehabCostFinanced').val('');
        return false;
    } else {
        return true;
    }

}

function calculatePercentageRehabCostFinanced() {
    let rehabCostFinancedAmt = 0;
    let rehabCost = getFieldsValue('rehabCost');
    let rehabCostPercentageFinanced = getFieldsValue('rehabCostPercentageFinanced');

    if (rehabCostPercentageFinanced > 0) {
        rehabCostFinancedAmt = rehabCostPercentageFinanced / 100.0;
        rehabCostFinancedAmt = rehabCost * rehabCostFinancedAmt;
        rehabCostFinancedAmt = autoNumericConverter(rehabCostFinancedAmt.toFixed(2));
    }

    console.log({
        func: 'calculatePercentageRehabCostFinanced',
        rehabCostFinancedAmt: rehabCostFinancedAmt,
        rehabCost: rehabCost,
        rehabCostPercentageFinanced: rehabCostPercentageFinanced,
    });

    $('#rehabCostFinanced').val(rehabCostFinancedAmt);

    updateLoanDetail();
    validateRehabCostFinanced();
}

function calculateRehabCostFinancedByPercentage() {
    let rehabCostPercentageFinanced = getFieldsValue('rehabCostPercentageFinanced');
    let rehabCostFinancedAmt = 0;
    let rehabCost = getFieldsValue('rehabCost');

    if (rehabCostPercentageFinanced > 0) {
        rehabCostFinancedAmt = rehabCostPercentageFinanced / 100.0;
        rehabCostFinancedAmt = rehabCost * rehabCostFinancedAmt;
        rehabCostFinancedAmt = autoNumericConverter(rehabCostFinancedAmt.toFixed(2));
    }

    console.log({
        func: 'calculateRehabCostFinancedByPercentage',
        rehabCostPercentageFinanced: rehabCostPercentageFinanced,
        rehabCostFinancedAmt: rehabCostFinancedAmt,
        rehabCost: rehabCost,
    });

    $('#rehabCostFinanced').val(rehabCostFinancedAmt);
    updateLoanDetail();
}

function calculateRehabValues() {

    //rehabCostFinanced =  rehabCost * rehabCostPercentageFinanced / 100.0
    // rehabCost = rehabCostFinanced * 100 / rehabCostPercentageFinanced;
    //  rehabCostPercentageFinanced = rehabCostFinanced * 100 / rehabCost;

    let rehabCost = getFieldsValue('rehabCost');
    let rehabCostPercentageFinanced = getFieldsValue('rehabCostPercentageFinanced');
    let rehabCostFinanced = getFieldsValue('rehabCostFinanced');

    let rehabCostEle = $('#rehabCost');
    let rehabCostPercentageFinancedEle = $('#rehabCostPercentageFinanced');
    let rehabCostFinancedEle = $('#rehabCostFinanced');

    if (rehabCostFinanced > 0) {
        if (rehabCost === 0 && rehabCostPercentageFinanced === 0) {
            rehabCostEle.val(autoNumericConverter(rehabCostFinanced.toFixed(2)));
            rehabCostPercentageFinancedEle.val('100.00');
        } else if (rehabCostFinanced > rehabCost) {
            let rehabCostUpdated = rehabCostFinanced * 100.0 / rehabCostPercentageFinanced;
            rehabCostEle.val(autoNumericConverter(rehabCostUpdated.toFixed(2)));
        } else if (rehabCost > rehabCostFinanced) {
            let rehabCostPercentageFinancedUpdated = rehabCostFinanced * 100.0 / rehabCost;
            rehabCostPercentageFinancedEle.val(loanCalculation.formatNumber(rehabCostPercentageFinancedUpdated,10));
            if (rehabCostPercentageFinancedUpdated > 100) {
                toastrNotification('Rehab/Construction Cost Financed should not be greater than Rehab/Construction Cost', 'error');
                rehabCostFinancedEle.val('');
            }
        }
    }
    updateLoanDetail();
}


class originationPoints {
    static typeOfHMLOLoanRequesting = null;
    static originationPointsRate = null;
    static rehabCostFinanced = null;
    static coTotalAmt = null;
    static CORTotalLoanAmt = null;
    static totalLoanAmount1 = null;
    static totalCashOut = null;
    static closingCostFinanced = null;

    static originationIncludeClosingCosts = null;
    static brokerIncludeClosingCosts = null;

    static prepaidInterestReserve = null;
    static totalLoanAmount = null;
    static initialLoanAmount = null;
    static originationPointsValue = null;

    static brokerPointsValue = null;
    static _lockOriginationValue = null;
    static _lockBrokerValue = null;

    static init() {
        this.typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();

        this.originationPointsRate = getFieldsValue('originationPointsRate');
        this.originationPointsValue = getFieldsValue('originationPointsValue');

        this.brokerPointsValue = getFieldsValue('brokerPointsValue');
        this.brokerPointsRate = getFieldsValue('brokerPointsRate');


        this.rehabCostFinanced = getFieldsValue('rehabCostFinanced');

        this.coTotalAmt = getFieldsValue('coTotalAmt');            // txt - Inner text Value
        this.CORTotalLoanAmt = getFieldsValue('CORTotalLoanAmt');
        this.totalLoanAmount1 = getFieldsValue('totalLoanAmount1');
        this.totalCashOut = getFieldsValue('totalCashOut');
        this.closingCostFinanced = getFieldsValue('closingCostFinanced');

        this.originationIncludeClosingCosts = $('input[name=origination_total_loan_amt_checked]').is(':checked');
        this.brokerIncludeClosingCosts = $('input[name=broker_total_loan_amt_checked]').is(':checked');
        this.lockOriginationCheckedStatus = getFieldsValue('lockOriginationValue');
        this.lockBrokerValueCheckedStatus = getFieldsValue('lockBrokerValue');

        this.prepaidInterestReserve = $('input[name="haveInterestreserve"]:checked').val() === 'No' ? 0 : getFieldsValue('prepaidInterestReserve');

        this.totalLoanAmount = this.coTotalAmt ? this.coTotalAmt : (this.CORTotalLoanAmt ? this.CORTotalLoanAmt : this.totalLoanAmount1);
    }

    static setOriginationPoints() {
        console.log({
            func: 'setOriginationPoints',
        });
        this.init();

        let originationPointsRate;
        /**
         * If "on Total Loan Amount" is checked, calculate origination points based on total loan amount.
         * Otherwise, origination points calculation based on "initial loan amount, rehabCostFinanced, prepaidInterestReserve"
         */
        if (this.originationIncludeClosingCosts) {
            if (this.totalLoanAmount > 0) {
                originationPointsRate = this.originationPointsValue / this.totalLoanAmount * 100.0;
                originationPointsRate = roundNumber(originationPointsRate, 6);
            }
        } else {
            if (this.totalLoanAmount - this.closingCostFinanced > 0) {
                originationPointsRate = this.originationPointsValue / (this.totalLoanAmount - this.closingCostFinanced) * 100.0;
                originationPointsRate = roundNumber(originationPointsRate, 6);
            }
        }
        console.log({
            func: 'setOriginationPoints',
            originationIncludeClosingCosts: this.originationIncludeClosingCosts,
            totalLoanAmount: this.totalLoanAmount,
            closingCostFinanced: this.closingCostFinanced,
            originationPointsValue: this.originationPointsValue,
            originationPointsRate: originationPointsRate,
        });
        $('#originationPointsRate').val(originationPointsRate);
        cv3Origination.cv3OriginationPoints();
        cv3Origination.cv3OriginationAmount();
    }

    static setOriginationValue() {
        console.log({
            func: 'setOriginationValue',
        });
        this.init();

        let originationPointsValue;

        if (this.originationIncludeClosingCosts) {
            originationPointsValue = (this.originationPointsRate * this.totalLoanAmount) / 100.0;
            $('#origination_based_on_total_loan_amt').val(1);
        } else {
            originationPointsValue = (this.originationPointsRate * (this.totalLoanAmount - this.closingCostFinanced)) / 100.0;
            $('#origination_based_on_total_loan_amt').val(0);
        }
        if (this.lockOriginationCheckedStatus) {
            this.setOriginationPoints();
        } else {
            $('#originationPointsValue').val(autoNumericConverter(originationPointsValue.toFixed(2)));
        }
        let cv3OriginationPoint = $('#cv3OriginationPoint');
        let cv3ReferralPoint = $('#cv3ReferralPoint');
        if (cv3OriginationPoint.length && cv3ReferralPoint.length) {
            cv3Origination.cv3OriginationPoints();
            cv3Origination.cv3OriginationAmount();
        }

    }

    static setBrokerPointsRate() {
        console.log({
            func: 'setBrokerPointsRate',
        });
        this.init();

        let brokerPointsRate;

        /**
         * If "on Total Loan Amount" is checked, calculate origination points based on total loan amount.
         * Otherwise, origination points calculation based on "initial loan amount, rehabCostFinanced, prepaidInterestReserve"
         */
        if (this.brokerIncludeClosingCosts) {

            if (this.totalLoanAmount > 0) {
                brokerPointsRate = (this.brokerPointsValue / this.totalLoanAmount) * 100.0;
                brokerPointsRate = roundNumber(brokerPointsRate, 6);
            }
        } else {

            if (this.totalLoanAmount - this.closingCostFinanced > 0) {
                brokerPointsRate = (this.brokerPointsValue / (this.totalLoanAmount - this.closingCostFinanced)) * 100.0;
                brokerPointsRate = roundNumber(brokerPointsRate, 6);
            }
        }
        console.log({
            func: 'setBrokerPointsRate',
            brokerIncludeClosingCosts: this.brokerIncludeClosingCosts,
            totalLoanAmount: this.totalLoanAmount,
            closingCostFinanced: this.closingCostFinanced,
            brokerPointsValue: this.brokerPointsValue,
            brokerPointsRate: brokerPointsRate,
        });
        $('#brokerPointsRate').val(brokerPointsRate);
    }

    static setBrokerPointsValue() {
        console.log({
            func: 'setBrokerPointsValue',
        });
        this.init();

        let brokerPointsValue;

        /**
         * If "on Total Loan Amount" is checked, calculate broker points based on total loan amount.
         * Otherwise, broker points calculation based on "initial loan amount, rehabCostFinanced, prepaidInterestReserve"
         */
        if (this.brokerIncludeClosingCosts) {
            brokerPointsValue = (this.brokerPointsRate * this.totalLoanAmount) / 100.0;
            $('#broker_based_on_total_loan_amt').val(1);

        } else {
            brokerPointsValue = (this.brokerPointsRate * (this.totalLoanAmount - this.closingCostFinanced)) / 100.0;
            $('#broker_based_on_total_loan_amt').val(0);
        }
        if (this.lockBrokerValueCheckedStatus) {
            this.setBrokerPointsRate();
        } else {
            brokerPointsValue = autoNumericConverter(brokerPointsValue.toFixed(2));
            $('#brokerPointsValue').val(brokerPointsValue);
        }
    }

    static lockOriginationValue(_eleId) {
        console.log({
            func: 'lockOriginationValue',
        });

        let _eleVal = getFieldsValue(_eleId);
        if (_eleVal) {
            $("#originationPointsValue, #originationPointsRate").attr("readonly", true);
            $("#origination_total_loan_amt_checked").attr("disabled", true).parent('label.checkbox').addClass('checkbox-disabled');
        } else {
            $("#originationPointsValue, #originationPointsRate").prop("readonly", false);
            $("#origination_total_loan_amt_checked").attr("disabled", false).parent('label.checkbox').removeClass('checkbox-disabled');
        }
    }

    static lockBrokerValue(_eleId) {
        console.log({
            func: 'lockBrokerValue',
        });

        let _eleVal = getFieldsValue(_eleId);
        if (_eleVal) {
            $("#brokerPointsRate, #brokerPointsValue").attr("readonly", true);
            $("#broker_total_loan_amt_checked").attr("disabled", true).parent('label.checkbox').addClass('checkbox-disabled');
        } else {
            $("#brokerPointsRate, #brokerPointsValue").prop("readonly", false);
            $("#broker_total_loan_amt_checked").attr("disabled", false).parent('label.checkbox').removeClass('checkbox-disabled');
        }
    }

    static debug() {
        console.log({
            typeOfHMLOLoanRequesting: this.typeOfHMLOLoanRequesting,
            originationPointsRate: this.originationPointsRate,
            rehabCostFinanced: this.rehabCostFinanced,
            coTotalAmt: this.coTotalAmt,
            CORTotalLoanAmt: this.CORTotalLoanAmt,
            totalLoanAmount1: this.totalLoanAmount1,
            totalCashOut: this.totalCashOut,
            closingCostFinanced: this.closingCostFinanced,
            originationIncludeClosingCosts: this.originationIncludeClosingCosts,
            brokerIncludeClosingCosts: this.brokerIncludeClosingCosts,
            prepaidInterestReserve: this.prepaidInterestReserve,
            totalLoanAmount: this.totalLoanAmount,
            initialLoanAmount: this.initialLoanAmount,
            originationPointsValue: this.originationPointsValue,
            brokerPointsValue: this.brokerPointsValue,
            brokerPointsRate: this.brokerPointsRate,
            lockOriginationCheckedStatus: this.lockOriginationCheckedStatus,
            lockBrokerValueCheckedStatus: this.lockBrokerValueCheckedStatus,
        });
    }
}

function calculateOriginationPointsRate() {
    console.log({
        func: 'calculateOriginationPointsRate',
    });

    if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
        originationPoints.setOriginationPoints();
    } else {
        loanCalculation.calculateOriginationRateForLTC2();
        loanCalculation.initLTC2();
    }
    originationPoints.debug();
}

function calculateOriginationPointsValue() {
    console.log({
        func: 'calculateOriginationPointsValue',
    });
    if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
        originationPoints.setOriginationValue();
    } else {
        loanCalculation.calculateOriginationPointValueForLTC2();
    }
    originationPoints.debug();
}

function calculateBrokerPointsRate() {
    console.log({
        func: 'calculateBrokerPointsRate',
    });
    if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
        originationPoints.setBrokerPointsRate();
    } else {
        loanCalculation.calculateBrokerPointsRateForLTC2();
        loanCalculation.initLTC2();
    }
    originationPoints.debug();
}

function calculateBrokerPointsValue() {
    console.log({
        func: 'calculateBrokerPointsValue',
    });
    if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
        originationPoints.setBrokerPointsValue();
    } else {
        loanCalculation.calculateBrokerPointsValueForLTC2();
    }
    originationPoints.debug();
}

function calculateTotalFeesAndCost(autoCal) {
    console.log({
        func: 'calculateTotalFeesAndCost',
    });

    if (autoCal === undefined) {
        autoCal = 0;
    }   // https://www.pivotaltracker.com/story/show/161121479

    let originationPointsValue = 0;
    let brokerPointsValue = 0;
    let appraisalFee = 0;
    let applicationFee = 0;
    let cloChk = false;
    let drawsSetUpFee = 0;
    let estdTitleClosingFee = 0;
    let miscellaneousFee = 0;
    let processingFee = 0;
    let totalFeesAndCost = 0;
    let totalEstPerDiem = 0;
    let closingCostFinanced = 0;
    let perClosingCostFinanced = 0;
    let totalLoanAmount = 0;
    let thirdPartyFees = 0;
    let closingCostNotFinanced = 0;
    let activeTab = $('#activeTab').val();
    let feesArray = [
        "originationPointsValue",
        "brokerPointsValue",
        "appraisalFee",
        "applicationFee",
        "drawsSetUpFee",
        "estdTitleClosingFee",
        "processingFee",
        "valuationBPOFee",
        "valuationAVMFee",
        "creditReportFee",
        "backgroundCheckFee",
        "floodCertificateFee",
        "otherFee",
        "thirdPartyFees",
        "taxImpoundsFee",
        "insImpoundsFee",
        "documentPreparationFee",
        "servicingSetUpFee",
        "floodServiceFee",
        "drawsFee",
        "wireFee",
        "taxServiceFee",
        "inspectionFees",
        "projectFeasibility",
        "dueDiligence",
        "UccLienSearch",
        "closingCostFinancingFee",
        "attorneyFee",
        "escrowFees",
        "recordingFee",
        "underwritingFees",
        "propertyTax",
        "bufferAndMessengerFee",
        "travelNotaryFee",
        "prePaidInterest",
        "realEstateTaxes",
        "insurancePremium",
        "payOffLiensCreditors",
        "wireTransferFeeToTitle",
        "wireTransferFeeToEscrow",
        "pastDuePropertyTaxes",
        "survey",
        "wholeSaleAdminFee",
        "cityCountyTaxStamps",
        "valuationCMAFee",
        "valuationAVEFee",
        "creditCheckFee",
        "employmentVerificationFee",
        "taxReturnOrderFee",
        "constructionHoldbackFee",
        "brokerProcessingFee",
    ];

    feesArray.forEach(function (feeName) {
        let tempFee = parseFloat(getFieldsValue(feeName)) || 0; // Default to 0 if not a number
        totalFeesAndCost += tempFee;
        // console.log(feeName + ' ' + tempFee);
    });
    let _totalFeesAndCost = $('#totalFeesAndCost');
    totalEstPerDiem = getTextValue('totalEstPerDiem');
    totalFeesAndCost = parseFloat(totalFeesAndCost) + parseFloat(totalEstPerDiem);
    totalFeesAndCost = autoNumericConverter(totalFeesAndCost.toFixed(2));

    if (_totalFeesAndCost.length > 0) {
        _totalFeesAndCost.html(totalFeesAndCost);
    }

    if ($("#chkCCF").is(':checked')) {
        let _closingCostFinanced = $('#closingCostFinanced');
        _closingCostFinanced.val(totalFeesAndCost).prop('readOnly', true);
        $('#includeCCF').val('1');
        if (activeTab === 'HMLI') {
            validateClosingCostFinanced();
        }                              // | Validate closing cost financed...
    } else {
        if (autoCal == 1) {
            let _closingCostFinanced = $('#closingCostFinanced');
            _closingCostFinanced.prop('readOnly', false).val('');
            $('#includeCCF').val('0');
        }
    }

    /* Percentage of Closing Cost Financed */
    totalFeesAndCost = replaceCommaValues(totalFeesAndCost);
    closingCostFinanced = getFieldsValue('closingCostFinanced');

    if (totalFeesAndCost > 0) {
        perClosingCostFinanced = (parseFloat(closingCostFinanced) / parseFloat(totalFeesAndCost)) * 100;
    }
    perClosingCostFinanced = autoNumericConverter(perClosingCostFinanced.toFixed(2));
    let _perClosingCostFinanced = $('#perClosingCostFinanced');
    if (_perClosingCostFinanced.length > 0) {
        _perClosingCostFinanced.html(perClosingCostFinanced);
    }

    let _closingCostNotFinancedTooltip = $('#closingCostNotFinancedTooltip');
    loanCalculation.closingCostNotFinancedTooltip = _closingCostNotFinancedTooltip.attr('data-formula');

    /* Closing Cost Not Financed */
    closingCostNotFinanced = parseFloat(totalFeesAndCost) - parseFloat(closingCostFinanced);
    closingCostNotFinanced = autoNumericConverter(closingCostNotFinanced.toFixed(2));

    loanCalculation.closingCostNotFinancedTooltipWithValues = loanCalculation.closingCostNotFinancedTooltip
        + '<hr>' + `${totalFeesAndCost} - ${closingCostFinanced}`
        + '<hr>' + `${closingCostNotFinanced}`;

    let _closingCostNotFinanced = $('#closingCostNotFinanced');
    if (_closingCostNotFinanced.length > 0) {
        _closingCostNotFinanced.html(closingCostNotFinanced);
        _closingCostNotFinancedTooltip.attr('data-content', loanCalculation.closingCostNotFinancedTooltipWithValues);
    }
}

function validateClosingCostFinanced() {
    console.log({
        func: 'validateClosingCostFinanced',
    });

    var closingCostFinanced = 0, totalFeesAndCost = 0;
    var returnVal = false;

    closingCostFinanced = getFieldsValue('closingCostFinanced');
    totalFeesAndCost = getTextValue('totalFeesAndCost');

    if (parseFloat(closingCostFinanced) > parseFloat(totalFeesAndCost)) {
        toastrNotification('Closing Costs Financed should not be greater than Total Fees & Costs', 'error');
        $('#closingCostFinanced').val('');
        return false;

    } else {
        return true;
    }
}

/*Total Cash to Close*/
function calculateTotalCashToClose() {
    console.log({
        func: 'calculateTotalCashToClose',
    });
    let _totalCashToClose = $('#totalCashToClose');
    let closingCostNotFinanced = getTextValue('closingCostNotFinanced');            // txt - Inner text Value
    let downPayment = getTextValue('downPayment');
    let earnestDeposit = getFieldsValue('earnestDeposit');
    let otherDownPayment = getFieldsValue('otherDownPayment');
    let totalCashToClose = 0;
    loanCalculation.isFileSPREO = loanCalculation.getEncryptedPCID() === loanCalculation.PCID_SPREO_CAPITAL;

    if (loanCalculation.isFileSPREO) {
        //loanCalculation._hideFieldsForLTC2 = $('.hideFieldsForLTC2');
        //loanCalculation._hideFieldsForLTC2.hide();

        totalCashToClose = loanCalculation.calculateTotalCashToCloseForSPREO();
    } else if (loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
        totalCashToClose = loanCalculation.calculateTotalCashToCloseForLTC2();
    } else {
        totalCashToClose = closingCostNotFinanced
            + downPayment
            - earnestDeposit
            - otherDownPayment;

        loanCalculation._totalCashToCloseTooltip = $('#totalCashToCloseTooltip');
        loanCalculation.totalCashToCloseTooltip = 'Closing Cost Not Financed + Down Payment - Earnest Deposit -  Paid Outside Escrow';
        loanCalculation.totalCashToCloseTooltipWithValues = `(${closingCostNotFinanced} 
                            + ${downPayment}
                            - ${earnestDeposit}
                            - ${otherDownPayment}
                            )`;

        let totalCashToCloseTooltip = loanCalculation.totalCashToCloseTooltip
            + '<hr>' + loanCalculation.totalCashToCloseTooltipWithValues
            + '<hr>' + autoNumericConverter(totalCashToClose.toFixed(2))
        ;
        loanCalculation._totalCashToCloseTooltip.attr('data-content', totalCashToCloseTooltip);
    }
    totalCashToClose = autoNumericConverter(totalCashToClose.toFixed(2));
    if (_totalCashToClose.length > 0) {
        _totalCashToClose.html(totalCashToClose);
    }
    console.log({
        closingCostNotFinanced: closingCostNotFinanced,
        downPayment: downPayment,
        earnestDeposit: earnestDeposit,
        otherDownPayment: otherDownPayment,
        totalCashToClose: totalCashToClose,
    });
}

class requiredConstruction {
    static requiredConstruction = null;
    static rehabCost = null;
    static rehabCostFinanced = null;
    static targetName = null;
    static requiredConstructionAmt = null;

    static init(targetName) {
        this.requiredConstruction = getFieldsValue('requiredConstruction');
        this.rehabCost = getFieldsValue('rehabCost');
        this.rehabCostFinanced = getFieldsValue('rehabCostFinanced');
        this.targetName = targetName;
    }

    static calculate() {
        console.log({
            func: 'calculate',
        });
        if (this.requiredConstruction > 0) {
            this.requiredConstructionAmt = this.requiredConstruction / 100.0 * (this.rehabCost - this.rehabCostFinanced);
        }

        try {
            $('#' + this.targetName).html(autoNumericConverter(this.requiredConstructionAmt.toFixed(2)));
        } catch (e) {
        }

    }

    static debug() {
        console.log({
            requiredConstruction: this.requiredConstruction,
            rehabCost: this.rehabCost,
            rehabCostFinanced: this.rehabCostFinanced,
            targetName: this.targetName,
            requiredConstructionAmt: this.requiredConstructionAmt,
        });
    }
}

function calculateRequiredConstruction(targetName) {
    console.log({
        func: 'calculateRequiredConstruction',
    });

    requiredConstruction.init(targetName);
    requiredConstruction.calculate();
    requiredConstruction.debug();
}


function calculateDownPaymentByPercentage() {
    console.log({
        func: 'calculateDownPaymentByPercentage',
    });

    let downPaymentPercentage = getFieldsValue('downPaymentPercentage');
    let costBasis = getFieldsValue('costBasis');
    let _maxAmtToPutDown = $('#maxAmtToPutDown');
    let downPaymentAmt;

    if (downPaymentPercentage > 0) {
        downPaymentAmt = parseFloat(downPaymentPercentage / 100);
        downPaymentAmt = parseFloat(costBasis) * parseFloat(downPaymentAmt);
        downPaymentAmt = autoNumericConverter(downPaymentAmt.toFixed(2));
    }
    if(_maxAmtToPutDown.length > 0){
        _maxAmtToPutDown.val(downPaymentAmt);
    }
    loanCalculation.setDownPaymentHtml(downPaymentAmt);
    updateLoanDetail();
}

function calculateDownPaymentPercentage() {
    console.log({
        func: 'calculateDownPaymentPercentage',
    });

    let _downPaymentPercentage = $('#downPaymentPercentage');
    let maxAmtToPutDown = getFieldsValue('maxAmtToPutDown');
    let costBasis = getFieldsValue('costBasis');
    let downPaymentPercentage;

    if (costBasis > 0) {
        downPaymentPercentage = parseFloat(maxAmtToPutDown / costBasis) * 100;
        downPaymentPercentage = autoNumericConverter(downPaymentPercentage.toFixed(5));
    }
    if(_downPaymentPercentage.length > 0){
        _downPaymentPercentage.val(downPaymentPercentage);
    }
    loanCalculation.setDownPaymentHtml(maxAmtToPutDown);
    updateLoanDetail();
}

function calculateCORefiLoanAmtByLTVPercentage() {

    console.log({
        func: 'calculateCORefiLoanAmtByLTVPercentage',
    });
    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();

    if (typeOfHMLOLoanRequesting !== TransactionType.NEW_CONSTRUCTION_EXISTING_LAND) {
        let CORefiLoanAmt;
        let CORefiLTVPercentage = getFieldsValue('CORefiLTVPercentage');
        let assessedValue = getFieldsValue('homeValue');

        if (CORefiLTVPercentage > 0) {
            CORefiLoanAmt = parseFloat(CORefiLTVPercentage / 100);
            CORefiLoanAmt = parseFloat(assessedValue) * parseFloat(CORefiLoanAmt);
            CORefiLoanAmt = autoNumericConverter(CORefiLoanAmt.toFixed(2));

            $('#CORTotalLoanAmt').val(CORefiLoanAmt);
        }
    }

    updateLoanDetail();
}

function calculateCORefiLTVPercentage(autoCal) {
    console.log({
        func: 'calculateCORefiLTVPercentage',
    });

    if (autoCal === undefined) autoCal = 0;   // https://www.pivotaltracker.com/story/show/161121479

    var CORTotalLoanAmt = 0;
    var CORefiLTVPercentage = 0;
    var assessedValue = 0;

    CORTotalLoanAmt = getFieldsValue('CORTotalLoanAmt');
    assessedValue = getFieldsValue('homeValue');
    if (assessedValue > 0) {
        CORefiLTVPercentage = parseFloat(CORTotalLoanAmt / assessedValue) * 100;
        CORefiLTVPercentage = Math.round(CORefiLTVPercentage);
    }

    try {
        $('#CORefiLTVPercentage').val(CORefiLTVPercentage);
    } catch (e) {
    }

    if (autoCal == 0) updateLoanDetail();
}


/**
 * Descrition: New Supporting fields for Interest Rate.
 * Date      : Jan 04, 2018
 * PT #      : 153961351
 * Added By  : Viji
 **/
function calculateHMLOInterestRate(formName, targetName) {
    console.log({
        func: 'calculateHMLOInterestRate',
    });

    var costOfCapital = 0;
    var lien1Rate = 0;
    var yieldSpread = 0;

    costOfCapital = getFieldsValue('costOfCapital');
    yieldSpread = getFieldsValue('yieldSpread');

    lien1Rate = parseFloat(costOfCapital) + parseFloat(yieldSpread);
    lien1Rate = lien1Rate.toFixed(3);

    try {
        $('#' + targetName).val(lien1Rate);
    } catch (e) {
    }

    updateLoanDetail();
}

function dateDiffDiemDays() {
    let interestChargedFromDate = $('#interestChargedFromDate').val();
    let interestChargedEndDate = $('#interestChargedEndDate').val();
    let totalDailyInterestCharge = getFieldsValue('totalDailyInterestCharge');
    let accrualType = $('#accrualType').val();
    let perDiemAccrualType = $('#perDiemAccrualType').val();

    per_diem.calc(
        interestChargedFromDate,
        interestChargedEndDate,
        totalDailyInterestCharge,
        accrualType,
        perDiemAccrualType,
        'diemDays',
        'totalEstPerDiem'
    );

    if (perDiemAccrualType) {
        accrualType = perDiemAccrualType;
    }

    let _diemDays = $('#diemDays');
    let _totalEstPerDiem = $('#totalEstPerDiem');

    /* Diem Days */
    if (!interestChargedFromDate || !interestChargedEndDate) {
        _diemDays.html('');
        _totalEstPerDiem.html('0.00');

        return 0;
    }

    let monthsDays = Dates.monthsDaysDiff(
        interestChargedFromDate,
        interestChargedEndDate,
        !loanCalculation.nonInclusivePerDiem
    );


    console.log({
        func: 'dateDiffDiemDays',
        interestChargedFromDate: interestChargedFromDate,
        interestChargedEndDate: interestChargedEndDate,
        totalDailyInterestCharge: totalDailyInterestCharge,
        accrualType: accrualType,
        monthsDays: monthsDays,
    });


    let diemDays;
    let totalEstPerDiem;

    switch (accrualType) {
        case '365': // 365 is also actual
        case 'Actual/360':
            diemDays = Math.round(monthsDays.actualDays) + ' Days';
            totalEstPerDiem = parseFloat(monthsDays.actualDays) * parseFloat(totalDailyInterestCharge);
            break;

        case '360':
        case '30/365':
            diemDays = ''; //monthsDays.months + ' Months / ' + monthsDays.days + ' Days';
            if (monthsDays.months) {
                diemDays += monthsDays.months + ' Month' + (monthsDays.months !== 1 ? 's' : '');
            }
            if (monthsDays.days) {
                if (monthsDays.months) {
                    diemDays += ' / ';
                }
                diemDays += monthsDays.days + ' Day' + (monthsDays.days !== 1 ? 's' : '');
            }
            totalEstPerDiem = parseFloat(monthsDays.months * 30 + monthsDays.days) * parseFloat(totalDailyInterestCharge);
            break;
    }

    _diemDays.html(diemDays ? diemDays : '0');
    _totalEstPerDiem.html(autoNumericConverter(totalEstPerDiem.toFixed(2)));
}

function autoCalculateTotalLoanAmountARVNew(fldVal) {
    console.log({
        func: 'autoCalculateTotalLoanAmountARVNew',
    });

    let homeValue = 0;
    let downpayment = 0;
    let totalLoanAmount;
    let initialLoanAmount;
    let CORefiLTVPercentage;
    let totalLoanAmountBeforeCal;
    let downPaymentPercentage;
    let LTC;
    let ARV = 0;

    let autoCalcTLAARV = $('input[name=autoCalcTLAARV]:checked').val();
    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
    let totalProjectCost = getTextValue('totalProjectCost');
    let maxArvPer = getFieldsValue('maxArvPer');
    let assessedValue = getFieldsValue('assessedValue');
    let costBasis = getFieldsValue('costBasis');
    let rehabCost = getFieldsValue('rehabCost');
    let maxAmtToPutDown = getFieldsValue('maxAmtToPutDown');
    let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
    let prepaidInterestReserve = $('input[name="haveInterestreserve"]:checked').val() === 'No'
        ? 0
        : getFieldsValue('prepaidInterestReserve');
    let costSpent = getFieldsValue('costSpent');
    let LTCPercentage = getFieldsValue('maxLTCPer');

    let _maxArvPer = $('#maxArvPer');
    let _maxLTCPer = $('#maxLTCPer');
    let _LoanToCost = $('#Loan-to-Cost');
    let _ARV = $('#ARV');
    let _fullARVTooltip = $("#fullARVTooltip");
    let _coTotalAmt = $('#coTotalAmt');
    let _CORTotalLoanAmt = $('#CORTotalLoanAmt');
    let _CORefiLTVPercentage = $('#CORefiLTVPercentage');
    let _totalLoanAmount1 = $('#totalLoanAmount1');
    let _maxAmtToPutDown = $('#maxAmtToPutDown');
    let _downPaymentPercentage = $('#downPaymentPercentage');
    let _acquisitionPriceFinanced = $('#acquisitionPriceFinanced'); //initial Loan Amount
    let _totalLoanAmount = $('.totalLoanAmount');

    if (autoCalcTLAARV === "Yes") {
        _maxArvPer.show();
        _maxLTCPer.hide();
        if (fldVal == 1) {
            _maxArvPer.val('70');
        }
        maxArvPer = getFieldsValue('maxArvPer');
    } else if (autoCalcTLAARV === "LTC" || autoCalcTLAARV === "LTC2") {
        _maxArvPer.hide();
        _maxLTCPer.show();
    } else {
        _maxArvPer.hide();
        _maxLTCPer.hide();
    }
    if (fldVal === 'totalloanamount') {
        loanCalculation.calculateInitialLoanAmount();
        totalLoanAmount = getFieldsValue('totalLoanAmount1');
        maxArvPer = (totalLoanAmount * 100) / parseFloat(assessedValue);
        _maxArvPer.val(parseFloat(maxArvPer.toFixed(5)));
    } else {
        if (autoCalcTLAARV === "Yes") {
            if (maxArvPer > 0) {
                if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
                    typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
                    typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
                    typeOfHMLOLoanRequesting === TransactionType.REFINANCE) {
                    homeValue = getFieldsValue('homeValue');
                    //newinitialLAmt = (.7 x ARV) - rehabcost
                    //initialLoanAmount = (((parseFloat(maxArvPer) * parseFloat(assessedValue)) / 100) - parseFloat(rehabCost));
                    initialLoanAmount = ((parseFloat(maxArvPer) / 100) * parseFloat(assessedValue) - (parseFloat(rehabCostFinanced) + parseFloat(prepaidInterestReserve)));
                    CORefiLTVPercentage = ((parseFloat(initialLoanAmount) * 100) / parseFloat(homeValue));
                    _CORTotalLoanAmt.val(autoNumericConverter(initialLoanAmount.toFixed(2)));
                    _CORefiLTVPercentage.val(CORefiLTVPercentage.toFixed(2));
                    calculateCurrentLoanBalance();
                    totalLoanAmount = parseFloat(initialLoanAmount) + parseFloat(rehabCostFinanced) + parseFloat(prepaidInterestReserve);
                    _coTotalAmt.html(autoNumericConverter(totalLoanAmount.toFixed(2)));
                    if (assessedValue > 0) {
                        ARV = (parseFloat(totalLoanAmount) / parseFloat(assessedValue)) * 100;
                    }
                    ARV = autoNumericConverter(ARV.toFixed(2));
                    if (_ARV.length > 0) {
                        _ARV.html(ARV);
                    }
                    if (_fullARVTooltip.length > 0) {
                        let fullARVTooltip = ` ${totalLoanAmount} / ${assessedValue} * 100 = ${ARV} %`;
                        fullARVTooltip = _fullARVTooltip.data('formula') + '<hr>' + fullARVTooltip;
                        _fullARVTooltip.attr('data-original-title', fullARVTooltip);
                    }
                    if (totalProjectCost > 0) {
                        LTC = (parseFloat(totalLoanAmount) / parseFloat(totalProjectCost)) * 100;
                        LTC = LTC.toFixed(2);
                    }
                    if (_LoanToCost.length > 0) {
                        _LoanToCost.html(LTC);
                    }
                } else {

                    downpayment = (parseFloat(totalProjectCost) - parseFloat(costSpent)) - (parseFloat(assessedValue) * (parseFloat(maxArvPer) / 100));
                    downPaymentPercentage = (downpayment * 100) / parseFloat(costBasis);

                    _maxAmtToPutDown.val(autoNumericConverter(downpayment.toFixed(2)));
                    _downPaymentPercentage.val(downPaymentPercentage.toFixed(2));
                    initialLoanAmount = parseFloat(costBasis) - parseFloat(downpayment);
                    _acquisitionPriceFinanced.html(autoNumericConverter(initialLoanAmount.toFixed(2)));

                    calculateCurrentLoanBalance();
                    calculateHMLOFeeCostTotalLoanAmount();

                    totalLoanAmount = (parseFloat(assessedValue) * parseFloat(maxArvPer)) / 100;
                    _totalLoanAmount1.val(autoNumericConverter(totalLoanAmount.toFixed(2)));
                    _totalLoanAmount.html(autoNumericConverter(totalLoanAmount.toFixed(2)));

                    if (assessedValue > 0) {
                        ARV = (parseFloat(totalLoanAmount) / parseFloat(assessedValue)) * 100;
                    }
                    ARV = autoNumericConverter(ARV.toFixed(2));
                    if (_ARV.length > 0) {
                        _ARV.html(ARV);
                    }
                    if (_fullARVTooltip.length > 0) {
                        let fullARVTooltip = ` ${totalLoanAmount} / ${assessedValue} * 100 = ${ARV} %`;
                        fullARVTooltip = _fullARVTooltip.data('formula') + '<hr>' + fullARVTooltip;
                        _fullARVTooltip.attr('data-original-title', fullARVTooltip);
                    }
                    if (totalProjectCost > 0) {
                        LTC = (parseFloat(totalLoanAmount) / parseFloat(totalProjectCost)) * 100;
                        LTC = LTC.toFixed(2);
                    }
                    if (_LoanToCost.length > 0) {
                        _LoanToCost.html(LTC);
                    }
                }
            }
        } else if (autoCalcTLAARV === "LTC") {
            if (LTCPercentage > 0) {
                if (_LoanToCost.length > 0) {
                    _LoanToCost.html(LTCPercentage);
                }
                totalLoanAmountBeforeCal = getFieldsValue('totalLoanAmount1');

                if (typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
                    typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE ||
                    typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
                    typeOfHMLOLoanRequesting === TransactionType.REFINANCE) {

                    totalLoanAmount = (parseFloat(LTCPercentage) / 100) * parseFloat(totalProjectCost);
                    _coTotalAmt.html(autoNumericConverter(totalLoanAmount.toFixed(2)));

                    initialLoanAmount = parseFloat(totalLoanAmount) - (parseFloat(rehabCostFinanced) + parseFloat(prepaidInterestReserve));
                    _CORTotalLoanAmt.val(autoNumericConverter(initialLoanAmount.toFixed(2)));

                    homeValue = getFieldsValue('homeValue');
                    CORefiLTVPercentage = ((parseFloat(initialLoanAmount) * 100) / parseFloat(homeValue));
                    _CORefiLTVPercentage.val(CORefiLTVPercentage.toFixed(2));

                } else {

                    totalLoanAmount = (parseFloat(LTCPercentage) / 100) * parseFloat(totalProjectCost);
                    _totalLoanAmount1.val(autoNumericConverter(totalLoanAmount.toFixed(2)));
                    _totalLoanAmount.html(autoNumericConverter(totalLoanAmount.toFixed(2)));

                    maxAmtToPutDown = parseFloat(maxAmtToPutDown) + (parseFloat(totalLoanAmountBeforeCal) - parseFloat(totalLoanAmount));
                    _maxAmtToPutDown.val(autoNumericConverter(maxAmtToPutDown.toFixed(2)));

                    downPaymentPercentage = ((parseFloat(maxAmtToPutDown) * 100) / parseFloat(costBasis));
                    _downPaymentPercentage.val(downPaymentPercentage.toFixed(2));

                    initialLoanAmount = parseFloat(costBasis) - parseFloat(maxAmtToPutDown);
                    _acquisitionPriceFinanced.html(autoNumericConverter(initialLoanAmount.toFixed(2)));
                }
            }
        }
    }
    loanCalculation.initLTC2();
}


/*
* Description   : Current Loan Balance.
* Formula       : Current Loan Balance = Initial Loan Amount + Pre-paid Interest Reserves + Closing Costs + Funded Draws
* Task          : https://www.pivotaltracker.com/story/show/160959499
*/
function calculateCurrentLoanBalance() {
    console.log({
        func: 'calculateCurrentLoanBalance',
    });

    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();
    let initialLoanAmount;
    let closingCostFinanced = 0;
    loanCalculation._currentLoanBalanceTooltip = $('#currentLoanBalanceTooltip');
    let prepaidInterestReserve = getFieldsValue('prepaidInterestReserve');
    let totalDrawsFunded;
    let payDownAmount;

    loanCalculation.isFileSPREO = loanCalculation.getEncryptedPCID() === loanCalculation.PCID_SPREO_CAPITAL;

    if (isTransactionTypeRefinance(typeOfHMLOLoanRequesting)
        || typeOfHMLOLoanRequesting === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND) {
        initialLoanAmount = getFieldsValue('CORTotalLoanAmt');   // Initial Loan Amount
    } else {
        initialLoanAmount = getTextValue('acquisitionPriceFinanced');   // Acquisition / Purchase Price
    }

    if(loanCalculation.isFileSPREO) {

        loanCalculation.currentLoanBalance = initialLoanAmount + prepaidInterestReserve;
        loanCalculation.currentLoanBalanceTooltip = "Initial Loan Amount + Prepaid Interest Reserve";
        loanCalculation.currentLoanBalanceTooltipWithValues = `${initialLoanAmount} + ${prepaidInterestReserve}`;

    } else {

         totalDrawsFunded = getTextValue('totalDrawsFunded');
         payDownAmount = getFieldsValue('paydownamount');

        loanCalculation.currentLoanBalanceTooltip =  'Current Loan Balance = Initial Loan Amount \n' +
            '                        + Pre-paid Interest Reserves \n' +
            '                        + Closing Costs \n' +
            '                        + Funded Draws.<br> ' +
            '<b>For Refinance Transaction Types,</b> Current Loan Balance = (Initial Loan Amount + Any Funded Draws)';

        if (!isTransactionTypeRefinance(typeOfHMLOLoanRequesting)) {
            prepaidInterestReserve = getFieldsValue('haveInterestreserveYes', null, true) === 'Yes' ? prepaidInterestReserve : 0;
            closingCostFinanced = getFieldsValue('closingCostFinanced');
            loanCalculation.currentLoanBalanceTooltipWithValues = `${initialLoanAmount} + ${prepaidInterestReserve} + ${closingCostFinanced} + ${totalDrawsFunded} - ${payDownAmount}`
        } else {
            loanCalculation.currentLoanBalanceTooltipWithValues = ` ${initialLoanAmount} + ${totalDrawsFunded} `;
        }
        loanCalculation.currentLoanBalance = initialLoanAmount + prepaidInterestReserve + closingCostFinanced + totalDrawsFunded - payDownAmount;
    }

    loanCalculation.currentLoanBalance = autoNumericConverter(loanCalculation.currentLoanBalance.toFixed(2));
    loanCalculation.currentLoanBalanceTooltip = loanCalculation.currentLoanBalanceTooltip
    + '<hr>' + loanCalculation.currentLoanBalanceTooltipWithValues
    + '<hr>' + loanCalculation.currentLoanBalance;


    loanCalculation._currentLoanBalanceTooltip.attr('data-content', loanCalculation.currentLoanBalanceTooltip);

    console.log({
        typeOfHMLOLoanRequesting: typeOfHMLOLoanRequesting,
        initialLoanAmount: initialLoanAmount,
        prepaidInterestReserve: prepaidInterestReserve,
        closingCostFinanced: closingCostFinanced,
        totalDrawsFunded: totalDrawsFunded,
        payDownAmount: payDownAmount,
        currentLoanBalance: loanCalculation.currentLoanBalance,
    });
    $('#currentLoanBalance').html(loanCalculation.currentLoanBalance);
}

function updateOriginationBrokerPoints(pointType, updateLoanDetails = true) {
    console.log({
        func: 'updateOriginationBrokerPoints',
    });

    setTimeout(function () {
        if (pointType === 'Origination') {
            if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
                calculateOriginationPointsValue();
            } else {
                loanCalculation.calculateOriginationPointValueForLTC2();
            }
        } else {
            if (!loanCalculation.isAutoCalcTotalLoanAmountBasedOnLTC2()) {
                calculateBrokerPointsValue();
            } else {
                loanCalculation.calculateBrokerPointsValueForLTC2();
            }
        }
        if (updateLoanDetails) {
            updateLoanDetail('points');
        }
    }, 100);

}

function updatePaymentTooltip(paymentBased) {
    console.log({
        func: 'updatePaymentTooltip',
    });

    let totalMonthlyPaymentTooltip = '';
    if (paymentBased === 'ILA') {
        totalMonthlyPaymentTooltip = 'If Amortization is <b>Interest Only</b><br>Loan Payment = (Current Loan Balance * Int. Rate) / 1200<br><br>Otherwise,<br>Loan Payment = Current Loan Balance * Int.  Rate / (1 - (1/ (1 + (Int. Rate / 1200))<sup>((Amortization * 12) / 1200)</sup>)';
    }
    if (paymentBased === 'TLA') {
        totalMonthlyPaymentTooltip = 'If Amortization is <b>Interest Only</b><br>Loan Payment = (Total Loan Amount * Int. Rate) / 1200<br><br>Otherwise,<br>Loan Payment = Total Loan Amount * Int.  Rate / (1 - (1/ (1 + (Int. Rate / 1200))<sup>((Amortization * 12) / 1200)</sup>)';
    }
    if (paymentBased === 'SMP') {
        totalMonthlyPaymentTooltip = 'Enter payment amount manually';
    }
    $('#totalMonthlyPaymentTooltip').attr('data-original-title', totalMonthlyPaymentTooltip);
}


function isTransactionTypeRefinance(typeOfHMLOLoanRequesting) {

    return typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.CASH_OUT_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_RATE_TERM_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.RATE_TERM_REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.REFINANCE ||
        typeOfHMLOLoanRequesting === TransactionType.DELAYED_PURCHASE;
}

function isTransactionTypePurchase(typeOfHMLOLoanRequesting) {
    return typeOfHMLOLoanRequesting === TransactionType.PURCHASE
        || typeOfHMLOLoanRequesting === TransactionType.COMMERCIAL_PURCHASE
        || typeOfHMLOLoanRequesting === TransactionType.TRANSACTIONAL
        || typeOfHMLOLoanRequesting === TransactionType.LINE_OF_CREDIT
}

function isTransactionTypeNewConstruction(typeOfHMLOLoanRequesting) {
    return typeOfHMLOLoanRequesting === TransactionType.NEW_CONSTRUCTION_EXISTING_LAND
}


/* calculation for New TPC & LTC */
function calculationNewTPCLTC() {
    console.log({
        func: 'calculationNewTPCLTC',
    });
    let costBasis;
    let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();

    if (isTransactionTypeRefinance(typeOfHMLOLoanRequesting)) {
        costBasis = replaceCommaValues($('.LTCOriginalPurchasePriceField:first').val());
    } else if (isTransactionTypePurchase(typeOfHMLOLoanRequesting) || isTransactionTypeNewConstruction(typeOfHMLOLoanRequesting)) {
        costBasis = (getFieldsValue('costBasis') !== '') ? replaceCommaValues(getFieldsValue('costBasis')) : 0;
    } else {
        costBasis = replaceCommaValues($('.LTCOriginalPurchasePriceField:first').val());
    }
    let costSpent = (getFieldsValue('costSpent') != '') ? replaceCommaValues(getFieldsValue('costSpent')) : 0;
    let rehabCost = (getFieldsValue('rehabCost') != '') ? replaceCommaValues(getFieldsValue('rehabCost')) : 0;
    let totalFeesAndCost = ($('#totalFeesAndCost').text() != '') ? replaceCommaValues($('#totalFeesAndCost').text()) : 0;
    let totalEstPerDiem = (getTextValue('totalEstPerDiem') != '') ? replaceCommaValues(getTextValue('totalEstPerDiem')) : 0;

    let totalFeesAndCostNew = parseFloat(totalFeesAndCost) - parseFloat(totalEstPerDiem);
    let NewTotalProjectCost = parseFloat(costBasis) + parseFloat(rehabCost) + parseFloat(totalFeesAndCostNew) + costSpent;
    NewTotalProjectCost = autoNumericConverter(NewTotalProjectCost.toFixed(2));
    $('#NewTotalProjectCost').text(NewTotalProjectCost);

    let NewTPCToolTipWithValues = ` ${costBasis} + ${rehabCost} + ${totalFeesAndCostNew} + ${costSpent} = $ ${NewTotalProjectCost}`;
    let _NewTPCToolTip = $('#NewTPCToolTip');
    let NewTPCToolTip = _NewTPCToolTip.data('formula') + '<hr>' + NewTPCToolTipWithValues;
    _NewTPCToolTip.attr('data-original-title', NewTPCToolTip);

    /*  LTC w/Soft & Hard Cost */
    let NewLTCToolTip;
    let NewLTCToolTipWithValues;
    let NewLoanToCost;
    let NewLTCCal;

    if (isTransactionTypeRefinance(typeOfHMLOLoanRequesting)) {
        NewLTCToolTip = ' LTC w/Soft & Hard Cost  = Total Loan Amount / (Original Purchase Price + Rehab Cost + Cost Spent + Closing Costs not Financed) / 100 ';
    } else if (isTransactionTypePurchase(typeOfHMLOLoanRequesting) || isTransactionTypeNewConstruction(typeOfHMLOLoanRequesting)) {
        NewLTCToolTip = ' LTC w/Soft & Hard Cost  = Total Loan Amount / (Purchase Price + Rehab Cost + Cost Spent + Closing Costs not Financed) / 100 ';
    } else {
        NewLTCToolTip = ' LTC w/Soft & Hard Cost  = Total Loan Amount / (Original Purchase Price + Rehab Cost + Cost Spent + Closing Costs not Financed) / 100 ';
    }

    let totalLoanAmount = (getFieldsValue('totalLoanAmount1') !== '') ? replaceCommaValues(getFieldsValue('totalLoanAmount1')) : 0;
    let closingCostNotFinanced = (getTextValue('closingCostNotFinanced') != '') ? replaceCommaValues(getTextValue('closingCostNotFinanced')) : 0;

    if ($('.costSpent_disp').is(':visible')) {
        NewLTCCal = parseFloat(costBasis) + parseFloat(rehabCost) + parseFloat(costSpent) + parseFloat(closingCostNotFinanced);
        NewLTCToolTipWithValues = ` ${costBasis} + ${rehabCost} + ${costSpent} + ${closingCostNotFinanced} `;
    } else {
        NewLTCCal = parseFloat(costBasis) + parseFloat(rehabCost) + parseFloat(closingCostNotFinanced);
        NewLTCToolTipWithValues = ` ${costBasis} + ${rehabCost} + ${closingCostNotFinanced} `;
    }
    NewLoanToCost = (parseFloat(totalLoanAmount) / parseFloat(NewLTCCal)) * 100;
    NewLoanToCost = autoNumericConverter(NewLoanToCost.toFixed(2));

    NewLTCToolTip = NewLTCToolTip + "<hr>" + ` ${totalLoanAmount} / ( ${NewLTCToolTipWithValues}) * 100 = ${NewLoanToCost} %`;
    $("#NewLTCToolTip").attr('data-original-title', NewLTCToolTip);

    let _NewLoanToCost = $('#NewLoanToCost');
    if (_NewLoanToCost.length > 0) {
        _NewLoanToCost.html(NewLoanToCost);
    }
}

function calculateExitAmount(val) {
    console.log({
        func: 'calculateExitAmount',
    });

    var totalLoanAmount = exitFeePoints = exitFeeAmount = 0;
    totalLoanAmount = (getFieldsValue('totalLoanAmount1') != '') ? replaceCommaValues(getFieldsValue('totalLoanAmount1')) : 0;
    if (val == '') {
        exitFeePoints = (getFieldsValue('exitFeePoints') != '') ? replaceCommaValues(getFieldsValue('exitFeePoints')) : 0;
        if (exitFeePoints > 0) val = 'points';
        else val = 'amount';
    }
    if (val == 'points') {
        exitFeePoints = (getFieldsValue('exitFeePoints') != '') ? replaceCommaValues(getFieldsValue('exitFeePoints')) : 0;
        exitFeeAmount = (totalLoanAmount * exitFeePoints) / 100;
        $('#exitFeeAmount').val(autoNumericConverter(exitFeeAmount.toFixed(2)));
    } else if (val == 'amount') {
        exitFeeAmount = (getFieldsValue('exitFeeAmount') != '') ? replaceCommaValues(getFieldsValue('exitFeeAmount')) : 0;
        exitFeePoints = (exitFeeAmount * 100) / totalLoanAmount;
        $('#exitFeePoints').val(autoNumericConverter(exitFeePoints.toFixed(2)));
    }
}

$(document).on('change', '.rateMarginClone:first', function () {
    console.log('rateMarginClone');
    $('#' + $(this).attr('data-clone-field')).val(autoNumericConverter($(this).val()));
});

$(document).on('change', '#spread', function () {
    $('.rateMarginClone:first').val(autoNumericConverter($(this).val()));
});

$(document).on('change', '.rateIndexClone:first', function () {
    console.log('rateIndexClone');
    $('#' + $(this).attr('data-clone-field')).val($(this).val()).chosen().trigger("chosen:updated");
});
$(document).on('change', '#rateIndex', function () {
    $('.rateIndexClone:first').val($(this).val()).chosen().trigger("chosen:updated");
});


class cv3Origination {
    static cv3OriginationPoints() {
        let cv3OriginationPoint = $('#cv3OriginationPoint');
        let cv3ReferralPoint = $('#cv3ReferralPoint');
        if (cv3OriginationPoint.length && cv3ReferralPoint.length) {
            let originationPoints = $('#originationPointsRate');
            let originationPointValue = originationPoints.val() ? parseFloat(originationPoints.val()) : 0;
            let cv3ReferralPointValue = cv3ReferralPoint.val() ? parseFloat(cv3ReferralPoint.val()) : 0;
            let cv3OriginationPointValue = parseFloat(originationPointValue) - parseFloat(cv3ReferralPointValue);
            cv3OriginationPoint.val(cv3OriginationPointValue.toFixed(3));
            cv3ReferralPoint.val(cv3ReferralPointValue.toFixed(3));
            this.cv3OriginationAmount();
        }
    }

    static cv3OriginationAmount() {

        let cv3OriginationAmountId = $('#cv3OriginationAmount');
        let cv3ReferralAmountId = $('#cv3ReferralAmount');
        let cv3OriginationPointId = $('#cv3OriginationPoint');
        let cv3ReferralPointId = $('#cv3ReferralPoint');

        let calculatedValue = 0;

        let originationPointValue = getFieldsValue('originationPointsRate');
        let cv3OriginationPointValue = getFieldsValue('cv3OriginationPoint');
        let cv3ReferralPointValue = getFieldsValue('cv3ReferralPoint');

        let originationAmountValue = getFieldsValue('originationPointsValue');
        let cv3OriginationAmountValue = getFieldsValue('cv3OriginationAmount');
        let cv3ReferralAmountValue = getFieldsValue('cv3ReferralAmount');
        let totalLoanAmount = getFieldsValue('LOCTotalLoanAmt');
        if (!totalLoanAmount) totalLoanAmount = getFieldsValue('totalLoanAmount1');

        if (cv3OriginationAmountId.length && cv3ReferralAmountId.length) {
            if (cv3ReferralPointValue && cv3ReferralAmountValue) {
                /*let x = (cv3ReferralPointValue / originationPointValue).toFixed(2);
                calculatedValue = (x * originationAmountValue.toFixed(2)).toFixed(2);
                cv3ReferralAmountId.val(autoNumericConverter(calculatedValue));
                cv3OriginationAmountValue = (parseFloat(originationAmountValue) - calculatedValue).toFixed(2);*/
                cv3OriginationAmountValue = (parseFloat(totalLoanAmount) * parseFloat(cv3OriginationPointValue) / 100).toFixed(2);
                calculatedValue = (replaceCommaValues(originationAmountValue) - replaceCommaValues(cv3OriginationAmountValue)).toFixed(2);
                cv3OriginationAmountId.val(cv3OriginationAmountValue);
                cv3ReferralAmountId.val(calculatedValue);
            } else if (cv3ReferralPointValue && !cv3ReferralAmountValue) {
                let x = (cv3ReferralPointValue / originationPointValue).toFixed(2);
                calculatedValue = (x * originationAmountValue);
                cv3ReferralAmountId.val(autoNumericConverter(calculatedValue.toFixed(2)));
                cv3OriginationAmountValue = parseFloat(originationAmountValue) - calculatedValue;
                cv3OriginationAmountId.val(autoNumericConverter(cv3OriginationAmountValue.toFixed(2)));
            } else {
                cv3OriginationPointId.val(cv3OriginationPointValue.toFixed(3));//0.000
                cv3OriginationAmountId.val(autoNumericConverter(originationAmountValue)); // Origination Amount Value
                cv3ReferralAmountId.val(cv3ReferralPointValue.toFixed(2));//0.00 / entered value
            }
        }
    }
}

class mirrorField {
    static mirrorFieldValues(fromID, toID) {
        $('#' + toID).val($('#' + fromID).val());
    }
}
