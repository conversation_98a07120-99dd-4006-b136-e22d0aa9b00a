<?php
global $assignedPCID, $modulesArray, $glQuickAppTabSection, $userName, $userGroup, $hideBorrowerInfo;

use models\composite\oPC\getQuickAppCustomFormFields;
use models\constants\formFieldDisabledFields;
use models\constants\gl\glFormFieldsVisibleToPC;
use models\constants\gl\glPCID;
use models\cypher;
use models\PageVariables;
use models\Request;
use models\standard\Strings;

$fieldsResultArray = $PCFieldsArray = $sectionsArray = [];
$tabIndex = 1;
$fileTypeSearch = $sectionSearch = $fieldNameSearch = [];

$formFieldDisabledFields = formFieldDisabledFields::$formFieldDisabledFields;
$glFormFieldsVisibleToPC = glFormFieldsVisibleToPC::$glFormFieldsVisibleToPC;

if (isset($_GET['pcId'])) $pcid = cypher::myDecryption(Request::GetClean('pcId'));
$hideResetToDefault = (in_array($pcid, [glPCID::PCID_PROD_CV3, glPCID::PCID_DEV_DAVE]) && PageVariables::$userRole != 'Super') ? 'd-none' : '';

if (isset($_POST['fileTypeSearch'])) $fileTypeSearch = array_map("htmlspecialchars", Request::GetClean('fileTypeSearch'));
if (isset($_POST['sectionSearch'])) $sectionSearch = array_map("htmlspecialchars", Request::GetClean('sectionSearch'));
if (isset($_POST['fieldNameSearch'])) $fieldNameSearch = array_map("htmlspecialchars", Request::GetClean('fieldNameSearch'));

$fieldsResultArray = getQuickAppCustomFormFields::getReport([
        'assignedPCID'    => $assignedPCID,
        'fileTypeSearch'  => $fileTypeSearch,
        'myOpt'           => '',
        'module'          => $modulesArray,
        'sectionSearch'   => $sectionSearch,
        'fieldNameSearch' => $fieldNameSearch,
        'sectionsArr'     => $glQuickAppTabSection]
);

$fieldArr = cypher::myEncryption(json_encode([
    'assignedPCID'    => $assignedPCID,
    'fileTypeSearch'  => $fileTypeSearch,
    'myOpt'           => '',
    'module'          => $modulesArray,
    'sectionSearch'   => $sectionSearch,
    'fieldNameSearch' => $fieldNameSearch,
    'sectionsArr'     => $glQuickAppTabSection]));

if (isset($fieldsResultArray['PCquickAppFields'])) {
    $PCFieldsArray = $fieldsResultArray['PCquickAppFields'];
}

if (isset($fieldsResultArray['PCSectionHeadings'])) {
    $sectionsArray = $fieldsResultArray['PCSectionHeadings'];
}

$tempPCFieldsArray = $tempPCSectionArray = [];
foreach ($PCFieldsArray as $i => $field) {
    $fieldID = trim($field['fieldID']);
    $tempPCFieldsArray[$fieldID] = $field;
}

$glQuickAppTabSectionForForm = $glQuickAppTabSectionTooltip = [];
foreach ($sectionsArray as $s => $section) {
    $glQuickAppTabSectionForForm[$section['sectionID']] = $section['sectionHeading'];
    $glQuickAppTabSectionTooltip[$section['sectionID']] = $section['sectionTooltip'];
}

$pcFieldNameArray = [];
foreach ($PCFieldsArray as $f => $field) {
    $fieldFileTypeArray = explode(',', $field['fileType']);
    if (count(array_intersect(array_column($modulesArray, 'moduleCode'), $fieldFileTypeArray)) > 0 &&
        in_array($field['sectionID'], array_keys($fieldsResultArray))) {
        if (array_key_exists($field['fieldName'], $glFormFieldsVisibleToPC) && !in_array($pcid, $glFormFieldsVisibleToPC[$field['fieldName']])) {
            continue;
        }
        $pcFieldNameArray[$field['fieldName']] = $field['fieldLabel'];
    }
}

if (count($glQuickAppTabSectionForForm) > 0) {
    $sectionBySort = $glQuickAppTabSectionForForm;
    asort($sectionBySort);
}
foreach ($fieldNameSearch as $key => $item) {
    $fieldNameSearch[$key] = stripslashes($item);
}
?>
<form name="processingCompanyForm" id="processingCompanyForm" method="post" action="#">
    <input type="hidden" name="pcId" id="pcId" value="<?php echo cypher::myEncryption($assignedPCID); ?>">
    <input type="hidden" name="tabName" id="tabName" value="LI">
    <input type="hidden" name="userName" id="userName" value="<?php echo $userName; ?>">
    <input type="hidden" name="userGroup" id="userGroup" value="<?php echo $userGroup; ?>">
    <div class="row notice-top notice-warning">
        <div class="col-md-2">
            <label class="col-md-12 font-weight-bold">File Type</label>
            <select name="fileTypeSearch[]" id="fileTypeSearch" data-placeholder="Select File Types"
                    class=" chzn-select odd" multiple>
                <?php
                for ($pM = 0; $pM < count($modulesArray); $pM++) {
                    $selected = '';
                    if (in_array($modulesArray[$pM]['moduleCode'], $fileTypeSearch)) $selected = 'selected';
                    echo "<option value=\"" . trim($modulesArray[$pM]['moduleCode']) . "\" " . $selected . ' >' . trim($modulesArray[$pM]['moduleName']) . '</option>';
                }
                ?>
            </select>
        </div>
        <div class="col-md-2">
            <label class="col-md-12 font-weight-bold">Section</label>
            <select name="sectionSearch[]" id="sectionSearch" data-placeholder="Select Section"
                    class="chzn-select odd" multiple>
                <?php
                foreach ($sectionBySort as $sectionKey => $sectionValue) {
                    $selected = '';
                    if (in_array($sectionKey, $sectionSearch)) $selected = 'selected';
                    echo "<option value=\"" . $sectionKey . "\" " . $selected . ' >' . trim($sectionValue) . '</option>';
                }
                ?>
            </select>
        </div>
        <div class="col-md-3">
            <label class="col-md-12 font-weight-bold">Field Name</label>
            <select name="fieldNameSearch[]" id="fieldNameSearch" data-placeholder="Select Field Names"
                    class="chzn-select odd" multiple>
                <?php
                foreach ($pcFieldNameArray as $fnKey => $fnValue) {
                    $selected = '';
                    if (in_array($fnKey, $fieldNameSearch)) $selected = 'selected';
                    echo "<option value=\"" . htmlentities($fnKey) . "\" " . $selected . ' >' . trim(htmlentities($fnValue)) . '</option>';
                }
                ?>
            </select>
        </div>
        <div class="col-md-1">
            <label class="col-md-12">&nbsp;</label>
            <input class="btn btn-primary " type="submit" name="Filter" id="Filter" value="Filter">
        </div>
        <div class="col-md-1">
            <label class="col-md-12">&nbsp;</label>
            <input class="btn btn-danger" type="submit" onclick="resetField()" name="Reset" id="Reset" value="Reset">
        </div>
        <div class="col-md-7">
            <div class="row">
                <div class="col-md-3 <?php echo $hideResetToDefault ?>">
                    <label class="col-md-12 ">&nbsp;</label>
                    <button class="btn btn-danger btn-primary--icon resetAll" name="resetAll"
                            type="button" id="resetAll">
													<span>
														<i class="flaticon-refresh"></i>
														<span>Reset All to Default</span>
													</span>
                    </button>

                    <span class=" d-none label pad10 resetAll label-danger pointer" id="resetAll">
                        <i class="fa fa-refresh fa-lg" aria-hidden="true"></i> &nbsp;Reset All to Default
                    </span>

                </div>
                <div class="col-md-4">
                    <label class="col-md-12">&nbsp;</label>

                    <button class="btn btn-primary btn-primary--icon" name="req_fields"
                            type="button" id="req_fields">
													<span>
														<i class="far fa-envelope"></i>
														<span>Request New Fields</span>
													</span>
                    </button>

                    <span class="label pad10 label-info pointer d-none" id="req_fields">
                        <i class="fa fa-envelope-o fa-lg" aria-hidden="true"></i> &nbsp;Request New Fields
                    </span>
                </div>
                <div class="col-md-3">
                    <label class="col-md-12">&nbsp;</label>
                    <button class="btn btn-primary btn-primary--icon" name="view_webform"
                            type="button" id="view_webform"
                            onclick="openInNewTab('https://app.lendingwise.com/HMLOWebForm.php?bRc=e4e75f047173cd24&fOpt=8e614f58c0d670e4&lid=03155f04e9d3daeb&opt=e637bd2de4735343&ft=HMLO&op=69ae9aa7bfc04392&UType=00eedfe87c52b964');">
                            <span>
                                <i class="fa fa-search"></i>
                                <span>Example webform with all fields enabled</span>
                            </span>
                    </button>
                </div>
                <div class="col-md-1">
                    <label class="col-md-12">&nbsp;</label>
                    <div id="divLoaderAll" class="left pad2" style="display:none;">
                        <img src="<?php echo CONST_SITE_URL; ?>assets/images/ajax-loader.gif" alt="Please Wait...">
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>


<!-- Modal -->
<div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Required Fields</h4>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                    <div class="alert alert-success hidden" role="alert">
                        <strong>Sucess!</strong> Your request has been sent successfully.
                    </div>
                    <div class="alert alert-warning hidden" role="alert">
                        <strong>Warning!</strong> There was an error sending your request, please try again.
                    </div>
                </div>
                <form name="requiredfields" id="requiredfields" method="post">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="subject">Subject:</label>
                            <input type="text" class="form-control" id="subject" name="subject"
                                   value="Request for new form fields" readonly>
                        </div>
                        <div class="form-group">
                            <label for="comment">Comment:</label>
                            <textarea class="form-control" rows="5" id="comment" name="comment"></textarea>
                            <p id="error_comment" class="text-danger hidden">Please add some comments.</p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="col-md-12">
                    <button type="button" id="req_field_send" name="req_field_send" class="btn btn-primary right">Send
                    </button>
                </div>
            </div>
        </div>

    </div>
</div>

<?php //pr($glQuickAppTabSectionTooltip);
foreach ($glQuickAppTabSectionForForm as $sectionKey => $sectionValue) {
    if (count($fieldsResultArray) > 0) {
        if (isset($fieldsResultArray[$sectionKey])) {
            $tempFieldsResultArray = [];
            $tempFieldsResultArray = $fieldsResultArray[$sectionKey];
            if (count($tempFieldsResultArray) > 0) {
                ?>
                <input type="hidden" name="selectedFId" id="selectedFId" value="">

                <div class="card card-custom mb-4">
                    <div class="card-header card-header-tabs-line bg-gray-100  ">
                        <div class="card-title">
                            <h3 class="card-label">
                                <a href="#" id="secLabel_<?php echo $sectionKey; ?>"
                                   data-type="text"
                                   data-pk="<?php echo cypher::myEncryption($assignedPCID . '#@#' . $sectionKey); ?>"
                                   data-value="<?php echo htmlentities($sectionValue); ?>" data-title="Section Label"
                                   class="updateSectionLabel editable editable-click editable-open tooltipClass"
                                   style="border-bottom: none;"
                                   title="Click to change Section Label">
                                    <?php echo htmlentities($sectionValue) ?>
                                </a>
                            </h3>
                            <span id="secTooltip_<?php echo $sectionKey; ?>"
                                  data-type="textarea"
                                  data-pk="<?php echo cypher::myEncryption($assignedPCID . '-' . $sectionKey); ?>"
                                  data-value="<?php echo html_entity_decode($glQuickAppTabSectionTooltip[$sectionKey]); ?>"
                                  data-html="true"
                                  style="border-bottom: none;"
                                  title="Click to change Section Tooltip"
                                  class="updateSectionToolTip editable editable-click editable-open  ">
                                <i class="fas fa-info-circle text-primary popoverClass"
                                   title="Click to change Section Tooltip"
                                   data-content="<?php echo (nl2br($glQuickAppTabSectionTooltip[$sectionKey])); ?>"></i>
                            </span>
                        </div>
                        <div class="card-toolbar ">

                            <a role="button"
                               class="d-none"
                               data-toggle="collapse"
                               data-parent="#accordion"
                               href="#collapse_<?php echo $sectionKey ?>"
                               aria-expanded="true"
                               aria-controls="collapseOne">
                                        <span class="clickable"
                                              id="">
                                            <i class="glyphicon glyphicon-chevron-down"></i>
                                        </span>
                            </a>


                            <a href="javascript:void(0);"
                               class="clickable tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass chevron_<?php echo $sectionKey ?>_walkthru"
                               id="<?php echo cypher::myEncryption($assignedPCID) . '_' . cypher::myEncryption($sectionKey); ?>"
                               data-card-tool="toggle"
                               data-section="borrowerEmploymentInfo"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                                <i class="ki ki-arrow-down icon-nm"></i>
                            </a>
                            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                               data-card-tool="reload"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                                <i class="ki ki-reload icon-nm"></i>
                            </a>
                            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none"
                               data-card-tool="remove"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                                <i class="ki ki-close icon-nm"></i>
                            </a>
                        </div>
                    </div>

                    <div id="collapse_<?php echo $sectionKey ?>"
                         class="card-body div_<?php echo $sectionKey ?> panel-collapse collapse collapse_img_<?php echo cypher::myEncryption($sectionKey); ?>"
                         role="tabpanel"
                         aria-labelledby="headingOne" style="text-align: center;">
                    </div>
                </div>
            <?php } ?>

            <?php
            if ($sectionKey == 'CBI' || $sectionKey == 'CBA' || $sectionKey == 'SPCF' || $sectionKey == 'CBEI' || $sectionKey == 'CBMI') {
                ?>
                <div class="clearfix"></div>
                <?php
            }
        }
    }
}
?>

<script type="text/javascript">
    $(document).ready(function () {

        $(".resetAll").click(function () {
            var tVal = $(this).val();
            toastr.clear();
            msgType = 'success';
            toastr.options = {
                "positionClass": "toast-center-center",
                "closeButton": true,
                "showDuration": "100000",
                "hideDuration": "100000",
                "timeOut": "2800",
                "extendedTimeOut": "100000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut",
                "allowHtml": true,
            };
            var msg = 'Would you like to reset the form fields?<br><br><input type="button" id="confirmBtn" class="btn btn-primary" value="Yes">&nbsp;<button type="button" id="cancelBtn" class="btn btn-danger">No</button>';
            var $toast = toastr[msgType](msg);
            if ($toast.find('#confirmBtn').length) {
                $toast.delegate('#confirmBtn', 'click', function () {
                    $toast.remove();
                    setTimeout(function () {
                        //saveQuickAppCustomFormFields('', tVal, 'resetAll');
                        saveQuickAppCustomFormFieldsNew('', tVal, 'resetAll');
                    }, 100);
                });
            }
        });

        $("#HBInfo").click(function () {
            saveQuickAppCustomFormFieldsNew('', $('#hideBorrowerInfo').val(), 'HBInfo');
        });
    });


    /*  enable the section wise save button only any event trigger in the section*/
    $(document).on('click', '.panel-collapse', function (e) {
        var data = (this.id).split("_");
        if ($("#save_" + data[1]).is(":disabled")) {
            $(".div_" + data[1] + " input[type=checkbox]").change(function (e) {
                $("#save_" + data[1]).prop("disabled", false);
            });
            $(".chzn-select" + data[1]).change(function (e) {
                $("#save_" + data[1]).prop("disabled", false);
            });
            $(".chzn-select_" + data[1]).change(function (e) {
                $("#save_" + data[1]).prop("disabled", false);
            });
            $(".chzn-select_QA" + data[1]).change(function (e) {
                $("#save_" + data[1]).prop("disabled", false);
            });
            $(".chzn-select_FA" + data[1]).change(function (e) {
                $("#save_" + data[1]).prop("disabled", false);
            });
        }
    });


    $(document).on('click', '.Mandatory', function (e) {
        if (($('#' + this.id).prop('checked'))) {
            var id = '';
            id = ((this.id).replace('Mandatory', '')).trim();
            $("#" + id).prop('checked', true);
        }
    });


    /* Parent unchecked child also unchecked*/

    $(document).on('click', '.unCheckFA', function (e) {
        id = '';
        fieldID = $(this).val();
        id = this.id;
        if (!($('#' + id).prop('checked'))) {
            $("#" + id + "Mandatory").prop('checked', false);
            jQuery(".PR_" + fieldID + "_FA").each(function () {
                var tFieldId = 0;
                tFieldId = $(this).val();
                $(this).prop("checked", false);
                $("#" + this.id).prop('checked', false);
                $("#" + tFieldId + "FAMandatory").prop("checked", false);
            });
        }

    });

    $(document).on('click', '.unCheck', function (e) {
        id = '';
        fieldID = $(this).val();
        id = this.id;
        if (!($('#' + id).prop('checked'))) {
            $("#" + id + "Mandatory").prop('checked', false);
            jQuery(".PR_" + fieldID + "_QA").each(function () {
                var tFieldId = 0;
                tFieldId = $(this).val();
                $(this).prop("checked", false);
                $("#" + this.id).prop('checked', false);
                $("#" + tFieldId + "QAMandatory").prop("checked", false);
            });
        }
    });

    $(document).on('click', '.dispBO', function (e) {
        id = '';
        fieldID = $(this).val();
        id = this.id;
        if (!($('#' + id).prop('checked'))) {
            $("#" + id + "Mandatory").prop('checked', false);
            jQuery(".PR_" + fieldID + "_BO").each(function () {
                var tFieldId = 0;
                tFieldId = $(this).val();
                $(this).prop("checked", false);
                $("#" + this.id).prop('checked', false);
                $("#" + tFieldId + "BOMandatory").prop("checked", false);
            });
        }
    });


    /* Reset section wise data to default settings*/
    $(document).on('click', '.resetSection', function (e) {
        var data = (this.id).split("_");
        $.confirm({
            icon: 'fa fa-warning',
            closeIcon: true,
            title: 'Confirm',
            content: "Do you want to reset the section settings to default?",
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: function () {
                    saveQuickAppCustomFormFieldsNew('', data[0], 'resetSection');
                },
                cancel: function () {

                },
            },
        });
    });

    /* check all Full app display fields*/
    $(document).on('click', '.CheckAllFA', function (e) {
        var classes = $(this).val();
        var disabledValues = "<?php echo implode(',', $formFieldDisabledFields); ?>";
        var disabledArray = [];
        disabledArray = disabledValues.split(',');

        //alert(disabledArray.split(','));
        //if (trim($formFieldDisabledFields) == '') $abc = explode(",", $formFieldDisabledFields); alert($abc);
        if ($(this).prop('checked') == true) {
            $(".checkFA" + classes).not("[disabled]").prop('checked', $(this).prop('checked'));  //check all in column
        } else {
            $(".checkFA" + classes).not("[disabled]").prop("checked", false);  //uncheck all in column
            $(".checkFA" + classes + "Man").not("[disabled]").prop("checked", false);  //uncheck all in mandatory column since the question is turned off no need for mandatory to be selected
            $(".CheckAllFAM").prop("checked", false);  //uncheck the mandatory all checkbox after all mandatory are unchecked
            for (var i = 0; i < disabledArray.length; i++) {
                $('input:checkbox[name="' + disabledArray[i] + '"]').not("[disabled]").prop('checked', true);
            }
        }
    });

    /* check all Quick app display fields*/
    $(document).on('click', '.CheckAllQA', function (e) {
        var classes = this.id;
        var disabledValues = "<?php echo implode(',', $formFieldDisabledFields); ?>";
        var disabledArray = [];
        disabledArray = disabledValues.split(',');

        if ($(this).prop('checked') == true) {
            $(".check" + classes).not("[disabled]").prop('checked', $(this).prop('checked'));   //check all in column
        } else {
            $(".check" + classes).not("[disabled]").prop("checked", false);  //uncheck all in column
            $(".check" + classes + "Man").not("[disabled]").prop("checked", false);   //uncheck all in mandatory column since the question is turned off no need for mandatory to be selected
            $(".CheckAllQAM").prop("checked", false);   //uncheck the mandatory all checkbox after all mandatory are unchecked
            for (var i = 0; i < disabledArray.length; i++) {
                $('input:checkbox[name="' + disabledArray[i] + '"]').not("[disabled]").prop('checked', true);
            }
        }
    });

    /* check all Back Office display fields*/
    $(document).on('click', '.CheckAllBO', function (e) {
        var classes = $(this).val();
        var disabledValues = "<?php echo implode(',', $formFieldDisabledFields); ?>";
        var disabledArray = [];
        disabledArray = disabledValues.split(',');

        if ($(this).prop('checked') == true) {
            $(".checkBO" + classes).not("[disabled]").prop('checked', $(this).prop('checked'));   //check all in column
        } else {
            //clickbo = 0;
            $(".checkBO" + classes).not("[disabled]").prop("checked", false);  //uncheck all in column
            $(".checkBOM" + classes).not("[disabled]").prop("checked", false);   //uncheck all in mandatory column since the question is turned off no need for mandatory to be selected
            $(".CheckAllBOM").prop("checked", false);   //uncheck the mandatory all checkbox after all mandatory are unchecked
            for (var i = 0; i < disabledArray.length; i++) {
                $('input:checkbox[name="BO_' + disabledArray[i] + '"]').not("[disabled]").prop('checked', true);
            }
        }
    });

    /* check all FA Mandatory display fields*/
    $(document).on('click', '.CheckAllFAM', function (e) {
        var classes = $(this).val();
        var disabledValues = "<?php echo implode(',', $formFieldDisabledFields); ?>";
        var disabledArray = [];
        disabledArray = disabledValues.split(',');

        if ($(this).prop('checked') == true) {
            $(".checkFA" + classes + "Man").not("[disabled]").prop('checked', $(this).prop('checked'));  //fa mandatory not on go to on
            $(".CheckAllFA").prop("checked", true);  //check the fa checkbox because if mandatory all is selected for fa then fa select all checkbox needs on
            $(".checkFA" + classes).not("[disabled]").prop('checked', $(this).prop('checked'));  //fa questions not on so turn them on
        } else {
            $(".checkFA" + classes + "Man").not("[disabled]").prop("checked", false);
            for (var i = 0; i < disabledArray.length; i++) {
                $('input:checkbox[name="' + disabledArray[i] + '"]').not("[disabled]").prop('checked', true);
            }
        }
    });

    /* check all QA Mandatory display fields*/
    $(document).on('click', '.CheckAllQAM', function (e) {
        var classes = $(this).val();
        var disabledValues = "<?php echo implode(',', $formFieldDisabledFields); ?>";
        var disabledArray = [];
        disabledArray = disabledValues.split(',');

        if ($(this).prop('checked') == true) {
            $(".check" + classes + "Man").not("[disabled]").prop('checked', $(this).prop('checked'));  //qa mandatory not on go to on
            $(".CheckAllQA").prop("checked", true);  //check the qa checkbox because if mandatory all is selected for qa then qa select all checkbox needs on
            $(".check" + classes).not("[disabled]").prop('checked', $(this).prop('checked'));  //qa questions not on so turn them on
        } else {
            $(".check" + classes + "Man").not("[disabled]").prop("checked", false);
            for (var i = 0; i < disabledArray.length; i++) {
                $('input:checkbox[name="' + disabledArray[i] + '"]').not("[disabled]").prop('checked', true);
            }
        }
    });

    /* check all Back Office Mandatory display fields*/
    $(document).on('click', '.CheckAllBOM', function (e) {
        var classes = $(this).val();
        var disabledValues = "<?php echo implode(',', $formFieldDisabledFields); ?>";
        var disabledArray = [];
        disabledArray = disabledValues.split(',');

        if ($(this).prop('checked') == true) {
            $(".checkBOM" + classes).not("[disabled]").prop('checked', $(this).prop('checked'));
            $(".CheckAllBO").prop("checked", true);  //check the bo checkbox because if mandatory all is selected for qa then qa select all checkbox needs on
            $(".checkBO" + classes).not("[disabled]").prop('checked', $(this).prop('checked'));  //bo questions not on so turn them on
        } else {
            $(".checkBOM" + classes).not("[disabled]").prop("checked", false);
            for (var i = 0; i < disabledArray.length; i++) {
                $('input:checkbox[name="BO_' + disabledArray[i] + '_M"]').not("[disabled]").prop('checked', true);
            }
        }
    });

    $(document).on('click', '.clickToShowLoanProgram', function (e) {
        if ($(this).attr('show-status') == '0') {
            $("#" + $(this).attr('data-id') + "_chosen").show();
            $(this).attr('show-status', '1');
            $(this).html('Click To Hide LoanPrograms');
        } else {
            $("#" + $(this).attr('data-id') + "_chosen").hide();
            $(this).attr('show-status', '0');
            $(this).html('Click To View LoanPrograms');
        }
    });


    /* section wise data save*/
    $(document).on('click', '.savecustomdata', function (e) {
        var data = (this.id).split("_");
        var count = 0;
        // var result = true;
        // $.each($("select.fileLoanPrg"), function(){ console.log(this.id);
        //     if( $("#"+this.id+" :selected").length == 0 ){
        //         toastrNotification('Please select atleast one loan program', 'error');
        //         result = false;
        //         return false;
        //     }
        // });
        // if( result ) {
        $("#save_" + data[1]).prop("disabled", true);
        saveQuickAppCustomFormFieldsNew('', data[1], '');
        //}
    });

    /* colaspe click event */
    $(document).on('click', 'a.clickable', function (e) {
        var $this = $(this);
        if (!$this.hasClass('panel-collapsed')) {
            var data = (this.id).split("_");
            setTimeout(function () {
                getSectionWiseCustomFormFields(data[0], data[1], $('#fieldNameSearch').val());
            }, 100);
            $this.parents('.panel').find('.panel-body').slideUp();
            $this.addClass('panel-collapsed');
            $this.find('i').removeClass('glyphicon-chevron-down').addClass('glyphicon-chevron-up');
        } else {
            $this.parents('.panel').find('.panel-body').slideDown();
            $this.removeClass('panel-collapsed');
            $this.find('i').removeClass('glyphicon-chevron-up').addClass('glyphicon-chevron-down');
        }
    });

    /* section lable editable function */
    $('.updateSectionLabel').editable({
        url: siteSSLUrl + 'backoffice/updateFormSectionLabel.php',
        response: function (upStatus) {
        }
    });

    /*open all section on click function*/
    $('#toggleSwitch').click(function () {
        var opTy = '';
        $('#divLoaderAll').show();
        var pcid = "<?php echo cypher::myEncryption($assignedPCID);?>";
        setTimeout(function () {
            getSectionWiseCustomFormFields(pcid, 'all');
        }, 100);

        $('.collapse').each(function () {
            var tIcon = $('#tIcon').html();
            if (tIcon == '<i class="fa fa-lg fa-plus-circle"></i>') {
                if ($(this).hasClass('in')) {
                    $(this).removeClass('in');
                }
                $(this).addClass('in');
                opTy = 'open';
            } else {
                $(this).removeClass('in');
                opTy = 'close';
            }
        });

        if (opTy == 'open') {
            $('#tIcon').html('<i class="fa fa-lg fa-minus-circle"></i>');
        } else {
            $('#tIcon').html('<i class="fa fa-lg fa-plus-circle"></i>');
        }
    });

    function getLoanProgramData(id, pcid, fileid, fileloanprg, filetypes, type) {
        console.log(fileloanprg); // $fileloanPrgArr = explode(',', $loanPrg);/
        var fileloanprg = $("#fileLoanPrg_" + fileid).val().toString();
        var filetypes = $("#fileModule_" + fileid).val().toString();
        var fileloanprgArr = fileloanprg.split(','); // alert(fileloanprg); console.log(fileloanprgArr);
        var filetypesArr = filetypes.split(',');
        //alert($('#'+id).val() +'===' +fileid);
//fileLoanPrg
        $.ajax({
            type: 'POST',
            url: 'getLoanProgramData.php',
            data: jQuery.param({'fileTypes': $('#' + id).val(), 'pcid': pcid}),
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            success: function (status) {  //console.log(status);
                var result = JSON.parse(status);
                $("#fileLoanPrg_" + fileid + " option").remove();
                $("#fileLoanPrg_" + fileid).val('').trigger("chosen:updated");
                var selectedText = '';
                $.each(result, function (secId, secArr) {
                    if (!jQuery.isEmptyObject(secArr)) { //console.log(secArr); console.log(secId);
                        $("#fileLoanPrg_" + fileid).append('<option class="optnGrp" value="">' + secId + '</option>').trigger("chosen:updated");
                        $.each(secArr, function (fldName, fldArr) {
                            //console.log(fldArr);
                            var a, b;
                            var selected = '';
                            a = fileloanprgArr.indexOf(fldArr.STCode); // alert(a);
                            b = filetypesArr.indexOf(fldArr.moduleCode);
                            if (type == 'selectall' && b >= 0) {
                                selected = ' selected ';
                            }
                            if (a >= 0) {
                                selected = ' selected ';
                            }  //alert(selected);
                            if (selected != '') {
                                selectedText += fldArr.serviceType + ",";
                            }
                            //selected = ' selected ';
                            $("#fileLoanPrg_" + fileid).append('<option value="' + fldArr.STCode + '" ' + selected + '>' + fldArr.serviceType + '</option>').trigger("chosen:updated");
                        });
                        $("#fileLoanPrg_" + fileid).trigger("chosen:updated");
                        $('#spanText_' + fileid).text(selectedText.substring(0, 20) + '...');
                        $('#div_fileLoanPrg_' + fileid).prop('title', selectedText);
                    }
                });
            }

        });
    }

    /* check/uncheck cashflow required fields fields*/
    $(document).on('click', '.disableorenableCashflowFields', function (e) {
        var data = (this.id);
        var classType = data.substr(data.length - 2);
        if ($(this).prop('checked') == true) {
            $(".disableorenableCashflowFields_" + classType).prop("checked", true);
        } else {
            $(".disableorenableCashflowFields_" + classType).prop("checked", false);
        }
    });

    //Required Fields Modal
    $(document).on('click', '#req_fields', function () {
        //load the model here
        $('#myModal').modal({ // disable overlay click to hide the popup
            backdrop: 'static',
            keyboard: false
        });
        //empty the previous note, comments, alerts
        $("#comment").val('');
        $("#error_comment").addClass('hidden');
        $(".alert-success").addClass('hidden');
        $(".alert-warning").addClass('hidden');
    });
    //comments validation
    $(document).on('keyup', '#comment', function () {
        var comment = $('#comment').val();
        if (comment != '') { // hide error note
            $("#error_comment").addClass('hidden');
        } else { // show error note
            $("#error_comment").removeClass('hidden');
            //return false;
        }
    });
    //Required fields send email
    $(document).on('click', '#req_field_send', function () {
        //$('#req_field_send').prop('disabled', true);
        //var form=$("#requiredfields");
        if ($('#comment').val() != '') { // validate for empty comments
            $.ajax({
                type: "POST",
                url: "../pops/send_email_required_fields.php",
                data: $("#requiredfields input, textarea").serialize(),
                success: function (response) {
                    if (response == 1) { //sucess
                        $(".alert-success").removeClass('hidden');
                        $(".alert-warning").addClass('hidden');
                        setTimeout(function () {
                            $(".alert-success").addClass('hidden');
                        }, 4000); //auto close the alert
                        setTimeout(function () {
                            $('#myModal').modal('hide')
                        }, 2000); //auto close the modal popup
                    } else if (response == 0) { // Fail
                        $(".alert-success").addClass('hidden');
                        $(".alert-warning").removeClass('hidden');
                        setTimeout(function () {
                            $(".alert-warning").addClass('hidden');
                        }, 4000);
                        $('#comment').focus();
                    }
                    //$('#req_field_send').prop('disabled', false);
                }
            });
        } else { //show error, stop form submit
            $("#error_comment").removeClass('hidden');
            //$('#req_field_send').prop('disabled', false);
            return false;
        }
    });

    /* clear all loan programs*/
    $(document).on('click', '.clearLP', function (e) {
        if ($('#' + this.id).prop('checked')) {
            var data = (this.id).split("_");
            $("#fileLoanPrg_" + data[0] + " option:selected").prop("selected", false);
            $("#fileLoanPrg_" + data[0]).trigger("chosen:updated");
            $('#div_fileLoanPrg_' + data[0]).prop('title', '');
            $('#' + data[0] + '_selctAllLP').prop('checked', false);
            $("#fileLoanPrg_" + data[0]).prop('disabled', false).trigger("chosen:updated");
            $('#spanText_' + data[0]).text('...');

        }
    });

    $(document).on('click', '.enableUniqueLoanPrograms', function (e) {
        var data = (this.id).split("_");
        if ($('#' + this.id).prop('checked')) {
            $('#file' + data[0] + 'LoanPrg_' + data[1] + "_chosen").show();
            $('#file' + data[0] + 'LoanPrg_' + data[1] + "_chosen").removeClass('d-none');
            $("#file" + data[0] + "LoanPrg_" + data[1]).removeClass('d-none');
        } else {
            $("#file" + data[0] + "LoanPrg_" + data[1] + " option:selected").prop("selected", false);
            $("#file" + data[0] + "LoanPrg_" + data[1]).trigger("chosen:updated");
            $('#file' + data[0] + 'LoanPrg_' + data[1] + "_chosen").hide();
        }
    });

    $(document).on('click', '.selctAllLP', function (e) {
        var data = (this.id).split("_");
        if ($('#' + this.id).prop('checked')) {
            $('#' + data[0] + '_LP').prop('checked', false);
            var res = document.getElementById('div_fileLoanPrg_' + data[0]).getElementsByTagName('a');
            if ($(res).attr('show-status') == '0') {
                $("#" + $(res).attr('data-id') + "_chosen").show();
                $(res).attr('show-status', '1');
                $(res).html('Click To Hide LoanPrograms');
            }
            $("#fileLoanPrg_" + data[0]).prop('disabled', true).trigger("chosen:updated");
        } else {
            $("#fileLoanPrg_" + data[0]).prop('disabled', false).trigger("chosen:updated");
        }
    });


    /* section tooltip editable function */
    $('.updateSectionToolTip').editable({
        display: function (icon) {
            $(this).html($(this).attr('data-icon'));
        },
        url: siteSSLUrl + 'backoffice/updateFromSectionTooltip.php',
        success: function (response) {
            var data = $.parseJSON(response);
            var str = data.sectionToolTip.replace("\\'", "'");
            str = str.replace('\\"', '“');
            str = str.replace('\"', '“');
            str = str.replace('\\', '');
            //var res = '<span class="with-children-tip" style="margin-left: 5px;"><i class="fa fa-info-circle fa-lg tip-bottom" style="color: #ffffff !important;" title="' + str + '"></i></span>';
            $('#' + this.id).find('i').attr('data-content', str);
            $('.popoverClass').popover('hide');
        },
        response: function (upStatus) {
        }
    });

</script>
<script>
    function openInNewTab(url) {
        var win = window.open(url, '_blank');
        win.focus();
    }

    function resetField() {
        $("#fileTypeSearch option:selected").prop("selected", false);
        $("#fileTypeSearch").trigger("chosen:updated");

        $("#sectionSearch option:selected").prop("selected", false);
        $("#sectionSearch").trigger("chosen:updated");

        $("#fieldNameSearch option:selected").prop("selected", false);
        $("#fieldNameSearch").trigger("chosen:updated");
    }

    $(document).on('change', '.fileLoanPrg', function (e) {
        let data = this.id.split("_");
        if ($("#" + this.id).val().length) {
            $("#" + data[1] + "_LP").prop('checked', false);
        } else {
            $("#" + data[1] + "_LP").prop('checked', true);
        }
    });
</script>
<style>
    .editable-pre-wrapped {
        white-space: normal;
    }
</style>

<!-- quickAppCustomFormFields.php -->
