<?php
global $publicUser, $assignedPCID, $fieldsInfo,
       $fileTab, $webFormFOpt, $isLOC, $isHMLO, $myFileInfo, $LMRId, $processorAssignedCompany,
       $webformName, $userGroup, $isMF, $PCID, $selClientId;

use models\composite\oClient\getPCClientEntityInfo;
use models\constants\gl\glBorrowerType;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glDefaultBrokerInHMLOPC;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\glHMLOPresentOccupancy;
use models\constants\gl\glprePaymentPenalty;
use models\constants\gl\glPCID;
use models\Controllers\LMRequest\Property;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Strings;

$glprePaymentPenalty = glprePaymentPenalty::getPrePaymentPenalty($PCID);
$glHMLOPresentOccupancy = ($PCID == glPCID::PCID_PROD_CV3) ? glHMLOPresentOccupancy::$glHMLOPresentOccupancyCV3 :
    $glHMLOPresentOccupancy = glHMLOPresentOccupancy::$glHMLOPresentOccupancy;

$glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;
$glDefaultBrokerInHMLOPC = glDefaultBrokerInHMLOPC::$glDefaultBrokerInHMLOPC;

$fileHMLONewLoanInfo = [];
$fileHMLOInfo = [];
$fileHMLOAssetsInfo = [];
$LMRInfo = [];
$fileContacts = $PCInfo = [];
$listingRealtorInfo = [];
$fileHMLOPropertyInfo = [];
$BrokerInfo = [];
$assetsInfo = [];
$fileAdditionalGuarantorsInfo = [];
$preferredBrokerInfoArray = [];
$fOpt = '';
$fileHMLOBackGroundInfo = [];
$ssnNumberArray = [];
$FilePropInfo = [];
$PCSelectedLoanTerm = [];
$insuranceCompanyContacts = [];
$fileLOAssetsInfo = [];
$glHMLOExtensionOptionKey = [];
$docArray = [];
$PCSelectedTransactionType = [];
$PCSelectedExtnOption = [];
$PCSelectedOccupancy = [];
$REBroker = $REBrokerYesBtn = '';
$QAInfo = $titleContacts = [];

$clientDocsInfo = [];
$clientExperienceInfo = [];
$clientExpProInfo = [];
$clientGUExpInfo = [];
$clientDocsArray = [];
$fileExpFilpGroundUp = [];
$PCClientEntityInfoArray = [];
$propertyManagementInfo = $fileBudgetAndDrawDoc = [];

$haveBorREInvestmentExperience = $borNoOfREPropertiesCompleted = $haveBorRehabConstructionExperience = $borNoOfYearRehabExperience = $borRehabPropCompleted = $areBuilderDeveloper = $doYouHireGC = '';
$haveBorProjectCurrentlyInProgress = $borNoOfProjectCurrently = $haveBorOwnInvestmentProperties = $borNoOfOwnProp = $areBorMemberOfInvestmentClub = '';
$borClubName = '';
$borNoOfFlippingExperience = '';
$fileHMLOEntityInfo = [];
$clientSellExpInfo = [];
$BranchInfo = [];
$estimatedProjectCostArray = [];
$showREInvestmentDispOpt = 'display : none';
$showProjectInProgressDispOpt = 'display : none';
$showRCDispOpt = 'display : none';
$showMemberDispOpt = 'display : none';
$showProjectInProgressDispOpt = 'display : none';
if ($publicUser == 1) {
    if (in_array($assignedPCID, $glDefaultBrokerInHMLOPC)) {
        $REBroker = 'No';
        $REBrokerYesBtn = 'disabled';
    }
}

$brokerLName = '';
$brokerCompany = '';
$brokerEmail = '';
$brokerPhone = '';
$tabIndex = 1;
$coBorLicenseNo = '';
$brokerPhNo1 = '';
$brokerPhNo2 = '';
$brokerPhNo3 = '';
$brokerExt = '';
$borrowerName = '';
$borrowerMName = '';
$borrowerLName = '';
$borrowerEmail = '';
$borrowerSecondaryEmail = '';
$phNo1 = '';
$phNo2 = '';
$phNo3 = '';
$ext = '';
$phoneNumber = '';
$coBPhoneNumber = '';
$isCoBorrower = 0;
$mailingAddress = '';
$mailingCity = '';
$mailingState = '';
$mailingZip = '';
$coBorrowerFName = '';
$coBorrowerLName = '';
$coBorrowerEmail = '';
$coBorrowerMailingAddress = '';
$coBorrowerMailingCity = '';
$coBorrowerMailingState = '';
$coBorrowerMailingZip = '';
$fax = '';
$cellNumber = '';
$coBCellNumber = '';
$coBFax = '';
$coBSsnNumber = '';
$borCreditScoreRange = '';
$ssnNumber = '';
$coBorCreditScoreRange = '';
$borExperianScore = '';
$borEquifaxScore = '';
$borTransunionScore = '';
$coBorExperianScore = '';
$coBorEquifaxScore = '';
$coBorTransunionScore = $borrowerCitizenship = $isServicingMember = $servicingMemberInfo = $agesOfDependent = $numberOfDependents = '';
$defaultDrawFee = null;
$drawFundCalOnDrawsFee = 0;
$serviceProvider = '';
$coBServiceProvider = '';
$marriageDate = '';
$divorceDate = '';
$isHMLOSelOpt = 0;
$borrowerDOB = '';
$borrowerPOB = '';
$presentAddress = '';
$presentUnit = '';
$presentCountry = '';
$presentPropLengthMonths = '';
$currentRPM = '';
$previousUnit = '';
$previousCountry = '';
$previousPropLengthMonths = '';
$previousRPM = '';
$presentCity = '';
$presentState = '';
$presentZip = $presentPropLengthTime = '';
$coBorrowerDOB = '';
$coBorrowerPOB = '';
$coBPresentAddress = '';
$coBPresentCity = '';
$coBPresentState = '';
$coBPresentZip = '';
$borResidedPresentAddr = '';
$coBResidedPresentAddr = '';
$borPresentPropType = '';
$coBPresentPropType = '';
$presentPropLengthTimeCoBor = '';
$coBorrowerCitizenship = '';

$isBorUSCitizen = '';
$isBorDecalredBankruptPastYears = '';
$isAnyBorOutstandingJudgements = '';
$hasBorAnyActiveLawsuits = '';
$hasBorPropertyTaxLiens = '';
$hasBorObligatedInForeclosure = '';
$isBorPresenltyDelinquent = '';
$haveBorOtherFraudRelatedCrimes = '';
$borDecalredBankruptExpln = '';
$personalBankruptcy = '';
$statusForeclosure = '';
$borOutstandingJudgementsExpln = '';
$borActiveLawsuitsExpln = '';
$borPropertyTaxLiensExpln = '';
$borObligatedInForeclosureExpln = '';
$borDelinquentExpln = '';
$borOtherFraudRelatedCrimesExpln = '';
$borBackgroundExplanation = '';
$borDecalredBankruptDispOpt = $borOutstandingJudgementsDispOpt = $borActiveLawsuitsDispOpt = $borOriginAndVisaDispOpt = 'display: none';
$borPropertyTaxLiensDispOpt = 'display: none';
$borObligatedInForeclosureDispOpt = $borDelinquentDispOpt = 'display: none';
$borOtherFraudRelatedCrimesDispOpt = 'display: none';
$borDesignatedBeneficiaryAgreement = '';
$borDesignatedBeneficiaryAgreementExpln = '';

$isCoBorUSCitizen = '';
$isCoBorDecalredBankruptPastYears = '';
$isAnyCoBorOutstandingJudgements = '';
$hasCoBorAnyActiveLawsuits = '';
$hasCoBorPropertyTaxLiens = '';
$hasCoBorObligatedInForeclosure = '';
$isCoBorPresenltyDelinquent = '';
$isCoBorBorrowedDownPayment = '';
$haveCoBorOtherFraudRelatedCrimes = '';
$coBorDecalredBankruptExpln = '';
$coBorOutstandingJudgementsExpln = '';
$coBorActiveLawsuitsExpln = '';
$coBorPropertyTaxLiensExpln = '';
$coBorObligatedInForeclosureExpln = '';
$coBorDelinquentExpln = '';
$coBorOtherFraudRelatedCrimesExpln = '';
$coBorBackgroundExplanation = '';
$coBorBackgroundExplanation = '';
$coBorDecalredBankruptDispOpt = $coBorOutstandingJudgementsDispOpt = $coBorActiveLawsuitsDispOpt = 'display: none';
$coBorPropertyTaxLiensDispOpt = 'display: none';
$coBorObligatedInForeclosureDispOpt = $coBorDelinquentDispOpt = 'display: none';
$coBorOtherFraudRelatedCrimesDispOpt = 'display: none';
$coBorDesignatedBeneficiaryAgreement = '';
$coBorDesignatedBeneficiaryAgreementExpln = '';
$isCoBorIntendToOccupyPropAsPRI = '';
$marriedToBor = '';
$midFicoScoreCoBor = '';

$haveBorREInvestmentExperience = '';
$haveBorRehabConstructionExperience = '';
$haveBorProjectCurrentlyInProgress = '';
$haveBorOwnInvestmentProperties = '';
$areBorMemberOfInvestmentClub = '';
$borNoOfREPropertiesCompleted = '';
$borNoOfFlippingExperience = '';
$coBorNoOfFlippingExperience = '';
$flipPropCompletedLifetime = '';
$borNoOfYearRehabExperience = '';
$borRehabPropCompleted = '';
$borNoOfProjectCurrently = '';
$borNoOfOwnProp = '';
$borClubName = '';
$borREAddress1 = '';
$borREAddress2 = '';
$borREAddress3 = '';
$borOutcomeRE1 = '';
$borOutcomeRE1 = '';
$borOutcomeRE2 = '';
$borOutcomeRE3 = '';
$borRCAddress1 = '';
$borRCAddress2 = '';
$borRCAddress3 = '';
$borRCOutcome1 = '';
$borRCOutcome2 = '';

$borRCOutcome3 = '';
$showREInvestmentDispOpt = $showRCDispOpt = $showProjectInProgressDispOpt = $showBorSquareFootageDispOpt = 'display:none;';
$showOwnInvestmentDispOpt = $showSOREDispOpt = $showMemberDispOpt = $haveBorSellPropertieDisp = 'display:none;';

$haveCoBorREInvestmentExperience = '';
$haveCoBorRehabConstructionExperience = '';
$haveCoBorProjectCurrentlyInProgress = '';
$haveCoBorOwnInvestmentProperties = '';
$areCoBorMemberOfInvestmentClub = '';
$coBorNoOfREPropertiesCompleted = '';
$groundPropCompletedLifetime = '';
$coBorNoOfYearRehabExperience = '';
$coBorRehabPropCompleted = '';
$coBorNoOfProjectCurrently = '';
$coBorNoOfOwnProp = '';
$coBorClubName = '';
$coBorREAddress1 = '';
$coBorREAddress2 = '';
$coBorREAddress3 = '';
$coBorOutcomeRE1 = '';
$coBorOutcomeRE2 = '';
$coBorOutcomeRE3 = '';
$coBorRCAddress1 = '';
$coBorRCAddress2 = '';
$coBorRCAddress3 = '';
$coBorRCOutcome1 = '';
$coBorRCOutcome2 = '';
$coBorRCOutcome3 = '';
$showCoBorREInvestmentDispOpt = $showcoBorRCDispOpt = $showcoBorProjectInProgressDispOpt = $showcoBorOwnInvestmentDispOpt = 'display:none;';
$showcoBorMemberDispOpt = $showcoBorSellDispOpt = 'display:none;';
$showcoBorProfLicenceDispOpt = 'display:none;';
$haveCoBorProfLicences = $coBorProfLicence = '';

$otherMortgage1 = 0;
$otherMortgageBalance1 = 0;
$unsecuredLoans1 = 0;
$unsecuredLoanBalance1 = 0;
$creditCards1 = 0;
$studentLoans1 = 0;
$childSupportOrAlimonyMonthly1 = 0;
$other1 = 0;
$studentLoansBalance1 = 0;
$otherMortgage2 = 0;
$creditCardsBalance1 = 0;
$otherMortgageBalance2 = 0;
$unsecuredLoans2 = 0;
$unsecuredLoanBalance2 = 0;
$creditCards2 = 0;
$creditCardsBalance2 = 0;
$studentLoans2 = 0;
$childSupportOrAlimonyMonthly2 = 0;
$other2 = 0;
$studentLoansBalance2 = 0;
$childSupportOrAlimonyMonthlyBalance1 = 0;
$otherBalance1 = 0;
$childSupportOrAlimonyMonthlyBalance2 = 0;
$otherBalance2 = 0;

$primTotalHouseHoldExpenses = 0;
$coTotalHouseHoldExpenses = 0;
$totalHouseHoldExpenses = 0;
$taxes1 = 0;
$spcf_hoafees = 0;
$lien1Payment = 0;
$lien2Payment = 0;
$otherDescription = '';
$otherAmtOwed = 0;

$propertyAddress = '';
$propertyCity = '';
$propertyState = '';
$propertyZip = '';
$isHouseProperty = '';
$entityZip = '';
$entityLocation = '';
$entityNotes = '';
$corporateSecretaryName = '';
$borrowerUnderEntity = 'Yes';
$entityName = '';
$ENINo = '';
$naicsCode = '';
$entityAddress = '';
$entityCity = '';
$entityState = '';
$entityType = $businessCategory = $productTypeOrServiceSold = $terminalOrMakeModel = $entityPropertyOwnerShip = $businessPhone = $startDateAtLocation = $landlordMortagageContactName = $landlordMortagagePhone = $rentMortagagePayment = $avgMonthlyCreditcardSale = $avgTotalMonthlySale = $annualGrossSales = $annualGrossProfit = $ordinaryBusinessIncome = '';
$businessType = '';
$entityWebsite = '';
$entityStateOfFormation = '';
$statesRegisterdIn = '';
$showBorrowerEntityDispOpt = 'display: none';
$originalPurchasePrice = 0;
$presentOccupancy = '';
$valueOfProperty = '';
$totalDebtOnProperty = '';
$nameOfLenders = '';

$loanTermExpireDate = '';
$HMLOLender = '';
$typeOfHMLOLoanRequesting = '';
$lienPosition = '';
$noOfPropertiesAcquiring = '';
$loanTerm = '';
$lien1Terms = '';
$amortizationType = '';
$lien1Rate = 0;
$isTaxesInsEscrowed = '';
$insurance1 = 0;
$propertyNeedRehab = '';
$isThisGroundUpConstruction = '';
$lotPurchasePrice = 0;
$currentLotMarket = 0;
$lotStatus = '';
$typeOfSale = '';
$docTypeLoanterms = '';
$purchaseCloseDate = '';
$borComment = '';
$resalePrice = 0;
$resaleClosingDate = '';
$maxAmtToPutDown = 0;
$ARV = 0;
$marketLTV = 0;
$rehabCostFinanced = 0;
$LTC = 0;
$totalLoanAmount = 0;
$totalMonthlyPayment = 0;
$acquisitionPriceFinanced = 0;
$acquisitionLTV = 0;
$totalProjectCost = 0;
$perRehabCostFinanced = 0;
$cashOutAmt = 0;
$closingCostFinanced = 0;
$payOffMortgage1 = 0;
$payOffMortgage2 = 0;
$payOffOutstandingTaxes = 0;
$payOffOtherOutstandingAmounts = 0;
$originationPointsRate = 0;
$originationPointsValue = 0;
$brokerPointsRate = 0;
$brokerPointsValue = 0;
$appraisalFee = 0;
$applicationFee = 0;
$drawsSetUpFee = 0;
$estdTitleClosingFee = 0;
$miscellaneousFee = 0;
$processingFee = 0;
$isBorBorrowedDownPayment = '';
$isTaxesInsEscrowedDispOpt = $doesPropertyNeedRehabDispDiv = 'display: none';
$additionalPropertyRestrictions = '';
$acceptedPurchase = '';
$PAExpirationDate = '';

$titleAttorneyName = '';
$titleAttorneyFirmName = '';
$titleAttorneyEmail = '';
$titleAttorneyCity = '';
$titleAttorneyZip = '';
$titleAttorneyState = '';
$titleAttorneyState = '';
$titleAttorneyAddress = '';
$titleAttorneyPhone = '';
$proInsFirstName = '';
$proInsLastName = '';
$proInsName = '';
$proIncEmail = '';
$proIncPhone = '';
$proInsAddress = '';
$proInsCity = '';
$proInsState = '';
$proInsZip = '';
$insuranceCompanyID = 0;
$brokerContactInfo = '';
$initialAdvance = 0;
$SID = 0;
$PCBasicLoanTabFileIdExists = [];

$secondaryFinancingAmount = 0;
$isBorBorrowedDownPayment = '';
$secondaryHolderName = '';
$isBorIntendToOccupyPropAsPRI = '';
$isBorPersonallyGuaranteeLoan = '';
$borBorrowedDownPaymentExpln = '';
$isBlanketLoan = '';
$LOCTotalLoanAmt = '';
$payReserves = '';
$downPaymentPercentage = '';
$totalCashOutAmt = 0;
$valuationBPOFee = 0;
$valuationCMAFee = 0;
$valuationAVEFee = 0;
$valuationAVMFee = 0;
$creditReportFee = 0;
$creditCheckFee = 0;
$employmentVerificationFee = 0;
$backgroundCheckFee = 0;
$taxReturnOrderFee = 0;
$floodCertificateFee = 0;
$loanOriginationFee = 0;
$documentPreparationFee = 0;
$wireFee = 0;
$servicingSetUpFee = 0;
$taxServiceFee = 0;
$floodServiceFee = 0;
$constructionHoldbackFee = 0;
$thirdPartyFees = 0;
$otherFee = 0;
$taxImpoundsMonth = 0;
$taxImpoundsMonthAmt = 0;
$taxImpoundsFee = 0;
$insImpoundsMonth = 0;
$insImpoundsMonthAmt = 0;
$insImpoundsFee = 0;
$interestChargedFromDate = '';
$interestChargedEndDate = '';
$netLenderFundsToBorrower = 0;
$diemDays = '';
$totalDailyInterestCharge = 0;
$totalEstPerDiem = 0;
$paymentReserves = '';
$requiredConstruction = '';
$contingencyReserve = '';
$costOfImprovementsToBeMade = 0;
$LOCTotalLoanAmt = 0;
$rehabCostPercentageFinanced = 100;
$downPaymentPercentage = '';
$welcomeCallDate = '';
$prePaymentPenaltyPercentage = '';
$prePaymentPenalty = '';
$assumability = '';
$calcInrBasedOnMonthlyPayment = '';
$brokerName = '';
$secondaryBrokerName = '';
$HMLOEstateHeldIn = '';
$CORTotalLoanAmt = 0;

$extensionOption = '';
$costBasis = 0;
$rehabCost = 0;
$assessedValue = 0;
$costSpent = 0;
$homeValue = 0;
$restrictionsExplain = '';
$CORefiLTVPercentage = '';
$originalPurchaseDate = '';
$refinanceCurrentLender = '';
$costOfImprovementsMade = 0;
$refinanceMonthlyPayment = 0;
$refinanceCurrentRate = '';
$exitStrategy = '';
$incomeInfo = [];
$titleAttorneyBarNo = '';
$borrowerCallBack = '';
$receivedDate = '';
$entityBillAddrIsPresent = '';
$entityBillAddress = '';
$entityBillCity = '';
$entityBillState = '';
$entityBillZip = '';
$sameAsEntityAddr = 0;
$includeCCF = 0;
$isOwnLand = '';
$landValue = '';
$clientType = '';
$guarantorNotes = '';
$businessTypeEF = '';
$inspectionFees = 0;
$projectFeasibility = 0;
$dueDiligence = 0;
$UccLienSearch = 0;
$assetCollateralized = '';
$tabIndexNo = 1;
$isCoBorrower = 0;
$PublishBInfo = '';
$PublishCBInfo = '';
$BEthnicity = '';
$CBEthnicity = '';
$maritalStatus = '';
$BGender = '';
$CBGender = $desiredCloseDate = '';
$BRace = $CBRace = '';
$propDetailsProcess = '';
$QAID = '';
/*HMDA - (Borrower)*/
$BVeteran = '';
$bFiEthnicity = '';
$bFiEthnicitySub = '';
$bFiEthnicitySubOther = '';
$bFiSex = '';
$bFiRace = '';
$bFiRaceSub = '';
$bFiRaceAsianOther = '';
$bFiRacePacificOther = '';
$bDemoInfo = '';
/*HMDA - (Borrower)*/
/*HMDA - (Co-Borrower)*/
$CBVeteran = '';
$CBFiEthnicity = '';
$CBEthnicitySub = '';
$CBEthnicitySubOther = '';
$CBFiGender = '';
$CBFiRace = '';
$CBRaceSub = '';
$CBRaceAsianOther = '';
$CBRacePacificOther = '';
$CBDDemoInfo = '';
/*HMDA - (Co-Borrower)*/
$memberName = $memberTitle = $memberOwnership = $memberAnnualSalary = $memberAddress = $memberPhone = $memberCell = $memberSSN = $memberDOB = $memberEmail = '';
$haveBorSquareFootage = $borNoOfSquareFeet = '';
$previousState = $previousPropLengthTime = '';
$midFicoScore = '';
$liquidAssets = 0;
$prepaidInterestReserve = $noOfMonthsPrepaid = $interestOnInterestReserve = $closingCostFinancingFee = 0;
$projectName = '';
$referringParty = $hereAbout = '';
$isLO = '';
$mailingAddrAsPresent = 1;
$coBorMailingAddrAsPresent = 0;

$haveInterestreserve = '';
$haveInterestreserveDivDispOpt = '';
$involvedPurchaseDispOpt = 'display : none';
$involvedPurchase = '';
$wholesaleFee = 0;
$isAdditionalGuarantorsDisp = 'display: none;';
$areBorHaveProfLicences = '';
$loanInfoLPSectionDisp = '';
$LMRClientTypeDisplay = '';
$desiredLoanAmount = 0;
$organizationalRef = '';

if (!$LMRId) {
    $HMLOLoanInfoSectionsDisp = ' display: none; ';
}
$contact = '';
$titleCo = '';
$ownedFreeAndClear = $ownedSameEntity = '';
$coBFormerPropType = '';
$coBorPreviousState = '';
$methodOfContact = '';
$methodContactArray = [];
$isLoanPaymentAmt = '';
$LOBorResidedDispOpt = 'display: none;';
$refDivDisplay = 'display: none;';
$showBorrowerPropertyDispOpt = 'display: block;';

if (!is_array($fieldsInfo)) {
    Debug($fieldsInfo);
}
if (count(Arrays::getValueFromArray('ABI', $fieldsInfo)) > 0) {
    $agSecArr = BaseHTML::sectionAccess(['sId' => 'ABI', 'opt' => $fileTab]);
}
if ($publicUser == 1 && $webFormFOpt != 'agent' && trim(BaseHTML::fieldAccess(['fNm' => 'REBroker', 'sArr' => $agSecArr, 'opt' => 'D'])) == 'secShow') {
    $loanInfoLPSectionDisp = 'display: none;';
    $HMLOLoanInfoSectionsDisp = ' display: none;';
}
if ($publicUser == 1) {
    if (trim(BaseHTML::fieldAccess(['fNm' => 'REBroker', 'sArr' => $agSecArr, 'opt' => 'D'])) != 'secShow' || $webFormFOpt == 'agent') {
        if ($LMRId) {
            $LMRClientTypeDisplay = ' display: none;';
        } else {
            $LMRClientTypeDisplay = ' display: block;';
        }
        $HMLOLoanInfoSectionsDisp = ' display: none;';
        $loanInfoLPSectionDisp = 'display: none;';
    } else {
        $LMRClientTypeDisplay = ' display: none;';
    }
}
$seekingCashRefinanceDispOpt = 'display : none;';
$seekingCashRefinance = '';
$seekingCash = '';
$seekingFund = '';
$isAdditionalGuarantors = '';
$showProfLicencesOpt = 'display : none;';
$propertyDescDivDisplay = 'display : none;';
$haveBorProfLicences = $borProfLicence = '';
$fullTimeRealEstateInvestor = '';
$coFullTimeRealEstateInvestor = '';
$propertyValuationDocInfo = $fileLOScheduleRealInfo = $propertyValuationDocs = [];

$haveBorSellPropertie = '';
$borNoOfProSellExperience = '';
$borNoOfProSellCompleted = '';
$haveCoBorSellPropertie = '';
$coBorNoOfProSellExperience = '';
$coBorNoOfProSellCompleted = '';
$coBorSellAddress1 = '';
$coBorSellOutcome1 = '';
$proofOfSale13 = '';
$coBorSellAddress2 = '';
$coBorSellOutcome2 = '';
$proofOfSale14 = '';
$coBorSellAddress3 = '';
$coBorSellOutcome3 = '';
$proofOfSale15 = '';
$sellPropCompletedLifetime = '';

$coBorTotalExp = 0;
$borTotalExp = 0;
$amountOfFinancing = $amountOfFinancingTo = $typicalPurchasePrice = $typicalPurchasePriceTo = $typicalConstructionCosts = $typicalConstructionCostsTo = '';
$typicalSalePrice = $typicalSalePriceTo = $constructionDrawsPerProject = $constructionDrawsPerProjectTo = $monthsPurchaseDateToFirstConst = '';
$monthsPurchaseDateToFirstConstTo = $monthsPurchaseDateUntilConst = $monthsPurchaseDateUntilConstTo = $monthsPurchaseDateToSaleDate = $monthsPurchaseDateToSaleDateTo = $NoOfSuchProjects = $NoOfSuchProjectsTo = '';

$borPriInvesStrategyArray = $coBorPriInvesStrategyArray = [];
$propertyCountyInfo = [];
$FileProInfo = [];
$borPrimaryInvestmentStrategy = $borPrimaryInvestmentStrategyExplain = $coBorPrimaryInvestmentStrategy = $coBorPrimaryInvestmentStrategyExplain = '';
$borPriInvesStrategyDispOpt = 'display : none';
$coBorPriInvesStrategyDispOpt = 'display : none';

$ownPropertyFreeAndClear = '';
$servicer1 = '';
$lien1MaturityDate = '';
$lean1CurrentDefault = '';
$servicer2 = '';
$lien2MaturityDate = '';
$lean2CurrentDefault = '';

$ownPropertyFreeAndClearDispOpt = 'display : none;';
$lien2Rate = '';
$lien2Payment = '';
$rentalIncomePerMonth = 0;
$escrowFees = 0;
$recordingFee = 0;
$prepayentSectionDisplay = 'display : none;';
$bufferAndMessengerFee = 0;
$travelNotaryFee = 0;
$prePaidInterest = 0;
$realEstateTaxes = 0;
$insurancePremium = 0;
$downPayment = 0;
$wireTransferFeeToTitle = 0;
$wireTransferFeeToEscrow = 0;
$pastDuePropertyTaxes = 0;
$underwritingFees = 0;
$propertyTax = 0;
$earnestDeposit = 0;
$otherDownPayment = 0;
$isTherePrePaymentPenalty = '';
$survey = 0;
$wholeSaleAdminFee = 0;

$lenderAddress = $lenderCity = $lenderZip = $lenderStateOfFormation = $isLoanPaymentAmt = '';
$ownedFreeAndClear = $ownedSameEntity = '';
$allowEditToIRPCBranch = '';
$allowEditToIRPCAgent = '';
$allowEditToIRBranch = '';
$allowEditToIRAgent = '';
$LBInfo = $HMLOmlsnumber = $isHouseProperty = $presentOccupancy = $LBContactName = $propConstructionType = $LBContactPhone = $LBContactEmail =
$yearBuilt = $legalDescription = $propertyTaxDueDate = $mortgageDelinquencyAmount = $LBContactName = $LBContactEmail = $LBContactEmail = $LBInfo = '';
$hideLOC = 'display :block;';
$borVisaStatus = $borOrigin = '';
$borLicenseNo = '';
$totalAutoMobiles = 0;
$assetCars = 0;
$assetCarsOwed = 0;
$assetCarsOwed = 0;
$automobilesOwned3x = 0;
$automobilesOwned3x1 = 0;
$faceAmount = 0;
$dateOfFormation = $stocksBondsComNameNumberDesc = '';
$minTimeInBusiness = '';
$recentNSFs = '';
$hasBusinessBankruptcy = '';
$businessBankruptcy = '';
$borrFax = $coBorrowerFax = '';
$basementHome = $CBEID = 0;
$budgetAndDrawsInfo = [];
$propConstructionMethod = '';

$parcelNo = $municipality = $howManyHalfBathRoom = $yearPurchased = $occupancyNotes = $obtainInteriorAccess = $rehabToComplete = $occupancy = $noOfBuildings = $stabilizedRate = $ownerOccupancyPercentage = $noUnitsOccupied = $lot = $district = $section = $block = '';
#new fields MK
$addRentableSqFt = '';
$noOfParking = '';
$currentCommercialTenantOccupancyRate = '';
$anchorTenant = '';
$anchorTenantName = '';
$cre = '';
$resi = '';
$propertyClass = '';
$addNoOfStories = '';
$leaseType = '';
$yearRenovated = '';
$pastDuePropertyTaxes = '';
#new fields MK
$minLiabilityCoverage = '';
$proInsPolicyNo = '';
$floodInsurance1 = '';
$proInsPolicyExpDate = '';
$proInsPolicyEffDate = '';
$proIncWebsite = '';
$proIncRepNotes = '';
$propIncNotes = $desiredFundingAmount = $haveCurrentLoanBal = $balance = $doYouHaveInvoiceToFactor = $invoiceFactorAmount = $heldWith = $useOfFunds = '';
$refinanceCurrentLoanBalance = 0;
$proInsCarrier = '';
$proInsType = '';
$proInsIds = '';
$selInsNames = '';
$reValuationMethod = '';
$geographicAreas = [];
$altPhoneNumber = $workNumber = '';
$overallRealEstateInvesExp = '';
$approvedAcquisition = $borMailingPropType = $leadSource = $borFormerPropType = $coBMailingPropType = $dateOfOperatingAgreement = '';
$coBorliquidReserves = 0;
$showOnBorFormerDiv = '';
$driverLicenseState = '';
$driverLicenseNumber = '';
$coBorDriverLicenseState = '';
$coBorDriverLicenseNumber = '';
$maritalStatusCoBor = '';
/* Subject Property Details */
$propertyLocation = '';
/* Subject Property Details */
/* Estimated Project Cost */
$epcArray = [];
$landAcquisition = '';
$newBuildingConstruction = '';
$constructionContingency = '';
$businessAcquisition = '';
$landAndBusinessAcquisition = '';
$buildingOrLeasehold = '';
$acquisitionOfMachineryEquipment = '';
$acquisitionOfFurnitureFixtures = '';
$inventoryPurchase = '';
$workingCapital = '';
$refinancingExistingBusinessDebt = '';
$franchiseFee = '';
$closingCost = '';
$otherCostText = '';
$otherCost = '';
$otherCost2 = '';
$otherCostText2 = '';
$estProjectCost = '';
$lessOwnerEquityToBeInjected = '';
$lessSellerCarryBack = '';
$loanRequestedForProject = '';
/* Estimated Project Cost */

/* Creditors / Liabilities (Non-Real Estate) */
$creditorInfoArray = [];
$creditorInfoStatusArray = [];
$creditorName = '';
$creditorAddress = '';
$creditorPhone = '';
$creditorAcctNumber = '';
$creditorAcctType = '';
$creditorAcctStatus = '';
$creditorIsItSecured = '';
$creditorStatus = '';
$creditorType = '';
$accDesc = '';
$creditorRate = '';
$creditorMinPayment = '';
$creditorMonthsBehind = '';
$monthsLeftToPay = '';
$originalBalance = '';
$creditorAcctBalance = '';
$payAtBeforeClosing = '';
$notificationDate = '';
$creditorMaturityDate = '';
$otherLiabilityNotes = '';
//Creditor Details
$creditorRepName = '';
$creditorRepAddress = '';
$creditorCity = '';
$creditorState = '';
$creditorZip = '';
$creditorRepPhoneNo = '';
$creditorRepFax = '';
$creditorRepEmail = '';
$creditorRepNotes = '';
//Collection Agency Details
$collectionAgencyName = '';
$creditorAgentName = '';
$creditorAgentAddress = '';
$creditorAgentCity = '';
$creditorAgentState = '';
$creditorAgentZip = '';
$creditorAgentPhoneNo = '';
$creditorAgentFax = '';
$creditorAgentEmail = '';
$creditorAgentNotes = '';

$clTotalLiabilities = 0;
$clTotalMonthlyPayment = 0;
/* Creditors / Liabilities (Non-Real Estate) */
/* Business Entity */
$grossIncomeLastYear = '';
$netIncomeLastYear = '';
$grossIncome2YearsAgo = '';
$netIncome2YearsAgo = '';
$entityService = '';
$entityProduct = '';
$entityB2B = '';
$entityB2C = '';
$benBusinessHomeBased = '';
$benCreditCardPayments = '';
$benChargeSalesTax = '';
$benEmployeesPaid = '';
$benCardProcessorBank = '';
$merchantProcessingBankName = '';
$benBusinessLocation = '';
$benHowManyLocation = '';
$benOtherLocation = '';
$benBusinessFranchise = '';
$benNameOfFranchise = '';
$benPointOfContact = '';
$benPointOfContactPhone = '';
$benPointOfContactEmail = '';
$benWebsiteForFranchise = '';
$noOfEmployeesAfterLoan = '';
$averageBankBalance = '';
$isBusinessSeasonal = '';
$isBusinessSeasonalPeakMonth = '';
/* Business Entity */
/* Additional Questions */
$purposeOfLoan = '';
$balloonPayment = '';
$prePayExcessOf20percent = '';
$limitedOrNot = '';
$loanMadeWholly = '';
$securityInstrument = '';
/* Additional Questions */
/* Collateral */
$collateralArray = null;
$commercialRealEstate = '';
$furnitureFixtures = '';
$residentialRealEstate = '';
$vehicle = '';
$inventory = '';
$usedEquipmentMachinery = '';
$accountsReceivable = '';
$vacantLand = '';
$cash = '';
$leaseholdImprovement = '';
$investmentAccount = '';
$newEquipmentMachinery = '';
$retirementAccount = '';
$other = '';
$none = '';
$collateralDescription = '';

$commercialRealEstateDate = '';
$commercialRealEstateDesc = '';
$vehiclesDate = '';
$vehiclesDesc = '';
$accountsReceivableDate = '';
$leaseholdImprovementsDesc = '';
$newEquipmentMachineryDesc = '';
$furnitureFixturesDesc = '';
$inventoryDate = '';
$vacantLandDate = '';
$vacantLandDesc = '';
$investmentAccountsDesc = '';
$otherDate = '';
$otherDesc = '';
$residentialRealEstateDate = '';
$residentialRealEstateDesc = '';
$usedEquipmentMachineryDesc = '';
$retirementAccountDesc = '';

/* Collateral */

/* Admin Info */
$hearingDate = '';
/* Admin Info */

/* Loan Terms */
$desiredInterestRateRangeFrom = '';
$desiredInterestRateRangeTo = '';
/* Loan Terms */

/* Project Management */
$propMgmtArray = [];
$propMgmtDocsArray = [];
$propMgmtRowId = '';
$pmSubjectMarketArea = '';
$pmSubjectMarketAreaExpl = '';
$pmRealEstatePropertyManagement = '';
$pmRealEstatePropertyManagementDocumentation = '';
$pmTenant = '';
$pmTenantExpl = '';
$pmAuditsPastYear = '';
$pmAuditsPastYearExpl = '';
$pmLeaseTerms = '';
$pmLeaseTermsExpl = '';
$pmIllegalActivities = '';
$pmIllegalActivitiesExpl = '';
$pmMaterialDeferred = '';
$pmMaterialDeferredExpl = '';
$pmPermitViolation = '';
$pmPermitViolationExpl = '';
$pmTenantDelinquencies = '';
$pmTenantDelinquenciesExpl = '';
$pmCondemnationProceeding = '';
$pmCondemnationProceedingExpl = '';
$pmAffordableHousing = '';
$pmAffordableHousingExpl = '';
$pmRentControl = '';
$pmRentControlExpl = '';
$pmFairHousingACT = '';
$pmFairHousingACTExpl = '';
$pmAmericanDisability = '';
$pmAmericanDisabilityExpl = '';
$pmHazardReductionAct = '';
$pmHazardReductionActExpl = '';
$pmPracticesAct = '';
$pmPracticesActExpl = '';
$pmOverallExperienceDesc = '';
/* Project Management */
$businessReference = '';
$liabilitiesInfo = [];

if ($isLOC == 1) $hideLOC = 'display :none;'; //https://www.pivotaltracker.com/story/show/159946019
for ($j = 1; $j <= 10; $j++) {
    if ($j == 1) {
        ${'showMember' . $j . 'DispOpt'} = 'display : table-row';
    } else {
        ${'showMember' . $j . 'DispOpt'} = 'display:none';
    }

    ${'member' . $j . 'Name'} = '';
    ${'member' . $j . 'Title'} = '';
    ${'member' . $j . 'Ownership'} = '';
    ${'member' . $j . 'AnnualSalary'} = '';
    ${'member' . $j . 'Address'} = '';
    ${'member' . $j . 'Phone'} = '';
    ${'member' . $j . 'Cell'} = '';
    ${'member' . $j . 'SSN'} = '';
    ${'member' . $j . 'DOB'} = '';
    ${'member' . $j . 'Email'} = '';
    ${'member' . $j . 'CreditScore'} = '';
}
$trustType = '';
$retirementEntity = '';
if ($isHMLO != '') $isHMLOSelOpt = $isHMLO;
global $PCFieldsInfo;
if (count($myFileInfo) > 0) {
    if (array_key_exists('fileHMLOInfo', $myFileInfo)) $fileHMLOInfo = $myFileInfo['fileHMLOInfo'];
    if (array_key_exists('fileHMLOAssetsInfo', $myFileInfo)) $fileHMLOAssetsInfo = $myFileInfo['fileHMLOAssetsInfo'];
    if (array_key_exists('LMRInfo', $myFileInfo)) $LMRInfo = $myFileInfo['LMRInfo'];
    if (array_key_exists('fileContacts', $myFileInfo)) $fileContacts = $myFileInfo['fileContacts'];
    if (array_key_exists('listingRealtorInfo', $myFileInfo)) $listingRealtorInfo = $myFileInfo['listingRealtorInfo'];
    if (array_key_exists('fileHMLOPropertyInfo', $myFileInfo)) $fileHMLOPropertyInfo = $myFileInfo['fileHMLOPropertyInfo'];
    if (array_key_exists('BrokerInfo', $myFileInfo)) $BrokerInfo = $myFileInfo['BrokerInfo'];
    if (array_key_exists('branchModuleInfo', $myFileInfo)) $moduleRequested = $myFileInfo['branchModuleInfo'];
    if (array_key_exists('docArray', $myFileInfo)) $docArray = $myFileInfo['docArray'];
    if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['LMRClientTypeInfo'])) $LMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'][$LMRId];
    }

    if (array_key_exists('branchClientTypeInfo', $myFileInfo)) $servicesRequested = $myFileInfo['branchClientTypeInfo'];
    if (array_key_exists('fileModuleInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['fileModuleInfo'])) $fileModuleInfo = $myFileInfo['fileModuleInfo'][$LMRId];
    }
    if (array_key_exists('fileHMLOBackGroundInfo', $myFileInfo)) $fileHMLOBackGroundInfo = $myFileInfo['fileHMLOBackGroundInfo'];
    if (array_key_exists('incomeInfo', $myFileInfo)) $incomeInfo = $myFileInfo['incomeInfo'];
    if (array_key_exists('PCFieldsInfo', $myFileInfo)) $PCFieldsInfo = $myFileInfo['PCFieldsInfo'];
    if (array_key_exists('AddGuarantorsInfo', $myFileInfo)) $fileAdditionalGuarantorsInfo = $myFileInfo['AddGuarantorsInfo'];
    if (array_key_exists('QAInfo', $myFileInfo)) $QAInfo = $myFileInfo['QAInfo'];
    /** Fetch Q+A info **/
    if (array_key_exists('fileExpFilpGroundUp', $myFileInfo)) $fileExpFilpGroundUp = $myFileInfo['fileExpFilpGroundUp'];
    /** Fetch File Background **/
    if (array_key_exists('clientDocsArray', $myFileInfo)) $clientDocsArray = $myFileInfo['clientDocsArray'];
    /** Fetch Client Docs **/
    if (array_key_exists('fileHMLOEntityInfo', $myFileInfo)) $fileHMLOEntityInfo = $myFileInfo['fileHMLOEntityInfo'];
    if (array_key_exists('fileMemberOfficerInfo', $myFileInfo)) $fileMemberOfficerInfo = $myFileInfo['fileMemberOfficerInfo'];
    if (array_key_exists('fileHMLONewLoanInfo', $myFileInfo)) $fileHMLONewLoanInfo = $myFileInfo['fileHMLONewLoanInfo'];
    if (array_key_exists('BranchInfo', $myFileInfo)) $BranchInfo = $myFileInfo['BranchInfo'];
    if (array_key_exists('PCInfo', $myFileInfo)) $PCInfo = $myFileInfo['PCInfo'];
    if (array_key_exists('propertyValuation', $myFileInfo)) $propertyValuationDocInfo = $myFileInfo['propertyValuation'];
    if (array_key_exists('fileLOScheduleRealInfo', $myFileInfo)) $fileLOScheduleRealInfo = $myFileInfo['fileLOScheduleRealInfo'];
    if (array_key_exists('propertyCountyInfo', $myFileInfo)) $propertyCountyInfo = $myFileInfo['propertyCountyInfo'];
    if (array_key_exists('FileProInfo', $myFileInfo)) $FileProInfo = $myFileInfo['FileProInfo'];
    if (array_key_exists('fileBudgetAndDrawDoc', $myFileInfo)) $fileBudgetAndDrawDoc = $myFileInfo['fileBudgetAndDrawDoc'];
    if (array_key_exists('propertyValuationDocs', $myFileInfo)) $propertyValuationDocs = $myFileInfo['propertyValuationDocs'];
    if (array_key_exists('paydownInfo', $myFileInfo)) $filepaydownInfo = $myFileInfo['paydownInfo'];
    if (array_key_exists('estimatedProjectCost', $myFileInfo)) $estimatedProjectCostArray = $myFileInfo['estimatedProjectCost'];
    if (array_key_exists('creditorInfo', $myFileInfo)) $creditorInfoArray = $myFileInfo['creditorInfo'];
    if (array_key_exists('creditorInfoStatus', $myFileInfo)) $creditorInfoStatusArray = $myFileInfo['creditorInfoStatus'];
    if (array_key_exists('collateralArray', $myFileInfo)) $collateralArray = $myFileInfo['collateralArray'];
    if (array_key_exists('collateralValuesArray', $myFileInfo)) $collateralValuesArray = $myFileInfo['collateralValuesArray'];
    if (array_key_exists('propMgmtArray', $myFileInfo)) $propMgmtArray = $myFileInfo['propMgmtArray'];
    if (array_key_exists('propMgmtDocsArray', $myFileInfo)) $propMgmtDocsArray = $myFileInfo['propMgmtDocsArray'];
    $fileHMLOEntityRefInfoArray = [];
    $borrowerAlternateNamesArray = [];
    if (array_key_exists('fileHMLOEntityRefInfo', $myFileInfo)) $fileHMLOEntityRefInfoArray = $myFileInfo['fileHMLOEntityRefInfo'];
    if (array_key_exists('borrowerAlternateNames', $myFileInfo)) $borrowerAlternateNamesArray = $myFileInfo['borrowerAlternateNames'];
    if (array_key_exists('liabilitiesInfo', $myFileInfo)) $liabilitiesInfo = $myFileInfo['liabilitiesInfo'];


    for ($i = 0; $i < count($LMRClientTypeInfo ?? []); $i++) {
        $clientType = $LMRClientTypeInfo[$i]['ClientType'];
    }
    if ($clientType == '') {
        $clientType = 'TBD';
    }
    if (count($LMRInfo) > 0) {
        $lien1Rate = Strings::showField('lien1Rate', 'LMRInfo');
        $lien1Terms = Strings::showField('lien1Terms', 'LMRInfo');
        $SID = Strings::showField('SID', 'RESTInfo');
        $maritalStatus = Strings::showField('maritalStatus', 'LMRInfo');
        $occupancy = Strings::showField('occupancy', 'LMRInfo');
    }

    $annualPremium = Strings::showField('annualPremium', 'fileHMLOPropertyInfo');
    $brokerName = ucwords(Strings::showField('firstName', 'BrokerInfo') . ' ' . Strings::showField('lastName', 'BrokerInfo'));

    $secondaryBrokerName = ucwords(Strings::showField('firstName', 'SecondaryBrokerInfo') . ' ' . Strings::showField('lastName', 'SecondaryBrokerInfo'));


}

$purchaseCloseDate = Strings::showField('closingDate', 'QAInfo'); /* Merge closing Date in Admin tab with Purchase / Target Closing Date in Loan info tab on Jul 26, 2017 - Ticket Id: **********/

/** Broker Info Section **/
$brokerFName = ucwords(Strings::showField('firstName', 'BrokerInfo'));
$brokerLName = ucwords(Strings::showField('lastName', 'BrokerInfo'));
$brokerCompany = ucwords(Strings::showField('company', 'BrokerInfo'));
$brokerEmail = Strings::showField('email', 'BrokerInfo');
$brokerPhone = Strings::showField('phoneNumber', 'BrokerInfo');
$allowEditToIRAgent = Strings::showField('allowEditToIR', 'BrokerInfo');

$brokerPhoneNumberArray = Strings::splitPhoneNumber($brokerPhone);
if (count($brokerPhoneNumberArray) > 0) {
    $brokerPhNo1 = trim($brokerPhoneNumberArray['No1']);
    $brokerPhNo2 = trim($brokerPhoneNumberArray['No2']);
    $brokerPhNo3 = trim($brokerPhoneNumberArray['No3']);
    $brokerExt = trim($brokerPhoneNumberArray['Ext']);
}
/** Broker Info End **/


/** Secondary Broker Info Section **/
$secondaryBrokerFName = ucwords(Strings::showField('firstName', 'SecondaryBrokerInfo'));
$secondaryBrokerLName = ucwords(Strings::showField('lastName', 'SecondaryBrokerInfo'));
$secondaryBrokerCompany = ucwords(Strings::showField('company', 'SecondaryBrokerInfo'));
$secondaryBrokerEmail = Strings::showField('email', 'SecondaryBrokerInfo');
$secondaryBrokerPhone = Strings::showField('phoneNumber', 'SecondaryBrokerInfo');
$secondaryBallowEditToIRAgent = Strings::showField('allowEditToIR', 'SecondaryBrokerInfo');

$secondaryBrokerPhoneNumberArray = Strings::splitPhoneNumber($secondaryBrokerPhone);
if (count($secondaryBrokerPhoneNumberArray) > 0) {
    $secondaryBokerPhNo1 = trim($secondaryBrokerPhoneNumberArray['No1']);
    $secondaryBrokerPhNo2 = trim($secondaryBrokerPhoneNumberArray['No2']);
    $secondaryBrokerPhNo3 = trim($secondaryBrokerPhoneNumberArray['No3']);
    $secondaryBrokerExt = trim($secondaryBrokerPhoneNumberArray['Ext']);
}
/** Secondary Broker Info End **/


/** Branch Info Section **/
if (count($BranchInfo) > 0) {
    $allowEditToIRBranch = Strings::showField('allowEditToIR', 'BranchInfo');
}

/** Branch Info End **/

/** Pc Info End **/


/** Borrower & Co-Borrower Info Start **/
$borrowerName = Strings::showField('borrowerName', 'LMRInfo');
$borrowerFName = Strings::showField('borrowerFName', 'LMRInfo');
$borrowerMName = Strings::showField('borrowerMName', 'LMRInfo');
$borrowerLName = Strings::showField('borrowerLName', 'LMRInfo');
$borrowerEmail = Strings::showField('borrowerEmail', 'LMRInfo');
$borrowerSecondaryEmail = Strings::showField('borrowerSecondaryEmail', 'LMRInfo');
$cellNumber = Strings::showField('cellNumber', 'LMRInfo') == '' ? Strings::showField('clientCell', 'clientInfo') : Strings::showField('cellNumber', 'LMRInfo');
$borrFax = Strings::showField('fax', 'LMRInfo');
$ssnNumber = Strings::showField('ssnNumber', 'LMRInfo') == '' ? Strings::showField('ssnNumber', 'clientInfo') : Strings::showField('ssnNumber', 'LMRInfo');
$isCoBorrower = trim(Strings::showField('isCoBorrower', 'LMRInfo'));
$mailingAddress = Strings::showField('mailingAddress', 'LMRInfo');
$mailingCity = Strings::showField('mailingCity', 'LMRInfo');
$mailingState = Strings::showField('mailingState', 'LMRInfo');
$mailingZip = Strings::showField('mailingZip', 'LMRInfo');
$coBorrowerFName = Strings::showField('coBorrowerFName', 'LMRInfo');
$coBorrowerLName = Strings::showField('coBorrowerLName', 'LMRInfo');
$coBorrowerEmail = Strings::showField('coBorrowerEmail', 'LMRInfo');
$coBCellNumber = Strings::showField('coBCellNumber', 'LMRInfo');
$coBorrowerFax = Strings::showField('coBFax', 'LMRInfo');
$coBSsnNumber = Strings::showField('coBSsnNumber', 'LMRInfo');
$coBorrowerMailingAddress = Strings::showField('coBorrowerMailingAddress', 'LMRInfo');
$coBorrowerMailingCity = Strings::showField('coBorrowerMailingCity', 'LMRInfo');
$coBorrowerMailingState = Strings::showField('coBorrowerMailingState', 'LMRInfo');
$coBorrowerMailingZip = Strings::showField('coBorrowerMailingZip', 'LMRInfo');
$coBorPreviousState = Strings::showField('coBorPreviousState', 'LMRInfo');
$phoneNumber = Strings::showField('phoneNumber', 'LMRInfo');
$coBPhoneNumber = Strings::showField('coBPhoneNumber', 'LMRInfo');
$serviceProvider = Strings::showField('serviceProvider', 'LMRInfo');
$coBServiceProvider = Strings::showField('coBServiceProvider', 'LMRInfo');
$marriageDate = Strings::showField('marriageDate', 'LMRInfo');
$divorceDate = Strings::showField('divorceDate', 'LMRInfo');

$presentAddress = Strings::showField('presentAddress', 'file2Info');
$presentCity = Strings::showField('presentCity', 'file2Info');
$presentState = Strings::showField('presentState', 'file2Info');
$presentZip = Strings::showField('presentZip', 'file2Info');
$presentPropLengthTime = Strings::showField('presentPropLengthTime', 'file2Info');
$previousPropLengthTime = Strings::showField('previousPropLengthTime', 'file2Info');
$borMailingPropType = Strings::showField('borMailingPropType', 'file2Info');
$borPresentPropType = Strings::showField('borPresentPropType', 'file2Info');
$presentUnit = Strings::showField('presentUnit', 'file2Info');
$presentCountry = Strings::showField('presentCountry', 'file2Info');
$presentPropLengthMonths = Strings::showField('presentPropLengthMonths', 'file2Info');
$currentRPM = Strings::showField('currentRPM', 'file2Info');
$previousUnit = Strings::showField('previousUnit', 'file2Info');
$previousCountry = Strings::showField('previousCountry', 'file2Info');
$previousPropLengthMonths = Strings::showField('previousPropLengthMonths', 'file2Info');
$previousRPM = Strings::showField('previousRPM', 'file2Info');

$coBPresentAddress = Strings::showField('coBPresentAddress', 'file2Info');
$coBPresentCity = Strings::showField('coBPresentCity', 'file2Info');
$coBPresentState = Strings::showField('coBPresentState', 'file2Info');
$coBPresentZip = Strings::showField('coBPresentZip', 'file2Info');
$borrowerDOB = Strings::showField('borrowerDOB', 'LMRInfo') != '' ? Strings::showField('borrowerDOB', 'LMRInfo') : Strings::showField('borrowerDOB', 'clientInfo');
$borrowerPOB = Strings::showField('borrowerPOB', 'LMRInfo') != '' ? Strings::showField('borrowerPOB', 'LMRInfo') : Strings::showField('borrowerPOB', 'clientInfo');
$coBorrowerDOB = Strings::showField('coBorrowerDOB', 'LMRInfo');
$coborrowerPOB = Strings::showField('coborrowerPOB', 'LMRInfo');
$receivedDate = Strings::showField('receivedDate', 'LMRInfo');
$guarantorNotes = Strings::showField('guarantorNotes', 'file2Info');
$methodOfContact = Strings::showField('methodOfContact', 'file2Info') != '' ? Strings::showField('methodOfContact', 'file2Info') : Strings::showField('methodOfContact', 'clientInfo');
$borResidedPresentAddr = Strings::showField('borResidedPresentAddr', 'fileLoanOriginationInfo');
$coBorMailingAddrAsPresent = Strings::showField('coBorMailingAddrAsPresent', 'file2Info');
$coBResidedPresentAddr = Strings::showField('coBResidedPresentAddr', 'fileLoanOriginationInfo');
$coBPresentPropType = Strings::showField('coBPresentPropType', 'file2Info');
$presentPropLengthTimeCoBor = Strings::showField('presentPropLengthTimeCoBor', 'file2Info');
$mailingAddrAsPresent = Strings::showField('mailingAddrAsPresent', 'file2Info');
$coBorMailingAddrAsPresent = Strings::showField('coBorMailingAddrAsPresent', 'file2Info');
$coBFormerPropType = Strings::showField('coBFormerPropType', 'file2Info');
$borFormerPropType = Strings::showField('borFormerPropType', 'file2Info');
$coBMailingPropType = Strings::showField('coBMailingPropType', 'file2Info');
$workNumber = Strings::showField('workNumber', 'LMRInfo');
$altPhoneNumber = Strings::showField('altPhoneNumber', 'LMRInfo');
$borComment = Strings::showField('borComment', 'LOExplanationInfo'); //Exit Strategy Explanation

$propertyAddress = Property::$primaryPropertyInfo->propertyAddress;  // Property address
$propertyCountry = Property::$primaryPropertyInfo->propertyCountry;
$propertyUnit = Property::$primaryPropertyInfo->propertyUnit;
$propertyCondition = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyCondition;  //Subject Property Condition
$conditionNotes = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyConditionNotes;  //Condition Notes
$propertyZip = Property::$primaryPropertyInfo->propertyZipCode;
$propertyCity = Property::$primaryPropertyInfo->propertyCity;
$propertyState = Property::$primaryPropertyInfo->propertyState;
$driverLicenseState = Strings::showField('driverLicenseState', 'LMRInfo') != '' ? Strings::showField('driverLicenseState', 'LMRInfo') : Strings::showField('driverLicenseState', 'clientInfo');
$driverLicenseNumber = Strings::showField('driverLicenseNumber', 'LMRInfo') != '' ? Strings::showField('driverLicenseNumber', 'LMRInfo') : Strings::showField('driverLicenseNumber', 'clientInfo');
$coBorDriverLicenseState = Strings::showField('coBorDriverLicenseState', 'LMRInfo');
$coBorDriverLicenseNumber = Strings::showField('coBorDriverLicenseNumber', 'LMRInfo');
$maritalStatusCoBor = Strings::showField('maritalStatusCoBor', 'LMRInfo');

$propMgmntContactID = '';
$HOA1ContactID = '';
$tempPropMgmntContactName = '';
$propMgmntContactPerson = '';
$propMgmntCompany = '';
$propMgmntNotes = '';
$propMgmntPhone = '';
$propMgmntContactEmail = '';
$propMgmntAddress = '';
$propMgmntCity = '';
$propMgmntState = '';
$propMgmntZip = '';
$HOA2Contacts = [];
$HOA2ContactID = 0;
$HOA2ContactName = '';
$HOA2CompanyName = '';

if (trim($mailingAddrAsPresent) == '') $mailingAddrAsPresent = 0;
if (trim($coBorMailingAddrAsPresent) == '') $coBorMailingAddrAsPresent = 0;

if (array_key_exists('HOA2', $fileContacts)) {
    $HOA2Contacts = $fileContacts['HOA2'];
}
if (count($HOA2Contacts) > 0) {
    $HOA2ContactID = trim($HOA2Contacts['CID']);
    $HOA2ContactName = stripslashes(trim($HOA2Contacts['contactName']));
    $HOA2CompanyName = trim($HOA2Contacts['companyName']);
}
if (array_key_exists('PM', $fileContacts)) {
    $propertyManagementInfo = $fileContacts['PM'];
}
if (count($propertyManagementInfo) > 0) {
    $propMgmntContactID = trim($propertyManagementInfo['CID']);
    $propMgmntContactPerson = $tempPropMgmntContactName = trim($propertyManagementInfo['contactName']);
    $propMgmntCompany = trim($propertyManagementInfo['companyName']);
    $propMgmntContactEmail = trim($propertyManagementInfo['email']);
    $propMgmntAddress = trim($propertyManagementInfo['address']);
    $propMgmntCity = trim($propertyManagementInfo['city']);
    $propMgmntState = trim($propertyManagementInfo['state']);
    $propMgmntZip = trim($propertyManagementInfo['zip']);
    $propMgmntNotes = trim($propertyManagementInfo['description']);
    $propMgmntPhone = trim($propertyManagementInfo['phone']);
}
$propMgmntPhoneArray = [];
$propMgmntPhNo1 = '';
$propMgmntPhNo2 = '';
$propMgmntPhNo3 = '';
$propMgmntPhExt = '';
$propMgmntPhoneArray = Strings::splitPhoneNumber($propMgmntPhone);
if (count($propMgmntPhoneArray) > 0) {
    $propMgmntPhNo1 = $propMgmntPhoneArray['No1'];
    $propMgmntPhNo2 = $propMgmntPhoneArray['No2'];
    $propMgmntPhNo3 = $propMgmntPhoneArray['No3'];
    $propMgmntPhExt = $propMgmntPhoneArray['Ext'];
}

if (count($QAInfo) > 0) {
    $QAID = Strings::showField('QAID', 'QAInfo');
    $bankruptcyDispositionStatus = Strings::showField('bankruptcyDispositionStatus', 'QAInfo');
    $saleForHowLong = Strings::showField('saleForHowLong', 'QAInfo');
    $PublishBInfo = Strings::showField('PublishBInfo', 'QAInfo');
    $BEthnicity = Strings::showField('BEthnicity', 'QAInfo') != '' ? Strings::showField('BEthnicity', 'QAInfo') : Strings::showField('ethnicity', 'clientInfo');
    $BRace = Strings::showField('BRace', 'QAInfo') != '' ? Strings::showField('BRace', 'QAInfo') : Strings::showField('race', 'clientInfo');
    $BGender = Strings::showField('BGender', 'QAInfo') != '' ? Strings::showField('BGender', 'QAInfo') : Strings::showField('gender', 'clientInfo');
    $BVeteran = Strings::showField('BVeteran', 'QAInfo') != '' ? Strings::showField('BVeteran', 'QAInfo') : Strings::showField('veteran', 'clientInfo');
    $bFiEthnicity = Strings::showField('bFiEthnicity', 'QAInfo') != '' ? Strings::showField('bFiEthnicity', 'QAInfo') : Strings::showField('FIEthnicity', 'clientInfo');
    $bFiEthnicitySub = Strings::showField('bFiEthnicitySub', 'QAInfo') != '' ? Strings::showField('bFiEthnicitySub', 'QAInfo') : Strings::showField('FIEthnicitySub', 'clientInfo');
    $bFiEthnicitySubOther = Strings::showField('bFiEthnicitySubOther', 'QAInfo') != '' ? Strings::showField('bFiEthnicitySubOther', 'QAInfo') : Strings::showField('FIEthnicitySubOther', 'clientInfo');
    $bFiSex = Strings::showField('bFiSex', 'QAInfo') != '' ? Strings::showField('bFiSex', 'QAInfo') : Strings::showField('FISex', 'clientInfo');
    $bFiRace = Strings::showField('bFiRace', 'QAInfo') != '' ? Strings::showField('bFiRace', 'QAInfo') : Strings::showField('FIRace', 'clientInfo');
    $bFiRaceSub = Strings::showField('bFiRaceSub', 'QAInfo') != '' ? Strings::showField('bFiRaceSub', 'QAInfo') : Strings::showField('FIRaceSub', 'clientInfo');
    $bFiRaceAsianOther = Strings::showField('bFiRaceAsianOther', 'QAInfo') != '' ? Strings::showField('bFiRaceAsianOther', 'QAInfo') : Strings::showField('FIRaceAsianOther', 'clientInfo');
    $bFiRacePacificOther = Strings::showField('bFiRacePacificOther', 'QAInfo') != '' ? Strings::showField('bFiRacePacificOther', 'QAInfo') : Strings::showField('FIRacePacificOther', 'clientInfo');
    $bDemoInfo = Strings::showField('bDemoInfo', 'QAInfo') != '' ? Strings::showField('bDemoInfo', 'QAInfo') : Strings::showField('DemoInfo', 'clientInfo');
    $PublishCBInfo = Strings::showField('PublishCBInfo', 'QAInfo');
    $CBEthnicity = Strings::showField('CBEthnicity', 'QAInfo');
    $CBRace = Strings::showField('CBRace', 'QAInfo');
    $CBGender = Strings::showField('CBGender', 'QAInfo');
    $desiredCloseDate = Strings::showField('desiredClosingDate', 'QAInfo');

    $CBVeteran = Strings::showField('CBVeteran', 'QAInfo') != '' ? Strings::showField('CBVeteran', 'QAInfo') : Strings::showField('CBVeteran', 'clientInfo');
    $CBFiEthnicity = Strings::showField('CBFiEthnicity', 'QAInfo') != '' ? Strings::showField('CBFiEthnicity', 'QAInfo') : Strings::showField('CBFiEthnicity', 'clientInfo');
    $CBEthnicitySub = Strings::showField('CBEthnicitySub', 'QAInfo') != '' ? Strings::showField('CBEthnicitySub', 'QAInfo') : Strings::showField('CBEthnicitySub', 'clientInfo');
    $CBEthnicitySubOther = Strings::showField('CBEthnicitySubOther', 'QAInfo') != '' ? Strings::showField('CBEthnicitySubOther', 'QAInfo') : Strings::showField('CBEthnicitySubOther', 'clientInfo');
    $CBFiGender = Strings::showField('CBFiGender', 'QAInfo') != '' ? Strings::showField('CBFiGender', 'QAInfo') : Strings::showField('CBFiGender', 'clientInfo');
    $CBFiRace = Strings::showField('CBFiRace', 'QAInfo') != '' ? Strings::showField('CBFiRace', 'QAInfo') : Strings::showField('CBFiRace', 'clientInfo');
    $CBRaceSub = Strings::showField('CBRaceSub', 'QAInfo') != '' ? Strings::showField('CBRaceSub', 'QAInfo') : Strings::showField('CBRaceSub', 'clientInfo');
    $CBRaceAsianOther = Strings::showField('CBRaceAsianOther', 'QAInfo') != '' ? Strings::showField('CBRaceAsianOther', 'QAInfo') : Strings::showField('CBRaceAsianOther', 'clientInfo');
    $CBRacePacificOther = Strings::showField('CBRacePacificOther', 'QAInfo') != '' ? Strings::showField('CBRacePacificOther', 'QAInfo') : Strings::showField('CBRacePacificOther', 'clientInfo');
    $CBDDemoInfo = Strings::showField('CBDDemoInfo', 'QAInfo') != '' ? Strings::showField('CBDDemoInfo', 'QAInfo') : Strings::showField('CBDDemoInfo', 'clientInfo');
}

if ($methodOfContact != '') $methodContactArray = explode(',', $methodOfContact);
$borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'YMD', 'm/d/Y');
$coBorrowerDOB = Dates::formatDateWithRE($coBorrowerDOB, 'YMD', 'm/d/Y');

if (Dates::IsEmpty($desiredCloseDate)) {
    $desiredCloseDate = '';
} else {
    $desiredCloseDate = Dates::formatDateWithRE($desiredCloseDate, 'YMD', 'm/d/Y');
}

if ($isCoBorrower == '1') {
    $coBorDisp = 'display: block;';
} else {
    $coBorDisp = 'display: none;';
}
if ($borResidedPresentAddr == 'Yes') {
    $showOnBorFormerDiv = 'display: block;';
} else {
    $showOnBorFormerDiv = 'display: none;';
}
/*$phoneNumberArray = Strings::splitPhoneNumber($phoneNumber);
if(count($phoneNumberArray) > 0) {
    $phNo1	= trim($phoneNumberArray['No1']);
    $phNo2	= trim($phoneNumberArray['No2']);
    $phNo3	= trim($phoneNumberArray['No3']);
    $ext	= trim($phoneNumberArray['Ext']);
}*/
$fax1 = '';
$fax2 = '';
$fax3 = '';
$faxArray = Strings::splitPhoneNumber($fax);
if (count($faxArray) > 0) {
    $fax1 = trim($faxArray['No1']);
    $fax2 = trim($faxArray['No2']);
    $fax3 = trim($faxArray['No3']);
}
$HOPhone = '';
$HOFax = '';
$HOPhNoArray = [];
$HOPhone = Strings::showField('HOPhone', 'listingRealtorInfo');
$HOFax = Strings::showField('HOFax', 'listingRealtorInfo');
$HOPhNoArray = Strings::splitPhoneNumber($HOPhone);
$HOPhNo1 = '';
$HOPhNo2 = '';
$HOPhNo3 = '';
if (count($HOPhNoArray) > 0) {
    $HOPhNo1 = $HOPhNoArray['No1'];
    $HOPhNo2 = $HOPhNoArray['No2'];
    $HOPhNo3 = $HOPhNoArray['No3'];
    $HOPhExt = substr($HOPhone, 10, 5);
}
$HOFaxNo1 = '';
$HOFaxNo2 = '';
$HOFaxNo3 = '';
$HOFaxArray = Strings::splitPhoneNumber($HOFax);
if (count($HOFaxArray) > 0) {
    $HOFaxNo1 = $HOFaxArray['No1'];
    $HOFaxNo2 = $HOFaxArray['No2'];
    $HOFaxNo3 = $HOFaxArray['No3'];
}
$coBFax1 = '';
$coBFax2 = '';
$coBFax3 = '';
$coBFaxArray = Strings::splitPhoneNumber($coBFax);
if (count($coBFaxArray) > 0) {
    $coBFax1 = trim($coBFaxArray['No1']);
    $coBFax2 = trim($coBFaxArray['No2']);
    $coBFax3 = trim($coBFaxArray['No3']);
}
/*$coBSsn1 = '';	$coBSsn2 = '';	$coBSsn3 = '';
$coBSsnNumberArray = splitSSNNumber($coBSsnNumber);
if(count($coBSsnNumberArray) > 0) {
    $coBSsn1	= trim($coBSsnNumberArray['No1']);
    $coBSsn2	= trim($coBSsnNumberArray['No2']);
    $coBSsn3	= trim($coBSsnNumberArray['No3']);
}*/
if (Dates::IsEmpty($marriageDate)) {
    $marriageDate = '';
} else {
    $marriageDate = Dates::formatDateWithRE($marriageDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($divorceDate)) {
    $divorceDate = '';
} else {
    $divorceDate = Dates::formatDateWithRE($divorceDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($purchaseCloseDate)) {
    $purchaseCloseDate = '';
} else {
    $purchaseCloseDate = Dates::formatDateWithRE($purchaseCloseDate, 'YMD', 'm/d/Y');
}


/** Borrower Info End **/
if (count($fileHMLOInfo) > 0) {
    $REBroker = Strings::showField('REBroker', 'fileHMLOInfo');
    $borCreditScoreRange = Strings::showField('borCreditScoreRange', 'fileHMLOInfo');
    $midFicoScore = Strings::showField('midFicoScore', 'fileHMLOInfo');
    $coBorCreditScoreRange = Strings::showField('coBorCreditScoreRange', 'fileHMLOInfo');
    $borExperianScore = Strings::showField('borExperianScore', 'fileHMLOInfo');
    $borEquifaxScore = Strings::showField('borEquifaxScore', 'fileHMLOInfo');
    $borTransunionScore = Strings::showField('borTransunionScore', 'fileHMLOInfo');
    $coBorExperianScore = Strings::showField('coBorExperianScore', 'fileHMLOInfo');
    $coBorEquifaxScore = Strings::showField('coBorEquifaxScore', 'fileHMLOInfo');
    $midFicoScoreCoBor = Strings::showField('midFicoScoreCoBor', 'fileHMLOInfo');
    $coBorTransunionScore = Strings::showField('coBorTransunionScore', 'fileHMLOInfo');
    $borrowerCitizenship = Strings::showField('borrowerCitizenship', 'fileHMLOInfo');
    $coBorrowerCitizenship = Strings::showField('coBorrowerCitizenship', 'fileHMLOInfo');
    $isServicingMember = Strings::showField('isServicingMember', 'fileHMLOInfo');
    $servicingMemberInfo = Strings::showField('servicingMemberInfo', 'fileHMLOInfo');
    $agesOfDependent = Strings::showField('agesOfDependent', 'fileHMLOInfo');
    $numberOfDependents = Strings::showField('numberOfDependents', 'fileHMLOInfo');
    $defaultDrawFee = Strings::showField('defaultDrawFee', 'fileHMLOInfo');
    $drawFundCalOnDrawsFee = Strings::showField('drawFundCalOnDrawsFee', 'fileHMLOInfo');
}
/** Borrower Background Info Start **/
$isBorUSCitizen = Strings::showField('isBorUSCitizen', 'fileHMLOBackGroundInfo');
if ($borrowerCitizenship == 'U.S. Citizen') {
    $isBorUSCitizen = 'Yes';
} else if ($borrowerCitizenship != '') {
    $isBorUSCitizen = 'No';
}
$isBorDecalredBankruptPastYears = Strings::showField('isBorDecalredBankruptPastYears', 'fileHMLOBackGroundInfo');
$isAnyBorOutstandingJudgements = Strings::showField('isAnyBorOutstandingJudgements', 'fileHMLOBackGroundInfo');
$hasBorAnyActiveLawsuits = Strings::showField('hasBorAnyActiveLawsuits', 'fileHMLOBackGroundInfo');
$hasBorPropertyTaxLiens = Strings::showField('hasBorPropertyTaxLiens', 'fileHMLOBackGroundInfo');
$hasBorObligatedInForeclosure = Strings::showField('hasBorObligatedInForeclosure', 'fileHMLOBackGroundInfo');
$isBorPresenltyDelinquent = Strings::showField('isBorPresenltyDelinquent', 'fileHMLOBackGroundInfo');
$haveBorOtherFraudRelatedCrimes = Strings::showField('haveBorOtherFraudRelatedCrimes', 'fileHMLOBackGroundInfo');

$borDecalredBankruptExpln = Strings::showField('borDecalredBankruptExpln', 'fileHMLOBackGroundInfo');
$personalBankruptcy = Strings::showField('personalBankruptcy', 'fileHMLOBackGroundInfo');
$statusForeclosure = Strings::showField('statusForeclosure', 'fileHMLOBackGroundInfo');
$borOutstandingJudgementsExpln = Strings::showField('borOutstandingJudgementsExpln', 'fileHMLOBackGroundInfo');
$borActiveLawsuitsExpln = Strings::showField('borActiveLawsuitsExpln', 'fileHMLOBackGroundInfo');
$borPropertyTaxLiensExpln = Strings::showField('borPropertyTaxLiensExpln', 'fileHMLOBackGroundInfo');
$borObligatedInForeclosureExpln = Strings::showField('borObligatedInForeclosureExpln', 'fileHMLOBackGroundInfo');
$borDelinquentExpln = Strings::showField('borDelinquentExpln', 'fileHMLOBackGroundInfo');
$borOtherFraudRelatedCrimesExpln = Strings::showField('borOtherFraudRelatedCrimesExpln', 'fileHMLOBackGroundInfo');
$borBackgroundExplanation = Strings::showField('borBackgroundExplanation', 'fileHMLOBackGroundInfo');
$borDesignatedBeneficiaryAgreement = Strings::showField('borDesignatedBeneficiaryAgreement', 'fileHMLOBackGroundInfo');
$borDesignatedBeneficiaryAgreementExpln = Strings::showField('borDesignatedBeneficiaryAgreementExpln', 'fileHMLOBackGroundInfo');

$isCoBorUSCitizen = Strings::showField('isCoBorUSCitizen', 'fileHMLOBackGroundInfo');
if ($coBorrowerCitizenship == 'U.S. Citizen') {
    $isCoBorUSCitizen = 'Yes';
} elseif ($coBorrowerCitizenship != '') {
    $isCoBorUSCitizen = 'No';
}
$isCoBorDecalredBankruptPastYears = Strings::showField('isCoBorDecalredBankruptPastYears', 'fileHMLOBackGroundInfo');
$isAnyCoBorOutstandingJudgements = Strings::showField('isAnyCoBorOutstandingJudgements', 'fileHMLOBackGroundInfo');
$hasCoBorAnyActiveLawsuits = Strings::showField('hasCoBorAnyActiveLawsuits', 'fileHMLOBackGroundInfo');
$hasCoBorPropertyTaxLiens = Strings::showField('hasCoBorPropertyTaxLiens', 'fileHMLOBackGroundInfo');
$hasCoBorObligatedInForeclosure = Strings::showField('hasCoBorObligatedInForeclosure', 'fileHMLOBackGroundInfo');
$isCoBorPresenltyDelinquent = Strings::showField('isCoBorPresenltyDelinquent', 'fileHMLOBackGroundInfo');
$haveCoBorOtherFraudRelatedCrimes = Strings::showField('haveCoBorOtherFraudRelatedCrimes', 'fileHMLOBackGroundInfo');

$coBorDecalredBankruptExpln = Strings::showField('coBorDecalredBankruptExpln', 'fileHMLOBackGroundInfo');
$coBorOutstandingJudgementsExpln = Strings::showField('coBorOutstandingJudgementsExpln', 'fileHMLOBackGroundInfo');
$coBorActiveLawsuitsExpln = Strings::showField('coBorActiveLawsuitsExpln', 'fileHMLOBackGroundInfo');
$coBorPropertyTaxLiensExpln = Strings::showField('coBorPropertyTaxLiensExpln', 'fileHMLOBackGroundInfo');
$coBorObligatedInForeclosureExpln = Strings::showField('coBorObligatedInForeclosureExpln', 'fileHMLOBackGroundInfo');
$coBorDelinquentExpln = Strings::showField('coBorDelinquentExpln', 'fileHMLOBackGroundInfo');
$coBorOtherFraudRelatedCrimesExpln = Strings::showField('coBorOtherFraudRelatedCrimesExpln', 'fileHMLOBackGroundInfo');
$coBorBackgroundExplanation = Strings::showField('coBorBackgroundExplanation', 'fileHMLOBackGroundInfo');

$coBorDesignatedBeneficiaryAgreement = Strings::showField('coBorDesignatedBeneficiaryAgreement', 'fileHMLOBackGroundInfo');
$coBorDesignatedBeneficiaryAgreementExpln = Strings::showField('coBorDesignatedBeneficiaryAgreementExpln', 'fileHMLOBackGroundInfo');
$isCoBorIntendToOccupyPropAsPRI = Strings::showField('isCoBorIntendToOccupyPropAsPRI', 'fileHMLOBackGroundInfo');
$marriedToBor = Strings::showField('marriedToBor', 'fileHMLOBackGroundInfo');

$isBorBorrowedDownPayment = Strings::showField('isBorBorrowedDownPayment', 'fileHMLOBackGroundInfo');
$isBorIntendToOccupyPropAsPRI = Strings::showField('isBorIntendToOccupyPropAsPRI', 'fileHMLOBackGroundInfo');
$isBorPersonallyGuaranteeLoan = Strings::showField('isBorPersonallyGuaranteeLoan', 'fileHMLOBackGroundInfo');
$borBorrowedDownPaymentExpln = Strings::showField('borBorrowedDownPaymentExpln', 'fileHMLOBackGroundInfo');
$borOrigin = Strings::showField('borOrigin', 'fileHMLOBackGroundInfo');
$borVisaStatus = Strings::showField('borVisaStatus', 'fileHMLOBackGroundInfo');

if ($isBorDecalredBankruptPastYears == 'Yes') {
    $borDecalredBankruptDispOpt = 'display: table-row;';
}
if ($isAnyBorOutstandingJudgements == 'Yes') {
    $borOutstandingJudgementsDispOpt = 'display: table-row;';
}
if ($hasBorAnyActiveLawsuits == 'Yes') {
    $borActiveLawsuitsDispOpt = 'display: table-row;';
}
if ($hasBorPropertyTaxLiens == 'Yes') {
    $borPropertyTaxLiensDispOpt = 'display: table-row;';
}
if ($hasBorObligatedInForeclosure == 'Yes') {
    $borObligatedInForeclosureDispOpt = 'display: table-row;';
}
if ($isBorPresenltyDelinquent == 'Yes') {
    $borDelinquentDispOpt = 'display: table-row;';
}
if ($haveBorOtherFraudRelatedCrimes == 'Yes') {
    $borOtherFraudRelatedCrimesDispOpt = 'display: table-row;';
}

if ($isCoBorDecalredBankruptPastYears == 'Yes') {
    $coBorDecalredBankruptDispOpt = 'display: table-row;';
}
if ($isAnyCoBorOutstandingJudgements == 'Yes') {
    $coBorOutstandingJudgementsDispOpt = 'display: table-row;';
}
if ($hasCoBorAnyActiveLawsuits == 'Yes') {
    $coBorActiveLawsuitsDispOpt = 'display: table-row;';
}
if ($hasCoBorPropertyTaxLiens == 'Yes') {
    $coBorPropertyTaxLiensDispOpt = 'display: table-row;';
}
if ($hasCoBorObligatedInForeclosure == 'Yes') {
    $coBorObligatedInForeclosureDispOpt = 'display: table-row;';
}
if ($isCoBorPresenltyDelinquent == 'Yes') {
    $coBorDelinquentDispOpt = 'display: table-row;';
}
if ($haveCoBorOtherFraudRelatedCrimes == 'Yes') {
    $coBorOtherFraudRelatedCrimesDispOpt = 'display: table-row;';
}
if ($borResidedPresentAddr == 'Yes') {
    $LOBorResidedDispOpt = 'display: block;';
}
if ($REBroker != '') {
    $loanInfoLPSectionDisp = 'display: block;';
}

if ($clientType != '' && $publicUser == 1) {
    $HMLOLoanInfoSectionsDisp = 'display: block;';
}
$isBlanketLoanDispOpt = 'display: none;';
if ($isBlanketLoan == 'Yes') {
    $isBlanketLoanDispOpt = 'display: block;';
}
/** Borrower Background Info End **/

/** Borrower Experience Info Start **/
$haveBorREInvestmentExperience = Strings::showField('haveBorREInvestmentExperience', 'fileHMLOExperienceInfo');
$haveBorRehabConstructionExperience = Strings::showField('haveBorRehabConstructionExperience', 'fileHMLOExperienceInfo');
$haveBorProjectCurrentlyInProgress = Strings::showField('haveBorProjectCurrentlyInProgress', 'fileHMLOExperienceInfo');
$haveBorOwnInvestmentProperties = Strings::showField('haveBorOwnInvestmentProperties', 'fileHMLOExperienceInfo');
$areBorMemberOfInvestmentClub = Strings::showField('areBorMemberOfInvestmentClub', 'fileHMLOExperienceInfo');

$borNoOfREPropertiesCompleted = Strings::showField('borNoOfREPropertiesCompleted', 'fileHMLOExperienceInfo');
$borNoOfFlippingExperience = Strings::showField('borNoOfFlippingExperience', 'fileHMLOExperienceInfo');
$coBorNoOfFlippingExperience = Strings::showField('coBorNoOfFlippingExperience', 'fileHMLOExperienceInfo');
$borNoOfYearRehabExperience = Strings::showField('borNoOfYearRehabExperience', 'fileHMLOExperienceInfo');

$borRehabPropCompleted = Strings::showField('borRehabPropCompleted', 'fileHMLOExperienceInfo');
$borNoOfProjectCurrently = Strings::showField('borNoOfProjectCurrently', 'fileHMLOExperienceInfo');
$borNoOfOwnProp = Strings::showField('borNoOfOwnProp', 'fileHMLOExperienceInfo');
$borClubName = Strings::showField('borClubName', 'fileHMLOExperienceInfo');
$liquidAssets = Strings::showField('liquidAssets', 'fileHMLOExperienceInfo');
$areBuilderDeveloper = Strings::showField('areBuilderDeveloper', 'fileHMLOExperienceInfo');
$doYouHireGC = Strings::showField('doYouHireGC', 'fileHMLOExperienceInfo');

$haveCoBorREInvestmentExperience = Strings::showField('haveCoBorREInvestmentExperience', 'fileHMLOExperienceInfo');
$haveCoBorRehabConstructionExperience = Strings::showField('haveCoBorRehabConstructionExperience', 'fileHMLOExperienceInfo');
$haveCoBorProjectCurrentlyInProgress = Strings::showField('haveCoBorProjectCurrentlyInProgress', 'fileHMLOExperienceInfo');
$haveCoBorOwnInvestmentProperties = Strings::showField('haveCoBorOwnInvestmentProperties', 'fileHMLOExperienceInfo');
$areCoBorMemberOfInvestmentClub = Strings::showField('areCoBorMemberOfInvestmentClub', 'fileHMLOExperienceInfo');
$haveCoBorProfLicences = Strings::showField('haveCoBorProfLicences', 'fileHMLOExperienceInfo');
$coBorProfLicence = Strings::showField('coBorProfLicence', 'fileHMLOExperienceInfo');
$coBorLicenseNo = Strings::showField('coBorLicenseNo', 'fileHMLOExperienceInfo');

$coBorNoOfREPropertiesCompleted = Strings::showField('coBorNoOfREPropertiesCompleted', 'fileHMLOExperienceInfo');
$coBorNoOfYearRehabExperience = Strings::showField('coBorNoOfYearRehabExperience', 'fileHMLOExperienceInfo');
$coBorRehabPropCompleted = Strings::showField('coBorRehabPropCompleted', 'fileHMLOExperienceInfo');
$coBorNoOfProjectCurrently = Strings::showField('coBorNoOfProjectCurrently', 'fileHMLOExperienceInfo');
$coBorNoOfOwnProp = Strings::showField('coBorNoOfOwnProp', 'fileHMLOExperienceInfo');
$coBorClubName = Strings::showField('coBorClubName', 'fileHMLOExperienceInfo');

$coBorREAddress1 = Strings::showField('coBorREAddress1', 'fileHMLOExperienceInfo');
$coBorREAddress2 = Strings::showField('coBorREAddress2', 'fileHMLOExperienceInfo');
$coBorREAddress3 = Strings::showField('coBorREAddress3', 'fileHMLOExperienceInfo');
$coBorOutcomeRE1 = Strings::showField('coBorOutcomeRE1', 'fileHMLOExperienceInfo');
$coBorOutcomeRE2 = Strings::showField('coBorOutcomeRE2', 'fileHMLOExperienceInfo');
$coBorOutcomeRE3 = Strings::showField('coBorOutcomeRE3', 'fileHMLOExperienceInfo');

$coBorRCAddress1 = Strings::showField('coBorRCAddress1', 'fileHMLOExperienceInfo');
$coBorRCAddress2 = Strings::showField('coBorRCAddress2', 'fileHMLOExperienceInfo');
$coBorRCAddress3 = Strings::showField('coBorRCAddress3', 'fileHMLOExperienceInfo');
$coBorRCOutcome1 = Strings::showField('coBorRCOutcome1', 'fileHMLOExperienceInfo');
$coBorRCOutcome2 = Strings::showField('coBorRCOutcome2', 'fileHMLOExperienceInfo');
$coBorRCOutcome3 = Strings::showField('coBorRCOutcome3', 'fileHMLOExperienceInfo');
$borNoOfSquareFeet = Strings::showField('borNoOfSquareFeet', 'fileHMLONewLoanInfo');
$haveBorSquareFootage = Strings::showField('haveBorSquareFootage', 'fileHMLONewLoanInfo');
$prepaidInterestReserve = Strings::showField('prepaidInterestReserve', 'fileHMLONewLoanInfo');
$noOfMonthsPrepaid = Strings::showField('noOfMonthsPrepaid', 'fileHMLONewLoanInfo');
$haveInterestreserve = Strings::showField('haveInterestreserve', 'fileHMLONewLoanInfo');
$haveBorProfLicences = Strings::showField('haveBorProfLicences', 'fileHMLOExperienceInfo');
$borProfLicence = Strings::showField('borProfLicence', 'fileHMLOExperienceInfo');
$fullTimeRealEstateInvestor = Strings::showField('fullTimeRealEstateInvestor', 'fileHMLOExperienceInfo');
$coFullTimeRealEstateInvestor = Strings::showField('coFullTimeRealEstateInvestor', 'fileHMLOExperienceInfo');
$coBorliquidReserves = Strings::showField('coBorliquidReserves', 'fileHMLOExperienceInfo');
$ownedFreeAndClear = Strings::showField('ownedFreeAndClear', 'fileHMLONewLoanInfo');
$ownedSameEntity = Strings::showField('ownedSameEntity', 'fileHMLONewLoanInfo');
$isLoanPaymentAmt = Strings::showField('isLoanPaymentAmt', 'fileHMLONewLoanInfo');
$refinanceCurrentLoanBalance = Strings::showField('refinanceCurrentLoanBalance', 'fileHMLONewLoanInfo');
$approvedAcquisition = Strings::showField('approvedAcquisition', 'fileHMLONewLoanInfo');

$haveBorSellPropertie = Strings::showField('haveBorSellPropertie', 'fileHMLOExperienceInfo');
$borNoOfProSellExperience = Strings::showField('borNoOfProSellExperience', 'fileHMLOExperienceInfo');
$borNoOfProSellCompleted = Strings::showField('borNoOfProSellCompleted', 'fileHMLOExperienceInfo');

$haveCoBorSellPropertie = Strings::showField('haveCoBorSellPropertie', 'fileHMLOExperienceInfo');
$coBorNoOfProSellExperience = Strings::showField('coBorNoOfProSellExperience', 'fileHMLOExperienceInfo');
$coBorNoOfProSellCompleted = Strings::showField('coBorNoOfProSellCompleted', 'fileHMLOExperienceInfo');

$coBorSellAddress1 = Strings::showField('coBorSellAddress1', 'fileHMLOExperienceInfo');
$coBorSellAddress2 = Strings::showField('coBorSellAddress2', 'fileHMLOExperienceInfo');
$coBorSellAddress3 = Strings::showField('coBorSellAddress3', 'fileHMLOExperienceInfo');

$coBorSellOutcome1 = Strings::showField('coBorSellOutcome1', 'fileHMLOExperienceInfo');
$coBorSellOutcome2 = Strings::showField('coBorSellOutcome2', 'fileHMLOExperienceInfo');
$coBorSellOutcome3 = Strings::showField('coBorSellOutcome3', 'fileHMLOExperienceInfo');

$amountOfFinancing = Strings::showField('amountOfFinancing', 'fileHMLOExperienceInfo');
$amountOfFinancingTo = Strings::showField('amountOfFinancingTo', 'fileHMLOExperienceInfo');
$typicalPurchasePrice = Strings::showField('typicalPurchasePrice', 'fileHMLOExperienceInfo');
$typicalPurchasePriceTo = Strings::showField('typicalPurchasePriceTo', 'fileHMLOExperienceInfo');
$typicalConstructionCosts = Strings::showField('typicalConstructionCosts', 'fileHMLOExperienceInfo');
$typicalConstructionCostsTo = Strings::showField('typicalConstructionCostsTo', 'fileHMLOExperienceInfo');
$typicalSalePrice = Strings::showField('typicalSalePrice', 'fileHMLOExperienceInfo');
$typicalSalePriceTo = Strings::showField('typicalSalePriceTo', 'fileHMLOExperienceInfo');

$constructionDrawsPerProject = Strings::showField('constructionDrawsPerProject', 'fileHMLOExperienceInfo');
$constructionDrawsPerProjectTo = Strings::showField('constructionDrawsPerProjectTo', 'fileHMLOExperienceInfo');
$monthsPurchaseDateToFirstConst = Strings::showField('monthsPurchaseDateToFirstConst', 'fileHMLOExperienceInfo');
$monthsPurchaseDateToFirstConstTo = Strings::showField('monthsPurchaseDateToFirstConstTo', 'fileHMLOExperienceInfo');
$monthsPurchaseDateUntilConst = Strings::showField('monthsPurchaseDateUntilConst', 'fileHMLOExperienceInfo');
$monthsPurchaseDateUntilConstTo = Strings::showField('monthsPurchaseDateUntilConstTo', 'fileHMLOExperienceInfo');
$monthsPurchaseDateToSaleDate = Strings::showField('monthsPurchaseDateToSaleDate', 'fileHMLOExperienceInfo');
$monthsPurchaseDateToSaleDateTo = Strings::showField('monthsPurchaseDateToSaleDateTo', 'fileHMLOExperienceInfo');
$NoOfSuchProjects = Strings::showField('NoOfSuchProjects', 'fileHMLOExperienceInfo');
$NoOfSuchProjectsTo = Strings::showField('NoOfSuchProjectsTo', 'fileHMLOExperienceInfo');
$borPrimaryInvestmentStrategy = Strings::showField('borPrimaryInvestmentStrategy', 'fileHMLOExperienceInfo');
$borPrimaryInvestmentStrategyExplain = Strings::showField('borPrimaryInvestmentStrategyExplain', 'fileHMLOExperienceInfo');

$coBorPrimaryInvestmentStrategy = Strings::showField('coBorPrimaryInvestmentStrategy', 'fileHMLOExperienceInfo');
$coBorPrimaryInvestmentStrategyExplain = Strings::showField('coBorPrimaryInvestmentStrategyExplain', 'fileHMLOExperienceInfo');
$borLicenseNo = Strings::showField('borLicenseNo', 'fileHMLOExperienceInfo');
$flipPropCompletedLifetime = Strings::showField('flipPropCompletedLifetime', 'fileHMLOExperienceInfo');
$groundPropCompletedLifetime = Strings::showField('groundPropCompletedLifetime', 'fileHMLOExperienceInfo');
$sellPropCompletedLifetime = Strings::showField('sellPropCompletedLifetime', 'fileHMLOExperienceInfo');
$overallRealEstateInvesExp = Strings::showField('overallRealEstateInvesExp', 'fileHMLOExperienceInfo');

$ownPropertyFreeAndClear = Strings::showField('ownPropertyFreeAndClear', 'fileHMLONewLoanInfo');
$lien1MaturityDate = Dates::formatDateWithRE(Strings::showField('lien1MaturityDate', 'fileHMLONewLoanInfo'), 'YMD', 'm/d/Y');
$lien2MaturityDate = Dates::formatDateWithRE(Strings::showField('lien2MaturityDate', 'fileHMLONewLoanInfo'), 'YMD', 'm/d/Y');
$lean1CurrentDefault = Strings::showField('lean1CurrentDefault', 'fileHMLONewLoanInfo');
$lean2CurrentDefault = Strings::showField('lean2CurrentDefault', 'fileHMLONewLoanInfo');
$desiredLoanAmount = Strings::showField('desiredLoanAmount', 'fileHMLONewLoanInfo');
$earnestDeposit = Strings::showField('earnestDeposit', 'fileHMLONewLoanInfo');
$otherDownPayment = Strings::showField('otherDownPayment', 'fileHMLONewLoanInfo');

/* Multi Select Value */
if ($borPrimaryInvestmentStrategy != '') $borPriInvesStrategyArray = explode(',', $borPrimaryInvestmentStrategy);
if ($coBorPrimaryInvestmentStrategy != '') $coBorPriInvesStrategyArray = explode(',', $coBorPrimaryInvestmentStrategy);
if (Strings::showField('geographicAreas', 'fileHMLOExperienceInfo') != '') $geographicAreas = explode(',', Strings::showField('geographicAreas', 'fileHMLOExperienceInfo'));

if (in_array('Other', $borPriInvesStrategyArray)) {
    $borPriInvesStrategyDispOpt = 'display:block;';
}
if (in_array('Other', $coBorPriInvesStrategyArray)) {
    $coBorPriInvesStrategyDispOpt = 'display:block;';
}
if ($haveBorREInvestmentExperience == 'Yes') {
    $showREInvestmentDispOpt = 'display:block;';
}
if ($haveBorSellPropertie == 'Yes') {
    $haveBorSellPropertieDisp = 'display:block;';
}
if ($haveBorRehabConstructionExperience == 'Yes') {
    $showRCDispOpt = 'display:block;';
}
if ($haveBorProjectCurrentlyInProgress == 'Yes') {
    $showProjectInProgressDispOpt = 'display:block;';
}
if ($haveBorSquareFootage == 'Yes') {
    $showBorSquareFootageDispOpt = 'display:block;';
}
if ($haveBorOwnInvestmentProperties == 'Yes') {
    $showOwnInvestmentDispOpt = 'display:block;';
}
if ($haveBorOwnInvestmentProperties == 'Yes' || $haveBorOwnInvestmentProperties == '' || $haveBorOwnInvestmentProperties == 'NA' || $haveBorOwnInvestmentProperties == 0) {
    $showSOREDispOpt = 'display:block;';
}
if ($areBorMemberOfInvestmentClub == 'Yes') {
    $showMemberDispOpt = 'display:block;';
}
if ($haveBorProfLicences == 'Yes') {
    $showProfLicencesOpt = 'display:block;';
}

if ($haveCoBorREInvestmentExperience == 'Yes') {
    $showCoBorREInvestmentDispOpt = 'display:block;';
}
if ($haveCoBorRehabConstructionExperience == 'Yes') {
    $showcoBorRCDispOpt = 'display:block;';
}
if ($haveCoBorSellPropertie == 'Yes') {
    $showcoBorSellDispOpt = 'display:block;';
}
if ($haveCoBorProjectCurrentlyInProgress == 'Yes') {
    $showcoBorProjectInProgressDispOpt = 'display:block;';
}
if ($haveCoBorOwnInvestmentProperties == 'Yes') {
    $showcoBorOwnInvestmentDispOpt = 'display:block;';
}
if ($areCoBorMemberOfInvestmentClub == 'Yes') {
    $showcoBorMemberDispOpt = 'display:block;';
}
if ($haveCoBorProfLicences == 'Yes') {
    $showcoBorProfLicenceDispOpt = 'display:block;';
}
if ($haveInterestreserve == 'Yes') {
    // $haveInterestreserveDivDispOpt = 'display: block;';
}
if ($haveInterestreserve == '') {
    $haveInterestreserve = 'No';
}
if (Strings::showField('leadSource', 'ResponseInfo') == 'Other') {
    $refDivDisplay = 'display: block;';
}

/** Borrower Experience Info End **/
$assetCheckingAccounts = '';
$taxes1 = Strings::showField('taxes1', 'incomeInfo');
$taxes1LastPaid = Strings::showField('taxes1LastPaid', 'incomeInfo');
$taxes1LastAmount = Strings::showField('taxes1LastAmount', 'incomeInfo');

$lien1Payment = Strings::showField('lien1Payment', 'LMRInfo');
$isTaxesInsEscrowed = Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo');
$spcf_hoafees = Strings::showField('spcf_hoafees', 'fileHMLONewLoanInfo');

$assetsInfo = $myFileInfo['AssetsInfo'] ?? null;
$FilePropInfo = $myFileInfo['FilePropInfo'] ?? null;
$fileLOAssetsInfo = $myFileInfo['fileLOAssetsInfo'] ?? null;
$glMultiPropertyCountry = $myFileInfo['multiPropertyCountry'] ?? null;

require 'incExpCalculation.php';
/** Income and Exp calculation Page **/

$legalDescription = urldecode(Property::$primaryPropertyInfo->propertyLegalDescription);
$district = Property::$primaryPropertyInfo->propertyDistrict;
$section = Property::$primaryPropertyInfo->propertySection;
$block = Property::$primaryPropertyInfo->propertyBlock;
$lot = Property::$primaryPropertyInfo->propertyLot;
$parcelNo = Property::$primaryPropertyInfo->propertyParcelNumber;
$municipality = Property::$primaryPropertyInfo->propertyMunicipality;


$LBInfo = Property::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessLockBoxInfo;
$HMLOmlsnumber = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyMLSNumber;
$presentOccupancy = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy;
$LBContactName = Property::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessName;
$propConstructionType = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyConstructionType;
$propConstructionMethod = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyConstructionMethod;
$LBContactPhone = Property::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessPhone;
$LBContactEmail = Property::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessEmail;
$obtainInteriorAccess = Property::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessObtainInteriorAccess;
$yearBuilt = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyYearBuilt;
$occupancyNotes = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyOccupancyNotes;
$rehabToComplete = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyRehabToComplete;
$addRentableSqFt = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyRentableSqFt;
$noOfParking = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfParkings;
$currentCommercialTenantOccupancyRate = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyCommercialTenantOccupancyRate;

$anchorTenant = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyIsAnchorTenant;
$anchorTenantName = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyAnchorTenantName;
$cre = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyCRE;
$resi = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyRESI;
$propertyClass = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyClass;
$addNoOfStories = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfStories;
$leaseType = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyLeaseType;
$yearRenovated = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyRenovatedYear;
$pastDuePropertyTaxes = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPastDuePropertyTaxes;
$propertySqFt = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertySqFt;
$adjustedSqFt = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyAdjustedSqFt;

$basementHome = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyIsHomeHaveBasement;
$yearPurchased = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyYearPurchased;
$howManyBedRoom = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfBedRooms;
$howManyBathRoom = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfBathRooms;
$howManyHalfBathRoom = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfHalfBathRooms;

$noOfBuildings = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfBuildings;
$ownerOccupancyPercentage = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyOwnerOccupancy;

$propertyTaxDueDate = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyTaxDueDate;


if (count($FilePropInfo ?? []) > 0) {
    $isHouseProperty = Strings::showField('isHouseProperty', 'FilePropInfo');
}


/** Entity Info Section **/
$entityName = Strings::showField('entityName', 'fileHMLOEntityInfo');
$entityType = Strings::showField('entityType', 'fileHMLOEntityInfo');
$borrowerType = Strings::showField('borrowerType', 'fileHMLOEntityInfo');
$trustType = Strings::showField('trustType', 'fileHMLOEntityInfo');
$retirementEntity = Strings::showField('retirementEntity', 'fileHMLOEntityInfo');
$businessCategory = Strings::showField('businessCategory', 'fileHMLOEntityInfo');
$businessType = Strings::showField('businessType', 'fileHMLOEntityInfo');
$productTypeOrServiceSold = Strings::showField('productTypeOrServiceSold', 'fileHMLOEntityInfo');
$terminalOrMakeModel = Strings::showField('terminalOrMakeModel', 'fileHMLOEntityInfo');
$businessPhone = Strings::showField('businessPhone', 'fileHMLOEntityInfo');
$startDateAtLocation = Strings::showField('startDateAtLocation', 'fileHMLOEntityInfo');
$entityPropertyOwnerShip = Strings::showField('entityPropertyOwnerShip', 'fileHMLOEntityInfo');
$valueOfProperty = Strings::showField('valueOfProperty', 'fileHMLOEntityInfo');
$totalDebtOnProperty = Strings::showField('totalDebtOnProperty', 'fileHMLOEntityInfo');
$nameOfLenders = Strings::showField('nameOfLenders', 'fileHMLOEntityInfo');
$landlordMortagageContactName = Strings::showField('landlordMortagageContactName', 'fileHMLOEntityInfo');
$landlordMortagagePhone = Strings::showField('landlordMortagagePhone', 'fileHMLOEntityInfo');
$rentMortagagePayment = Strings::showField('rentMortagagePayment', 'fileHMLOEntityInfo');
$avgMonthlyCreditcardSale = Strings::showField('avgMonthlyCreditcardSale', 'fileHMLOEntityInfo');
$avgTotalMonthlySale = Strings::showField('avgTotalMonthlySale', 'fileHMLOEntityInfo');
$annualGrossSales = Strings::showField('annualGrossSales', 'fileHMLOEntityInfo');
$annualGrossProfit = Strings::showField('annualGrossProfit', 'fileHMLOEntityInfo');
$ordinaryBusinessIncome = Strings::showField('ordinaryBusinessIncome', 'fileHMLOEntityInfo');
$ENINo = Strings::showField('ENINo', 'fileHMLOEntityInfo');
$naicsCode = Strings::showField('naicsCode', 'fileHMLOEntityInfo');
$entityAddress = Strings::showField('entityAddress', 'fileHMLOEntityInfo');
$entityCity = Strings::showField('entityCity', 'fileHMLOEntityInfo');
$entityState = Strings::showField('entityState', 'fileHMLOEntityInfo');
$entityZip = Strings::showField('entityZip', 'fileHMLOEntityInfo');
$entityLocation = Strings::showField('entityLocation', 'fileHMLOEntityInfo');
$entityStateOfFormation = Strings::showField('entityStateOfFormation', 'fileHMLOEntityInfo');
$statesRegisterdIn = Strings::showField('statesRegisterdIn', 'fileHMLOEntityInfo');
$entityNotes = Strings::showField('entityNotes', 'fileHMLOEntityInfo');
$corporateSecretaryName = Strings::showField('corporateSecretaryName', 'fileHMLOEntityInfo');
$borrowerUnderEntity = Strings::showField('borrowerUnderEntity', 'fileHMLOEntityInfo') ? Strings::showField('borrowerUnderEntity', 'fileHMLOEntityInfo') : 'Yes'; ////default yes, as the dependency is removed.
$entityWebsite = Strings::showField('entityWebsite', 'fileHMLOEntityInfo');
$sameAsEntityAddr = Strings::showField('sameAsEntityAddr', 'fileHMLOEntityInfo');
$entityBillAddress = Strings::showField('entityBillAddress', 'fileHMLOEntityInfo');
$entityBillCity = Strings::showField('entityBillCity', 'fileHMLOEntityInfo');
$entityBillState = Strings::showField('entityBillState', 'fileHMLOEntityInfo');
$entityBillZip = Strings::showField('entityBillZip', 'fileHMLOEntityInfo');
$sameAsEntityAddr = Strings::showField('sameAsEntityAddr', 'fileHMLOEntityInfo');
$businessTypeEF = Strings::showField('businessTypeEF', 'fileHMLOEntityInfo');

$tradeName = Strings::showField('tradeName', 'fileHMLOEntityInfo');
$crossCorporateGuarantor = Strings::showField('crossCorporateGuarantor', 'fileHMLOEntityInfo');
$noOfEmployees = Strings::showField('noOfEmployees', 'fileHMLOEntityInfo');
$noOfEmployeesAfterLoan = Strings::showField('noOfEmployeesAfterLoan', 'fileHMLOEntityInfo');
$grossAnnualRevenues = Strings::showField('grossAnnualRevenues', 'fileHMLOEntityInfo');
$grossIncomeLastYear = Strings::showField('grossIncomeLastYear', 'fileHMLOEntityInfo');
$netIncomeLastYear = Strings::showField('netIncomeLastYear', 'fileHMLOEntityInfo');
$grossIncome2YearsAgo = Strings::showField('grossIncome2YearsAgo', 'fileHMLOEntityInfo');
$netIncome2YearsAgo = Strings::showField('netIncome2YearsAgo', 'fileHMLOEntityInfo');
$averageBankBalance = Strings::showField('averageBankBalance', 'fileHMLOEntityInfo');
//$entityBusinessSell = Strings::showField('entityBusinessSell', 'fileHMLOEntityInfo');
$entityService = Strings::showField('entityService', 'fileHMLOEntityInfo');
$entityProduct = Strings::showField('entityProduct', 'fileHMLOEntityInfo');
//$entityBusinessType = Strings::showField('entityBusinessType', 'fileHMLOEntityInfo');
$entityB2B = Strings::showField('entityB2B', 'fileHMLOEntityInfo');
$entityB2C = Strings::showField('entityB2C', 'fileHMLOEntityInfo');
$benBusinessHomeBased = Strings::showField('benBusinessHomeBased', 'fileHMLOEntityInfo');
$benCardProcessorBank = Strings::showField('benCardProcessorBank', 'fileHMLOEntityInfo');
$benCreditCardPayments = Strings::showField('benCreditCardPayments', 'fileHMLOEntityInfo');
$benChargeSalesTax = Strings::showField('benChargeSalesTax', 'fileHMLOEntityInfo');
$benEmployeesPaid = Strings::showField('benEmployeesPaid', 'fileHMLOEntityInfo');
$benBusinessLocation = Strings::showField('benBusinessLocation', 'fileHMLOEntityInfo');
$benHowManyLocation = Strings::showField('benHowManyLocation', 'fileHMLOEntityInfo');
$benOtherLocation = Strings::showField('benOtherLocation', 'fileHMLOEntityInfo');
$benBusinessFranchise = Strings::showField('benBusinessFranchise', 'fileHMLOEntityInfo');
$benNameOfFranchise = Strings::showField('benNameOfFranchise', 'fileHMLOEntityInfo');
$benPointOfContact = Strings::showField('benPointOfContact', 'fileHMLOEntityInfo');
$benPointOfContactPhone = Strings::showField('benPointOfContactPhone', 'fileHMLOEntityInfo');
$benPointOfContactEmail = Strings::showField('benPointOfContactEmail', 'fileHMLOEntityInfo');
$benWebsiteForFranchise = Strings::showField('benWebsiteForFranchise', 'fileHMLOEntityInfo');
$businessDescription = Strings::showField('businessDescription', 'fileHMLOEntityInfo');
$merchantProcessingBankName = Strings::showField('merchantProcessingBankName', 'fileHMLOEntityInfo');
$organizationalRef = Strings::showField('organizationalRef', 'fileHMLOEntityInfo');
$dateOfFormation = Strings::showField('dateOfFormation', 'fileHMLOEntityInfo');
$minTimeInBusiness = Strings::showField('minTimeInBusiness', 'fileHMLOEntityInfo');
$recentNSFs = Strings::showField('recentNSFs', 'fileHMLOEntityInfo');
$hasBusinessBankruptcy = Strings::showField('hasBusinessBankruptcy', 'fileHMLOEntityInfo');
$businessBankruptcy = Strings::showField('businessBankruptcy', 'fileHMLOEntityInfo');
$CBEID = Strings::showField('CBEID', 'fileHMLOEntityInfo');
$dateOfOperatingAgreement = Strings::showField('dateOfOperatingAgreement', 'fileHMLOEntityInfo');
$isBusinessSeasonal = Strings::showField('isBusinessSeasonal', 'fileHMLOEntityInfo');
$isBusinessSeasonalPeakMonth = Strings::showField('isBusinessSeasonalPeakMonth', 'fileHMLOEntityInfo');
$businessReference = Strings::showField('businessReference', 'fileHMLOEntityInfo');

if ($borrowerType == glBorrowerType::BORROWER_TYPE_ENTITY) {
    $showBorrowerEntityDispOpt = 'display: contents;';
}
/*story - 27792 populate the parent question for entity field*/
if ($PCID != glPCID::PCID_G1CommMort) {
    // $showBorrowerEntityDispOpt = 'display: contents;';
}

if ($borrowerUnderEntity == 'Yes' && $sameAsEntityAddr != 1) {
    $showBillEntityDispOpt = 'display: block';
} else {
    $showBillEntityDispOpt = 'display: none';
}

if (Dates::IsEmpty($dateOfFormation)) {
    $dateOfFormation = '';
} else {
    $dateOfFormation = Dates::formatDateWithRE($dateOfFormation, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($startDateAtLocation)) {
    $startDateAtLocation = '';
} else {
    $startDateAtLocation = Dates::formatDateWithRE($startDateAtLocation, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($dateOfOperatingAgreement)) {
    $dateOfOperatingAgreement = '';
} else {
    $dateOfOperatingAgreement = Dates::formatDateWithRE($dateOfOperatingAgreement, 'YMD', 'm/d/Y');
}

for ($j = 1; $j <= 10; $j++) {
    ${'member' . $j . 'Name'} = Strings::showField('member' . $j . 'Name', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Title'} = Strings::showField('member' . $j . 'Title', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Ownership'} = Strings::showField('member' . $j . 'Ownership', 'fileHMLOEntityInfo');
    ${'member' . $j . 'AnnualSalary'} = Strings::showField('member' . $j . 'AnnualSalary', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Address'} = Strings::showField('member' . $j . 'Address', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Phone'} = Strings::showField('member' . $j . 'Phone', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Cell'} = Strings::showField('member' . $j . 'Cell', 'fileHMLOEntityInfo');
    ${'member' . $j . 'SSN'} = Strings::showField('member' . $j . 'SSN', 'fileHMLOEntityInfo');
    ${'member' . $j . 'DOB'} = Strings::showField('member' . $j . 'DOB', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Email'} = Strings::showField('member' . $j . 'Email', 'fileHMLOEntityInfo');
    ${'member' . $j . 'CreditScore'} = Strings::showField('member' . $j . 'CreditScore', 'fileHMLOEntityInfo');

    if (${'member' . $j . 'Name'} != '' || ${'member' . $j . 'Title'} != '' || ${'member' . $j . 'Ownership'} > 0 || $j == 1) {
        ${'showMember' . $j . 'DispOpt'} = 'display : table-row';
    } else {
        ${'showMember' . $j . 'DispOpt'} = 'display:none';
    }
}
/** Loan Info Section **/
$additionalPropertyRestrictionsDispOpt = 'display: none;';

if (count($fileHMLONewLoanInfo) > 0) {

    $assumability = Strings::showField('assumability', 'fileHMLONewLoanInfo');
    $rehabCostFinanced = Strings::showField('rehabCostFinanced', 'fileHMLONewLoanInfo');
    $originationPointsRate = Strings::showField('originationPointsRate', 'fileHMLONewLoanInfo');
    $originationPointsValue = Strings::showField('originationPointsValue', 'fileHMLONewLoanInfo');
    $processingFee = Strings::showField('processingFee', 'fileHMLONewLoanInfo');
    $brokerPointsRate = Strings::showField('brokerPointsRate', 'fileHMLONewLoanInfo');
    $brokerPointsValue = Strings::showField('brokerPointsValue', 'fileHMLONewLoanInfo');
    $appraisalFee = Strings::showField('appraisalFee', 'fileHMLONewLoanInfo');
    $applicationFee = Strings::showField('applicationFee', 'fileHMLONewLoanInfo');
    $drawsSetUpFee = Strings::showField('drawsSetUpFee', 'fileHMLONewLoanInfo');
    $estdTitleClosingFee = Strings::showField('estdTitleClosingFee', 'fileHMLONewLoanInfo');
    $miscellaneousFee = Strings::showField('miscellaneousFee', 'fileHMLONewLoanInfo');
    $closingCostFinanced = Strings::showField('closingCostFinanced', 'fileHMLONewLoanInfo');
    $extensionOption = Strings::showField('extensionOption', 'fileHMLONewLoanInfo');
    $loanTermExpireDate = Strings::showField('loanTermExpireDate', 'fileHMLONewLoanInfo');
    $HMLOLender = Strings::showField('HMLOLender', 'fileHMLONewLoanInfo');
    $drawsFee = Strings::showField('drawsFee', 'fileHMLONewLoanInfo');
    $interestReserves = Strings::showField('interestReserves', 'fileHMLONewLoanInfo');
    $percentageOfBudget = Strings::showField('percentageOfBudget', 'fileHMLONewLoanInfo');
    $originalPurchaseDate = Strings::showField('originalPurchaseDate', 'fileHMLONewLoanInfo');
    $originalPurchasePrice = Strings::showField('originalPurchasePrice', 'fileHMLONewLoanInfo');
    $costOfImprovementsMade = Strings::showField('costOfImprovementsMade', 'fileHMLONewLoanInfo');
    $payOffMortgage1 = Strings::showField('payOffMortgage1', 'fileHMLONewLoanInfo');
    $payOffMortgage2 = Strings::showField('payOffMortgage2', 'fileHMLONewLoanInfo');
    $payOffOutstandingTaxes = Strings::showField('payOffOutstandingTaxes', 'fileHMLONewLoanInfo');
    $payOffOtherOutstandingAmounts = Strings::showField('payOffOtherOutstandingAmounts', 'fileHMLONewLoanInfo');
    $refinanceCurrentLender = Strings::showField('refinanceCurrentLender', 'fileHMLONewLoanInfo');
    $refinanceCurrentRate = Strings::showField('refinanceCurrentRate', 'fileHMLONewLoanInfo');
    $refinanceMonthlyPayment = Strings::showField('refinanceMonthlyPayment', 'fileHMLONewLoanInfo');
    $CashOut = Strings::showField('CashOut', 'fileHMLONewLoanInfo');
    $additionalPropertyRestrictions = Strings::showField('additionalPropertyRestrictions', 'fileHMLONewLoanInfo');
    $restrictionsExplain = Strings::showField('restrictionsExplain', 'fileHMLONewLoanInfo');
    $prePaymentPenaltyPercentage = Strings::showField('prePaymentPenaltyPercentage', 'fileHMLONewLoanInfo');
    $prePaymentSelectVal = Strings::showField('prePaymentSelectVal', 'fileHMLONewLoanInfo');
    $prePaymentSelectValArr = explode(',', $prePaymentSelectVal);
    $amortizationType = Strings::showField('amortizationType', 'fileHMLONewLoanInfo');
    $noOfPropertiesAcquiring = Strings::showField('noOfPropertiesAcquiring', 'fileHMLONewLoanInfo');
    $cashOutAmt = Strings::showField('cashOutAmt', 'fileHMLONewLoanInfo');
    $datesigned = Strings::showField('datesigned', 'fileHMLONewLoanInfo');
    $resalePrice = Strings::showField('resalePrice', 'fileHMLONewLoanInfo');
    $resaleClosingDate = Strings::showField('resaleClosingDate', 'fileHMLONewLoanInfo');
    $initialAdvance = Strings::showField('initialAdvance', 'fileHMLONewLoanInfo');
    $secondaryFinancingAmount = Strings::showField('secondaryFinancingAmount', 'fileHMLONewLoanInfo');
    $secondaryHolderName = Strings::showField('secondaryHolderName', 'fileHMLONewLoanInfo');

    $valuationBPOFee = Strings::showField('valuationBPOFee', 'fileHMLONewLoanInfo');
    $valuationCMAFee = Strings::showField('valuationCMAFee', 'fileHMLONewLoanInfo');
    $valuationAVEFee = Strings::showField('valuationAVEFee', 'fileHMLONewLoanInfo');
    $valuationAVMFee = Strings::showField('valuationAVMFee', 'fileHMLONewLoanInfo');
    $creditReportFee = Strings::showField('creditReportFee', 'fileHMLONewLoanInfo');
    $creditCheckFee = Strings::showField('creditCheckFee', 'fileHMLONewLoanInfo');
    $employmentVerificationFee = Strings::showField('employmentVerificationFee', 'fileHMLONewLoanInfo');
    $backgroundCheckFee = Strings::showField('backgroundCheckFee', 'fileHMLONewLoanInfo');
    $taxReturnOrderFee = Strings::showField('taxReturnOrderFee', 'fileHMLONewLoanInfo');
    $floodCertificateFee = Strings::showField('floodCertificateFee', 'fileHMLONewLoanInfo');
    $loanOriginationFee = Strings::showField('loanOriginationFee', 'fileHMLONewLoanInfo');
    $documentPreparationFee = Strings::showField('documentPreparationFee', 'fileHMLONewLoanInfo');
    $wireFee = Strings::showField('wireFee', 'fileHMLONewLoanInfo');
    $servicingSetUpFee = Strings::showField('servicingSetUpFee', 'fileHMLONewLoanInfo');
    $taxServiceFee = Strings::showField('taxServiceFee', 'fileHMLONewLoanInfo');
    $floodServiceFee = Strings::showField('floodServiceFee', 'fileHMLONewLoanInfo');
    $constructionHoldbackFee = Strings::showField('constructionHoldbackFee', 'fileHMLONewLoanInfo');
    $thirdPartyFees = Strings::showField('thirdPartyFees', 'fileHMLONewLoanInfo');
    $otherFee = Strings::showField('otherFee', 'fileHMLONewLoanInfo');
    $taxImpoundsMonth = Strings::showField('taxImpoundsMonth', 'fileHMLONewLoanInfo');
    $taxImpoundsMonthAmt = Strings::showField('taxImpoundsMonthAmt', 'fileHMLONewLoanInfo');
    $taxImpoundsFee = Strings::showField('taxImpoundsFee', 'fileHMLONewLoanInfo');
    $insImpoundsMonth = Strings::showField('insImpoundsMonth', 'fileHMLONewLoanInfo');
    $insImpoundsMonthAmt = Strings::showField('insImpoundsMonthAmt', 'fileHMLONewLoanInfo');
    $insImpoundsFee = Strings::showField('insImpoundsFee', 'fileHMLONewLoanInfo');
    $interestChargedFromDate = Strings::showField('interestChargedFromDate', 'fileHMLONewLoanInfo');
    $interestChargedEndDate = Strings::showField('interestChargedEndDate', 'fileHMLONewLoanInfo');
    $costOfImprovementsToBeMade = Strings::showField('costOfImprovementsToBeMade', 'fileHMLONewLoanInfo');
    $LOCTotalLoanAmt = Strings::showField('LOCTotalLoanAmt', 'fileHMLONewLoanInfo');
    $rehabCostPercentageFinanced = Strings::showField('rehabCostPercentageFinanced', 'fileHMLONewLoanInfo');
    $downPaymentPercentage = Strings::showField('downPaymentPercentage', 'fileHMLONewLoanInfo');
    $CORTotalLoanAmt = Strings::showField('CORTotalLoanAmt', 'fileHMLONewLoanInfo');
    $CORefiLTVPercentage = Strings::showField('CORefiLTVPercentage', 'fileHMLONewLoanInfo');
    $PAExpirationDate = Strings::showField('PAExpirationDate', 'fileHMLONewLoanInfo');
    $includeCCF = Strings::showField('includeCCF', 'fileHMLONewLoanInfo');
    $isOwnLand = Strings::showField('isOwnLand', 'fileHMLONewLoanInfo');
    $landValue = Strings::showField('landValue', 'fileHMLONewLoanInfo');
    $desiredLoanAmount = Strings::showField('desiredLoanAmount', 'fileHMLONewLoanInfo');
    $desiredInterestRateRangeFrom = Strings::showField('desiredInterestRateRangeFrom', 'fileHMLONewLoanInfo');
    $desiredInterestRateRangeTo = Strings::showField('desiredInterestRateRangeTo', 'fileHMLONewLoanInfo');

    $inspectionFees = Strings::showField('inspectionFees', 'fileHMLONewLoanInfo');
    $projectFeasibility = Strings::showField('projectFeasibility', 'fileHMLONewLoanInfo');
    $dueDiligence = Strings::showField('dueDiligence', 'fileHMLONewLoanInfo');
    $UccLienSearch = Strings::showField('UccLienSearch', 'fileHMLONewLoanInfo');
    $closingCostFinancingFee = Strings::showField('closingCostFinancingFee', 'fileHMLONewLoanInfo');
    $extensionOptionPercentage = Strings::showField('extensionOptionPercentage', 'fileHMLONewLoanInfo');
    $extensionRatePercentage = Strings::showField('extensionRatePercentage', 'fileHMLONewLoanInfo');

    $noUnitsOccupied = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits;
    $stabilizedRate = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyStabilizedRate;
    $calcInrBasedOnMonthlyPayment = Strings::showField('calcInrBasedOnMonthlyPayment', 'fileHMLONewLoanInfo');


    if (Dates::IsEmpty($originalPurchaseDate)) {
        $originalPurchaseDate = '';
    } else {
        $originalPurchaseDate = Dates::formatDateWithRE($originalPurchaseDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($datesigned)) {
        $datesigned = '';
    } else {
        $datesigned = Dates::formatDateWithRE($datesigned, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($resaleClosingDate)) {
        $resaleClosingDate = '';
    } else {
        $resaleClosingDate = Dates::formatDateWithRE($resaleClosingDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($loanTermExpireDate)) {
        $loanTermExpireDate = '';
    } else {
        $loanTermExpireDate = Dates::formatDateWithRE($loanTermExpireDate, 'YMD', 'm/d/Y');
    }
    if ($additionalPropertyRestrictions == 'Yes') {
        $additionalPropertyRestrictionsDispOpt = 'display: table-row;';
    }
    if (Dates::IsEmpty($PAExpirationDate)) {
        $PAExpirationDate = '';
    } else {
        $PAExpirationDate = Dates::formatDateWithRE($PAExpirationDate, 'YMD', 'm/d/Y');
    }
}

$borrowerCallBack = Strings::showField('borrowerCallBack', 'ResponseInfo');
$welcomeCallDate = Strings::showField('welcomeCallDate', 'ResponseInfo');
$projectName = Strings::showField('projectName', 'ResponseInfo');
$leadSource = Strings::showField('leadSource', 'ResponseInfo');

if (Dates::IsEmpty($borrowerCallBack)) {
    $borrowerCallBack = '';
} else {
    $borrowerCallBack = Dates::formatDateWithRE($borrowerCallBack, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($receivedDate)) {
    $receivedDate = '';
} else {
    $receivedDate = Dates::formatDateWithRE($receivedDate, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($welcomeCallDate)) {
    $welcomeCallDate = '';
} else {
    $welcomeCallDate = Dates::formatDateWithRE($welcomeCallDate, 'YMD', 'm/d/Y');
}
if (count($fileHMLOPropertyInfo) > 0) {

    $typeOfHMLOLoanRequesting = trim(Strings::showField('typeOfHMLOLoanRequesting', 'fileHMLOPropertyInfo'));
    $lienPosition = Strings::showField('lienPosition', 'fileHMLOPropertyInfo');
    $loanTerm = Strings::showField('loanTerm', 'fileHMLOPropertyInfo');
    $propertyNeedRehab = Strings::showField('propertyNeedRehab', 'fileHMLOPropertyInfo');
    $isThisGroundUpConstruction = Strings::showField('isThisGroundUpConstruction', 'fileHMLOPropertyInfo');
    $lotStatus = Strings::showField('lotStatus', 'fileHMLOPropertyInfo');
    $lotPurchasePrice = Strings::showField('lotPurchasePrice', 'fileHMLOPropertyInfo');
    $currentLotMarket = Strings::showField('currentLotMarket', 'fileHMLOPropertyInfo');
    $acceptedPurchase = Strings::showField('acceptedPurchase', 'fileHMLOPropertyInfo');
    $requiredLoanAmount = Strings::showField('requiredLoanAmount', 'fileHMLOPropertyInfo');
    $exitStrategy = Strings::showField('exitStrategy', 'fileHMLOPropertyInfo');
    $rehabCost = Strings::showField('rehabCost', 'fileHMLOPropertyInfo');
    $rehabRepairDetails = Strings::showField('rehabRepairDetails', 'fileHMLOPropertyInfo');
    $neededToCompleteRehab = Strings::showField('neededToCompleteRehab', 'fileHMLOPropertyInfo');
    $rehabCompanyName = Strings::showField('rehabCompanyName', 'fileHMLOPropertyInfo');
    $rehabContractorName = Strings::showField('rehabContractorName', 'fileHMLOPropertyInfo');
    $rehabGCLicense = Strings::showField('rehabGCLicense', 'fileHMLOPropertyInfo');
    $rehabContractorEmail = Strings::showField('rehabContractorEmail', 'fileHMLOPropertyInfo');
    $rehabContractorPhone = Strings::showField('rehabContractorPhone', 'fileHMLOPropertyInfo');
    $rehabStrategyPlans = Strings::showField('rehabStrategyPlans', 'fileHMLOPropertyInfo');
    $annualPremium = Strings::showField('annualPremium', 'fileHMLOPropertyInfo');
    $isHiredPerformRehab = Strings::showField('isHiredPerformRehab', 'fileHMLOPropertyInfo');
    $prePaymentPenalty = Strings::showField('prePaymentPenalty', 'fileHMLOPropertyInfo');
    $propEquity = Strings::showField('propEquity', 'fileHMLOPropertyInfo');
    $maxAmtToPutDown = Strings::showField('maxAmtToPutDown', 'fileHMLOPropertyInfo');
    $approvedLoanAmt = Strings::showField('approvedLoanAmt', 'fileHMLOPropertyInfo');
    $HMLOEstateHeldIn = Strings::showField('HMLOEstateHeldIn', 'fileHMLOPropertyInfo');
    $isBlanketLoan = Strings::showField('isBlanketLoan', 'fileHMLOPropertyInfo');
    $paymentReserves = Strings::showField('paymentReserves', 'fileHMLOPropertyInfo');
    $requiredConstruction = Strings::showField('requiredConstruction', 'fileHMLOPropertyInfo');
    $contingencyReserve = Strings::showField('contingencyReserve', 'fileHMLOPropertyInfo');
    $typeOfSale = Strings::showField('typeOfSale', 'fileHMLOPropertyInfo');
    $docTypeLoanterms = Strings::showField('docType', 'fileHMLOPropertyInfo');
    $assetCollateralized = Strings::showField('assetCollateralized', 'fileHMLOPropertyInfo');
    $propDetailsProcess = Strings::showField('propDetailsProcess', 'fileHMLOPropertyInfo');

    $involvedPurchase = Strings::showField('involvedPurchase', 'fileHMLOPropertyInfo');
    $wholesaleFee = Strings::showField('wholesaleFee', 'fileHMLOPropertyInfo');
    $seekingCashRefinance = Strings::showField('seekingCashRefinance', 'fileHMLOPropertyInfo');
    $seekingCash = Strings::showField('seekingCash', 'fileHMLOPropertyInfo');
    $seekingFund = Strings::showField('seekingFund', 'fileHMLOPropertyInfo');
    $isAdditionalGuarantors = Strings::showField('isAdditionalGuarantors', 'fileHMLOPropertyInfo');
    $referringParty = Strings::showField('referringParty', 'fileHMLOPropertyInfo');
    $hereAbout = Strings::showField('hereAbout', 'fileHMLOPropertyInfo');
    $minLiabilityCoverage = Strings::showField('minLiabilityCoverage', 'fileHMLOPropertyInfo');
    $proInsPolicyNo = Strings::showField('proInsPolicyNo', 'fileHMLOPropertyInfo');
    $floodInsurance1 = Strings::showField('floodInsurance1', 'fileHMLOPropertyInfo');
    $proInsPolicyExpDate = Strings::showField('proInsPolicyExpDate', 'fileHMLOPropertyInfo');
    $proInsPolicyEffDate = Strings::showField('proInsPolicyEffDate', 'fileHMLOPropertyInfo');
    $proIncRepNotes = Strings::showField('proIncRepNotes', 'fileHMLOPropertyInfo');
    $propIncNotes = Strings::showField('propIncNotes', 'fileHMLOPropertyInfo');
    $proInsCarrier = Strings::showField('proInsCarrier', 'fileHMLOPropertyInfo');
    $desiredFundingAmount = Strings::showField('desiredFundingAmount', 'fileHMLOPropertyInfo');
    $purposeOfLoan = Strings::showField('purposeOfLoan', 'fileHMLOPropertyInfo');
    $useOfFunds = Strings::showField('useOfFunds', 'fileHMLOPropertyInfo');
    $haveCurrentLoanBal = Strings::showField('haveCurrentLoanBal', 'fileHMLOPropertyInfo');
    $balance = Strings::showField('balance', 'fileHMLOPropertyInfo');
    $doYouHaveInvoiceToFactor = Strings::showField('haveInvoiceToFactor', 'fileHMLOPropertyInfo');
    $invoiceFactorAmount = Strings::showField('amount', 'fileHMLOPropertyInfo');
    $amount = Strings::showField('amount', 'fileHMLOPropertyInfo');
    $heldWith = Strings::showField('heldWith', 'fileHMLOPropertyInfo');

    $isTherePrePaymentPenalty = Strings::showField('isTherePrePaymentPenalty', 'fileHMLOPropertyInfo');
    $balloonPayment = Strings::showField('balloonPayment', 'fileHMLOPropertyInfo');
    $prePayExcessOf20percent = Strings::showField('prePayExcessOf20percent', 'fileHMLOPropertyInfo');
    $limitedOrNot = Strings::showField('limitedOrNot', 'fileHMLOPropertyInfo');
    $loanMadeWholly = Strings::showField('loanMadeWholly', 'fileHMLOPropertyInfo');
    $securityInstrument = Strings::showField('securityInstrument', 'fileHMLOPropertyInfo');
    $rentalIncomePerMonth = Strings::showField('rentalIncomePerMonth', 'fileHMLOPropertyInfo');

    $rehabToBeMade = Strings::showField('rehabToBeMade', 'fileHMLOPropertyInfo');
    $rehabTime = Strings::showField('rehabTime', 'fileHMLOPropertyInfo');
    $isSubjectUnderConst = Strings::showField('isSubjectUnderConst', 'fileHMLOPropertyInfo');
    $areKnownHazards = Strings::showField('areKnownHazards', 'fileHMLOPropertyInfo');
    $areProReports = Strings::showField('areProReports', 'fileHMLOPropertyInfo');
    $isSubjectSS = Strings::showField('isSubjectSS', 'fileHMLOPropertyInfo');
    $changeInCircumstance = Strings::showField('changeInCircumstance', 'fileHMLOPropertyInfo');
    $changeDescription = Strings::showField('changeDescription', 'fileHMLOPropertyInfo');
    $useOfProceeds = Strings::showField('useOfProceeds', 'fileHMLOPropertyInfo');

    if ($isTherePrePaymentPenalty == 'Yes') $prepayentSectionDisplay = 'display: block;';
}
if (Dates::IsEmpty($propertyTaxDueDate)) {
    $propertyTaxDueDate = '';
} else {
    $propertyTaxDueDate = Dates::formatDateWithRE($propertyTaxDueDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($proInsPolicyExpDate)) {
    $proInsPolicyExpDate = '';
} else {
    $proInsPolicyExpDate = Dates::formatDateWithRE($proInsPolicyExpDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($proInsPolicyEffDate)) {
    $proInsPolicyEffDate = '';
} else {
    $proInsPolicyEffDate = Dates::formatDateWithRE($proInsPolicyEffDate, 'YMD', 'm/d/Y');
}
if ($propDetailsProcess == 'Looking for General Info' || $propDetailsProcess == 'Actively Looking For Property') {
    $showBorrowerPropertyDispOpt = 'display: none;';
}
$secArr = Arrays::getValueFromArray('Admin', $fieldsInfo);
$borrowerActiveSectionDisp = '';
if ($publicUser == 1 && $REBroker == 'Yes' && $propDetailsProcess == 'Looking for General Info' || $propDetailsProcess == 'Actively Looking For Property' && trim(BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') {
    $borrowerActiveSectionDisp = 'display: none';
}

if ($publicUser == 1 && $LMRId == 0 && trim(BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') {
    $borrowerActiveSectionDisp = 'display: none';
}
if ($borrowerActiveSectionDisp == '') $borrowerActiveSectionDisp = $HMLOLoanInfoSectionsDisp;
if ($involvedPurchase == 'Yes') $involvedPurchaseDispOpt = 'display: block;';
if ($seekingCashRefinance == 'Yes') $seekingCashRefinanceDispOpt = 'display: block;';
if ($isBorUSCitizen == 'No') $borOriginAndVisaDispOpt = 'display: block;';

if ($isAdditionalGuarantors == 'Yes') {
    $isAdditionalGuarantorsDisp = 'display: block;';
}

require 'HMLOLoanTermsCalculation.php';

if ($HMLOLender == '') $HMLOLender = $processorAssignedCompany;
$titleAttorneyContacts = [];
$titleAttorneyID = '';
if (array_key_exists('Attorney', $fileContacts)) {
    $titleAttorneyContacts = $fileContacts['Attorney'];
}

if (count($titleAttorneyContacts) > 0) {
    $titleAttorneyID = trim($titleAttorneyContacts['CID']);
    $titleAttorneyName = trim($titleAttorneyContacts['contactName']);
    $titleAttorneyFirmName = trim($titleAttorneyContacts['companyName']);
    $titleAttorneyPhone = trim($titleAttorneyContacts['phone']);
    $titleAttorneyEmail = trim($titleAttorneyContacts['email']);
    $titleAttorneyAddress = trim($titleAttorneyContacts['address']);
    $titleAttorneyCity = trim($titleAttorneyContacts['city']);
    $titleAttorneyState = trim($titleAttorneyContacts['state']);
    $titleAttorneyZip = trim($titleAttorneyContacts['zip']);
    $titleAttorneyBarNo = trim($titleAttorneyContacts['barNo']);
}

$disableInsuranceCompanyContacts = '';
if (array_key_exists('Insurance Rep', $fileContacts)) {
    $disableInsuranceCompanyContacts = 'disabled';
    $insuranceCompanyContacts = $fileContacts['Insurance Rep'];
}

$insuranceAgentInfoArray = [];
$attorneysInfoArray = [];
$escrowsInfoArray = [];
$financialAdvisorArray = [];
$accountantArray = [];
if (array_key_exists('multiContact', $fileContacts)) {
    if (array_key_exists('Insurance Rep', $fileContacts['multiContact'])) {
        $disableInsuranceCompanyContacts = 'disabled';
        $insuranceAgentInfoArray = $fileContacts['multiContact']['Insurance Rep'];
        if (count($insuranceAgentInfoArray) > 0) {
            usort($insuranceAgentInfoArray, function ($a, $b) {
                return $a['SID'] - $b['SID'];
            });
        }
    }
    if (array_key_exists('Attorney', $fileContacts['multiContact'])) {
        $attorneysInfoArray = $fileContacts['multiContact']['Attorney'];
        if (count($attorneysInfoArray) > 0) {
            usort($attorneysInfoArray, function ($a, $b) {
                return $a['SID'] - $b['SID'];
            });
        }
    }
    if (array_key_exists('Escrow', $fileContacts['multiContact'])) {
        $escrowsInfoArray = $fileContacts['multiContact']['Escrow'];
        if (count($escrowsInfoArray) > 0) {
            usort($escrowsInfoArray, function ($a, $b) {
                return $a['SID'] - $b['SID'];
            });
        }
    }
    if (array_key_exists('Title Rep', $fileContacts['multiContact'])) {
        $titlesInfoArray = $fileContacts['multiContact']['Title Rep'];
        if (count($titlesInfoArray) > 0) {
            usort($titlesInfoArray, function ($a, $b) {
                return $a['SID'] - $b['SID'];
            });
        }
    }

    if (array_key_exists('Financial Advisor', $fileContacts['multiContact'])) {
        $financialAdvisorArray = $fileContacts['multiContact']['Financial Advisor'];
        if (count($financialAdvisorArray) > 0) {
            usort($financialAdvisorArray, function ($a, $b) {
                return $a['SID'] - $b['SID'];
            });
        }
    }
    if (array_key_exists('Accountant', $fileContacts['multiContact'])) {
        $accountantArray = $fileContacts['multiContact']['Accountant'];
        if (count($accountantArray) > 0) {
            usort($accountantArray, function ($a, $b) {
                return $a['SID'] - $b['SID'];
            });
        }
    }
}
//echo 'sasasasa';
//pr($accountantArray);
//exit;
if (count($insuranceCompanyContacts) > 0) {
    $insuranceCompanyID = trim($insuranceCompanyContacts['CID']);
    $proInsFirstName = trim($insuranceCompanyContacts['contactName']);
    $proInsName = trim($insuranceCompanyContacts['companyName']);
    $proIncCell = trim($insuranceCompanyContacts['cell']);
    $proIncFax = trim($insuranceCompanyContacts['fax']);
    $proIncTollFree = trim($insuranceCompanyContacts['tollFree']);
    $proIncRepNotes = trim($insuranceCompanyContacts['description']);
    $proInsLastName = trim($insuranceCompanyContacts['contactLName']);
    $proIncWebsite = trim($insuranceCompanyContacts['website']);
    $proIncEmail = trim($insuranceCompanyContacts['email']);
    $proIncPhone = trim($insuranceCompanyContacts['phone']);
    $proInsAddress = trim($insuranceCompanyContacts['address']);
    $proInsCity = trim($insuranceCompanyContacts['city']);
    $proInsState = trim($insuranceCompanyContacts['state']);
    $proInsZip = trim($insuranceCompanyContacts['zip']);
}
if (array_key_exists('Title Rep', $fileContacts)) {
    $titleContacts = $fileContacts['Title Rep'];
}

$representativeID = 0;
$titleContactName = '';
$titleCompanyName = '';
$titleCell = '';
$titletollFree = '';
$titlePhoneNumber = '';
$titleFax = '';
$titleNotes = '';
$titleContactLName = $titleReportDate = $titlereportDoc = '';
if (count($titleContacts) > 0) {
    $representativeID = trim($titleContacts['CID']);
    $titleContactName = trim($titleContacts['contactName']);
    $titleCompanyName = trim($titleContacts['companyName']);
    $titleCell = trim($titleContacts['cell']);
    $titletollFree = trim($titleContacts['tollFree']);
    $titleNotes = trim($titleContacts['description']);
    $titleContactLName = trim($titleContacts['contactLName']);
}
$titleReportDate = Strings::showField('titleReportDate', 'FilePropInfo');
$titlePhoneNumber = Strings::showField('titleCompanyPhoneNumber', 'listingRealtorInfo');
$titleFax = Strings::showField('sales2Fax', 'listingRealtorInfo');

if (Dates::IsEmpty($titleReportDate)) {
    $titleReportDate = '';
} else {
    $titleReportDate = Dates::formatDateWithRE($titleReportDate, 'YMD', 'm/d/Y');
}

$escrowContacts = [];
$escrowID = '';
$escrowOfficer = $escrowOfficerLName = $escrowOfficerFirmName = $escrowOfficerEmail = $escrowOfficerPhone = $escrowOfficertollFree = $escrowOfficerFax = $escrowOfficerCell = $escrowNo = $escrowAddress = $escrowCity = $escrowState = $escrowZip = '';
if (array_key_exists('Escrow Officer', $fileContacts)) {
    $escrowContacts = $fileContacts['Escrow Officer'];
}
if (count($escrowContacts) > 0) {
    $escrowID = trim($escrowContacts['CID']);
    $escrowOfficer = trim($escrowContacts['contactName']);
    $escrowOfficerLName = trim($escrowContacts['contactLName']);
    $escrowOfficerFirmName = trim($escrowContacts['companyName']);
    $escrowOfficerEmail = trim($escrowContacts['email']);
    $escrowOfficerPhone = trim($escrowContacts['phone']);
    $escrowOfficertollFree = trim($escrowContacts['tollFree']);
    $escrowOfficerFax = trim($escrowContacts['fax']);
    $escrowOfficerCell = trim($escrowContacts['cell']);
    $escrowNo = trim($escrowContacts['barNo']);
    $escrowAddress = trim($escrowContacts['address']);
    $escrowCity = trim($escrowContacts['city']);
    $escrowState = trim($escrowContacts['state']);
    $escrowZip = trim($escrowContacts['zip']);

}
$escrowcFax1 = $escrowcFax2 = $escrowcFax3 = '';
$escrowCellNo1 = $escrowCellNo2 = $escrowCellNo3 = '';
$escrowOfficerFaxArray = Strings::splitPhoneNumber($escrowOfficerFax);
if (count($escrowOfficerFaxArray) > 0) {
    $escrowFax1 = $escrowOfficerFaxArray['No1'];
    $escrowFax2 = $escrowOfficerFaxArray['No2'];
    $escrowFax3 = $escrowOfficerFaxArray['No3'];
}


$escrowOfficerCellArray = Strings::splitPhoneNumber($escrowOfficerCell);
if (count($escrowOfficerCellArray) > 0) {
    $escrowCellNo1 = $escrowOfficerCellArray['No1'];
    $escrowCellNo2 = $escrowOfficerCellArray['No2'];
    $escrowCellNo3 = $escrowOfficerCellArray['No3'];
}

if ($lien1Terms == '') {
    if (!in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
        $lien1Terms = LoanTerms::INTEREST_ONLY;
    }
}

//	if($loanTerm == '')				$loanTerm ='360 Months';     /** By default loan term dropdown should be 'select'_ card308**/
if ($prePaymentPenalty == '') $prePaymentPenalty = 'None';
if ($lienPosition == '') $lienPosition = '1';
//if ($downPaymentPercentage == '') $downPaymentPercentage = '20';

$agentSectionDiv = 'display: block;';

if ($webformName == 'Hard Money App without Tracking') {
    $agentSectionDiv = 'display: none;';
}
$disabledInputForClient = 1;
if ($userGroup == 'Client') $disabledInputForClient = 0;

//$mandatoryCls = ' class="mandatory" required="required" ';	/* Used to Set the fields as mandatory - Set in Pc profile Ticket ID - ********* */
$mandatoryCls = ' class="mandatory" ';    /* Used to Set the fields as mandatory - Set in Pc profile Ticket ID - ********* */

$landValueCls = 'display: none;';
if ($clientType == 'CONS' || $typeOfHMLOLoanRequesting == 'New Construction - Existing Land') {
    $landValueCls = 'display: table-cell;';
}

if (($maritalStatus == 'Married' || $maritalStatus == 'Divorced' || $maritalStatus == 'Widowed' || $maritalStatus == 'Separated') && $isMF == 0) {
    $maritalDisp = 'display: block;';
} else {
    $maritalDisp = 'display: none;';
}

/**
 * Get Borrower Flipping Experience and Ground Up Construction Experience
 */

if (array_key_exists('Flip', $fileExpFilpGroundUp)) $clientExpProInfo = $fileExpFilpGroundUp['Flip'];
if (array_key_exists('Gup', $fileExpFilpGroundUp)) $clientGUExpInfo = $fileExpFilpGroundUp['Gup'];
if (array_key_exists('Sell', $fileExpFilpGroundUp)) $clientSellExpInfo = $fileExpFilpGroundUp['Sell'];

$previousState = Strings::showField('previousState', 'LMRInfo');
$PCClientEntityInfoArray = getPCClientEntityInfo::getReport(['PCID' => $PCID, 'CID' => $selClientId]);

if (in_array($PCID, $glFirstRehabLending)) {
    unset($glHMLOPresentOccupancy[1]);
    unset($glHMLOPresentOccupancy[2]);
    unset($glHMLOPresentOccupancy[3]);
    $glHMLOPresentOccupancy = array_values($glHMLOPresentOccupancy);
}

$escrowContacts = [];
$escrowID = '';
$escrowOfficer = $escrowOfficerLName = $escrowOfficerFirmName = $escrowOfficerEmail = $escrowOfficerPhone = $escrowOfficertollFree = $escrowOfficerFax = $escrowOfficerCell = $escrowNo = $escrowAddress = $escrowCity = $escrowState = $escrowZip = '';
if (array_key_exists('Escrow', $fileContacts)) {
    $escrowContacts = $fileContacts['Escrow'];
}

if (count($escrowContacts) > 0) {
    $escrowID = trim($escrowContacts['CID']);
    $escrowOfficer = trim($escrowContacts['contactName']);
    $escrowOfficerLName = trim($escrowContacts['contactLName']);
    $escrowOfficerFirmName = trim($escrowContacts['companyName']);
    $escrowOfficerEmail = trim($escrowContacts['email']);
    $escrowOfficerPhone = trim($escrowContacts['phone']);
    $escrowOfficertollFree = trim($escrowContacts['tollFree']);
    $escrowOfficerFax = trim($escrowContacts['fax']);
    $escrowOfficerCell = trim($escrowContacts['cell']);
    $escrowNo = trim($escrowContacts['barNo']);
    $escrowAddress = trim($escrowContacts['address']);
    $escrowCity = trim($escrowContacts['city']);
    $escrowState = trim($escrowContacts['state']);
    $escrowZip = trim($escrowContacts['zip']);
}
$escrowcFax1 = $escrowcFax2 = $escrowcFax3 = '';
$escrowCellNo1 = $escrowCellNo2 = $escrowCellNo3 = '';
$escrowOfficerFaxArray = Strings::splitPhoneNumber($escrowOfficerFax);
if (count($escrowOfficerFaxArray) > 0) {
    $escrowFax1 = $escrowOfficerFaxArray['No1'];
    $escrowFax2 = $escrowOfficerFaxArray['No2'];
    $escrowFax3 = $escrowOfficerFaxArray['No3'];
}
$escrowOfficerCellArray = Strings::splitPhoneNumber($escrowOfficerCell);
if (count($escrowOfficerCellArray) > 0) {
    $escrowCellNo1 = $escrowOfficerCellArray['No1'];
    $escrowCellNo2 = $escrowOfficerCellArray['No2'];
    $escrowCellNo3 = $escrowOfficerCellArray['No3'];
}
/* Estimated Project Cost */
$epcArray = BaseHTML::sectionAccess(['sId' => 'EPC', 'opt' => $fileTab]);
if (count($estimatedProjectCostArray) > 0) {
    if (BaseHTML::fieldAccess(['fNm' => 'landAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $landAcquisition = trim($estimatedProjectCostArray['landAcquisition']);
    if (BaseHTML::fieldAccess(['fNm' => 'newBuildingConstruction', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $newBuildingConstruction = trim($estimatedProjectCostArray['newBuildingConstruction']);
    if (BaseHTML::fieldAccess(['fNm' => 'constructionContingency', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $constructionContingency = trim($estimatedProjectCostArray['constructionContingency']);
    if (BaseHTML::fieldAccess(['fNm' => 'businessAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $businessAcquisition = trim($estimatedProjectCostArray['businessAcquisition']);
    if (BaseHTML::fieldAccess(['fNm' => 'landAndBusinessAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $landAndBusinessAcquisition = trim($estimatedProjectCostArray['landAndBusinessAcquisition']);
    if (BaseHTML::fieldAccess(['fNm' => 'buildingOrLeasehold', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $buildingOrLeasehold = trim($estimatedProjectCostArray['buildingOrLeasehold']);
    if (BaseHTML::fieldAccess(['fNm' => 'acquisitionOfMachineryEquipment', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $acquisitionOfMachineryEquipment = trim($estimatedProjectCostArray['acquisitionOfMachineryEquipment']);
    if (BaseHTML::fieldAccess(['fNm' => 'acquisitionOfFurnitureFixtures', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $acquisitionOfFurnitureFixtures = trim($estimatedProjectCostArray['acquisitionOfFurnitureFixtures']);
    if (BaseHTML::fieldAccess(['fNm' => 'inventoryPurchase', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $inventoryPurchase = trim($estimatedProjectCostArray['inventoryPurchase']);
    if (BaseHTML::fieldAccess(['fNm' => 'workingCapital', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $workingCapital = trim($estimatedProjectCostArray['workingCapital']);
    if (BaseHTML::fieldAccess(['fNm' => 'refinancingExistingBusinessDebt', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $refinancingExistingBusinessDebt = trim($estimatedProjectCostArray['refinancingExistingBusinessDebt']);
    if (BaseHTML::fieldAccess(['fNm' => 'franchiseFee', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $franchiseFee = trim($estimatedProjectCostArray['franchiseFee']);
    if (BaseHTML::fieldAccess(['fNm' => 'closingCost', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $closingCost = trim($estimatedProjectCostArray['closingCost']);
    $otherCostText = trim($estimatedProjectCostArray['otherCostText']); //no FF control
    if (BaseHTML::fieldAccess(['fNm' => 'otherCost', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $otherCost = trim($estimatedProjectCostArray['otherCost']);
    if (BaseHTML::fieldAccess(['fNm' => 'otherCost2', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $otherCost2 = trim($estimatedProjectCostArray['otherCost2']);
    //if (BaseHTML::fieldAccess(array('fNm' => 'otherCostText2', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1)))
    $otherCostText2 = trim($estimatedProjectCostArray['otherCostText2']); //no FF control
    //if (BaseHTML::fieldAccess(array('fNm' => 'estimatedProjectCost', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1))) $estimatedProjectCost = trim($estimatedProjectCostArray["estimatedProjectCost"]);
    if (BaseHTML::fieldAccess(['fNm' => 'lessOwnerEquityToBeInjected', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $lessOwnerEquityToBeInjected = trim($estimatedProjectCostArray['lessOwnerEquityToBeInjected']);
    if (BaseHTML::fieldAccess(['fNm' => 'lessSellerCarryBack', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $lessSellerCarryBack = trim($estimatedProjectCostArray['lessSellerCarryBack']);
    //if (BaseHTML::fieldAccess(array('fNm' => 'loanRequestedForProject', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1))) $loanRequestedForProject = trim($estimatedProjectCostArray["loanRequestedForProject"]);

    $estimatedProjectCost = Strings::replaceCommaValues($landAcquisition) + Strings::replaceCommaValues($newBuildingConstruction) + Strings::replaceCommaValues($constructionContingency) +
        Strings::replaceCommaValues($businessAcquisition) + Strings::replaceCommaValues($landAndBusinessAcquisition) + Strings::replaceCommaValues($buildingOrLeasehold) + Strings::replaceCommaValues($acquisitionOfMachineryEquipment) +
        Strings::replaceCommaValues($acquisitionOfFurnitureFixtures) + Strings::replaceCommaValues($inventoryPurchase) + Strings::replaceCommaValues($workingCapital) + Strings::replaceCommaValues($refinancingExistingBusinessDebt) +
        Strings::replaceCommaValues($otherCost) + Strings::replaceCommaValues($otherCost2) + Strings::replaceCommaValues($franchiseFee) + Strings::replaceCommaValues($closingCost);
    $estProjectCost = round($estimatedProjectCost, 2);

    $loanRequestedForProject = ($estimatedProjectCost - (Strings::replaceCommaValues($lessOwnerEquityToBeInjected) + Strings::replaceCommaValues($lessSellerCarryBack)));
    $loanRequestedForProject = round($loanRequestedForProject, 2);
}
/* Estimated Project Cost */
/* Creditors / Liabilities (Non-Real Estate) */
/* This is a multiple / add more fields section hence data added in UI directly*/
/* Creditors / Liabilities (Non-Real Estate) */

/* Collateral */
if (count($collateralArray ?? []) > 0) {
    $commercialRealEstate = trim($collateralArray['commercialRealEstate']);
    $furnitureFixtures = trim($collateralArray['furnitureFixtures']);
    $residentialRealEstate = trim($collateralArray['residentialRealEstate']);
    $vehicle = trim($collateralArray['vehicle']);
    $inventory = trim($collateralArray['inventory']);
    $usedEquipmentMachinery = trim($collateralArray['usedEquipmentMachinery']);
    $accountsReceivable = trim($collateralArray['accountsReceivable']);
    $vacantLand = trim($collateralArray['vacantLand']);
    $cash = trim($collateralArray['cash']);
    $leaseholdImprovement = trim($collateralArray['leaseholdImprovement']);
    $investmentAccount = trim($collateralArray['investmentAccount']);
    $newEquipmentMachinery = trim($collateralArray['newEquipmentMachinery']);
    $retirementAccount = trim($collateralArray['retirementAccount']);
    $other = trim($collateralArray['other']);
    $none = trim($collateralArray['noneField']);
    $collateralDescription = trim($collateralArray['collateralDescription']);

    if ($collateralArray['commercialRealEstateDate']) {
        $commercialRealEstateDate = DateTime::createFromFormat('Y-m-d', $collateralArray['commercialRealEstateDate'])->format('Y/m/d');
    }
    $commercialRealEstateDesc = trim($collateralArray['commercialRealEstateDesc']);


    if ($collateralArray['vehiclesDate']) {
        $vehiclesDate = DateTime::createFromFormat('Y-m-d', $collateralArray['vehiclesDate'])->format('Y/m/d');
    }
    $vehiclesDesc = trim($collateralArray['vehiclesDesc']);

    if ($collateralArray['accountsReceivableDate']) {
        $accountsReceivableDate = DateTime::createFromFormat('Y-m-d', $collateralArray['accountsReceivableDate'])->format('Y/m/d');
    }

    $leaseholdImprovementsDesc = trim($collateralArray['leaseholdImprovementsDesc']);
    $newEquipmentMachineryDesc = trim($collateralArray['newEquipmentMachineryDesc']);
    $furnitureFixturesDesc = trim($collateralArray['furnitureFixturesDesc']);

    if ($collateralArray['inventoryDate']) {
        $inventoryDate = DateTime::createFromFormat('Y-m-d', $collateralArray['inventoryDate'])->format('Y/m/d');
    }
    if ($collateralArray['vacantLandDate']) {
        $vacantLandDate = DateTime::createFromFormat('Y-m-d', $collateralArray['vacantLandDate'])->format('Y/m/d');
    }
    $vacantLandDesc = trim($collateralArray['vacantLandDesc']);
    $investmentAccountsDesc = trim($collateralArray['investmentAccountsDesc']);

    if ($collateralArray['otherDate']) {
        $otherDate = DateTime::createFromFormat('Y-m-d', $collateralArray['otherDate'])->format('Y/m/d');
    }
    $otherDesc = trim($collateralArray['otherDesc']);

    if ($collateralArray['residentialRealEstateDate']) {
        $residentialRealEstateDate = DateTime::createFromFormat('Y-m-d', $collateralArray['residentialRealEstateDate'])->format('Y/m/d');
    }
    $residentialRealEstateDesc = trim($collateralArray['residentialRealEstateDesc']);
    $usedEquipmentMachineryDesc = trim($collateralArray['usedEquipmentMachineryDesc']);
    $retirementAccountDesc = trim($collateralArray['retirementAccountDesc']);
}
/* Collateral */

/* Admin Info */
$hearingDate = Strings::showField('hearingDate', 'QAInfo');
if (Dates::IsEmpty($hearingDate)) {
    $hearingDate = '';
} else {
    $hearingDate = Dates::formatDateWithRE($hearingDate, 'YMD', 'm/d/Y');
}
/* Admin Info */

/* Project Management */
if (count($propMgmtArray) > 0) {
    $propMgmtRowId = trim($propMgmtArray['id']);
    $pmSubjectMarketArea = trim($propMgmtArray['pmSubjectMarketArea']);
    $pmSubjectMarketAreaExpl = trim($propMgmtArray['pmSubjectMarketAreaExpl']);
    $pmRealEstatePropertyManagement = trim($propMgmtArray['pmRealEstatePropertyManagement']);
    //$pmRealEstatePropertyManagementDocumentation = '';
    $pmTenant = trim($propMgmtArray['pmTenant']);
    $pmTenantExpl = trim($propMgmtArray['pmTenantExpl']);
    $pmAuditsPastYear = trim($propMgmtArray['pmAuditsPastYear']);
    $pmAuditsPastYearExpl = trim($propMgmtArray['pmAuditsPastYearExpl']);
    $pmLeaseTerms = trim($propMgmtArray['pmLeaseTerms']);
    $pmLeaseTermsExpl = trim($propMgmtArray['pmLeaseTermsExpl']);
    $pmIllegalActivities = trim($propMgmtArray['pmIllegalActivities']);
    $pmIllegalActivitiesExpl = trim($propMgmtArray['pmIllegalActivitiesExpl']);
    $pmMaterialDeferred = trim($propMgmtArray['pmMaterialDeferred']);
    $pmMaterialDeferredExpl = trim($propMgmtArray['pmMaterialDeferredExpl']);
    $pmPermitViolation = trim($propMgmtArray['pmPermitViolation']);
    $pmPermitViolationExpl = trim($propMgmtArray['pmPermitViolationExpl']);
    $pmTenantDelinquencies = trim($propMgmtArray['pmTenantDelinquencies']);
    $pmTenantDelinquenciesExpl = trim($propMgmtArray['pmTenantDelinquenciesExpl']);
    $pmCondemnationProceeding = trim($propMgmtArray['pmCondemnationProceeding']);
    $pmCondemnationProceedingExpl = trim($propMgmtArray['pmCondemnationProceedingExpl']);
    $pmAffordableHousing = trim($propMgmtArray['pmAffordableHousing']);
    $pmAffordableHousingExpl = trim($propMgmtArray['pmAffordableHousingExpl']);
    $pmRentControl = trim($propMgmtArray['pmRentControl']);
    $pmRentControlExpl = trim($propMgmtArray['pmRentControlExpl']);
    $pmFairHousingACT = trim($propMgmtArray['pmFairHousingACT']);
    $pmFairHousingACTExpl = trim($propMgmtArray['pmFairHousingACTExpl']);
    $pmAmericanDisability = trim($propMgmtArray['pmAmericanDisability']);
    $pmAmericanDisabilityExpl = trim($propMgmtArray['pmAmericanDisabilityExpl']);
    $pmHazardReductionAct = trim($propMgmtArray['pmHazardReductionAct']);
    $pmHazardReductionActExpl = trim($propMgmtArray['pmHazardReductionActExpl']);
    $pmPracticesAct = trim($propMgmtArray['pmPracticesAct']);
    $pmPracticesActExpl = trim($propMgmtArray['pmPracticesActExpl']);
    $pmOverallExperienceDesc = trim($propMgmtArray['pmOverallExperienceDesc']);
}
/* Project Management */

$prePaymentPenaltyResArr = glprePaymentPenalty::getPCLevelPrePaymentPenalty($PCID, 'FC');
