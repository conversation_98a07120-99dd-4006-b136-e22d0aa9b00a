<?php
global $borrowerAlternateNamesArray, $allowToEdit, $secArr;

use models\Controllers\loanForm;
use models\standard\BaseHTML;

$altNamesCnt = count($borrowerAlternateNamesArray ?? []);
if ($altNamesCnt == 0) $altNamesCnt = 1;
for ($alt = 0; $alt < $altNamesCnt ; $alt++) {
    $alternateFName = '';
    $alternateMName = '';
    $alternateLName = '';
    $alternateNameID = 0;

    $alternateFName = $borrowerAlternateNamesArray[$alt]['alternateFName'];
    $alternateMName = $borrowerAlternateNamesArray[$alt]['alternateMName'];
    $alternateLName = $borrowerAlternateNamesArray[$alt]['alternateLName'];
    $alternateNameID = $borrowerAlternateNamesArray[$alt]['nameID'];
    ?>
    <div class="row bg-light ml-1 mr-1 py-2 mb-2 alternativeClass" id="alternativeNameDiv_<?php echo $alt + 1; ?>">
        <div class="col-md-12 form-group">
            <div class="row form-group bg-secondary mb-2 py-3 " id="alternativeClass">
                <label class="  col-auto mr-auto"> Alternate Section <span
                            class="icrementSec"><?php echo $alt + 1 ?></span></label>
                <span class=" col-auto removeButtonClass"></span>
    <?php if ($alt > 0) { ?>
        <a href="javascript:void(0);"
           class="btn btn-sm btn-danger btn-text-primary  btn-icon ml-2 tooltipClass removeAlternateDetails " title=""
           data-original-title="Click to Delete"><i class=" icon-md fas fa-minus-circle "></i></a>
    <?php } ?>
            </div>
        </div>
        <div class="col-md-6 alternateNamesInfo alternateFName_disp <?php echo loanForm::showField('alternateFName'); ?>">
            <div class="form-group  row ">
                <label class="col-md-5 font-weight-bold"
                       for="alternateFName_<?php echo $alt + 1; ?>"><?php echo (BaseHTML::fieldAccess(['fNm' => 'alternateFName', 'sArr' => $secArr, 'opt' => 'L']) != '') ? BaseHTML::fieldAccess(['fNm' => 'alternateFName', 'sArr' => $secArr, 'opt' => 'L']) : 'First Name'; ?></label>
                <div class="col-md-7 isClientInfo">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm "
                               type="text" name="alternateFName[]"
                               id="alternateFName_<?php echo $alt + 1; ?>" value="<?php echo htmlentities($alternateFName); ?>"
                               autocomplete="off"
                               tabindex="<?php echo $tabIndex++ ?>"/>
                    <?php } else {
                        echo '<h5>' . $alternateFName . '</h5>';
                    } ?>
                </div>
            </div>
            <input type="hidden" name="alternateNameID[]" id="alternateNameID_<?php echo $alt + 1; ?>"
                   value="<?php echo $alternateNameID; ?>">
        </div>
        <div class="col-md-6 alternateNamesInfo alternateMName_disp <?php echo loanForm::showField('alternateMName'); ?>">
            <div class="form-group  row ">
                <label class="col-md-5 font-weight-bold"
                       for="alternateMName_<?php echo $alt + 1; ?>"><?php echo (BaseHTML::fieldAccess(['fNm' => 'alternateMName', 'sArr' => $secArr, 'opt' => 'L']) != '') ? BaseHTML::fieldAccess(['fNm' => 'alternateMName', 'sArr' => $secArr, 'opt' => 'L']) : 'First Name'; ?></label>
                <div class="col-md-7 isClientInfo">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm"
                               type="text" name="alternateMName[]"
                               id="alternateMName_<?php echo $alt + 1; ?>" value="<?php echo htmlentities($alternateMName); ?>"
                               autocomplete="off"
                               tabindex="<?php echo $tabIndex++ ?>"/>
                    <?php } else {
                        echo '<h5>' . $alternateMName . '</h5>';
                    } ?>

                </div>
            </div>
        </div>
        <div class="col-md-6 alternateNamesInfo alternateLName_disp <?php echo loanForm::showField('alternateLName'); ?>">
            <div class="form-group  row ">
                <label class="col-md-5 font-weight-bold"
                       for="alternateLName_<?php echo $alt + 1; ?>"><?php echo (BaseHTML::fieldAccess(['fNm' => 'alternateLName', 'sArr' => $secArr, 'opt' => 'L']) != '') ? BaseHTML::fieldAccess(['fNm' => 'alternateLName', 'sArr' => $secArr, 'opt' => 'L']) : 'First Name'; ?></label>
                <div class="col-md-7 isClientInfo">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm alternateNamesTabindex"
                               type="text" name="alternateLName[]"
                               id="alternateLName_<?php echo $alt + 1; ?>" value="<?php echo htmlentities($alternateLName); ?>"
                               autocomplete="off"
                               tabindex="<?php echo $tabIndex++ ?>"/>
                    <?php } else {
                        echo '<h5>' . $alternateLName . '</h5>';
                    } ?>

                </div>
            </div>
        </div>
    </div>
<?php } ?>
<script>
    if ($('.alternateNamesInfo.secShow').length > 0) { //show title
        $("#alternateNamesSection").show();
        $(".alternativeClass").show();
    } else { //hide title
        $("#alternateNamesSection").hide();
        $(".alternativeClass").hide();
    }
</script>
