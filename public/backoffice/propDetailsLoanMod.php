<?php
global $LODispOpt, $MFDispOpt, $HMLODispOpt, $FUDispOpt, $EFDispOptNew, $hideLOC,
       $borrowerActiveSectionDisp, $isHMLO, $allowToEdit, $tabIndexNo,
       $tabIndex, $occupancy, $areTaxesInsuranceEscrowed, $areInsuranceEscrowed;

use models\constants\gl\glHMLOPresentOccupancy;
use models\constants\gl\glOccupancyArray;
use models\constants\gl\glPCID;
use models\constants\ResidentialPropArr;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Strings;

$ResidentialPropArr = ResidentialPropArr::$ResidentialPropArr;
$glOccupancyArray = glOccupancyArray::$glOccupancyArray;
$glHMLOPresentOccupancy = (LMRequest::File()->FPCID == glPCID::PCID_PROD_CV3) ? glHMLOPresentOccupancy::$glHMLOPresentOccupancyCV3 :
    glHMLOPresentOccupancy::$glHMLOPresentOccupancy;
$ResiPropArrZillowValDisp = ' display: none; '; // https://www.pivotaltracker.com/story/show/*********
if (in_array(Strings::showField('propertyType', 'LMRInfo'), $ResidentialPropArr)) $ResiPropArrZillowValDisp = ' display: block; ';
?>
<div class="hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF hideLOC"
     style="<?php echo $LODispOpt . $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOptNew . $hideLOC ?>">

    <div class="card card-custom propAddress borrowerActiveSection subjectPropertyCard1"
         style="<?php echo $borrowerActiveSectionDisp; ?>">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    Subject Property
                </h3>
            </div>
            <div class="card-toolbar ">
                <a href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="subjectPropertyCard1"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                    <i class="ki ki-close icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body subjectPropertyCard1_body">

            <div class="row">
                <div class=" col-md-6">
                    <div class="row form-group">
                        <label class="col-md-5 font-weight-bold" for="presentOccupancy">Present Occupancy</label>
                        <div class="col-md-7">
                            <?php if ($isHMLO == 1) {
                                if ($allowToEdit) { ?>
                                    <select class="form-control input-sm" name="presentOccupancy" id="presentOccupancy"
                                            TABINDEX="<?php echo $tabIndexNo++; ?>">
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glHMLOPresentOccupancy); $i++) {
                                            $sOpt = '';
                                            $typeOfHouse = '';
                                            $typeOfHouse = trim($glHMLOPresentOccupancy[$i]);
                                            $sOpt = Arrays::isSelected($typeOfHouse, Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy);
                                            echo "<option value=\"" . $typeOfHouse . "\" " . $sOpt . '>' . $typeOfHouse . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('presentOccupancy', 'FilePropInfo') ?></h5>
                                <?php }
                            } else { ?>

                                <?php if ($allowToEdit) { ?>
                                    <select name="occupancy" id="occupancy"
                                            onchange="principalResidenceServicerInfo(); principalResidenceInfo(this.value);"
                                            class="form-control input-sm " tabindex="<?php echo $tabIndex++; ?>">
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glOccupancyArray); $i++) {
                                            $sOpt = '';
                                            $glOccupancy = '';
                                            $glOccupancy = trim($glOccupancyArray[$i]);
                                            $sOpt = Arrays::isSelected($glOccupancy, $occupancy);
                                            echo "<option value=\"" . $glOccupancy . "\" " . $sOpt . '>' . $glOccupancy . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>

                                    <h5><?php echo Strings::showField('occupancy', 'LMRInfo') ?></h5>
                                <?php }
                            } ?>

                        </div>
                    </div>
                </div>


                <div class=" col-md-6">
                    <div class="row form-group">
                        <label class="col-md-5 font-weight-bold" for="areTaxesInsuranceEscrowed">Are taxes
                            escrowed?</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>

                                <span class="switch switch-icon">
	<label class="font-weight-bold">
		<input class="form-control" <?php if (Strings::showField('areTaxesInsuranceEscrowed', 'LMRInfo') == 1) { ?> checked="checked" <?php } ?>
			   id="isEscrowed" type="checkbox" tabindex="<?php echo $tabIndexNo++; ?>"
               onchange="toggleSwitch('isEscrowed', 'areTaxesInsuranceEscrowed', '1', '0' );">
		    <input type="hidden" name="areTaxesInsuranceEscrowed" id="areTaxesInsuranceEscrowed"
                   value="<?php echo $areTaxesInsuranceEscrowed; ?>">
		<span></span>
	</label>
</span>

                            <?php } else {

                                if (Strings::showField('areTaxesInsuranceEscrowed', 'LMRInfo') == 1) echo '<h5>Yes</h5>';
                                else if (Strings::showField('areTaxesInsuranceEscrowed', 'LMRInfo') == 0) echo '<h5>No</h5>';
                            } ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="row form-group">
                        <label class="col-md-5 font-weight-bold" for="areInsuranceEscrowed">Is Insurance
                            escrowed?</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <span class="switch switch-icon">
	<label class="font-weight-bold">
		<input class="form-control" <?php if (Strings::showField('areInsuranceEscrowed', 'LMRInfo') == 1) { ?> checked="checked" <?php } ?>
			   id="isInsEscrowed" type="checkbox" tabindex="<?php echo $tabIndexNo++; ?>"
               onchange="toggleSwitch('isInsEscrowed', 'areInsuranceEscrowed', '1', '0' );">
      <input type="hidden" name="areInsuranceEscrowed" id="areInsuranceEscrowed"
             value="<?php echo $areInsuranceEscrowed; ?>">
		<span></span>
	</label>
</span>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class=" col-md-6">
                    <div class="row form-group">
                        <label class="col-md-5  font-weight-bold" for="homeValue">Home value? (As-Is)</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
		<span class="input-group-text">
			$
		</span>
                                    </div>
                                    <input type="text" class="form-control input-sm" name="homeValue" id="homeValue"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('homeValue', 'LMRInfo')) ?>"
                                           onblur="currencyConverter(this, this.value);"
                                           placeholder="0.00"
                                           TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                                </div>


                            <?php } else { ?>
                                <b><?php echo Strings::showField('homeValue', 'LMRInfo') ?></b>
                            <?php } ?>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    function buildurl(newhref) {
        var newPropCity = document.getElementById('propertyCity').value;
        var newPropState = document.getElementById('propertyState').value;
        var newPropZip = document.getElementById('propertyZip').value;
        var newaddress = document.getElementById('propertyAddress').value;
        newstring = 'http://www.zillow.com/search/RealEstateSearch.htm?citystatezip=' + newaddress + ' ' + newPropCity + ' ' + newPropState + ' ' + newPropZip;
        newhref.href = newstring;
    }
</script>
