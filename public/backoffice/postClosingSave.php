<?php


use models\Controllers\LMRequest\PostClosing;
use models\cypher;
use models\lendingwise\tblFile;
use models\lendingwise_log\ChangeLog;
use models\PageVariables;
use models\Request;
use models\standard\HTTP;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';
require 'initPageVariables.php';
require 'getPageVariables.php';



if (($_SERVER['REQUEST_METHOD'] ?? null) !== 'POST') {
    HTTP::ExitJSON(['success' => true]);
    exit;
}
UserAccess::checkReferrerPgs(['url' => 'LMRequest.php']);
UserAccess::CheckAdminUse();

$LMRId = (isset($_POST['encryptedLId']) ? cypher::myDecryption(Request::GetClean('encryptedLId')) : 0);
$goToTab = Request::GetClean('goToTab') ?? '';
$isSave = Request::GetClean('isSave') ?? '';
$activeTab = Request::GetClean('activeTab') ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    ChangeLog::LogChanges(
        tblFile::class,
        $LMRId,
        basename(__FILE__, '.php'),
        $_REQUEST,
        PageVariables::$userNumber
    );
}

PostClosing::$LMRId = $LMRId;
PostClosing::save();
//re-calculate HUD
if ($isSave == 1) {
    $parts = parse_url($_SERVER['HTTP_REFERER']);
    parse_str($parts['query'], $urlParameters);
    $urlParameters['tabOpt'] = $activeTab;
    $redirect = strtok($_SERVER['HTTP_REFERER'], '?');
    $redirect .= '?' . http_build_query($urlParameters);
} else {
    $redirect = $_SERVER['HTTP_REFERER'];
}
HTTP::Redirect($redirect);
