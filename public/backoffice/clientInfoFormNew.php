<?php
global $isHMLO, $myFileInfo, $LMRId, $userRole, $isEF, $PCClientBackgroundInfoArray,
       $PCClientExperienceInfoArray, $clientFileListArray, $allowToEdit, $publicUser;
global $oBranch, $PCID, $isLO, $isMF, $isFU, $selClientId,
       $isHOALien, $userGroup, $propertyAddress,
       $propertyCity, $stateArray, $propertyState, $propertyZip,
       $occupancy, $GpropertyTypeKeyArray;
global $branchReferralCode, $tabIndex, $allowClientToCreateHMLOFile,
       $tempClientEmail,
       $tempClientEmail, $PCquickAppFieldsInfo,
       $processingCompanyId;
global $userTimeZone, $oldFPCID;

use models\composite\oBranch\getBranchHearAbout;
use models\composite\oClient\getPCClientEntityInfo;
use models\composite\proposalFormula;
use models\constants\gl\glAmountDesired;
use models\constants\gl\glBorrowerType;
use models\constants\gl\glCreditIssues;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glDate;
use models\constants\gl\glDisableClientImportPC;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\glFundsNeeded;
use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOPresentOccupancy;
use models\constants\gl\glLien1LoanTypeArray;
use models\constants\gl\glLien2LoanTypeArray;
use models\constants\gl\glMandatoryFieldForPC;
use models\constants\gl\glMortgageInvestorOwnerArray;
use models\constants\gl\glMortgageOwnerArray;
use models\constants\gl\glOccupancyArray;
use models\constants\gl\glPCID;
use models\constants\gl\glPropTypeArray;
use models\constants\GpropertyTypeNumbArray;
use models\constants\lien1TermsArray;
use models\constants\lien2TermsArray;
use models\constants\methodOfContactArray;
use models\constants\monthsBehindArray;
use models\constants\SMSServiceProviderArray;
use models\constants\timeZoneArray;
use models\Controllers\LMRequest\Property;
use models\cypher;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;

$glPropTypeArray = glPropTypeArray::$glPropTypeArray;
$SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
$methodOfContactArray = methodOfContactArray::$methodOfContactArray;
$timeZoneArray = timeZoneArray::$timeZoneArray;
$monthsBehindArray = monthsBehindArray::$monthsBehindArray;
$lien1TermsArray = lien1TermsArray::$lien1TermsArray;
$lien2TermsArray = lien2TermsArray::$lien2TermsArray;
$glLien1LoanTypeArray = glLien1LoanTypeArray::$glLien1LoanTypeArray;
$glLien2LoanTypeArray = glLien2LoanTypeArray::$glLien2LoanTypeArray;
$glMortgageOwnerArray = glMortgageOwnerArray::$glMortgageOwnerArray;
$glMortgageInvestorOwnerArray = glMortgageInvestorOwnerArray::$glMortgageInvestorOwnerArray;
$glOccupancyArray = glOccupancyArray::$glOccupancyArray;
$glCreditIssues = glCreditIssues::$glCreditIssues;
$glFundsNeeded = glFundsNeeded::$glFundsNeeded;
$glAmountDesired = glAmountDesired::$glAmountDesired;
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::$glHMLOCreditScoreRange;
$glHMLOPresentOccupancy = ($PCID == glPCID::PCID_PROD_CV3) ? glHMLOPresentOccupancy::$glHMLOPresentOccupancyCV3 :
    $glHMLOPresentOccupancy = glHMLOPresentOccupancy::$glHMLOPresentOccupancy;
$glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;
$glDisableClientImportPC = glDisableClientImportPC::$glDisableClientImportPC;
$glMandatoryFieldForPC = glMandatoryFieldForPC::$glMandatoryFieldForPC;
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange($PCID);
$ssn1 = '';
$ssn2 = '';
$ssn3 = '';
$borrowerFName = '';
$borrowerLName = '';
$maritalStatus = '';
$maidenName = '';
$spouseName = '';
$phoneNumber = '';
$phNo1 = '';
$phNo2 = '';
$phNo3 = '';
$ext = '';
$phNo1 = '';
$phNo2 = '';
$phNo3 = '';
$ext = '';
$altPhoneNumber = '';
$altPhNo1 = '';
$altPhNo2 = '';
$altPhNo3 = '';
$altExt = '';
$ssn1 = '';
$ssn2 = '';
$ssn3 = '';
$cellNumber = '';
$cellNo1 = '';
$cellNo2 = '';
$cellNo3 = '';
$fax = '';
$fax1 = '';
$fax2 = '';
$fax3 = '';
$workNumber = '';
$workNo1 = '';
$workNo2 = '';
$workNo3 = '';
$marriageDate = '';
$divorceDate = '';
$coBSsnNumber = '';
$coBSsn1 = '';
$coBSsn2 = '';
$coBSsn3 = '';
$coBorrowerDOB = '';
$coBPhoneNumber = '';
$coBPhNo1 = '';
$coBPhNo2 = '';
$coBPhNo3 = '';
$coBExt = '';
$coBAltPhoneNumber = '';
$coBAltPhNo1 = '';
$coBAltPhNo2 = '';
$coBAltPhNo3 = '';
$coBAltExt = '';
$workNoExt = '';
$REBroker = 'No';
$addBranchHearAbout = 0;
$coBCellNumber = '';
$coBCellNo1 = '';
$coBCellNo2 = '';
$coBCellNo3 = '';
$coBFax = '';
$coBFax1 = '';
$coBFax2 = '';
$coBFax3 = '';
$coBorrowerWorkNumber = '';
$coBWorkNo1 = '';
$coBWorkNo2 = '';
$coBWorkNo3 = '';
$coBWorkNoExt = '';
$servicer1 = '';
$servicer1Text = $originalLender1Text = $servicer2Text = $originalLender2Text = '- Type Name Here -';
$noOfMonthsBehind1 = '';
$noOfDaysBehind1 = '';
$mailingState = '';
$previousState = '';
$coBorrowerMailingState = '';
$coBorPreviousState = '';
$mortgageOwner1 = '';
$originalLender1 = '';
$lien1Payment = '';
$taxes1 = '';
$insurance1 = '';
$floodInsurance1 = '';
$mortgageInsurance1 = '';
$HOAFees1 = '';
$totalPayment = '';
$servicer2 = '';
$noOfMonthsBehind2 = '';
$noOfDaysBehind2 = '';
$originalLender2 = '';
$mortgageOwner2 = '';
$propertyCounty = '';
$coBorrowerCounty = '';
$propertyType = '';
$serviceProvider = '';
$phoneNumberArray = [];
$altPhoneNumberArray = [];
$cellNumberArray = [];
$faxArray = [];
$workNumberArray = [];
$coBSsnNumberArray = [];
$coBPhoneNumberArray = [];
$coBAltPhoneNumberArray = [];
$coBCellNumberArray = [];
$coBFaxArray = [];
$coBWorkNumberArray = [];
$propertyCountyInfo = [];
$coBorrowerCountyInfo = [];
$branchClientTypeInfo = [];
$brokerContactInfo = '';
$BrokerInfo = [];
$lien1LPMade = '';
$lien2LPMade = '';
$brokerName = '';
$brokerCompany = '';
$brokerEmail = '';
$brokerAddr = '';
$brokerAddress = '';
$brokerCity = '';
$brokerState = '';
$brokerZip = '';
$appStr = '';
$isCoBorrower = 0;
$leadSource = '';
$lien2Terms = '';
$lien1Terms = '';
$noteDate = '';
$SID = 0;
$mortgageInvestor1 = '';
$mortgageInvestor2 = '';
$borrowerTimeZone = '';
$coBorrowerTimeZone = '';
$coBServiceProvider = '';
$remainingMonths = '';
$noticeAccelerationDate = '';
$condominiumOrHOAFee = '0';
$bestTime = '';
$methodOfContact = '';
$borBackgroundExplanation = '';
$escrowAdvances = '';
$coBorBackgroundExplanation = '';
$projectedEscrowAdvances = '';
$proposalLateFees = '';
$mailingAddrAsPresent = 1;
$pastDueMtg = '';
$coBorMailingAddrAsPresent = 1;
$pastDueHOA = '';
$ssnNumber = '';
$HOA1ContactID = '';
$propertyManagementInfo = [];
$fileContacts = $QAInfo = [];
$HOPhone = '';
$HOPhNo1 = '';
$HOPhNo2 = '';
$HOPhExt = '';
$HOFax = '';
$HOPhNoArray = [];
$HOFaxArray = [];
$coMethodOfContact = '';
$nonBorrowerDOB = '';
$nonBorrowerSSNNumber = '';
$nonBorrowerSSNArray = [];
$coBestTime = '';
$tempNonborrowerSSN = '';
$isNonBorrower = 0;
$nonBorrrowerSSN1 = '';
$nonBorrrowerSSN2 = '';
$nonBorrrowerSSN3 = '';
$BGender = '';
$BRace = '';
$BEthnicity = '';
$PublishBInfo = '3';
$CBGender = '';
$noOfPeopleDependent = 0;
$CBRace = '';
$CBEthnicity = '';
$PublishCBInfo = '3';
$invoices = '';
$monthlyRevenue = '';
$majorityBusiness = '';
$ownersName = '';
$Owned = '';
$businessName = '';
$businessNameDBA = '';
$businessLegalName = '';
$businessTax = '';
$businessType = '';
$Website = '';
$Industry = '';
$businessConsumers = '';
$monthsBusiness = '';
$creditScore = '';
$grossRevenue = '';
$monthlyDepositVolume = '';
$bankBalance = '';
$invoices = '';
$invoiceAmount = '';
$requestedAmount = '';
$requestedTermLength = '';
$funding = '';
$businessCreditCards = '';
$businessFinancing = '';
$netProfit = '';
$amount = '';
$borResidedPresentAddr = 'NA';
$coBResidedPresentAddr = 'NA';
$ownCollateral = '';
$monthlyDebtPayments = '';
$fileLoanOriginationInfo = [];
$presentAddress = '';
$presentCity = '';
$presentState = '';
$presentZip = '';
$coBPresentAddress = '';
$coBPresentCity = $coBPresentState = $coBPresentZip = $coBNoOfDependent = '';
$borPresentPropType = $borMailingPropType = $borFormerPropType = '';
$coBPresentPropType = '';
$coBMailingPropType = '';
$coBFormerPropType = '';
$creditIssuesArray = [];
$fundsNeededReasonArray = [];
$haveEmployed = '';
$isFMInUSMilitary = '';
$ownBusiness = '';
$amtDesired = '';
$havePaystubW2ITReturn = '';
$FUInfo = [];
$creditIssues = '';
$fundsNeededReason = '';
$FUPropType = '';
$borCompanyName = '';
$borCreditScoreRange = '';
$borCreditScore = '';
$fileHMLOInfo = [];
$coBorCompanyName = '';
$coBorCreditScoreRange = '';
$coBorCreditScore = '';
$REBrokerFName = '';
$creditScoreRange = '';
$fileHMLOEntityInfo = [];
$midFicoScore = '';
$ENTITYnAME = '';
$ENTITYtYPE = '';
$borrowerType = '';
$eninO = '';
$ENTITYaDDRESS = '';
$ENTITYcITY = '';
$ENTITYsTATE = '';
$ENTITYzIP = '';
$ENTITYsTATEoFfORMATION = '';
$ENTITYnOTES = '';
$MEMBER1nAME = '';
$MEMBER1tITLE = '';
$ENTITYwEBSITE = '';
$MEMBER1oWNERSHIP = '';
$MEMBER2nAME = '';
$MEMBER2tITLE = '';
$MEMBER2oWNERSHIP = '';
$MEMBER3nAME = '';
$MEMBER3tITLE = '';
$MEMBER3oWNERSHIP = '';
$FILEhmlobACKgROUNDiNFO = [];
$FILEhmloeXPERIENCEiNFO = [];
$ISbORuscITIZEN = '';
$ISbORdECALREDbANKRUPTpASTyEARS = '';
$ISaNYcObORoUTSTANDINGjUDGEMENTS = '';
$HAScObORaNYaCTIVElAWSUITS = '';
$HAScObORpROPERTYtAXlIENS = '';
$HAScObORoBLIGATEDiNfORECLOSURE = '';
$IScObORpRESENLTYdELINQUENT = '';
$IScObORbORROWEDdOWNpAYMENT = '';
$IScObORiNTENDtOoCCUPYpROPaSpri = '';
$HAVEcObORoTHERfRAUDrELATEDcRIMES = '';
$IScObORpERSONALLYgUARANTEElOAN = '';
$BOReXPERIANsCORE = '';
$BOReQUIFAXsCORE = '';
$BORtRANSUNIONsCORE = '';
$CObOReXPERIANsCORE = '';
$CObOReQUIFAXsCORE = '';
$CObORtRANSUNIONsCORE = '';
$IScObORuscITIZEN = '';
$IScObORdECALREDbANKRUPTpASTyEARS = '';
$ISaNYbORoUTSTANDINGjUDGEMENTS = '';
$HASbORaNYaCTIVElAWSUITS = '';
$HASbORpROPERTYtAXlIENS = '';
$HASbORoBLIGATEDiNfORECLOSURE = '';
$ISbORpRESENLTYdELINQUENT = '';
$ISbORbORROWEDdOWNpAYMENT = '';
$ISbORiNTENDtOoCCUPYpROPaSpri = '';
$HAVEbORoTHERfRAUDrELATEDcRIMES = '';
$ISbORpERSONALLYgUARANTEElOAN = '';
$HAVEbORreiNVESTMENTeXPERIENCE = '';
$BORnOoFrepROPERTIEScOMPLETED = '';
$BORnOoFfLIPPINGeXPERIENCE = '';
$HAVEbORrEHABcONSTRUCTIONeXPERIENCE = '';
$BORnOoFyEARrEHABeXPERIENCE = '';
$BORrEHABpROPcOMPLETED = '';
$HAVEbORpROJECTcURRENTLYiNpROGRESS = '';
$BORnOoFpROJECTcURRENTLY = '';
$HAVEbORoWNiNVESTMENTpROPERTIES = '';
$BORnOoFoWNpROP = '';
$AREbORmEMBERoFiNVESTMENTcLUB = '';
$BORcLUBnAME = '';
$HAVEcObORreiNVESTMENTeXPERIENCE = '';
$CObORnOoFrepROPERTIEScOMPLETED = '';
$HAVEcObORrEHABcONSTRUCTIONeXPERIENCE = '';
$CObORnOoFyEARrEHABeXPERIENCE = '';
$CObORrEHABpROPcOMPLETED = '';
$HAVEcObORpROJECTcURRENTLYiNpROGRESS = '';
$CObORnOoFpROJECTcURRENTLY = '';
$HAVEcObORoWNiNVESTMENTpROPERTIES = '';
$CObORnOoFoWNpROP = '';
$AREcObORmEMBERoFiNVESTMENTcLUB = '';
$CObORcLUBnAME = '';
$BORreaDDRESS1 = '';
$BORoUTCOMEre1 = '';
$BORreaDDRESS2 = '';
$BORoUTCOMEre2 = '';
$BORreaDDRESS3 = '';
$BORoUTCOMEre3 = '';
$CObORoUTCOMEre3 = '';
$CObORreaDDRESS3 = '';
$CObORoUTCOMEre2 = '';
$CObORreaDDRESS2 = '';
$CObORoUTCOMEre1 = '';
$CObORreaDDRESS1 = '';
$BORrcoUTCOME1 = '';
$BORrcaDDRESS1 = '';
$BORrcoUTCOME2 = '';
$BORrcaDDRESS2 = '';
$BORrcoUTCOME3 = '';
$BORrcaDDRESS3 = '';
$CObORrcaDDRESS1 = '';
$CObORoUTCOMErc1 = '';
$CObORrcaDDRESS2 = '';
$CObORrcoUTCOME2 = '';
$CObORrcaDDRESS3 = '';
$CObORrcoUTCOME3 = '';
$SHOWreiNVESTMENTdISPoPT = $SHOWrcdISPoPT = $SHOWpROJECTiNpROGRESSdISPoPT = 'DISPLAY:NONE;';
$SHOWoWNiNVESTMENTdISPoPT = $SHOWmEMBERdISPoPT = 'DISPLAY:NONE;';
$SHOWcObORreiNVESTMENTdISPoPT = $SHOWCObORrcdISPoPT = $SHOWCObORpROJECTiNpROGRESSdISPoPT = 'DISPLAY:NONE;';
$SHOWCObORoWNiNVESTMENTdISPoPT = $SHOWCObORmEMBERdISPoPT = 'DISPLAY:NONE;';
$SHOWmEMBER2dISPoPT = $SHOWmEMBER3dISPoPT = 'DISPLAY:NONE;';
$SHOWbORROWEReNTITYdISPoPT = 'DISPLAY:NONE;';

$borDecalredBankruptExpln = $borOutstandingJudgementsExpln = $borActiveLawsuitsExpln = $borPropertyTaxLiensExpln = $borObligatedInForeclosureExpln = '';
$borDelinquentExpln = $borOtherFraudRelatedCrimesExpln = $borBorrowedDownPaymentExpln = '';
$borDecalredBankruptDispOpt = $borOutstandingJudgementsDispOpt = $borActiveLawsuitsDispOpt = $borPropertyTaxLiensDispOpt = $borObligatedInForeclosureDispOpt = 'display: none';
$borDelinquentDispOpt = $borOtherFraudRelatedCrimesDispOpt = $borBorrowedDownPaymentDispOpt = 'display: none';
$coBorDecalredBankruptExpln = $coBorOutstandingJudgementsExpln = $coBorActiveLawsuitsExpln = $coBorPropertyTaxLiensExpln = $coBorObligatedInForeclosureExpln = '';
$coBorDelinquentExpln = $coBorOtherFraudRelatedCrimesExpln = $coBorBorrowedDownPaymentExpln = '';
$borrowerUnderEntity = '';
$coBorDecalredBankruptDispOpt = $coBorOutstandingJudgementsDispOpt = $coBorActiveLawsuitsDispOpt = $coBorPropertyTaxLiensDispOpt = $coBorObligatedInForeclosureDispOpt = 'display: none';
$coBorDelinquentDispOpt = $coBorOtherFraudRelatedCrimesDispOpt = $coBorBorrowedDownPaymentDispOpt = 'display: none';
$memberCnt = 0;
$entityBillAddrIsPresent = '';
$entityBillAddress = '';
$entityBillCity = '';
$entityBillState = '';
$entityBillZip = '';
$entityLocation = '';
$sameAsEntityAddr = 0;
$propertyValuationDocInfo = [];

$docArray = [];
$fileAdditionalGuarantorsInfo = [];
$guarantorNotes = '';
$businessTypeEF = '';
$fileHMLONewLoanInfo = [];
$tradeName = '';
$crossCorporateGuarantor = '';
$noOfEmployees = '';
$grossAnnualRevenues = '';
$grossIncomeLastYear = '';
$netIncomeLastYear = '';
$grossIncome2YearsAgo = '';
$netIncome2YearsAgo = '';
$entityBusinessSell = '';
$entityBusinessType = '';
$businessDescription = '';
$merchantProcessingBankName = '';
$liquidAssets = '';
$brokerDealPoints = '';
$brokerDealFee = '';
$brokerQuotedinterestRate = '';
$accessWholesalePricing = '';
$businessEntityPhone = '';
$businessEntityFax = '';
$organizationalRef = '';
$haveBorProfLicences = $borProfLicence = '';
$showProfLicencesOpt = 'display:none;';
$haveCoBorProfLicences = '';
$coBorProfLicence = '';
$isAdditionalGuarantors = '';
$fullTimeRealEstateInvestor = '';
$clientExpProInfo = $clientGUExpInfo = [];
$clientDocsArray = [];
$fileExpFilpGroundUp = [];
$areBuilderDeveloper = '';
$isHMLOSelOpt = $isHMLO;

for ($j = 1; $j <= 10; $j++) {
    if ($j == 1) {
        ${'showMember' . $j . 'DispOpt'} = 'display:block';
    } else {
        ${'showMember' . $j . 'DispOpt'} = 'display:none';
    }
    ${'member' . $j . 'Name'} = '';
    ${'member' . $j . 'Title'} = '';
    ${'member' . $j . 'Ownership'} = '';
    ${'member' . $j . 'Address'} = '';
    ${'member' . $j . 'Phone'} = '';
    ${'member' . $j . 'Cell'} = '';
    ${'member' . $j . 'SSN'} = '';
    ${'member' . $j . 'DOB'} = '';
    ${'member' . $j . 'Email'} = '';
    ${'member' . $j . 'CreditScore'} = '';
}

if (count($myFileInfo) > 0) {
    if (array_key_exists('propertyCountyInfo', $myFileInfo)) $propertyCountyInfo = $myFileInfo['propertyCountyInfo'];
    /** Fetch all county for the particular property States **/
    if (array_key_exists('coBorrowerCountyInfo', $myFileInfo)) $coBorrowerCountyInfo = $myFileInfo['coBorrowerCountyInfo'];
    /** Fetch all county for the particular co-borrower mailing States **/
    if (array_key_exists('branchModuleInfo', $myFileInfo)) $moduleRequested = $myFileInfo['branchModuleInfo'];
    /** Fetch all Branch modules requested **/
    if (array_key_exists('branchClientTypeInfo', $myFileInfo)) $servicesRequested = $myFileInfo['branchClientTypeInfo'];
    /** Fetch all Branch services requested **/
    if (array_key_exists('BrokerInfo', $myFileInfo)) $BrokerInfo = $myFileInfo['BrokerInfo'];
    /** Fetch agent info **/
    if (array_key_exists('RESTInfo', $myFileInfo)) $RESTInfo = $myFileInfo['RESTInfo'];
    /** Fetch all REST Info **/
    if (array_key_exists('fileContacts', $myFileInfo)) $fileContacts = $myFileInfo['fileContacts'];
    /** Fetch all REST Info **/
    if (array_key_exists('QAInfo', $myFileInfo)) $QAInfo = $myFileInfo['QAInfo'];
    /** Fetch all QA Info **/
    if (array_key_exists('fileLoanOriginationInfo', $myFileInfo)) $fileLoanOriginationInfo = $myFileInfo['fileLoanOriginationInfo'];
    if (array_key_exists('propertyValuation', $myFileInfo)) $propertyValuationDocInfo = $myFileInfo['propertyValuation'];
    if (array_key_exists('docArray', $myFileInfo)) $docArray = $myFileInfo['docArray'];
    if (count($fileLoanOriginationInfo) > 0) {
        $borResidedPresentAddr = Strings::showField('borResidedPresentAddr', 'fileLoanOriginationInfo');
        $coBResidedPresentAddr = Strings::showField('coBResidedPresentAddr', 'fileLoanOriginationInfo');
    }
    if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['LMRClientTypeInfo'])) $LMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'][$LMRId];
    }
    /** Fetch file services **/
    if (array_key_exists('fileModuleInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['fileModuleInfo'])) $fileModuleInfo = $myFileInfo['fileModuleInfo'][$LMRId];
    }
    /** Fetch file modules **/
    if (array_key_exists('FUInfo', $myFileInfo)) $FUInfo = $myFileInfo['FUInfo'];
    /** Fetch all Funding Module Info **/

    if (array_key_exists('fileHMLOInfo', $myFileInfo)) $fileHMLOInfo = $myFileInfo['fileHMLOInfo'];
    if (array_key_exists('fileHMLOEntityInfo', $myFileInfo)) $fileHMLOEntityInfo = $myFileInfo['fileHMLOEntityInfo'];

    if (array_key_exists('fileHMLOBackGroundInfo', $myFileInfo)) $fileHMLOBackGroundInfo = $myFileInfo['fileHMLOBackGroundInfo'];

    if (array_key_exists('fileHMLOExperienceInfo', $myFileInfo)) $fileHMLOExperienceInfo = $myFileInfo['fileHMLOExperienceInfo'];
    if (array_key_exists('AddGuarantorsInfo', $myFileInfo)) $fileAdditionalGuarantorsInfo = $myFileInfo['AddGuarantorsInfo'];
    if (array_key_exists('fileHMLONewLoanInfo', $myFileInfo)) $fileHMLONewLoanInfo = $myFileInfo['fileHMLONewLoanInfo'];
    if (array_key_exists('clientDocsArray', $myFileInfo)) $clientDocsArray = $myFileInfo['clientDocsArray'];
    /** Fetch Client Docs **/
    if (array_key_exists('fileExpFilpGroundUp', $myFileInfo)) $fileExpFilpGroundUp = $myFileInfo['fileExpFilpGroundUp'];
    /** Fetch File Background **/
}

/** Fetch all Hard / Private Money LOS Module Info **/
$phoneNumber = Strings::cleanPhoneNo(Strings::showField('phoneNumber', 'LMRInfo'));
$altPhoneNumber = Strings::showField('altPhoneNumber', 'LMRInfo');
$cellNumber = Strings::showField('cellNumber', 'LMRInfo');
$fax = Strings::showField('fax', 'LMRInfo');
$workNumber = Strings::showField('workNumber', 'LMRInfo');
$ssnNumber = Strings::showField('ssnNumber', 'LMRInfo');
$coBSsnNumber = Strings::showField('coBSsnNumber', 'LMRInfo');
$coBPhoneNumber = Strings::showField('coBPhoneNumber', 'LMRInfo');
$coBAltPhoneNumber = Strings::showField('coBAltPhoneNumber', 'LMRInfo');
$coBCellNumber = Strings::showField('coBCellNumber', 'LMRInfo');
$coBFax = Strings::showField('coBFax', 'LMRInfo');
$coBorrowerWorkNumber = Strings::showField('coBorrowerWorkNumber', 'LMRInfo');
$servicer1 = Strings::showField('servicer1', 'LMRInfo');
$originalLender1 = Strings::showField('originalLender1', 'LMRInfo');
$mortgageOwner1 = Strings::showField('mortgageOwner1', 'LMRInfo');
$servicer2 = Strings::showField('servicer2', 'LMRInfo');
$originalLender2 = Strings::showField('originalLender2', 'LMRInfo');
$mortgageOwner2 = Strings::showField('mortgageOwner2', 'LMRInfo');
$marriageDate = Strings::showField('marriageDate', 'LMRInfo');
$divorceDate = Strings::showField('divorceDate', 'LMRInfo');
$isCoBorrower = Strings::showField('isCoBorrower', 'LMRInfo');
$coBorrowerDOB = Strings::showField('coBorrowerDOB', 'LMRInfo');
$mailingState = Strings::showField('mailingState', 'LMRInfo');
$previousState = Strings::showField('previousState', 'LMRInfo');
$coBorrowerMailingState = Strings::showField('coBorrowerMailingState', 'LMRInfo');
$coBorPreviousState = Strings::showField('coBorPreviousState', 'LMRInfo');
$noOfMonthsBehind1 = Strings::showField('noOfMonthsBehind1', 'LMRInfo');
$noOfMonthsBehind2 = Strings::showField('noOfMonthsBehind2', 'LMRInfo');
$propertyCounty = Strings::showField('propertyCounty', 'LMRInfo');
$coBorrowerCounty = Strings::showField('coBorrowerCounty', 'LMRInfo');
$propertyType = Strings::showField('propertyType', 'LMRInfo');
$loanType = Strings::showField('loanType', 'LMRInfo');
$loanType2 = Strings::showField('loanType2', 'LMRInfo');
$lien1Terms = Strings::showField('lien1Terms', 'LMRInfo');
$lien2Terms = Strings::showField('lien2Terms', 'LMRInfo');
$serviceProvider = Strings::showField('serviceProvider', 'LMRInfo');
$coBServiceProvider = Strings::showField('coBServiceProvider', 'LMRInfo');
$executiveId = Strings::showField('FBRID', 'LMRInfo');
$agentNumber = Strings::showField('brokerNumber', 'LMRInfo');
$LMRResponseId = Strings::showField('LMRResponseId', 'ResponseInfo');
$leadSource = Strings::showField('leadSource', 'ResponseInfo');
$lien1LPMade = Strings::showField('lien1LPMade', 'LMRInfo');
$lien2LPMade = Strings::showField('lien2LPMade', 'LMRInfo');
$mortgageInvestor1 = Strings::showField('mortgageInvestor1', 'LMRInfo');
$mortgageInvestor2 = Strings::showField('mortgageInvestor2', 'LMRInfo');
$escrowAdvances = Strings::showField('lien1ProposalEscrowShortage', 'proposalInfo');
$projectedEscrowAdvances = Strings::showField('projectedEscrowAdvances', 'incomeInfo');
$proposalLateFees = Strings::showField('lien1ProposalFeesAdminCosts', 'proposalInfo');
$pastDueMtg = Strings::showField('pastDueMtg', 'incomeInfo');
$pastDueHOA = Strings::showField('pastDueHOA', 'incomeInfo');
$noteDate = Strings::showField('noteDate', 'RESTInfo');
$SID = Strings::showField('SID', 'RESTInfo');
$noticeAccelerationDate = Strings::showField('noticeAccelerationDate', 'file2Info');
$methodOfContact = Strings::showField('methodOfContact', 'file2Info');
$coMethodOfContact = Strings::showField('coMethodOfContact', 'file2Info');
$borrowerTimeZone = Strings::showField('borrowerTimeZone', 'LMRInfo');
$coBorrowerTimeZone = Strings::showField('coBorrowerTimeZone', 'LMRInfo');
$ssnNumberArray = Strings::splitSSNNumber($ssnNumber);
$borrowerFName = Strings::showField('borrowerFName', 'LMRInfo');
$borrowerLName = Strings::showField('borrowerLName', 'LMRInfo');
$maritalStatus = Strings::showField('maritalStatus', 'LMRInfo');
$maidenName = Strings::showField('maidenName', 'LMRInfo');
$spouseName = Strings::showField('spouseName', 'LMRInfo');
$remainingMonths = Strings::showField('remainingMonths', 'LMRInfo');
$nonBorrowerDOB = Strings::showField('nonBorrowerDOB', 'file2Info');
$nonBorrowerSSNNumber = Strings::showField('nonBorrowerSSN', 'file2Info');
$isNonBorrower = Strings::showField('isNonBorrower', 'file2Info');
$mailingAddrAsPresent = Strings::showField('mailingAddrAsPresent', 'file2Info');
$coBorMailingAddrAsPresent = Strings::showField('coBorMailingAddrAsPresent', 'file2Info');
/** Fetch the LO Present Address Section **/
$presentAddress = Strings::showField('presentAddress', 'file2Info');
$presentCity = Strings::showField('presentCity', 'file2Info');
$presentState = Strings::showField('presentState', 'file2Info');
$presentZip = Strings::showField('presentZip', 'file2Info');
$coBPresentAddress = Strings::showField('coBPresentAddress', 'file2Info');
$coBPresentCity = Strings::showField('coBPresentCity', 'file2Info');
$coBPresentState = Strings::showField('coBPresentState', 'file2Info');
$coBPresentZip = Strings::showField('coBPresentZip', 'file2Info');
$coBNoOfDependent = Strings::showField('coBNoOfDependent', 'file2Info');
$borPresentPropType = Strings::showField('borPresentPropType', 'file2Info');
$borMailingPropType = Strings::showField('borMailingPropType', 'file2Info');
$borFormerPropType = Strings::showField('borFormerPropType', 'file2Info');
$coBPresentPropType = Strings::showField('coBPresentPropType', 'file2Info');
$coBMailingPropType = Strings::showField('coBMailingPropType', 'file2Info');
$coBFormerPropType = Strings::showField('coBFormerPropType', 'file2Info');
$guarantorNotes = Strings::showField('guarantorNotes', 'file2Info');
/** Fetch the Merchant Funding Section (March 25, 2016) **/
$majorityBusiness = Strings::showField('majorityBusiness', 'MFInfo');
$ownersName = Strings::showField('ownersName', 'MFInfo');
$Owned = Strings::showField('Owned', 'MFInfo');
$businessName = Strings::showField('businessName', 'MFInfo');
$businessNameDBA = Strings::showField('businessNameDBA', 'MFInfo');
$businessLegalName = Strings::showField('businessLegalName', 'MFInfo');
$businessTax = Strings::showField('businessTax', 'MFInfo');
$businessType = Strings::showField('businessType', 'MFInfo');
$Website = Strings::showField('Website', 'MFInfo');
$Industry = Strings::showField('Industry', 'MFInfo');
$businessConsumers = Strings::showField('businessConsumers', 'MFInfo');
$monthsBusiness = Strings::showField('monthsBusiness', 'MFInfo');
$monthlyRevenue = Strings::showField('monthlyRevenue', 'MFInfo');
$creditScore = Strings::showField('creditScore', 'MFInfo');
$grossRevenue = Strings::showField('grossRevenue', 'MFInfo');
$monthlyDepositVolume = Strings::showField('monthlyDepositVolume', 'MFInfo');
$bankBalance = Strings::showField('bankBalance', 'MFInfo');
$invoices = Strings::showField('invoices', 'MFInfo');
$invoiceAmount = Strings::showField('invoiceAmount', 'MFInfo');
$requestedAmount = Strings::showField('requestedAmount', 'MFInfo');
$requestedTermLength = Strings::showField('requestedTermLength', 'MFInfo');
$funding = Strings::showField('funding', 'MFInfo');
$businessCreditCards = Strings::showField('businessCreditCards', 'MFInfo');
$businessFinancing = Strings::showField('businessFinancing', 'MFInfo');
$netProfit = Strings::showField('netProfit', 'MFInfo');
$amount = Strings::showField('amount', 'MFInfo');
$ownCollateral = Strings::showField('ownCollateral', 'MFInfo');
$monthlyDebtPayments = Strings::showField('monthlyDebtPayments', 'MFInfo');
/** Fetch the Hard / Private Money LOS Modules (Nov 21, 2016) **/
$borCompanyName = Strings::showField('borCompanyName', 'fileHMLOInfo');
$borCreditScoreRange = Strings::showField('borCreditScoreRange', 'fileHMLOInfo');
$midFicoScore = Strings::showField('midFicoScore', 'fileHMLOInfo');
$borCreditScore = Strings::showField('borCreditScore', 'fileHMLOInfo');
$coBorCompanyName = Strings::showField('coBorCompanyName', 'fileHMLOInfo');
$coBorCreditScoreRange = Strings::showField('coBorCreditScoreRange', 'fileHMLOInfo');
$coBorCreditScore = Strings::showField('coBorCreditScore', 'fileHMLOInfo');
$borExperianScore = Strings::showField('borExperianScore', 'fileHMLOInfo');
$borEquifaxScore = Strings::showField('borEquifaxScore', 'fileHMLOInfo');
$borTransunionScore = Strings::showField('borTransunionScore', 'fileHMLOInfo');
$coBorExperianScore = Strings::showField('coBorExperianScore', 'fileHMLOInfo');
$coBorEquifaxScore = Strings::showField('coBorEquifaxScore', 'fileHMLOInfo');
$coBorTransunionScore = Strings::showField('coBorTransunionScore', 'fileHMLOInfo');

$entityName = Strings::showField('entityName', 'fileHMLOEntityInfo');
$entityType = Strings::showField('entityType', 'fileHMLOEntityInfo');
$borrowerType = Strings::showField('borrowerType', 'fileHMLOEntityInfo');
$ENINo = Strings::showField('ENINo', 'fileHMLOEntityInfo');
$entityAddress = Strings::showField('entityAddress', 'fileHMLOEntityInfo');
$entityCity = Strings::showField('entityCity', 'fileHMLOEntityInfo');
$entityState = Strings::showField('entityState', 'fileHMLOEntityInfo');
$entityZip = Strings::showField('entityZip', 'fileHMLOEntityInfo');
$entityStateOfFormation = Strings::showField('entityStateOfFormation', 'fileHMLOEntityInfo');
$businessEntityFax = Strings::showField('businessEntityFax', 'fileHMLOEntityInfo');
$businessEntityPhone = Strings::showField('businessEntityPhone', 'fileHMLOEntityInfo');
$entityNotes = Strings::showField('entityNotes', 'fileHMLOEntityInfo');
$borrowerUnderEntity = Strings::showField('borrowerUnderEntity', 'fileHMLOEntityInfo');
$entityWebsite = Strings::showField('entityWebsite', 'fileHMLOEntityInfo');

$entityBillAddress = Strings::showField('entityBillAddress', 'fileHMLOEntityInfo');
$entityBillCity = Strings::showField('entityBillCity', 'fileHMLOEntityInfo');
$entityBillState = Strings::showField('entityBillState', 'fileHMLOEntityInfo');
$entityBillZip = Strings::showField('entityBillZip', 'fileHMLOEntityInfo');
$entityLocation = Strings::showField('entityLocation', 'fileHMLOEntityInfo');
$sameAsEntityAddr = Strings::showField('sameAsEntityAddr', 'fileHMLOEntityInfo');
$businessTypeEF = Strings::showField('businessTypeEF', 'fileHMLOEntityInfo');

$tradeName = Strings::showField('tradeName', 'fileHMLOEntityInfo');
$crossCorporateGuarantor = Strings::showField('crossCorporateGuarantor', 'fileHMLOEntityInfo');
$noOfEmployees = Strings::showField('noOfEmployees', 'fileHMLOEntityInfo');
$grossAnnualRevenues = Strings::showField('grossAnnualRevenues', 'fileHMLOEntityInfo');
$grossIncomeLastYear = Strings::showField('grossIncomeLastYear', 'fileHMLOEntityInfo');
$netIncomeLastYear = Strings::showField('netIncomeLastYear', 'fileHMLOEntityInfo');
$grossIncome2YearsAgo = Strings::showField('grossIncome2YearsAgo', 'fileHMLOEntityInfo');
$netIncome2YearsAgo = Strings::showField('netIncome2YearsAgo', 'fileHMLOEntityInfo');
//$entityBusinessSell = Strings::showField('entityBusinessSell', 'fileHMLOEntityInfo');
$entityService = Strings::showField('entityService', 'fileHMLOEntityInfo');
$entityProduct = Strings::showField('entityProduct', 'fileHMLOEntityInfo');
//$entityBusinessType = Strings::showField('entityBusinessType', 'fileHMLOEntityInfo');
$entityB2B = Strings::showField('entityB2B', 'fileHMLOEntityInfo');
$entityB2C = Strings::showField('entityB2C', 'fileHMLOEntityInfo');
$businessDescription = Strings::showField('businessDescription', 'fileHMLOEntityInfo');
$merchantProcessingBankName = Strings::showField('merchantProcessingBankName', 'fileHMLOEntityInfo');
$organizationalRef = Strings::showField('organizationalRef', 'fileHMLOEntityInfo');

/*
		$member1Name			= Strings::showField('member1Name', 'fileHMLOEntityInfo');
        $member1Title			= Strings::showField('member1Title', 'fileHMLOEntityInfo');
		$member1Ownership		= Strings::showField('member1Ownership', 'fileHMLOEntityInfo');
		$member2Name			= Strings::showField('member2Name', 'fileHMLOEntityInfo');
        $member2Title			= Strings::showField('member2Title', 'fileHMLOEntityInfo');
		$member2Ownership		= Strings::showField('member2Ownership', 'fileHMLOEntityInfo');
		$member3Name			= Strings::showField('member3Name', 'fileHMLOEntityInfo');
        $member3Title			= Strings::showField('member3Title', 'fileHMLOEntityInfo');
		$member3Ownership		= Strings::showField('member3Ownership', 'fileHMLOEntityInfo');
*/
for ($j = 1; $j <= 10; $j++) {
    ${'member' . $j . 'Name'} = Strings::showField('member' . $j . 'Name', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Title'} = Strings::showField('member' . $j . 'Title', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Ownership'} = Strings::showField('member' . $j . 'Ownership', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Address'} = Strings::showField('member' . $j . 'Address', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Phone'} = Strings::showField('member' . $j . 'Phone', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Cell'} = Strings::showField('member' . $j . 'Cell', 'fileHMLOEntityInfo');
    ${'member' . $j . 'SSN'} = Strings::showField('member' . $j . 'SSN', 'fileHMLOEntityInfo');
    ${'member' . $j . 'DOB'} = Strings::showField('member' . $j . 'DOB', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Email'} = Strings::showField('member' . $j . 'Email', 'fileHMLOEntityInfo');
    ${'member' . $j . 'CreditScore'} = Strings::showField('member' . $j . 'CreditScore', 'fileHMLOEntityInfo');
}

$isBorUSCitizen = Strings::showField('isBorUSCitizen', 'fileHMLOBackGroundInfo');
$isBorDecalredBankruptPastYears = Strings::showField('isBorDecalredBankruptPastYears', 'fileHMLOBackGroundInfo');
$isAnyBorOutstandingJudgements = Strings::showField('isAnyBorOutstandingJudgements', 'fileHMLOBackGroundInfo');
$hasBorAnyActiveLawsuits = Strings::showField('hasBorAnyActiveLawsuits', 'fileHMLOBackGroundInfo');
$hasBorPropertyTaxLiens = Strings::showField('hasBorPropertyTaxLiens', 'fileHMLOBackGroundInfo');
$hasBorObligatedInForeclosure = Strings::showField('hasBorObligatedInForeclosure', 'fileHMLOBackGroundInfo');
$isBorPresenltyDelinquent = Strings::showField('isBorPresenltyDelinquent', 'fileHMLOBackGroundInfo');
$isBorBorrowedDownPayment = Strings::showField('isBorBorrowedDownPayment', 'fileHMLOBackGroundInfo');
$isBorIntendToOccupyPropAsPRI = Strings::showField('isBorIntendToOccupyPropAsPRI', 'fileHMLOBackGroundInfo');
$haveBorOtherFraudRelatedCrimes = Strings::showField('haveBorOtherFraudRelatedCrimes', 'fileHMLOBackGroundInfo');
$isBorPersonallyGuaranteeLoan = Strings::showField('isBorPersonallyGuaranteeLoan', 'fileHMLOBackGroundInfo');
$borBackgroundExplanation = Strings::showField('borBackgroundExplanation', 'fileHMLOBackGroundInfo');

$isCoBorUSCitizen = Strings::showField('isCoBorUSCitizen', 'fileHMLOBackGroundInfo');
$isCoBorDecalredBankruptPastYears = Strings::showField('isCoBorDecalredBankruptPastYears', 'fileHMLOBackGroundInfo');
$isAnyCoBorOutstandingJudgements = Strings::showField('isAnyCoBorOutstandingJudgements', 'fileHMLOBackGroundInfo');
$hasCoBorAnyActiveLawsuits = Strings::showField('hasCoBorAnyActiveLawsuits', 'fileHMLOBackGroundInfo');
$hasCoBorPropertyTaxLiens = Strings::showField('hasCoBorPropertyTaxLiens', 'fileHMLOBackGroundInfo');
$hasCoBorObligatedInForeclosure = Strings::showField('hasCoBorObligatedInForeclosure', 'fileHMLOBackGroundInfo');
$isCoBorPresenltyDelinquent = Strings::showField('isCoBorPresenltyDelinquent', 'fileHMLOBackGroundInfo');
$isCoBorBorrowedDownPayment = Strings::showField('isCoBorBorrowedDownPayment', 'fileHMLOBackGroundInfo');
$isCoBorIntendToOccupyPropAsPRI = Strings::showField('isCoBorIntendToOccupyPropAsPRI', 'fileHMLOBackGroundInfo');
$haveCoBorOtherFraudRelatedCrimes = Strings::showField('haveCoBorOtherFraudRelatedCrimes', 'fileHMLOBackGroundInfo');
$isCoBorPersonallyGuaranteeLoan = Strings::showField('isCoBorPersonallyGuaranteeLoan', 'fileHMLOBackGroundInfo');
$coBorBackgroundExplanation = Strings::showField('coBorBackgroundExplanation', 'fileHMLOBackGroundInfo');

$haveBorREInvestmentExperience = Strings::showField('haveBorREInvestmentExperience', 'fileHMLOExperienceInfo');
$borNoOfREPropertiesCompleted = Strings::showField('borNoOfREPropertiesCompleted', 'fileHMLOExperienceInfo');
$borNoOfFlippingExperience = Strings::showField('borNoOfFlippingExperience', 'fileHMLOExperienceInfo');
$haveBorRehabConstructionExperience = Strings::showField('haveBorRehabConstructionExperience', 'fileHMLOExperienceInfo');
$borNoOfYearRehabExperience = Strings::showField('borNoOfYearRehabExperience', 'fileHMLOExperienceInfo');
$borRehabPropCompleted = Strings::showField('borRehabPropCompleted', 'fileHMLOExperienceInfo');
$haveBorProjectCurrentlyInProgress = Strings::showField('haveBorProjectCurrentlyInProgress', 'fileHMLOExperienceInfo');
$borNoOfProjectCurrently = Strings::showField('borNoOfProjectCurrently', 'fileHMLOExperienceInfo');
$haveBorOwnInvestmentProperties = Strings::showField('haveBorOwnInvestmentProperties', 'fileHMLOExperienceInfo');
$borNoOfOwnProp = Strings::showField('borNoOfOwnProp', 'fileHMLOExperienceInfo');
$areBorMemberOfInvestmentClub = Strings::showField('areBorMemberOfInvestmentClub', 'fileHMLOExperienceInfo');
$borClubName = Strings::showField('borClubName', 'fileHMLOExperienceInfo');
$liquidAssets = Strings::showField('liquidAssets', 'fileHMLOExperienceInfo');
$areBuilderDeveloper = Strings::showField('areBuilderDeveloper', 'fileHMLOExperienceInfo');

$haveCoBorREInvestmentExperience = Strings::showField('haveCoBorREInvestmentExperience', 'fileHMLOExperienceInfo');
$coBorNoOfREPropertiesCompleted = Strings::showField('coBorNoOfREPropertiesCompleted', 'fileHMLOExperienceInfo');
$haveCoBorRehabConstructionExperience = Strings::showField('haveCoBorRehabConstructionExperience', 'fileHMLOExperienceInfo');
$coBorNoOfYearRehabExperience = Strings::showField('coBorNoOfYearRehabExperience', 'fileHMLOExperienceInfo');
$coBorRehabPropCompleted = Strings::showField('coBorRehabPropCompleted', 'fileHMLOExperienceInfo');
$haveCoBorProjectCurrentlyInProgress = Strings::showField('haveCoBorProjectCurrentlyInProgress', 'fileHMLOExperienceInfo');
$coBorNoOfProjectCurrently = Strings::showField('coBorNoOfProjectCurrently', 'fileHMLOExperienceInfo');
$haveCoBorOwnInvestmentProperties = Strings::showField('haveCoBorOwnInvestmentProperties', 'fileHMLOExperienceInfo');
$coBorNoOfOwnProp = Strings::showField('coBorNoOfOwnProp', 'fileHMLOExperienceInfo');
$areCoBorMemberOfInvestmentClub = Strings::showField('areCoBorMemberOfInvestmentClub', 'fileHMLOExperienceInfo');

$coBorClubName = Strings::showField('coBorClubName', 'fileHMLOExperienceInfo');
$borREAddress1 = Strings::showField('borREAddress1', 'fileHMLOExperienceInfo');
$borOutcomeRE1 = Strings::showField('borOutcomeRE1', 'fileHMLOExperienceInfo');
$borREAddress2 = Strings::showField('borREAddress2', 'fileHMLOExperienceInfo');
$borOutcomeRE2 = Strings::showField('borOutcomeRE2', 'fileHMLOExperienceInfo');
$borREAddress3 = Strings::showField('borREAddress3', 'fileHMLOExperienceInfo');
$borOutcomeRE3 = Strings::showField('borOutcomeRE3', 'fileHMLOExperienceInfo');
$coBorREAddress1 = Strings::showField('coBorREAddress1', 'fileHMLOExperienceInfo');
$coBorOutcomeRE1 = Strings::showField('coBorOutcomeRE1', 'fileHMLOExperienceInfo');
$coBorREAddress2 = Strings::showField('coBorREAddress2', 'fileHMLOExperienceInfo');
$coBorOutcomeRE2 = Strings::showField('coBorOutcomeRE2', 'fileHMLOExperienceInfo');
$coBorREAddress3 = Strings::showField('coBorREAddress3', 'fileHMLOExperienceInfo');
$coBorOutcomeRE3 = Strings::showField('coBorOutcomeRE3', 'fileHMLOExperienceInfo');

$borRCAddress1 = Strings::showField('borRCAddress1', 'fileHMLOExperienceInfo');
$borRCOutcome1 = Strings::showField('borRCOutcome1', 'fileHMLOExperienceInfo');
$borRCAddress2 = Strings::showField('borRCAddress2', 'fileHMLOExperienceInfo');
$borRCOutcome2 = Strings::showField('borRCOutcome2', 'fileHMLOExperienceInfo');
$borRCAddress3 = Strings::showField('borRCAddress3', 'fileHMLOExperienceInfo');
$borRCOutcome3 = Strings::showField('borRCOutcome3', 'fileHMLOExperienceInfo');

$coBorRCAddress1 = Strings::showField('coBorRCAddress1', 'fileHMLOExperienceInfo');
$coBorRCOutcome1 = Strings::showField('coBorRCOutcome1', 'fileHMLOExperienceInfo');
$coBorRCAddress2 = Strings::showField('coBorRCAddress2', 'fileHMLOExperienceInfo');
$coBorRCOutcome2 = Strings::showField('coBorRCOutcome2', 'fileHMLOExperienceInfo');
$coBorRCAddress3 = Strings::showField('coBorRCAddress3', 'fileHMLOExperienceInfo');
$coBorRCOutcome3 = Strings::showField('coBorRCOutcome3', 'fileHMLOExperienceInfo');
$haveBorProfLicences = Strings::showField('haveBorProfLicences', 'fileHMLOExperienceInfo');
$borProfLicence = Strings::showField('borProfLicence', 'fileHMLOExperienceInfo');
$haveCoBorProfLicences = Strings::showField('haveCoBorProfLicences', 'fileHMLOExperienceInfo');
$coBorProfLicence = Strings::showField('coBorProfLicence', 'fileHMLOExperienceInfo');
$fullTimeRealEstateInvestor = Strings::showField('fullTimeRealEstateInvestor', 'fileHMLOExperienceInfo');
$coFullTimeRealEstateInvestor = Strings::showField('coFullTimeRealEstateInvestor', 'fileHMLOExperienceInfo');

if ($haveBorProfLicences == 'Yes') {
    $showProfLicencesOpt = 'display:block;';
}
$borDecalredBankruptExpln = Strings::showField('borDecalredBankruptExpln', 'fileHMLOBackGroundInfo');
$borOutstandingJudgementsExpln = Strings::showField('borOutstandingJudgementsExpln', 'fileHMLOBackGroundInfo');
$borActiveLawsuitsExpln = Strings::showField('borActiveLawsuitsExpln', 'fileHMLOBackGroundInfo');
$borPropertyTaxLiensExpln = Strings::showField('borPropertyTaxLiensExpln', 'fileHMLOBackGroundInfo');
$borObligatedInForeclosureExpln = Strings::showField('borObligatedInForeclosureExpln', 'fileHMLOBackGroundInfo');
$borDelinquentExpln = Strings::showField('borDelinquentExpln', 'fileHMLOBackGroundInfo');
$borOtherFraudRelatedCrimesExpln = Strings::showField('borOtherFraudRelatedCrimesExpln', 'fileHMLOBackGroundInfo');
$borBorrowedDownPaymentExpln = Strings::showField('borBorrowedDownPaymentExpln', 'fileHMLOBackGroundInfo');

$coBorDecalredBankruptExpln = Strings::showField('coBorDecalredBankruptExpln', 'fileHMLOBackGroundInfo');
$coBorOutstandingJudgementsExpln = Strings::showField('coBorOutstandingJudgementsExpln', 'fileHMLOBackGroundInfo');
$coBorActiveLawsuitsExpln = Strings::showField('coBorActiveLawsuitsExpln', 'fileHMLOBackGroundInfo');
$coBorPropertyTaxLiensExpln = Strings::showField('coBorPropertyTaxLiensExpln', 'fileHMLOBackGroundInfo');
$coBorObligatedInForeclosureExpln = Strings::showField('coBorObligatedInForeclosureExpln', 'fileHMLOBackGroundInfo');
$coBorDelinquentExpln = Strings::showField('coBorDelinquentExpln', 'fileHMLOBackGroundInfo');
$coBorOtherFraudRelatedCrimesExpln = Strings::showField('coBorOtherFraudRelatedCrimesExpln', 'fileHMLOBackGroundInfo');
$coBorBorrowedDownPaymentExpln = Strings::showField('coBorBorrowedDownPaymentExpln', 'fileHMLOBackGroundInfo');

$isAdditionalGuarantors = Strings::showField('isAdditionalGuarantors', 'fileHMLOPropertyInfo');

$isAdditionalGuarantorsDisp = 'display: none;';
if ($isAdditionalGuarantors == 'Yes') $isAdditionalGuarantorsDisp = 'display: block;';

if ($userRole == 'Client' && $LMRId == 0 && $isHMLO == 1 || $isEF == 1) { // Client Info For New Client Create

    if (count($PCClientBackgroundInfoArray) > 0) {
        $isBorUSCitizen = $PCClientBackgroundInfoArray['isBorUSCitizen'];
        $isBorDecalredBankruptPastYears = $PCClientBackgroundInfoArray['isBorDecalredBankruptPastYears'];
        $isAnyBorOutstandingJudgements = $PCClientBackgroundInfoArray['isAnyBorOutstandingJudgements'];
        $hasBorAnyActiveLawsuits = $PCClientBackgroundInfoArray['hasBorAnyActiveLawsuits'];
        $hasBorPropertyTaxLiens = $PCClientBackgroundInfoArray['hasBorPropertyTaxLiens'];
        $hasBorObligatedInForeclosure = $PCClientBackgroundInfoArray['hasBorObligatedInForeclosure'];
        $isBorPresenltyDelinquent = $PCClientBackgroundInfoArray['isBorPresenltyDelinquent'];
        $isBorBorrowedDownPayment = $PCClientBackgroundInfoArray['isBorBorrowedDownPayment'];
        $isBorIntendToOccupyPropAsPRI = $PCClientBackgroundInfoArray['isBorIntendToOccupyPropAsPRI'];
        $haveBorOtherFraudRelatedCrimes = $PCClientBackgroundInfoArray['haveBorOtherFraudRelatedCrimes'];
        $isBorPersonallyGuaranteeLoan = $PCClientBackgroundInfoArray['isBorPersonallyGuaranteeLoan'];
        $borBackgroundExplanation = $PCClientBackgroundInfoArray['borBackgroundExplanation'];

        $borDecalredBankruptExpln = $PCClientBackgroundInfoArray['borDecalredBankruptExpln'];
        $borOutstandingJudgementsExpln = $PCClientBackgroundInfoArray['borOutstandingJudgementsExpln'];
        $borActiveLawsuitsExpln = $PCClientBackgroundInfoArray['borActiveLawsuitsExpln'];
        $borPropertyTaxLiensExpln = $PCClientBackgroundInfoArray['borPropertyTaxLiensExpln'];
        $borObligatedInForeclosureExpln = $PCClientBackgroundInfoArray['borObligatedInForeclosureExpln'];
        $borDelinquentExpln = $PCClientBackgroundInfoArray['borDelinquentExpln'];
        $borOtherFraudRelatedCrimesExpln = $PCClientBackgroundInfoArray['borOtherFraudRelatedCrimesExpln'];
        $borBorrowedDownPaymentExpln = $PCClientBackgroundInfoArray['borBorrowedDownPaymentExpln'];
    }

    if (count($PCClientExperienceInfoArray) > 0) {
        $haveBorREInvestmentExperience = $PCClientExperienceInfoArray['haveBorREInvestmentExperience'];
        $borNoOfREPropertiesCompleted = $PCClientExperienceInfoArray['borNoOfREPropertiesCompleted'];
        $borNoOfFlippingExperience = $PCClientExperienceInfoArray['borNoOfFlippingExperience'];
        $haveBorRehabConstructionExperience = $PCClientExperienceInfoArray['haveBorRehabConstructionExperience'];
        $borNoOfYearRehabExperience = $PCClientExperienceInfoArray['borNoOfYearRehabExperience'];
        $borRehabPropCompleted = $PCClientExperienceInfoArray['borRehabPropCompleted'];
        $haveBorProjectCurrentlyInProgress = $PCClientExperienceInfoArray['haveBorProjectCurrentlyInProgress'];
        $borNoOfProjectCurrently = $PCClientExperienceInfoArray['borNoOfProjectCurrently'];
        $haveBorOwnInvestmentProperties = $PCClientExperienceInfoArray['haveBorOwnInvestmentProperties'];
        $borNoOfOwnProp = $PCClientExperienceInfoArray['borNoOfOwnProp'];
        $areBorMemberOfInvestmentClub = $PCClientExperienceInfoArray['areBorMemberOfInvestmentClub'];
        $borClubName = $PCClientExperienceInfoArray['borClubName'];

        $borREAddress1 = $PCClientExperienceInfoArray['borREAddress1'];
        $borOutcomeRE1 = $PCClientExperienceInfoArray['borOutcomeRE1'];
        $borREAddress2 = $PCClientExperienceInfoArray['borREAddress2'];
        $borOutcomeRE2 = $PCClientExperienceInfoArray['borOutcomeRE2'];
        $borREAddress3 = $PCClientExperienceInfoArray['borREAddress3'];
        $borOutcomeRE3 = $PCClientExperienceInfoArray['borOutcomeRE3'];

        $borRCAddress1 = $PCClientExperienceInfoArray['borRCAddress1'];
        $borRCOutcome1 = $PCClientExperienceInfoArray['borRCOutcome1'];
        $borRCAddress2 = $PCClientExperienceInfoArray['borRCAddress2'];
        $borRCOutcome2 = $PCClientExperienceInfoArray['borRCOutcome2'];
        $borRCAddress3 = $PCClientExperienceInfoArray['borRCAddress3'];
        $borRCOutcome3 = $PCClientExperienceInfoArray['borRCOutcome3'];
    }
}

if ($haveBorREInvestmentExperience == 'Yes') {
    $showREInvestmentDispOpt = 'display:block;';
}

if ($haveBorRehabConstructionExperience == 'Yes') {
    $showRCDispOpt = 'display:block;';
}

if ($haveBorProjectCurrentlyInProgress == 'Yes') {
    $showProjectInProgressDispOpt = 'display:block;';
}

if ($haveBorOwnInvestmentProperties == 'Yes') {
    $showOwnInvestmentDispOpt = 'display:block;';
}

if ($areBorMemberOfInvestmentClub == 'Yes') {
    $showMemberDispOpt = 'display:block;';
}

if ($isBorDecalredBankruptPastYears == 'Yes') {
    $borDecalredBankruptDispOpt = 'display: block;';
}
if ($isAnyBorOutstandingJudgements == 'Yes') {
    $borOutstandingJudgementsDispOpt = 'display: block;';
}
if ($hasBorAnyActiveLawsuits == 'Yes') {
    $borActiveLawsuitsDispOpt = 'display: block;';
}
if ($hasBorPropertyTaxLiens == 'Yes') {
    $borPropertyTaxLiensDispOpt = 'display: block;';
}
if ($hasBorObligatedInForeclosure == 'Yes') {
    $borObligatedInForeclosureDispOpt = 'display: block;';
}
if ($isBorPresenltyDelinquent == 'Yes') {
    $borDelinquentDispOpt = 'display: block;';
}
if ($haveBorOtherFraudRelatedCrimes == 'Yes') {
    $borOtherFraudRelatedCrimesDispOpt = 'display: block;';
}
if ($isBorBorrowedDownPayment == 'Yes') {
    $borBorrowedDownPaymentDispOpt = 'display: block;';
}

if ($isCoBorDecalredBankruptPastYears == 'Yes') {
    $coBorDecalredBankruptDispOpt = 'display: block;';
}
if ($isAnyCoBorOutstandingJudgements == 'Yes') {
    $coBorOutstandingJudgementsDispOpt = 'display: block;';
}
if ($hasCoBorAnyActiveLawsuits == 'Yes') {
    $coBorActiveLawsuitsDispOpt = 'display: block;';
}
if ($hasCoBorPropertyTaxLiens == 'Yes') {
    $coBorPropertyTaxLiensDispOpt = 'display: block;';
}
if ($hasCoBorObligatedInForeclosure == 'Yes') {
    $coBorObligatedInForeclosureDispOpt = 'display: block;';
}
if ($isCoBorPresenltyDelinquent == 'Yes') {
    $coBorDelinquentDispOpt = 'display: block;';
}
if ($haveCoBorOtherFraudRelatedCrimes == 'Yes') {
    $coBorOtherFraudRelatedCrimesDispOpt = 'display: block;';
}
if ($isCoBorBorrowedDownPayment == 'Yes') {
    $coBorBorrowedDownPaymentDispOpt = 'display: block;';
}

if ($haveCoBorREInvestmentExperience == 'Yes') {
    $showCoBorREInvestmentDispOpt = 'display:block;';
}

if ($haveCoBorRehabConstructionExperience == 'Yes') {
    $showcoBorRCDispOpt = 'display:block;';
}

if ($haveCoBorProjectCurrentlyInProgress == 'Yes') {
    $showcoBorProjectInProgressDispOpt = 'display:block;';
}

if ($haveCoBorOwnInvestmentProperties == 'Yes') {
    $showcoBorOwnInvestmentDispOpt = 'display:block;';
}

if ($areCoBorMemberOfInvestmentClub == 'Yes') {
    $showcoBorMemberDispOpt = 'display:block;';
}
/*
		if($member2Name !='' || $member2Title !='' || $member2Ownership >0) {
			$showMember2DispOpt = 'display: block';
		}

		if($member3Name !='' || $member3Title !='' || $member3Ownership >0) {
			$showMember3DispOpt = 'display: block';
		}
*/
for ($m = 1; $m <= 10; $m++) {
    if (${'member' . $m . 'Name'} != '' || ${'member' . $m . 'Title'} != '' || ${'member' . $m . 'Ownership'} > 0) {
        ${'showMember' . $m . 'DispOpt'} = 'display: block';
        $memberCnt++;
    }
}

for ($j = 1; $j <= 10; $j++) {
    ${'member' . $j . 'Name'} = Strings::showField('member' . $j . 'Name', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Title'} = Strings::showField('member' . $j . 'Title', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Ownership'} = Strings::showField('member' . $j . 'Ownership', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Address'} = Strings::showField('member' . $j . 'Address', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Phone'} = Strings::showField('member' . $j . 'Phone', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Cell'} = Strings::showField('member' . $j . 'Cell', 'fileHMLOEntityInfo');
    ${'member' . $j . 'SSN'} = Strings::showField('member' . $j . 'SSN', 'fileHMLOEntityInfo');
    ${'member' . $j . 'DOB'} = Strings::showField('member' . $j . 'DOB', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Email'} = Strings::showField('member' . $j . 'Email', 'fileHMLOEntityInfo');
    ${'member' . $j . 'CreditScore'} = Strings::showField('member' . $j . 'CreditScore', 'fileHMLOEntityInfo');

    if (${'member' . $j . 'Name'} != '' || ${'member' . $j . 'Title'} != '' || ${'member' . $j . 'Ownership'} > 0 || $j == 1) {
        ${'showMember' . $j . 'DispOpt'} = 'display : block';
    } else {
        ${'showMember' . $j . 'DispOpt'} = 'display:none';
    }
}

$showBorrowerEntityTBLOpt = 'display: none';
if ($borrowerType == glBorrowerType::BORROWER_TYPE_ENTITY) {
    $showBorrowerEntityDispOpt = 'display: block';
    $showBorrowerEntityTBLOpt = 'display: table';
}

if ($borrowerUnderEntity == 'Yes' && $sameAsEntityAddr != 1) {
    $showBillEntityDispOpt = 'display: block';
} else {
    $showBillEntityDispOpt = 'display: none';
}

if (trim($nonBorrowerSSNNumber) != '') $nonBorrowerSSNArray = Strings::splitSSNNumber($nonBorrowerSSNNumber);
if (count($FUInfo) > 0) {
    $haveEmployed = Strings::showField('haveEmployed', 'FUInfo');
    $havePaystubW2ITReturn = Strings::showField('havePaystubW2ITReturn', 'FUInfo');
    $isFMInUSMilitary = Strings::showField('isFMInUSMilitary', 'FUInfo');
    $ownBusiness = Strings::showField('ownBusiness', 'FUInfo');
    $creditIssues = Strings::showField('creditIssues', 'FUInfo');
    $fundsNeededReason = Strings::showField('fundsNeededReason', 'FUInfo');
    $FUPropType = Strings::showField('propType', 'FUInfo');
    $amtDesired = Strings::showField('amtDesired', 'FUInfo');
    if (trim($creditIssues) != '') {
        $creditIssuesArray = explode(',', $creditIssues);
    }
    if (trim($fundsNeededReason) != '') {
        $fundsNeededReasonArray = explode(',', $fundsNeededReason);
    }
}
/*
        if ($borrowerTimeZone == '') {
            $borrowerTimeZone = recursive_array_search($mailingState, $stateTimeZone);
        }
        if ($coBorrowerTimeZone == '') {
            $coBorrowerTimeZone = recursive_array_search($coBorrowerMailingState, $stateTimeZone);
        }
*/
$HOPhone = '';
$HOFax = '';
$HOPhNoArray = [];
$HOPhone = Strings::showField('HOPhone', 'listingRealtorInfo');
$HOFax = Strings::showField('HOFax', 'listingRealtorInfo');
$HOPhNoArray = Strings::splitPhoneNumber($HOPhone);
$HOPhNo1 = '';
$HOPhNo2 = '';
$HOPhNo3 = '';
if (count($HOPhNoArray) > 0) {
    $HOPhNo1 = $HOPhNoArray['No1'];
    $HOPhNo2 = $HOPhNoArray['No2'];
    $HOPhNo3 = $HOPhNoArray['No3'];
    $HOPhExt = substr($HOPhone, 10, 5);
}
$HOFaxNo1 = '';
$HOFaxNo2 = '';
$HOFaxNo3 = '';
$HOFaxArray = Strings::splitPhoneNumber($HOFax);
if (count($HOFaxArray) > 0) {
    $HOFaxNo1 = $HOFaxArray['No1'];
    $HOFaxNo2 = $HOFaxArray['No2'];
    $HOFaxNo3 = $HOFaxArray['No3'];
}
if (count($nonBorrowerSSNArray) > 0) {
    $nonBorrrowerSSN1 = trim($nonBorrowerSSNArray['No1']);
    $nonBorrrowerSSN2 = trim($nonBorrowerSSNArray['No2']);
    $nonBorrrowerSSN3 = trim($nonBorrowerSSNArray['No3']);
}
$tempNonborrowerSSN = $nonBorrrowerSSN1 . $nonBorrrowerSSN2 . $nonBorrrowerSSN3;

$altPhoneNumberArray = Strings::splitPhoneNumber($altPhoneNumber);
if (count($altPhoneNumberArray) > 0) {
    $altPhNo1 = trim($altPhoneNumberArray['No1']);
    $altPhNo2 = trim($altPhoneNumberArray['No2']);
    $altPhNo3 = trim($altPhoneNumberArray['No3']);
    $altExt = trim($altPhoneNumberArray['Ext']);
}
$workNumberArray = Strings::splitPhoneNumber($workNumber);
if (count($workNumberArray) > 0) {
    $workNo1 = trim($workNumberArray['No1']);
    $workNo2 = trim($workNumberArray['No2']);
    $workNo3 = trim($workNumberArray['No3']);
    $workNoExt = trim($workNumberArray['Ext']);
}
$coBPhoneNumberArray = Strings::splitPhoneNumber($coBPhoneNumber);
if (count($coBPhoneNumberArray) > 0) {
    $coBPhNo1 = trim($coBPhoneNumberArray['No1']);
    $coBPhNo2 = trim($coBPhoneNumberArray['No2']);
    $coBPhNo3 = trim($coBPhoneNumberArray['No3']);
    $coBExt = trim($coBPhoneNumberArray['Ext']);
}
$coBAltPhoneNumberArray = Strings::splitPhoneNumber($coBAltPhoneNumber);
if (count($coBAltPhoneNumberArray) > 0) {
    $coBAltPhNo1 = trim($coBAltPhoneNumberArray['No1']);
    $coBAltPhNo2 = trim($coBAltPhoneNumberArray['No2']);
    $coBAltPhNo3 = trim($coBAltPhoneNumberArray['No3']);
    $coBAltExt = trim($coBAltPhoneNumberArray['Ext']);
}
$coBCellNumberArray = Strings::splitPhoneNumber($coBCellNumber);
if (count($coBCellNumberArray) > 0) {
    $coBCellNo1 = trim($coBCellNumberArray['No1']);
    $coBCellNo2 = trim($coBCellNumberArray['No2']);
    $coBCellNo3 = trim($coBCellNumberArray['No3']);
}
$coBFaxArray = Strings::splitPhoneNumber($coBFax);
if (count($coBFaxArray) > 0) {
    $coBFax1 = trim($coBFaxArray['No1']);
    $coBFax2 = trim($coBFaxArray['No2']);
    $coBFax3 = trim($coBFaxArray['No3']);
}
$coBWorkNumberArray = Strings::splitPhoneNumber($coBorrowerWorkNumber);
if (count($coBWorkNumberArray) > 0) {
    $coBWorkNo1 = trim($coBWorkNumberArray['No1']);
    $coBWorkNo2 = trim($coBWorkNumberArray['No2']);
    $coBWorkNo3 = trim($coBWorkNumberArray['No3']);
    $coBWorkNoExt = trim($coBWorkNumberArray['Ext']);
}

$coBSsnNumberArray = Strings::splitSSNNumber($coBSsnNumber);
if (count($coBSsnNumberArray) > 0) {
    $coBSsn1 = trim($coBSsnNumberArray['No1']);
    $coBSsn2 = trim($coBSsnNumberArray['No2']);
    $coBSsn3 = trim($coBSsnNumberArray['No3']);
}

$inDBArray['lastPaymentMade'] = Strings::showField('lien1LPMade', 'LMRInfo');
$noOfDaysBehind1 = Integers::calculateNoOfDaysBehind($inDBArray);
/** No of days behind calculation **/
$inDBArray['lastPaymentMade'] = Strings::showField('lien2LPMade', 'LMRInfo');
$noOfDaysBehind2 = Integers::calculateNoOfDaysBehind($inDBArray);
/** No of days behind calculation **/
if ($noOfDaysBehind1 > 0) {
} else {
    $noOfDaysBehind1 = '';
}
if ($noOfDaysBehind2 > 0) {
} else {
    $noOfDaysBehind2 = '';
}
if (Dates::IsEmpty($coBorrowerDOB)) {
    $coBorrowerDOB = '';
} else {
    $coBorrowerDOB = Dates::formatDateWithRE($coBorrowerDOB, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($nonBorrowerDOB)) {
    $nonBorrowerDOB = '';
} else {
    $nonBorrowerDOB = Dates::formatDateWithRE($nonBorrowerDOB, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($lien1LPMade)) {
    $lien1LPMade = '';
} else {
    $lien1LPMade = Dates::formatDateWithRE($lien1LPMade, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($lien2LPMade)) {
    $lien2LPMade = '';
} else {
    $lien2LPMade = Dates::formatDateWithRE($lien2LPMade, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($noteDate)) {
    $noteDate = '';
} else {
    $noteDate = Dates::formatDateWithRE($noteDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($noticeAccelerationDate)) {
    $noticeAccelerationDate = '';
} else {
    $noticeAccelerationDate = Dates::formatDateWithRE($noticeAccelerationDate, 'YMD', 'm/d/Y');
}
$addBranchHearAbout = Strings::showField('addBranchHearAbout', 'BranchInfo');
$lien1Payment = Strings::showField('lien1Payment', 'LMRInfo');
$taxes1 = Strings::showField('taxes1', 'incomeInfo');
$insurance1 = Strings::showField('insurance1', 'incomeInfo');
$mortgageInsurance1 = Strings::showField('mortgageInsurance1', 'incomeInfo');
$HOAFees1 = Strings::showField('HOAFees1', 'incomeInfo');
$floodInsurance1 = Strings::showField('floodInsurance1', 'incomeInfo');
$totalPayment = Strings::replaceCommaValues($lien1Payment)
    + Strings::replaceCommaValues($taxes1)
    + Strings::replaceCommaValues($insurance1)
    + Strings::replaceCommaValues($mortgageInsurance1)
    + Strings::replaceCommaValues($HOAFees1)
    + Strings::replaceCommaValues($floodInsurance1);

if (count($clientFileListArray) > 0 && $userRole == 'Client' && $LMRId == 0 && $isHMLO == 1 && $isEF == 1) { // Client Info For New Client Create
    $borrowerFName = $clientFileListArray['borrowerName'];
    $borrowerLName = $clientFileListArray['borrowerLName'];
    $phoneNumber = $clientFileListArray['phoneNumber'];
    $cellNumber = $clientFileListArray['cellNumber'];
    $fax = $clientFileListArray['fax'];
    $maritalStatus = $clientFileListArray['maritalStatus'];
    $marriageDate = $clientFileListArray['marriageDate'];
    $divorceDate = $clientFileListArray['divorceDate'];
    $maidenName = $clientFileListArray['maidenName'];
    $spouseName = $clientFileListArray['spouseName'];
    $borrowerDOB = $clientFileListArray['borrowerDOB'];
    $ssnNumber = $clientFileListArray['ssnNumber'];
    $serviceProvider = $clientFileListArray['serviceProvider'];
    $borCreditScoreRange = $clientFileListArray['borCreditScoreRange'];
    $midFicoScore = $clientFileListArray['midFicoScore'];
    $borExperianScore = $clientFileListArray['borExperianScore'];
    $borEquifaxScore = $clientFileListArray['borEquifaxScore'];
    $borTransunionScore = $clientFileListArray['borTransunionScore'];
    $borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'YMD', 'm/d/Y');
}

$phoneNumberArray = Strings::splitPhoneNumber($phoneNumber);
if (count($phoneNumberArray) > 0) {
    $phNo1 = trim($phoneNumberArray['No1']);
    $phNo2 = trim($phoneNumberArray['No2']);
    $phNo3 = trim($phoneNumberArray['No3']);
    $ext = trim($phoneNumberArray['Ext']);
}

$cellNumberArray = Strings::splitPhoneNumber($cellNumber);
if (count($cellNumberArray) > 0) {
    $cellNo1 = trim($cellNumberArray['No1']);
    $cellNo2 = trim($cellNumberArray['No2']);
    $cellNo3 = trim($cellNumberArray['No3']);
}

$marriageDate = Dates::formatDateWithRE($marriageDate, 'YMD', 'm/d/Y');
$divorceDate = Dates::formatDateWithRE($divorceDate, 'YMD', 'm/d/Y');

$ssnNumberArray = Strings::splitSSNNumber($ssnNumber);
if (count($ssnNumberArray) > 0) {
    $ssn1 = trim($ssnNumberArray['No1']);
    $ssn2 = trim($ssnNumberArray['No2']);
    $ssn3 = trim($ssnNumberArray['No3']);
}

$faxArray = Strings::splitPhoneNumber($fax);
if (count($faxArray) > 0) {
    $fax1 = trim($faxArray['No1']);
    $fax2 = trim($faxArray['No2']);
    $fax3 = trim($faxArray['No3']);
}

if ($memberCnt == 0) $showMember1DispOpt = 'display: block';

if (trim($mailingAddrAsPresent) == '') $mailingAddrAsPresent = 1;
if (trim($coBorMailingAddrAsPresent) == '') $coBorMailingAddrAsPresent = 1;
if ($isCoBorrower == '') $isCoBorrower = 0;
if ($isNonBorrower == '') $isNonBorrower = 0;
if (trim($servicer1) == '' && $allowToEdit) $servicer1 = $servicer1Text;
if (trim($originalLender1) == '' && $allowToEdit) $originalLender1 = $originalLender1Text;
if (trim($servicer2) == '' && $allowToEdit) $servicer2 = $servicer2Text;
if (trim($originalLender2) == '' && $allowToEdit) $originalLender2 = $originalLender2Text;
if ($publicUser == 1 || $isHMLO == 1 || $isEF == 1) {
    $branchHAInfoArray = [];
    if ($executiveId > 0) {
        $inArray['LMRAEID'] = $executiveId;
        $branchHearAboutInfoArray = getBranchHearAbout::getReport($inArray);
        if (count($branchHearAboutInfoArray) > 0) {
            if (array_key_exists($executiveId, $branchHearAboutInfoArray)) {
                $branchHAInfoArray = $branchHearAboutInfoArray[$executiveId];
            }
        }
    }
}

if ($remainingMonths > 0) {
} else {
    /** Calculate Remaining months **/
    $remainingMonths = Dates::calculateRemainingMonths(['loanOriginationDate' => Strings::showField('noteDate', 'RESTInfo'), 'terms' => $lien1Terms]);
}
$propMgmntContactID = '';
$tempPropMgmntContactName = '';
$propMgmntContactPerson = '';
$propMgmntCompany = '';
$propMgmntNotes = '';
$propMgmntPhone = '';
$propMgmntContactEmail = '';
$propMgmntAddress = '';
$propMgmntCity = '';
$propMgmntState = '';
$propMgmntZip = '';
$HOA2Contacts = [];
$HOA2ContactID = 0;
$HOA2ContactName = '';
$HOA2CompanyName = '';
if (array_key_exists('HOA2', $fileContacts)) {
    $HOA2Contacts = $fileContacts['HOA2'];
}
if (count($HOA2Contacts) > 0) {
    $HOA2ContactID = trim($HOA2Contacts['CID']);
    $HOA2ContactName = trim($HOA2Contacts['contactName']);
    $HOA2CompanyName = trim($HOA2Contacts['companyName']);
}
if (array_key_exists('PM', $fileContacts)) {
    $propertyManagementInfo = $fileContacts['PM'];
}
if (count($propertyManagementInfo) > 0) {
    $propMgmntContactID = trim($propertyManagementInfo['CID']);
    $propMgmntContactPerson = $tempPropMgmntContactName = trim($propertyManagementInfo['contactName']);
    $propMgmntCompany = trim($propertyManagementInfo['companyName']);
    $propMgmntContactEmail = trim($propertyManagementInfo['email']);
    $propMgmntAddress = trim($propertyManagementInfo['address']);
    $propMgmntCity = trim($propertyManagementInfo['city']);
    $propMgmntState = trim($propertyManagementInfo['state']);
    $propMgmntZip = trim($propertyManagementInfo['zip']);
    $propMgmntNotes = trim($propertyManagementInfo['description']);
    $propMgmntPhone = trim($propertyManagementInfo['phone']);
}
$propMgmntPhoneArray = [];
$propMgmntPhNo1 = '';
$propMgmntPhNo2 = '';
$propMgmntPhNo3 = '';
$propMgmntPhExt = '';
$propMgmntPhoneArray = Strings::splitPhoneNumber($propMgmntPhone);
if (count($propMgmntPhoneArray) > 0) {
    $propMgmntPhNo1 = $propMgmntPhoneArray['No1'];
    $propMgmntPhNo2 = $propMgmntPhoneArray['No2'];
    $propMgmntPhNo3 = $propMgmntPhoneArray['No3'];
    $propMgmntPhExt = $propMgmntPhoneArray['Ext'];
}
$tabIndexNo = 1;
/* Loan origination */
if (count($QAInfo) > 0) {
    $PublishBInfo = Strings::showField('PublishBInfo', 'QAInfo');
    $BEthnicity = Strings::showField('BEthnicity', 'QAInfo');
    $BRace = Strings::showField('BRace', 'QAInfo');
    $BGender = Strings::showField('BGender', 'QAInfo');
    $PublishCBInfo = Strings::showField('PublishCBInfo', 'QAInfo');
    $CBEthnicity = Strings::showField('CBEthnicity', 'QAInfo');
    $CBRace = Strings::showField('CBRace', 'QAInfo');
    $CBGender = Strings::showField('CBGender', 'QAInfo');
    $noOfPeopleDependent = Strings::showField('noOfPeopleDependent', 'QAInfo');
}
if ($PublishCBInfo == 2) {
} else {
    $CBEthnicity = '';
    $CBRace = '';
    $CBGender = '';
}
if ($PublishBInfo == 2) {
} else {
    $BEthnicity = '';
    $BRace = '';
    $BGender = '';
}
/* Loan Origination */
$showLimitedMandatoryField = 0; /* This Field used for PC = Mitigation Resolve,LLC Sep 23,2015 */
if (($publicUser == 1) && ($PCID == 609)) { /*  Mitigation Resolve,LLC = 609 Sep 23,2015 */
    $showLimitedMandatoryField = 1;
}

$LODispOpt = '';
$LOSectionDispOpt = $LOSectionDivDispOpt = 'display: none;';
$LOAndHMLOSectionDivDispOpt = 'display: none;';
$LOBorResidedDispOpt = $LOCoBorResidedDispOpt = 'display: block;';
if ($isLO == 1 && $publicUser != 1) {
    /** Loan Origination **/
    $LODispOpt = 'display: none;';
    $LOSectionDivDispOpt = 'display: block;';
    $LOSectionDispOpt = 'display: block;';
    $LOAndHMLOSectionDivDispOpt = 'display: block;';
    $LOBorResidedDispOpt = $LOCoBorResidedDispOpt = 'display: none;';
    if ($borResidedPresentAddr == 'Yes') {
        $LOBorResidedDispOpt = 'display: block;';
    }
    if ($coBResidedPresentAddr == 'Yes') {
        $LOCoBorResidedDispOpt = 'display: block;';
    }
}
$MFDispOpt = '';
$MFSectionDispOpt = $MFSectionDivDispOpt = 'display: none;';
if ($isMF == 1 && $publicUser != 1) {
    /** Merchant Funding **/
    $MFDispOpt = 'display: none;';
    $MFSectionDivDispOpt = 'display: block;';
    $MFSectionDispOpt = 'display: block;';
}
$FUDispOpt = '';
$FUSectionDispOpt = $FUSectionDivDispOpt = $FUSectionSubEmpDivDispOpt = $FUSectionSubOwnBusinessDivDispOpt = $FUSectionSubNotesDivDispOpt = 'display: none;';
if ($isFU == 1 && $publicUser != 1) {
    /** Funding **/
    $FUDispOpt = 'display: none;';
    $FUSectionDivDispOpt = 'display: block;';
    $FUSectionDispOpt = 'display: block;';

    if ($haveEmployed == 'Yes') {
        $FUSectionSubEmpDivDispOpt = 'display: block;';
    }
    if ($ownBusiness == 'Yes') {
        $FUSectionSubOwnBusinessDivDispOpt = 'display: block;';
    }
    if (count($fundsNeededReasonArray) > 0) {
        if (in_array('Other', $fundsNeededReasonArray)) {
            $FUSectionSubNotesDivDispOpt = 'display: block;';
        }
    }
}

/**
 ** Description    : Hard / Private Money LOS module Section Show and Hide
 ** Developer    : Viji & Venkatesh
 ** Author        : Awatasoftsys
 ** Date            : Nov 18, 2016
 **/
$HMLODispOpt = '';
$HMLOSectionDispOpt = $HMLOSectionDivDispOpt = 'display: none;';
$EFEXPDisp = '';
$HMLOBorResidedDispOpt = $HMLOCoBorResidedDispOpt = 'display: block;';
$EFDispOptNew = 'display: block;';
if (($isHMLO == 1 || $isEF == 1) && $publicUser != 1) {
    /** Hard / Private Money LOS **/
    $HMLODispOpt = 'display: none;';
    $HMLOSectionDivDispOpt = 'display: block;';
    $HMLOSectionDispOpt = 'display: block;';
    $HMLOBorResidedDispOpt = $HMLOCoBorResidedDispOpt = 'display: none;';
    $LOAndHMLOSectionDivDispOpt = 'display: block;';
    if ($borResidedPresentAddr == 'Yes') {
        $HMLOBorResidedDispOpt = 'display: block;';
    }
    if ($coBResidedPresentAddr == 'Yes') {
        $HMLOCoBorResidedDispOpt = 'display: block;';
    }
}
if ($isEF == 1) {
    $EFDispOptNew = 'display: none;';
    $EFEXPDisp = 'display :none;';
}

$EFDispOpt = '';
$EFSectionDispOpt = $EFSectionDivDispOpt = '';
$EFBorResidedDispOpt = $EFCoBorResidedDispOpt = 'display: block;';
/*if ($isEF == 1 && $publicUser != 1) {
		$EFDispOpt = 'display: none;';$EFSectionDivDispOpt = 'display: block;';$EFSectionDispOpt = 'display: block;';
        $EFBorResidedDispOpt =  $EFCoBorResidedDispOpt = 'display: none;'; $LOAndHMLOSectionDivDispOpt = 'display: block;';
        if ($borResidedPresentAddr == 'Yes') {
            $EFBorResidedDispOpt = 'display: block;';
        }
        if ($coBResidedPresentAddr == 'Yes') {
            $EFCoBorResidedDispOpt = 'display: block;';
        }
	}*/
$propDetailsProcess = '';
$propDetailsProcess = Strings::showField('propDetailsProcess', 'fileHMLOPropertyInfo');


/**
 * Get Borrower Flipping Experience and Ground Up Construction Experience
 */
if (array_key_exists('Flip', $fileExpFilpGroundUp)) $clientExpProInfo = $fileExpFilpGroundUp['Flip'];
if (array_key_exists('Gup', $fileExpFilpGroundUp)) $clientGUExpInfo = $fileExpFilpGroundUp['Gup'];

$previousState = Strings::showField('previousState', 'LMRInfo');
$PCClientEntityInfoArray = getPCClientEntityInfo::getReport(['PCID' => $PCID, 'CID' => $selClientId]);

if (in_array($PCID, $glFirstRehabLending)) {
    unset($glHMLOPresentOccupancy[1]);
    unset($glHMLOPresentOccupancy[2]);
    unset($glHMLOPresentOccupancy[3]);
    $glHMLOPresentOccupancy = array_values($glHMLOPresentOccupancy);
}

?>
<input type="hidden" name="SID" value="<?php echo $SID; ?>">
<input type="hidden" name="typeText" value="<?php echo $servicer1Text; ?>">
<input type="hidden" name="isLOOpt" value="<?php echo $isLO; ?>">

<?php
if ($isHOALien == 1 && $publicUser != 1) {
    ?>
    <input type="hidden" name="borrowerEmail" id="borrowerEmail"
           value="<?php echo Strings::showField('borrowerEmail', 'LMRInfo'); ?>">
    <div class="clear pad5"></div>
    <?php
}
if (($publicUser != 1 && $userGroup != 'Client') || ($userGroup == 'Client' && $LMRId == 0 && $publicUser != 1)) { // Not allow this section for public users
    require 'fileAdminInfo.php'; // | File Admin Section..
}
?>

</div>

<div class="clear pad10"></div>
<div class="row">
    <div class="col-md-6"> <!-- Full Property Address Section Start -->
        <div class="block-content changeWidthOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
             style="<?php echo $LODispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOptNew; ?>">
            <div class="text-on-pannel text-primary">Full Property Address</div>

            <div class="form-group">
                <label class="col-md-5" for="propertyAddress">Address</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="text"
                               name="propertyAddress"
                               id="propertyAddress"
                               tabindex="<?php echo $tabIndexNo++; ?>"
                               value="<?php echo htmlspecialchars($propertyAddress); ?>" <?php if (($publicUser == '1' && $showLimitedMandatoryField == 0) || glCustomJobForProcessingCompany::is($PCID)) { ?> class="full-width mandatory form-control input-sm" <?php } else { ?> class="full-width form-control input-sm" <?php } ?>
                               autocomplete="off">
                    <?php } else { ?>
                        <h5><?php echo $propertyAddress; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="propertyCity">City</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="text"
                               name="propertyCity"
                               id="propertyCity"
                               tabindex="<?php echo $tabIndexNo++; ?>"
                               value="<?php echo htmlspecialchars($propertyCity); ?>" <?php if (($publicUser == '1' && $showLimitedMandatoryField == 0) || glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>
                               autocomplete="off">
                    <?php } else { ?>
                        <h5><?php echo $propertyCity; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="propertyState">State</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select name="propertyState"
                                id="propertyState"
                                tabindex="<?php echo $tabIndexNo++; ?>" <?php if (($publicUser == '1' && $showLimitedMandatoryField == 0) || glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>
                                onchange="populateStateCounty('loanModForm', 'propertyState', 'propertyCounty');">
                            <option value=''> - Select -</option>
                            <?php
                            for ($j = 0; $j < count($stateArray); $j++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $propertyState);
                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $propertyState; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="propertyCounty">County</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select name="propertyCounty"
                                id="propertyCounty" <?php if (($publicUser == '1' && $showLimitedMandatoryField == 0) || glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory form-control input-sm" <?php } else { ?> class=" form-control input-sm" <?php } ?>
                                tabindex="<?php echo $tabIndexNo++; ?>"
                                onchange="checkCountyData('loanModForm', 'propertyState');">
                            <option value=''> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($propertyCountyInfo); $i++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($propertyCountyInfo[$i]['countyName']), $propertyCounty);
                                echo "<option value=\"" . trim($propertyCountyInfo[$i]['countyName']) . "\" " . $sOpt . '>' . trim($propertyCountyInfo[$i]['countyName']) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $propertyState; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="propertyZip">Zip Code</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="text"
                               name="propertyZip"
                               id="propertyZip"
                               tabindex="<?php echo $tabIndexNo++; ?>"
                               value="<?php echo $propertyZip; ?>" <?php if (($publicUser == '1' && $showLimitedMandatoryField == 0) || glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>
                               autocomplete="off">
                    <?php } else { ?>
                        <h5><?php echo $propertyZip; ?></h5>
                    <?php } ?>
                </div>
            </div>

        </div>
    </div> <!-- Full Property Address Section End -->

    <div class="col-md-6"> <!-- Subject Property Section Start -->
        <div class="block-content hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
             style="<?php echo $LODispOpt . $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOptNew; ?>">
            <div class="text-on-pannel text-primary">Subject Property</div>

            <div class="form-group">
                <label class="col-md-5" for="occupancy">Present Occupancy</label>
                <div class="col-md-7">
                    <?php
                    if ($isHMLO == 1) {
                        if ($allowToEdit) {
                            ?>
                            <select name="presentOccupancy" id="occupancy" class="form-control input-sm"
                                    tabindex="<?php echo $tabIndexNo++; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count($glHMLOPresentOccupancy); $i++) {
                                    $sOpt = '';
                                    $typeOfHouse = '';
                                    $typeOfHouse = trim($glHMLOPresentOccupancy[$i]);
                                    $sOpt = Arrays::isSelected($typeOfHouse, Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy);
                                    echo "<option value=\"" . $typeOfHouse . "\" " . $sOpt . '>' . $typeOfHouse . '</option>';
                                }
                                ?>
                            </select>
                            <?php
                        } else {
                            echo '<h5>' . Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy . '</h5>';
                        }
                    } else {
                        if ($allowToEdit) {
                            ?>
                            <select name="occupancy"
                                    id="occupancy" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>
                                    tabindex="<?php echo $tabIndexNo++; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count($glOccupancyArray); $i++) {
                                    $sOpt = '';
                                    $glOccupancy = '';
                                    $glOccupancy = trim($glOccupancyArray[$i]);
                                    $sOpt = Arrays::isSelected($glOccupancy, $occupancy);
                                    echo "<option value=\"" . $glOccupancy . "\" " . $sOpt . '>' . $glOccupancy . '</option>';
                                }
                                ?>
                            </select>
                            <?php
                        } else {
                            echo '<h5>' . $occupancy . '</h5>';
                        }
                    }
                    ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="propertyType">Property Type</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select name="propertyType"
                                id="propertyType" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>
                                tabindex="<?php echo $tabIndexNo++; ?>">
                            <option value=""> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($GpropertyTypeKeyArray); $i++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($GpropertyTypeKeyArray[$i]), $propertyType);
                                echo "<option value=\"" . trim($GpropertyTypeKeyArray[$i]) . "\" " . $sOpt . '>' . trim(GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$GpropertyTypeKeyArray[$i]]) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php if (array_key_exists($propertyType, GpropertyTypeNumbArray::$GpropertyTypeNumbArray)) {
                                echo GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertyType];
                            } ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="homeValue">What is the home value?<br>(As-Is)</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <span class="input-group-addon">$</span>
                            <input type="text" name="homeValue" id="homeValue" tabindex="<?php echo $tabIndexNo++; ?>"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('homeValue', 'LMRInfo')); ?>"
                                   autocomplete="off"<?php if (($publicUser == '1' && $showLimitedMandatoryField == 0 && $branchReferralCode != '696655') || $PCID == glPCID::PCID_MORTGAGE_ASSISTANCE) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                        </div>
                        <span class="with-children-tip right pad5"><a class="fa fa-info-circle fa-lg tip-bottom"
                                                                      style="text-decoration:none;"
                                                                      href="javascript:void(0);"
                                                                      title='Please click the "Get Zillow Value" link to get your estimated home value.<br>Pick the lowest value to be safe.'></a></span>
                        <span class="right pad2"><a target="_blank" href="#" onclick=buildurl(this)><h4>Get Zillow Value&nbsp;&nbsp;</h4></a></span>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('homeValue', 'LMRInfo'); ?></h5>
                    <?php } ?>
                </div>
            </div>

        </div>
    </div> <!-- Subject Property Section End -->
    <?php
    if ($isHOALien == 1 && $publicUser != 1) {
        $borDispOpt = 'display: none;';
    } else {
        $borDispOpt = 'display: block;';
    }
    ?>
    <div class="clear pad10"></div>
    <div class="col-md-6" id="borrowerInfo" style="<?php echo $borDispOpt; ?>"> <!-- Subject Property Section Start -->
        <div class="block-content">
            <div class="text-on-pannel text-primary">Borrower Info</div>

            <div class="form-group">
                <label class="col-md-5" for="borrowerFName">Borrower's First Name</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm mandatory" type="text" name="borrowerFName"
                               id="borrowerFName" value="<?php echo $borrowerFName; ?>" autocomplete="off"
                               tabindex="<?php echo $tabIndex++ ?>"/>
                    <?php } else { ?>
                        <h5><?php echo $borrowerFName; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="borrowerLName">Borrower's Last Name</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm mandatory" type="text" name="borrowerLName"
                               id="borrowerLName" value="<?php echo $borrowerLName; ?>" autocomplete="off"
                               tabindex="<?php echo $tabIndex++ ?>"/>
                    <?php } else { ?>
                        <h5><?php echo $borrowerLName; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="borrowerEmail">Borrower Email</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit && ($userGroup != 'Client' || $publicUser == 1)) { ?>
                        <input type="email" class="form-control input-sm" name="borrowerEmail" id="borrowerEmail"
                               tabindex="<?php echo $tabIndex++; ?>" <?php if ($LMRId == 0 && (!in_array($PCID, $glDisableClientImportPC))) { ?> onblur="populateClientInfo('loanModForm', this.value, '<?php echo $PCID ?>');" <?php } ?>
                               value="<?php echo Strings::showField('borrowerEmail', 'LMRInfo'); ?>" autocomplete="off">

                    <?php } else if ($userGroup == 'Client' && $allowClientToCreateHMLOFile == 1 && $LMRId == 0) { ?>
                        <h5 class="with-children-tip"><?php echo $tempClientEmail; ?>&nbsp;&nbsp;<a
                                    style="text-decoration:none;"
                                    class="fa fa-refresh fa-2x tip-bottom" <?php if ($LMRId == 0 && (!in_array($PCID, $glDisableClientImportPC))) { ?> onclick="populateClientInfo('loanModForm', '<?php echo trim($tempClientEmail); ?>', '<?php echo $PCID ?>');" <?php } ?>
                                    title="Update Profile Info"></a></h5>
                        <input type="hidden" name="borrowerEmail" id="borrowerEmail"
                               value="<?php echo $tempClientEmail; ?>" maxlength="75" size="40">

                    <?php } else { ?>
                        <h5><?php echo $tempClientEmail; ?></h5>
                        <input type="hidden" name="borrowerEmail" id="borrowerEmail"
                               value="<?php echo Strings::showField('borrowerEmail', 'LMRInfo'); ?>" maxlength="75" size="40">
                    <?php } ?>
                </div>
            </div>

            <div id="showClientEmailExists" class="left"></div>
            <div id="divLo1" class="left pad2" style="display:none;">Please Wait... <img
                        src="<?php echo CONST_SITE_URL; ?>assets/images/ajax-loader.gif" alt=""></div>

            <div class="form-group">
                <label class="col-md-5" for="phoneNumber">Phone Number</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="phoneno form-control input-sm mask_phone" name="phoneNumber" id="phoneNumber"
                               tabindex="<?php echo $tabIndex++; ?>"
                               value="<?php echo Strings::formatPhoneNumber($phoneNumber); ?>" autocomplete="off" type="text"
                               placeholder="(___) ___ - ____ Ext ____"/>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatPhoneNumber($phoneNumber); ?></h5>
                    <?php } ?>
                </div>
            </div>

            <?php if ($isLO == 1 && $publicUser != 1) {
            } else {
                /** Loan Origination **/ ?>
                <div class="form-group hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
                     style="<?php echo $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOpt ?>">
                    <label class="col-md-5" for="altPhoneNumber">ALT Phone Number</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm mask_phone" name="altPhoneNumber" id="altPhoneNumber"
                                   tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo Strings::formatPhoneNumber($phoneNumber); ?>" autocomplete="off"
                                   type="text" placeholder="(___) ___ - ____ Ext ____"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::formatPhoneNumber($altPhoneNumber); ?></h5>
                        <?php } ?>
                    </div>
                </div>
            <?php } /** Loan Origination End **/ ?>

            <div class="form-group">
                <label class="col-md-5" for="cell">Cell Number</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm mask_cell" name="cell" id="cell"
                               tabindex="<?php echo $tabIndex++; ?>"
                               value="<?php echo Strings::formatCellNumber($cellNumber); ?>" autocomplete="off" type="text"
                               placeholder="xxx-xxx-xxxx"/>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatCellNumber($cellNumber); ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="fax">Fax</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm mask_cell" name="fax" id="fax"
                               tabindex="<?php echo $tabIndex++; ?>" value="<?php echo Strings::formatCellNumber($fax); ?>"
                               autocomplete="off" type="text" placeholder="xxx-xxx-xxxx"/>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatCellNumber($fax); ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="maritalStatus">Marital Status?</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="radio" name="maritalStatus" id="maritalStatus"
                               class="<?php echo BaseHTML::chkMandField('maritalStatus', $PCquickAppFieldsInfo, ''); ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               value="Single" <?php echo Strings::isChecked(Strings::showField('maritalStatus', 'LMRInfo'), 'Single'); ?>
                               onclick="showAndHideDiv('hide', 'maritalDiv', 0);">&nbsp;Single
                        <input type="radio" name="maritalStatus" id="maritalStatus"
                               tabindex="<?php echo $tabIndex++; ?>" value="Married"
                               onclick="showAndHideDiv('show', 'maritalDiv', 0);">&nbsp;Married
                        <input type="radio" name="maritalStatus" id="maritalStatus"
                               tabindex="<?php echo $tabIndex++; ?>" value="Divorced"
                               onclick="showAndHideDiv('show', 'maritalDiv', 0);">&nbsp;Divorced
                        <input type="radio" name="maritalStatus" id="maritalStatus"
                               tabindex="<?php echo $tabIndex++; ?>" value="Widowed"
                               onclick="showAndHideDiv('show', 'maritalDiv', 0);">&nbsp;Widowed
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('maritalStatus', 'LMRInfo'); ?></h5>
                    <?php } ?>
                </div>
            </div>
            <?php
            if (($maritalStatus == 'Married' || $maritalStatus == 'Divorced' || $maritalStatus == 'Widowed') && $isMF == 0) {
                $maritalDisp = 'display: block;';
            } else {
                $maritalDisp = 'display: none;';
            }
            ?>
            <div id="maritalDiv" class="even" style="<?php echo $maritalDisp; ?>">
                <div class="form-group col-md-6">
                    <label class="col-md-12" for="marriageDate">Date of Marriage</label>
                    <div class="col-md-12">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar text-primary"
                                                                   aria-hidden="true"></i></span>
                                <input class="form-control input-sm <?php echo BaseHTML::chkMandField('marriageDate', $PCquickAppFieldsInfo, ''); ?>"
                                       onkeyup="formatDateField(this)" placeholder="MM/DD/YYYY" type="text"
                                       name="marriageDate" id="marriageDate" value="<?php echo $marriageDate; ?>"
                                       tabindex="<?php echo $tabIndex++; ?>" autocomplete="off" maxlength="10"/>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $marriageDate; ?></h5>
                        <?php } ?>
                    </div>
                </div>

                <div class="form-group col-md-6">
                    <label class="col-md-12" for="divorceDate">Date of Divorce (If applicable)</label>
                    <div class="col-md-12">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar text-primary"
                                                                   aria-hidden="true"></i></span>
                                <input class="form-control input-sm dateNewClass"
                                       placeholder="MM/DD/YYYY"
                                       type="text"
                                name="divorceDate"
                                       onkeyup="formatDateField(this)"
                                id="divorceDate"
                                       value="<?php echo $divorceDate; ?>"
                                tabindex="<?php echo $tabIndex++; ?>"
                                       autocomplete="off" maxlength="10"/>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $divorceDate; ?></h5>
                        <?php } ?>
                    </div>
                </div>

                <div class="form-group col-md-6">
                    <label class="col-md-12" for="maidenName">Maiden Name</label>
                    <div class="col-md-12">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm <?php echo BaseHTML::chkMandField('maidenName', $PCquickAppFieldsInfo, ''); ?>"
                                   type="text" name="maidenName" id="maidenName"
                                   value="<?php echo Strings::showField('maidenName', 'LMRInfo'); ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('maidenName', 'LMRInfo'); ?></h5>
                        <?php } ?>
                    </div>
                </div>

                <div class="form-group col-md-6">
                    <label class="col-md-12" for="spouseName">Full name of Spouse <span style="font-size: 9px">(if not on mortgage)</span></label>
                    <div class="col-md-12">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm <?php echo BaseHTML::chkMandField('spouseName', $PCquickAppFieldsInfo, ''); ?>"
                                   type="text" name="spouseName" id="spouseName"
                                   value="<?php echo Strings::showField('spouseName', 'LMRInfo'); ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" autocomplete="off" maxlength="30"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('spouseName', 'LMRInfo'); ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <div class="clear"></div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="borrowerDOB">Date Of Birth</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <span class="input-group-addon borrowerDOB"><i class="fa fa-calendar text-primary"
                                                                           aria-hidden="true"></i></span>
                            <input class="form-control input-sm" id="borrowerDOB" onkeyup="formatDateField(this)"
                                   placeholder="MM/DD/YYYY" type="text" name="borrowerDOB"
                                   tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $borrowerDOB; ?>"
                                   autocomplete="off"/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo $borrowerDOB; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="mask_ssn">Social Security Number</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm ssn mask_ssn <?php echo BaseHTML::chkMandField('ssn', $PCquickAppFieldsInfo, ''); ?>"
                               name="ssn" id="mask_ssn" type="text" placeholder="xxx-xx-xxxx"
                               tabindex="<?php echo $tabIndex++; ?>"
                               value="<?php echo Strings::formatSSNNumber($ssnNumber); ?>"/>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatSSNNumber($ssnNumber); ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
                 style="<?php echo $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOpt ?>">
                <label class="col-md-5" for="borrowerTimeZone">Time Zone</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select name="borrowerTimeZone" id="borrowerTimeZone"
                                tabindex="<?php echo $tabIndexNo++; ?>" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                            <option value=''> - Select -</option>
                            <?php
                            $timeZoneKeyArray = [];
                            $timeZoneKeyArray = array_keys($timeZoneArray);
                            for ($tz = 0; $tz < count($timeZoneKeyArray); $tz++) {
                                $timeZone = trim($timeZoneKeyArray[$tz]);
                                ?>
                                <option value="<?php echo $timeZone ?>" <?php echo Arrays::isSelected($timeZone, $borrowerTimeZone); ?>><?php echo $timeZoneArray[$timeZone] ?></option>
                            <?php } ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $timeZoneArray[$borrowerTimeZone]; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-5" for="serviceProvider">Please select service provider if you would like to
                    receive task reminders via SMS</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select class="form-control input-sm <?php echo BaseHTML::chkMandField('serviceProvider', $PCquickAppFieldsInfo, ''); ?>"
                                name="serviceProvider"
                                id="serviceProvider" <?php if (in_array($processingCompanyId, $glMandatoryFieldForPC)) { ?><?php } ?>
                                tabindex="<?php echo $tabIndex++; ?>">
                            <option value=''> - Select -</option>
                            <?php
                            $SMSServiceProviderKeyArray = [];
                            $SMSServiceProviderKeyArray = array_keys($SMSServiceProviderArray);
                            for ($j = 0; $j < count($SMSServiceProviderKeyArray); $j++) {
                                $sOpt = '';
                                $servicePr = '';
                                $servicePr = trim($SMSServiceProviderKeyArray[$j]);
                                $sOpt = Arrays::isSelected($servicePr, $serviceProvider);
                                echo "<option value=\"" . trim($servicePr) . "\" " . $sOpt . '>' . trim($SMSServiceProviderArray[$servicePr]) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $serviceProvider; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
                 style="<?php echo $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOpt ?>">
                <label class="col-md-5" for="workNumber">Work Number</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm mask_phone" name="workNumber" id="workNumber"
                               tabindex="<?php echo $tabIndex++; ?>"
                               value="<?php echo Strings::formatPhoneNumber($workNumber); ?>" autocomplete="off" type="text"
                               placeholder="(___) ___ - ____ Ext ____"/>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatPhoneNumber($workNumber); ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
                 style="<?php echo $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOpt ?>">
                <label class="col-md-5" for="worldPhone">International Number</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm mask_phone" name="worldPhone" id="worldPhone"
                               tabindex="<?php echo $tabIndex++; ?>"
                               value="<?php echo Strings::formatPhoneNumber(Strings::showField('worldPhone', 'file2Info')); ?>"
                               autocomplete="off" type="text" placeholder="(___) ___ - ____ Ext ____"/>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatPhoneNumber(Strings::showField('worldPhone', 'file2Info')); ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
                 style="<?php echo $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOpt ?>">
                <label class="col-md-5" for="timeToContact">Best time to contact?</label>
                <div class="col-md-3">
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="timeToContact" id="timeToContact"
                               tabindex="<?php echo $tabIndexNo++; ?>" maxlength="5" size="6"
                               value="<?php echo Strings::showField('timeToContact', 'file2Info'); ?>"
                               autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatPhoneNumber(Strings::showField('worldPhone', 'file2Info')); ?></h5>
                    <?php } ?>
                </div>
                <div class="col-md-4">
                    <select name="bestTime" id="bestTime"
                            tabindex="<?php echo $tabIndexNo++; ?>" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                        <option value=''> - Select -</option>
                        <option value="AM" <?php echo Arrays::isSelected('AM', Strings::showField('bestTime', 'file2Info')) ?>>AM
                        </option>
                        <option value="PM" <?php echo Arrays::isSelected('PM', Strings::showField('bestTime', 'file2Info')) ?>>PM
                        </option>
                    </select>
                </div>
            </div>

            <div class="form-group hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
                 style="<?php echo $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOpt ?>">
                <label class="col-md-5" for="methodOfContact">Preferred method of contact</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select name="methodOfContact" id="methodOfContact"
                                tabindex="<?php echo $tabIndexNo++; ?>" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                            <option value=''> - Select -</option>
                            <?php
                            $methodOfContactKeyArray = [];
                            $methodOfContactKeyArray = array_keys($methodOfContactArray);
                            for ($j = 0; $j < count($methodOfContactKeyArray); $j++) {
                                $sOpt = '';
                                $methodContact = '';
                                $methodContact = trim($methodOfContactKeyArray[$j]);
                                $sOpt = Arrays::isSelected($methodContact, $methodOfContact);
                                echo "<option value=\"" . trim($methodContact) . "\" " . $sOpt . '>' . trim($methodOfContactArray[$methodContact]) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $methodOfContact; ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="form-group hideOnMF hideOnFU" style="<?php echo $MFDispOpt . $FUDispOpt ?>">
                <label class="col-md-5" for="isCoBorrower">Is there a Co-borrower?</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                    <div <?php if ($isCoBorrower == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?>
                            id="isCoBo"
                            onclick="toggleSwitch('isCoBo', 'isCoBorrower', '1', '0' );toggleSwitch('isCoBo', 'isCoBorrowerExists', '1', '0' ); hideShowCoBorrowerSections('isCoBorrower', 'coBorrowerSections');showCoBorrowerDiv('coBorDiv', 4);"
                    ">
                    <input type="hidden" name="isCoBorrower" id="isCoBorrower" value="<?php echo $isCoBorrower; ?>">
                    <input type="hidden" name="isCoBorrowerExists" id="isCoBorrowerExists"
                           value="<?php echo $isCoBorrower; ?>">
                </div>
                <?php } ?>
            </div>
        </div>

        <div class="form-group hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
             style="<?php echo $LODispOpt . $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOpt ?>">
            <label class="col-md-5" for="isCoBorrower">Is there a Non-Borrower Contributor?</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <div <?php if ($isNonBorrower == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?>
                            id="isNonBo"
                            onclick="toggleSwitch('isNonBo', 'isNonBorrower', '1', '0' ); showAndHideNonBorrDiv('nonBorrowerDiv', 2);">
                        <input type="hidden" name="isNonBorrower" id="isNonBorrower"
                               value="<?php echo $isNonBorrower; ?>">
                    </div>
                <?php } ?>
            </div>
        </div>

        <div class="form-group showOnLO" style="<?php echo $LOSectionDispOpt ?>">
            <label class="col-md-5" for="noOfPeopleDependent"># Of Dependents</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="noOfPeopleDependent" id="noOfPeopleDependent" class="form-control input-sm"
                           value="<?php echo $noOfPeopleDependent ?>" tabindex="<?php echo $tabIndexNo++; ?>"/>
                <?php } else {
                    echo '<h5>' . $noOfPeopleDependent . '</h5>';
                } ?>
            </div>
        </div>
        <div class="clear"></div>
    </div>
</div> <!-- Borrower Section End -->

<div class="col-md-6"><!-- Borrower Address Info Section Start -->
    <div class="block-content">
        <div class="text-on-pannel text-primary">Borrower <span
                    class="hideOnHMLO hideOnEF" <?php if ($isHMLO == 1 || $isEF == 1) { ?> style="display:none" <?php } ?>>Present</span>
            Address
        </div>
        <div class="form-group">
            <label class="col-md-5" for="presentAddress">Address</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text"
                           class="form-control input-sm <?php echo BaseHTML::chkMandField('presentAddress', $PCquickAppFieldsInfo, ''); ?>"
                           name="presentAddress" id="presentAddress" tabindex="<?php echo $tabIndex++; ?>"
                           value="<?php echo $presentAddress ?>" autocomplete="off">
                <?php } else { ?>
                    <b><?php echo $presentAddress ?></b>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="presentCity">City </label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text"
                           class="form-control input-sm <?php echo BaseHTML::chkMandField('presentCity', $PCquickAppFieldsInfo, ''); ?>"
                           name="presentCity" id="presentCity" value="<?php echo $presentCity ?>"
                           tabindex="<?php echo $tabIndex++; ?>" autocomplete="off">
                <?php } else { ?>
                    <b><?php echo $presentCity ?></b>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="presentState">State </label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select class="form-control input-sm <?php echo BaseHTML::chkMandField('presentState', $PCquickAppFieldsInfo, ''); ?>"
                            name="presentState" id="presentState" tabindex="<?php echo $tabIndex++; ?>">
                        <option value=''> - Select -</option>
                        <?php
                        for ($j = 0; $j < count($stateArray); $j++) {
                            $sOpt = '';
                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $presentState);
                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                        }
                        ?>
                    </select>
                <?php } else { ?>
                    <b><?php echo $presentState ?></b>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="presentZip">Zip Code</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input class="form-control input-sm <?php echo BaseHTML::chkMandField('presentZip', $PCquickAppFieldsInfo, ''); ?>"
                           type="text" name="presentZip" id="presentZip" tabindex="<?php echo $tabIndex++; ?>"
                           value="<?php echo $presentZip ?>" autocomplete="off">
                <?php } else { ?>
                    <b><?php echo $presentZip ?></b>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="presentZip">Same as Mailing Address?</label>
            <div class="col-md-7">
                <div <?php if ($mailingAddrAsPresent == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?>
                        id="isPresentAdd"
                        onclick="toggleSwitch('isPresentAdd', 'mailingAddrAsPresent', '1', '0' );autoPopulateLOPresentAdd();">
                    <br>
                    <input type="hidden" name="mailingAddrAsPresent" id="mailingAddrAsPresent"
                           value="<?php echo $mailingAddrAsPresent; ?>">
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="borResidedPresentAddr">Has the borrower resided at the present address for less
                than two years? </label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="radio" class="<?php echo BaseHTML::chkMandField('presentZip', $PCquickAppFieldsInfo, ''); ?>"
                           name="borResidedPresentAddr" id="borResidedPresentAddr" value="Yes"
                           tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $borResidedPresentAddr); ?>
                           onclick="showFormerAddrDiv(this.value, 'Bor');">Yes
                    <input type="radio" name="borResidedPresentAddr" id="borResidedPresentAddr" value="No"
                           tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $borResidedPresentAddr); ?>
                           onclick="showFormerAddrDiv(this.value, 'Bor');">No
                <?php } else { ?>
                    <b><?php echo $borResidedPresentAddr ?></b>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="borPresentPropType">Rent or own</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="borPresentPropType" id="borPresentPropType" tabindex="<?php echo $tabIndexNo++; ?>">
                        <option value=""> - Select -</option>
                        <?php
                        for ($i = 0; $i < count($glPropTypeArray); $i++) {
                            $sOpt = '';
                            $glPropType = '';
                            $glPropType = trim($glPropTypeArray[$i]);
                            $sOpt = Arrays::isSelected($glPropType, $borPresentPropType);
                            echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                        }
                        ?>
                    </select>
                <?php } else { ?>
                    <b><?php echo $borPresentPropType ?></b>
                <?php } ?>
            </div>
        </div>
    </div>
</div>

<!-- Borrower's Mailing Address Info Section Start -->
<div class="col-md-6 hideMailingAddr" <?php if ($mailingAddrAsPresent == 1 && $isHMLO == 1 || $isEF == 1) { ?> style="display:none;" <?php } ?> >
    <div class="block-content">
        <div class="text-on-pannel text-primary">Borrower's Mailing Address</div>
        <?php
        if ($isLO == 1 || $isFU == 1 || $isHMLO == 1 || $isEF == 1) {
        } else {
            if ($allowToEdit) {
                ?>
                <div class="form-group hideOnLO hideOnFU hideOnHMLO hideOnEF">
                    <label class="col-md-5" for="mailingAddressAsProp">Same as Property Address?</label>
                    <div class="col-md-7">
                        <div <?php if (Strings::showField('mailingAddressAsProp', 'LMRInfo') == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?>
                                id="ismailAdd"
                                onclick="toggleSwitch('ismailAdd', 'mailingAddressAsProp', '1', '0' );autoPopulatePropAddress(); populateStateTimeZone('loanModForm', 'mailingState', 'borrowerTimeZone');">
                            <br>
                            <input type="hidden" name="mailingAddressAsProp" id="mailingAddressAsProp"
                                   value="<?php echo Strings::showField('mailingAddressAsProp', 'LMRInfo'); ?>">
                        </div>
                    </div>
                </div>

                <?php
            }
        }
        ?>
        <div class="form-group">
            <label class="col-md-5" for="mailingAddress">Address</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                <input type="text" name="mailingAddress" id="mailingAddress" tabindex="<?php echo $tabIndexNo++; ?>"
                       value="<?php echo Strings::showField('mailingAddress', 'LMRInfo'); ?>" <?php if (($publicUser == '1' && $showLimitedMandatoryField == 0) || glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>
                       autocomplete="off">
            </div>
            <?php } else { ?>
                <h5><?php echo Strings::showField('mailingAddress', 'LMRInfo'); ?></h5>
            <?php } ?>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="mailingCity">City</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                <input type="text" name="mailingCity" id="mailingCity" tabindex="<?php echo $tabIndexNo++; ?>"
                       value="<?php echo Strings::showField('mailingCity', 'LMRInfo'); ?>" <?php if (($publicUser == '1' && $showLimitedMandatoryField == 0) || glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>
                       autocomplete="off">
            </div>
            <?php } else { ?>
                <h5><?php echo Strings::showField('mailingCity', 'LMRInfo'); ?></h5>
            <?php } ?>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="mailingState">State</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                <select name="mailingState"
                        id="mailingState" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?><?php }
                if ($showLimitedMandatoryField == 1) {
                } else { ?> class="mandatory form-control input-sm" <?php } ?>
                        onchange="populateStateTimeZone('loanModForm', 'mailingState', 'borrowerTimeZone');"
                        tabindex="<?php echo $tabIndexNo++; ?>">
                    <option value=''> - Select -</option>
                    <?php
                    for ($j = 0; $j < count($stateArray); $j++) {
                        $sOpt = '';
                        $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $mailingState);
                        echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                    }
                    ?>
                </select>
            </div>
            <?php } else { ?>
                <h5><?php echo $mailingState; ?></h5>
            <?php } ?>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="mailingZip">Zip Code</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                <input type="text" name="mailingZip" id="mailingZip" tabindex="<?php echo $tabIndexNo++; ?>"
                       value="<?php echo Strings::showField('mailingZip', 'LMRInfo') ?>" size="10"
                       maxlength="10" <?php if (($publicUser == '1' && $showLimitedMandatoryField == 0) || glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>
                       autocomplete="off">
            </div>
            <?php } else { ?>
                <h5><?php echo Strings::showField('mailingZip', 'LMRInfo'); ?></h5>
            <?php } ?>
        </div>

        <div class="form-group showOnLO" <?php echo $LOSectionDispOpt ?>>
            <label class="col-md-5" for="borMailingPropType">Property Type</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                <select name="borMailingPropType" id="borMailingPropType" tabindex="<?php echo $tabIndexNo++; ?>">
                    <option value=""> - Select -</option>
                    <?php
                    for ($i = 0; $i < count($glPropTypeArray); $i++) {
                        $sOpt = '';
                        $glPropType = '';
                        $glPropType = trim($glPropTypeArray[$i]);
                        $sOpt = Arrays::isSelected($glPropType, $borMailingPropType);
                        echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                    }
                    ?>
                </select>
            </div>
            <?php } else { ?>
                <h5><?php echo $borMailingPropType; ?></h5>
            <?php } ?>
        </div>

    </div>
</div>

<!-- Borrower's Previous Address Info Section Start -->
<div class="col-md-6 hideOnMF showOnBorFormerDiv hideOnFU hideOnHMLO hideOnEF"
     style="<?php echo $LOBorResidedDispOpt . $MFDispOpt . $HMLOBorResidedDispOpt . $EFBorResidedDispOpt ?>">
    <div class="block-content">
        <div class="text-on-pannel text-primary"><span class="showOnLO" style="<?php echo $LOSectionDivDispOpt ?>"> Borrower's Former Address</span><span
                    class="hideOnLO" style="<?php echo $LODispOpt ?>">Borrower's Previous Address</span></div>
        <?php
        if ($isLO == 1 || $isHMLO == 1 || $isEF == 1) {
        } else {
            if ($allowToEdit) {
                ?>
                <div class="form-group hideOnLO hideOnHMLO hideOnEF" style="<?php echo $HMLODispOpt . $EFDispOpt ?>">
                    <label class="col-md-5" for="previousAddrAsMailing">Previous Mailing address on the last tax<br>return
                        filed if different from mailing address</label>
                    <div class="col-md-7">
                        <div <?php if (Strings::showField('previousAddrAsMailing', 'LMRInfo') == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?>
                                id="isPreviousAdd"
                                onclick="toggleSwitch('isPreviousAdd', 'previousAddrAsMailing', '1', '0' );autoPopulatePreviousAddress('loanModForm', '');">
                            <br>
                            <input type="hidden" name="previousAddrAsMailing" id="previousAddrAsMailing"
                                   value="<?php echo Strings::showField('previousAddrAsMailing', 'LMRInfo'); ?>">
                        </div>
                    </div>
                </div>

                <?php
            }
        }
        ?>
        <div class="form-group">
            <label class="col-md-5" for="previousAddress">Address</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="previousAddress" id="previousAddress"
                           tabindex="<?php echo $tabIndexNo++; ?>"
                           value="<?php echo Strings::showField('previousAddress', 'LMRInfo'); ?>"
                           autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                <?php } else { ?>
                    <h5><?php echo Strings::showField('previousAddress', 'LMRInfo'); ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="previousCity">City</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="previousCity" id="previousCity" tabindex="<?php echo $tabIndexNo++; ?>"
                           value="<?php echo Strings::showField('previousCity', 'LMRInfo'); ?>"
                           autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                <?php } else { ?>
                    <h5><?php echo Strings::showField('previousCity', 'LMRInfo'); ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="previousState">State</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="previousState" id="previousState"
                            tabindex="<?php echo $tabIndexNo++; ?>" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                        <option value=''> - Select -</option>
                        <?php
                        for ($j = 0; $j < count($stateArray); $j++) {
                            $sOpt = '';
                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $previousState);
                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                        }
                        ?>
                    </select>
                <?php } else { ?>
                    <h5><?php echo $previousState; ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="previousZip">Zip Code</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="previousZip" id="previousZip" tabindex="<?php echo $tabIndexNo++; ?>"
                           value="<?php echo Strings::showField('previousZip', 'LMRInfo'); ?>"
                           autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory form-control input-sm" <?php } else { ?><?php } ?>>
                <?php } else { ?>
                    <h5><?php echo Strings::showField('previousZip', 'LMRInfo'); ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group showOnLO hideOnFU hideOnHMLO hideOnEF hideOnMF"
             style="<?php echo $LOSectionDispOpt . $MFDispOpt . $FUDispOpt . $LODispOpt ?>">
            <label class="col-md-5" for="borNoOfYrAtPrevAddr">Number of Years at Address ?</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="borNoOfYrAtPrevAddr" id="borNoOfYrAtPrevAddr" class="form-control input-sm"
                           tabindex="<?php echo $tabIndexNo++; ?>"
                           value="<?php echo Strings::showField('borNoOfYrAtPrevAddr', 'fileLoanOriginationInfo'); ?>"
                           autocomplete="off">
                <?php } else { ?>
                    <h5><?php echo Strings::showField('borNoOfYrAtPrevAddr', 'fileLoanOriginationInfo'); ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group showOnLO" style="<?php echo $LOSectionDispOpt ?>">
            <label class="col-md-5" for="borFormerPropType">Property Type</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="borFormerPropType" id="borFormerPropType" tabindex="<?php echo $tabIndexNo++; ?>">
                        <option value=""> - Select -</option>
                        <?php
                        for ($i = 0; $i < count($glPropTypeArray); $i++) {
                            $sOpt = '';
                            $glPropType = '';
                            $glPropType = trim($glPropTypeArray[$i]);
                            $sOpt = Arrays::isSelected($glPropType, $borFormerPropType);
                            echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                        }
                        ?>
                    </select>
                <?php } else { ?>
                    <h5><?php echo $borFormerPropType; ?></h5>
                <?php } ?>
            </div>
        </div>

    </div>
</div>

<!-- Borrower Background Info Section Start -->
<div class="col-md-12"><?php include('borrowerBackground.php');
    include_once 'sbaBackgroundAdditionalQuestions.php';

    ?></div>
<!-- Borrower Background Info Section End -->

<!-- Borrower Entity Info Section Start -->
<div class="col-md-12 showOnEF hideOnFU hideOnLO hideOnMF"
     style="<?php echo $HMLOSectionDivDispOpt . $EFSectionDivDispOpt ?>">
    <?php include('businessEntitySection.php'); ?>
</div>
<!-- Borrower Entity Info Section End -->

<!-- Borrower Experience Section Start -->
<?php if ($isHMLO == 1 || $isEF == 1) { ?>
    </div>
    <input type="hidden" name="recordDate" id="recordDate" value="<?php echo Strings::showField('recordDate', 'LMRInfo') ?>"/>
    <input type="hidden" name="max_upload_size" id="max_upload_size"
           value="<?php echo CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED ?>">
    <input type="hidden" name="fileSize" id="fileSize" value="<?php echo ini_get('upload_max_filesize') ?>">
    <?php
    $recordDate = Strings::showField('recordDate', 'LMRInfo');
    /**
     * Desc : Proof of sale (HUD)
     * Date : 09 Mar, 2017
     */
    $docCnt = 1;
    $proofOfSale1 = '';
    $proofOfSale2 = '';
    $proofOfSale3 = '';
    $proofOfSale4 = '';
    $proofOfSale5 = '';
    $proofOfSale6 = '';
    $proofOfSale7 = '';
    $proofOfSale8 = '';
    $proofOfSale9 = '';
    $proofOfSale10 = '';
    $proofOfSale11 = '';
    $proofOfSale12 = '';
    for ($doc = 0; $doc < count($docArray); $doc++) {
        $tempDocArray = [];
        $docCategoryArray = [];
        $flatNotes = '';
        $uploadDocUrl = '';
        $docName = '';
        $displayDocName = '';
        $docId = 0;
        $myUploadedBy = '';
        $myUploadedRole = '';
        $docCategory = '';

        $docName = trim($docArray[$doc]['docName']);
        $displayDocName = trim($docArray[$doc]['displayDocName']);
        $uploadedDate = trim($docArray[$doc]['uploadedDate']);
        $userId = trim($docArray[$doc]['uploadedBy']);
        $userType = trim($docArray[$doc]['uploadingUserType']);
        $docCategory = trim($docArray[$doc]['docCategory']);
        $docId = trim($docArray[$doc]['docID']);
        $fileType = trim($docArray[$doc]['fileType']);

        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
        $ipArray['outputZone'] = $userTimeZone;
        $ipArray['inputTime'] = $uploadedDate;
        $uploadedDate = Dates::timeZoneConversion($ipArray);

        $docCategoryArray = explode('-', $docCategory);
        if (count($docCategoryArray) > 0) {
            if ($docCategoryArray[0] == 'Proof of sale (HUD)') {

                if (Dates::IsEmpty($uploadedDate)) {
                    $uploadedDate = '';
                } else {
                    $uploadedDate = Dates::formatDateWithRE($uploadedDate, 'YMD_HMS', 'm/d/Y h:i A');
                }

                if ($displayDocName == '' || $displayDocName == NULL) {
                    $displayDocName = $docName;
                }

                $tempRecDate = str_replace('-', '', $recordDate);
                $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
                $fileValue = $LMRId;

                $fP = $folderName . '/' . $fileValue . '/upload';
                $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
                ${'proofOfSale' . $docCategoryArray[1]} = $uploadDocUrl;
            }
        }
    }
}
?>
<div class="showOnHMLO showOnEF hideOnFU hideOnLO hideOnMF hideOnEF"
     style="<?php echo $HMLOSectionDivDispOpt . $EFEXPDisp . $MFDispOpt . $FUDispOpt . $LODispOpt ?>">
    <?php require 'borrowerExperience.php'; ?>
</div>
<!-- Borrower Experience Section End -->

<?php
if ($isNonBorrower == '1' && $isLO != 1 && $isMF != 1 && $isFU != 1) {
    $nonBorDisp = 'display: block;';
} else {
    $nonBorDisp = 'display: none;';
} /** Loan Origination **/
?>
<!-- Non-Borrower Contributor Start -->
<div class="col-md-6 hideOnMF hideOnLO hideOnFU" id="nonBorrowerDiv1" style="<?php echo $nonBorDisp; ?>">
    <div class="block-content">
        <div class="text-on-pannel text-primary">Non-Borrower Contributor</div>

        <div class="form-group">
            <label class="col-md-5" for="nonBorrowerName">Name</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="nonBorrowerName" id="nonBorrowerName"
                           tabindex="<?php echo $tabIndexNo++; ?>"
                           value="<?php echo Strings::showField('nonBorrowerName', 'file2Info'); ?>"
                           class="form-control input-sm" autocomplete="off">
                <?php } else { ?>
                    <h5><?php echo Strings::showField('nonBorrowerName', 'file2Info'); ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="nonBorrowerDOB">Date Of Birth</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <div class="input-group">
                        <span class="input-group-addon nonBorrowerDOB"><i class="fa fa-calendar text-primary"
                                                                          aria-hidden="true"></i></span>
                        <input type="text" name="nonBorrowerDOB" id="nonBorrowerDOB" class="form-control input-sm"
                               onkeyup="formatDateField(this)" placeholder="MM/DD/YYYY"
                               tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $borrowerDOB; ?>"
                               autocomplete="off"/>
                    </div>
                <?php } else { ?>
                    <h5><?php echo $nonBorrowerDOB; ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="nonBorrowerSSN">SSN Number</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input class="form-control input-sm mask_ssn" name="nonBorrowerSSN" id="nonBorrowerSSN" type="text"
                           placeholder="xxx-xx-xxxx" tabindex="<?php echo $tabIndex++; ?>"
                           value="<?php echo Strings::formatSSNNumber($nonBorrowerSSNNumber); ?>"/>
                <?php } else { ?>
                    <h5><?php echo Strings::formatSSNNumber($nonBorrowerSSNNumber); ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="nonBorrowerEmail">Email Address</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="nonBorrowerEmail" id="nonBorrowerEmail"
                           tabindex="<?php echo $tabIndexNo++; ?>"
                           value="<?php echo Strings::showField('nonBorrowerEmail', 'file2Info'); ?>"
                           class="form-control input-sm" autocomplete="off">
                <?php } else { ?>
                    <h5><?php echo Strings::showField('nonBorrowerEmail', 'file2Info'); ?></h5>
                <?php } ?>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-5" for="monthlyContribution">Monthly Contribution</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="monthlyContribution" id="monthlyContribution"
                           tabindex="<?php echo $tabIndexNo++; ?>"
                           value="<?php echo Strings::showField('monthlyContribution', 'file2Info'); ?>" size="20" maxlength="20"
                           autocomplete="off">
                <?php } else { ?>
                    <h5><?php echo Strings::showField('monthlyContribution', 'file2Info'); ?></h5>
                <?php } ?>
            </div>
        </div>

    </div>
</div>
<!-- Non-Borrower Contributor End -->


<div class="clearfix"></div>

<?php if ($isHMLO == 1 || $isEF == 1) { ?>
    <!-- Additional Guarantors Info-->
    <?php require 'additionalGuarantorsSection.php'; ?>
<?php } // Additional Guarantors End ?>
<?php
if (Strings::showField('isCoBorrower', 'LMRInfo') == '1' && $isMF != 1 && $isFU != 1) {
    $coBorDisp = 'display: block;';
} else {
    $coBorDisp = 'display: none;';
}
?>
<div id="coBorDiv1" class="hideOnMF hideOnFU row coBorrowerSections" style="<?php echo $coBorDisp; ?>">
    <div class="col-md-6">
        <div class="block-content">
            <div class="text-on-pannel text-primary">Co-borrower Information</div>
            <div id="coBorDiv2" class="hideOnMF" style="<?php echo $coBorDisp; ?>">
                <div class="form-group">
                    <div class="row">
                        <!-- <div class="hideOnHMLO" style="<?php echo $HMLODispOpt ?>">First name</div> -->
                        <div class="showOnHMLO showOnEF"><label class="col-md-5" for="coBorrowerFName">First Name</label>
                        </div>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm mandatory" name="coBorrowerFName"
                                       id="coBorrowerFName" tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo htmlentities(Strings::showField('coBorrowerFName', 'LMRInfo')); ?>" maxlength="30"
                                       autocomplete="off" <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory" <?php } ?> />
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorrowerFName', 'LMRInfo'); ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <!-- <div class="showOnHMLO" style="<?php echo $HMLOSectionDivDispOpt ?>">Last Name</div> -->
                        <div class="hideOnHMLO hideOnEF"><label class="col-md-5" for="coBorrowerLName">Last Name</label>
                        </div>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm mandatory" name="coBorrowerLName"
                                       id="coBorrowerLName" tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo htmlentities(Strings::showField('coBorrowerLName', 'LMRInfo')); ?>" maxlength="30"
                                       autocomplete="off" <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory" <?php } ?>/>
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorrowerLName', 'LMRInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <!-- <div class="showOnHMLO" style="<?php echo $HMLOSectionDivDispOpt ?>">Email</div> -->
                        <div class="hideOnHMLO"><label class="col-md-5" for="coBorrowerEmail">Email</label></div>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm mandatory" name="coBorrowerEmail"
                                       id="coBorrowerEmail" tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo Strings::showField('coBorrowerEmail', 'LMRInfo') ?>" size="40"
                                       onblur="fieldValidation(this.id,this.name);"
                                       maxlength="75"
                                       autocomplete="off" <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory" <?php } ?> />
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorrowerEmail', 'LMRInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <!-- <div class="showOnHMLO" style="<?php echo $HMLOSectionDivDispOpt ?>">Primary Phone</div> -->
                        <div class="hideOnHMLO"><label class="col-md-5" for="coBPhNo">Phone Number</label></div>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="phoneno form-control input-sm mandatory mask_home_phone" type="text"
                                       name="coBPhNo" id="coBPhNo" tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo Strings::formatPhoneNumber($coBPhNo1) ?>"
                                       autocomplete="off" <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory" <?php } ?> />
                            <?php } else { ?>
                                <h5><?php echo Strings::formatPhoneNumber($coBPhoneNumber) ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <div class="hideOnHMLO"><label class="col-md-5" for="coBAltPhNo1">ALT Phone Number</label>
                        </div>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="phoneno form-control input-sm mandatory mask_phone" type="text"
                                       name="coBAltPhNo1" id="coBAltPhNo1" tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo $coBAltPhNo1 ?>"
                                       autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?> />
                            <?php } else { ?>
                                <h5><?php echo Strings::formatPhoneNumber($coBAltPhoneNumber) ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group '<?php if ($isHMLO == 0) { ?>even<?php } ?> hideOnLO' ">
                    <div class="row">
                        <div class="hideOnHMLO"><label class="col-md-5" for="coBCellNo">Cell Number</label></div>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="coBCellNo" id="coBCellNo"
                                       tabindex="<?php echo $tabIndexNo++; ?>" value="<?php echo $coBCellNo1 ?>"
                                       autocomplete="off"
                                       class="cellno form-control input-sm mask_cell <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> mandatory <?php } else { ?>  <?php } ?>  />"
                            <?php } else { ?>
                                <h5><?php echo Strings::formatPhoneNumber($coBCellNumber) ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <?php if ($isLO == 1 && $publicUser != 1) {
                } else {
                    /** Loan Origination **/ ?>
                    <div class="form-group '<?php if ($isHMLO == 1 || $isEF == 1) { ?>even <?php } ?>hideOnLO' ">
                        <div class="row">
                            <div class="hideOnHMLO"><label class="col-md-5" for="coBFax">Fax</label></div>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="coBFax" id="coBFax" tabindex="<?php echo $tabIndexNo++; ?>"
                                           value="<?php echo $coBFax1 ?>" autocomplete="off"
                                           class="cellno form-control input-sm mask_cell <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> mandatory <?php } else { ?>  <?php } ?>"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::formatPhoneNumber($coBCellNumber) ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                <?php } /** Loan Origination **/ ?>
                <div class="form-group '<?php if ($isHMLO == 0 && $isLO == 0) { ?>even <?php } ?>'">
                    <div class="row">
                        <label class="col-md-5" for="coBorrowerDOB">Date Of Birth</label>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">
                                    <span class="input-group-addon borrowerDOB"><i class="fa fa-calendar text-primary"
                                                                                   aria-hidden="true"></i></span>
                                    <input class="form-control input-sm dateNewClass" name="coBorrowerDOB" id="coBorrowerDOB"
                                           data-date-dob-start-date="<?php echo glDate::getMinRequirementDate(); ?>"
                                           data-date-dob-end-date="<?php echo glDate::getMaxRequirementDate(); ?>"
                                           onkeyup="formatDateField(this)" placeholder="MM/DD/YYYY" type="text"
                                           tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $coBorrowerDOB; ?>"
                                           autocomplete="off"/>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo Strings::formatPhoneNumber($coBorrowerDOB) ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group '<?php if ($isHMLO == 1 || $isEF == 1 || $isLO == 1) { ?>even <?php } ?>'">
                    <div class="row">
                        <label class="col-md-5" for="coBSsn">SSN Number</label>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="coBSsn" id="coBSsn" tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo $coBSsn1 ?>" autocomplete="off"
                                       class="mask_ssn form-control input-sm <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> mandatory <?php } ?>">
                            <?php } else { ?>
                                <h5><?php echo Strings::formatSSNNumber($coBSsnNumber) ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="form-group showOnHMLO showOnEF hideOnFU hideOnLO hideOnMF">
                    <div class="row">
                        <label class="col-md-5" for="coBorCreditScoreRange">Credit Score Range</label>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <select name="coBorCreditScoreRange" id="coBorCreditScoreRange"
                                        tabindex="<?php echo $tabIndexNo++; ?>">
                                    <option value=''> - Select -</option>
                                    <?php
                                    for ($i = 0; $i < count($glHMLOCreditScoreRange); $i++) {
                                        $sOpt = '';
                                        $glcoBorCreditScoreRange = '';
                                        $glcoBorCreditScoreRange = trim($glHMLOCreditScoreRange[$i]);
                                        $sOpt = Arrays::isSelected($glcoBorCreditScoreRange, $coBorCreditScoreRange);
                                        echo "<option value=\"" . $glcoBorCreditScoreRange . "\" " . $sOpt . '>' . $glcoBorCreditScoreRange . '</option>';
                                    }
                                    ?>
                                </select>
                            <?php } else { ?>
                                <h5><?php echo $coBorCreditScoreRange; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>


                <div class="form-group showOnHMLO showOnEF hideOnFU hideOnLO hideOnMF">
                    <div class="row">
                        <label class="col-md-5" for="coBorExperianScore">Experian Score</label>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <input class="cellno form-control input-sm" type="text" name="coBorExperianScore"
                                       id="coBorExperianScore" value="<?php echo $coBorExperianScore; ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            <?php } else { ?>
                                <h5><?php echo $coBorExperianScore; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group showOnHMLO showOnEF hideOnFU hideOnLO hideOnMF">
                    <div class="row">
                        <label class="col-md-5" for="coBorEquifaxScore">Equifax Score</label>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <input class="cellno form-control input-sm" type="text" name="coBorEquifaxScore"
                                       id="coBorEquifaxScore" value="<?php echo $coBorEquifaxScore; ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            <?php } else { ?>
                                <h5><?php echo $coBorExperianScore; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group showOnHMLO showOnEF hideOnFU hideOnLO hideOnMF">
                    <div class="row">
                        <label class="col-md-5" for="coBorTransunionScore">Transunion Score</label>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <input class="cellno form-control input-sm" type="text" name="coBorTransunionScore"
                                       id="coBorTransunionScore" value="<?php echo $coBorTransunionScore; ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            <?php } else { ?>
                                <h5><?php echo $coBorTransunionScore; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group showOnLO">
                    <div class="row">
                        <label class="col-md-5" for="coBYearsOfSchool">Years of School?</label>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <input class="cellno form-control input-sm" type="text" name="coBYearsOfSchool"
                                       id="coBYearsOfSchool" tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo Strings::showField('coBYearsOfSchool', 'fileLoanOriginationInfo') ?>"
                                       maxlength="30" autocomplete="off">
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBYearsOfSchool', 'fileLoanOriginationInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <?php if ($isLO == 1 && $publicUser != 1) {
                } else {
                    /** Loan Origination **/ ?>

                    <div class="form-group hideOnLO hideOnHMLO hideOnEF">
                        <div class="row">
                            <label class="col-md-5" for="coBorrowerTimeZone">Time Zone</label>
                            <div class="col-md-5">
                                <?php if ($allowToEdit) { ?>
                                    <select name="coBorrowerTimeZone" id="coBorrowerTimeZone"
                                            tabindex="<?php echo $tabIndexNo++; ?>" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=''> - Select -</option>
                                        <?php
                                        $timeZoneKeyArray = [];
                                        $timeZoneKeyArray = array_keys($timeZoneArray);
                                        for ($tz = 0; $tz < count($timeZoneKeyArray); $tz++) {
                                            $timeZone1 = trim($timeZoneKeyArray[$tz]);
                                            ?>
                                            <option value="<?php echo $timeZone1 ?>" <?php echo Arrays::isSelected($timeZone1, $coBorrowerTimeZone); ?>><?php echo $timeZoneArray[$timeZone1] ?></option>
                                        <?php } ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $timeZoneArray[$coBorrowerTimeZone]; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-group hideOnLO">
                        <div class="row">
                            <label class="col-md-5" for="coBServiceProvider">Please select service provider<br> if you
                                would like to receive<br> task reminders via SMS</label>
                            <div class="col-md-5">
                                <?php if ($allowToEdit) { ?>
                                    <select name="coBServiceProvider" id="coBServiceProvider"
                                            tabindex="<?php echo $tabIndexNo++; ?>" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=''> - Select -</option>
                                        <?php
                                        $SMSServiceProviderKeyArray = [];
                                        $SMSServiceProviderKeyArray = array_keys($SMSServiceProviderArray);
                                        for ($j = 0; $j < count($SMSServiceProviderKeyArray); $j++) {
                                            $sOpt = '';
                                            $servicePr = '';
                                            $servicePr = trim($SMSServiceProviderKeyArray[$j]);
                                            $sOpt = Arrays::isSelected($servicePr, $coBServiceProvider);
                                            echo "<option value=\"" . trim($servicePr) . "\" " . $sOpt . '>' . trim($SMSServiceProviderArray[$servicePr]) . '</option>';
                                        }
                                        ?>
                                    </select><span class="pad2 with-children-tip"><a
                                                class="fa fa-info-circle fa-2x tip-right" style="text-decoration:none;"
                                                title="Please select service provider if you would like to receive task reminders via SMS"></a></i></span>
                                <?php } else { ?>
                                    <h5><?php echo $timeZoneArray[$coBorrowerTimeZone]; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                <?php } /** Loan Origination **/ ?>
                <?php if ($isLO == 1 && $publicUser != 1) {
                } else {
                    /** Loan Origination **/ ?>

                    <div class="form-group hideOnLO hideOnHMLO hideOnEF">
                        <div class="row">
                            <label class="col-md-5" for="coBWorkNo">Work Number?</label>
                            <div class="col-md-5">
                                <?php if ($allowToEdit) { ?>
                                    <input class="mask_phone form-control input-sm" type="text" name="coBWorkNo"
                                           id="coBWorkNo" tabindex="<?php echo $tabIndexNo++; ?>"
                                           value="<?php echo $coBWorkNo1 ?>"
                                           autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?> >
                                <?php } else { ?>
                                    <h5><?php echo $coBorrowerWorkNumber ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-group hideOnLO hideOnHMLO hideOnEF">
                        <div class="row">
                            <label class="col-md-5" for="worldPhoneCoB">International Number?</label>
                            <div class="col-md-5">
                                <?php if ($allowToEdit) { ?>
                                    <input class="cellno form-control input-sm" type="text" name="worldPhoneCoB"
                                           id="worldPhoneCoB"
                                           value="<?php echo Strings::showField('worldPhoneCoB', 'file2Info') ?>" size="23"
                                           maxlength="23" tabindex="<?php echo $tabIndexNo++; ?>"
                                           autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?> >
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('worldPhoneCoB', 'file2Info') ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-group hideOnLO hideOnHMLO hideOnEF">
                        <div class="row">
                            <label class="col-md-5" for="coTimeToContact">Best time to contact?</label>
                            <div class="col-md-5">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm" type="text" name="coTimeToContact"
                                           id="coTimeToContact" tabindex="<?php echo $tabIndexNo++; ?>" maxlength="5"
                                           size="6" value="<?php echo Strings::showField('coTimeToContact', 'file2Info'); ?>"
                                           autocomplete="off">
                                    <div class="left">
                                        <select name="coBestTime" id="coBestTime"
                                                tabindex="<?php echo $tabIndexNo++; ?>">
                                            <option value=''> - Select -</option>
                                            <option value="AM" <?php echo Arrays::isSelected('AM', Strings::showField('coBestTime', 'file2Info')) ?>>
                                                AM
                                            </option>
                                            <option value="PM" <?php echo Arrays::isSelected('PM', Strings::showField('coBestTime', 'file2Info')) ?>>
                                                PM
                                            </option>
                                        </select>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-group hideOnLO hideOnHMLO hideOnEF">
                        <div class="row">
                            <label class="col-md-5" for="coMethodOfContact">Preferred method of contact</label>
                            <div class="col-md-5">
                                <?php if ($allowToEdit) { ?>
                                    <select name="coMethodOfContact" id="coMethodOfContact"
                                            tabindex="<?php echo $tabIndexNo++; ?>">
                                        <option value=''> - Select -</option>
                                        <?php
                                        $methodOfContactKeyArray = [];
                                        $methodOfContactKeyArray = array_keys($methodOfContactArray);
                                        for ($j = 0; $j < count($methodOfContactKeyArray); $j++) {
                                            $sOpt = '';
                                            $coMethodContact = '';
                                            $coMethodContact = trim($methodOfContactKeyArray[$j]);
                                            $sOpt = Arrays::isSelected($coMethodContact, $coMethodOfContact);
                                            echo "<option value=\"" . trim($coMethodContact) . "\" " . $sOpt . '>' . trim($methodOfContactArray[$coMethodContact]) . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $coMethodOfContact; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>

                <div class="form-group showOnLO">
                    <div class="row">
                        <label class="col-md-5" for="coBNoOfDependent"># Of Dependents</label>
                        <div class="col-md-5">
                            <?php if ($allowToEdit) { ?>
                                <input class="cellno form-control input-sm" type="text" name="coBNoOfDependent"
                                       id="coBNoOfDependent" value="<?php echo $coBNoOfDependent ?>" maxlength="5"
                                       size="5" tabindex="<?php echo $tabIndexNo++; ?>">
                            <?php } else {
                                echo '<b>' . $coBNoOfDependent . '</b>';
                            } ?>
                        </div>
                    </div>
                </div>
            </div> <!-- Co-Borrower Div -->
        </div>
        <div class="clearfix"></div>

        <div class="block-content" style="<?php echo $LOAndHMLOSectionDivDispOpt ?>">
            <div class="text-on-pannel text-primary">Co-borrower <span
                        class="hideOnHMLO hideOnEF" <?php if ($isHMLO == 1 || $isEF == 1) { ?> style="display:none" <?php } ?>>Present</span>
                Address
            </div>
            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="coBPresentAddress">Address</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm"
                                   name="coBPresentAddress"
                                   id="coBPresentAddress"
                                   tabindex="<?php echo $tabIndexNo++; ?>" value="<?php echo $coBPresentAddress; ?>"
                                   maxlength="75"
                                   autocomplete="off"/>
                        <?php } else { ?>
                            <h5><?php echo $coBPresentAddress; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="coBPresentCity">City</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm" name="coBPresentCity" id="coBPresentCity"
                                   tabindex="<?php echo $tabIndexNo++; ?>" value="<?php echo $coBPresentCity; ?>"
                                   size="20" maxlength="30" <?php if ($isHMLO == 1 || $isEF == 1) {
                            } else { ?> class="mandatory" <?php } ?> autocomplete="off"/>
                        <?php } else { ?>
                            <h5><?php echo $coBPresentCity; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="coBPresentState">State</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="coBPresentState" id="coBPresentState"
                                    tabindex="<?php echo $tabIndexNo++; ?>" <?php if ($isHMLO == 1 || $isEF == 1) {
                            } else { ?> class="mandatory" <?php } ?>>
                                <option value=''> - Select -</option>
                                <?php
                                for ($j = 0; $j < count($stateArray); $j++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $coBPresentState);
                                    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $coBPresentState; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="coBPresentZip">Zip Code</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input class="form-control input-sm" name="coBPresentZip" id="coBPresentZip"
                                   tabindex="<?php echo $tabIndexNo++; ?>" value="<?php echo $coBPresentZip; ?>"
                                   size="10" maxlength="10" <?php if ($isHMLO == 1 || $isEF == 1) {
                            } else { ?> class="mandatory" <?php } ?> autocomplete="off"/>
                        <?php } else { ?>
                            <h5><?php echo $coBPresentZip; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php
            if ($allowToEdit) {
                ?>

                <div class="form-group">
                    <div class="row">
                        <label class="col-md-5" for="coBorMailingAddrAsPresent">Same as Mailing Address?</label>
                        <div class="col-md-7">
                            <div <?php if ($coBorMailingAddrAsPresent == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?>
                                    id="iscoborrowerPreAdd"
                                    onclick="toggleSwitch('iscoborrowerPreAdd', 'coBorMailingAddrAsPresent', '1', '0' );autoPopulateCoBPresentAddr();">
                                <br>
                                <input type="hidden" name="coBorMailingAddrAsPresent" id="coBorMailingAddrAsPresent"
                                       value="<?php echo $coBorMailingAddrAsPresent; ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
            <div class="form-group showOnLO hideOnHMLO hideOnEF">
                <div class="row">
                    <label class="col-md-5" for="coBNoOfYrAtMailingAddr">Number of Years at Address ?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="coBNoOfYrAtMailingAddr" id="coBNoOfYrAtMailingAddr"
                                   tabindex="<?php echo $tabIndexNo++; ?>"
                                   value="<?php echo Strings::showField('coBNoOfYrAtMailingAddr', 'fileLoanOriginationInfo'); ?>"
                                   size="20" maxlength="30" autocomplete="off">
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('coBNoOfYrAtMailingAddr', 'fileLoanOriginationInfo'); ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="coBResidedPresentAddr">Has the borrower resided at the present address
                        for less than two years?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="coBResidedPresentAddr" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $coBResidedPresentAddr); ?>
                                   onclick="showFormerAddrDiv(this.value, 'CoBor');">Yes
                            <input type="radio" name="coBResidedPresentAddr" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $coBResidedPresentAddr); ?>
                                   onclick="showFormerAddrDiv(this.value, 'CoBor');">No
                        <?php } else { ?>
                            <h5><?php echo $coBResidedPresentAddr; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="row">
                    <label class="col-md-5"
                           for="coBPresentPropType"><?php if ($isHMLO == 1) { ?>Rent or own<?php } else { ?>Property Type<?php } ?></label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="coBPresentPropType" id="coBPresentPropType"
                                    tabindex="<?php echo $tabIndexNo++; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count($glPropTypeArray); $i++) {
                                    $sOpt = '';
                                    $glPropType = '';
                                    $glPropType = trim($glPropTypeArray[$i]);
                                    $sOpt = Arrays::isSelected($glPropType, $coBPresentPropType);
                                    echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $coBPresentPropType; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>


        </div> <!-- Property Address Div -->
        <div class="clearfix"></div>
        <?php //if($isHMLO ==1){?>
        <div class="block-content"
             style="<?php echo $HMLOSectionDivDispOpt . $EFSectionDivDispOpt . $MFDispOpt . $FUDispOpt . $LODispOpt ?>">
            <div class="text-on-pannel text-primary">Background</div>
            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="coBNoOfYrAtMailingAddr">Are you a U.S. Citizen?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="isCoBorUSCitizen" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $isCoBorUSCitizen); ?>>Yes
                            <input type="radio" name="isCoBorUSCitizen" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $isCoBorUSCitizen); ?>>No
                        <?php } else { ?>
                            <h5><?php echo $isCoBorUSCitizen; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group even">
                <div class="row">
                    <label class="col-md-5" for="coBNoOfYrAtMailingAddr">Has the borrower been declared bankrupt within
                        the past 7 years?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="isCoBorDecalredBankruptPastYears" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $isCoBorDecalredBankruptPastYears); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorDecalredBankrupt');">Yes
                            <input type="radio" name="isCoBorDecalredBankruptPastYears" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $isCoBorDecalredBankruptPastYears); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorDecalredBankrupt');">No
                        <?php } else { ?>
                            <h5><?php echo $isCoBorDecalredBankruptPastYears; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group even coBorDecalredBankruptTR" style="<?php echo $coBorDecalredBankruptDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBNoOfYrAtMailingAddr">(If Yes, Provide explanation below.)</label>
                </div>
            </div>

            <div class="form-group even coBorDecalredBankruptTR" style="<?php echo $coBorDecalredBankruptDispOpt ?>">
                <div class="row">
                    <div class="col-md-7">
                        <textarea name="coBorDecalredBankruptExpln" rows="3" cols="55"
                                  tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $coBorDecalredBankruptExpln; ?></textarea>
                    </div>
                </div>
            </div>

            <div class="form-group coBorDecalredBankruptTR">
                <div class="row">
                    <label class="col-md-5" for="isAnyCoBorOutstandingJudgements">Are there an outstanding judgements
                        against the borrowers?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="isAnyCoBorOutstandingJudgements" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $isAnyCoBorOutstandingJudgements); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorOutstandingJudgements');">Yes
                            <input type="radio" name="isAnyCoBorOutstandingJudgements" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $isAnyCoBorOutstandingJudgements); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorOutstandingJudgements');">No
                        <?php } else { ?>
                            <h5><?php echo $isAnyCoBorOutstandingJudgements; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group coBorOutstandingJudgementsTR"
                 style="<?php echo $coBorOutstandingJudgementsDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBorOutstandingJudgementsExpln">(If Yes, Provide explanation
                        below.)</label>
                </div>
            </div>

            <div class="form-group coBorOutstandingJudgementsTR"
                 style="<?php echo $coBorOutstandingJudgementsDispOpt ?>">
                <div class="row">
                    <div class="col-md-7">
                        <textarea name="coBorOutstandingJudgementsExpln" rows="3" cols="55"
                                  tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $coBorOutstandingJudgementsExpln; ?></textarea>
                    </div>
                </div>
            </div>

            <div class="form-group coBorDecalredBankruptTR even">
                <div class="row">
                    <label class="col-md-5" for="hasCoBorAnyActiveLawsuits">Do you have any active lawsuits against
                        you?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="hasCoBorAnyActiveLawsuits" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $hasCoBorAnyActiveLawsuits); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorActiveLawsuits');">Yes
                            <input type="radio" name="hasCoBorAnyActiveLawsuits" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $hasCoBorAnyActiveLawsuits); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorActiveLawsuits');">No
                        <?php } else { ?>
                            <h5><?php echo $hasCoBorAnyActiveLawsuits; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="form-group even coBorActiveLawsuitsTR" style="<?php echo $coBorActiveLawsuitsDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBorOutstandingJudgementsExpln">(If Yes, Provide explanation
                        below.)</label>
                </div>
            </div>

            <div class="form-group even coBorActiveLawsuitsTR" style="<?php echo $coBorActiveLawsuitsDispOpt ?>">
                <div class="row">
                    <div class="col-md-7">
                        <textarea name="coBorActiveLawsuitsExpln" rows="3" cols="55"
                                  tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $coBorActiveLawsuitsExpln; ?></textarea>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="hasCoBorPropertyTaxLiens">Have you had or currently have any property
                        tax liens?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="hasCoBorPropertyTaxLiens" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $hasCoBorPropertyTaxLiens); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorPropertyTaxLiens');">Yes
                            <input type="radio" name="hasCoBorPropertyTaxLiens" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $hasCoBorPropertyTaxLiens); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorPropertyTaxLiens');">No
                        <?php } else { ?>
                            <h5><?php echo $hasCoBorPropertyTaxLiens; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group  coBorPropertyTaxLiensTR" style="<?php echo $coBorActiveLawsuitsDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBorPropertyTaxLiensExpln">(If Yes, Provide explanation
                        below.)</label>
                </div>
            </div>

            <div class="form-group  coBorPropertyTaxLiensTR" style="<?php echo $coBorPropertyTaxLiensDispOpt ?>">
                <div class="row">
                    <div class="col-md-7">
                        <textarea name="coBorPropertyTaxLiensExpln" rows="3" cols="55"
                                  tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $coBorPropertyTaxLiensExpln; ?></textarea>
                    </div>
                </div>
            </div>


            <div class="form-group even">
                <div class="row">
                    <label class="col-md-5" for="hasCoBorObligatedInForeclosure">Has the borrower directly or indirectly
                        been obligated on any loan which resulted in foreclosure, transfer of title in lieu of
                        foreclosure, or judgement?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="hasCoBorObligatedInForeclosure" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $hasCoBorObligatedInForeclosure); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorObligatedInForeclosure');">Yes
                            <input type="radio" name="hasCoBorObligatedInForeclosure" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $hasCoBorObligatedInForeclosure); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorObligatedInForeclosure');">No
                        <?php } else { ?>
                            <h5><?php echo $hasCoBorObligatedInForeclosure; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group even coBorObligatedInForeclosureTR"
                 style="<?php echo $coBorObligatedInForeclosureDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBorObligatedInForeclosureExpln">(If Yes, Provide explanation
                        below.)</label>
                </div>
            </div>

            <div class="form-group even coBorObligatedInForeclosureTR"
                 style="<?php echo $coBorObligatedInForeclosureDispOpt ?>">
                <div class="row">
                    <div class="col-md-7">
                        <textarea name="coBorObligatedInForeclosureExpln" rows="3" cols="55"
                                  tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $coBorObligatedInForeclosureExpln; ?></textarea>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="hasCoBorPropertyTaxLiens">Is the borrower presently delinquent or in
                        any default on any Federal debt or any other loan, mortgage, financial obligation, bond, or loan
                        guarantee</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="isCoBorPresenltyDelinquent" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $isCoBorPresenltyDelinquent); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorDelinquent');">Yes
                            <input type="radio" name="isCoBorPresenltyDelinquent" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $isCoBorPresenltyDelinquent); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorDelinquent');">No
                        <?php } else { ?>
                            <h5><?php echo $isCoBorPresenltyDelinquent; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group  coBorDelinquentTR" style="<?php echo $coBorDelinquentDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBorPropertyTaxLiensExpln">(If Yes, Provide explanation
                        below.)</label>
                </div>
            </div>

            <div class="form-group  coBorDelinquentTR" style="<?php echo $coBorDelinquentDispOpt ?>">
                <div class="row">
                    <div class="col-md-7">
                        <textarea name="coBorDelinquentExpln" rows="3" cols="55"
                                  tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $coBorDelinquentExpln; ?></textarea>
                    </div>
                </div>
            </div>

            <div class="form-group even">
                <div class="row">
                    <label class="col-md-5" for="haveCoBorOtherFraudRelatedCrimes">Have you ever been convicted of a
                        felony or other fraud related crimes?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="haveCoBorOtherFraudRelatedCrimes" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $haveCoBorOtherFraudRelatedCrimes); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorOtherFraudRelatedCrimes');">Yes
                            <input type="radio" name="haveCoBorOtherFraudRelatedCrimes" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $haveCoBorOtherFraudRelatedCrimes); ?>
                                   onclick="showAndHideBorBackgroundDiv(this.value, 'coBorOtherFraudRelatedCrimes');">No
                        <?php } else { ?>
                            <h5><?php echo $haveCoBorOtherFraudRelatedCrimes; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group even coBorOtherFraudRelatedCrimesTR"
                 style="<?php echo $coBorOtherFraudRelatedCrimesDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBorPropertyTaxLiensExpln">(If Yes, Provide explanation
                        below.)</label>
                </div>
            </div>

            <div class="form-group even coBorOtherFraudRelatedCrimesTR"
                 style="<?php echo $coBorOtherFraudRelatedCrimesDispOpt ?>">
                <div class="row">
                    <div class="col-md-7">
                        <textarea name="coBorOtherFraudRelatedCrimesExpln" rows="3" cols="55"
                                  tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $coBorOtherFraudRelatedCrimesExpln; ?></textarea>
                    </div>
                </div>
            </div>

            <div class="form-group even">
                <div class="row">
                    <label class="col-md-5" for="coBorPropertyTaxLiensExpln">Background Explanation</label>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-7">
                        <textarea class="form-control" name="coBorBackgroundExplanation" rows="7"
                                  tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $coBorBackgroundExplanation; ?></textarea>
                    </div>
                </div>
            </div>


        </div><!-- left Div -->

        <?php /*
</div> <!-- Co-Borrower Mailing Div -->

			</div> <!-- right 1 Div -->
*/ ?>
    </div>
    <div class="col-md-6">
        <div class="block-content hideMailingAddr">
            <div class="text-on-pannel text-primary">Co-borrower's Mailing Address</div>
            <div id="coBorDiv3" class="hideOnMF" style="<?php echo $coBorDisp ?>">
                <?php if ($allowToEdit) { ?>
                    <div class="form-group">
                        <div class="row">
                            <div class="hideOnHMLO hideOnEF" style="<?php echo $HMLODispOpt . $EFDispOpt ?>">
                                <div class="showOnLO" style="<?php echo $LOSectionDivDispOpt ?>">Same as Borrower's
                                    Mailing Address?
                                </div>
                                <div class="hideOnLO" style="<?php echo $LODispOpt ?>">Same as borrower's mailing
                                    address?
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="left hideOnHMLO hideOnEF" style="<?php echo $HMLODispOpt . $EFDispOpt ?>">
                                    <div <?php if (Strings::showField('mailingAddressAsBorrower', 'LMRInfo') == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?>
                                            id="isborrowerAdd"
                                            onclick="toggleSwitch('isborrowerAdd', 'mailingAddressAsBorrower', '1', '0' );autoPopulateMailingAddressAsFile('loanModForm', ''); populateStateTimeZone('loanModForm', 'coBorrowerMailingState', 'coBorrowerTimeZone');">
                                        <br>
                                        <input type="hidden" name="mailingAddressAsBorrower"
                                               id="mailingAddressAsBorrower"
                                               value="<?php echo Strings::showField('mailingAddressAsBorrower', 'LMRInfo') ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php } ?>

                <div class="form-group">
                    <div class="row">
                        <label class="col-md-5" for="coBorrowerMailingAddress">Address</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm mandatory" type="text"
                                       name="coBorrowerMailingAddress" id="coBorrowerMailingAddress"
                                       value="<?php echo Strings::showField('coBorrowerMailingAddress', 'LMRInfo') ?>" size="40"
                                       tabindex="<?php echo $tabIndexNo++; ?>" maxlength="76"
                                       autocomplete="off" <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory" <?php } ?> >
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorrowerMailingAddress', 'LMRInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <label class="col-md-5" for="coBorrowerMailingCity">City</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm mandatory" type="text" name="coBorrowerMailingCity"
                                       id="coBorrowerMailingCity"
                                       value="<?php echo Strings::showField('coBorrowerMailingCity', 'LMRInfo') ?>" size="20"
                                       maxlength="30" tabindex="<?php echo $tabIndexNo++; ?>"
                                       autocomplete="off" <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory" <?php } ?> >
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorrowerMailingCity', 'LMRInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <label class="col-md-5" for="coBorrowerMailingState">State</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <select name="coBorrowerMailingState" id="coBorrowerMailingState"
                                        onchange="populateStateTimeZone('loanModForm', 'coBorrowerMailingState', 'coBorrowerTimeZone');"
                                        tabindex="<?php echo $tabIndexNo++; ?>" <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory" <?php } ?> >
                                    <option value=""> - Select -</option>
                                    <?php
                                    for ($i = 0; $i < count($stateArray); $i++) {
                                        $sOpt = '';
                                        $sOpt = Arrays::isSelected(trim($stateArray[$i]['stateCode']), $coBorrowerMailingState);
                                        echo "<option value=\"" . trim($stateArray[$i]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$i]['stateName']) . '</option>';
                                    }
                                    ?>
                                </select>
                            <?php } else { ?>
                                <h5><?php echo $coBorrowerMailingState ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <label class="col-md-5" for="coBorrowerMailingZip">Zip Code</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm mandatory" type="text" name="coBorrowerMailingZip"
                                       id="coBorrowerMailingZip" tabindex="<?php echo $tabIndexNo++; ?>"
                                       value="<?php echo Strings::showField('coBorrowerMailingZip', 'LMRInfo') ?>" size="10"
                                       maxlength="10"
                                       autocomplete="off" <?php if (glCustomJobForProcessingCompany::is($PCID)) { ?> class="mandatory" <?php } ?> >
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorrowerMailingZip', 'LMRInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group showOnLO" style="<?php echo $LOSectionDispOpt ?>">
                    <div class="row">
                        <label class="col-md-5" for="coBMailingPropType">Property Type</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <select name="coBMailingPropType" id="coBMailingPropType"
                                        tabindex="<?php echo $tabIndexNo++; ?>">
                                    <option value=""> - Select -</option>
                                    <?php
                                    for ($i = 0; $i < count($glPropTypeArray); $i++) {
                                        $sOpt = '';
                                        $glPropType = '';
                                        $glPropType = trim($glPropTypeArray[$i]);
                                        $sOpt = Arrays::isSelected($glPropType, $coBMailingPropType);
                                        echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                                    }
                                    ?>
                                </select>
                            <?php } else { ?>
                                <h5><?php echo $coBMailingPropType; ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

            </div> <!-- Co-Borrower Mailing Div -->

        </div> <!-- right 1 Div -->


        <div class="pad2"></div>
        <?php // if (($coBResidedPresentAddr == 'No' || $coBResidedPresentAddr == 'NA') && $isLO == 1) {} else { ?>

        <div class="block-content  showOnCoBorFormerDiv hideOnHMLO hideOnEF"
             style="<?php echo $LOCoBorResidedDispOpt . $HMLOCoBorResidedDispOpt . $EFCoBorResidedDispOpt ?>">
            <div class="text-on-pannel text-primary">
                <div class="showOnLO" style="<?php echo $LOSectionDivDispOpt ?>"> Co-borrower's Former Address</div>
                <div class="hideOnLO" style="<?php echo $LODispOpt ?>">Co-borrower's Previous Address</div>
            </div>

            <div id="coBorDiv4" class="hideOnMF" style="<?php echo $coBorDisp ?>">
                <?php
                if ($isLO == 1 || $isHMLO == 1 || $isEF == 1) {
                } else {
                    if ($allowToEdit) {
                        ?>
                        <div class="form-group hideOnLO hideOnHMLO hideOnEF"
                             style="<?php echo $HMLODispOpt . $EFDispOpt ?>">
                            <div class="row">
                                <label class="col-md-5" for="coBorrowerMailingCity">Previous Mailing address on the last
                                    tax<br>return filed if different from mailing address</label>
                                <div class="col-md-7">
                                    <div <?php if (Strings::showField('coBorPreviousAddrAsMailing', 'LMRInfo') == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?>
                                            id="iscoborrowerPreAdd"
                                            onclick="toggleSwitch('iscoborrowerPreAdd', 'coBorPreviousAddrAsMailing', '1', '0' );autoPopulatePreviousAddress('loanModForm', 'cobor');">
                                        <br>
                                        <input type="hidden" name="coBorPreviousAddrAsMailing"
                                               id="coBorPreviousAddrAsMailing"
                                               value="<?php echo Strings::showField('coBorPreviousAddrAsMailing', 'LMRInfo') ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                }
                ?>
                <div class="form-group">
                    <div class="row">
                        <label class="col-md-5" for="coBorPreviousAddress">Address</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" class="form-control input-sm" name="coBorPreviousAddress"
                                       id="coBorPreviousAddress"
                                       value="<?php echo htmlentities(Strings::showField('coBorPreviousAddress', 'LMRInfo')); ?>" size="40"
                                       maxlength="75" tabindex="<?php echo $tabIndexNo++; ?>"
                                       autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorPreviousAddress', 'LMRInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <label class="col-md-5" for="coBorPreviousCity">City</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" class="form-control input-sm" name="coBorPreviousCity"
                                       id="coBorPreviousCity"
                                       value="<?php echo htmlentities(Strings::showField('coBorPreviousCity', 'LMRInfo')); ?>" size="20"
                                       maxlength="30" tabindex="<?php echo $tabIndexNo++; ?>"
                                       autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('coBorPreviousCity', 'LMRInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="coBorPreviousState">State</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="coBorPreviousState" id="coBorPreviousState"
                                    tabindex="<?php echo $tabIndexNo++; ?>" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <option value=""> - Select -</option>
                                <?php
                                for ($j = 0; $j < count($stateArray); $j++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $coBorPreviousState);
                                    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $coBorPreviousState ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="row">
                    <label class="col-md-5" for="coBorPreviousZip">Zip Code</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="text" class="form-control input-sm" name="coBorPreviousZip"
                                   id="coBorPreviousZip" tabindex="<?php echo $tabIndexNo++; ?>"
                                   value="<?php echo Strings::showField('coBorPreviousZip', 'LMRInfo') ?>" size="10"
                                   maxlength="10"
                                   autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('coBorPreviousZip', 'LMRInfo') ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="form-group showOnLO" style="<?php echo $LOSectionDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBNoOfYrAtPrevAddr">Number of Years at Address ?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <input type="text" class="form-control input-sm" name="coBNoOfYrAtPrevAddr"
                                   id="coBNoOfYrAtPrevAddr" tabindex="<?php echo $tabIndexNo++; ?>"
                                   value="<?php echo Strings::showField('coBNoOfYrAtPrevAddr', 'fileLoanOriginationInfo'); ?>"
                                   size="20" maxlength="30" autocomplete="off">
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('coBNoOfYrAtPrevAddr', 'fileLoanOriginationInfo') ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="form-group showOnLO" style="<?php echo $LOSectionDispOpt ?>">
                <div class="row">
                    <label class="col-md-5" for="coBFormerPropType">Number of Years at Address ?</label>
                    <div class="col-md-7">
                        <?php if ($allowToEdit) { ?>
                            <select name="coBFormerPropType" id="coBFormerPropType"
                                    tabindex="<?php echo $tabIndexNo++; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count($glPropTypeArray); $i++) {
                                    $sOpt = '';
                                    $glPropType = '';
                                    $glPropType = trim($glPropTypeArray[$i]);
                                    $sOpt = Arrays::isSelected($glPropType, $coBFormerPropType);
                                    echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $coBFormerPropType; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>


        </div> <!-- Co-Borrower Previous Div -->
        <?php //} ?>

        <?php if ($isHMLO == 1) { ?>
        <?php } ?>
        <?php require 'coborExperience.php'; ?>


    </div> <!-- right 2 Div -->
    <!-- <?php if ($isHMLO == 1) { ?>
<?php } ?>
    <?php //} ?>
</div> <!-- right Div -->
<div class="clear"></div>
</div> <!-- pad2 Co-borrower section -->
<!-- </div>  -->
<!-- Co-borrower whole section -->
<?php
if ($publicUser == 1) {
    ?>
    <div class="clearfix"></div>
    <div class="pad5">
        <div class="block-content">
            <div class="text-on-pannel text-primary">1st Lien Current Mortgage Scenario</div>
            <div id="lien1Div">
                <div class="row">
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="loanType">Loan
                            Type</label><br>
                        <?php if ($allowToEdit) { ?>
                            <select name="loanType"
                                    id="loanType" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                    tabindex="86">
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count($glLien1LoanTypeArray); $i++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelected(trim($glLien1LoanTypeArray[$i]), $loanType);
                                    echo "<option value=\"" . trim($glLien1LoanTypeArray[$i]) . "\" " . $sOpt . '>' . trim($glLien1LoanTypeArray[$i]) . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $loanType ?></h5>
                        <?php } ?>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien1Terms">Terms</label><br>
                        <?php if ($allowToEdit) { ?>
                            <select name="lien1Terms"
                                    id="lien1Terms" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                    tabindex="87"
                                    onchange="Dates::calculateRemainingMonths('<?php echo date('m/d/Y'); ?>', 'loanModForm', 'loanOriginationDate', 'lien1Terms', 'remainingMonths');">
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 1; $i <= count($lien1TermsArray); $i++) {
                                    $sOpt = '';
                                    if (trim($lien1TermsArray[$i]) == 'Not Sure') {
                                    } else {
                                        $sOpt = Arrays::isSelected(trim($lien1TermsArray[$i]), $lien1Terms);
                                        echo "<option value=\"" . trim($lien1TermsArray[$i]) . "\" " . $sOpt . '>' . trim($lien1TermsArray[$i]) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $lien1Terms; ?></h5>
                        <?php } ?>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien1Rate">Rate %</label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="lien1Rate" id="lien1Rate"
                                   value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('lien1Rate', 'LMRInfo')), 3) ?>"
                                   autocomplete="off" maxlength="12"
                                   size="5" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                   tabindex="88">
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('lien1Rate', 'LMRInfo') ?></h5>
                        <?php } ?>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien1Amount">Current
                            Unpaid Principal Balance $</label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="lien1Amount" id="lien1Amount"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1Amount', 'LMRInfo')) ?>"
                                   autocomplete="off" maxlength="13"
                                   size="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                   tabindex="89"
                                   onblur="currencyConverter(this, this.value);populateNewHomeProposalValue('loanModForm', 'lien1Amount', 'lien1UnPaidBalance');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');">
                        <?php } else { ?>
                            <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1Amount', 'LMRInfo')) ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien1OriginalBalance">Original
                            Loan Amt $</label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="lien1OriginalBalance" id="lien1OriginalBalance"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1OriginalBalance', 'LMRInfo')) ?>"
                                   autocomplete="off" maxlength="13"
                                   size="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                   tabindex="90">
                        <?php } else { ?>
                            <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1OriginalBalance', 'LMRInfo')) ?></h5>
                        <?php } ?>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="loanNumber">Loan
                            Number</label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="loanNumber" id="loanNumber"
                                   value="<?php echo Strings::showField('loanNumber', 'LMRInfo') ?>" autocomplete="off"
                                   maxlength="50" size="10"
                                   tabindex="91" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('loanNumber', 'LMRInfo') ?></h5>
                        <?php } ?>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="noticeAccelerationDate">Notice
                            of Acceleration</label><br>
                        <?php if ($allowToEdit) { ?>
                            <div class="left">
                                <input type="text" name="noticeAccelerationDate" id="noticeAccelerationDate"
                                       value="<?php echo $noticeAccelerationDate ?>" maxlength="10" size="12"
                                       tabindex="91" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory dateNewClass" <?php } else { ?> class=" dateNewClass" <?php } ?>>
                                <br>(MM/DD/YYYY)
                            </div>
                            <div class="left pad2">
                                <a class="fa fa-calendar fa-2x noticeAccelerationDate cursor icon-red"
                                   style="text-decoration:none;" title="Click to open the calender"></a>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $noticeAccelerationDate ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <div class="row">
                    <?php if ($publicUser == 1) { ?>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td> <?php } else { ?>
                        <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="loanOriginationDate">Loan
                                Origination Date</label><br>
                            <?php if ($allowToEdit) { ?>
                                <div class="left">
                                    <input type="text" name="loanOriginationDate" id="loanOriginationDate"
                                           value="<?php echo $noteDate ?>"
                                           autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory dateNewClass" <?php } else { ?>class=" dateNewClass"<?php } ?>
                                           tabindex="92" maxlength="10" size="9"
                                           onchange="Dates::calculateRemainingMonths('<?php echo date('m/d/Y'); ?>', 'loanModForm', 'loanOriginationDate', 'lien1Terms', 'remainingMonths');">
                                    <br>
                                    <div class="hint">(MM/DD/YYYY)</div>
                                </div>
                                <div class="left">
                                    <a class="fa fa-calendar fa-2x loanOriginationDate cursor icon-red"
                                       style="text-decoration:none;" href="javascript:void(0);"
                                       title="Click to open the calender"></a>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo $noteDate ?></h5>
                            <?php } ?>
                        </div>
                        <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="remainingMonths">Remaining
                                Months</label><br>
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="remainingMonths" id="remainingMonths"
                                       value="<?php echo $remainingMonths ?>" size="4" maxlength="3" tabindex="92"/>
                            <?php } else { ?>
                                <h5><?php echo $remainingMonths ?></h5>
                            <?php } ?>
                        </div>
                    <?php } ?>
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="noOfMonthsBehind1">Months
                            Behind <i>(Manual Entry)</label><br>
                        <?php if ($allowToEdit) { ?>
                            <select name="noOfMonthsBehind1" id="noOfMonthsBehind1"
                                    tabindex="93" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <option value=""> - Select -</option>
                                <?php
                                for ($i = 0; $i < count($monthsBehindArray); $i++) {
                                    $sOpt = '';
                                    $monthsBehind = '';
                                    $monthsBehind = trim($monthsBehindArray[$i]);
                                    $sOpt = Arrays::isSelected($monthsBehind, $noOfMonthsBehind1);
                                    echo "<option value=\"" . $monthsBehind . "\" " . $sOpt . '>' . $monthsBehind . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $noOfMonthsBehind1 ?></h5>
                        <?php } ?>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien1LPMade">Last Payment
                            Made</label><br>
                        <?php if ($allowToEdit) { ?>
                            <div class="left">
                                <div class="left">
                                    <input type="text" name="lien1LPMade" id="lien1LPMade"
                                           value="<?php echo $lien1LPMade ?>"
                                           onchange="calculateNoOfDaysBehind('<?php echo date('m/d/Y'); ?>',this.value, 'lien1DaysBehind');"
                                           autocomplete="off" tabindex="104"
                                           maxlength="10" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory dateNewClass" <?php } else { ?> class=" dateNewClass" <?php } ?>>
                                    <br>
                                    <div class="hint">(MM/DD/YYYY)</div>

                                </div>
                                <div class="left">
                                    <a class="fa fa-calendar fa-2x lien1LPMade cursor icon-red"
                                       style="text-decoration:none;" href="javascript:void(0);"
                                       title="Click to open the calender"></a>
                                </div>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $lien1LPMade ?></h5>
                        <?php } ?>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="noticeAccelerationDate">Days
                            Behind</label><br>
                        <div id="lien1DaysBehind" class="pad5"><?php echo $noOfDaysBehind1; ?></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="originalLender1">Original
                            Lender</label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="originalLender1" id="originalLender1"
                                   value="<?php echo $originalLender1 ?>" autocomplete="off" maxlength="50"
                                   tabindex="94" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                   onclick="clearMyMsg('loanModForm', 'originalLender1', '<?php echo $originalLender1Text ?>');"
                                   onfocus="clearMyMsg('loanModForm', 'originalLender1', '<?php echo $originalLender1Text ?>');"
                                   onblur="putMyMsg('loanModForm', 'originalLender1', '<?php echo $originalLender1Text ?>');">
                        <?php } else { ?>
                            <h5><?php echo $originalLender1 ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="servicer1">Current
                            Lender/Servicer</label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="servicer1" id="servicer1" value="<?php echo $servicer1 ?>"
                                   autocomplete="off" maxlength="50"
                                   tabindex="95" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                   onclick="clearMyMsg('loanModForm', 'servicer1', '<?php echo $servicer1Text ?>');"
                                   onfocus="clearMyMsg('loanModForm', 'servicer1', '<?php echo $servicer1Text ?>');"
                                   onblur="putMyMsg('loanModForm', 'servicer1', '<?php echo $servicer1Text ?>');">
                        <?php } else { ?>
                            <h5><?php echo $servicer1 ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="mortgageOwner1">Mortgage
                            Type</label><br>
                        <?php if ($allowToEdit) { ?>
                            <select name="mortgageOwner1" id="mortgageOwner1"
                                    tabindex="96" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <option value=""> - Select -</option>
                                <?php
                                $glMortgageOwnerKeyArray = [];
                                $glMortgageOwnerKeyArray = array_keys($glMortgageOwnerArray);
                                for ($i = 0; $i < count($glMortgageOwnerKeyArray); $i++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelected(trim($glMortgageOwnerKeyArray[$i]), $mortgageOwner1);
                                    echo "<option value=\"" . trim($glMortgageOwnerKeyArray[$i]) . "\" " . $sOpt . '>' . trim($glMortgageOwnerArray[$glMortgageOwnerKeyArray[$i]]) . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $mortgageOwner1 ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="mortgageInvestor1">Mortgage
                            Investor/Owner</label><br>
                        <?php if ($allowToEdit) { ?>
                            <select name="mortgageInvestor1" id="mortgageInvestor1"
                                    tabindex="97" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <option value=""> - Select -</option>
                                <?php
                                $glMortgageInvestorOwnerKeyArray = [];
                                $glMortgageInvestorOwnerKeyArray = array_keys($glMortgageInvestorOwnerArray);
                                for ($i = 0; $i < count($glMortgageInvestorOwnerKeyArray); $i++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelected(trim($glMortgageInvestorOwnerKeyArray[$i]), $mortgageInvestor1);
                                    echo "<option value=\"" . trim($glMortgageInvestorOwnerKeyArray[$i]) . "\" " . $sOpt . '>' . trim($glMortgageInvestorOwnerArray[$glMortgageInvestorOwnerKeyArray[$i]]) . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $mortgageInvestor1 ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien1Payment">Monthly P +
                            I Payment $</label><br>
                        <?php if ($allowToEdit) { ?>
                            <div class="left">
                                <input type="text" name="lien1Payment" id="lien1Payment"
                                       onblur="currencyConverter(this, this.value);calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');"
                                       value="<?php echo $lien1Payment ?>" autocomplete="off" maxlength="13"
                                       tabindex="98" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                       >
                            </div>
                            <div class="left with-children-tip pad2">
                                <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                   href="javascript:void(0);"
                                   title='Enter the monthly <b>Principal and Interest</b> payment only. Please refer to your mortgage statement or call your lender/servicer.<br>If you make interest only payments, please enter that amount. If you have a Pay Option Loan,<br>enter the <b>Fully Indexed Payment</b>, <u>NOT</u> the minimum payment amount.'></a>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $lien1Payment ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="taxes1">Monthly Taxes
                            $</label><br>
                        <?php if ($allowToEdit) { ?>
                            <div class="left">
                                <input type="text" name="taxes1" id="taxes1"
                                       onblur="currencyConverter(this, this.value);calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');"
                                       value="<?php echo $taxes1 ?>" autocomplete="off"
                                       maxlength="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0 && $branchReferralCode != '696655') { ?> class="mandatory" <?php } else { ?><?php } ?>
                                       tabindex="99">
                            </div>
                            <div class="left with-children-tip pad2">
                                <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                   href="javascript:void(0);"
                                   title='Enter the monthly property tax. If you pay annually, please divide the yearly amount by 12.<br>If your lender pays this out of escrow, please reference your mortgage statement or call your lender/servicer.'></a>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $taxes1 ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="insurance1">Monthly
                            Property Insurance $</label><br>
                        <?php if ($allowToEdit) { ?>
                            <div class="left">
                                <input type="text" name="insurance1" id="insurance1"
                                       onblur="currencyConverter(this, this.value);calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');"
                                       value="<?php echo $insurance1 ?>" autocomplete="off"
                                       maxlength="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                       tabindex="100">
                            </div>
                            <div class="left with-children-tip pad2">
                                <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                   href="javascript:void(0);"
                                   title='Enter the monthly property insurance. If you pay annually, please divide the amount by 12.<br>If your lender pays this out of escrow, please reference your mortgage statement or call your lender/servicer or property insurance provider.'></a>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $insurance1 ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="floodInsurance1">Flood
                            Insurance $</label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="floodInsurance1" id="floodInsurance1" tabindex="101"
                                   onblur="currencyConverter(this, this.value);calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');"
                                   value="<?php echo $floodInsurance1 ?>" autocomplete="off" maxlength="12"
                                   onblur=""
                                <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                        <?php } else { ?>
                            <h5><?php echo $floodInsurance1 ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="mortgageInsurance1">Priv.
                            Monthly Mortg. Insurance $</label><br>
                        <?php if ($allowToEdit) { ?>
                            <div class="left">
                                <input type="text" name="mortgageInsurance1" id="mortgageInsurance1"
                                       onblur="currencyConverter(this, this.value);calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');populateNewHomeProposalValue('loanModForm', 'mortgageInsurance1', 'lien1MtgInsurance');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                       value="<?php echo $mortgageInsurance1 ?>" autocomplete="off" maxlength="12"
                                       tabindex="102"
                                    <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0 && $branchReferralCode != '696655') { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            </div>
                            <div class="left with-children-tip pad2">
                                <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                   href="javascript:void(0);"
                                   title='If you have Mortgage Insurance, please enter the monthly amount allocated for this. Mortgage Insurance is usually for loans originally taken over 80% LTV.<br>Reference your mortgage statement to find this amount if applicable, or call your lender/servicer.'></a>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $mortgageInsurance1 ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="HOAFees1">Monthly H.O.A
                            Fees $</label><br>
                        <?php if ($allowToEdit) { ?>
                            <div class="left">
                                <input type="text" name="HOAFees1" id="HOAFees1" tabindex="103"
                                       onblur="currencyConverter(this, this.value);calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');"
                                       value="<?php echo $HOAFees1 ?>" autocomplete="off" maxlength="12"
                                    <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            </div>
                            <div class="left with-children-tip pad2">
                                <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                   href="javascript:void(0);"
                                   title='HOA Fees or associations fees are paid be homeowners who live in a condominium, town house, or housing development.<br>This amount is usually paid separate from a mortgage payment, but is considered in the housing payment.'></a>
                            </div>
                        <?php } else { ?>
                            <h5><?php echo $HOAFees1 ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="totalPayment">Total
                            Monthly Payment</label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="totalPayment" id="totalPayment" value="<?php echo $totalPayment ?>"
                                   autocomplete="off" maxlength="13" readonly class="totalPaymentDiv">
                        <?php } else { ?>
                            <h5><?php echo $totalPayment ?></h5>
                        <?php } ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <label class="control-label" for="lien1BalanceDue">Past Due Interest
                            <div class="right with-children-tip">
                                <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                   href="javascript:void(0);"
                                   title='This is the total accrued interest that is past due. Do NOT include any past due principal or escrow.'></a>
                            </div>
                        </label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="lien1BalanceDue" id="lien1BalanceDue"
                                   value="<?php echo Strings::showField('lien1BalanceDue', 'LMRInfo') ?>" autocomplete="off"
                                   maxlength="50" tabindex="105"
                                   onblur="populateNewHomeProposalValue('loanModForm', 'lien1BalanceDue', 'lien1PastDuePI');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                        <?php } else { ?>
                            <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1BalanceDue', 'LMRInfo')) ?></h5>
                        <?php } ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <label class="control-label" for="lien1ProposalEscrowShortage">Out of Pocket Escrow Adv $
                            <div class="right with-children-tip">
                                <a class="fa fa-info-circle fa-2x tip-bottom cursor" style="text-decoration:none;"
                                   title="This is the amount needed to get the property taxes and insurance current"></a>
                            </div>
                        </label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="lien1ProposalEscrowShortage" id="lien1ProposalEscrowShortage"
                                   onblur="currencyConverter(this, this.value);populateNewHomeProposalValue('loanModForm', 'lien1ProposalEscrowShortage', 'lien1PocketProposalEscrowAdvances');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                   value="<?php echo $escrowAdvances ?>" maxlength="13" tabindex="106"
                                <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>/>
                            <?php
                        } else {
                            if ($escrowAdvances < 0) { ?><span style="color:#ff0000">(<?php } ?>
                            $ <?php echo proposalFormula::convertToAbsoluteValue($escrowAdvances) ?><?php if ($escrowAdvances < 0) { ?>)</span><?php }
                        }
                        ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <label class="control-label" for="projectedEscrowAdvances">Projected Escrow Adv during Trial $
                            <div class="right with-children-tip">
                                <a class="fa fa-info-circle fa-2x tip-bottom cursor" style="text-decoration:none;"
                                   title="Most trial periods are 3 months. Please estimate the amount of escrow payments required to be recapitalized for taxes and insurances"></a>
                            </div>
                        </label><br>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="projectedEscrowAdvances" id="projectedEscrowAdvances"
                                   onblur="currencyConverter(this, this.value);populateNewHomeProposalValue('loanModForm', 'projectedEscrowAdvances', 'lien1ProjectedProposalEscrowAdvances');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                   value="<?php echo $projectedEscrowAdvances ?>" maxlength="13"
                                   tabindex="107" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>/>
                            <?php
                        } else {
                            if ($projectedEscrowAdvances < 0) { ?><span style="color:#ff0000">(<?php } ?>
                            $ <?php echo proposalFormula::convertToAbsoluteValue($projectedEscrowAdvances) ?><?php if ($projectedEscrowAdvances < 0) { ?>)</span><?php }
                        }
                        ?>
                    </div>

                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <label class="control-label" for="pastDueMtg">Past Due Mtg. Ins $</label><br>
                        <input type="text" name="pastDueMtg" id="pastDueMtg" tabindex="108"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo $pastDueMtg ?>" autocomplete="off"
                               maxlength="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <label class="control-label" for="pastDueHOA">Past Due H.O.A $</label><br>
                        <input type="text" name="pastDueHOA" id="pastDueHOA" tabindex="109"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo $pastDueHOA ?>" autocomplete="off"
                               maxlength="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <label class="control-label" for="lien1ProposalFeesAdminCosts">Late Fees (Not Included) $
                            <div class="right with-children-tip">
                                <a class="fa fa-info-circle fa-2x tip-bottom cursor" style="text-decoration:none;"
                                   title="Late fees should NOT be capitalized back into the new loan balance"></a>
                            </div>
                        </label><br>
                        <?php
                        if ($allowToEdit) {
                            ?>
                            <input type="text" name="lien1ProposalFeesAdminCosts" id="lien1ProposalFeesAdminCosts"
                                   onblur="currencyConverter(this, this.value);populateNewHomeProposalValue('loanModForm', 'lien1ProposalFeesAdminCosts', 'lien1ProposalLateFees');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                   value="<?php echo $proposalLateFees ?>" maxlength="13" tabindex="110"
                                <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>/>
                            <?php
                        } else {
                            if ($proposalLateFees < 0) { ?><span style="color:#ff0000">(<?php } ?>
                            $ <?php echo proposalFormula::convertToAbsoluteValue($proposalLateFees) ?><?php if ($proposalLateFees < 0) { ?>)</span><?php }
                        }
                        ?>
                    </div>

                </div>
            </div>
        </div>
    </div> <!-- pad2 current mortgage section -->

    <div class="block-content">
        <div class="text-on-pannel text-primary">2nd Lien Mortgage Scenario</div>
        <div id="lien2Div">
            <div class="row">
                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="loanType2">2nd lien Loan
                        Type</label><br>
                    <?php if ($allowToEdit) { ?>
                        <select name="loanType2" id="loanType2"
                                tabindex="111" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            <option value=""> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($glLien2LoanTypeArray); $i++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($glLien2LoanTypeArray[$i]), $loanType2);
                                echo "<option value=\"" . trim($glLien2LoanTypeArray[$i]) . "\" " . $sOpt . '>' . trim($glLien2LoanTypeArray[$i]) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $loanType2 ?></h5>
                    <?php } ?>
                </div>
                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien2Terms">Terms</label><br>
                    <?php if ($allowToEdit) { ?>
                        <select name="lien2Terms" id="lien2Terms"
                                tabindex="113" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            <option value=""> - Select -</option>
                            <?php
                            for ($i = 1; $i <= count($lien2TermsArray); $i++) {
                                $sOpt = '';
                                if (trim($lien2TermsArray[$i]) == 'Not Sure') {
                                } else {
                                    $sOpt = Arrays::isSelected(trim($lien2TermsArray[$i]), $lien2Terms);
                                    echo "<option value=\"" . trim($lien2TermsArray[$i]) . "\" " . $sOpt . '>' . trim($lien2TermsArray[$i]) . '</option>';
                                }
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $lien2Terms; ?></h5>
                    <?php } ?>
                </div>


                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien2Rate">Rate %</label><br>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="lien2Rate" id="lien2Rate" tabindex="114"
                               value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('lien2Rate', 'LMRInfo')), 3) ?>"
                               autocomplete="off" maxlength="12"
                               size="5" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="form-control input-sm mandatory" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('lien2Rate', 'LMRInfo') ?></h5>
                    <?php } ?>
                </div>

                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien2Amount">Current Unpaid
                        Balance $</label><br>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="lien2Amount" id="lien2Amount" tabindex="115"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien2Amount', 'LMRInfo')) ?>"
                               autocomplete="off" maxlength="13"
                               size="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="form-control input-sm mandatory" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('lien2Amount', 'LMRInfo') ?></h5>
                    <?php } ?>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien2OriginalBalance">Original
                        Loan Amount $</label><br>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="lien2OriginalBalance" id="lien2OriginalBalance" tabindex="116"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien2OriginalBalance', 'LMRInfo')) ?>"
                               autocomplete="off" maxlength="13"
                               size="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('lien2OriginalBalance', 'LMRInfo') ?></h5>
                    <?php } ?>
                </div>

                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien2Payment">Payment
                        $</label><br>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="lien2Payment" id="lien2Payment" tabindex="117"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien2Payment', 'LMRInfo')) ?>"
                               autocomplete="off" maxlength="13"
                               size="6" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('lien2Payment', 'LMRInfo') ?></h5>
                    <?php } ?>
                </div>

                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="loanNumber2">Loan
                        Number</label><br>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="loanNumber2" id="loanNumber2" tabindex="118"
                               value="<?php echo Strings::showField('loanNumber2', 'LMRInfo'); ?>" autocomplete="off"
                               maxlength="50"
                               size="10" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="form-control input-sm mandatory" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('loanNumber2', 'LMRInfo') ?></h5>
                    <?php } ?>
                </div>

                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="lien2BalanceDue">Past Due
                        Interest</label><br>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="lien2BalanceDue" id="lien2BalanceDue" tabindex="119"
                               value="<?php echo Strings::showField('lien2BalanceDue', 'LMRInfo') ?>" autocomplete="off"
                               maxlength="50"
                               size="10" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('lien2BalanceDue', 'LMRInfo') ?></h5>
                    <?php } ?>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 col-sm-6 col-xs-12">
                    <label class="control-label" for="lien2LPMade">Last Payment Made</label><br>
                    <?php if ($allowToEdit) { ?>
                        <div class="left">
                            <input type="text" name="lien2LPMade" id="lien2LPMade" tabindex="120"
                                   value="<?php echo $lien2LPMade ?>" autocomplete="off" maxlength="10" size="9"
                                   onchange="calculateNoOfDaysBehind('<?php echo date('m/d/Y'); ?>',this.value, 'lien2DaysBehind');" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory dateNewClass" <?php } else { ?> class=" dateNewClass" <?php } ?>>
                            <br>
                            <div class="hint">(MM/DD/YYYY)</div>
                        </div>
                        <div class="left">
                            <a class="fa fa-calendar fa-2x lien2LPMade cursor icon-red" style="text-decoration:none;"
                               href="javascript:void(0);" title="Click to open the calender"></a>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo $lien2LPMade ?></h5>
                    <?php } ?>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="servicer2">Current
                        Lender/Servicer</label><br>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="servicer2" id="servicer2" value="<?php echo $servicer2 ?>"
                               tabindex="121" autocomplete="off" maxlength="50" size="45"
                               onclick="clearMyMsg('loanModForm', 'servicer2', '<?php echo $servicer2Text ?>');"
                               onfocus="clearMyMsg('loanModForm', 'servicer2', '<?php echo $servicer2Text ?>');"
                               onblur="putMyMsg('loanModForm', 'servicer2', '<?php echo $servicer2Text ?>');"
                               style="width:200px;" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo $servicer2 ?></h5>
                    <?php } ?>
                </div>

                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="noOfMonthsBehind2">Months
                        Behind <i>(Manual Entry)</label><br>
                    <?php if ($allowToEdit) { ?>
                        <select name="noOfMonthsBehind2" id="noOfMonthsBehind2"
                                tabindex="122" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            <option value=""> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($monthsBehindArray); $i++) {
                                $sOpt = '';
                                $monthsBehind = '';
                                $monthsBehind = trim($monthsBehindArray[$i]);
                                $sOpt = Arrays::isSelected($monthsBehind, $noOfMonthsBehind2);
                                echo "<option value=\"" . $monthsBehind . "\" " . $sOpt . '>' . $monthsBehind . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $noOfMonthsBehind2 ?></h5>
                    <?php } ?>
                </div>

                <div class="col-md-4 col-sm-6 col-xs-12"><label class="control-label">Days Behind <i>(Based on last
                            payment date)</i></label><br>
                    <div id="lien2DaysBehind"><?php echo $noOfDaysBehind2 ?></div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="originalLender2">Original
                        Lender</label><br>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="originalLender2" id="originalLender2"
                               value="<?php echo $originalLender2 ?>" tabindex="123" autocomplete="off" maxlength="50"
                               size="45"
                               onclick="clearMyMsg('loanModForm', 'originalLender2', '<?php echo $originalLender2Text ?>');"
                               onfocus="clearMyMsg('loanModForm', 'originalLender2', '<?php echo $originalLender2Text ?>');"
                               onblur="putMyMsg('loanModForm', 'originalLender2', '<?php echo $originalLender2Text ?>');"
                               style="width:200px;" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                    <?php } else { ?>
                        <h5><?php echo $originalLender2 ?></h5>
                    <?php } ?>
                </div>

                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="mortgageOwner2">Mortgage
                        Type</label><br>
                    <?php if ($allowToEdit) { ?>
                        <select name="mortgageOwner2" id="mortgageOwner2"
                                tabindex="124" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            <option value=""> - Select -</option>
                            <?php
                            $glMortgageOwnerKeyArray = [];
                            $glMortgageOwnerKeyArray = array_keys($glMortgageOwnerArray);
                            for ($i = 0; $i < count($glMortgageOwnerKeyArray); $i++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($glMortgageOwnerKeyArray[$i]), $mortgageOwner2);
                                echo "<option value=\"" . trim($glMortgageOwnerKeyArray[$i]) . "\" " . $sOpt . '>' . trim($glMortgageOwnerArray[$glMortgageOwnerKeyArray[$i]]) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $mortgageOwner2 ?></h5>
                    <?php } ?>
                </div>
                <div class="col-md-3 col-sm-6 col-xs-12"><label class="control-label" for="mortgageInvestor2">Mortgage
                        Investor/Owner</label><br>
                    <?php if ($allowToEdit) { ?>
                        <select name="mortgageInvestor2" id="mortgageInvestor2"
                                tabindex="125" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            <option value=""> - Select -</option>
                            <?php
                            $glMortgageInvestorOwnerKeyArray = [];
                            $glMortgageInvestorOwnerKeyArray = array_keys($glMortgageInvestorOwnerArray);
                            for ($i = 0; $i < count($glMortgageInvestorOwnerKeyArray); $i++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($glMortgageInvestorOwnerKeyArray[$i]), $mortgageInvestor2);
                                echo "<option value=\"" . trim($glMortgageInvestorOwnerKeyArray[$i]) . "\" " . $sOpt . '>' . trim($glMortgageInvestorOwnerArray[$glMortgageInvestorOwnerKeyArray[$i]]) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $mortgageInvestor2 ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
    </div> <!-- 2nd lien div -->

    <?php
}
?>
<div class="clear pad5"></div>
<?php /* Government For Monitoring Purposes  */ ?>
<div style="<?php echo $LOSectionDivDispOpt ?>" class="showOnLO pad5">
    <div class="pad2 even"><h2>Information for Government Monitoring Purposes</h2></div>
    <div class="clear pad5"></div>
    <div class="col-md-6">
        <div class="block-content">
            <div class="text-on-pannel text-primary">Borrower</div>
            <div class="pad2">
                <div class="form-group row">
                    <label class="col-md-7">Do you wish to furnish this information?</label>
                    <div class="col-md-5">
                        <?php
                        if ($allowToEdit) {
                            ?>
                            <input type="radio" name="PublishBInfo" value="2"
                                   tabindex="169" <?php echo Strings::isChecked('2', $PublishBInfo); ?>
                                   onclick="showAndHideQADiv16(this.value, 'borrowerSelector');">Yes
                            <input type="radio" name="PublishBInfo" value="1"
                                   tabindex="170" <?php echo Strings::isChecked('1', $PublishBInfo); ?>
                                   onclick="showAndHideQADiv16(this.value, 'borrowerSelector');">No
                            <input type="radio" name="PublishBInfo" value="3" tabindex="171"
                                   <?php echo Strings::isChecked('3', $PublishBInfo); ?>onclick="showAndHideQADiv16(this.value, 'borrowerSelector');">NA
                            <?php
                        } else {
                            if ($PublishBInfo == 2) {
                                echo '<h5>Yes</h5>';
                            } else {
                                echo '<h5>No</h5>';
                            }
                        }
                        ?>
                    </div>
                </div>
                <div class="form-group row even">
                    <label class="col-md-7">What is your Ethnicity:</label>
                    <div class="col-md-5">
                        <?php
                        if ($allowToEdit) {
                            ?>
                            <input type="radio" name="BEthnicity" value="2" <?php echo Strings::isChecked('2', $BEthnicity); ?>
                                   tabindex="172" class="borrowerSelector">Hispanic or Latino
                            <input type="radio" name="BEthnicity" value="1" <?php echo Strings::isChecked('1', $BEthnicity); ?>
                                   tabindex="173" class="borrowerSelector">Not Hispanic or Latino
                        <?php } else { ?>
                            <input type="radio" name="BEthnicity" value="2"
                                   disabled <?php echo Strings::isChecked('2', $BEthnicity); ?> tabindex="174">Hispanic or Latino
                            <input type="radio" name="BEthnicity" value="1"
                                   disabled <?php echo Strings::isChecked('1', $BEthnicity); ?>
                                   tabindex="175">Not Hispanic or Latino
                            <?php
                        }
                        ?>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-md-7">What is your Race:</label>
                    <div class="col-md-5">
                        <?php
                        if ($allowToEdit) {
                            ?>
                            <input type="radio" name="BRace" value="1" <?php echo Strings::isChecked($BRace, '1'); ?>
                                   tabindex="176" class="borrowerSelector">&nbsp;American Indian
                            <input type="radio" name="BRace" value="2" <?php echo Strings::isChecked($BRace, '2'); ?>
                                   tabindex="177" class="borrowerSelector">&nbsp;Asian<br>
                            <input type="radio" name="BRace" value="3" <?php echo Strings::isChecked($BRace, '3'); ?>
                                   tabindex="178" class="borrowerSelector">&nbsp;Black or African American<br>
                            <input type="radio" name="BRace" value="4" <?php echo Strings::isChecked($BRace, '4'); ?>
                                   tabindex="179"
                                   class="borrowerSelector">&nbsp;Native Hawaiian or Other Pacific Islander<br>
                            <input type="radio" name="BRace" value="5" <?php echo Strings::isChecked($BRace, '5'); ?>
                                   tabindex="180" class="borrowerSelector">&nbsp;White
                            <div class="clsAnswer"></div>
                        <?php } else { ?>
                            <input type="radio" name="BRace" value="1" disabled <?php echo Strings::isChecked($BRace, '1'); ?>
                                   tabindex="181">&nbsp;American Indian
                            <input type="radio" name="BRace" value="2" disabled <?php echo Strings::isChecked($BRace, '2'); ?>
                                   tabindex="182">&nbsp;Asian<br>
                            <input type="radio" name="BRace" value="3" disabled <?php echo Strings::isChecked($BRace, '3'); ?>
                                   tabindex="183">&nbsp;Black or African American<br>
                            <input type="radio" name="BRace" value="4" disabled <?php echo Strings::isChecked($BRace, '4'); ?>
                                   tabindex="184">&nbsp;Native Hawaiian or Other Pacific Islander<br>
                            <input type="radio" name="BRace" value="5" disabled <?php echo Strings::isChecked($BRace, '5'); ?>
                                   tabindex="185">&nbsp;White
                        <?php } ?>
                    </div>
                </div>
                <div class="form-group row even">
                    <label class="col-md-7">Sex:</label>
                    <div class="col-md-5">
                        <?php
                        if ($allowToEdit) {
                            ?>
                            <input type="radio" name="BGender" value="2" <?php echo Strings::isChecked('2', $BGender); ?>
                                   class="borrowerSelector" tabindex="186">Male
                            <input type="radio" name="BGender" value="1" <?php echo Strings::isChecked('1', $BGender); ?>
                                   class="borrowerSelector" tabindex="187">Female
                        <?php } else { ?>
                            <input type="radio" name="BGender" value="2"
                                   disabled <?php echo Strings::isChecked('2', $BGender); ?> tabindex="188">Male
                            <input type="radio" name="BGender" value="1"
                                   disabled <?php echo Strings::isChecked('1', $BGender); ?> tabindex="189">Female
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    //     if($isCoBorrower == 1) {
    ?>
    <div class="col-md-6">
        <div class="block-content" id="coBorGovDiv" style="<?php if ($isCoBorrower == 1) { ?>display:block<?php } ?>">
            <div class="text-on-pannel text-primary">Co-Borrower</div>
            <div class="form-group row">
                <label class="col-md-7">Do you wish to furnish this information?</label>
                <div class="col-md-5">
                    <?php
                    if ($allowToEdit) {
                        ?>              <input type="radio" name="PublishCBInfo" value="2"
                                               tabindex="190" <?php echo Strings::isChecked('2', $PublishCBInfo); ?>
                                               onclick="showAndHideQADiv16(this.value, 'coBorrower');">Yes
                        <input type="radio" name="PublishCBInfo" value="1"
                               tabindex="191" <?php echo Strings::isChecked('1', $PublishCBInfo); ?>
                               onclick="showAndHideQADiv16(this.value, 'coBorrower');">No
                        <input type="radio" name="PublishCBInfo" value="3"
                               tabindex="192" <?php echo Strings::isChecked('3', $PublishCBInfo); ?>
                               onclick="showAndHideQADiv16(this.value, 'coBorrower');">NA

                        <?php
                    } else {
                        if ($PublishCBInfo == 2) {
                            echo '<h5>Yes</h5>';
                        } else {
                            echo '<h5>No</h5>';
                        }
                    }
                    ?>
                </div>
            </div>
            <div class="form-group row even">
                <label class="col-md-7">What is your Ethnicity:</label>
                <div class="col-md-5">
                    <?php
                    if ($allowToEdit) {
                        ?>
                        <input type="radio" name="CBEthnicity" value="2" <?php echo Strings::isChecked('2', $CBEthnicity); ?>
                               tabindex="193" class="coBorrower"> Hispanic or Latino
                        <input type="radio" name="CBEthnicity" value="1" <?php echo Strings::isChecked('1', $CBEthnicity); ?>
                               tabindex="194" class="coBorrower">Not Hispanic or Latino
                    <?php } else { ?>
                        <input type="radio" name="CBEthnicity" disabled
                               value="2" <?php echo Strings::isChecked('2', $CBEthnicity); ?> tabindex="195"> Hispanic or Latino
                        <input type="radio" name="CBEthnicity" disabled
                               value="1" <?php echo Strings::isChecked('1', $CBEthnicity); ?>
                               tabindex="196">Not Hispanic or Latino
                        <?php
                    }
                    ?>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-7">What is your Race:</label>
                <div class="col-md-5">
                    <?php
                    if ($allowToEdit) {
                        ?>
                        <input type="radio" name="CBRace" value="1" <?php echo Strings::isChecked($CBRace, '1'); ?>tabindex="197"
                               class="coBorrower">&nbsp;American Indian
                        <input type="radio" name="CBRace" value="2" <?php echo Strings::isChecked($CBRace, '2'); ?>tabindex="198"
                               class="coBorrower">&nbsp;Asian<br>
                        <input type="radio" name="CBRace" value="3" <?php echo Strings::isChecked($CBRace, '3'); ?>tabindex="199"
                               class="coBorrower">&nbsp;Black or African American<br>
                        <input type="radio" name="CBRace" value="4" <?php echo Strings::isChecked($CBRace, '4'); ?>tabindex="200"
                               class="coBorrower">&nbsp;Native Hawaiian or Other Pacific Islander<br>
                        <input type="radio" name="CBRace" value="5" <?php echo Strings::isChecked($CBRace, '5'); ?>tabindex="201"
                               class="coBorrower">&nbsp;White
                    <?php } else { ?>
                        <input type="radio" name="CBRace" value="1" disabled <?php echo Strings::isChecked($CBRace, '1'); ?>
                               tabindex="202">&nbsp;American Indian
                        <input type="radio" name="CBRace" value="2" disabled <?php echo Strings::isChecked($CBRace, '2'); ?>
                               tabindex="203">&nbsp;Asian<br>
                        <input type="radio" name="CBRace" value="3" disabled <?php echo Strings::isChecked($CBRace, '3'); ?>
                               tabindex="204">&nbsp;Black or African American<br>
                        <input type="radio" name="CBRace" value="4" disabled <?php echo Strings::isChecked($CBRace, '4'); ?>
                               tabindex="205">&nbsp;Native Hawaiian or Other Pacific Islander<br>
                        <input type="radio" name="CBRace" value="5" disabled <?php echo Strings::isChecked($CBRace, '5'); ?>
                               tabindex="206">&nbsp;White
                        <?php
                    }
                    ?>
                </div>
            </div>
            <div class="form-group row even">
                <label class="col-md-7">Sex:</label>
                <div class="col-md-5">
                    <?php
                    if ($allowToEdit) {
                        ?>
                        <input type="radio" name="CBGender" value="2" <?php echo Strings::isChecked('2', $CBGender); ?>
                               class="coBorrower" tabindex="207">Male
                        <input type="radio" name="CBGender" value="1" <?php echo Strings::isChecked('1', $CBGender); ?>
                               class="coBorrower" tabindex="208">Female
                    <?php } else { ?>
                        <input type="radio" name="CBGender" disabled value="2" <?php echo Strings::isChecked('2', $CBGender); ?>
                               tabindex="209">Male
                        <input type="radio" name="CBGender" disabled value="1" <?php echo Strings::isChecked('1', $CBGender); ?>
                               tabindex="210">Female
                        <?php
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <?php
    //	 } /* Government For Monitoring Purposes  */
    ?>
</div>
<?php
if ($isFU != 1) {
    $notesDisp = 'display: block;';
} else {
    $notesDisp = 'display: none;';
}

if ($userGroup != 'Client') {
    ?>
    <div class="pad5 hideOnMF" style="<?php echo $notesDisp . $MFDispOpt ?>">
        <div class="form-group hideOnFU">
            <div class="row">
                <label class="col-md-7"
                       for="mortgageNotes"><?php if ($isHMLO == 1) { ?>Borrower<?php } else { ?>Client<?php } ?>
                    Notes <?php if ($allowToEdit) { ?><a href="#"
                                                         onclick="printerfriendlytextbox('mortgageNotes', '<?php echo Strings::escapeQuote(Strings::showField('borrowerFName', 'LMRInfo')) ?>', '<?php echo Strings::escapeQuote(Strings::showField('borrowerFName', 'LMRInfo')) ?>');return false;">
                            PrintFriendly</a><?php } ?></label>
                <div class="col-md-6">
                    <textarea class="form-control" rows="9" name="mortgageNotes" tabindex="126"
                              id="mortgageNotes"></textarea>
                </div>
            </div>
        </div>
    </div>
    <?php
}
if ($allowToEdit) { ?>

    <?php /** Merchant Funding start **/ ?>
    <div class="showOnMF" style="<?php echo $MFSectionDispOpt; ?>">
        <div class="left" style="width:48%">
            <div class="block-content">
                <div class="head">Personal Information</div>
                <div id="personalInfoDiv">
                    <table style="width:100%">
                        <tr>
                            <td style="width:50%">Do you own the majority of the business?</td>
                            <td style="width:50%">
                                <input type="radio" name="majorityBusiness" value="Yes"
                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $majorityBusiness); ?>>Yes
                                <input type="radio" name="majorityBusiness" value="No"
                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $majorityBusiness); ?>>No
                            </td>
                        </tr>
                        <tr class="even">
                            <td>Owner's Name</td>
                            <td>
                                <input type="text" name="ownersName"
                                       value="<?php echo Strings::showField('ownersName', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                        <tr>
                            <td>% Owned</td>
                            <td>
                                <input type="text" name="Owned" value="<?php echo Strings::showField('Owned', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                    </table>
                </div> <!-- personalInfoDiv END -->
            </div><!-- Personal Information END -->
            <div class="clear pad2"></div>
            <div class="block-content">
                <div class="head">Business Information</div>
                <div id="businessInfoDiv">
                    <table style="width:100%">
                        <tr>
                            <td style="width:40%">Business Name</td>
                            <td style="width:60%">
                                <input type="text" name="businessName"
                                       value="<?php echo Strings::showField('businessName', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                        <tr class="even">
                            <td>Business Name (DBA)</td>
                            <td>
                                <input type="text" name="businessNameDBA"
                                       value="<?php echo Strings::showField('businessNameDBA', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                        <tr>
                            <td>Business Legal Name</td>
                            <td>
                                <input type="text" name="businessLegalName" id="businessLegalName"
                                       value="<?php echo Strings::showField('businessLegalName', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                        <tr class="even">
                            <td>Business Tax ID</td>
                            <td>
                                <input type="text" name="businessTax" id="businessTax"
                                       value="<?php echo Strings::showField('businessTax', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                        <tr>
                            <td>Business Entity Type</td>
                            <td>
                                <input type="text" name="businessType" id="businessType"
                                       value="<?php echo Strings::showField('businessType', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                        <tr class="even">
                            <td>Website</td>
                            <td>
                                <input type="text" name="Website" id="Website"
                                       value="<?php echo Strings::showField('Website', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                        <tr>
                            <td>Industry</td>
                            <td>
                                <input type="text" name="Industry" id="Industry"
                                       value="<?php echo Strings::showField('Industry', 'MFInfo') ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            </td>
                        </tr>
                        <tr class="even">
                            <td nowrap>Are your clients businesses or consumers ?</td>
                            <td>
                                <input type="radio" name="businessConsumers" value="bus"
                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('bus', $businessConsumers); ?>>Businesses
                                <input type="radio" name="businessConsumers" value="con"
                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('con', $businessConsumers); ?>>Consumers
                            </td>
                        </tr>
                    </table>
                </div><!-- businessInfoDiv END -->
            </div><!-- Business END -->
        </div><!-- Left DIV END -->
        <div class="block-content right" style="width:48%">
            <div class="head">Financial Information</div>
            <div id="financialInfoDiv">
                <table>
                    <tr>
                        <td style="width:50%">Months in business?</td>
                        <td style="width:50%">
                            <input type="text" name="monthsBusiness"
                                   value="<?php echo Strings::showField('monthsBusiness', 'MFInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr class="even">
                        <td>Average monthly revenue ($)</td>
                        <td>
                            <input type="text" name="monthlyRevenue"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('monthlyRevenue', 'MFInfo')) ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr>
                        <td>Credit score</td>
                        <td>
                            <input type="text" name="creditScore" id="creditScore"
                                   value="<?php echo Strings::showField('creditScore', 'MFInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr class="even">
                        <td>Annual Gross Revenue ($)</td>
                        <td>
                            <input type="text" name="grossRevenue" id="grossRevenue"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('grossRevenue', 'MFInfo')) ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr>
                        <td>Monthly Bank Deposit Volume ($)</td>
                        <td>
                            <input type="text" name="monthlyDepositVolume" id="monthlyDepositVolume"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('monthlyDepositVolume', 'MFInfo')) ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr class="even">
                        <td>Average Daily Bank Balance ($)</td>
                        <td>
                            <input type="text" name="bankBalance" id="bankBalance"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('bankBalance', 'MFInfo')) ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr>
                        <td>Do you have invoices to factor?</td>
                        <td>
                            <?php
                            if ($invoices == 'Yes') {
                                $invoicesDisp = 'display: block;';
                            } else {
                                $invoicesDisp = 'display: none;';
                            }
                            ?>
                            <input type="radio" name="invoices" id="iyes" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $invoices); ?>>Yes
                            <input type="radio" name="invoices" id="ino" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $invoices); ?>>No
                        </td>
                    </tr>
                    <tr class="even" id="row-yes" style="<?php echo $invoicesDisp; ?>">
                        <td style="text-align:right;">How much ($) ?</td>
                        <td><input type="text" name="invoiceAmount" id="invoiceAmount"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('invoiceAmount', 'MFInfo')) ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>"></td>
                    </tr>
                    <tr class="even">
                        <td>Requested Advance Amount ($)</td>
                        <td>
                            <input type="text" name="requestedAmount" id="requestedAmount"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('requestedAmount', 'MFInfo')) ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr>
                        <td>Requested Term Length (in Months)</td>
                        <td>
                            <input type="text" name="requestedTermLength" id="requestedTermLength"
                                   value="<?php echo Strings::showField('requestedTermLength', 'MFInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr class="even">
                        <td>How do you plan to use your funding?</td>
                        <td>
                            <input type="text" name="funding" id="funding"
                                   value="<?php echo Strings::showField('funding', 'MFInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr>
                        <td>Does the business accept credit cards?</td>
                        <td>
                            <input type="radio" name="businessCreditCards" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $businessCreditCards); ?>>Yes
                            <input type="radio" name="businessCreditCards" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $businessCreditCards); ?>>No
                        </td>
                    </tr>
                    <tr class="even">
                        <td>Does the business have exsisting financing?</td>
                        <td>
                            <input type="radio" name="businessFinancing" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $businessFinancing); ?>>Yes
                            <input type="radio" name="businessFinancing" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $businessFinancing); ?>>No
                        </td>
                    </tr>
                    <tr>
                        <td>On your most recent business tax return, did you show a net profit or a net loss?</td>
                        <td>
                            <input type="radio" name="netProfit" value="Pro"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Pro', $netProfit); ?>>Profit
                            <input type="radio" name="netProfit" value="Loss"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Loss', $netProfit); ?>>Loss
                        </td>
                    </tr>
                    <tr class="even">
                        <td>Amount?</td>
                        <td>
                            <input type="text" name="amount" id="amount"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('amount', 'MFInfo')) ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                    <tr>
                        <td>Do you own collateral?</td>
                        <td>
                            <input type="radio" name="ownCollateral" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $ownCollateral); ?>>Yes
                            <input type="radio" name="ownCollateral" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $ownCollateral); ?>>No
                        </td>
                    </tr>
                    <tr class="even">
                        <td>Sum of monthly recurring debt payments</td>
                        <td>
                            <input type="text" name="monthlyDebtPayments" id="monthlyDebtPayments"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('monthlyDebtPayments', 'MFInfo')) ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>">
                        </td>
                    </tr>
                </table>
            </div><!-- financialInfoDiv END -->
        </div><!-- Financial Info END -->
        <div class="clear"></div>
    </div>
<?php } ?>
<div class="showOnFU hideOnLO hideOnMF" style="<?php echo $FUSectionDispOpt; ?>">
    <div class="block-content">
        <div class="head">Q+A</div>
        <div id="QAInfoDiv">
            <table cellpadding="2" border="0" style="width:100%">
                <tr>
                    <td>Credit Check Total Username</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="creditChkUsername" id="creditChkUsername"
                                   value="<?php echo htmlentities(Strings::showField('creditChkUsername', 'FUInfo')); ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off" maxlength="50"
                                   style="width:210px"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('creditChkUsername', 'FUInfo') ?></h5>
                        <?php } ?>

                    </td>
                    <td>Credit Check Total Password</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="creditChkPwd" id="creditChkPwd"
                                   value="<?php echo htmlentities(Strings::showField('creditChkPwd', 'FUInfo')); ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('creditChkPwd', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                </tr>
                <tr class="even">
                    <td>Experian Score</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="experianScore" id="experianScore"
                                   value="<?php echo Strings::showField('experianScore', 'FUInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('experianScore', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                    <td>Equifax Score</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="equifaxScore" id="equifaxScore"
                                   value="<?php echo Strings::showField('equifaxScore', 'FUInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('equifaxScore', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                </tr>
                <tr>
                    <td>Transunion Score</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="transunionScore" id="transunionScore"
                                   value="<?php echo Strings::showField('transunionScore', 'FUInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('transunionScore', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                    <td>Monthly Income before Taxes?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="monthlyIncomeBFTax" id="monthlyIncomeBFTax"
                                   value="<?php echo Strings::showField('monthlyIncomeBFTax', 'FUInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('monthlyIncomeBFTax', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                </tr>
                <tr class="even">
                    <td>Monthly Income after Taxes?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="monthlyIncomeAFTax" id="monthlyIncomeAFTax"
                                   value="<?php echo Strings::showField('monthlyIncomeAFTax', 'FUInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('monthlyIncomeAFTax', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                    <td width="200">Are you employed?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="haveEmployed" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $haveEmployed); ?>
                                   onclick="showFundingSubDiv(this.value, 'Employed');">Yes
                            <input type="radio" name="haveEmployed" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $haveEmployed); ?>
                                   onclick="showFundingSubDiv(this.value, 'Employed');">No
                        <?php } else { ?>
                            <h5><?php echo $haveEmployed ?></h5>
                        <?php } ?>
                    </td>
                </tr>
                <tr>
                    <td>Can you prove income with paystub, W2, and income<br>tax return?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="havePaystubW2ITReturn" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $havePaystubW2ITReturn); ?>>Yes
                            <input type="radio" name="havePaystubW2ITReturn" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $havePaystubW2ITReturn); ?>>No
                        <?php } else { ?>
                            <h5><?php echo $havePaystubW2ITReturn ?></h5>
                        <?php } ?>
                    </td>
                    <td class="showOnEmployedFUDiv" style="<?php echo $FUSectionSubEmpDivDispOpt ?>">
                        Number of Months at Current Job?
                    </td>
                    <td class="showOnEmployedFUDiv" style="<?php echo $FUSectionSubEmpDivDispOpt ?>">
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="noOfMonthsAtCurrentJob" id="noOfMonthsAtCurrentJob"
                                   value="<?php echo Strings::showField('noOfMonthsAtCurrentJob', 'FUInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>" style="width:150px"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('noOfMonthsAtCurrentJob', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                </tr>
                <tr class="even">
                    <td>Have you or any family member served in the US Military?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="isFMInUSMilitary" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $isFMInUSMilitary); ?>>Yes
                            <input type="radio" name="isFMInUSMilitary" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $isFMInUSMilitary); ?>>No
                        <?php } else { ?>
                            <h5><?php echo $isFMInUSMilitary ?></h5>
                        <?php } ?>
                    </td>
                    <td>Do you own a business ?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="ownBusiness" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $ownBusiness); ?>
                                   onclick="showFundingSubDiv(this.value, 'Business');">Yes
                            <input type="radio" name="ownBusiness" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $ownBusiness); ?>
                                   onclick="showFundingSubDiv(this.value, 'Business');">No
                        <?php } else { ?>
                            <h5><?php echo $ownBusiness ?></h5>
                        <?php } ?>
                    </td>
                </tr>
                <tr class="showOnBusinessFUDiv even" style="<?php echo $FUSectionSubOwnBusinessDivDispOpt ?>">
                    <td>Type of Business</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="typeOfBusiness" id="typeOfBusiness"
                                   value="<?php echo htmlentities(Strings::showField('typeOfBusiness', 'FUInfo')); ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>" maxlength="45" style="width:210px"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('typeOfBusiness', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                    <td>When did you incorporate?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="date" name="businessStartDate" id="businessStartDate"
                                   value="<?php echo Strings::showField('businessStartDate', 'FUInfo') ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('businessStartDate', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                </tr>
                <tr>
                    <td>Do you own or rent?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="propType" value="Yes"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $FUPropType); ?>>Own
                            <input type="radio" name="propType" value="No"
                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $FUPropType); ?>>Rent
                        <?php } else { ?>
                            <h5><?php echo $FUPropType ?></h5>
                        <?php } ?>
                    </td>
                    <td>Credit Issues</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <select data-placeholder="" name="creditIssues[]" id="creditIssues" class="chzn-select odd"
                                    multiple="" style="width:245px;" tabindex="<?php echo $tabIndexNo++; ?>">
                                <?php
                                for ($s = 0; $s < count($glCreditIssues); $s++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelectedArray($creditIssuesArray, $glCreditIssues[$s]);
                                    echo "<option value=\"" . $glCreditIssues[$s] . "\" " . $sOpt . '>' . $glCreditIssues[$s] . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo implode(', ', $creditIssuesArray); ?></h5>
                        <?php } ?>

                    </td>
                </tr>
                <tr class="even">
                    <td class="valign-top">Amount Desired?</td>
                    <td class="valign-top">
                        <?php if ($allowToEdit) { ?>
                            <select name="amtDesired" id="amtDesired" tabindex="<?php echo $tabIndexNo++; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                $glAmountDesiredKeys = [];
                                $glAmountDesiredKeys = array_keys($glAmountDesired);
                                for ($i = 0; $i < count($glAmountDesiredKeys); $i++) {
                                    $amountDesired = $sOpt = '';
                                    $amountDesired = trim($glAmountDesiredKeys[$i]);
                                    $sOpt = Arrays::isSelected($amtDesired, $amountDesired);

                                    ?>
                                    <option value="<?php echo $amountDesired ?>" <?php echo $sOpt ?>><?php echo $glAmountDesired[$amountDesired] ?></option>
                                    <?php
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo $amtDesired ?></h5>
                        <?php } ?>
                    </td>
                    <td class="valign-top">Reason for Funds</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <select data-placeholder="" name="fundsNeededReason[]" id="fundsNeededReason"
                                    class="chzn-select odd" multiple="" style="width:245px;"
                                    tabindex="<?php echo $tabIndexNo++; ?>"
                                    onchange="showFundingNotesDiv('fundsNeededReason', 'MortgageNotes');">
                                <?php
                                for ($s = 0; $s < count($glFundsNeeded); $s++) {
                                    $sOpt = '';
                                    $sOpt = Arrays::isSelectedArray($fundsNeededReasonArray, $glFundsNeeded[$s]);
                                    echo "<option value=\"" . $glFundsNeeded[$s] . "\" " . $sOpt . '>' . $glFundsNeeded[$s] . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else { ?>
                            <h5><?php echo implode(', ', $fundsNeededReasonArray); ?></h5>
                        <?php } ?>
                    </td>
                </tr>
                <tr class="showOnMortgageNotesFUDiv" style="<?php echo $FUSectionSubNotesDivDispOpt ?>">
                    <td colspan="4">Comments<br>
                        <?php if ($allowToEdit) { ?>
                            <textarea rows="4" cols="110" name="FUMortgageNotes" tabindex="<?php echo $tabIndexNo++; ?>"
                                      id="FUMortgageNotes"><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></textarea>
                        <?php } else { ?>
                            <div style="text-align: left;width:900px;">
                                <h5><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></h5></div>
                        <?php } ?>

                    </td>
                </tr>
                <tr>
                    <td>Who Referred You?</td>
                    <td>
                        <?php if ($allowToEdit) { ?>
                            <input type="text" name="referredBy" id="referredBy"
                                   value="<?php echo htmlentities(Strings::showField('referredBy', 'FUInfo')); ?>"
                                   tabindex="<?php echo $tabIndexNo++; ?>"/>
                        <?php } else { ?>
                            <h5><?php echo Strings::showField('referredBy', 'FUInfo') ?></h5>
                        <?php } ?>
                    </td>
                    <td></td>
                    <td></td>
                </tr>

            </table>
        </div>
    </div>
</div>
<div class="clear"></div>

<table style="width:100%"> <!-- save section -->
    <?php if ($allowToEdit) { ?>
        <tr>
            <td style="text-align: center;">
                <input type="submit" class="button" name="btnSave" value="Save" tabindex="<?php echo $tabIndexNo++; ?>"
                       onclick="if(this.disabled==false) {return true;} else {return false;}">
                <input type="submit" class="button" name="btnSave" value="Save & Next"
                       tabindex="<?php echo $tabIndexNo++; ?>"
                       onclick="if(this.disabled==false) {return true;} else {return false;}">
            </td>
        </tr>
    <?php } ?>
</table> <!-- save button -->
</div>

<script type="text/javascript">
    <!--
    function buildurl(newhref) {
        var newPropCity = document.getElementById('propertyCity').value;
        var newPropState = document.getElementById('propertyState').value;
        var newPropZip = document.getElementById('propertyZip').value;
        var newaddress = document.getElementById('propertyAddress').value;
        newstring = 'http://www.zillow.com/search/RealEstateSearch.htm?citystatezip=' + newaddress + ' ' + newPropCity + ' ' + newPropState + ' ' + newPropZip;
        newhref.href = newstring;
    }

    $(function () {
        $('#borrowerDOB').datepicker({
            autoclose: false,changeMonth: true,
            changeYear: true,
            dateFormat: 'mm/dd/yy',
            defaultDate: '01/01/1923',
            startDate: '01/01/1900',
        });
        $('#nonBorrowerDOB').datepicker({
            autoclose: false,changeMonth: true,
            changeYear: true,
            dateFormat: 'mm/dd/yy',
            defaultDate: '01/01/1923',
            startDate: '01/01/1900',
        });
        $('#coBorrowerDOB').datepicker({
            autoclose: false,changeMonth: true, changeYear: true, dateFormat: 'mm/dd/yy',
            defaultDate: '01/01/1923',
            startDate: '01/01/1900',
        });
        $('#lien1LPMade').datepicker({autoclose: false,changeMonth: true, changeYear: true, dateFormat: 'mm/dd/yy',startDate: '01/01/1900',});
        $('#lien2LPMade').datepicker({autoclose: false,changeMonth: true, changeYear: true, dateFormat: 'mm/dd/yy',startDate: '01/01/1900',});
        $('#marriageDate').datepicker({
            autoclose: false,changeMonth: true,
            changeYear: true,
            dateFormat: 'mm/dd/yy',
            startDate: '01/01/1900',
        });
        $('#divorceDate').datepicker({
            autoclose: false,changeMonth: true,
            changeYear: true,
            dateFormat: 'mm/dd/yy',
            startDate: '01/01/1900',
        });
        $('#loanOriginationDate').datepicker({
            autoclose: false,changeMonth: true,
            changeYear: true,
            dateFormat: 'mm/dd/yy',
            startDate: '01/01/1900',
        });
        $('#noticeAccelerationDate').datepicker({
            autoclose: false,changeMonth: true,
            changeYear: true,
            dateFormat: 'mm/dd/yy',
            startDate: '01/01/1900',
        });
        $('#guarantorDOB').datepicker({
            autoclose: false,changeMonth: true,
            changeYear: true,
            dateFormat: 'mm/dd/yy',
            startDate: '01/01/1900',
        });

    });
    //-->
</script>
<script>
    $(function () {
        $('#iyes').click(function () {
            $('#row-yes').show();
        });
        $('#ino').click(function () {
            $('#row-yes').hide();
        });
    });
</script>
<!-- clientInfoFormNew.php -->
