<?php

use models\composite\oClient\saveClientAssetsInfo;
use models\composite\oClient\saveClientLOCheckingSavingInfo;
use models\composite\oClient\saveClientLORealEstateInfo;
use models\cypher;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';

UserAccess::checkReferrerPgs(['url' => 'clientCreate.php']);
global $userGroup;
//$PCID = 0;
$cnt = 0;
$CID = Request::isset('CID') ? cypher::myDecryption(Request::GetClean('CID')) : 0;
$submitOpt = Request::isset('butSubmit') ? Request::GetClean('butSubmit') : '';
$CLOSRID = Request::isset('encCLOSRID') ? cypher::myDecryption(trim(Request::GetClean('encCLOSRID'))) : 0;
$redirectionOPT = Request::isset('but_search') ? Request::GetClean('but_search') : '';
$executiveId = Request::isset('executiveId') ? cypher::myDecryption(trim(Request::GetClean('executiveId'))) : 0;
$userRole = Request::isset('userRole') ? cypher::myDecryption(trim($_POST['userRole'])) : '';
$schedulePropAddr = Request::isset('schedulePropAddr') ? Request::GetClean('schedulePropAddr') : '';
$schedulePropCity = Request::isset('schedulePropCity') ? Request::GetClean('schedulePropCity') : '';
$schedulePropState = Request::isset('schedulePropState') ? Request::GetClean('schedulePropState') : '';
$schedulePropZip = Request::isset('schedulePropZip') ? Request::GetClean('schedulePropZip') : '';
$scheduleStatus = Request::isset('scheduleStatus') ?  Request::GetClean('scheduleStatus') : '';
$propType = Request::isset('propType') ? Request::GetClean('propType') : '';
$propertyDesc = Request::isset('propertyDesc') ?  Request::GetClean('propertyDesc') : '';
$presentMarketValue = Request::isset('presentMarketValue') ? Strings::replaceCommaValues(Request::GetClean('presentMarketValue')) : 0;
$amountOfMortgages = Request::isset('amountOfMortgages') ? Strings::replaceCommaValues(Request::GetClean('amountOfMortgages')) : 0;
$grossRentalIncome = Request::isset('grossRentalIncome') ? Strings::replaceCommaValues(Request::GetClean('grossRentalIncome')) : 0;
$mortgagePayments = Request::isset('mortgagePayments') ? Strings::replaceCommaValues(Request::GetClean('mortgagePayments')) : 0;
$insMaintTaxMisc = Request::isset('insMaintTaxMisc') ? Strings::replaceCommaValues(Request::GetClean('insMaintTaxMisc')) : 0;
$netRentalIncome = Request::isset('netRentalIncome') ? Strings::replaceCommaValues(Request::GetClean('netRentalIncome')) : 0;
$tabOpt = Request::isset('tabOpt') ? Request::GetClean('tabOpt') : 6;

$titledUnder = $_REQUEST['titledUnder'] ?? '';
//echo $_REQUEST["datePurchased"];
$datePurchased = $_REQUEST['datePurchased'] ?? '';
if (Dates::IsEmpty($datePurchased)) {
    $datePurchased = '0000-00-00';
} else {
    $datePurchased = trim(Dates::formatDateWithRE($datePurchased, 'MDY', 'Y-m-d'));
}


$maturityDateAnother = $_REQUEST['maturityDateAnother'] ?? '';
if (Dates::IsEmpty($maturityDateAnother)) {
    $maturityDateAnother = '0000-00-00';
} else {
    $maturityDateAnother = trim(Dates::formatDateWithRE($maturityDateAnother, 'MDY', 'Y-m-d'));
}

$maturityDateSchedule = $_REQUEST['maturityDateSchedule'] ?? '';
if (Dates::IsEmpty($maturityDateSchedule)) {
    $maturityDateSchedule = '0000-00-00';
} else {
    $maturityDateSchedule = trim(Dates::formatDateWithRE($maturityDateSchedule, 'MDY', 'Y-m-d'));
}

$salesDate = $_REQUEST['salesDate'] ?? '';
if (Dates::IsEmpty($salesDate)) {
    $salesDate = '0000-00-00';
} else {
    $salesDate = trim(Dates::formatDateWithRE($salesDate, 'MDY', 'Y-m-d'));
}


$purchasePrice = Request::isset('purchasePrice') ? Strings::replaceCommaValues(Request::GetClean('purchasePrice')) : 0;
$valueofImprovementsMade = Request::isset('valueofImprovementsMade') ? Strings::replaceCommaValues(Request::GetClean('valueofImprovementsMade')) : 0;
$intendedOccupancy = Request::GetClean('intendedOccupancy') ?? '';
$anyMortgagesLiens = Request::GetClean('anyMortgagesLiens') ?? '';
$creditorName = Request::GetClean('creditorName') ?? '';
$accountNumber = Request::GetClean('accountNumber') ?? '';
$loanStatus = Request::GetClean('loanStatus') ?? '';
$unpaidBalance = Request::isset('unpaidBalance') ? Strings::replaceCommaValues(Request::GetClean('unpaidBalance')) : 0;
$monthlyPayment = Request::isset('monthlyPayment') ? Strings::replaceCommaValues(Request::GetClean('monthlyPayment')) : 0;
$mortgageType = Request::GetClean('type') ?? '';
$creditLimit = Request::isset('creditLimit') ? Strings::replaceCommaValues(Request::GetClean('creditLimit')) : 0;
$anyOtherMortgagesLiens = Request::GetClean('anyOtherMortgagesLiens') ?? '';
$creditorNameAnother = Request::GetClean('creditorNameAnother') ?? '';
$accountNumberAnother = Request::GetClean('accountNumberAnother') ?? '';
$loanStatusAnother = Request::GetClean('loanStatusAnother') ?? '';
$unpaidBalanceAnother = Request::isset('unpaidBalanceAnother') ? Strings::replaceCommaValues(Request::GetClean('unpaidBalanceAnother')) : 0;
$monthlyPaymentAnother = Request::isset('monthlyPaymentAnother') ? Strings::replaceCommaValues(Request::GetClean('monthlyPaymentAnother')) : 0;
$typeAnother = Request::GetClean('typeAnother') ?? '';
$creditLimitAnother = Request::isset('creditLimitAnother') ? Strings::replaceCommaValues(Request::GetClean('creditLimitAnother')) : 0;
$ownership = Request::isset('ownership') ? Strings::replaceCommaValues(Request::GetClean('ownership')) : 0;
$salesPrice = Request::isset('salesPrice') ? Strings::replaceCommaValues(Request::GetClean('salesPrice')) : 0;
$scheduleInvestType = Request::isset('scheduleInvestType') ? Request::GetClean('scheduleInvestType') : '';

$schedulePropUnit = Request::isset('schedulePropUnit') ? Request::GetClean('schedulePropUnit') : '';
$schedulePropCountry = Request::isset('schedulePropCountry') ? Request::GetClean('schedulePropCountry') : '';
$scheduleInterestRate = Request::isset('scheduleInterestRate') ? Request::GetClean('scheduleInterestRate') : '';
$scheduleInterestRateAnother = Request::isset('scheduleInterestRateAnother') ? Request::GetClean('scheduleInterestRateAnother') : '';
$paidAtOrBeforeClose = Request::isset('paidAtOrBeforeClose') ? Request::GetClean('paidAtOrBeforeClose') : 0;
$paidAtOrBeforeCloseAnother = Request::isset('paidAtOrBeforeCloseAnother') ? Request::GetClean('paidAtOrBeforeCloseAnother') : 0;

$ipArray = [
    'CID' => $CID,
    'schedulePropAddr' => $schedulePropAddr,
    'schedulePropCity' => $schedulePropCity,
    'schedulePropState' => $schedulePropState,
    'schedulePropZip' => $schedulePropZip,
    'scheduleStatus' => $scheduleStatus,
    'propType' => $propType,
    'propertyDesc' => $propertyDesc,
    'presentMarketValue' => $presentMarketValue,
    'amountOfMortgages' => $amountOfMortgages,
    'grossRentalIncome' => $grossRentalIncome,
    'mortgagePayments' => $mortgagePayments,
    'insMaintTaxMisc' => $insMaintTaxMisc,
    'netRentalIncome' => $netRentalIncome,
    'titledUnder' => $titledUnder,
    'datePurchased' => $datePurchased,
    'purchasePrice' => $purchasePrice,
    'valueofImprovementsMade' => $valueofImprovementsMade,
    'intendedOccupancy' => $intendedOccupancy,
    'anyMortgagesLiens' => $anyMortgagesLiens,
    'creditorName' => $creditorName,
    'accountNumber' => $accountNumber,
    'loanStatus' => $loanStatus,
    'unpaidBalance' => $unpaidBalance,
    'monthlyPayment' => $monthlyPayment,
    'mortgageType' => $mortgageType,
    'creditLimit' => $creditLimit,
    'anyOtherMortgagesLiens' => $anyOtherMortgagesLiens,
    'creditorNameAnother' => $creditorNameAnother,
    'accountNumberAnother' => $accountNumberAnother,
    'loanStatusAnother' => $loanStatusAnother,
    'unpaidBalanceAnother' => $unpaidBalanceAnother,
    'monthlyPaymentAnother' => $monthlyPaymentAnother,
    'typeAnother' => $typeAnother,
    'creditLimitAnother' => $creditLimitAnother,
    'CLOSRID' => $CLOSRID,
    'maturityDateSchedule' => $maturityDateSchedule,
    'maturityDateAnother' => $maturityDateAnother,
    'ownership' => $ownership,
    'salesDate' => $salesDate,
    'salesPrice' => $salesPrice,
    'scheduleInvestType' => $scheduleInvestType,
    'schedulePropUnit' => $schedulePropUnit,
    'schedulePropCountry' => $schedulePropCountry,
    'scheduleInterestRate' => $scheduleInterestRate,
    'scheduleInterestRateAnother' => $scheduleInterestRateAnother,
    'paidAtOrBeforeClose' => $paidAtOrBeforeClose,
    'paidAtOrBeforeCloseAnother' => $paidAtOrBeforeCloseAnother
];
/**
 * Description        : PC under Client Real Estate Information Save
 * Date                : May 28, 2017,
 * Pivotal Task Number : #156318354
 **/
if ($CID > 0) {
    $ip = [
        'p' => $_POST,
        'CID' => $CID,
    ];
    $cnt = saveClientAssetsInfo::getReport($ip);
    //avoid creating empty records while saving form borrower profile assets tab
    if (Request::isset('encCLOSRID')) {
        $cnt = saveClientLORealEstateInfo::getReport($ipArray);
    }

    $cnt = saveClientLOCheckingSavingInfo::getReport($ip);
}


if ($cnt > 0) {
    Strings::SetSess('msg', 'Updated Successfully');
} else {
    Strings::SetSess('msg', 'Error while save.');
}

if ($userRole == 'Branch') $reDirectUrl = CONST_URL_BRSSL;
else if ($userRole == 'Agent') $reDirectUrl = CONST_URL_AG_SSL;
else if (($userRole == 'Client') && (preg_match('/client_new\//i', $_SERVER['HTTP_REFERER']))) $reDirectUrl = CONST_URL_CL_NEW_SSL;
else if ($userRole == 'Client') $reDirectUrl = CONST_URL_CL_SSL;
else    $reDirectUrl = CONST_URL_BOSSL;

$reDirectUrl .= 'clientCreate.php?cId=' . cypher::myEncryption($CID) . '&encEId=' . cypher::myEncryption($executiveId);

if ($tabOpt > 0) {
    if ($redirectionOPT == 'Save & Next') {
        header('Location: ' . $reDirectUrl . '&tabNumb=7');
        exit();
    } elseif ($redirectionOPT == 'Save') {
        header('Location: ' . $reDirectUrl . '&tabNumb=6');
        exit();
    }
} else {
    header('Location: ' . $reDirectUrl);
    exit();
}
