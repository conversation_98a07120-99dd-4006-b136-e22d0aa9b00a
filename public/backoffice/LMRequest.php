<?php

use models\constants\docStatusArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\Database2;
use models\lendingwise\tblFile;
use models\lendingwise_log\ChangeLog;
use models\PageVariables;
use models\Request;
use models\standard\BaseHTML;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;

ob_start();
global $LMRId, $allowToCreateFiles, $allowClientToCreateHMLOFile,
       $PCID, $isHMLO, $activeTab, $titleBorName, $moduleName,
       $eOpt, $titleBorName, $moduleName, $userName, $userGroup;
session_start();

if (($_REQUEST['lId'] ?? '') === '0000000000000000') {
    header('location: ' . $_SERVER['URL']);
    exit;
}


require '../includes/util.php';

if(($_REQUEST['tabOpt'] ?? null) == 'SER2') {
    HTTP::Redirect('/backoffice/loan/servicing?lId=' . Request::GetClean('lId'));
}

if(($_REQUEST['tabOpt'] ?? null) == 'FCI') {
    HTTP::Redirect('/backoffice/loan/fci?lId=' . Request::GetClean('lId')
        . '&eId=' . Request::GetClean('eId')
        . '&rId=' . Request::GetClean('rId')
        . '&tabOpt=SER2&op=' . Request::GetClean('op')
    );
}


require '../includes/router.php';
//require '../includes/imageUtil.php';
require CONST_BO_PATH . 'initPageVariables.php';
require CONST_BO_PATH . 'getPageVariables.php';

UserAccess::CheckAdminUse();

require __DIR__ . '/fileCommon.php';

if ($LMRId == 0) {
    if (!$allowToCreateFiles && $allowClientToCreateHMLOFile != 1) {
        header('Location: ' . CONST_SITE_URL . 'unauthorizedPage.php');
        /*** Re-direct the user to unauthorized page ***/
        exit();
    }
}

LMRequest::$hasChangeLog = $LMRId ? ChangeLog::hasChangeLog(tblFile::class, $LMRId) : 0;
LMRequest::setLMRId($LMRId);

docStatusArray::reset();
$temp = PageVariables::$PCID;
PageVariables::$PCID = LMRequest::File()->FPCID;
$res = docStatusArray::getStatuses();
PageVariables::$PCID = $temp;



if ($isHMLO == 1 && $activeTab != 'TA') {
    echo BaseHTML::openPage($titleBorName . $moduleName);
} elseif ($eOpt == 0 && $LMRId == 0 && $activeTab == 'QAPP') {
    echo BaseHTML::openPage('Quick App');
} elseif ($eOpt == 0 && $LMRId == 0 && $activeTab == 'LI') {
    echo BaseHTML::openPage('Full App');
} else {
    echo BaseHTML::openPage($titleBorName . $moduleName);
}
$customCSSArray = [
    '/assets/js/3rdParty/datatables-1.10.22/datatables.bundle.css',
    '/assetsNew/css/scroll.css',
];
Strings::includeMyCSS($customCSSArray);

require 'LMScriptFiles.php';


$subHeaderHTML = '';
$debugSubPage = false;
$bodyVariables = 'subheader-enabled subheader-fixed';
require('../includesNew/_page-body-loader.php');
require('../includesNew/_layoutOpen.php');

require 'LMRequest/LMRequestForm.php';

require('../includesNew/_layoutClose.php');

require 'adminFooter.php';
echo BaseHTML::closePage();

$scriptArray = [
    '/assets/js/3rdParty/datatables-1.10.22/datatables.bundle.js',
    'https://code.jquery.com/jquery-migrate-3.0.0.js',
    '/backoffice/api_v2/js/address_lookup.js',
    '/assets/js/popup.js',
    '/assets/js/pops/addNotesTemplate.js',
    '/assets/js/pops/addChecklistItemControls.js',
    '/assets/js/pops/editFileChecklistItemControls.js',
];


//for next part, tabopt is not in url when default tab is FA or qa however it appears it is populated by past dev to keep functionalities relying on it working
if ($_REQUEST['tabOpt'] == 'LI' || $_REQUEST['tabOpt'] == '1003' || $_REQUEST['tabOpt'] == 'QAPP') {
    $scriptArray[] = '/assets/js/importExportFM34/configFM34.js';
    $scriptArray[] = '/assets/js/importExportFM34/funcFM34.js';
    $scriptArray[] = '/assets/js/importExportFM34/globalFM34.js';
    $scriptArray[] = '/assets/js/importExportFM34.js';
}

// websocket start of snip LMRequest.php backoffice

if (WEBSOCKET_SERVER > '' && ($_GET['lId'] ?? '')) { ?>
    <script type="text/javascript">
        let daveSocket = {
            userName: "<?php echo htmlspecialchars($userName); ?>",
            userId: "<?php echo htmlspecialchars($userGroup . PageVariables::$userNumber); ?>",
            documentId: "<?php echo htmlspecialchars(Request::GetClean('lId')) ?>",
        };
    </script>
    <script type="text/javascript" src="/assets/js/dave-socket.js?v1.2"></script>
<?php } ?>

    <!-- client portal does not have the div to show the bubbles purposely, so they cannot see employees, etc.  Ignore the console error.  We want this behavior, so
 they still load the script and bo users can see them -->
    <!-- websocket end of snip -->

<?php
Strings::includeMyScript($scriptArray);
echo BaseHTML::thirdPartyFileManager();
require('../includesNew/_customPage.php');
?>
    <script src="/assets/js/3rdParty/datatables-FixedHeader-3.2.4/dataTables.fixedHeader.js?<?php echo CONST_JS_VERSION; ?>"></script>
    <script src="/pops/js/addChecklistFlatNotesClass.js?<?php echo CONST_JS_VERSION; ?>"></script>
    <script src="/pops/js/addChecklistFlatNotes.js?<?php echo CONST_JS_VERSION; ?>"></script>

    <div id="sessMsg">
        <?php
        echo Strings::DisplayMessage(Strings::GetSess('msg'));
        Strings::SetSess('msg', '');
        ?>
    </div>
    <div id="warning"></div>
    <div id="warningLG"></div>

<?php require 'LMSuggestedDD.php'; ?>
<?php
if($activeTab !== 'CL') {
    require __DIR__ . '/LMRequest/modal/changeLog.php';
}
?>


<?php

if ($activeTab == 'LI' || $activeTab == 'QAPP' || $activeTab == 'AL' || $activeTab == '1003') { ?>
    <div class="modal fade realEstateInfoContent" id="realEstateInfoContent" data-backdrop="static" tabindex="-1"
         role="dialog"
         aria-labelledby="exampleModal1"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
            <form name="realEstateForm" id="realEstateForm" method="POST" action="#">

                <div class="modal-content" id="modal-content-id">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Schedule of Real Estate</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <i aria-hidden="true" class="ki ki-close"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <?php require 'addLORealEstateInfoPopup.php'; ?>
                    </div>
                    <div class="modal-footer">
                        <input type="button" name="butSubmit" id="butSubmit" value="Save" class="btn btn-primary">
                        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <?php require __DIR__ . '/../pops/addLOLiabilitiesInfo.php'; ?>

    <script type="text/javascript"
            src="/backoffice/LMRequest/js/addLOLiabilitiesInfo.js?<?php echo CONST_JS_VERSION; ?>"></script>
    <?php
}
require CONST_ROOT_PATH . 'assets/bootstrap-modal/contactForm.php'; // Contact popup form.

if (isset($_REQUEST['tabOpt'])) {
    if ($_REQUEST['tabOpt'] != 'MP') { ?>
        <script>
            $(window).scroll(function () {
                //if ($(window).scrollTop() > ($(document).height() - $(window).height() - 100)) {  //scrolltotop is sometimes null lets do this instead (next line)
                if (($(document).height() + 100) < $(window).height()) {
                    $('#saveBtn').removeClass('scrollSave');
                } else {
                    $('#saveBtn').addClass('scrollSave');
                }
            });
        </script>
    <?php }
} ?>
<?php
//
$html = ob_get_clean();
echo $html;
if (isset($_SESSION['Generate_PDF'])) {
    unset($_SESSION['Generate_PDF']);
    ?>
    <script>
        HTTP.Post('/api/saveLMRequestPDF.php',
            {
                'htmlCode': '<?php echo base64_encode($html); ?>',
                'activeTab': '<?php echo (LMRequest::$activeTab); ?>',
                'LMRId': '<?php echo (intval($LMRId)); ?>',
            }, function (res) {
                let pdfProgressbar = $('.pdf-progress-bar');
                if (parseInt(res.id)) {
                    pdfProgressbar.addClass('bg-success font-weight-boldest');
                    pdfProgressbar.html('Refresh to Load Document');
                   setTimeout(function(){
                       $('.PDFDocumentCard').replaceWith(res.html);
                   },1000);
                } else {
                    pdfProgressbar.addClass('bg-danger font-weight-boldest');
                    pdfProgressbar.removeClass('progress-bar-striped');
                    pdfProgressbar.html('Error in Generating PDF');
                }
                pdfProgressbar.width('100%');
            }
        );
    </script>
    <?php
}
Database2::saveLogQuery();
if(!($_SESSION['UR'] ?? '')) {
    Debug('MISSING UR', $_SESSION);
}
