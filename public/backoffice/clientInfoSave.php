<?php

global $clientId, $clientEmail, $userGroup, $allowClientToCreateHMLOFile,
       $userNumber, $userRole, $userName,
       $redirect, $allowAutomation;

use models\composite\oBranch\getBranchModulesForServices;
use models\composite\oBroker\getBrokerInfo;
use models\composite\oClient\saveOrUpdateBorrowerAlternateNes;
use models\composite\oClient\savePCClientBackgroundInfo;
use models\composite\oClient\savePCClientExperienceInfo;
use models\composite\oClient\uploadClientDocs;
use models\composite\oContacts\saveContacts;
use models\composite\oContacts\saveFileContacts;
use models\composite\oFile\getFileInfo;
use models\composite\oFile\getFileModules;
use models\composite\oFileDoc\saveFileDocument;
use models\composite\oFileEmail\LeadNotifierForPCs;
use models\composite\oFileEmail\sendEmailOnHMLOFileCreation;
use models\composite\oFileEmail\sendHMLORequestEmail;
use models\composite\oFileEmail\sendSVLoanModificationRequestEmail;
use models\composite\oFileUpdate\assignUserToFile;
use models\composite\oFileUpdate\postDataToEndPoint;
use models\composite\oFileUpdate\saveClientInfo;
use models\composite\oFileUpdate\saveEquipmentInformation;
use models\composite\oFileUpdate\saveFeeSchedule;
use models\composite\oFileUpdate\saveFileNotes;
use models\composite\oFileUpdate\saveFileRentRoll;
use models\composite\oFileUpdate\saveFileSubstatusChange;
use models\composite\oFileUpdate\saveSaleMethodInfo;
use models\composite\oFileUpdate\updateQAInfo;
use models\composite\oFileUpdate2\saveBllingFee;
use models\composite\oFileUpdate2\updateBranchLeadSourceToLMR;
use models\composite\oFunding\saveFunding;
use models\composite\oHMLOInfo\saveAdditionalGuarantorsInfo;
use models\composite\oHMLOInfo\saveHMLOBorrowerInfo;
use models\composite\oHMLOInfo\saveHMLOFileResponseInfo;
use models\composite\oHMLOInfo\saveHMLONewLoanInfo;
use models\composite\oHMLOInfo\saveHMLOQAInfo;
use models\composite\oHMLOInfo\updateClientProfile;
use models\composite\oLMProposal\saveLMProposalInfo;
use models\composite\oLoanOrigination\saveFileLoanOrigination;
use models\composite\oLoanOrigination\saveFileLOQAInfo;
use models\composite\oPackage\getLibPackage;
use models\constants\BCForm;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glMimeTypes;
use models\constants\gl\glNortheastLendingLeadNotifyPCs;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\fileAdminInfo;
use models\Controllers\LMRequest\HUD as HUDController;
use models\Controllers\LMRequest\HUDCalculation;
use models\Controllers\LMRequest\Property;
use models\CustomField;
use models\cypher;
use models\Database2;
use models\FileStorage;
use models\lendingwise\tblAutomatedRuleRequestV2;
use models\lendingwise\tblFile;
use models\lendingwise_log\ChangeLog;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Strings;
use models\standard\UserAccess;
use models\UploadServer;

session_start();
require '../includes/util.php';
require 'initPageVariables.php';
require 'getPageVariables.php';

UserAccess::checkReferrerPgs(['url' => 'LMRequest.php, loanModificationPrequalRemote.php, loanModificationFile.php']);

// guarantorNotes

$glNortheastLendingLeadNotifyPCs = glNortheastLendingLeadNotifyPCs::$glNortheastLendingLeadNotifyPCs;

$publicUser = 0;
$DIYUser = 0;
$PID = 0;
$OSID = 0;

if (isset($_POST['publicUser'])) $publicUser = trim($_POST['publicUser']);
if (isset($_POST['DIYUser'])) $DIYUser = trim($_POST['DIYUser']);


if ($publicUser == 1 || $DIYUser == 1) {
    doNothing();
} else {
    UserAccess::CheckAdminUse();
}

//Allow automated rules repeat code here
//This is only for use confirmation if required
$allowRepeat = Request::GetClean('allowRepeat') ?? '';
$triggerRule = Request::GetClean('triggerRule') ?? 'No';
$LMRId = $_POST['LMRID'] ?? 0;
if ($allowAutomation == 1 && $LMRId > 0 && $allowRepeat == 'check') {
    include('automatedRulesActionController.php');
    exit();
}

if ($DIYUser == 1) {
} else {
    require 'checkValidEmailAddress.php';
    /** Check borrower email is valid else return stop the save process and return **/
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    ChangeLog::LogChanges(
        tblFile::class,
        $LMRId,
        basename(__FILE__, '.php'),
        $_REQUEST,
        PageVariables::$userNumber
    );
}

$glMimeTypes = glMimeTypes::$glMimeTypes;

$executiveId = 0;
$brokerNumber = 0;
$rsCnt = 0;
$LMRId = 0;
$responseId = 0;
$existingLMRId = 0;
$goToTab = '';
$activeTab = '';
$btnValue = '';
$clientId = 0;
$resultArray = [];
$branchLeadSource = '';
$newLMRId = 0;
$SID = 0;
$isSysNotesPrivate = 0;
$selectedModuleServices = $selectedModuleServiceKeys = [];
$op = $leadSource = '';
$existingBranchSelectedServices = $existingBranchSelectedModules = [];
$noOfPeopleDependent = 0;
$existingBRID = 0;
$existingAID = 0;
$existingSAID = 0;

$propMgmntContactID = 0;
$propMgmntContactPerson = '';
$propMgmntCompany = '';
$propMgmntContactEmail = '';
$propMgmntAddress = '';
$propMgmntCity = '';
$propMgmntState = '';
$propMgmntZip = '';
$propMgmntPhNo1 = '';
$propMgmntPhNo2 = '';
$propMgmntPhNo3 = '';
$propMgmntPhExt = '';
$propMgmntPhone = '';
$propMgmntNotes = '';
$CFPBFileUser = 0;
$PublishBInfo = '';
$BEthnicity = '';
$BRace = '';
$BGender = '';
$PublishCBInfo = '';
$CBEthnicity = '';
$CBRace = '';
$CBGender = '';
$QAId = '';

if (isset($_POST['encryptedEId'])) $existingBRID = trim(cypher::myDecryption(trim($_POST['encryptedEId'])));
if (isset($_POST['encryptedBId'])) $existingAID = trim(cypher::myDecryption(trim($_POST['encryptedBId'])));
if (isset($_POST['encryptedSBId'])) $existingSAID = trim(cypher::myDecryption(trim($_POST['encryptedSBId'])));

if ($userGroup == 'Super' || $userGroup == 'Employee' || $userGroup == 'Agent' || $DIYUser == 1) {
    if (isset($_POST['branchId'])) $executiveId = trim(cypher::myDecryption(trim($_POST['branchId'])));
} else {
    if (isset($_POST['encryptedEId'])) $executiveId = trim(cypher::myDecryption(trim($_POST['encryptedEId'])));
}

if ($userGroup == 'Super' || $userGroup == 'Employee' || $userGroup == 'Branch' || $userGroup == 'Agent' || $publicUser == 1) {
    if (isset($_POST['agentId'])) $brokerNumber = trim(cypher::myDecryption(trim($_POST['agentId'])));
    if (isset($_POST['secondaryAgentId'])) $secondaryBrokerNumber = trim(cypher::myDecryption(trim($_POST['secondaryAgentId'])));
} else {
    if (isset($_POST['encryptedBId'])) $brokerNumber = trim(cypher::myDecryption(trim($_POST['encryptedBId'])));
}
if (isset($_POST['CFPBFileUser'])) $CFPBFileUser = trim($_POST['CFPBFileUser']);

if ($userGroup == 'Super' || $userGroup == 'Auditor' || $userGroup == 'Client' || $CFPBFileUser == 1) {
    if (isset($_POST['selectedPC'])) $PCID = $_POST['selectedPC'];
}
if ($publicUser == 1) {
    if (isset($_POST['encryptedPCID'])) $PCID = trim(cypher::myDecryption($_POST['encryptedPCID']));
}
$LMRClientType = [];
$fileModule = [];
if (isset ($_REQUEST['LMRClientType'])) $LMRClientType = $_REQUEST['LMRClientType'];
if (isset ($_REQUEST['isSysNotesPrivate'])) $isSysNotesPrivate = trim($_REQUEST['isSysNotesPrivate']);

if (isset ($_REQUEST['fileModule'])) $fileModule = $_REQUEST['fileModule'];

if ($userGroup == 'Client' && $allowClientToCreateHMLOFile == 1 && $LMRId == 0) {
    if (isset($_POST['agentId'])) $brokerNumber = trim(cypher::myDecryption(trim($_POST['agentId'])));
    if (isset($_POST['secondaryAgentId'])) $secondaryBrokerNumber = trim(cypher::myDecryption(trim($_POST['secondaryAgentId'])));
    if (isset($_POST['branchId'])) $executiveId = trim(cypher::myDecryption(trim($_POST['branchId'])));
}
if (isset($_POST['leadSource'])) $leadSource = trim($_POST['leadSource']);
if (isset($_POST['encryptedLId'])) $existingLMRId = trim(cypher::myDecryption(trim($_POST['encryptedLId'])));
if (isset($_POST['encryptedRId'])) $responseId = trim(cypher::myDecryption(trim($_POST['encryptedRId'])));
if (isset($_POST['encryptedCId'])) $clientId = trim(cypher::myDecryption(trim($_POST['encryptedCId'])));
if (isset($_POST['goToTab'])) $goToTab = trim($_POST['goToTab']);
if (isset($_POST['activeTab'])) $activeTab = trim($_POST['activeTab']);
if (isset($_POST['btnSave'])) $btnValue = trim($_POST['btnSave']);
if (isset($_POST['branchLeadSource'])) $branchLeadSource = trim($_POST['branchLeadSource']);
if (isset($_POST['SID'])) $SID = trim($_POST['SID']);
if (isset($_POST['OSID'])) $OSID = trim($_POST['OSID']);

if (isset($_POST['propMgmntContactID'])) {
    $propMgmntContactID = trim($_POST['propMgmntContactID']);
}
if (isset($_POST['propMgmntContactPerson'])) {
    $propMgmntContactPerson = trim($_POST['propMgmntContactPerson']);
}
if (isset($_POST['propMgmntCompany'])) {
    $propMgmntCompany = trim($_POST['propMgmntCompany']);
}
if (isset($_POST['propMgmntContactEmail'])) {
    $propMgmntContactEmail = trim($_POST['propMgmntContactEmail']);
}
if (isset($_POST['propMgmntAddress'])) {
    $propMgmntAddress = trim($_POST['propMgmntAddress']);
}
if (isset($_POST['propMgmntCity'])) {
    $propMgmntCity = trim($_POST['propMgmntCity']);
}
if (isset($_POST['propMgmntState'])) {
    $propMgmntState = trim($_POST['propMgmntState']);
}
if (isset($_POST['propMgmntZip'])) {
    $propMgmntZip = trim($_POST['propMgmntZip']);
}
if (isset($_POST['propMgmntPhNo1'])) {
    $propMgmntPhNo1 = trim($_POST['propMgmntPhNo1']);
}
if (isset($_POST['propMgmntPhNo2'])) {
    $propMgmntPhNo2 = trim($_POST['propMgmntPhNo2']);
}
if (isset($_POST['propMgmntPhNo3'])) {
    $propMgmntPhNo3 = trim($_POST['propMgmntPhNo3']);
}
if (isset($_POST['propMgmntPhExt'])) {
    $propMgmntPhExt = trim($_POST['propMgmntPhExt']);
}
$propMgmntPhone = $propMgmntPhNo1 . $propMgmntPhNo2 . $propMgmntPhNo3 . $propMgmntPhExt;
if (isset($_POST['propMgmntNotes'])) {
    $propMgmntNotes = trim($_POST['propMgmntNotes']);
}
$HOA1ContactID = 0;
$HOA2ContactID = 0;
$HOA2ContactName = '';
$HOA2CompanyName = '';
$condominiumOrHOAFeeAmtReceiver = '';
$feeAmtReceiverAddress = '';
$feeAmtReceiverCity = '';
$feeAmtReceiverState = '';
$feeAmtReceiverZip = '';
$HOPhone = '';
$HOFax = '';
$HOPhNo1 = '';
$HOPhNo2 = '';
$HOPhNo3 = '';
$HOPhExt = '';
$HOFaxNo1 = '';
$HOFaxNo2 = '';
$HOFaxNo3 = '';
$HOEmail = '';
$HOContactName = '';
$HOA2ContactID = 0;
$HOAOrCOAFeeAddress = '';
$HOAOrCOAFeeCity = '';
$HOAOrCOAFeeState = '';
$HOAOrCOAFeeZip = '';

$condominiumOrHOAFee = '';
$isHOAOrCOAFee = 0;
$HOAFeePaidCurrent = '';
$condominiumOrHOAFeeAmt = '';
$HOAOrCOAFeeAmt = '';
$IsCommunity = '';
$HOAAllowRentals = '';
$HO1Notes = '';
$HO2Notes = '';
$isLOOpt = $substatusID = 0;

if (isset($_POST['HOA1ContactID'])) {
    $HOA1ContactID = trim($_POST['HOA1ContactID']);
}
if (isset($_POST['HOA2ContactID'])) {
    $HOA2ContactID = trim($_POST['HOA2ContactID']);
}
if (isset($_POST['HOContactName'])) $HOContactName = trim($_POST['HOContactName']);
if (isset($_POST['condominiumOrHOAFeeAmtReceiver'])) $condominiumOrHOAFeeAmtReceiver = trim($_POST['condominiumOrHOAFeeAmtReceiver']);
if (isset($_POST['feeAmtReceiverAddress'])) $feeAmtReceiverAddress = trim($_POST['feeAmtReceiverAddress']);
if (isset($_POST['feeAmtReceiverCity'])) $feeAmtReceiverCity = trim($_POST['feeAmtReceiverCity']);
if (isset($_POST['feeAmtReceiverState'])) $feeAmtReceiverState = trim($_POST['feeAmtReceiverState']);
if (isset($_POST['feeAmtReceiverZip'])) $feeAmtReceiverZip = trim($_POST['feeAmtReceiverZip']);
if (isset($_POST['HOPhNo1'])) $HOPhNo1 = trim($_POST['HOPhNo1']);
if (isset($_POST['HOPhNo2'])) $HOPhNo2 = trim($_POST['HOPhNo2']);
if (isset($_POST['HOPhNo3'])) $HOPhNo3 = trim($_POST['HOPhNo3']);
if (isset($_POST['HOPhExt'])) $HOPhExt = trim($_POST['HOPhExt']);
if (isset($_POST['HOFaxNo1'])) $HOFaxNo1 = trim($_POST['HOFaxNo1']);
if (isset($_POST['HOFaxNo2'])) $HOFaxNo2 = trim($_POST['HOFaxNo2']);
if (isset($_POST['HOFaxNo3'])) $HOFaxNo3 = trim($_POST['HOFaxNo3']);
if (isset($_POST['HOEmail'])) $HOEmail = trim($_POST['HOEmail']);

$HOPhone = $HOPhNo1 . $HOPhNo2 . $HOPhNo3 . $HOPhExt;
$HOFax = $HOFaxNo1 . $HOFaxNo1 . $HOFaxNo1;
$lmrFileID = 0;
if (isset($_POST['HOA2ContactName'])) $HOA2ContactName = trim($_POST['HOA2ContactName']);
if (isset($_POST['HOA2CompanyName'])) $HOA2CompanyName = trim($_POST['HOA2CompanyName']);
if (isset($_POST['HOAOrCOAFeeAddress'])) $HOAOrCOAFeeAddress = trim($_POST['HOAOrCOAFeeAddress']);
if (isset($_POST['HOAOrCOAFeeCity'])) $HOAOrCOAFeeCity = trim($_POST['HOAOrCOAFeeCity']);
if (isset($_POST['HOAOrCOAFeeState'])) $HOAOrCOAFeeState = trim($_POST['HOAOrCOAFeeState']);
if (isset($_POST['HOAOrCOAFeeZip'])) $HOAOrCOAFeeZip = trim($_POST['HOAOrCOAFeeZip']);

if (isset($_POST['condominiumOrHOAFee'])) $condominiumOrHOAFee = trim($_POST['condominiumOrHOAFee']);
if (isset($_POST['isHOAOrCOAFee'])) $isHOAOrCOAFee = trim($_POST['isHOAOrCOAFee']);
if (isset($_POST['HOAFeePaidCurrent'])) $HOAFeePaidCurrent = trim($_POST['HOAFeePaidCurrent']);
if (isset($_POST['condominiumOrHOAFeeAmt'])) $condominiumOrHOAFeeAmt = trim($_POST['condominiumOrHOAFeeAmt']);
if (isset($_POST['HOAOrCOAFeeAmt'])) $HOAOrCOAFeeAmt = trim($_POST['HOAOrCOAFeeAmt']);
if (isset($_POST['HO1Notes'])) $HO1Notes = trim($_POST['HO1Notes']);
if (isset($_POST['HO2Notes'])) $HO2Notes = trim($_POST['HO2Notes']);
if (isset($_POST['HOAAllowRentals'])) $HOAAllowRentals = trim($_POST['HOAAllowRentals']);
if (isset($_POST['IsCommunity'])) $IsCommunity = trim($_POST['IsCommunity']);

if (isset($_POST['noOfPeopleDependent'])) $noOfPeopleDependent = trim($_POST['noOfPeopleDependent']);

if (isset($_POST['isLOOpt'])) $isLOOpt = trim($_POST['isLOOpt']);

if (isset($_POST['FUMortgageNotes'])) $FUMortgageNotes = trim($_POST['FUMortgageNotes']);

if (isset($_POST['op'])) $op = trim($_POST['op']);
if (isset($_POST['substatusID'])) $substatusID = $_POST['substatusID'];
if (isset($_POST['cellNo'])) $_REQUEST['cell'] = trim($_POST['cellNo']);
if (isset($_POST['fax'])) $_REQUEST['fax'] = trim($_POST['fax']);
if (isset($_POST['isCoBo'])) $_REQUEST['isCoBorrower'] = trim($_POST['isCoBo']);
if (isset($_POST['coBSsnNumber'])) $_REQUEST['cSsn'] = trim($_POST['coBSsnNumber']);
if (isset($_POST['coBPhoneNumber'])) $_REQUEST['cPhone1'] = trim($_POST['coBPhoneNumber']);
if (isset($_POST['LMRId'])) $lmrFileID = trim($_POST['LMRId']);
$isBorrowermailEdited = $_POST['isBorrowermailEdited'] ?? '';
$originalClientId = $_POST['originalClientId'] ?? '';

if ($op == '') $op = cypher::myEncryption('edit');

$ip = [
    'p'                => $_POST,
    'LMRId'            => $existingLMRId,
    'responseId'       => $responseId,
    'branchId'         => $executiveId,
    'agentId'          => $brokerNumber,
    'secondaryAgentId' => $secondaryBrokerNumber,
    'clientId'         => $clientId,
    'UID'              => PageVariables::$userNumber,
    'URole'            => $userRole,
    'PCID'             => $PCID,
    'SID'              => $SID,
    'OSID'             => $OSID,
    'userName'         => $userName,
    'userGroup'        => $userGroup,
    'publicUser'       => $publicUser,
    'saveTab'          => 'CI',
    'isLOOpt'          => $isLOOpt,
    'leadSource'       => $leadSource,
];

if (in_array('FU', $fileModule)) {
    $ip['FUMortgageNotes'] = $FUMortgageNotes;
}
$clientEmail = '';
$resultArray = saveClientInfo::getReport($ip);

if (count($resultArray) > 0) {
    if (array_key_exists('LMRId', $resultArray)) $LMRId = $resultArray['LMRId'];
    if (array_key_exists('upCnt', $resultArray)) $upCnt = $resultArray['upCnt'];
    if (array_key_exists('responseId', $resultArray)) $responseId = $resultArray['responseId'];
    if (array_key_exists('newLMRId', $resultArray)) $newLMRId = $resultArray['newLMRId'];
    if (array_key_exists('clientEmail', $resultArray)) $clientEmail = $resultArray['clientEmail'];

    if ($upCnt > 0) Strings::SetSess('msg', 'Updated Successfully');
    else  Strings::SetSess('msg', 'Error while update.');
    //if ($newLMRId > 0 || Arrays::getArrayValue('upEmailInClientPro', $_REQUEST) == 'No') {
    if (array_key_exists('clientId', $resultArray)) $clientId = $resultArray['clientId'];
    //}
    if ($brokerNumber > 0 || $brokerNumber) {
    } else {
        if (array_key_exists('brokerNumber', $resultArray)) $brokerNumber = $resultArray['brokerNumber'];
    }

    if(!$existingLMRId){
        $notesArray['processorNotes'] = PageVariables::$userName . ' created a New File';
        $notesArray['displayIn'] = 'NH';
        $notesArray['clientId'] = $clientId;
        $notesArray['privateNotes'] = 0;
        $notesArray['isSysNotes'] = 1;
        $notesArray['fileID'] = $LMRId;
        saveFileNotes::getReport($notesArray);
    }
}

fileAdminInfo::$LMRId = $LMRId;
fileAdminInfo::save();


Property::$LMRId = $LMRId;
Property::$userGroup = $userGroup;
Property::$userNumber = PageVariables::$userNumber;
Property::initSave();


if ($LMRId > 0) {
    saveOrUpdateBorrowerAlternateNes::getReport([
        'LMRID'           => $LMRId, 'alternateFName' => $_POST['alternateFName'],
        'alternateMName'  => $_POST['alternateMName'],
        'alternateLName'  => $_POST['alternateLName'],
        'alternateNameID' => $_POST['alternateNameID'],
    ]);
}
/**
 * Description : Get the file selected module code in user Type >> Client.
 * developer    : Venkatesh
 * Date        : Dec 19, 2017
 **/

if ($userGroup == 'Client' && $LMRId > 0 && count($fileModule) == 0) {
    $fileInfo = [];
    $fileModuleInfo = [];

    $inArray = ['fileID' => $LMRId];
    $fileInfo = getFileModules::getReport($inArray);

    if (array_key_exists($LMRId, $fileInfo)) $fileModuleInfo = $fileInfo[$LMRId];

    for ($i = 0; $i < count($fileModuleInfo); $i++) {
        $fileModule[] = trim($fileModuleInfo[$i]['moduleCode']);
    }
}

/** Merchant Funding Section Start (March 25, 2016) **/

if (in_array('MFO', $LMRClientType) && $activeTab == 'CI' && $publicUser != 1 && $LMRId > 0) {

    if (isset($_POST['majorityBusiness'])) $majorityBusiness = trim($_POST['majorityBusiness']);
    if (isset($_POST['ownersName'])) $ownersName = trim($_POST['ownersName']);
    if (isset($_POST['Owned'])) $Owned = trim($_POST['Owned']);
    if (isset($_POST['businessName'])) $businessName = trim($_POST['businessName']);
    if (isset($_POST['businessNameDBA'])) $businessNameDBA = trim($_POST['businessNameDBA']);
    if (isset($_POST['businessLegalName'])) $businessLegalName = trim($_POST['businessLegalName']);
    if (isset($_POST['businessTax'])) $businessTax = trim($_POST['businessTax']);
    if (isset($_POST['businessType'])) $businessType = trim($_POST['businessType']);
    if (isset($_POST['Website'])) $Website = trim($_POST['Website']);
    if (isset($_POST['Industry'])) $Industry = trim($_POST['Industry']);
    if (isset($_POST['businessConsumers'])) $businessConsumers = trim($_POST['businessConsumers']);
    if (isset($_POST['monthsBusiness'])) $monthsBusiness = trim($_POST['monthsBusiness']);
    if (isset($_POST['monthlyRevenue'])) $monthlyRevenue = trim($_POST['monthlyRevenue']);
    if (isset($_POST['creditScore'])) $creditScore = trim($_POST['creditScore']);

    if (isset($_POST['grossRevenue'])) $grossRevenue = trim($_POST['grossRevenue']);
    if (isset($_POST['monthlyDepositVolume'])) $monthlyDepositVolume = trim($_POST['monthlyDepositVolume']);
    if (isset($_POST['bankBalance'])) $bankBalance = trim($_POST['bankBalance']);
    if (isset($_POST['invoices'])) $invoices = trim($_POST['invoices']);
    if (isset($_POST['invoiceAmount'])) $invoiceAmount = trim($_POST['invoiceAmount']);
    if (isset($_POST['requestedAmount'])) $requestedAmount = trim($_POST['requestedAmount']);
    if (isset($_POST['requestedTermLength'])) $requestedTermLength = trim($_POST['requestedTermLength']);
    if (isset($_POST['funding'])) $funding = trim($_POST['funding']);
    if (isset($_POST['businessCreditCards'])) $businessCreditCards = trim($_POST['businessCreditCards']);
    if (isset($_POST['businessFinancing'])) $businessFinancing = trim($_POST['businessFinancing']);
    if (isset($_POST['netProfit'])) $netProfit = trim($_POST['netProfit']);
    if (isset($_POST['amount'])) $amount = trim($_POST['amount']);
    if (isset($_POST['ownCollateral'])) $ownCollateral = trim($_POST['ownCollateral']);
    if (isset($_POST['monthlyDebtPayments'])) $monthlyDebtPayments = trim($_POST['monthlyDebtPayments']);

    $MFArray = [
        'p'     => $_POST,
        'LMRId' => $LMRId,
    ];

    // $MFResultArray = $oMerchantFunding->saveMerchantFunding($MFArray);
}

/** Merchant Funding Section End **/

if ($substatusID > 0 && $newLMRId > 0) {
    /* Ignore to save the file notes when the status came from Summary while new file creation */
    $subInput = [
        'FID'               => $newLMRId,
        'FRID'              => $responseId,
        'SSID'              => $substatusID,
        'chk'               => 1,
        'UID'               => PageVariables::$userNumber,
        'URole'             => $userGroup,
        'isSysNotesPrivate' => $isSysNotesPrivate,
        'saveTab'           => 'LI',
    ];
    saveFileSubstatusChange::getReport($subInput);
}

/* Funding Module */
if (in_array('FU', $fileModule)) {
    $FUArray = [
        'p'     => $_POST,
        'LMRId' => $LMRId,
    ];
    saveFunding::getReport($FUArray);

    if ($goToTab == 'MI') $goToTab = 'DOC';
}
/**
 * Description : HMLO Module section save functionality
 * Author        : Venkatesh
 * Date        : Jan 24, 2016
 **/

$HMLOArray = [
    'p'        => $_POST,
    'LMRId'    => $LMRId,
    'PCID'     => $PCID,
    'UID'      => PageVariables::$userNumber,
    'URole'    => $userGroup,
    'clientId' => $clientId,
    'saveTab'  => 'HMLOSummary',
];

$arrBG =
    [
        'CID'                             => cypher::myEncryption($clientId),
        'PCID'                            => cypher::myEncryption($PCID),
        'executiveId'                     => cypher::myEncryption($executiveId),
        'encClientId'                     => cypher::myEncryption($clientId),
        'userRole'                        => cypher::myEncryption($userRole),
        'isClient'                        => 1,
        'isBorUSCitizen'                  => $_REQUEST['isBorUSCitizen'],
        'borOrigin'                       => $_REQUEST['borOrigin'],
        'borVisaStatus'                   => $_REQUEST['borVisaStatus'],
        'borDecalredBankruptExpln'        => $_REQUEST['borDecalredBankruptExpln'],
        'borOutstandingJudgementsExpln'   => $_REQUEST['borOutstandingJudgementsExpln'],
        'borActiveLawsuitsExpln'          => $_REQUEST['borActiveLawsuitsExpln'],
        'borPropertyTaxLiensExpln'        => $_REQUEST['borPropertyTaxLiensExpln'],
        'borObligatedInForeclosureExpln'  => $_REQUEST['borObligatedInForeclosureExpln'],
        'borDelinquentExpln'              => $_REQUEST['borDelinquentExpln'],
        'borOtherFraudRelatedCrimesExpln' => $_REQUEST['borOtherFraudRelatedCrimesExpln'],
        'borBackgroundExplanation'        => $_REQUEST['borBackgroundExplanation'],
        'sync'                            => 'sync',
    ];


$_POST['userGroup'] = $userGroup;
$_POST['userNumber'] = PageVariables::$userNumber;
$_POST['userName'] = $userName;
$_POST['CID'] = $_POST['encryptedCId'];
$_POST['sync'] = 'sync';


saveHMLOBorrowerInfo::getReport($HMLOArray);
saveAdditionalGuarantorsInfo::getReport($HMLOArray);
saveHMLOFileResponseInfo::getReport($HMLOArray);
saveHMLONewLoanInfo::getReport($HMLOArray);
saveSaleMethodInfo::getReport($HMLOArray);
saveFeeSchedule::getReport($HMLOArray);
saveEquipmentInformation::getReport($HMLOArray);

if (in_array('HMLO', $fileModule) || in_array('EF', $fileModule)) {
    $docIP = ['CID' => $clientId, 'userGroup' => $userGroup, 'userName' => $userName, 'userNumber' => PageVariables::$userNumber, 'p' => $_POST];
    uploadClientDocs::getReport($docIP);
}

savePCClientBackgroundInfo::getReport($arrBG);
savePCClientExperienceInfo::getReport($_POST);

if (isset($_REQUEST['rentroll'])) {
    $ipArray['FileCreatedDate'] = $_POST['recordDate'];
    $ipArray['LMRId'] = $LMRId;
    $ipArray['PCID'] = $_POST['FPCID'];
    saveFileRentRoll::getReport($ipArray);
}

if (in_array($PCID, $glNortheastLendingLeadNotifyPCs) && $newLMRId > 0) {
    LeadNotifierForPCs::getReport(['LMRId' => $LMRId,]);
}

if ($LMRId > 0) {

    $inArray = ['LMRId' => $LMRId, 'PCID' => $PCID];
    $ip = [];
    $CIDArray = [];
    if (!$propMgmntContactID) {
        $ip[] = ['contactName' => $propMgmntContactPerson, 'companyName' => $propMgmntCompany, 'phone' => $propMgmntPhone, 'email' => $propMgmntContactEmail, 'address' => $propMgmntAddress, 'city' => $propMgmntCity, 'state' => $propMgmntState, 'zipCode' => $propMgmntZip, 'description' => $propMgmntNotes, 'contactType' => '9', 'cRole' => 'PM'];
    } else {
        $ip[] = ['CID' => $propMgmntContactID, 'contactName' => $propMgmntContactPerson, 'companyName' => $propMgmntCompany, 'phone' => $propMgmntPhone, 'email' => $propMgmntContactEmail, 'address' => $propMgmntAddress, 'city' => $propMgmntCity, 'state' => $propMgmntState, 'zipCode' => $propMgmntZip, 'description' => $propMgmntNotes, 'contactType' => '9', 'cRole' => 'PM'];
    }
    if (!$HOA1ContactID) {
        $ip[] = ['contactName' => $HOContactName, 'companyName' => $condominiumOrHOAFeeAmtReceiver, 'address' => $feeAmtReceiverAddress, 'city' => $feeAmtReceiverCity, 'state' => $feeAmtReceiverState, 'zipCode' => $feeAmtReceiverZip, 'phone' => $HOPhone, 'fax' => $HOFax, 'email' => $HOEmail, 'contactType' => '8', 'cRole' => 'HOA1'];
    } else {
        $ip[] = ['CID' => $HOA1ContactID, 'contactName' => $HOContactName, 'companyName' => $condominiumOrHOAFeeAmtReceiver, 'address' => $feeAmtReceiverAddress, 'city' => $feeAmtReceiverCity, 'state' => $feeAmtReceiverState, 'zipCode' => $feeAmtReceiverZip, 'phone' => $HOPhone, 'fax' => $HOFax, 'email' => $HOEmail, 'contactType' => '8', 'cRole' => 'HOA1'];
    }
    if (!$HOA2ContactID) {
        $ip[] = ['contactName' => $HOA2ContactName, 'companyName' => $HOA2CompanyName, 'address' => $HOAOrCOAFeeAddress, 'city' => $HOAOrCOAFeeCity, 'state' => $HOAOrCOAFeeState, 'zipCode' => $HOAOrCOAFeeZip, 'contactType' => '8', 'cRole' => 'HOA2'];
    } else {
        $ip[] = ['CID' => $HOA2ContactID, 'contactName' => $HOA2ContactName, 'companyName' => $HOA2CompanyName, 'address' => $HOAOrCOAFeeAddress, 'city' => $HOAOrCOAFeeCity, 'state' => $HOAOrCOAFeeState, 'zipCode' => $HOAOrCOAFeeZip, 'contactType' => '8', 'cRole' => 'HOA2'];
    }
    if (in_array('HOA', $LMRClientType) && $activeTab == 'CI' && $publicUser != 1) {
        $CIDArray1 = [];
        if (count($ip) > 0) {
            $inArray['contacts'] = $ip;
            $CIDArray1 = saveContacts::getReport($inArray);
        }
        $CIDArray = array_merge($CIDArray, $CIDArray1);
        if (count($CIDArray) > 0) {
            saveFileContacts::getReport(['LMRId' => $LMRId, 'CID' => $CIDArray]);
        }

        $QAInfoArray = [
            'LMRId' => $LMRId, 'feeAmtReceiverAddress' => $feeAmtReceiverAddress,
        ];
        if (isset($_POST['condominiumOrHOAFee'])) $QAInfoArray['condominiumOrHOAFee'] = $condominiumOrHOAFee;
        if (isset($_POST['condominiumOrHOAFeeAmt'])) $QAInfoArray['condominiumOrHOAFeeAmt'] = $condominiumOrHOAFeeAmt;
        if (isset($_POST['isHOAOrCOAFee'])) $QAInfoArray['isHOAOrCOAFee'] = $isHOAOrCOAFee;
        if (isset($_POST['HOAOrCOAFeeAmt'])) $QAInfoArray['HOAOrCOAFeeAmt'] = $HOAOrCOAFeeAmt;
        if (isset($_POST['HOAFeePaidCurrent'])) $QAInfoArray['HOAFeePaidCurrent'] = $HOAFeePaidCurrent;
        if (isset($_POST['HOAAllowRentals'])) $QAInfoArray['HOAAllowRentals'] = $HOAAllowRentals;
        if (isset($_POST['IsCommunity'])) $QAInfoArray['IsCommunity'] = $IsCommunity;
        if (isset($_POST['HO1Notes'])) $QAInfoArray['HOA1Notes'] = $HO1Notes;
        if (isset($_POST['HO2Notes'])) $QAInfoArray['HOA2Notes'] = $HO2Notes;

        updateQAInfo::getReport(['QAInfo' => $QAInfoArray, 'LMRId' => $LMRId]);

    }

    if (isset($_POST['PublishBInfo'])) $PublishBInfo = $_POST['PublishBInfo'];
    if (isset($_POST['BEthnicity'])) $BEthnicity = $_POST['BEthnicity'];
    if (isset($_POST['QAId'])) $QAId = $_POST['QAId'];
    if (isset($_POST['BRace'])) $BRace = $_POST['BRace'];

    if (isset($_POST['BGender'])) $BGender = $_POST['BGender'];

    if (isset($_POST['PublishCBInfo'])) $PublishCBInfo = $_POST['PublishCBInfo'];
    if (isset($_POST['CBEthnicity'])) $CBEthnicity = $_POST['CBEthnicity'];
    if (isset($_POST['CBRace'])) $CBRace = $_POST['CBRace'];
    if (isset($_POST['CBGender'])) $CBGender = $_POST['CBGender'];

    $govtIp = [
        'LMRId'         => $LMRId,
        'QAId'          => $QAId,
        'PublishBInfo'  => $PublishBInfo,
        'BEthnicity'    => $BEthnicity,
        'BRace'         => $BRace,
        'BGender'       => $BGender,
        'PublishCBInfo' => $PublishCBInfo,
        'CBEthnicity'   => $CBEthnicity,
        'CBRace'        => $CBRace,
        'CBGender'      => $CBGender,
        'p'             => $_POST,
        'responseId'    => $responseId,
        'PCID'          => $PCID,
        'UID'           => PageVariables::$userNumber,
        'branchId'      => $executiveId,
        'UGroup'        => $userGroup,
        'saveTab'       => 'QA'
    ];

    $QAId = saveHMLOQAInfo::getReport($govtIp);


    $docArray = [];
    $borrowerLName = '';
    $recordDate = '';
    $oldFPCID = 0;
    if (isset($_POST['borrowerLName'])) {
        $borrowerLName = $_POST['borrowerLName'];
    }
    if (isset($_POST['recordDate'])) {
        $recordDate = $_POST['recordDate'];
    }
    if (isset($_POST['oldFPCID'])) {
        $oldFPCID = $_POST['oldFPCID'];
    }
    if (isset($_POST['op'])) $op = trim($_POST['op']);

    /* Proof of sale (HUD) Upload */
    for ($i = 0; $i <= count($_FILES['proofOfSale']['name'] ?? []); $i++) {
        $fileSrc_name = '';
        $tmp_name = '';
        $file_type = '';
        $file_size = '';
        $fileExtension = '';
        $docName = '';
        if (isset($_FILES['proofOfSale']['name'][$i])) $fileSrc_name = trim($_FILES['proofOfSale']['name'][$i]);
        if (isset($_FILES['proofOfSale']['tmp_name'][$i])) $tmp_name = trim($_FILES['proofOfSale']['tmp_name'][$i]);
        if (isset($_FILES['proofOfSale']['type'][$i])) $file_type = trim($_FILES['proofOfSale']['type'][$i]);
        if (isset($_FILES['proofOfSale']['size'][$i])) $file_size = trim($_FILES['proofOfSale']['size'][$i]);
        if ($fileSrc_name) {
            $docName = Strings::removeDisAllowedChars($fileSrc_name);
            $info = pathinfo($docName);
            if (count($info) > 0) {
                $fileExtension = $info['extension'];
            }
            $docName = str_ireplace('.' . $fileExtension, '', $fileSrc_name);

        }
        if ($file_size > 0) {
            $tempArray = [
                'fileSrc_name' => $fileSrc_name,
                'tmp_name'     => $tmp_name,
                'file_type'    => $file_type,
                'file_size'    => $file_size,
                'docName'      => $docName,
                'docCategory'  => 'Proof of sale (HUD)-' . $i
            ];
            $docArray[] = $tempArray;
        }
    }

    $insCnt = 0;
    $tempDocName = '';
    for ($m = 0; $m < count($docArray); $m++) {
        if (in_array($docArray[$m]['file_type'], $glMimeTypes)) {
            if ($docArray[$m]['file_size'] > CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED) {
                Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
            } else {
                if ($docArray[$m]['fileSrc_name']) {
                    $file_name = Strings::removeDisAllowedChars($docArray[$m]['fileSrc_name']);
                    $info = pathinfo($file_name);
                    if (count($info) > 0) $fileExtension = $info['extension'];
                    $infoArray = [
                        'fileExtension'     => $fileExtension,
                        'LMRID'             => $LMRId,
                        'PCID'              => $oldFPCID,
                        'userGroup'         => $userGroup,
                        'userNumber'        => PageVariables::$userNumber,
                        'borrowerLName'     => $borrowerLName,
                        'userName'          => $userName,
                        'uploadedBy'        => PageVariables::$userNumber,
                        'uploadingUserType' => $userGroup,
                        'docName'           => Strings::stripQuote($docArray[$m]['docName']),
                        'docCategory'       => $docArray[$m]['docCategory'],
                        'isSysNotesPrivate' => $isSysNotesPrivate,
                        'recordDate'        => $recordDate,
                        'oldFPCID'          => $oldFPCID,
                        'tmpFileContent'    => base64_encode(FileStorage::getFile(dirname($docArray[$m]['tmp_name']) . '/' . basename($docArray[$m]['tmp_name']))),

                    ];
                    if ($m > 0) $tempDocName .= ', ';
                    $tempDocName .= Strings::stripQuote($docArray[$m]['docName']);
                    if (($m + 1) == count($docArray)) {
                        $infoArray['saveNotes'] = 1;
                        $infoArray['saveNotesDocs'] = $tempDocName;
                    }
                    $docId = 0;
                    $res = '';

                    $docId = saveFileDocument::getReport($infoArray);

                    if ($docId > 0) {
                        $infoArray['fileDocName'] = Strings::removeDisAllowedChars(Strings::stripQuote($docArray[$m]['docName'])) . '_' . Strings::removeDisAllowedChars($borrowerLName) . '_' . cypher::myEncryption($docId) . '.' . $fileExtension;
                        $res = UploadServer::upload($infoArray);
                        if ($res == 'Success') $insCnt++;
                    }
                }
            }
        } else {
            Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
        }
    }

    if (in_array('LO', $fileModule)) {
        $QAInfoArray = ['LMRId' => $LMRId];
        if (isset($_POST['noOfPeopleDependent'])) $QAInfoArray['noOfPeopleDependent'] = $noOfPeopleDependent;

        updateQAInfo::getReport(['QAInfo' => $QAInfoArray, 'LMRId' => $LMRId]);
    }


    if ($publicUser == 1) {
        saveLMProposalInfo::getReport([
            'LMRId'   => $LMRId,
            'PID'     => $PID,
            'opt'     => 'CI',
            'DIYUser' => '1',
            'UID'     => PageVariables::$userNumber,
            'UGroup'  => $userGroup
        ]);
    }
    $notes = '';
    $branchNotes = '';
    $agentNotes = '';
    if ($existingBRID == $executiveId) {
    } else {
        $branchIDs = $executiveId;
        if ($existingBRID > 0) {
            $branchIDs .= ', ' . $existingBRID;
            $branchInfo = [];
            if ($branchIDs) {
                $branchInfo = \models\composite\oBranch\getMyDetails::getReport(['executiveId' => $branchIDs]);
            }
            if (count($branchInfo) > 0) {
                $branchNotes = 'Branch changed from ';
                if (array_key_exists($existingBRID, $branchInfo)) {
                    $branchNotes .= $branchInfo[$existingBRID]['LMRExecutive'];
                }
                if (array_key_exists($executiveId, $branchInfo)) {
                    $branchNotes .= ' to ' . $branchInfo[$executiveId]['LMRExecutive'] . '.';
                }
            }
        }

        if ($executiveId > 0 && $existingBRID > 0 && count($LMRClientType) > 0) {
            $selectedModuleServices = getBranchModulesForServices::getReport(['branchID' => $existingBRID, 'STCode' => implode(',', $LMRClientType)]);
            if (count($selectedModuleServices) > 0) $selectedModuleServiceKeys = array_keys($selectedModuleServices);
            for ($i = 0; $i < count($selectedModuleServiceKeys); $i++) {
                $tempSelArray = [];
                $tempSelArray = $selectedModuleServices[$selectedModuleServiceKeys[$i]];
                for ($j = 0; $j < count($tempSelArray); $j++) {
                    $existingBranchSelectedServices[] = $tempSelArray[$j]['STCode'];
                    $existingBranchSelectedModules[] = $tempSelArray[$j]['moduleCode'];
                }
            }
        }
    }
    if ($existingAID == $brokerNumber) {
    } else {
        $agentIDs = $brokerNumber;
        if ($existingAID > 0) {
            $agentIDs .= ', ' . $existingAID;
            $agentInfo = [];
            if ($agentIDs) {
                $agentInfo = \models\composite\oBroker\getMyDetails::getReport(['agentId' => $agentIDs]);
            }
            if (count($agentInfo) > 0) {
                $agentNotes = 'Loan Officer/Broker changed from ';
                if (array_key_exists($existingAID, $agentInfo)) {
                    $agentNotes .= $agentInfo[$existingAID]['brokerName'];
                }
                if (array_key_exists($brokerNumber, $agentInfo)) {
                    $agentNotes .= ' to ' . $agentInfo[$brokerNumber]['brokerName'] . '.';
                }
            }
        }
    }
    if ($existingSAID != $secondaryBrokerNumber && $LMRId > 0) {
        $secondaryAgentIDs = $secondaryBrokerNumber;
        if ($existingSAID > 0) {
            $secondaryAgentIDs .= ', ' . $existingSAID;
            $secondaryAgentInfo = [];
            if ($secondaryAgentIDs) {
                $secondaryAgentInfo = \models\composite\oBroker\getMyDetails::getReport(['agentId' => $secondaryAgentIDs]);
            }
            if (count($secondaryAgentInfo) > 0) {
                $secondaryAgentNotes = 'Loan Officer changed from ';
                if (array_key_exists($existingSAID, $secondaryAgentInfo)) {
                    $secondaryAgentNotes .= $secondaryAgentInfo[$existingSAID]['brokerName'];
                }
                if (array_key_exists($secondaryBrokerNumber, $secondaryAgentInfo)) {
                    $secondaryAgentNotes .= ' to ' . $secondaryAgentInfo[$secondaryBrokerNumber]['brokerName'] . '.';
                }
            }
        }
    }
    $append = '';
    if ($branchNotes) {
        $notes = $branchNotes;
        $append = '<br>';
    }
    if ($agentNotes) $notes .= $append . $agentNotes;
    if ($secondaryAgentNotes) $notes .= $append . $secondaryAgentNotes;
    if ($notes == '') {
    } else {
        $exeId = 0;
        $empId = 0;
        $brId = 0;
        if ($userGroup == 'Super') {
            $exeId = 0;
            $empId = 0;
            $brId = 0;
        } else if ($userGroup == 'Employee') {
            $exeId = 0;
            $empId = PageVariables::$userNumber;
            $brId = 0;
        } else if ($userGroup == 'Branch') {
            $exeId = PageVariables::$userNumber;
            $empId = 0;
            $brId = 0;
        } else if ($userGroup == 'Agent') {
            $empId = 0;
            $exeId = 0;
            $brId = PageVariables::$userNumber;
        }

        $ip = [
            'processorNotes' => $notes,
            'fileID'         => $LMRId,
            'privateNotes'   => $isSysNotesPrivate,
            'employeeId'     => $empId,
            'executiveId'    => $exeId,
            'brokerNumber'   => $brId,
            'isSysNotes'     => 1,
        ];
        saveFileNotes::getReport($ip);
    }
}

if ($newLMRId > 0 && $userGroup == 'Employee') {
    $inArray['LMRId'] = trim($LMRId);
    $inArray['UID'] = [PageVariables::$userNumber];
    $inArray['URole'] = 'Employee';
    $inArray['assignedByUID'] = PageVariables::$userNumber;
    $inArray['assignedByURole'] = $userGroup;
    $inArray['opt'] = 'CI';

    $updateCnt = assignUserToFile::getReport($inArray);
}
if ($newLMRId > 0 && (glCustomJobForProcessingCompany::is($PCID))) {
    $feeType = [BCForm::CONST_BILLING_COM_RETAINER_FEE, BCForm::CONST_BILLING_COM_RECURRING_FEE];  /* 2, 47 */
    saveBllingFee::getReport(['LMRId' => $LMRId, 'feeType' => $feeType,]);
}
if ($publicUser == 1) {
    if (($responseId > 0) && ($branchLeadSource)) {
        $ipArray['LMRResponseId'] = $responseId;
        $ipArray['leadSource'] = $branchLeadSource;
        updateBranchLeadSourceToLMR::getReport($ipArray);
    }
}
$ip1 = [];
$brokerArray = [];
$sendNewDealAlert = 0;
$tempSiteName = '';

$ip1 = ['brokerNumber' => $brokerNumber,];

if ($brokerNumber > 0) {
    $brokerArray = getBrokerInfo::getReport($ip1);
    if (count($brokerArray) > 0) {
        $sendNewDealAlert = trim($brokerArray['sendNewDealAlert']);
    }
}
if ($LMRId > 0 && $existingLMRId == 0 && ($publicUser == 1 || $userRole == 'Agent') && (in_array('SS', $fileModule) || in_array('LM', $fileModule))) {
//        if($sendNewDealAlert > 0) {
    $ipArray = ['LMRId' => $LMRId, 'op' => 'CI'];
    sendSVLoanModificationRequestEmail::getReport($ipArray, $brokerNumber, $executiveId);
//        }
}

if ($LMRId > 0 && $existingLMRId == 0) {
    if ($userGroup == 'Client') {
        $attachmentArray = [];

        $LMRArray = getFileInfo::getReport([
            'LMRId' => $LMRId,
        ]);
        $myFileInfo = $LMRArray[$LMRId];
        $LMRInfoArray = $myFileInfo['LMRInfo'];
        if (count($LMRInfoArray) > 0) {

            $fileCreatedDate = $recordDate = $LMRInfoArray['recordDate'];
            $FPCID = $LMRInfoArray['FPCID'];
            $executiveId = $LMRInfoArray['FBRID'];
            $oldFPCID = $LMRInfoArray['oldFPCID'];
            $borrowerName = $LMRInfoArray['borrowerName'];
            $borrowerLName = $LMRInfoArray['borrowerLName'];
            $defaultBrokerNumber = $LMRInfoArray['brokerNumber'];

            $inArray = [];
            /**
             * Desc : Get Package Save Path - (#154749877)
             */
            $tempFileCreatedDate = '';
            $tempFileCreatedDate = str_replace('-', '', $fileCreatedDate);
            $dest = $oldFPCID . '/' . date('Y', strtotime($tempFileCreatedDate)) . '/' . date('m', strtotime($tempFileCreatedDate)) . '/' . date('d', strtotime($tempFileCreatedDate));
            /**
             * Desc : HMLO Loan Application Details.- (#154749877)
             */
            $WFPkgID = '';
            if ($activeTab == 'LI') {
                $WFPkgID = 887;
                $webformName = 'Full Loan App';
            } else {
                $WFPkgID = 886;
                $webformName = 'Quick App';
            }

            $pkgAllSelectedArray = getLibPackage::getReport(['PKGID' => $WFPkgID]); // Get Package Information...
            $docName = $pkgAllSelectedArray[$WFPkgID]['pkgName'];
            $docId = $pkgAllSelectedArray[$WFPkgID]['PKGID'];
            $esign = $pkgAllSelectedArray[$WFPkgID]['esign'];
            $packageType = $pkgAllSelectedArray[$WFPkgID]['packageType'];
            $selectedPkgUrl = trim($pkgAllSelectedArray[$WFPkgID]['filePath']);
            $inArray['userNumber'] = PageVariables::$userNumber;
            $inArray['userGroup'] = $userGroup;
            $inArray['LMRId'] = $LMRId;
            $inArray['pkgID'] = $docId;
            $inArray['responseId'] = $responseId;
            $inArray['printOutput'] = 'n';
            $inArray['actions'] = 'Emailed';
            $inArray['attach'] = 'y';
            $inArray['PCID'] = $PCID;
            $inArray['LMRArray'] = $LMRArray;
            generateAppropriatePkg($inArray);

            /**
             * Desc : Generate and Get Attachment of "HMLO Loan Application".- (#154749877)
             */
            $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . Strings::removeDisAllowedChars(stripslashes($docName)) . '_' . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($docId)) . '.pdf{' . $docName . '.pdf';

            $LMREmailSent = $LMRInfoArray['LMREmailSent'];
            $ip = [];
            if ($LMREmailSent == 0) {
                $ip['LMRId'] = $LMRId;
                $ip['userIPAddr'] = $_SERVER['REMOTE_ADDR'];
                $ip['webformName'] = $webformName;
                $ip['typeOfHMLOLoanRequesting'] = $_POST['typeOfHMLOLoanRequesting'];
                $ip['purchaseCloseDate'] = $_POST['purchaseCloseDate'];
                $ip['attachment'] = $attachmentArray;
                $ip['userGroup'] = $userGroup;
            }
            $ip['fileRow'] = 'Insert';
            $ip['typeOfForm'] = 'CI';
            if (in_array('HMLO', $fileModule)) {
                sendEmailOnHMLOFileCreation::getReport($ip, $defaultBrokerNumber, $executiveId, $myFileInfo);
            } else {
                sendHMLORequestEmail::getReport($ip, $defaultBrokerNumber, $executiveId, $myFileInfo);
            }
            $ip = [];
        }
    }
    //send email notification based on the user (BO, Branch, Broker, Loan Officer) permissions
    if ($userGroup != 'Client') { // loan file insert / update - to make sure file is created with no errors
        //hidden field condition
        $LMRArray = getFileInfo::getReport([
            'LMRId' => $LMRId,
        ]);
        $myFileInfo = $LMRArray[$LMRId];
        $LMRInfoArray = $myFileInfo['LMRInfo'];
        if (count($LMRInfoArray) > 0) {

            $fileCreatedDate = $recordDate = $LMRInfoArray['recordDate'];
            $FPCID = $LMRInfoArray['FPCID'];
            $executiveId = $LMRInfoArray['FBRID'];
            $oldFPCID = $LMRInfoArray['oldFPCID'];
            $borrowerName = $LMRInfoArray['borrowerName'];
            $borrowerLName = $LMRInfoArray['borrowerLName'];
            $defaultBrokerNumber = $LMRInfoArray['brokerNumber'];

            $inArray = [];
            $tempFileCreatedDate = '';
            $tempFileCreatedDate = str_replace('-', '', $fileCreatedDate);
            $dest = $oldFPCID . '/' . date('Y', strtotime($tempFileCreatedDate)) . '/' . date('m', strtotime($tempFileCreatedDate)) . '/' . date('d', strtotime($tempFileCreatedDate));
            $WFPkgID = '';
            if ($activeTab == 'LI') {
                $WFPkgID = 887;
                $webformName = 'Full Loan App';
            } else {
                $WFPkgID = 886;
                $webformName = 'Quick App';
            }

            $pkgAllSelectedArray = getLibPackage::getReport(['PKGID' => $WFPkgID]); // Get Package Information...
            $docName = $pkgAllSelectedArray[$WFPkgID]['pkgName'];
            $docId = $pkgAllSelectedArray[$WFPkgID]['PKGID'];
            $esign = $pkgAllSelectedArray[$WFPkgID]['esign'];
            $packageType = $pkgAllSelectedArray[$WFPkgID]['packageType'];
            $selectedPkgUrl = trim($pkgAllSelectedArray[$WFPkgID]['filePath']);
            $inArray['userNumber'] = PageVariables::$userNumber;
            $inArray['userGroup'] = $userGroup;
            $inArray['LMRId'] = $LMRId;
            $inArray['pkgID'] = $docId;
            $inArray['responseId'] = $responseId;
            $inArray['printOutput'] = 'n';
            $inArray['actions'] = 'Emailed';
            $inArray['attach'] = 'y';
            $inArray['PCID'] = $PCID;
            $inArray['LMRArray'] = $LMRArray;
            generateAppropriatePkg($inArray);
            $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . Strings::removeDisAllowedChars(stripslashes($docName)) . '_' . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($docId)) . '.pdf{' . $docName . '.pdf';
            $ip = [];
            $LMREmailSent = $LMRInfoArray['LMREmailSent'];
            if ($LMREmailSent == 0) {
                $ip['LMRId'] = $LMRId;
                $ip['userIPAddr'] = $_SERVER['REMOTE_ADDR'];
                $ip['webformName'] = $webformName;
                $ip['typeOfHMLOLoanRequesting'] = $_POST['typeOfHMLOLoanRequesting'];
                $ip['purchaseCloseDate'] = $_POST['purchaseCloseDate'];
                $ip['attachment'] = $attachmentArray;
                $ip['userGroup'] = $userGroup;
            }
            if (in_array('HMLO', $fileModule)) {
                sendEmailOnHMLOFileCreation::getReport($ip, $defaultBrokerNumber, $executiveId, $myFileInfo);
            } else {
                sendHMLORequestEmail::getReport($ip, $defaultBrokerNumber, $executiveId, $myFileInfo);
            }
            $ip = []; // this needs to be reset
        }

    }
    //end send email notification based on the user (BO, Branch, Broker, Loan Officer) permissions

}


$inputArray = [
    'p'     => $_POST,
    'LMRId' => $LMRId,
];

$ip = [
    'p'     => $_POST,
    'LMRId' => $LMRId,
];
if ($LMRId > 0 && in_array('LO', $fileModule)) {
    $resultArray = saveFileLoanOrigination::getReport($inputArray);
    $resArray = saveFileLOQAInfo::getReport($ip);
    if ($goToTab == 'MI') $goToTab = 'PI';
}

/**
 * param inputs $inputArray like LMRId, client id, option.
 * Check borrower prfile related info has changed.
 * Card #600 - Updating Borrower profile logic.
 * Added by Suresh Kasinathan <<EMAIL>>
 */
if (Arrays::getArrayValue('confirmType', $_REQUEST)) {
    updateClientProfile::getReport([
        'LMRId' => $LMRId,
        'CID'   => $clientId,
        'opt'   => Arrays::getArrayValue('confirmType', $_REQUEST),
    ]);
}

if ($isBorrowermailEdited == 'edited') {
    // need to update all loan files
    $updateqry = " UPDATE tblFile SET clientId = '" . $clientId . "', borrowerEmail='" . $clientEmail . "',
      enc_borrowerEmail   = '" . cypher::myEncryption($clientEmail) . "' WHERE clientId = " . $originalClientId . ';';
    $updatecnt = Database2::getInstance()->update($updateqry);

    $updatecqry = ' UPDATE tblClient SET activeStatus = 1 WHERE CID = ' . $originalClientId . ';';
    $updateccnt = Database2::getInstance()->update($updatecqry);
}

if ((trim($_POST['OSID']) != trim($_POST['primaryStatus'])) && $PCID == 4069 && $_POST['primaryStatus'] == 78881) {
    $salesForceID = $_POST['projectName'] ?? '';
    if ($salesForceID) {
        postDataToEndPoint::getReport(['salesForceID' => $salesForceID]);
    }
}
//CV3 sync Processing & Underwriting Fee to the HUD tab
//File Create Only
if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && !$existingLMRId) {
    LMRequest::$LMRId = $LMRId;
    LMRequest::$PCID = $PCID;
    HUDController::getUnderwritingFeeHUD($PCID);
    HUDController::getProcessingFeeHUD($PCID);
}

//CV3 sync Processing & Underwriting Fee to the HUD tab | New logic
if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
    LMRequest::$LMRId = $LMRId;
    LMRequest::$PCID = $PCID;
    HUDController::getSetUnderwritingFeeHUD();
    HUDController::getSetProcessingFeeHUD();
}

HUDController::getFeasibilityFeeHUD($PCID);
//re-calculate HUD
HUDCalculation::process((int)$LMRId);

tblAutomatedRuleRequestV2::Trigger($LMRId, $PCID); // last
CustomField::Save(
    tblFile::class,
    $LMRId,
    $PCID,
    PageVariables::$userNumber,
    PageVariables::$userRole,
    PageVariables::$userGroup
);

Database2::saveLogQuery();


if ($publicUser == 1) {
    $branchReferralCode = '';
    $agentReferralCode = '';
    $fOpt = '';
    if (isset($_POST['branchReferralCode'])) $branchReferralCode = trim($_POST['branchReferralCode']);
    if (isset($_POST['agentReferralCode'])) $agentReferralCode = trim($_POST['agentReferralCode']);
    if (isset($_POST['fOpt'])) $fOpt = trim($_POST['fOpt']);

    $redirect .= CONST_SITE_URL . 'loanModificationPrequalRemote.php?rsc=' . $branchReferralCode . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&lId=' . cypher::myEncryption($LMRId) . '&fOpt=' . cypher::myEncryption($fOpt) . '&tabOpt=';
    if ($btnValue == 'Save') $redirect .= $activeTab;
    else $redirect .= $goToTab;
    header('Location: ' . $redirect);

} else if ($DIYUser == 1) {
    $referralSiteCode = 0;
    if (isset($_POST['referralSiteCode'])) $referralSiteCode = trim($_POST['referralSiteCode']);
    $redirect .= CONST_SITE_URL . 'DoItUrSelf/loanModificationFile.php?cId=' . cypher::myEncryption($clientId) . '&rs=' . $referralSiteCode . '&lId=' . cypher::myEncryption($LMRId) . '&rId=' . cypher::myEncryption($responseId);
    if ($btnValue == 'Save') $redirect .= $activeTab;
    else $redirect .= $goToTab;
    header('Location: ' . $redirect);
} else {
    if ($userRole == 'Branch') $redirect = CONST_URL_BRSSL;
    elseif ($userRole == 'Agent') $redirect = CONST_URL_AG_SSL;
    elseif (($userRole == 'Client') && (preg_match('/client_new\//i', $_SERVER['HTTP_REFERER']))) $redirect = CONST_URL_CL_NEW_SSL;
    elseif ($userRole == 'Client') $redirect = CONST_URL_CL_SSL;
    else    $redirect = CONST_URL_BOSSL;

    $redirect .= 'LMRequest.php?eId=' . cypher::myEncryption($executiveId) . '&lId=' . cypher::myEncryption($LMRId) . '&rId=' . cypher::myEncryption($responseId) . '&op=' . trim($op) . '&tabOpt=';
    if ($btnValue == 'Save' || Arrays::getArrayValue('isSave', $_REQUEST) == 1) $redirect .= $activeTab;
    else $redirect .= $goToTab;

    //check if the PC is enabled for Automation
    if ($allowAutomation) { //$allowAutomation set in getPageVariables.php
        $fileTypesTxt = is_array($fileModule) ? implode(',', $fileModule) : $fileModule;
        include('automatedRulesActionController.php');
    }
    header('Location: ' . $redirect);
}
exit();

