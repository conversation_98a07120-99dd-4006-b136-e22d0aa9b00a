<?php
global $agentReferralCode, $lmrLogo, $userLogo, $fOpt, $branchReferralCode, $executiveId,
       $oBranch, $processingCompanyId, $tabOpt, $so, $myFileInfo, $LMRId,
       $wfOpt, $reqCnt, $assignedPCID, $agentNumber, $dummyBrokerId, $loanOfficerId, $primeStatusId,
       $publicUser, $responseId, $ft, $fileRecordDate, $emailOpt, $allowToEdit, $clientId, $hideBorrowerInfo, $allowCaptcha,
       $hsFADisplay, $hsQADisplay, $UType, $aud, $borInfoRed, $isHMLO, $isEsignRequired, $tacField, $LMRResponseId;
global $brokerLogo;

use models\composite\oBranch\getBranchDocuments;
use models\constants\gl\glMandatoryFieldForPC;
use models\constants\gl\glPCID;
use models\cypher;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;

$glMandatoryFieldForPC = glMandatoryFieldForPC::$glMandatoryFieldForPC;

$allowToSeeBillingSectionForFile = 1;
/*  Webforms --> Remove Internal titles from displaying inside webform 
*   Ticket ID = ********* on Dec 01, 2017
*/
if ($agentReferralCode != 1) {
    //$webformName  = "Hard Money App without Tracking";
    $webformName = 'Full Loan App';
} else {
    //$webformName  = "Hard Money App with Tracking";
    $webformName = 'Full Loan App';
}
$formEsignSignature = '';
//if(in_array($processingCompanyId, $glCustomWebformDesignForCorporateCapitalLLCPC)) {

$logoWidth = $logoHeight = '';
if (trim($lmrLogo) != '') {
    $LMRLogoExist = false;
    $LMRLogoExist = file_exists(CONST_BRANCH_LOGO_PATH . $lmrLogo);

    if ($LMRLogoExist) {
        list($logoWidth, $logoHeight) = getimagesize(CONST_BRANCH_LOGO_URL . $lmrLogo);
        $lmrLogo = CONST_BRANCH_LOGO_URL . $lmrLogo;
    } else {
        $lmrLogo = '';
    }
}

if ($lmrLogo == '') {

    $logoWidth = $logoHeight = '';
    if (trim($userLogo) != '') {
        $PCLogoExist = false;
        $PCLogoExist = file_exists(CONST_PATH_PC_LOGO . $userLogo);

        if ($PCLogoExist) {
            list($logoWidth, $logoHeight) = getimagesize(CONST_PC_LOGO_URL . $userLogo);
            $lmrLogo = CONST_PC_LOGO_URL . $userLogo;
        } else {
            $lmrLogo = '';
        }
    }
}

//Broker Logo
if (trim($brokerLogo) != '') {
    $brokerLogoFileExist = false;
    $brokerLogoFileExist = file_exists(CONST_BROKER_LOGO_PATH . $brokerLogo);
    if ($brokerLogoFileExist) {
        list($logoWidth, $logoHeight) = getimagesize(CONST_BROKER_LOGO_URL . $brokerLogo);
        $lmrLogo = CONST_BROKER_LOGO_URL . $brokerLogo;
    }
}
if ((($fOpt == 'branch')) || (($branchReferralCode > 1) && ($agentReferralCode <= 1))) {
    $docName = '';
    $displayDocName = '';
    $docArray = [];
    $tempDocArray = [];
    $inputArray = ['executiveId' => $executiveId,
        'attachWithEmail' => '',
        'publishInPQPortal' => '1',
        'publishDoument' => '',
    ];
    $docArray = getBranchDocuments::getReport($inputArray);
    $commFormName = 'HMLOWebForm';

    /*if(count($docArray) > 0) {
?>
      <div class="left" style="width:500px;height:90px;overflow:auto;">
        <?php  include "listLMRUploadedDoc.php";?>
      </div>
<?php
    }*/
}

$PCClientTypeInfoArray = [];
$PCLMRClientTypeInfoArray = [];
if ((($fOpt == 'branch')) || (($branchReferralCode > 1) && ($agentReferralCode <= 1))) {

    $docName = '';
    $displayDocName = '';
    $docArray = [];
    $tempDocArray = [];
    $inputArray = ['executiveId' => $executiveId,
        'attachWithEmail' => '',
        'publishInPQPortal' => '1',

        'publishDoument' => '',
    ];
    $docArray = getBranchDocuments::getReport($inputArray);
    $commFormName = 'HMLOWebForm';
}
/**
 *
 * Description : Make the SMS Servicer provider mandatory in all HMLO versions of their webforms.
 * Date        : Dec 16, 2017
 * Developer   : Suresh
 * 820  = Dave PC
 * 2    = AWATA PC
 * 3093 = North By NorthEast Lending PC
 **/
$checkMandatoryFields = 0;
if (in_array($processingCompanyId, $glMandatoryFieldForPC)) {
    $checkMandatoryFields = 1;
}
if ($tabOpt == 'CL') {
    $saveUrl = " action=\"" . CONST_URL_POPS . "requiredDocSave.php\" onsubmit=\"javascript:return uploadValidate('chkId');\" ";
} else {
    $saveUrl = " action=\"" . CONST_SITE_URL . "backoffice/saveHMLOWebForm.php\" ";
}
?>
<div class=" <?php if (isset($_REQUEST['view'])) {
    if (cypher::myDecryption(Request::GetClean('view')) == 'wizard') {
        echo 'px-2 container-fluid';
    }
} else {
    echo 'container ';
} ?>">
    <nav class="navbar bg-white navbar-light border-bottom">
        <!-- Brand -->
        <a class="navbar-brand img-responsive mx-auto mx-sm-0 p-0" href="#">
            <?php if ($lmrLogo != '') { ?>
                <img src="<?php echo $lmrLogo; ?>"
                     style="height: 100%;max-height: 79px !important; max-width: 100%;">
            <?php } ?>
        </a>
        <?php if ($so == 'yes') { //$so is set in HMLOWebForm.php
            $offerArray = [];
            $listingPageArray = [];
            $dealSummary = '';
            $pointOfContactName = '';
            $listingType = '';
            $dealStatus = '';
            if (count($myFileInfo) > 0) {
                if (array_key_exists('offerArray', $myFileInfo)) $offerArray = $myFileInfo['offerArray'];
                if (array_key_exists('listingPageArray', $myFileInfo)) $listingPageArray = $myFileInfo['listingPageArray'];
            }
            $dealSummary = $listingPageArray['listDescription'];
            $listingType = $listingPageArray['listingType'];
            if ($listingType == 'OFF') {
                $dealStatus = 'Open For Funding';
            } elseif ($listingType == 'NFS') {
                $dealStatus = 'Note for Sale';
            } elseif ($listingType == 'CL') {
                $dealStatus = 'Closed Loan';
            } elseif ($listingType == 'PFS') {
                $dealStatus = 'Property For Sale(Coming Soon)';
            }
            $pointOfContactName = $listingPageArray['pointOfContactName'];
            ?>
            <div class="row">
                <h5>Point of Contact: <i class="mr-4"><?php echo $pointOfContactName; ?></i>
                    Deal Status:
                    <i><?php echo $dealStatus; ?></i></h5>
            </div>

            <!-- Login for Submit Offer -->
            <?php

            if (!$_SESSION && cypher::myDecryption($_REQUEST['so']) == 'yes') { ?>
                <a href="<?php echo CONST_SITE_URL . 'account-login.php'; ?>" class="btn btn-primary" role="button">Login</a>

            <?php } ?>
            <!--// Login for Submit Offer //-->

        <?php } ?>


        <!-- Toggler/collapsibe Button -->
        <button class="navbar-toggler d-none" type="button" data-toggle="collapse" data-target="#collapsibleNavbar">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navbar links -->
        <div class="collapse navbar-collapse" id="collapsibleNavbar">
            <ul class=" navbar-nav">

                <li <?php if ($LMRId != '' && $LMRId > 0) { ?>
                    onclick="showTabForShortVersion('<?php echo $LMRId; ?>', 'BI', 'loanModForm', '<?php echo $agentReferralCode ?>');"
                    <?php if ($tabOpt == 'BI') { ?>
                        class="active nav-item " <?php }
                } else { ?> class=" nav-item " <?php } ?> >
                    <a href="#"
                       class="nav-link py-2"><?php if ($wfOpt == 'FA') { ?> Full Application <?php } else { ?> Quick Submission <?php } ?></a>
                </li>
                <?php
                if ($LMRId > 0 && $reqCnt > 0) { ?>
                    <li onclick="showTabForShortVersion('<?php echo $LMRId; ?>', 'CL', 'loanModForm', '<?php echo $agentReferralCode ?>');"
                        <?php if ($tabOpt == 'CL') { ?> class=" nav-item " <?php } else { ?> class="nav-item"<?php } ?> >
                        <a href="#" class=" nav-link py-2">Required Docs</a>
                    </li>
                <?php }
                if (count($docArray) > 0) { ?>
                    <li class="nav-item dropdown  <?php if (isset($_REQUEST['view'])) {
                        echo 'd-none';
                    } ?>">
                        <a href="javascript:void(0);" class="dropdown-toggle  nav-item" data-toggle="dropdown"
                           role="button"
                           aria-expanded="false"> Useful Documents <span class="caret"></span></a>
                        <ul class="dropdown-menu" role="menu">
                            <?php
                            for ($doc = 0; $doc < count($docArray); $doc++) {
                                $tempDocArray = [];
                                $docName = '';
                                $displayDocName = '';
                                $docId = 0;
                                $docId = trim($docArray[$doc]['serialNumber']);
                                $docName = trim($docArray[$doc]['docName']);
                                $displayDocName = trim($docArray[$doc]['displayDocName']);
                                if (($displayDocName == '') || ($displayDocName == NULL)) {
                                    $displayDocName = $docName;
                                }
                                $url = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_FOLDER_BRANCH_DOC) . '&opt=enc';
                                $dispFS = 0;
                                ?>
                                <li class="even"><a target="_blank" href="<?php echo $url ?>"
                                                    rel="nofollow"><?php echo $displayDocName ?></a></li>
                            <?php } ?>
                        </ul>
                    </li>
                <?php } ?>
            </ul>
        </div>
    </nav>

    <?php if ($so == 'yes') { //$so is set in HMLOWebForm.php?>
        <?php if (strlen($dealSummary) >= 850) { //accordian?>
            <div class="card card-custom">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            Deal Summary
                        </h3>
                        <i class="fa fa-chevron-circle-down  tooltipClass text-primary" title="click to view more"
                           aria-hidden="true" id="showToggle"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div id="showLess">
                        <?php echo substr($dealSummary, 0, 850) . ' .....'; ?>
                    </div>
                    <div id="showAll" class="hide">
                        <?php echo $dealSummary; ?>
                    </div>
                </div>
            </div>
            <?php
        } else {
            echo '<h5>Deal Summary</h5><p><i>' . $dealSummary . '</i></p>';
        }
        ?>

        <ul class="nav nav-pills nav-fill nav-tabs">
            <?php
            $shareLinkSO = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
            $FAclass = 'nav-link active';
            //$SOclass = 'nav-link gr_wh';
            if (isset($_REQUEST['tab'])) {
                $shareLinkSO = substr($shareLinkSO, 0, -7);
                $FAclass = ($_REQUEST['tab'] == 'LI') ? ' nav-link active' : 'nav-link';
                $SOclass = ($_REQUEST['tab'] == 'SO') ? ' nav-link active' : 'nav-link';
                $tabOpt = ($_REQUEST['tab'] == 'SO') ? 'SO' : 'BI';
            }
            $qafa = cypher::myDecryption($_REQUEST['op']);
            if ($qafa == 'QA') {
                $tabName = 'Quick App';
            } elseif ($qafa == 'FA') {
                $tabName = 'Full App';
            }
            ?>
            <li class="nav-item">
                <a class="<?php echo $FAclass; ?>" href="<?php echo htmlspecialchars($shareLinkSO) . '&tab=LI'; ?>">
                    <span id="tab_FA" class="nav-text "><?php echo $tabName; ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="<?php echo $SOclass; ?>" href="<?php echo htmlspecialchars($shareLinkSO) . '&tab=SO'; ?>">
                    <span id="tab_SO" class="nav-text ">Submit Offer</span>
                </a>
            </li>
        </ul>
    <?php } ?>

    <div style="row  ">
        <div class="col-md-12 d-flex flex-row-reverse bd-highlight" >
            <?php if($LMRId) { ?>
            <div class="">
                <ul>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Open/Close All Sections"
                        data-action="open"
                        onclick="globalJS.toggleSections(this);">
                        <span class="nav-link px-2 loanFileButtonLink text-dark-65"
                              style='cursor:pointer'>
                           Open/Close All Sections
                            <i class="ml-2 ki ki-arrow-down icon-nm"></i>
                        </span>
                    </li>
                </ul>
            </div>
            <?php } ?>
    <?php if (count($docArray) > 0 && $so == 'no') { // $so = YES Hide for the Submit Offer links  ?>
            <div class=" <?php if (isset($_REQUEST['view'])) {
                echo 'd-none';
            } ?>">
                <div class="btn-group">
                    <a class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown" href="#">
                        Useful Documents
                        <span class="caret"></span>
                    </a>
                    <div class="dropdown-menu">
                        <?php
                        for ($doc = 0; $doc < count($docArray); $doc++) {
                            $tempDocArray = [];
                            $docName = '';
                            $displayDocName = '';
                            $docId = 0;
                            $docId = trim($docArray[$doc]['serialNumber']);
                            $docName = trim($docArray[$doc]['docName']);
                            $displayDocName = trim($docArray[$doc]['displayDocName']);
                            if (($displayDocName == '') || ($displayDocName == NULL)) {
                                $displayDocName = $docName;
                            }
//           $url = $branchDocUrl.$docName;
                            $url = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_FOLDER_BRANCH_DOC) . '&opt=enc';
                            $dispFS = 0;

                            ?>
                            <li class="dropdown-item">
                                <a target="_blank" href="<?php echo $url ?>"
                                   rel="nofollow"><?php echo $displayDocName ?></a>
                            </li>
                            <?php
                        }
                        ?>
                        <!-- dropdown menu links -->
                    </div>
                </div>
            </div>
    <?php } ?>
        </div>
    </div>

    <div id="divLoader" style="display:block;" class="col-md-12 p-0">
        <div class="progress-bar rounded-right progress-bar-striped progress-bar-animated p-2 " role="progressbar"
             style="width: 75%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
    <div id="warning"></div>
    <div id="warningLG"></div>
    <form name="loanModForm" id="loanModForm" class="loanModForm form signatureForm"
          method="POST" <?php echo $saveUrl; ?>
          enctype="multipart/form-data">
        <input type="hidden" id="pageLoad" value='No'/>
        <input type="hidden" id="branchId" value='<?php echo $executiveId; ?>'/>
        <input type="text" name="noFill" id="noFill" value=""  style="display: none;" >
        <input type="hidden" name="branchReferralCode" value="<?php echo cypher::myEncryption($branchReferralCode) ?>">
        <input type="hidden" name="agentReferralCode" value="<?php echo cypher::myEncryption($agentReferralCode) ?>">
        <input type="hidden" name="encryptedBId" value="<?php echo cypher::myEncryption($executiveId) ?>">
        <input type="hidden" id="encryptedPCID" name="encryptedPCID"
               value="<?php echo cypher::myEncryption($assignedPCID) ?>">
        <input type="hidden" name="executiveId" value="<?php echo $executiveId ?>">
        <input type="hidden" name="defaultAgentId" id="defaultAgentId"
               value="<?php echo cypher::myEncryption($agentNumber) ?>">

        <input type="hidden" name="dummyBrokerId" id="dummyBrokerId"
               value="<?php echo cypher::myEncryption($dummyBrokerId) ?>">
        <input type="hidden" name="brokerNumber" id="brokerNumber"
               value="<?php echo cypher::myEncryption($agentNumber) ?>">
        <input type="hidden" name="agentId" id="agentId" value="<?php echo cypher::myEncryption($agentNumber) ?>">

        <input type="hidden" name="loanofficerNumber" id="loanofficerNumber"
               value="<?php echo cypher::myEncryption($loanOfficerId) ?>">
        <input type="hidden" name="secondaryAgentId" id="secondaryAgentId"
               value="<?php echo cypher::myEncryption($loanOfficerId) ?>">

        <input type="hidden" name="fOpt" value="<?php echo cypher::myEncryption($fOpt) ?>">
        <input type="hidden" name="brokerChange" id="brokerChange" value="false">
        <input type="hidden" name="brokerExists" id="brokerExists" value="no">
        <input type="hidden" name="referralSiteCode" value="<?php echo $branchReferralCode ?>">
        <input type="hidden" name="FPCID" id="FPCID" value="<?php echo $processingCompanyId ?>">
        <input type="hidden" name="primaryStatus" id="primaryStatus" value="<?php echo $primeStatusId ?>">
        <input type="hidden" name="publicUser" id="publicUser" value="<?php echo $publicUser ?>">
        <input type="hidden" name="wfOpt" id="wfOpt" value="<?php echo cypher::myEncryption($wfOpt) ?>">
        <input type="hidden" name="fileTab" id="fileTab" value="<?php echo($wfOpt) ?>">
        <input type="hidden" name="tabOpt" id="tabOpt" value="<?php echo $tabOpt ?>">
        <input type="hidden" name="activeTab" id="activeTab" value="<?php echo $tabOpt ?>">
        <input type="hidden" name="lId" id="lId" value="<?php echo cypher::myEncryption($LMRId) ?>">
        <input type="hidden" name="rId" id="rId" value="<?php echo cypher::myEncryption($LMRResponseId) ?>">
        <input type="hidden" name="fileModule[]" id="fileModule" value="<?php echo $ft; ?>"/>
        <input type="hidden" name="webformName" id="webformName"
               value="<?php echo cypher::myEncryption($webformName) ?>"/>
        <input type="hidden" name="fileRecordDate" id="fileRecordDate" value="<?php echo $fileRecordDate; ?>"/>
        <input type="hidden" name="encryptedLId" id="encryptedLId" value="<?php echo cypher::myEncryption($LMRId) ?>">
        <input type="hidden" name="checkMandatoryFields" id="checkMandatoryFields"
               value="<?php echo $checkMandatoryFields; ?>"/>
        <input type="hidden" name="LMRId" id="LMRId" value="<?php echo $LMRId; ?>">
        <input type="hidden" name="emailOpt" id="emailOpt" value="<?php echo $emailOpt; ?>">
        <input type="hidden" name="allowToEdit" id="allowToEdit" value="<?php echo $allowToEdit; ?>">
        <input type="hidden" name="encryptedCId" id="encryptedCId"
               value="<?php echo cypher::myEncryption($clientId) ?>">
        <input type="hidden" name="allowFormSubmit" id="allowFormSubmit" value="0">
        <input type="hidden" name="source" id="source" value="1">
        <input type="hidden" name="hideBorrowerInfo" id="hideBorrowerInfo" value="<?php echo $hideBorrowerInfo; ?>">
        <input type="hidden" name="selClientId" id="selClientId" value="<?php echo $clientId; ?>">
        <input type="hidden" name="ft" id="ft" value="<?php echo $ft; ?>">
        <input type="hidden" name="validCaptcha" id="validCaptcha" value="<?php echo $allowCaptcha; ?>">
        <input type="hidden" name="originalClientId" value="<?php echo $clientId; ?>">
        <input type="hidden" name="hsFADisplay" value="<?php echo $hsFADisplay; ?>">
        <input type="hidden" name="hsQADisplay" value="<?php echo $hsQADisplay; ?>">
        <input type="hidden" name="formEsignSignature" id="formEsignSignature"
               value="<?php echo $formEsignSignature; ?>">
        <input type="hidden" name="formCoBroEsignSignature" id="formCoBroEsignSignature"
               value="<?php echo $formCoBroEsignSignature; ?>">
        <input type="hidden" name="UType" id="UType" value="<?php echo cypher::myEncryption($UType); ?>"/>
        <input type="hidden" name="selectedUpDoc" id="selectedUpDoc" value="0">
        <input type="hidden" name="totFileSize" id="totFileSize" value="0"/>
        <input type="hidden" name="selectedPkg" id="selectedPkg" value="0">
        <input type="hidden" name="esignSelectedPkg" id="esignSelectedPkg" value="0">
        <input type="hidden" name="customSelectedPkg" id="customSelectedPkg" value="0">
        <input type="hidden" name="selectedAttachedPkg" id="selectedAttachedPkg" value="0">
        <input type="hidden" name="PCSelectedPkg" id="PCSelectedPkg" value="0">
        <input type="hidden" name="binderSelectedPkg" id="binderSelectedPkg" value="0">
        <input type="hidden" name="aud" id="aud" value="<?php echo cypher::myEncryption($aud); ?>"/>
        <input type="hidden" name="bir" id="bir" value="<?php echo cypher::myEncryption($borInfoRed); ?>"/>
        <input type="hidden" name="webFormView" id="webFormView"
               value="<?php echo htmlspecialchars(cypher::myDecryption(Request::GetClean('view'))); ?>"/>
        <input type="hidden" name="isHMLOOpt" id="isHMLOOpt" value="<?php echo $isHMLO; ?>">
        <input type="hidden" id="idNo" name="idNo">
        <input type="hidden" id="submitStatus" value="0">


        <?php require 'LMSuggestedDD.php';
        if ($tabOpt == 'BI') {
            if (isset($_REQUEST['view'])) {
                if (cypher::myDecryption($_REQUEST['view']) == 'wizard') {
                    require 'HMLOLoanInfoFormWizard.php';
                }
            } else {
                require 'HMLOLoanInfoForm.php';
            }
        }
        if ($tabOpt == 'SO') require 'submitOfferForm.php';
        if ($tabOpt == '1003') {
            require 'webForm1003.php';
            require 'HMLO1003InfoForm.php';
        }
        //if($tabOpt == 'CL') include "requiredDocsForQuickApp.php";
        ?>
    </form>
    <?php
    if (isset($_REQUEST['pdf']) && $processingCompanyId != glPCID::PCID_PROD_CV3) {
        echo "Submitted " . Dates::StandardDateTime() . " (EST)";
    }
    ?>
</div><!-- tab Div -->

<style>
    #myBtn {
        display: none;
        position: fixed;
        bottom: 60px;
        right: 6px;
        z-index: 99;
        font-size: 10px;
        /* border: none; */
        outline: none;
        color: white;
        cursor: pointer;
        padding: 4px;
    }

    #myBtnBottom {
        position: fixed;
        bottom: 20px;
        right: 6px;
        z-index: 99;
        font-size: 10px;
        /* border: none; */
        outline: none;
        color: white;
        cursor: pointer;
        padding: 4px;
    }
</style>
<a href="javascript:void(0);" id="myBtn"
   class=" btn  btn-primary btn-text-primary  btn-icon toggleClass scrollupClass">
    <i class="flaticon2-arrow-up"></i>
</a>
<a href="#endPage" id="myBtnBottom"
   class=" btn  btn-primary btn-text-primary  btn-icon toggleClass">
    <i class="flaticon2-arrow-down"></i>
</a>

<script>
    const UType = "<?php echo $UType; ?>";
    $(window).scroll(function () {
        if ($(this).scrollTop() > 400) {
            $('.scrollupClass').fadeIn();
        } else {
            $('.scrollupClass').fadeOut();
        }
    });
    $('.scrollupClass').click(function () {
        $("html,body").animate({
            scrollTop: 0
        }, 2000);
        return false;
    });

    $("#myBtnBottom").on('click', function (event) {

        // Make sure this.hash has a value before overriding default behavior
        if (this.hash !== "") {
            // Prevent default anchor click behavior
            event.preventDefault();

            // Store hash
            let hash = this.hash;

            // Using jQuery's animate() method to add smooth page scroll
            // The optional number (800) specifies the number of milliseconds it takes to scroll to the specified area
            $('html, body').animate({
                scrollTop: $(hash).offset().top
            }, 2000, function () {
                // Add hash (#) to URL when done scrolling (default click behavior)
                //  window.location.hash = hash;
            });
        } // End if
    });

    let isWebformMultiStep = parseInt('<?php echo isset($_REQUEST['view']) && cypher::myDecryption($_REQUEST['view']) == 'wizard'; ?>');
    let isCaptchaEnabled = parseInt('<?php echo (!isset($_REQUEST['view']) && !RECAPTCHA_DISABLE) ; ?>');
    let _loanModForm = $('#loanModForm');
    let _soWebForm = $('#soWebForm');

    /* validate the Submit Offer Web Form */
    if (_soWebForm.length
        && parseInt(_soWebForm.val()) === 1) {
        // Masking and unmasking before submit
        //$("#offerPoint").inputmask('9[9].999');
        //$("#offerRate").inputmask('9[9].999');
        $("#offerDirectPhone").inputmask('999 - 999 - 9999');
        $("#offerAltPhone").inputmask('999 - 999 - 9999');
        //validate & Submit Offer Web Form
        _loanModForm.submit(function (e) {
            e.preventDefault();
            /* validate the Submit Offer Web Form */
            if ($("#loanModForm").valid()) { // continue form submit / validations
                //return true;
            } else { // throw error
                return false;
            }
            /* validate the Submit Offer Web Form */
            document.getElementById("loanModForm").submit();
            return true;
        });
    } else {
        _loanModForm.submit(function (e) {
        //_loanModForm.on('submit',function(e){
            console.log({
                func: 'Submitted From HMLOModuleForm'
            });
            let noFillValue = $('#noFill').val();
            if (noFillValue) {
                toastrNotification('Invalid Data', 'error');
                return false;
            }
            let _submitStatus = $('#submitStatus');
            if(parseInt(_submitStatus.val()) === 1 && $('#submitType').val() !== "Save & Submit" ) {
                e.preventDefault();
                return false;
            }
            _submitStatus.val('1');
            let _btnSave = $('#btnSave');
            let PCID = "<?php echo $processingCompanyId; ?>";
            let APICheckPC = <?php echo json_encode(CONST_API_ACCESS_PC); ?>;

            //HMDA (Borrower) Validate child options
            //hmdaBorValidation();

            //HMDA (Co-Borrower) Validate child options
            //hmdaCoBorValidation();

            //validate the Credit Memo Section
            let _memoDescription = $('#memoDescription_1');
            if (_memoDescription.length) { // check desc
                if (_memoDescription.val() !== '' && $('.memoCategory').length) {
                    $(".memoCategory option:selected").each(function () {
                        if ($(this).val() === '') {
                            _submitStatus.val('0');
                            let selCatMsg = "Please select a category";
                            toastrNotification(selCatMsg, 'error', 1500);
                            $([document.documentElement, document.body]).animate({
                                scrollTop: $(".memoCategory").offset().top
                            }, 500);
                            return false;
                        }
                    });
                }
            }

            //validate the Credit Memo Section
            if (toggleClosedCard(PCID) && fieldLevelValidation()) {
                let apiresult = true;
                if ($.inArray(PCID, APICheckPC) >= 0) {
                    $.ajax({
                        url: "/JQFiles/getIDVerifyData.php",
                        method: "POST",
                        async: false,
                        data: jQuery.param({
                            'borrowerFName': $('#borrowerFName').val(),
                            'borrowerLName': $('#borrowerLName').val(),
                            'streetName': $('#streetName').val(),
                            'streetNumber': $('#streetNumber').val(),
                            'streetType': $('#streetType').val(),
                            'city': $('#presentCity').val(),
                            'state': $('#presentState').val(),
                            'zipcode': $('#presentZip').val(),
                            'cellNo': $('#cellNo').val().replace(/\ - /g, '').trim(),
                            'ssn': $('#ssn').val().replace(/\ - /g, '').trim(),
                            'borrowerDOB': $('#borrowerDOB').val()
                        }),
                        success: function (myData) {
                            if (myData != 1) {
                                apiresult = false;
                                toastrNotification(myData, 'error');
                                return false;
                            } else {
                                apiresult = true;
                            }
                        }
                    });
                }
                if (apiresult) {
                    //$('#formEsignSignature').val('');
                    //$('#formCoBroEsignSignature').val('');
                    _btnSave.prop('disabled', true);
                    let isEsignRequired = parseInt("<?php echo $isEsignRequired; ?>");
                    let UType = "<?php echo $UType; ?>";
                    let tacField = parseInt("<?php echo $tacField; ?>");

                    let _wizardForm = $('#wizardForm');
                    if (_wizardForm.length
                        && _wizardForm.val()
                        && parseInt($('#currentWizardPage').val()) === 1) { // check if wizard form
                            return false;
                    }
                    if (isEsignRequired === 1) {
                        if ($('#content').is(':visible')) {
                            /* validate the esign */
                            if (trim($('#signaturedPersonName:enabled').val()) === '') {
                                _submitStatus.val('0');
                                toastrNotification("Please enter the Name of Borrower signing", 'error', '', '', 'toast-bottom-center');
                                return false;
                            }
                            let borrowerSignatureElement = $("#signatureparent");
                            if (borrowerSignatureElement.jSignature('getData', 'native').length === 0) {
                                $('#formEsignSignature').val('');
                                _submitStatus.val('0');
                                toastrNotification("Please Sign the form", 'error', '', '', 'toast-bottom-center');
                                return false;
                            } else {
                                let borrowerSignatureData = borrowerSignatureElement.jSignature("getData", "default");
                                $('#formEsignSignature').val(borrowerSignatureData);
                            }
                        }
                        if ($('#contentCoBorrower').is(':visible')) {
                            /* validate the esign */
                            if (trim($('#coBroSignaturedPersonName:enabled').val()) === '' && UType === 'CoBorrower') {
                                _submitStatus.val('0');
                                toastrNotification("Please enter the Name of CoBorrower signing", 'error', '', '', 'toast-bottom-center');
                                return false;
                            }
                            let coBorrowerSignatureElement = $("#signatureparentCoBorrower");
                            if (coBorrowerSignatureElement.jSignature('getData', 'native').length === 0 && UType === 'CoBorrower') {
                                $('#formCoBroEsignSignature').val('');
                                _submitStatus.val('0');
                                toastrNotification("Please Sign the form", 'error', '', '', 'toast-bottom-center');
                                return false;
                            }
                            if (coBorrowerSignatureElement.jSignature('getData', 'native').length) {
                                let coBroImgData = coBorrowerSignatureElement.jSignature("getData", "default");
                                $('#formCoBroEsignSignature').val(coBroImgData);
                            }
                        }
                    }
                    if (!(_wizardForm.length > 0) && !webForm.checkBoxValidation()) {
                        return false;
                    }
                    /* reCaptcha Javascript Validation (client side)*/

                    if (isCaptchaEnabled) {
                        let checkCaptcha = $('#validCaptcha').val();
                        if (parseInt(checkCaptcha) === 1) { //check if captcha validation is required
                            let response = grecaptcha.getResponse();
                            if (parseInt(response.length) === 0) {
                                //reCaptcha not verified
                                $('#human_valid').removeClass('hidden');
                                _btnSave.prop('disabled', false);
                                _submitStatus.val('0');
                                return false;
                            } else {
                                //hide note
                                $('#human_valid').addClass('hidden');
                            }
                            //captcha verified
                            //do the rest of your validations here // form submit
                        }
                     }
                    /* reCaptcha Javascript Validation (client side)*/

                   // return false;
                    if(isWebformMultiStep) {
                        let ajaxUrl = _loanModForm.attr('action');
                        //var formData = formIdToSubmit.serialize();
                        let formData = new FormData(_loanModForm[0]);
                            _submitStatus.val('1');
                        $.ajax({
                            url: ajaxUrl,
                            type: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            beforeSend: function () {
                                BlockDiv('kt_wizard');
                            },
                            complete: function () {
                                UnBlockDiv('kt_wizard');
                            },
                            success: function (response, status, xhr) {
                                //response = JSON.parse(response);
                                if (typeof response.propertiesInserted !== "undefined") {
                                    $.each(response.propertiesInserted, function (propertyIndex, propertyId) {
                                        $('#propertyId_' + propertyIndex).val(propertyId);
                                    });
                                }
                                if (typeof response.propertiesRentrollInserted !== "undefined") {
                                    $.each(response.propertiesRentrollInserted.properties, function (propertyIndex, rentRollObj) {
                                        $.each(rentRollObj.rentRoll, function (rentRollIndex, rentRollValue) {
                                            $('#properties_' + propertyIndex + '_rentRoll_' + rentRollIndex + '_id').val(rentRollValue.id);
                                            $('#properties_' + propertyIndex + '_rentRoll_' + rentRollIndex + '_propertyId').val(rentRollValue.propertyId);
                                        });
                                    });
                                }

                                if (typeof response.propertiesFloodCertificatesInserted !== "undefined") {
                                    $.each(response.propertiesFloodCertificatesInserted.properties, function (propertyIndex, floodCertificatesObj) {
                                        $.each(floodCertificatesObj.floodCertificates, function (floodCertificateIndex, floodCertificate) {
                                            $('#properties_' + propertyIndex + '_floodCertificates_' + floodCertificateIndex + '_id').val(floodCertificate.id);
                                        });
                                    });
                                }
                                if (typeof response.propertiesAppraisersInserted !== "undefined") {
                                    $.each(response.propertiesAppraisersInserted.properties, function (propertyIndex, appraisersObj) {
                                        $.each(appraisersObj.floodCertificates, function (appraiserIndex, appraiser) {
                                            $('#properties_' + propertyIndex + '_appraiser_' + appraiserIndex + '_id').val(appraiser.id);
                                        });
                                    });
                                }
                                if (typeof response.propertiesHOAInserted !== "undefined") {
                                    $.each(response.propertiesHOAInserted.properties, function (propertyIndex, HOAObj) {
                                        $.each(HOAObj, function (HOAIndex, HOAInfo) {
                                            $('#properties_' + HOAIndex + '_primaryHOAId').val(HOAInfo.primaryHOAId);
                                            $('#properties_' + HOAIndex + '_secondaryHOAId').val(HOAInfo.secondaryHOAId);
                                        });
                                    });
                                }
                                if (typeof response.propertiesAppraisersInserted !== "undefined") {
                                    $.each(response.propertiesAppraisersInserted.properties, function (propertyIndex, appraisersObj) {
                                        $.each(appraisersObj.appraiser, function (appraiserIndex, appraiser) {
                                            $('#properties_' + propertyIndex + '_appraiser_' + appraiserIndex + '_id').val(appraiser.id);
                                            $('#properties_' + propertyIndex + '_appraiser_' + appraiserIndex + '_appraiserId').val(appraiser.appraiserId);
                                        });
                                    });
                                }
                                if (typeof response.members !== "undefined") {
                                    $.each(response.members, function (memberId, memberVal) {
                                        $('#memberId' + memberId).val(memberVal);
                                    });
                                }
                                if (typeof response.insuranceAgentId !== "undefined") {
                                    $.each(response.insuranceAgentId, function (insuranceAgentKey, insuranceAgentVal) {
                                        $('#insuranceCompanyID_' + insuranceAgentKey).val(insuranceAgentVal);
                                    });
                                }
                                if (typeof response.generalContractorID !== "undefined") {
                                    $('#generalContractorID').val(response.generalContractorID);
                                }
                                if (typeof response.scheduleRealStateInserted !== "undefined") {
                                    $.each(response.scheduleRealStateInserted, function (propertyIndex, propertyId) {
                                        $('#scheduleID_' + propertyIndex).val(propertyId);
                                    });
                                }
                                if (parseInt(response.code) === 100) {
                                    if (typeof response.redirect !== "undefined") {
                                        if (response.redirect !== '') {
                                            setTimeout(function () {
                                                window.location.href = atob(response.redirect);
                                            }, 100);
                                        }
                                    } else {
                                        $("body").animate({scrollTop: 0});
                                    }
                                } else {
                                    toastrNotification(response.msg, 'error');
                                }
                            },
                            error: function (jqXhr, textStatus, errorMessage) {
                                toastrNotification(errorMessage, 'error');
                            }
                        });
                        return false;
                    } else {
                        _submitStatus.val('1');
                       // document.getElementById("loanModForm").submit();
                        BlockDiv('loanModForm');
                        return true;
                    }
                }
            } else {
                _submitStatus.val('0');
                _btnSave.prop('disabled', false);
                return false;
            }
        });
    }

    //
    $('#showToggle').click(function () {
        if ($('#showAll').is(':visible')) {
            $('#showLess').removeClass('hide');
            $('#showAll').addClass('hide');
        } else {
            $('#showLess').addClass('hide');
            $('#showAll').removeClass('hide');
        }
    });
</script>
<div id="endPage"></div>

<!-- HMLOModuleForm.php -->
