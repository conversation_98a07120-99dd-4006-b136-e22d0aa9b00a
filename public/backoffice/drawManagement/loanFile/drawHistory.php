
<?php
    use models\composite\oDrawManagement\DrawRequestsHistory;
    use models\composite\oDrawManagement\SowTemplateManager;

    $drawRequest = $drawRequestManager->getDrawRequest();
    $isDrawRequest = $drawRequest->isDrawRequest;
    $drawRequestHistory = $drawRequestManager->getDrawRequestHistory();
    $initialSow = $drawRequestManager->getInitialScopeOfWork();
    $sowCount = 0;
    $sowID = 0;
    $userType = SowTemplateManager::$userType;
?>
<?php if (empty($drawRequestHistory)) return; ?>

<div class="card card-custom card-stretch d-flex p-0 drawHistoryCard">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Draw Request History
            </h3>
        </div>
        <div class="card-toolbar">
            <a href="javascript:void(0);"
                class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                data-card-tool="toggle" data-section="drawHistoryCard" data-toggle="tooltip" data-placement="top"
                title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body p-2">
        <div class="row">
            <div class="col-12">
                <div class="work-table history-table">
                    <table class="table table-striped table-hover mb-0">
                        <thead>
                            <tr>
                                <th>&emsp;&emsp;Status</th>
                                <th>Type</th>
                                <th>Submission Date</th>
                                <th>Amount Requested</th>
                                <th>Amount Approved</th>
                                <th>Rehab% Financed</th>
                                <th>Draw Fee</th>
                                <th>Wire Amount</th>
                                <th>Wire Sent Date</th>
                                <th style="width: 50px"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($drawRequestHistory as $idx => $history): ?>
                            <?php
                                $showInputs = $history->status != DrawRequestsHistory::STATUS_APPROVED && $history->isDrawRequest && $userType == SowTemplateManager::USER_LENDER;
                                $isInitialSow = $history->id === $initialSow->id;
                                $showAmounts = $history->isDrawRequest || $isInitialSow;
                            ?>
                            <tr <?php if ($showInputs) echo 'data-history-id="' . $history->id . '"'; ?>>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    $statusText = ucfirst($history->status);
                                    switch($history->status) {
                                        case DrawRequestsHistory::STATUS_PENDING:
                                            $statusClass = 'badge-warning';
                                            $statusText = 'Submitted';
                                            break;
                                        case DrawRequestsHistory::STATUS_APPROVED:
                                            $statusClass = 'badge-success';
                                            $statusText = 'Approved';
                                            break;
                                        case DrawRequestsHistory::STATUS_REJECTED:
                                            $statusClass = 'badge-danger';
                                            $statusText = 'Rejected';
                                            break;
                                    }
                                    ?>
                                    <span class="badge badge-light-primary"><?= ++$idx; ?></span>
                                    <span class="badge <?= $statusClass; ?>"><?= $statusText; ?></span>
                                </td>
                                <td>
                                    <?php if($history->isDrawRequest) { ?>
                                        <span class="badge badge-primary">Draw Request</span>
                                    <?php } else { ?>
                                        <span class="badge badge-info"><?= $isInitialSow ? 'Initial SOW' : 'Revision'; ?></span>
                                    <?php } ?>
                                </td>
                                <td><?= $history->submittedAt ? date('m/d/Y', strtotime($history->submittedAt)) : '-'; ?></td>
                                <td>
                                    <?= $showAmounts &&$history->requestedAmount ? '$' . number_format($history->requestedAmount, 2) : '-'; ?>
                                </td>
                                <td class="<?= $showInputs ? 'col-approved-amount' : ''; ?>">
                                    <?= $showAmounts  && $history->approvedAmount ? '$' . number_format($history->approvedAmount, 2) : '-'; ?>
                                </td>
                                <td>
                                    <?php if($showInputs): ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">%</span>
                                            </div>
                                            <input type="number"
                                                class="form-control"
                                                name="rehabPercentFinanced"
                                                data-field="rehabPercentFinanced"
                                                min="0"
                                                value="<?= $history->rehabPercentFinanced; ?>"
                                                placeholder="0.00">
                                        </div>
                                    <?php else: ?>
                                        <?= $history->rehabPercentFinanced ? number_format($history->rehabPercentFinanced, 2) . '%' : '-'; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($showInputs): ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                class="form-control"
                                                name="drawFee"
                                                data-field="drawFee"
                                                min="0"
                                                value="<?= $history->drawFee; ?>"
                                                placeholder="0.00">
                                        </div>
                                    <?php else: ?>
                                        <?= $history->drawFee ? '$' . number_format($history->drawFee, 2) : '-'; ?>
                                    <?php endif; ?>
                                </td>
                                <td class="<?= $showInputs ? 'col-wire-amount' : ''; ?>">
                                    <?= $history->wireAmount ? '$' . number_format($history->wireAmount, 2) : '-'; ?>
                                </td>
                                <td>
                                    <?php if ($history->wireSentDate): ?>
                                        <?= date('M j, Y', strtotime($history->wireSentDate)); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($history->status === DrawRequestsHistory::STATUS_APPROVED): ?>
                                        <a href="javascript:void(0);"
                                        class="btn btn-icon btn-light-primary btn-sm text-primary view-details"
                                        title="View Details"
                                        data-toggle="modal"
                                        data-target="#drawHistoryDetailsModal_<?= $history->id; ?>">
                                            <i class="fas fa-eye fa-lg"></i>
                                        </a>
                                    <?php elseif (!$isInitialSow): ?>
                                        <button type="submit" name="btnCancel" id="btnCancel" class="btn btn-warning btn-sm" tabindex="-1"
                                        data-type="<?= $history->isDrawRequest ? 'drawrequest' : 'revision';?>">Cancel</button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Draw History Details Modals -->
<?php foreach ($drawRequestHistory as $history): ?>
    <?php
        $statusClass = '';
        $statusText = ucfirst($history->status);
        switch($history->status) {
            case DrawRequestsHistory::STATUS_PENDING:
                $statusClass = 'badge-warning';
                $statusText = 'Submitted';
                break;
            case DrawRequestsHistory::STATUS_APPROVED:
                $statusClass = 'badge-success';
                $statusText = 'Approved';
                break;
            case DrawRequestsHistory::STATUS_REJECTED:
                $statusClass = 'badge-danger';
                $statusText = 'Rejected';
                break;
        }
    ?>
    <div class="modal fade" id="drawHistoryDetailsModal_<?= $history->id; ?>" tabindex="-1" role="dialog" aria-labelledby="drawHistoryDetailsModalLabel_<?= $history->id; ?>" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style="max-width: 90vw; height: 90vh;">
            <div class="modal-content" style="height: 100%;">
                <div class="modal-header">
                    <h5 class="modal-title" id="drawHistoryDetailsModalLabel_<?= $history->id; ?>">Draw Request Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" style="overflow-y: auto; padding: 20px;">
                    <div class="draw-history-details">
                        <!-- Header Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Draw Information</h6>
                                        <p><strong>Status:</strong> <span class="badge <?= $statusClass; ?>"><?= $statusText; ?></span></p>
                                        <p><strong>Type:</strong> <?= $history->isDrawRequest ? '<span class="badge badge-primary">Draw Request</span>' : '<span class="badge badge-info">' . ($history->id === $initialSow->id ? 'Initial SOW' : 'Revision') . '</span>'; ?></p>
                                        <p><strong>Submission Date:</strong> <?= $history->submittedAt ? date('M j, Y g:i A', strtotime($history->submittedAt)) : '-'; ?></p>
                                        <p><strong>Amount Requested:</strong> $<?= number_format($history->requestedAmount, 2); ?></p>
                                        <?php if ($history->status === DrawRequestsHistory::STATUS_APPROVED && $history->approvedAmount > 0): ?>
                                            <p><strong>Amount Approved:</strong> $<?= number_format($history->approvedAmount, 2); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                            $lineItemsHistoryData = $drawRequestManager->getDrawRequestLineItemsHistory($history->id);
                        ?>
                        <!-- Line Items Table -->
                        <?php if (!empty($lineItemsHistoryData)): ?>
                            <div class="table-responsive card">
                                <table class="table table-bordered line-item-table">
                                    <thead class="thead-light">
                                        <colgroup>
                                            <col style="width:18%">
                                            <col style="width:12%">
                                            <col style="width:12%">
                                            <col style="width:12%">
                                            <col style="width:8%">
                                            <col style="width:12%">
                                            <col style="width:13%">
                                            <col style="width:13%">
                                        </colgroup>
                                        <tr>
                                            <th scope="col">Line Item</th>
                                            <th scope="col">Total Budget</th>
                                            <th scope="col">Completed Renovations</th>
                                            <th scope="col">Disbursed Amount</th>
                                            <th scope="col">% Completed</th>
                                            <th scope="col">Requested Amount</th>
                                            <th scope="col">Borrower Notes</th>
                                            <th scope="col">Lender Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                            $costTotal = 0;
                                            $completedAmountTotal = 0;
                                            $disbursedAmountTotal = 0;
                                            $requestedAmountTotal = 0;
                                            ?>
                                        <?php foreach ($lineItemsHistoryData as $category): ?>
                                            <?php if (!empty($category['lineItems'])): ?>
                                                <!-- Category Header -->
                                                <tr class="category-header">
                                                    <td colspan="8">
                                                        <strong><?= htmlspecialchars(strtoupper($category['categoryName'])) ?></strong>
                                                    </td>
                                                </tr>
                                                <!-- Line Items -->
                                                <?php foreach ($category['lineItems'] as $lineItem): ?>
                                                    <?php
                                                        $costTotal += $lineItem['cost'];
                                                        $completedAmountTotal += $lineItem['completedAmount'];
                                                        $disbursedAmountTotal += $lineItem['disbursedAmount'];
                                                        $requestedAmountTotal += $lineItem['requestedAmount'];
                                                    ?>
                                                    <tr class="line-item">
                                                        <td>
                                                            <?= htmlspecialchars($lineItem['name']) ?>
                                                        </td>
                                                        <td>$<?= number_format($lineItem['cost'], 2) ?></td>
                                                        <td>$<?= number_format($lineItem['completedAmount'], 2) ?></td>
                                                        <td>$<?= number_format($lineItem['disbursedAmount'], 2) ?></td>
                                                        <td>
                                                            <span class="badge percentage"><?= round($lineItem['completedPercent']) ?>%</span>
                                                        </td>
                                                        <td>$<?= number_format($lineItem['requestedAmount'], 2) ?></td>
                                                        <td class="notes-column">
                                                            <div class="notes-content">
                                                                <?= !empty($lineItem['notes']) ? htmlspecialchars($lineItem['notes']) : '<span class="text-muted">-</span>' ?>
                                                            </div>
                                                        </td>
                                                        <td class="notes-column">
                                                            <div class="notes-content">
                                                                <?php
                                                                $lenderNotes = '';
                                                                if (!empty($lineItem['lenderNotes'])) {
                                                                    $lenderNotes .= $lineItem['lenderNotes'];
                                                                }
                                                                if (!empty($lineItem['rejectReason'])) {
                                                                    if (!empty($lenderNotes)) $lenderNotes .= ' | ';
                                                                    $lenderNotes .= 'Reject Reason: ' . $lineItem['rejectReason'];
                                                                }
                                                                echo !empty($lenderNotes) ? htmlspecialchars($lenderNotes) : '<span class="text-muted">-</span>';
                                                                ?>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                        <!-- Category Totals Row -->
                                        <tr class="category-totals" style="background-color: #f8f9fa; font-weight: bold;">
                                            <td>
                                                <strong>Totals</strong>
                                            </td>
                                            <td>$<?= number_format($costTotal, 2) ?></td>
                                            <td>$<?= number_format($completedAmountTotal, 2) ?></td>
                                            <td>$<?= number_format($disbursedAmountTotal, 2) ?></td>
                                            <td>
                                                <span class="badge percentage"><?= $costTotal > 0 ? round($completedAmountTotal / $costTotal * 100) : 0 ?>%</span>
                                            </td>
                                            <td>$<?= number_format($requestedAmountTotal, 2) ?></td>
                                            <td class="text-muted"></td>
                                            <td class="text-muted"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle"></i>
                                No line items data available for this draw request.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary export-history-btn" data-history-id="<?= $history->id; ?>">
                        <i class="fas fa-download mr-2 fa-sm"></i>Export Table
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>

<script>
$(document).ready(function() {
    $('.export-history-btn').on('click', function(e) {
        e.preventDefault();
        const historyId = $(this).data('history-id');
        const $modal = $(this).closest('.modal');
        exportHistoryTableToPdf(historyId, $modal);
    });
});

function exportHistoryTableToPdf(historyId, $modal) {
    const $exportBtn = $modal.find('.export-history-btn');
    const originalText = $exportBtn.html();

    $exportBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');

    const $modalBody = $modal.find('.modal-body');
    const modalHtml = $modalBody.html();

    const postData = {
        LMRId: <?= $drawRequest->LMRId; ?>,
        exportType: 'history',
        historyId: historyId,
        modalContent: modalHtml
    };

    drawManagementApi.exportToPdf(postData)
        .then(function(response) {
            if (response.success) {
                const byteCharacters = atob(response.data.pdf_data);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], {type: 'application/pdf'});

                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = response.data.filename || 'draw_history_export.pdf';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                toastrNotification('PDF exported successfully!', 'success');
            } else {
                toastrNotification(response.message || 'Failed to export PDF', 'error');
            }
        })
        .catch(function(error) {
            console.error('PDF Export Error:', error);
            let errorMsg = error.message || 'An error occurred while generating the PDF. Please try again.';
            toastrNotification(errorMsg, 'error');
        })
        .always(function() {
            $exportBtn.prop('disabled', false).html(originalText);
        });
}
</script>
