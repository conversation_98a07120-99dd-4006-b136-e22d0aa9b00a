<?php
global $glLMRClientTypeArray, $cls;

use models\composite\oChecklist\requiredDocsForFile;
use models\constants\docStatusArray;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFUModulesNotesTypeArray;
use models\constants\gl\glUserRole;
use models\Controllers\backoffice\DocsForm;
use models\Controllers\backoffice\DocsForm\requiredDocsForFileRow;
use models\Controllers\Base\generateWebformLinks;
use models\cypher;
use models\lendingwise\tblPCChecklistCategory;
use models\PageVariables;
use models\standard\HTTP;
use models\standard\Strings;

$disp = 0;


generateWebformLinks::$fileInfo = [
    DocsForm::$LMRId => DocsForm::$myFileInfo,
];
generateWebformLinks::init(DocsForm::$LMRId);

foreach (DocsForm::$moduleAndServiceRequiredDocs as $mSTArray) {

    /* @var $mSTArray requiredDocsForFile[] */

    if (!sizeof($mSTArray)) {
        continue;
    }

    usort($mSTArray, function ($a, $b) {
        return $a->displayOrder - $b->displayOrder;
    });

    $chk = 0;
    $mSTArray = requiredDocsForFile::docAdditionalLogic(
        DocsForm::$additionalLogicResult,
        DocsForm::$requiredAdditionalCond,
        $mSTArray
    );

    $docs = array_keys($mSTArray);
    $doc = $docs[0];
    $chST = $mSTArray[$doc]->serviceType;
    $chMT = $mSTArray[$doc]->moduleType;
    $moduleName = DocsForm::$fileModulesWithName[$chMT] ?? '';
    ?>

    <div class="card card-custom fileChecklistForm_<?php echo $doc; ?>">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    <?php
                    if (PageVariables::$userRole == 'Client') {
                        echo 'Loan Program - ' . $glLMRClientTypeArray[$chST];
                    } else {
                        echo $moduleName;
                        if ($moduleName == '') {
                            echo DocsForm::$glModulesArray[$chMT] . '*';
                        }
                        if (array_key_exists($chST, $glLMRClientTypeArray)) {
                            echo ' - ' . $glLMRClientTypeArray[$chST];
                        }
                    }
                    ?>
                </h3>
            </div>
            <div class="card-toolbar ">

                <?php if (PageVariables::$userRole == 'Manager' || PageVariables::$userRole == 'Super' || PageVariables::$allowToAccessDocs == 1) { ?>
                    <span class="tooltipAjax" data-placement="left" data-toggle="tooltip"
                          data-html="true" title=""
                          data-original-title="Create new required doc for this file">
                                                <a class="btn  btn-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass <?php if (DocsForm::$op == 'view') {
                                                    echo 'd-none';
                                                } ?>"
                                                   data-href="<?php echo CONST_URL_POPS; ?>addChecklistItem.php"
                                                   data-id="PCID=<?php echo cypher::myEncryption(DocsForm::$PCID) ?>&MC=<?php echo cypher::myEncryption($chMT) ?>&searchModuleType=<?php echo cypher::myEncryption($chMT) ?>&userGroup=<?php echo cypher::myEncryption(DocsForm::$userGroup) ?>&userNumber=<?php echo cypher::myEncryption(PageVariables::$userNumber) ?>&selServiceType=<?php echo cypher::myEncryption($chST) ?>&LMRId=<?php echo cypher::myEncryption(DocsForm::$LMRId) ?>&addOpt=FCL&tabOpt=DOC&showSaveBtn=1"
                                                   data-name="Add New Required Docs > <?php echo HTTP::escapeQuoteForPOST(htmlentities(DocsForm::$borrowerName)) ?>"
                                                   href="#" data-wsize='modal-xl'
                                                   data-toggle='modal' data-target='#exampleModal1'>
                                                   <i class=" icon-md fas fa-plus "></i>
                                                </a>
                                            </span>
                <?php } ?>


                <span
                        class="cursor-pointer tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                        data-card-tool="toggle"
                        data-section="fileChecklistForm_<?php echo $doc; ?>"
                        data-toggle="tooltip" data-placement="top" title=""
                        data-original-title="Toggle Card">
                                            <i class="ki ki-arrow-down icon-nm"></i>
                                        </span>

            </div>
        </div>
        <div class="card-body fileChecklistForm_<?php echo $doc; ?>_body p-2">
            <div class="row ">
                <div class="col-3 fileChecklistFormFilter text-left ">

                </div>
                <div class="col-3">
                    <?php $tblPCChecklistCategory = tblPCChecklistCategory::getPCCategories(DocsForm::$PCID); ?>
                    <select class="form-control form-control-lg bg-gray-300 chzn-selectShowSelectAll ml-4"
                            title="Search By Category Name"
                            id="categoryFilter"
                            multiple=""
                            data-live-search="true"
                            data-size="5"
                            data-actions-box="true"
                            style="width: 25%">
                        <?php foreach ($tblPCChecklistCategory as $category): ?>
                            <option value="<?php echo $category->categoryName; ?>">
                                <?php echo $category->categoryName; ?>
                            </option>
                        <?php endforeach; ?>
                        <option value="na">n/a</option>
                    </select>
                </div>
                <div class="col-6 text-right">
                    <?php if ((DocsForm::$userGroup == 'Employee' || DocsForm::$userGroup == 'Super' || DocsForm::$userGroup == 'Branch' || DocsForm::$userGroup == 'Agent') && $disp == 0) {
                        $disp = 1                           // | Send Request upload docs. ?>
                        <?php if (DocsForm::$showDeactiveDocsButton) { ?>
                            <div id="showNotReqDoc"
                                 class="tooltipClass btn btn-sm btn-secondary btn-hover-primary mr-2 mb-2">
                                                    <span class="showHideTxt tooltipClass"
                                                          title="Show/Hide Deactivated Docs">Show Deactivated Docs</span>
                                <i class="fa fa-eye fa-lg"></i>
                            </div>
                        <?php } ?>
                        <?php if (glCustomJobForProcessingCompany::hideSendDocumentUploadRequest(DocsForm::$PCID)) { ?>
                            <a data-href="<?php echo CONST_URL_POPS; ?>uploadRequiredDocs.php"
                               class="tooltipClass btn btn-sm btn-secondary btn-hover-primary mr-2 mb-2 <?php if (DocsForm::$op == 'view') {
                                   echo 'd-none';
                               } ?>"
                               data-id='rId=<?php echo cypher::myEncryption(DocsForm::$LMRResponseId) ?>&exID=<?php echo cypher::myEncryption(DocsForm::$executiveId) ?>&LMRId=<?php echo cypher::myEncryption(DocsForm::$LMRId) ?>&PCID=<?php echo cypher::myEncryption(DocsForm::$PCID) ?>&userType=<?php echo cypher::myEncryption(DocsForm::$userGroup) ?>&userNumber=<?php echo cypher::myEncryption(PageVariables::$userNumber) ?>&tabOpt=DOC&showSaveBtn=1'
                               data-wsize='modal-xl' href="#"
                               data-toggle='modal' data-target='#exampleModal1'
                               data-name="File: <?php echo htmlentities(DocsForm::$borrowerName); ?>  > Send Document Upload Request">Send
                                Document Upload Request <i class="far fa-envelope"></i></a>
                        <?php } ?>
                        <span id="docportalButton">
                            <a target="_blank"
                               href="<?php echo glCustomJobForProcessingCompany::customDocPortalLink(DocsForm::$PCID) ? generateWebformLinks::$requiredDocsBorrowerBrokerLogo : generateWebformLinks::$requiredDocsBorrower; ?>"
                               class="tooltipClass btn btn-sm btn-secondary btn-hover-primary mr-2 mb-2 <?php if (DocsForm::$op == 'view') {
                                   echo 'd-none';
                               } ?>">Loan Status/DocPortal <i class="far fa-file"></i></a>
                        </span>
                    <?php } ?>
                </div>
            </div>
            <div class="row ">

                <div class="col-12">
                    <table id="fileCheckListTableId"
                           class="table table-hover LWcustomTable table-bordered table-condensed table-sm table-vertical-center datatableRequiredDocs <?php echo 'search_services_table_Class' . trim($chMT) . '_' . trim($chST); ?>">
                        <thead class="thead-light">
                        <tr style="text-align: center;">
                            <th class="col-1 <?php if (DocsForm::$op == 'view') {
                                echo 'd-none';
                            } ?>">Upload
                            </th>
                            <th class="col-2" style="text-align: center;">Category
                                <i class="fa fa-info-circle tooltipClass text-primary"
                                   title="Category folders can be created in the platform settings--> Required docs tab. We have provided 5 categories by default: Borrower, property/collateral, loan docs, servicing, and other.  You can edit existing required docs and link to your desired categories."></i></th>
                            <th class="col-2" style="text-align: center;">Document Type</th>
                            <th class="col-2" style="text-align: center;">Required By</th>
                            <th class="col-2" style="text-align: center;">Document Status</th>
                            <th class="col-1" style="text-align: center;">Borrower<br>/Broker Notes</th>
                            <th class="col-1" style="text-align: center;">Underwriting Conditions</th>
                            <?php if (DocsForm::$userGroup != glUserRole::USER_ROLE_CLIENT) { ?>
                                <th class="col-1" style="text-align: center;">Internal Notes</th>
                            <?php } ?>
                            <th class="col-1" style="text-align: center;" title="Required/Not Required">Required</th>
                            <th class="col-1 <?php echo DocsForm::showInternalLoanProg(); ?>">
                                Internal<br>/Additional<br>Loan
                                Program
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        foreach ($mSTArray as $doc => $item) {
                            $rowSettings = requiredDocsForFileRow::fromRequiredDocsForFile($item, $chk);
                            $docStatusName = docStatusArray::getStatus($rowSettings->docStatus)->name;
                            ?>

                            <tr data-hiderow="<?php echo $rowSettings->datahiderow; ?>"
                                data-docid="<?php echo $rowSettings->chId; ?>"
                                class="<?php echo 'tr_'.cypher::myEncryption($rowSettings->chId). ' ' . $rowSettings->reqDocType . ' ' . $item->hideForAdditionalLogic; ?> "
                                style="<?php echo $rowSettings->deActivateDoc; ?>">
                                <td class="col-1 tooltipClass <?php if (DocsForm::$op == 'view') {
                                    echo 'd-none';
                                } ?>"
                                    id="TR_<?php echo cypher::myEncryption($rowSettings->chId); ?>"
                                    style="text-align: center; <?php echo $rowSettings->clsName; ?>"
                                    data-sort="<?php echo Strings::ZeroPad(docStatusArray::getStatus($rowSettings->docStatus)->sortOrder, 4) . ' - ' . Strings::ZeroPad($rowSettings->docSortOrder, 4) . ' - ' . $rowSettings->documentType; ?>"
                                    title="Drop Files To Upload">

                                    <input type="hidden" name="allowToAccessDocsStatus"
                                           id="AlloToAccesDocStatusPermi_<?php echo cypher::myEncryption($rowSettings->chId); ?>"
                                           value="<?php echo PageVariables::$allowToAccessDocs; ?>">
                                    <input type="hidden"
                                           id="dropzoneGetData_<?php echo cypher::myEncryption($rowSettings->chId); ?>"
                                           value="LMRResponseId=<?php echo(DocsForm::$LMRResponseId) ?>&exID=<?php echo(DocsForm::$executiveId) ?>&lId=<?php echo(DocsForm::$LMRId) ?>&uploadingUserType=<?php echo(DocsForm::$userGroup) ?>&uploadedBy=<?php echo(DocsForm::$userNumber) ?>&docId_1=<?php echo ($rowSettings->chId) . $rowSettings->chIdAppend; ?>&picOpt=5&CLType=<?php echo($rowSettings->chTy); ?>&tabOpt=DOC&bn=<?php echo DocsForm::$brokerNumber; ?>&clientName=<?php echo urlencode(DocsForm::$clientName); ?>&LMRExecutive=<?php echo DocsForm::$LMRExecutive; ?>&coBorrowerName=<?php echo urlencode(DocsForm::$coBorrowerName); ?>&brokerName=<?php echo urlencode(DocsForm::$brokerName); ?>&borrowerLName=<?php echo urlencode(DocsForm::$borrowerLName); ?>&FPCID=<?php echo DocsForm::$PCID; ?>&docCategory_1=Checklist Items&docId=<?php echo $rowSettings->chId; ?>&docStatsuId=<?php echo 'docStatus_' . $chk ?>&checkboxId=chk_cb_<?php echo $chk; ?>">
                                    <div class="dropzone dropzoneClass border <?php echo $rowSettings->clsName; ?> "
                                         docStatusId="docStatus_<?php echo $chk ?>"
                                         id="dropzoneTr_<?php echo cypher::myEncryption($rowSettings->chId); ?>"
                                         saveDocStatus="saveDocStatus('4','<?php echo cypher::myEncryption(DocsForm::$LMRId); ?>','<?php echo cypher::myEncryption($rowSettings->chId); ?>', '<?php echo $rowSettings->chTy; ?>')"
                                         style="min-height: 50px !important;">
                                    </div>

                                    <a title="Upload document" href="<?php echo CONST_URL_POPS; ?>uploadDocs.php"
                                       id='rId=<?php echo cypher::myEncryption(DocsForm::$LMRResponseId) ?>&exID=<?php echo cypher::myEncryption(DocsForm::$executiveId) ?>&LMRId=<?php echo cypher::myEncryption(DocsForm::$LMRId) ?>&userType=<?php echo cypher::myEncryption(DocsForm::$userGroup) ?>&userNumber=<?php echo cypher::myEncryption(DocsForm::$userNumber) ?>&PCChecklistID=<?php echo cypher::myEncryption($rowSettings->chId); ?>&picOpt=5&CLType=<?php echo cypher::myEncryption($rowSettings->chTy); ?>&tabOpt=DOC'
                                       data-name="File: <?php echo htmlspecialchars(DocsForm::$borrowerName); ?>  > Upload Documents"
                                       class="fa fa-upload fa-2x icon-dark-grey tip-bottom"
                                       style="text-decoration:none; display: none;"></a>
                                </td>
                                <td>
                                    <?php echo $rowSettings->categoryName ? $rowSettings->categoryName : '<span class="hide">na</span>'; ?>
                                </td>
                                <td
                                        title="Document Type"
                                        class="col-3"
                                        data-sort="<?php echo $rowSettings->documentType; ?>"
                                        <!-- data-sort="--><?php /*echo Strings::ZeroPad($rowSettings->docSortOrder, 4) . ' - ' . Strings::ZeroPad(docStatusArray::getStatus($rowSettings->docStatus)->sortOrder, 4) . ' - ' . $rowSettings->documentType; */?>"
                                >
                                    <div class="d-flex justify-content-between">
                                        <?php if ($_REQUEST['debug'] ?? null) { ?>
                                            <span><?php echo $rowSettings->docSortOrder; ?> - <?php echo $rowSettings->chId; ?>)</span>
                                        <?php } ?>
                                        <span class="align-middle"><?php echo $rowSettings->dnUrl; ?></span>
                                        <div class="d-flex justify-content-end">

                                            <?php if ($rowSettings->chTy == 'FCL' && ($item->createdUser == $_SESSION['userNumber'] || $_SESSION['userRole'] == 'Manager')) { ?>
                                                <a title="Click To Rename Required Doc Name"
                                                   data-id="PCID=<?php echo cypher::myEncryption(DocsForm::$PCID) ?>&userGroup=<?php echo cypher::myEncryption(DocsForm::$userGroup) ?>&userNumber=<?php echo cypher::myEncryption(DocsForm::$userNumber) ?>&showSaveBtn=1&reqDocType=File&checklistId=<?php echo cypher::myEncryption($rowSettings->chId); ?>&LMRId=<?php echo cypher::myEncryption(DocsForm::$LMRId); ?>&addOpt=FCL"
                                                   data-wsize='modal-xl' data-toggle='modal'
                                                   data-target='#exampleModal1' data-name="Edit Required Items"
                                                   class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass <?php if (DocsForm::$op == 'view') {
                                                       echo 'd-none';
                                                   } ?>"
                                                   data-href="<?php echo CONST_URL_POPS; ?>editFileChecklistItem.php">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                            <?php }

                                            echo $rowSettings->checklistDescription;

                                            if ($rowSettings->refDocName) { ?>
                                                <a title="Click To Download Ref Document"
                                                   target="_blank"
                                                   href="<?php echo $rowSettings->refDocName ?>"
                                                   class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass">
                                                    <i class="fa fa-download"></i>
                                                </a>
                                            <?php }
                                            if ($rowSettings->refDocUrl) { ?>
                                                <a title="Click To Open Ref Link"
                                                   href="<?php echo $rowSettings->refDocUrl ?>"
                                                   target="_blank"
                                                   class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass">
                                                    <i class="fa fa-link"></i>
                                                </a>
                                            <?php } ?>

                                        </div>
                                    </div>
                                </td>

                                <td
                                        title="Required By"
                                        class="col-2"
                                ><?php echo $rowSettings->rqBy; ?></td>

                                <td nowrap
                                    class="col-2"
                                    title="Document Status"
                                    data-sort_order="<?php echo $rowSettings->docSortOrder; ?>"
                                    data-status="<?php echo $rowSettings->docStatus; ?>"
                                    style="<?php echo docStatusArray::getStyle($rowSettings->docStatus); ?>"
                                    id="docStatus_<?php echo $chk ?>_td"
                                    data-sort="<?php echo $docStatusName; ?>"
                                >
                                    <?php if (DocsForm::$userGroup == 'Employee'
                                        || DocsForm::$userGroup == 'Super'
                                        || DocsForm::$allowToAccessDocs == 1) { ?>
                                        <select name="docStatus_<?php echo $chk ?>"
                                                id="docStatus_<?php echo $chk ?>"
                                                data-chk="<?php echo $chk ?>"
                                                style="width: 90%; display: inline-block"
                                                class="form-control  <?php if (DocsForm::$isHMLO == 1) {
                                                    echo 'mandatory';
                                                } ?>"
                                            <?php
                                            if (DocsForm::$allowToEdit) { ?>
                                                onchange="saveDocStatus(this, '<?php echo cypher::myEncryption(DocsForm::$LMRId); ?>', '<?php echo cypher::myEncryption($rowSettings->chId); ?>', '<?php echo $rowSettings->chTy; ?>');"
                                            <?php } else { ?> disabled <?php } ?>>
                                            <?php foreach (docStatusArray::getSelectStatuses() as $key => $status) {
                                                $key = explode('::', $key); ?>
                                                <option style="<?php echo docStatusArray::getStyle($key[0]); ?>"
                                                        value="<?php echo $key[0]; ?>" <?php echo in_array($rowSettings->docStatus, $key) ? 'selected="selected"' : ''; ?>><?php echo $status; ?></option>
                                            <?php } ?>
                                        </select>
                                    <?php } else { ?>
                                        <input type="hidden" id="docStatus_<?php echo $chk ?>" value="4">
                                        <b><?php if ($rowSettings->docStatus != 1) {
                                                echo $docStatusName;
                                            } ?></b>
                                    <?php } ?>
                                    <?php if ($rowSettings->newTimeStamp) { ?>
                                        <span class="tooltipClass" title="" data-html="true"
                                              data-original-title="<?php echo htmlentities($rowSettings->newTimeStamp); ?>">
                                    <i class="fa fa-info-circle text-primary tooltipClass"
                                       data-original-title="" title=""></i>
                                </span>
                                        <br>
                                        <span
                                                title="Document Status Log"
                                                class="tooltipClass"
                                                data-lmrid="<?php echo cypher::myEncryption(DocsForm::$LMRId); ?>"
                                                data-req-id="<?php echo cypher::myEncryption($rowSettings->chId); ?>"
                                                data-req-name="<?php echo cypher::myEncryption($item->docName); ?>"
                                                onclick="changeLog.documentStatus(this);">
                                      <i class="fa fa-clock cursor-pointer  text-primary"></i>
                                </span>
                                    <?php } ?>


                                </td>
                                <td class="col-1 text-center" title="Borrower/Broker Notes">
                                    <?php echo DocsForm::renderCommentsIcon($rowSettings, glFUModulesNotesTypeArray::GENERAL, $chk); ?>
                                </td>

                                <td class="text-center col-1" title="Underwriting Conditions">
                                    <?php echo DocsForm::renderCommentsIcon($rowSettings, glFUModulesNotesTypeArray::UNDERWRITING, $chk); ?>
                                </td>
                                <?php if (DocsForm::$userGroup != glUserRole::USER_ROLE_CLIENT) { ?>
                                    <td class="text-center col-1" title="Internal Notes">
                                        <?php echo DocsForm::renderCommentsIcon($rowSettings, glFUModulesNotesTypeArray::INTERNAL, $chk); ?>
                                    </td>
                                <?php } ?>


                                <td class="text-center col-1">
                                    <?php
                                    if (DocsForm::$allowToEdit && DocsForm::$userGroup !== 'Client') {
                                        $isReqDocs = ($rowSettings->reqDocType === 'reqDocs');
                                        $encryptedChId = cypher::myEncryption($rowSettings->chId);
                                        $encryptedLMRId = cypher::myEncryption(DocsForm::$LMRId);
                                        $encryptedLMRResponseId = cypher::myEncryption(DocsForm::$LMRResponseId);
                                        ?>
                                        <div id="chk_req_<?php echo $chk; ?>" data-DisableUpload="<?php echo $isReqDocs ? "1" : "0"; ?>">
                                            <span class="cursor-pointer btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass"
                                                  title="<?php echo $isReqDocs ? "Click To Disable The Document" : "Click To Enable The Document"; ?>"
                                                  data-cid="<?php echo $encryptedChId; ?>"
                                                  data-lmrid="<?php echo $encryptedLMRId; ?>"
                                                  data-req="<?php echo $isReqDocs ? "0" : "1"; ?>"
                                                  data-chkbxid="chk_cb_<?php echo $chk; ?>"
                                                  data-imgid="chk_req_<?php echo $chk; ?>"
                                                  data-responseid="<?php echo $encryptedLMRResponseId; ?>"
                                                  data-cltype="<?php echo $rowSettings->chTy; ?>"
                                                  onclick="fileCheckListForm.checkListNotRequired(this);">
                                                <i class="<?php echo $isReqDocs ? "fa fa-check text-success" : "fa fa-times text-danger"; ?>"></i>
                                            </span>
                                        </div>
<?php
                                        } else {
                                        if ($rowSettings->reqDocType != 'reqDocs') { ?>
                                            <div class="btn btn-sm btn-light btn-text-primary disabled  btn-icon m-1 tooltipClass"
                                                 id="chk_req_<?php echo $chk; ?>" data-DisableUpload="0"
                                                 title="<?php echo $rowSettings->chkReqUpdatedUser; ?>">
                                                <i class="fa fa-times text-danger"></i></div>
                                        <?php } else { ?>
                                            <div class="btn btn-dis btn-sm btn-light btn-text-primary disabled btn-icon m-1 tooltipClass"
                                                 title="Document Required" id="chk_req_<?php echo $chk; ?>"
                                                 data-DisableUpload="1">
                                                <i class="fa fa-check text-success"></i>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                </td>

                                <td title="Internal Loan Programs"
                                    class="col-1 <?php echo DocsForm::showInternalLoanProg(); ?>"
                                    style="text-align: center !important;">
                                    <?php echo $rowSettings->getLoanPrograms(); ?>
                                </td>
                            </tr>


                            <?php
                            $chk++;
                        }

                        ?>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php } ?>

<script>
    $(function () {
        fileCheckListForm.pendingReviewStatusId = <?php echo docStatusArray::pendingReviewStatusId(DocsForm::$PCID); ?>;
        fileCheckListForm.pendingReviewStatusStyle = "<?php echo docStatusArray::getStyle(docStatusArray::pendingReviewStatusId(DocsForm::$PCID)); ?>";
    });
</script>
<!-- moduleAndServiceRequiredDocs.php -->
