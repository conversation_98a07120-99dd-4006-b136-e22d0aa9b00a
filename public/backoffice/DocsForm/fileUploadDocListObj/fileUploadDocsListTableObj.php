<?php
global $allowToEdit, $recordDate, $LMRId, $aud, $oldFPCID, $docId, $displayDocName, $docCategory, $userId, $sortOpt, $orderBy, $tempDocInfoArray,
       $userTimeZone, $empInfoArray, $op,
       $branchInfoArray, $agentInfoArray, $clientInfoArray, $resFileSizeInfo, $userGroup, $userRole, $allowToDeleteUploadedDocs,
       $allowToAccessDocs, $activeTab, $publicUser, $borrowerName;
global $activeStatus;

use models\constants\gl\glCloudDocType;
use models\cypher;
use models\PageVariables;
use models\standard\Dates;
use models\standard\Directories;
use models\standard\HTTP;
use models\standard\Strings;

$glCloudDocType = glCloudDocType::$glCloudDocType;

?>

<input type="hidden" name="AE" value="<?php echo cypher::myEncryption($allowToEdit) ?>">
<input type="hidden" name="RD" value="<?php echo cypher::myEncryption($recordDate) ?>">

<table class="table table-hover LWcustomTable table-bordered table-condensed table-sm table-vertical-center dataTbl_<?php echo cypher::myEncryption($LMRId); ?>"
       id="UploadFileDocList">
    <thead class="thead-light">
    <tr>
        <th>#</th>
        <th class="text-center <?php if ($activeStatus == '0') echo 'hidden'; ?>">
            <div class="checkbox-inline">
                <label class="checkbox checkbox-outline checkbox-outline-2x checkbox-primary" id="checkallshow"
                       for="select_allAllUploadClass">
                    <input type="checkbox" class="newCheck" id="select_allAllUploadClass"/>
                    <span></span>
                    <a class="fa fa-info-circle tooltipClass"
                       style="text-decoration:none;" title="Click To Select All"></a>
                </label>
            </div>
        </th>
        <th class="text-center <?php if ($activeStatus == '0') echo 'hidden'; ?>"></th>
        <th title="File Name"> File Name <i class="ki ki-up-and-down text-primary icon-md"></i></th>
        <th style="vertical-align: middle !important;"> Category <i class="ki ki-up-and-down text-primary icon-md"></i></th>
        <th style="vertical-align: middle !important;"> Doc Type <i class="ki ki-up-and-down text-primary icon-md"></i></th>
        <?php if ($aud != 1) { ?>
            <th title="Expiry Date">Expiry Date <i class="ki ki-up-and-down text-primary icon-md"></i></th>
        <?php } ?>
        <th style="vertical-align: middle !important;">
            <span
                    id="sortByUploadDateAsc"
                    style="display: none;"
                    class="cursor-pointer"
                    onclick="showSortableListRefresh('uploadedDate', 'asc', '<?php echo cypher::myEncryption($LMRId) ?>', '<?php echo cypher::myEncryption($oldFPCID) ?>', '<?php echo cypher::myEncryption($allowToEdit) ?>', '<?php echo cypher::myEncryption($recordDate) ?>', '<?php echo cypher::myEncryption($docId) ?>', '<?php echo cypher::myEncryption($displayDocName) ?>', '<?php echo cypher::myEncryption($docCategory) ?>', '<?php echo cypher::myEncryption($userId) ?>', '', '1');"><i
                        class="fa fa-sort-asc fa-stack-1x <?php if (($sortOpt == 'uploadedDate' || $sortOpt == '') && $orderBy == 'asc') { ?> icon-active <?php } else { ?> icon-inactive <?php } ?>"></i></span>
            <div class="">Uploaded By <i class="ki ki-up-and-down text-primary icon-md"></i></div>
        </th>
        <th style="vertical-align: middle !important;" title="File Type">File Type<br>(Size) <i class="ki ki-up-and-down text-primary icon-md"></i></th>
        <th></th>
    </tr>
    </thead>
    <tbody>
    <?php
    if (count($tempDocInfoArray) > 0) {
        $displayCheckBox = 0;
        $snoD = 0;
        foreach ($tempDocInfoArray as $doc => $docInfo) {
            $tempDocArray = [];
            $flatNotes = '';
            $dType = '';
            $docUrl = '';
            $docName = '';
            $displayDocName = '';
            $docId = 0;
            $myUploadedBy = '';
            $myUploadedRole = '';
            $docCategory = '';
            $docChecklistName = '';
            $DocExpiryDate = '';

            $docName = trim($docInfo['docName']);
            $displayDocName = trim($docInfo['displayDocName']);
            $uploadedDate = trim($docInfo['uploadedDate']);
            $userId = trim($docInfo['uploadedBy']);
            $userType = trim($docInfo['uploadingUserType']);
            $docCategory = trim($docInfo['docCategory']);
            $docId = trim($docInfo['docID']);
            $fileType = trim($docInfo['fileType']);
            $dType = trim($docInfo['dType']);
            $docUrl = trim($docInfo['docUrl']);
            $categoryName = $docInfo['docChecklistName']['categoryName'] ?? null;
            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
            $ipArray['outputZone'] = $userTimeZone;
            $ipArray['inputTime'] = $uploadedDate;
            $uploadedDate = Dates::timeZoneConversion($ipArray);

            if ($docInfo['expiryDate'] != null) {
                $DocExpiryDate = Dates::formatDateWithRE($docInfo['expiryDate'], 'YMD', 'm/d/Y');
            }

            $flatNotes = trim($docInfo['notes']);

            if (Dates::IsEmpty($uploadedDate)) {
                $uploadedDate = '';
            } else {
                $uploadedDate = Dates::formatDateWithRE($uploadedDate, 'YMD_HMS', 'm/d/Y h:i A');
            }
            if ($displayDocName == '' || $displayDocName == NULL) $displayDocName = $docName;
            $tempRecDate = str_replace('-', '', $recordDate);
            $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
            $fileValue = $LMRId;

            $fP = $folderName . '/' . $fileValue . '/';

            $docReqbyListArray = [];
            /* New Required Document Option */
            if ($docInfo['docCategory'] == 'Checklist Items') {
                $docChecklistName = trim($docInfo['docChecklistName']['doctypeName']);
                $docReqbyListArray = explode(',', $docInfo['docChecklistName']['requiredByList']);
                $fP .= CONST_UPLOAD_DOC_FOLDER;


            } elseif ($docInfo['docCategory'] == 'Other') {

                if (in_array($docInfo['docChecklistName'], ['Appraisal1', 'Appraisal2', 'BPO1', 'BPO2', 'BPO3', 'Title Report', 'Property Insurance Coverage1', 'Property Insurance Coverage2', 'Property Insurance Coverage3', 'Property Insurance Coverage4', 'Property Insurance Coverage5', 'Property Insurance Coverage', 'AVM1', 'AVM2', 'AVM3'])) {
                    $docChecklistName = trim($docInfo['docChecklistName']);
                    $fP .= CONST_FOLDER_PROPERTY;

                } // View link for Property Management files //
                elseif (is_array($docInfo['docChecklistName']) && $docInfo['docChecklistName']['doctypeName'] == 'Prop. Manager Cert.') {
                    $docChecklistName = trim($docInfo['docChecklistName']['doctypeName']);
                    $fP .= CONST_FOLDER_PROPERTY_MANAGEMENT_DOC;
                } else {
                    $docChecklistName = is_array($docInfo['docChecklistName']) ? trim($docInfo['docChecklistName']['doctypeName']) : trim($docInfo['docChecklistName']);
                    $docReqbyListArray = is_array($docInfo['docChecklistName']) ? explode(',', $docInfo['docChecklistName']['requiredByList']) : [];
                    $fP .= CONST_UPLOAD_DOC_FOLDER;

                }
                $docCategory = 'Other';
            }

            $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc&dn=' . cypher::myEncryption(str_replace(' ', '_', $displayDocName));
            $fP .= $docName;


            /* New Required Document Option */


            if ($userType == 'Processor' || $userType == 'Employee') {
                if (array_key_exists($userId, $empInfoArray ?? [])) {
                    $tempEmpInfo = $empInfoArray[$userId];
                    $myUploadedBy = trim($tempEmpInfo['processorName']);
                    $myUploadedRole = trim($tempEmpInfo['role']);
                }

                if ($myUploadedRole == '' || $myUploadedRole == NULL) $myUploadedRole = 'Staff';
                $myUploadedBy = $myUploadedBy . ' (' . $myUploadedRole . ')';
            } elseif ($userType == 'LMR Executive' || $userType == 'Branch') {
                if (array_key_exists($userId, $branchInfoArray ?? [])) {
                    $tempBranchInfo = $branchInfoArray[$userId];
                    $myUploadedBy = trim($tempBranchInfo['LMRExecutive']);
                }
                $myUploadedBy = $myUploadedBy . ' (Branch)';
            } elseif ($userType == 'Broker' || $userType == 'Agent') {
                if (array_key_exists($userId, $agentInfoArray ?? [])) {
                    $tempAgentInfo = $agentInfoArray[$userId];
                    $myUploadedBy = trim($tempAgentInfo['brokerName']) . ' (Loan Officer/Broker)';
                }
            } elseif ($userType == 'Client') {
                if (array_key_exists($userId, $clientInfoArray ?? [])) {
                    $tempClientInfo = [];
                    $tempClientInfo = $clientInfoArray[$userId];

                    $myUploadedBy = trim($tempClientInfo['clientFName']) . ' ' . trim($tempClientInfo['clientLName']) . ' (' . $userType . ')';
                }

            } elseif ($userType == 'Super' || $userType == 'Admin') {
                $myUploadedBy = $userType;
            }

            $dispFS = 0;
            $tempFileSize = 0;
            if (count($resFileSizeInfo ?? []) > 0) {
                if (array_key_exists('UD', $resFileSizeInfo) && count($resFileSizeInfo['UD']) > 0) {
                    if (array_key_exists('file_size_' . $doc, $resFileSizeInfo['UD'])) {
                        $tempFileSize = trim($resFileSizeInfo['UD']['file_size_' . $doc]);
                    }
                }
            }
            $dispFS = trim(Directories::calculateAndDisplayRemoteFileSize($tempFileSize));

            if ($flatNotes == '') {
                $notes = "<a class=\"btn1 d-none\" id=\"docAddNotes\"
                                           href='#'
                                           data-href=\"" . CONST_URL_POPS . "addFlatNotes.php\"
                                           data-wsize='modal-sm'
                                           data-name='Add Notes'
                                           data-toggle='modal' data-target='#exampleModal1'
                                           data-id=''>
                                            <i class=\"ki ki-solid-plus icon-nm\"></i>
                                        </a><a class=\"btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1  tooltipClass\" style=\"text-decoration:none\" href=\"javascript:addFlatNotes('" . cypher::myEncryption($LMRId) . "', '" . cypher::myEncryption($docId) . "', 'uploadDocs', '', '" . HTTP::escapeQuoteForPOST($displayDocName) . "', '', '');\" title=\"Click to add notes\"><i class=\"icon-md fas fa-comment-medical\"></i></a>";
            } else {
                $notes = "<a class=\"btn1 d-none\" id=\"docAddNotes\"
                                           href='#'
                                           data-href=\"" . CONST_URL_POPS . "addFlatNotes.php\"
                                           data-wsize='modal-sm'
                                           data-name='Add Notes'
                                           data-toggle='modal' data-target='#exampleModal1'
                                           data-id=''>
                                            <i class=\"ki ki-solid-plus icon-nm\"></i>
                                        </a><a class=\"btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1  tooltipClass\" style=\"text-decoration:none\" href=\"javascript:addFlatNotes('" . cypher::myEncryption($LMRId) . "', '" . cypher::myEncryption($docId) . "', 'uploadDocs', '', '" . HTTP::escapeQuoteForPOST($displayDocName) . "', '', '');\" title=\"" . $flatNotes . "\"><i class=\"icon-md fas fa-comments\"></i></a>";
            }
            $cls = '';
            if ($doc % 2 == 0) $cls = 'odd';
            ?>
            <?php
            $LoadDocument = 0;
            $userTypeCheck = '';
            $editDocument = 0;
            $deleteDocument = 0;

            if ($userGroup == 'Super') {
                $LoadDocument = 1;
                $editDocument = 1;
                $deleteDocument = 1;
            } elseif ($userGroup == 'Employee' && $userRole == 'Manager') {
                $LoadDocument = 1;
                $editDocument = 1;
                $deleteDocument = 1;

            } elseif ($userGroup == 'Employee' && $userRole != 'Manager') {
                $LoadDocument = 1;
                if ($allowToDeleteUploadedDocs == 1) {
                    $editDocument = 1;
                    $deleteDocument = 1;
                } else {
                    if ($_SESSION['userNumber'] == $userId) {
                        $deleteDocument = 1;
                        $editDocument = 1;
                    } else {
                        $editDocument = 0;
                        $deleteDocument = 0;
                    }
                }
            } else if ($userGroup == 'Branch') {
                if ($allowToAccessDocs) {
                    $LoadDocument = 1;
                    $editDocument = 1;
                } else {
                    if ($userId == PageVariables::$userNumber || in_array($userRole, $docReqbyListArray)) {
                        $LoadDocument = 1;
                    }
                    if ($userId == PageVariables::$userNumber) {
                        $editDocument = 1;
                    }
                }
                if ($allowToDeleteUploadedDocs == 1 && $_SESSION['userNumber'] == $userId) {
                    $deleteDocument = 1;
                } else {
                    $deleteDocument = 0;
                }
            } else if ($userGroup == 'Agent') {
                $userTypeCheck = 'Broker';
                if ($allowToAccessDocs) {
                    $LoadDocument = 1;
                    $editDocument = 1;
                } else {
                    if ($userId == PageVariables::$userNumber || in_array($userTypeCheck, $docReqbyListArray)) {
                        $LoadDocument = 1;
                    }
                    if ($userId == PageVariables::$userNumber) {
                        $editDocument = 1;
                    }
                }
                if ($allowToDeleteUploadedDocs == 1 && $_SESSION['userNumber'] == $userId) {
                    $deleteDocument = 1;
                } else {
                    $deleteDocument = 0;
                }
            } elseif ($userGroup == 'Client') {
                $userTypeCheck = 'Borrower';

                if ($userId == PageVariables::$userNumber) {
                    $LoadDocument = 1;
                    $editDocument = 1;
                } else if (in_array($userTypeCheck, $docReqbyListArray)) {
                    $LoadDocument = 1;
                }
                //AUD
                if ($aud == 1) {
                    $LoadDocument = 1;
                }
            }
            ?>
            <?php if ($LoadDocument) {

                $snoD = $snoD + 1; ?>
                <tr class="bbg <?php echo $cls ?>" id="upload_<?php echo $docId ?>">
                    <td class="text-center"><?php echo $snoD; //echo($doc + 1); ?></td>
                    <td class="<?php if ($activeStatus == '0') echo 'hidden'; ?>">
                        <?php if (($dispFS > 0 && !(array_key_exists($dType, $glCloudDocType)) || (array_key_exists($dType, $glCloudDocType) && $activeTab != 'PE'))) {
                            $displayCheckBox = 1; ?>
                            <span class="checkbox-inline">
                                    <label for="upDoc_<?php echo $doc ?>"
                                           class="checkbox checkbox-outline checkbox-outline-2x checkbox-primary">
                                        <input
                                                class="newCheck selectAllUploadClass" type="checkbox"
                                                data-file-size="<?php echo $tempFileSize ?>"
                                                name="upDoc[]"
                                                id="upDoc_<?php echo $doc ?>"
                                                value="<?php echo $docId ?>"
                                                onclick="return getSelectedUpDoc('<?php echo $docId ?>','<?php echo $tempFileSize ?>',this.checked);" <?php if ($op == 'view' || $_REQUEST['op'] == 'view') {
                                            echo 'disabled';
                                        } ?>><span></span>
                                    </label>
                                </span>
                        <?php } ?>
                    </td>
                    <td class="<?php if ($activeStatus == '0') echo 'hidden'; ?>">
                        <div class="d-flex justify-content-between ">
                            <?php if ($allowToEdit && $publicUser != 1) { ?>
                                <span id="flatNotesDiv_<?php echo cypher::myEncryption($docId) ?>_<?php echo cypher::myEncryption($LMRId) ?>"><?php echo $notes ?></span>
                            <?php } ?>
                            <?php
                            if ($dispFS > 0 || array_key_exists($dType, $glCloudDocType)) {
                                if ($editDocument) { ?>
                                    <span id="<?php echo $docId ?>_editFile">
                                        <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass <?php if ($op == 'view' || $_REQUEST['op'] == 'view') {
                                            echo 'd-none';
                                        } ?>"
                                           data-toggle="modal" data-target="#exampleModal1" data-wsize="modal-lg"
                                           href="javascript:void(0);"
                                           data-href="<?php echo CONST_URL_POPS; ?>editFileDocInfo.php"
                                           data-id='LMRId=<?php echo cypher::myEncryption($LMRId) ?>&amp;docId=<?php echo cypher::myEncryption($docId) ?>&amp;displayDocName=<?php echo cypher::myEncryption($displayDocName) ?>&amp;docCategory=<?php echo cypher::myEncryption($docCategory) ?>&amp;userId=<?php echo cypher::myEncryption($userId) ?>&amp;userType=<?php echo cypher::myEncryption($userType); ?>&allowToAccessDocs=<?php echo cypher::myEncryption($allowToAccessDocs); ?>'
                                           data-name='File: <?php echo htmlentities($borrowerName, ENT_QUOTES, 'UTF-8') ?> > Doc Info'
                                           title="Click to edit File Doc Infos"><i class="fa fa-edit"></i></a>

                                    </span>
                                    <?php
                                }
                            }
                            $docTypeImg = '';
                            $docuTypeCheck = '';
                            if (array_key_exists($dType, $glCloudDocType)) {
                                if ($glCloudDocType[$dType] == 'Google Drive') {
                                    $docTypeImg = "<i style=\"color:#0070E0\" class=\"fa fa-google fa-2x\" aria-hidden=\"true\"></i>";
                                    $titleTxt = 'Google Drive Document';
                                } else if ($glCloudDocType[$dType] == 'Drop Box') {
                                    $docTypeImg = "<i style=\"color:#0070E0\" class=\"fa fa-dropbox fa-2x\" aria-hidden=\"true\"></i>";
                                    $titleTxt = 'Drop Box Document';
                                } else if ($glCloudDocType[$dType] == 'Cloud Box') {
                                    $docTypeImg = "<img src=\"/assets/images/box.png\" width=\"25\" height=\"20\">";
                                    $titleTxt = 'Cloud Box Document';
                                } else {
                                    $docTypeImg = "<i style=\"color:#0070E0\" class=\"fa fa-warning fa-2x tip-bottom icon-red\" aria-hidden=\"true\"></i>";
                                    $titleTxt = 'File is corrupted/File not found';
                                }
                                $displayDocName = "<a href =\"" . $docUrl . "\" title=\"" . $titleTxt . "\" target=\"_blank\">" . stripslashes($displayDocName) . '</a>';
                                $docuTypeCheck = 'webLink';
                            } else {
                                $displayDocName = stripslashes($displayDocName);
                                $docuTypeCheck = 'doc';
                            }
                            $dispDocCategory = '';
                            $dispDocCategory = $docCategory;

                            if (!$categoryName) {
                                $dispDocCategory = $docCategory;
                                if ($dispDocCategory == 'Checklist Items') $dispDocCategory = 'Required Docs';
                            } else {
                                $dispDocCategory = $categoryName;
                            }
                            ?>
                        </div>
                    </td>
                    <td class="py-2" id="<?php echo $docId ?>_docName" title="File Name">
                        <input type="hidden" id="docuTypeCheck_<?php echo $docId; ?>"
                               value="<?php echo $docuTypeCheck; ?>">
                        <div class="force-wrap with-children-tip"
                             style="margin-left:10px;"><?php if ($dispFS > 0 || array_key_exists($dType, $glCloudDocType)) { ?>
                            <a class="tip-bottom" style="text-decoration:none;" rel="nofollow" target="_blank"
                               href="<?php echo $uploadDocUrl ?>" title="Click to view File Doc Info"><?php } ?>
                                <?php echo $displayDocName; ?>
                                <?php if ($dispFS > 0 || array_key_exists($dType, $glCloudDocType)) { ?></a><?php } ?>
                        </div>
                    </td>


                    <td title="Doc Category" id="<?php echo $docId ?>_docCategory">
                        <?php echo stripslashes($dispDocCategory) ?>
                    </td>
                    <!--  Doc Type Name $docChecklistName -->
                    <td class="text-left" title="Doc Type"><?php echo $docChecklistName; ?></td>
                    <?php if ($aud != 1) {  //hide for AUD?>
                        <td title="Expiry Date"
                            style="text-align: center !important;"><?php echo $DocExpiryDate; ?></td>
                    <?php } ?>
                    <td class="text-left">
                        <?php echo trim($uploadedDate) . ' - ' . $userTimeZone . '<br>- ' . ucwords($myUploadedBy) ?></td>

                    <td style="text-align: center;">
                        <?php
                        if (array_key_exists($dType, $glCloudDocType)) {
                            echo $docTypeImg;
                        } else { ?>
                            (<?php echo strtoupper(wordwrap($fileType, 5, '<br>', 1)); ?>) <br>
                            <?php
                            if ($dispFS > 0) {
                                echo '(' . $dispFS . ')';
                            } else { ?>
                                <i class="flaticon2-warning text-danger tooltipClass"
                                   title="File is corrupted/File not found"></i>
                                <?php
                            }
                        } ?>
                    </td>
                    <td>
                        <?php
                        if ($deleteDocument && $activeStatus) { ?>
                            <a class="btn btn-xs btn-danger btn-text-primary  btn-icon m-1 tooltipClass <?php if ($op == 'view' || $_REQUEST['op'] == 'view') {
                                echo 'd-none';
                            } ?>"
                               style="text-decoration:none;"
                               href="javascript:deleteUploadFileDoc('<?php echo $docId ?>', '<?php echo cypher::myEncryption($userId) ?>', this, '<?php echo cypher::myEncryption($LMRId) ?>',  'upload_<?php echo $docId ?>', 'UploadFileDocList');"
                               alt="Click to delete" title="Click to delete">
                                <i class="flaticon2-trash"></i>
                            </a>
                        <?php } ?>

                        <?php if ($deleteDocument && $activeStatus == '0') { ?>
                            <a class="btn btn-xs btn-primary btn-text-primary btn-icon m-1 tooltipClass <?php if ($op == 'view' || $_REQUEST['op'] == 'view') {
                                echo 'd-none';
                            } ?>"
                               style="text-decoration:none;"
                               href="javascript:restoreDeletedUploadFileDoc('<?php echo $docId ?>', '<?php echo cypher::myEncryption($userId) ?>', this, '<?php echo cypher::myEncryption($LMRId) ?>',  'upload_<?php echo $docId ?>', 'UploadFileDocList');"
                               alt="Click to restore" title="Click to restore">
                                <i class="fas fa-trash-restore"></i>
                            </a>
                        <?php } ?>
                    </td>
                </tr>
                <?php
            }
        }
    } ?>
    </tbody>
</table>

<script>
    $(document).ready(function () {
        <?php if($aud != 1) { ?>
        initialiseDatatable('dataTbl_<?php echo cypher::myEncryption($LMRId); ?>');
        <?php } ?>
        <?php
        if($displayCheckBox == '1'){ ?>
        $('#checkallshow').show();
        <?php }?>
        $('#select_allAllUploadClass').on('click', function () {
            upDocId = '';
            dCnt = '';
            totFileSizeUpload = '0';
            if (this.checked) {
                $('.selectAllUploadClass').each(function () {
                    this.checked = true;
                    if (dCnt > 0) {
                        upDocId += ", ";
                    }
                    upDocId += this.value;
                    dCnt++;
                    totFileSizeUpload = parseFloat(totFileSizeUpload) + parseFloat($(this).attr('data-file-size'));
                });
                document.loanModForm.selectedUpDoc.value = upDocId;
                document.loanModForm.totFileSize.value = totFileSizeUpload;

            } else {
                $('.selectAllUploadClass').each(function () {
                    this.checked = false;
                });
                document.loanModForm.selectedUpDoc.value = '';
                document.loanModForm.totFileSize.value = '0';
            }
        });
        $('.selectAllUploadClass').on('click', function () {
            if ($('.selectAllUploadClass:checked').length == $('.selectAllUploadClass').length) {
                $('#select_allAllUploadClass').prop('checked', true);
            } else {
                $('#select_allAllUploadClass').prop('checked', false);
            }
        });
    });
    //selectAllUploadClass
</script>
<style>
    .sortingDivAscDesc > .fa-sort-height {
        /*display: none !important;*/
        visibility: hidden !important;
    }


    .InnerTableClass td {
        border-bottom: 0px !important;
    }
</style>
