<?php
//global variables

global $PCID, $userGroup, $userRole, $clientName;
global $loanNumber, $LMRResponseId, $brokerNumber, $LMRId, $servicer2, $tempFileSize;

use models\constants\gl\glGuarantyDynamicDocs;
use models\constants\lawofficesClientDocArray;
use models\Controllers\backoffice\DocsForm;
use models\cypher;
use models\standard\Strings;

$lawofficesClientDocArray = lawofficesClientDocArray::$lawofficesClientDocArray;
$glGuarantyDynamicDocs = glGuarantyDynamicDocs::$glGuarantyDynamicDocs;

$pkgListResult = DocsForm::$pkgListResult;
$fileModules = DocsForm::$fileModules;
$tempPkgGroups = DocsForm::$tempPkgGroups;

$selectedPcAndBranchDoc = DocsForm::$selectedPcAndBranchDoc;

$attachChkPkg = 0;
$attachCustWordPkg = 0;
$docsLen = 2; /* Default - STD and Custom Docs */

// s=0 means Standard Docs,  s=1 means Custom Docs,
for ($s = 0;
     $s < 1;
     $s++) {
    if (!isset($isDIYUserLogin)) {
        $isDIYUserLogin = 0;
    }

    $CompanyAndBranchDocArray = array_merge_recursive(DocsForm::$PCDocArray, DocsForm::$branchDocArray);
    $isGuarantDoc = 0;

    $tempGuarArray = [];

    if (count(DocsForm::$fileAdditionalGuarantorsInfo) > 0) {
        foreach ($pkgListResult['C'] as $cDoc => $pkgList) {
            $pkID = $pkgList->PKGID;
            if (in_array($pkID, $glGuarantyDynamicDocs)) {
                $tempGuarArray = $pkgListResult['C'][$cDoc];
                unset($pkgListResult['C'][$cDoc]);
                $isGuarantDoc = 1;
                break;
            }
        }

        if ($isGuarantDoc == 1) {
            $pkgListResult['C'] = array_values($pkgListResult['C']);
            $orgDocName = $tempGuarArray->packageName;
            foreach (DocsForm::$fileAdditionalGuarantorsInfo as $aGur => $item) {
                $guFName = $item->guarantorFName;
                $guLName = $item->guarantorLName;
                $guName = $guFName . ' ' . $guLName;
                $tempGuarArray->packageName = $orgDocName . '-' . $guName;
                $pkgListResult['C'][] = $tempGuarArray;
            }
        }
    }
    ?>
    <div class="card card-custom autoGenDocsCard">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    Auto-Generated Documents <i class="manualPopover fas fa-info-circle text-primary " data-html="true"
                                                title="How E-sign process works:" data-content='To send any package for fresh e-signature, use the "Auto-generated Doc" section.<br><br>If
                                you have already sent a package for e-sign, AND if you want to send the link again to
                                the borrower / coborrower, use the "resend" icon in the "e-signed documents" section.
                                For some reason, if you want to "delete" a signed document, use the "trash" icon against
                                the document name.'></i>
                </h3>
            </div>
            <div class="card-toolbar ">
                         <span class="fas fa-file-signature text-primary fa-1x"
                               title="E-Signable For Borrower">E-Signable For Borrower </span>
                <span class="fa fa-file-pdf fa-1x text-danger ml-2"
                      title="Non E-Signable Format">Non E-Signable Format </span>

                <a href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="autoGenDocsCard"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body autoGenDocsCard_body autoGeneratedDocs">
            <div class="row col-12 p-2">
                <?php
                $tempCArray = [];
                $aGurn = 0;
                if (count($selectedPcAndBranchDoc) > 0) { // Hardcoded for Branch and Pc Document list..
                    if (!in_array('C', $fileModules)) {
                        $fileModules[] = 'C';
                    }
                    if (array_key_exists('C', $pkgListResult)) {
                        $tempCArray = $pkgListResult['C'];
                        unset($pkgListResult['C']);
                        $pkgListResult['C'] = array_merge($tempCArray, $selectedPcAndBranchDoc);
                    } else {
                        $pkgListResult['C'] = $selectedPcAndBranchDoc;
                    }
                }

                $newRow = 0;
                $tToolTip = '';
                $AutoGenModuleWise = [];

                foreach($fileModules as $j => $fileModule) {
                    if (array_key_exists($fileModule, $pkgListResult)) {
                        if ($newRow == 0) echo "<div class=\"row col-12\">";
                        /** 4 category Main Div Start **/

                        if (array_key_exists($fileModule, $tempPkgGroups)) {
                            if ($tempPkgGroups[$fileModule] == 'Custom Auto-Generated') {
                                $tToolTip = "<i class=\"popoverClass fas fa-info-circle text-primary\" data-content=\"You can send us your own documents, packages, forms, etc... and we will make them auto-populate with E-sign if needed.\"></i>";
                            }
                            echo "<div class=\"col-md-6\"><h5>" . $tempPkgGroups[$fileModule] . $tToolTip . '</h5>';

                            $loanAutoGen = $tempPkgGroups[$fileModule];
                            /** Category div start **/
                            $AutoGenModuleWise[$loanAutoGen] = [];
                        }

                        $tempPkgCatArray = [];
                        $showDocs = 1;
                        $tempPkgCatArray = $pkgListResult[$fileModule];

                        $tempPkgSubCategory = '';
                        $subCatCnt = 0;

                        foreach ($tempPkgCatArray as $k => $tempPkgCat) {
                            $pkgSubCategory = trim($tempPkgCat->subCategory);
                            if (($PCID == '2863' && $userGroup != 'Employee' && $pkgSubCategory == 'Custom Docs')) {
                                continue;
                            }

                            if ($tempPkgSubCategory != $pkgSubCategory) {
                                if ($subCatCnt > 0) {
                                    echo '</ol></div></li></ol>';
                                }
                                $subCatCnt++;
                                ?>
                                <ol style="list-style-type: none;" class="my-4">
                                <li class="border-bottom pb-2" style=" border-bottom: 1px dashed #cccccc !important;">
                                <?php
                                if (trim($pkgSubCategory)) {
                                    $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory] = [];
                                    /**
                                     * Google Docs tool tip with video added..
                                     */ ?>

                                    <span class="expander">
                            <i class="fa fa-folder-open icon-2x text-success align-top"></i>
                            <span class="align-top ml-2"><?php echo $pkgSubCategory; ?>
                                <?php
                                if ($pkgSubCategory == 'Document Wizard Docs') { ?>
                                    <span class="float-right">
                                            <i class="fa fa-info-circle text-primary popoverClass"
                                               data-content="We upgraded our doc wizard tool,which now powered by Google Docs. You will need to copy the old templates over to Word, and upload the template to our new doc wizard tool. From there you will be able to further edit inside the doc wizard editor, which is a Google Docs editor. Note: All the merge tags will work the same way."></i>
                                        <a href="https://youtu.be/7M5PbeeAphg" class="popoverClass fab fa-youtube"
                                           target="_blank" title="Click Here For Demo"></a></span>
                                <?php } elseif ($pkgSubCategory == '4506T') { ?>
                                    <i class="fa fa-info-circle text-primary popoverClass float-right"
                                       data-content="You must enter the years requested under the Inc & Expense tab"></i>
                                <?php } ?>
                        </span>
                        </span>
                                <?php } ?>

                                <div class="<?php if (trim($pkgSubCategory) == '') { ?>subExpanderWOCat<?php } else { ?>subExpander<?php } ?>"
                                style="display:<?php if (trim($pkgSubCategory) == '') { ?>block<?php } else { ?>none<?php } ?>;">
                                <ol style="list-style-type: none;">
                                <?php
                            }
                            ?>
                            <li class="my-2"
                                style="<?php if ($PCID == 3406 && trim($tempPkgCat->PKGID) == 791 && $userRole != 'Manager') {
                                    echo 'display:none';
                                } else {
                                    echo 'display:block';
                                } ?>">
                                <?php
                                $packageType = '';
                                $expln = '';
                                $opt = '';

                                $packageName = trim($tempPkgCat->packageName);
                                $newDocId = trim($tempPkgCat->PKGID);
                                $digiSign = trim($tempPkgCat->digiSign);
                                $packageType = trim($tempPkgCat->packageType);
                                $expln = trim($tempPkgCat->explanation);
                                $docName = trim($tempPkgCat->docName); // this doesn't exist

                                $pkgUniqueID = $newDocId . '_' . $j . '_' . $k;

                                if ($digiSign == 1) $className = 'pdf_esignable';
                                else                $className = 'fa-file-pdf text-danger';
                                $disOpt = '';
                                $url = '';
                                $url2 = '';
                                $googleDocsId = '';
                                $docType = '';
                                if ($userGroup == 'Client') {
                                    $disOpt = 'disabled';
                                }
                                if ($packageType == 'static') {
                                    $filePath = '';
                                    $path_info = '';
                                    $fileExtension = '';
                                    $docName = '';
                                    $packageName = '';
                                    $filePath = trim($tempPkgCat->filePath);

                                    $docName = trim($tempPkgCat->docName); // does not exist
                                    $packageName = trim($tempPkgCat->packageName);
                                    $opt = trim($tempPkgCat->opt); // does not exist
                                    $googleDocsId = trim($tempPkgCat->googleDocsId); // does not exist
                                    $docType = trim($tempPkgCat->docType); // does not exist


                                    $path_info = pathinfo(CONST_PATH_LIBS_DOC . $filePath);

                                    if (count($path_info) > 0 && array_key_exists('extension', $path_info)) {
                                        $fileExtension = trim($path_info['extension']);
                                    }

                                    /** PC and Branch Documents Merge with Auto generated Docs **/
                                    if ($opt == 'PCDOC') {
                                        $url = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_PC_UP_DOC . $PCID) . '&opt=enc';
                                        if ($tempPkgCat->fileType) {
                                            $fileExtension = trim($tempPkgCat->fileType);
                                        }
                                    } elseif ($opt == 'BRDOC') {
                                        $url = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_FOLDER_BRANCH_DOC) . '&opt=enc';
                                        if ($tempPkgCat->fileType) {
                                            $fileExtension = trim($tempPkgCat->fileType);
                                        }
                                    } elseif ($opt == 'customDoc') {
                                        $fname = Strings::removeDisAllowedChars($clientName) . '-' . Strings::removeDisAllowedChars($packageName);
                                        if ($googleDocsId) {
                                            // luid = Logged In User ID.
                                            // lur  = Logged In User Role.
                                            // lugr = Logged In User Group.
                                            $loanNumber = Strings::removeDisAllowedChars($loanNumber); // data sanitize
                                            $clientName = Strings::removeDisAllowedChars($clientName); // data sanitize
                                            $url = CONST_CRON_SERVER_URL . 'backoffice/loan/create_custom_doc?clientName=' . $clientName . '&loanNumber=' . $loanNumber . '&file=' . base64_encode($packageName) . '&amp;rId=' . cypher::myEncryption($LMRResponseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($newDocId) . '&amp;opt=1lien&amp;luid=' . cypher::myEncryption(Strings::GetSess('userNumber')) . '&amp;lur=' . cypher::myEncryption(Strings::GetSess('userRole')) . '&amp;lugr=' . cypher::myEncryption(Strings::GetSess('userGroup')) . '&amp;fileNameEnc=1';
                                        } else {
                                            $url = CONST_SITE_URL . 'backoffice/loan/create_custom_doc?clientName=' . $clientName . '&loanNumber=' . $loanNumber . '&file=' . base64_encode($packageName) . '&amp;rId=' . cypher::myEncryption($LMRResponseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($newDocId) . '&amp;opt=1lien' . '&amp;fileNameEnc=1';
                                        }
                                    } else {
                                        $url = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($filePath) . '&fd=' . cypher::myEncryption(CONST_FOLDER_LIB_DOC) . '&opt=enc';
                                    }

                                    if ($fileExtension == 'xls' || $fileExtension == 'xlsx') {
                                        $className = 'fa-file-excel text-success';
                                    } elseif ($fileExtension == 'doc' || $fileExtension == 'docx') {
                                        $className = 'fa-file-word';
                                    } else {
                                        $className = 'fa-file-pdf text-danger';
                                    }

                                } else {
                                    $fname = Strings::removeDisAllowedChars($clientName) . '-' . Strings::removeDisAllowedChars($packageName) . '.pdf';

                                    if ($newDocId == '495' && $servicer2 == '') {
                                        $url = '';
                                    } else {
                                        $url = CONST_SITE_URL . 'package/pkgController.php?file=' . $fname . '&amp;rId=' . cypher::myEncryption($LMRResponseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($newDocId) . '&opt=sample';
                                        if (in_array($newDocId, $glGuarantyDynamicDocs)) {
                                            $url .= '&agur=' . $aGurn;
                                            $aGurn++;
                                        }
                                    }
                                    $packageName2 = '';
                                    $url2 = '';
                                    if ($newDocId == 286 || $newDocId == 147) {
                                        if ($servicer2) {
                                            $packageName2 = $packageName . ' - 2nd';
                                            $url2 = CONST_SITE_URL . 'package/pkgController.php?file=' . $fname . '&amp;rId=' . cypher::myEncryption($LMRResponseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($newDocId) . '&opt=2lien';
                                        }
                                        $packageName = $packageName . ' - 1st';
                                        $url = CONST_SITE_URL . 'package/pkgController.php?file=' . $fname . '&amp;rId=' . cypher::myEncryption($LMRResponseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($newDocId) . '&opt=1lien';
                                    }
                                }

                                ?>
                                <div class="col-12">
                                    <div class="row my-2">
                                        <?php
                                        if ($url) {
                                            $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory][$newDocId]['name'] = $packageName;
                                            $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory][$newDocId]['DocId'] = $newDocId;
                                            $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory][$newDocId]['url'] = $url;

                                            if ($isDIYUserLogin != 1) {
                                                if ($digiSign == 1) {
                                                    $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory][$newDocId]['signable'] = true;
                                                    ?>
                                                    <div class="col-1" data-opt="signDoc">
                                                        <div class="checkbox-list">
                                                            <label class="checkbox checkbox-outline checkbox-square"
                                                                   for="pkg_<?php echo $pkgUniqueID ?>">
                                                                <input data-doc-type="signDocument"
                                                                       class="newCheck1 signable" type="checkbox"
                                                                       name="pkg_<?php echo $newDocId ?>"
                                                                       id="pkg_<?php echo $pkgUniqueID ?>"
                                                                       value="<?php echo $newDocId ?>" <?php echo $disOpt ?>
                                                                       data-doc_name="<?php echo $docName; ?>"
                                                                       doc-name="<?php echo htmlentities($packageName); ?>"
                                                                       doc-url="<?php echo $url; ?>"
                                                                       doc-type="fas fa-file-signature text-primary  "
                                                                       onchange="getSelectedPKGID(); fileDocs.checkIfESigned(this.checked, this.value, 'pkg_<?php echo $pkgUniqueID ?>', '<?php echo cypher::myEncryption($LMRId) ?>');"><span></span></label>
                                                        </div>
                                                    </div>
                                                    <?php
                                                } else {
                                                    $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory][$newDocId]['unSignable'] = true;

                                                    if ($opt == 'PCDOC') {
                                                        /** PC Documents Selected. **/ ?>

                                                        <div class="col-1" data-opt="PCDOC">
                                                            <div class="checkbox-list">
                                                                <label class="checkbox checkbox-outline checkbox-square"
                                                                       for="PCDoc_<?php echo $newDocId ?>">
                                                                    <input type="checkbox"
                                                                           class="newCheck1 unsignable pcdocAttachable"
                                                                           name="PCDoc[]"
                                                                           id="PCDoc_<?php echo $newDocId ?>"
                                                                           value="<?php echo $newDocId ?>"
                                                                           data-doc_name="<?php echo $docName; ?>"
                                                                           data-filesize="<?php echo $tempFileSize; ?>"
                                                                           doc-name="<?php echo $packageName; ?>"
                                                                           doc-url="<?php echo $url; ?>"
                                                                           doc-type="<?php echo $className; ?>"
                                                                           onchange="getPCSelectedPKGID();"><span></span></label>
                                                            </div>
                                                        </div>

                                                    <?php } else if ($opt == 'BRDOC') {
                                                        /** Branch Documents Selected. **/ ?>
                                                        <div class="col-1" data-opt="BRDOC">
                                                            <div class="checkbox-list d-inline-block">
                                                                <label class="checkbox checkbox-outline checkbox-square"
                                                                       for="branchDoc_<?php echo $newDocId ?>"><input
                                                                            class="newCheck1 unsignable branchAttachable"
                                                                            type="checkbox"
                                                                            name="branchDoc[]"
                                                                            id="branchDoc_<?php echo $newDocId ?>"
                                                                            value="<?php echo $newDocId ?>"
                                                                            data-doc_name="<?php echo $docName; ?>"
                                                                            doc-name="<?php echo $packageName; ?>"
                                                                            doc-url="<?php echo $url; ?>"
                                                                            doc-type="<?php echo $className; ?>"
                                                                            onchange="getBranchSelectedPKGID();"><span></span></label>
                                                            </div>
                                                        </div>

                                                        <?php
                                                    } elseif ($opt == 'customDoc') {
                                                        /** Custom Documents Selected. **/
                                                        if ($googleDocsId) {
                                                            if ($docType != 'csv' && $docType != 'xlsx') { ?>
                                                                <div class="col-1" data-opt="customDoc">

                                                                    <div class="checkbox-list d-inline-block">
                                                                        <label class="checkbox checkbox-outline checkbox-square"
                                                                               for="customDoc_<?php echo $newDocId ?>"><input
                                                                                    class="newCheck1 unsignable googleDocument"
                                                                                    type="checkbox"
                                                                                    name="customDoc[]"
                                                                                    id="customDoc_<?php echo $newDocId ?>"
                                                                                    value="<?php echo $newDocId ?>"
                                                                                    data-doc_name="<?php echo $docName; ?>"
                                                                                    doc-name="<?php echo $packageName; ?>"
                                                                                    doc-url="<?php echo $url; ?>"
                                                                                    doc-type="<?php echo $className; ?>"
                                                                                    onchange="getGoogleDocumentsSelectedPKGID();"><span></span></label>
                                                                    </div>
                                                                </div>

                                                                <?php
                                                            }
                                                        }
                                                    } else { ?>

                                                        <div class="col-1" data-opt="otherDoc">
                                                            <div class="checkbox-list ">
                                                                <label class="checkbox checkbox-outline checkbox-square"
                                                                       for="pkg_<?php echo $pkgUniqueID ?>"><input
                                                                            class="newCheck1 nonESign_<?php echo $newDocId; ?> unsignable"
                                                                            type="checkbox"
                                                                            name="pkg_<?php echo $newDocId ?>"
                                                                            id="pkg_<?php echo $pkgUniqueID ?>"
                                                                            value="<?php echo $newDocId ?>"
                                                                        <?php echo $disOpt ?>
                                                                            data-doc_name="<?php echo $docName; ?>"
                                                                            doc-name="<?php echo $packageName; ?>"
                                                                            doc-url="<?php echo $url; ?>"
                                                                            doc-type="<?php echo $className; ?>"
                                                                            onchange="getSelectedPKGID(); fileDocs.checkIfESigned(this.checked, this.value, 'pkg_<?php echo $pkgUniqueID ?>', '<?php echo cypher::myEncryption($LMRId) ?>');"><span></span></label>
                                                            </div>
                                                        </div>
                                                        <?php
                                                    }
                                                }
                                            }
                                            if ($digiSign == 1) {
                                                $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory][$newDocId]['signable'] = true;
                                                ?>
                                                <div class="col-1" title="E-Signable For Borrower">
                                                    <a class="fas fa-file-signature text-primary "
                                                       title="E-Signable For Borrower"
                                                       target="_blank" style="text-decoration:none;" rel="nofollow"
                                                       href="<?php echo $url ?>"></a>
                                                </div>
                                                <?php
                                            } else {
                                                $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory][$newDocId]['unSignable'] = true;
                                                if ($docType != 'csv' && $docType != 'xlsx') { ?>
                                                    <div class="col-1"><a class="fa <?php echo $className ?> "
                                                                          style="text-decoration:none;" rel="nofollow"
                                                                          target="_blank"
                                                                          href="<?php echo $url ?>"></a></div>
                                                    <?php
                                                }
                                            }
                                            ?>
                                            <div class="col-10">
                                                <?php
                                                if ($expln) { ?>
                                                    <i class="fa fa-info-circle text-primary"
                                                       rel="nofollow"
                                                       title="<?php echo $expln ?>"></i>
                                                    <?php
                                                }
                                                if ($docType != 'csv' && $docType != 'xlsx') { ?>
                                                    <a target="_blank"
                                                       href="<?php echo $url ?>"
                                                       rel="nofollow"> <?php echo $packageName ?></a>
                                                    <?php
                                                }
                                                ?>
                                                <div class="checkbox-inline mt-2 docsSignUsers<?php echo $newDocId; ?>"></div>
                                            </div>
                                            <?php
                                        } ?>
                                    </div>
                                    <div class="row my-2">

                                        <?php if ($url2) { ?>
                                            <?php if ($isDIYUserLogin != 1) { ?>
                                                <div class="col-1">
                                                    <div class="checkbox-list">
                                                        <label class="checkbox checkbox-outline checkbox-square"
                                                               for="pkg_<?php echo $pkgUniqueID ?>">
                                                            <input class="newCheck1 unsignable"
                                                                   type="checkbox"
                                                                   name="pkg_<?php echo $newDocId ?>"
                                                                   id="pkg_<?php echo $pkgUniqueID ?>"
                                                                   value="<?php echo $newDocId ?>" <?php echo $disOpt ?>
                                                                   doc-name="<?php echo $packageName; ?>"
                                                                   doc-url="<?php echo $url; ?>"
                                                                   doc-type="<?php echo $className; ?>"
                                                                   onchange="getSelectedPKGID(); fileDocs.checkIfESigned(this.checked, this.value, 'pkg_<?php echo $pkgUniqueID ?>', '<?php echo cypher::myEncryption($LMRId) ?>');"><span></span></label>
                                                    </div>
                                                </div>
                                            <?php } ?>
                                            <div class="col-1">
                                                <a class="fa <?php echo $className ?> "
                                                   target="_blank"
                                                   rel="nofollow"
                                                   href="<?php echo $url2 ?>"></a>
                                            </div>
                                            <div class="col-10">
                                                <a target="_blank" rel="nofollow"
                                                   href="<?php echo $url2 ?>"><?php echo $packageName2 ?></a>
                                            </div>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                    <div class="row my-2 ">
                                        <?php
                                        if ($digiSign == 1 && $url) {
                                            $AutoGenModuleWise[$loanAutoGen][$pkgSubCategory][$newDocId]['unSignable'] = true;
                                            if ($isDIYUserLogin != 1) { ?>
                                                <div class="col-1">
                                                    <div class="checkbox-list">
                                                        <label class="checkbox checkbox-outline checkbox-square"
                                                               for="pkg_<?php echo $pkgUniqueID ?>_attachable"><input
                                                                    class="newCheck1 unsignable attachable"
                                                                    type="checkbox"
                                                                    name="pkg_<?php echo $newDocId ?>"
                                                                    id="pkg_<?php echo $pkgUniqueID ?>_attachable"
                                                                    value="<?php echo $newDocId ?>" <?php echo $disOpt ?>
                                                                    data-filesize="<?php echo $tempFileSize; ?>"
                                                                    doc-name="<?php echo $packageName; ?>"
                                                                    doc-url="<?php echo $url; ?>"
                                                                    doc-type="fa fa-file-pdf  text-danger"
                                                                    onchange="getSelectedAttachedPKGID();"><span></span></label>
                                                    </div>
                                                </div>
                                            <?php } ?>
                                            <div class="col-1" title="Non E-Signable Format">
                                                <a class="fa fa-file-pdf  text-danger"
                                                   title="Non E-Signable Format"
                                                   target="_blank" rel="nofollow"
                                                   href="<?php echo $url ?>"></a>
                                            </div>
                                            <div class="col-10">
                                                <?php
                                                if ($expln) { ?>
                                                    <i class="fa fa-info-circle text-primary"
                                                       title="<?php echo $expln ?>"></i>
                                                    <?php
                                                } ?>
                                                <a target="_blank" rel="nofollow"
                                                   href="<?php echo $url ?>"><?php echo $packageName ?></a>
                                            </div>
                                            <?php
                                            $attachChkPkg++;
                                        } ?>
                                    </div>
                                    <div class="row my-2 ">
                                        <?php if ($opt == 'customDoc' && $url) { ?>
                                            <?php if ($googleDocsId) { ?>
                                                <div class="col-1">
                                                    <div class="checkbox-list d-inline-block">
                                                        <label class="checkbox checkbox-outline checkbox-square"
                                                               for="customWordDoc_<?php echo $newDocId ?>"><input
                                                                    class="newCheck1 unsignable googleSheet"
                                                                    type="checkbox"
                                                                    name="customWordDoc[]"
                                                                    id="customWordDoc_<?php echo $newDocId ?>"
                                                                    value="<?php echo $newDocId ?>"
                                                                    data-doctype="<?php echo $docType; ?>"
                                                                    doc-name="<?php echo $packageName; ?>"
                                                                    doc-url="<?php echo $url; ?>"
                                                                    doc-type="fa fa-file-word text-success"
                                                                    onchange="getCustomGoogleSheetSelectedPKGID();"><span></span></label>
                                                    </div>
                                                </div>
                                            <?php } ?>

                                            <?php if ($googleDocsId && $docType == 'csv') { ?>
                                                <div class="col-1">
                                                    <a class="fa fa-file-excel text-success "
                                                       target="_blank" rel="nofollow"
                                                       href="<?php echo $url . '&open=' . cypher::myEncryption('C') ?>"></a>
                                                </div>
                                                <div class="col-10">
                                                    <a target="_blank" rel="nofollow"
                                                       href="<?php echo $url . '&open=' . cypher::myEncryption('C') ?>"><?php echo $packageName ?></a>
                                                </div>
                                            <?php } elseif ($googleDocsId && $docType == 'xlsx') { ?>
                                                <div class="col-1">
                                                    <a class="fa fa-file-excel text-success "
                                                       target="_blank"
                                                       rel="nofollow"
                                                       href="<?php echo $url . '&open=' . cypher::myEncryption('C') ?>"></a>
                                                </div>
                                                <div class="col-10">
                                                    <a target="_blank" rel="nofollow"
                                                       href="<?php echo $url . '&open=' . cypher::myEncryption('X') ?>"><?php echo $packageName ?></a>
                                                </div>
                                            <?php } else { ?>
                                                <div class="col-1">
                                                    <a class="fa fa-file-word text-primary " target="_blank"
                                                       rel="nofollow"
                                                       href="<?php echo $url . '&open=' . cypher::myEncryption('D') ?>"></a>
                                                </div>
                                                <div class="col-10">
                                                    <a target="_blank" rel="nofollow"
                                                       href="<?php echo $url . '&open=' . cypher::myEncryption('D') ?>"><?php echo $packageName ?></a>
                                                </div>
                                            <?php } ?>


                                            <?php
                                            $attachCustWordPkg++;
                                        } ?>
                                    </div>
                                </div>
                                <div class="separator separator-dashed my-2"></div>
                            </li>
                            <?php
                            $tempPkgSubCategory = $pkgSubCategory;
                        }  /* for loop end */
                        if ($subCatCnt > 0) { ?>

                            </ol>
                            </div>
                            </li>
                            </ol><!-- Tree View End -->
                            <?php
                        }
                        if (array_key_exists($fileModule, $tempPkgGroups)) {
                            echo '</div>';
                            /** Category Div End **/
                            $newRow++;
                        }
                        if ($newRow == 4) {
                            echo '</div>';
                            /** 4 category Main Div End **/
                            $newRow = 0;
                        }
                    }
                }
                if ($newRow > 0) {
                /** 4 category Main Div End - Missing **/
                ?>
            </div>
            <?php
            }
            ?>
        </div> <!-- divList end  -->
    </div>
    <!-- autoGeneratedDocsList.php -->
    <?php
} // for loop end Standard/Custom.

