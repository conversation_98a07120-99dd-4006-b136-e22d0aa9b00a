<?php

use models\composite\oFileUpdate\saveGiftsOrGrants;
use models\composite\oFileUpdate\saveOtherNewMortgageLoansOnProperty;
use models\composite\oHMLOInfo\saveHMLOAssetsInfo;
use models\composite\oIncExp\creditorsLiabilitiesSave;
use models\composite\oIncExp\saveAssetsInfo;
use models\composite\oIncExp\saveMDAInfo;
use models\composite\oLoanOrigination\LOLiabilitiesInfoSave;
use models\composite\oLoanOrigination\saveFinanceAndSecurities;
use models\composite\oLoanOrigination\saveLOAssetsInfo;
use models\composite\oLoanOrigination\scheduleRealEstateSave;
use models\composite\oPC\isPCAllowedAutomation;
use models\Controllers\LMRequest\contingentLiabilities as contingentLiabilitiesController;
use models\Controllers\LMRequest\partnerShips;
use models\CustomField;
use models\cypher;
use models\Database2;
use models\lendingwise\tblAutomatedRuleRequestV2;
use models\lendingwise\tblFile;
use models\lendingwise_log\ChangeLog;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';
require 'initPageVariables.php';
require 'getPageVariables.php';

//global variables
global $userGroup, $PCID, $redirect, $userRole;
//page variables
$branchReferralCode = $agentReferralCode = $fOpt = '';
UserAccess::checkReferrerPgs(['url' => 'LMRequest.php, loanModificationPrequalRemote.php']);

$publicUser = 0;
if (isset($_POST['publicUser'])) $publicUser = trim($_POST['publicUser']);
if ($publicUser != 1) {
    UserAccess::CheckAdminUse([]);
}
$executiveId = 0;
$brokerNumber = 0;
$LMRId = 0;
$goToTab = '';
$activeTab = '';
$btnValue = '';
$responseId = 0;
$assetId = 0;
$isLOOpt = 0;
$isHMLOOpt = 0;
$op = '';
$clientId = '';
if (isset($_POST['publicUser'])) $publicUser = trim($_POST['publicUser']);
if (isset($_POST['encryptedEId'])) $executiveId = cypher::myDecryption(trim($_POST['encryptedEId']));
if (isset($_POST['encryptedBId'])) $brokerNumber = cypher::myDecryption(trim($_POST['encryptedBId']));
if (isset($_POST['encryptedLId'])) $LMRId = cypher::myDecryption(trim($_POST['encryptedLId']));
if (isset($_POST['encryptedCId'])) $clientId = cypher::myDecryption(trim($_POST['encryptedCId']));
if (isset($_POST['encryptedRId'])) $responseId = trim(cypher::myDecryption(trim($_POST['encryptedRId'])));
if (isset($_POST['goToTab'])) $goToTab = trim($_POST['goToTab']);
if (isset($_POST['activeTab'])) $activeTab = trim($_POST['activeTab']);
if (isset($_POST['btnSave'])) $btnValue = trim($_POST['btnSave']);
if (isset($_POST['assetId'])) $assetId = trim($_POST['assetId']);

if (isset($_POST['isLOOpt'])) $isLOOpt = trim($_POST['isLOOpt']);
if (isset($_POST['isHMLOOpt'])) $isHMLOOpt = trim($_POST['isHMLOOpt']);

if (isset($_POST['op'])) $op = trim($_POST['op']);

if ($op == '') $op = cypher::myEncryption('edit');

if (isset($_REQUEST['LMRId'])) $LMRId = trim($_REQUEST['LMRId']);
if (isset($_REQUEST['LOLID'])) $LOLID = $_REQUEST['LOLID'];
if (isset($_REQUEST['nameAddrOfCompany'])) $nameAddrOfCompany = $_REQUEST['nameAddrOfCompany'];
if (isset($_REQUEST['monthlyPaymentExpenses'])) $monthlyPayment = $_REQUEST['monthlyPaymentExpenses'];
if (isset($_REQUEST['monthsLeftToPay'])) $monthsLeftToPay = $_REQUEST['monthsLeftToPay'];
if (isset($_REQUEST['unpaidBalanceExpenses'])) $unpaidBalance = $_REQUEST['unpaidBalanceExpenses'];
if (isset($_REQUEST['accountNo'])) $accountNo = $_REQUEST['accountNo'];
if (isset($_REQUEST['liabilityAccType'])) $liabilityAccType = $_REQUEST['liabilityAccType'];
if (isset($_REQUEST['liabilityAtorBeforeClose'])) $liabilityAtorBeforeClose = $_REQUEST['liabilityAtorBeforeClose'];
if (isset($_REQUEST['deletedLOLId'])) $deletedLOLId = $_REQUEST['deletedLOLId'];

$allowRepeat = Request::GetClean('allowRepeat') ?? '';
$triggerRule = Request::GetClean('triggerRule') ?? 'No';

if (!$LMRId) {
    header('Location: ' . 'LMRequest.php');
    exit();
}

if($_SERVER['REQUEST_METHOD'] === 'POST') {
    ChangeLog::LogChanges(
        tblFile::class,
        $LMRId,
        basename(__FILE__, '.php'),
        $_REQUEST,
        PageVariables::$userNumber
    );
}

$ip = [
    'p' => $_POST,
    'LMRId' => $LMRId,
    'responseId' => $responseId,
    'branchId' => $executiveId,
    'agentId' => $brokerNumber,
    'assetId' => $assetId,
    'UID' => PageVariables::$userNumber,
    'UGroup' => $userGroup,
    'PCID' => $PCID,
    'saveTab' => 'AL',
];

if ($isLOOpt != 1 && $isHMLOOpt != 1) {
    saveMDAInfo::getReport($ip);
}

if ($LMRId > 0) {
    saveAssetsInfo::getReport($ip);
    creditorsLiabilitiesSave::getReport($ip);
}
$inputArray = ['p' => $_POST,
    'LMRId' => $LMRId,
    'clientId' => $clientId,
];

if ($LMRId > 0 && ($isLOOpt == 1 || $isHMLOOpt == 1)) {
    saveLOAssetsInfo::getReport($inputArray);
    saveFinanceAndSecurities::getReport($inputArray);
}

$HMLOArray = ['p' => $_POST,
    'LMRId' => $LMRId,
];

if ($LMRId > 0 && ($isHMLOOpt == 1)) {
    saveHMLOAssetsInfo::getReport($HMLOArray);
}
/* contingentLiabilities */
contingentLiabilitiesController::saveData($_REQUEST['contingentLiabilitiesFields'], $LMRId,$PCID);
/* contingentLiabilities */
saveOtherNewMortgageLoansOnProperty::getReport($ip);
saveGiftsOrGrants::getReport($ip);
$InArray = ['LMRId' => $LMRId,
    'LOLID' => $LOLID,
    'nameAddrOfCompany' => $nameAddrOfCompany,
    'monthlyPayment' => $monthlyPayment,
    'monthsLeftToPay' => $monthsLeftToPay,
    'unpaidBalance' => $unpaidBalance,
    'accountNo' => $accountNo,
    'liabilityAccType' => $liabilityAccType,
    'liabilityAtorBeforeClose' => $liabilityAtorBeforeClose,
    'deletedLOLId' => $deletedLOLId,
];
LOLiabilitiesInfoSave::getReport($InArray);
$scheduleestate = [
    'LMRId' => $LMRId,
    'schedulePropAddr' => $_POST['schedulePropAddr'],
    'schedulePropCity' => $_POST['schedulePropCity'],
    'schedulePropState' => $_POST['schedulePropState'],
    'schedulePropZip' => $_POST['schedulePropZip'],
    'scheduleStatus' => $_POST['scheduleStatus'],
    'propType' => $_POST['propType'],
    'presentMarketValue' => $_POST['presentMarketValue'],
    'amountOfMortgages' => $_POST['amountOfMortgages'],
    'grossRentalIncome' => $_POST['grossRentalIncome'],
    'mortgagePayments' => $_POST['mortgagePayments'],
    'insMaintTaxMisc' => $_POST['insMaintTaxMisc'],
    'netRentalIncome' => $_POST['netRentalIncome'],
    'scheduleID' => $_POST['scheduleID'],
    'titledUnder' => $_POST['titledUnder'],
    'datePurchased' => $_POST['datePurchased'],
    'purchasePrice' => $_POST['purchasePrice'],
    'valueofImprovementsMade' => $_POST['valueofImprovementsMade'],
    'intendedOccupancy' => $_POST['intendedOccupancy'],
    'p' => $_POST,
];
scheduleRealEstateSave::getReport($scheduleestate);

partnerShips::saveData($_REQUEST['partnerShipFields'], $LMRId);

//check if the PC is enabled for Automation
$allowAutomation = 0;
$PCID = intval(Strings::GetSess('PCID')) ?? null;
//allow automation
$allowAutomation = isPCAllowedAutomation::getReport($PCID);
if ($allowAutomation) { //$allowAutomation set in getPageVariables.php
    include('automatedRulesActionController.php');
}

tblAutomatedRuleRequestV2::Trigger($LMRId, $PCID); // last
CustomField::Save(
    tblFile::class,
    $LMRId,
    $PCID,
    PageVariables::$userNumber,
    PageVariables::$userRole,
    PageVariables::$userGroup
);
Database2::saveLogQuery();

if(!Strings::GetSess('msg')) {
    Strings::SetSess('msg', 'Updated Successfully');
}

if ($publicUser == 1) {
    if (isset($_POST['branchReferralCode'])) $branchReferralCode = trim($_POST['branchReferralCode']);
    if (isset($_POST['agentReferralCode'])) $agentReferralCode = trim($_POST['agentReferralCode']);
    if (isset($_POST['fOpt'])) $fOpt = trim($_POST['fOpt']);
    $redirect .= CONST_SITE_URL . 'loanModificationPrequalRemote.php?rsc=' . $branchReferralCode . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&lId=' . cypher::myEncryption($LMRId) . '&fOpt=' . cypher::myEncryption($fOpt) . '&tabOpt=';
    if ($btnValue == 'Save') $redirect .= $activeTab;
    else $redirect .= $goToTab;
    header('Location: ' . $redirect);

} else {
    if ($userRole == 'Branch') $redirect = CONST_URL_BRSSL;
    else if ($userRole == 'Agent') $redirect = CONST_URL_AG_SSL;
    else if (($userRole == 'Client') && (preg_match('/client_new\//i', $_SERVER['HTTP_REFERER']))) $redirect = CONST_URL_CL_NEW_SSL;
    else if ($userRole == 'Client') $redirect = CONST_URL_CL_SSL;
    else    $redirect = CONST_URL_BOSSL;

    $redirect .= 'LMRequest.php?eId=' . cypher::myEncryption($executiveId) . '&lId=' . cypher::myEncryption($LMRId) . '&rId=' . cypher::myEncryption($responseId) . '&op=' . trim($op) . '&tabOpt=';
    if ($btnValue == 'Save' || Arrays::getArrayValue('isSave', $_REQUEST) == 1 ) $redirect .= $activeTab;

    else $redirect .= $goToTab;
    header('Location: ' . $redirect);
}



exit();

