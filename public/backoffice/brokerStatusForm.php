<?php

global $PCID;

use models\composite\oBroker\getBrokerStatusInfo;
use models\constants\brokerStatusArray;
use models\constants\gl\glPCID;
use models\Controllers\backoffice\createAgent;
use models\standard\Arrays;
use models\standard\Dates;

if($PCID == glPCID::PCID_PROD_CV3) {
    $brokerStatusArray = brokerStatusArray::$brokerStatusArrayCV3;
} else {
    $brokerStatusArray = brokerStatusArray::$brokerStatusArray;
}

$activeStatusInfo = [];
$brokerStatusInfo = [];
$brokerStatus = null;
$notesStatusInfo = [];
$mergedNotesArray = [];
$notesRecord = '';
$brokerStatusInfo = getBrokerStatusInfo::getReport([
        'brokerNumber' => createAgent::$brokerNo,
        'PCID' => $PCID
]);

if (array_key_exists('activeInfo', $brokerStatusInfo)) {
    $activeStatusInfo = $brokerStatusInfo['activeInfo'];
}

if (array_key_exists('notesInfo', $brokerStatusInfo)) {
    $notesStatusInfo = $brokerStatusInfo['notesInfo'];
}

$expirationDate = null;
$statusDate = null;
$brokerNotes = null;

if (count($activeStatusInfo)) {
    $brokerStatus = $activeStatusInfo[0]['brokerStatus'];
    $brokerNotes = $activeStatusInfo[0]['brokerNotes'];
    $notesRecord = $activeStatusInfo[0]['recordDate'];
    $expirationDate = $activeStatusInfo[0]['expirationDate'];
    $statusDate = $activeStatusInfo[0]['statusDate'];
}

$mergedNotesArray = array_merge($notesStatusInfo, $activeStatusInfo);

?>
<div class="row">
    <div class="col-lg-6">
        <div class="card card-custom p-0 m-0">
            <div class="card-body p-2 m-0">
                <div class="mb-7">
                    <label class="font-weight-bold" for="brokerStatus">Approval Status </label>
                    <div>
                        <?php if (createAgent::$allowAgentEdit) { ?>
                        <select name="brokerStatus" id="brokerStatus" class="form-control input-sm">
                            <option value="">- Select -</option>
                            <?php
                            $spKeyArray = array_keys($brokerStatusArray);
                            for ($sp = 0; $sp < count($spKeyArray); $sp++) {
                                $spKey = $spKeyArray[$sp];
                                $myBrokerStatus = $brokerStatusArray[$spKey];
                                ?>
                                <option value="<?php echo $spKey; ?>" <?php echo Arrays::isSelected($spKey, $brokerStatus); ?>><?php echo $myBrokerStatus; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <?php } else {
                        echo '<h5>' . $brokerStatus . '</h5>';
                    } ?>
                </div>
                <div class="mb-7">
                    <label class="font-weight-bold" for="expirationDate">Expiration Date</label>
                    <?php if (createAgent::$allowAgentEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend expirationDate">
                                    <span class="input-group-text">
                                    <i class="fa fa-calendar text-primary"></i>
                                    </span>
                            </div>
                            <input class="form-control input-sm dateNewClass"
                                   name="expirationDate"
                                   id="expirationDate"
                                   value="<?php echo Dates::formatDateWithRE($expirationDate, 'YMD', 'm/d/Y') ?>"
                                   autocomplete="off"
                                   maxlength="10"
                                   placeholder="MM/DD/YYYY"/>
                        </div>
                    <?php } else {
                        echo '<h5>' . $expirationDate . '</h5>';
                    } ?>
                </div>
                <div class="mb-7">
                    <label class="font-weight-bold" for="statusDate">Status Date</label>
                    <?php if (createAgent::$allowAgentEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend statusDate">
                                    <span class="input-group-text">
                                    <i class="fa fa-calendar text-primary"></i>
                                    </span>
                            </div>
                            <input class="form-control input-sm"
                                   name="statusDate"
                                   id="statusDate"
                                   value="<?php echo Dates::formatDateWithRE($statusDate, 'YMD', 'm/d/Y') ?>"
                                   autocomplete="off"
                                   maxlength="10"
                                   placeholder="MM/DD/YYYY"/>
                        </div>
                    <?php } else {
                        echo '<h5>' . $statusDate . '</h5>';
                    } ?>
                </div>
                <div class="mb-7">
                    <label class="col-md-12 font-weight-bold" for="address">Notes</label>
                    <div>
                        <?php if (createAgent::$allowAgentEdit) { ?>
                            <textarea name="brokerNotes" id="brokerNotes" class="form-control"></textarea>
                        <?php } else {
                            echo '<h5>' . $brokerNotes . '</h5>';
                        } ?>
                    </div>
                    <input type="hidden" name="statusOpt" id="statusOpt" value="4">
                    <input type="hidden" name="externalBroker" id="externalBroker"
                           value="<?php echo createAgent::Agent()->externalBroker; ?>">
                    <input type="hidden" id="PCID" name="PCID" value="<?php echo $PCID; ?>">
                </div>
                <div class="row d-flex justify-content-center">
                    <button type="submit" name="submit" id="submit"
                            class="btn btn-primary font-weight-bold">Save
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card card-custom p-0 m-0">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">Notes History</h3>
                </div>
            </div>
            <div class="card-body p-2 m-0">
                <ul class="list-group p-1 font-size-sm">
                    <li class="list-group-item px-2 py-1 text-success"># Notes</li>
                    <?php for ($s = 0; $s < count($mergedNotesArray); $s++) {
                        $clsName = '';
                        if (($s + 1) % 2 == 0) $clsName = 'bg-gray-100';
                        ?>

                        <li class="list-group-item px-2 py-1  <?php echo $clsName; ?>"><?php echo $mergedNotesArray[$s]['brokerNotes'] . ' ( ' . $mergedNotesArray[$s]['recordDate'] . ' )'; ?></li>
                        <?php
                    } ?>
                </ul>
            </div>
        </div>
    </div>
</div>


<script>
    var LWFormControls = function () {

        var _formSubmitValidation = function () {
            var formIdToSubmit = $('#newBrokerForm');
            formIdToSubmit.validate({
                ignore: ".ignoreValidation",
                rules: {
                    brokerStatus: "required",
                },
                messages: {
                    brokerStatus: "Please Select Broker Status",
                },
                errorElement: "em",
                errorPlacement: function (error, element) {
                    // Add the `invalid-feedback` class to the error element
                    error.addClass("invalid-feedback");

                    if (element.prop("type") === "checkbox") {
                        error.insertAfter(element.next("label"));
                    } else {
                        if (element.prop("name") == 'role') {
                            element.next('.input-group-append').after(error);
                        } else {
                            error.insertAfter(element);
                        }
                    }
                },
                highlight: function (element, errorClass, validClass) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).addClass("is-valid").removeClass("is-invalid");
                },
                submitHandler: function (form) {

                    let ajaxUrl = $(formIdToSubmit).attr('action');
                    let formData = $(formIdToSubmit).serialize();

                    $.ajax({
                        url: ajaxUrl,
                        type: "POST",
                        data: formData,
                        beforeSend: function () {
                            BlockDiv('employeeCreateDiv');
                        },
                        complete: function () {
                            UnBlockDiv('employeeCreateDiv');
                        },
                        success: function (response, status, xhr) {
                            //closeModal();
                            let res = JSON.parse(response);
                            if (res.code == 100) {
                                toastrNotification(res.msg, 'success');
                                setTimeout(function () {
                                    window.location.href = res.redirecturl;
                                }, 3000);

                            } else {
                                toastrNotification(res.msg, 'error');
                            }
                        },
                        error: function (jqXhr, textStatus, errorMessage) {
                            //toastrNotification(errorMessage, 'error');
                            $.alert({
                                icon: 'fa fa-warning',
                                type: 'red',
                                title: 'Alert!',
                                content: errorMessage,
                            });
                        }
                    });
                }
            });
        }
        return {
            // public functions
            init: function () {
                _formSubmitValidation();
            }
        };
    }();

    $(document).ready(function () {
        LWFormControls.init();
    });
    $(function() {
        $('#statusDate').datepicker({changeMonth: true, changeYear: true, dateFormat: 'mm/dd/yy',autoclose:false,startDate: '01/01/1900', orientation: 'bottom',});
        $('#expirationDate').datepicker({changeMonth: true, changeYear: true, dateFormat: 'mm/dd/yy',autoclose:false,startDate: '01/01/1900', orientation: 'bottom',});
    });
</script>

<!-- brokerStatusForm.php -->
