<!-- HMLO Layout Modification Jan 04, 2017-->
<?php
global $isHMLO, $allowToEdit, $tabIndex, $tabindex, $GCCompanyName, $GCFirstName, $GCLastName, $GCEmail, $GCPhone, $GCLicense, $GCContactID,
       $serviceLenderName;

use models\constants\gl\glPropertyConstructionLevel;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Strings;

$glPropertyConstructionLevel = glPropertyConstructionLevel::$glPropertyConstructionLevel;
$propertyNeedRehab = Strings::showField('propertyNeedRehab', 'fileHMLOPropertyInfo');
$isHiredPerformRehab = Strings::showField('isHiredPerformRehab', 'fileHMLOPropertyInfo');
$rehabStrategyPlans = Strings::showField('rehabStrategyPlans', 'fileHMLOPropertyInfo');
$propertyConstructionLevel = Strings::showField('propertyConstructionLevel', 'fileHMLOPropertyInfo');

if ($isHMLO == 1) { ?>
    <div class="card card-custom rehabConstructionCost">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    Rehab/Construction Costs
                </h3>
            </div>
            <div class="card-toolbar ">
                <a href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="rehabConstructionCost"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                    <i class="ki ki-close icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body rehabConstructionCost_body">
            <div class="row">
                <div class="form-group col-md-4 col-sm-6 font-weight-bold">Will the property need rehab or
                    construction?
                </div>
                <div class="form-group col-md-3 col-sm-6">
                    <?php if ($allowToEdit) { ?>
                        <input type="radio" name="propertyNeedRehab" value="Yes"
                               tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $propertyNeedRehab); ?>
                               onclick="showAndHideLODiv(this.value, 'doesPropertyNeedRehabDispDiv');">Yes
                        <input type="radio" name="propertyNeedRehab" value="No"
                               tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $propertyNeedRehab); ?>
                               onclick="showAndHideLODiv(this.value, 'doesPropertyNeedRehabDispDiv');">No
                        <?php
                    } else {
                        echo '<h5>' . $propertyNeedRehab . '</h5>';
                    }
                    ?>
                </div>
            </div>
            <div class="row">
                <div class="form-group row col-md-6 ">
                    <div class="col-md-6">Subject Property Construction Level</div>
                    <div class="col-md-6">
                        <?php if ($allowToEdit) { ?>
                            <select class="form-control input-sm"
                                   name="propertyConstructionLevel"
                                   id="propertyConstructionLevel"
                                   value="<?php echo $propertyConstructionLevel; ?>">
                                <option value="">Select</option>
                                <?php foreach ($glPropertyConstructionLevel as $value){ ?>
                                    <option value="<?php echo $value; ?>" <?php echo Arrays::isSelected($value,$propertyConstructionLevel)?>><?php echo $value; ?></option>
                                <?php } ?>

                            </select>
                        <?php } else { ?>
                            <h5><?php echo $propertyConstructionLevel ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php
            if ($propertyNeedRehab == 'Yes') {
                $doesPropertyNeedRehabDispDiv = 'display: block;';
            } else {
                $doesPropertyNeedRehabDispDiv = 'display: none;';
            }
            ?>
            <div id="doesPropertyNeedRehabDispDiv" style="<?php echo $doesPropertyNeedRehabDispDiv; ?>">
                <div class="row">
                    <div class="form-group col-md-4 col-sm-6">
                        Please use these templates to provide us a budget and timeline. <br>
                        <medium>(You may use your own budget / timeline as well)</medium>
                    </div>
                    <div class="form-group col-md-3 col-sm-3"><span><a class="fa fa-file-excel text-primary"
                                                                       href="<?php echo CONST_SITE_URL; ?>media/ConstructionBudgTemplate.xlsx"
                                                                       target="_blank"
                                                                       title="Click here to download budget"></a></span>
                        &nbsp; <span><a href="<?php echo CONST_SITE_URL; ?>media/ConstructionBudgTemplate.xlsx"
                                        style="text-decoration:none;" target="_blank"
                                        title="Click here to download budget"><h4>Download Budget</h4></a></span>
                    </div>

                    <div class="form-group col-md-3 col-sm-3">
                        <span>
                            <a class="fa fa-file-excel text-primary"
                               href="<?php echo CONST_SITE_URL; ?>media/Construction-timeline-template.xlsx"
                               target="_blank"
                               title="Click here to download Timeline"></a>
                        </span>
                        &nbsp; <span><a href="<?php echo CONST_SITE_URL; ?>media/Construction-timeline-template.xlsx"
                                        style="text-decoration:none;" target="_blank"
                                        title="Click here to download Timeline"><h4>Download Timeline</h4></a></span>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-md-4 col-sm-6">Will there be a general contractor hired to perform rehab?
                    </div>
                    <div class="form-group col-md-2 col-sm-6">
                        <?php if ($allowToEdit) { ?>
                            <input type="radio" name="isHiredPerformRehab" value="Yes"
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $isHiredPerformRehab); ?>
                                   onclick="showAndHideLODiv(this.value, 'generalContractorHiredPerformDispDiv');">Yes
                            <input type="radio" name="isHiredPerformRehab" value="No"
                                   tabindex="<?php echo $tabindex++; ?>" <?php echo Strings::isChecked('No', $isHiredPerformRehab); ?>
                                   onclick="showAndHideLODiv(this.value, 'generalContractorHiredPerformDispDiv');">No
                            <?php
                        } else {
                            echo '<h5>' . $isHiredPerformRehab . '</h5>';
                        }
                        ?>
                    </div>
                </div>
                <?php
                if ($isHiredPerformRehab == 'Yes') {
                    $generalContractorHiredPerformDispDiv = 'display: block;';
                } else {
                    $generalContractorHiredPerformDispDiv = 'display: none;';
                }
                ?>

                <div id="generalContractorHiredPerformDispDiv" class="generalContractor  form-group row"
                     style="<?php echo $generalContractorHiredPerformDispDiv ?>">
                    <div class="form-group col-lg-12 m-0 mb-4 px-0" id="genContractorTitle">
                        <label class="font-weight-bold bg-secondary  py-2  col-lg-12"><b>General Contractor
                                Info</b></label>
                    </div>
                    <div class="form-group col-lg-12">
                        <div class="row">
                            <div class="form-group  row col-md-4">
                                <label class="font-weight-bold col-md-4" for="GCFirstName">First Name</label>
                                <div class="col-md-8 ">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="GCContactCls">
                                            <div class="input-group">
                                                <input type="text"
                                                       class="GCPrimContactCls form-control input-sm" name="GCFirstName"
                                                       id="GCFirstName"
                                                       value="<?php echo htmlspecialchars($GCFirstName); ?>"
                                                       TABINDEX="<?php echo $tabIndex++; ?>" placeholder=""
                                                       onchange="allowToEditFileContacts('GCContactCls', 'GCPrimContactCls', 'Edit');"
                                                       onclick="populateContactName('GCName', this.value);"
                                                       autocomplete="off">
                                                <div class="input-group-append popoverClass"
                                                     onclick="allowToEditFileContacts('GCContactCls', 'GCPrimContactCls', 'Clear');"
                                                     data-content="Click to remove contact">
                                                    <div class="input-group-text ">
                                                        <i class="fa fa-times text-danger"></i>
                                                        <input type="hidden" class="GCContactCls" name="tempGCFirstName"
                                                               id="tempGCFirstName"
                                                               value="<?php echo $GCFirstName; ?>"/>
                                                        <input type="hidden" class="GCContactCls" name="GCContactID"
                                                               id="GCContactID"
                                                               value="<?php echo $GCContactID ?>"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } else {
                                        echo '<h5>' . $serviceLenderName . '</h5>';
                                    } ?>
                                </div>
                            </div>
                            <div class="form-group  row col-md-4">
                                <label class="font-weight-bold col-md-4">Last Name</label>
                                <div class="col-md-8">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="hidden" class="GCContactCls" name="tempGCLastName"
                                               id="tempGCLastName"
                                               value="<?php echo htmlspecialchars($GCLastName); ?>"/>
                                        <input type="text" class="GCPrimContactCls form-control input-sm"
                                               name="GCLastName"
                                               id="GCLastName" value="<?php echo htmlspecialchars($GCLastName); ?>" placeholder=" "
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               onchange="allowToEditFileContacts('GCContactCls', 'GCPrimContactCls', 'Edit');"
                                               onclick="populateContactName('GCLName', this.value);" autocomplete="off">
                                    <?php } else {
                                        echo '<h5>' . $GCLastName . '</h5>';
                                    } ?>
                                </div>
                            </div>
                            <div class="form-group  row col-md-4">
                                <label class="font-weight-bold col-md-4">Company Name</label>
                                <div class="col-md-8">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="hidden" class="GCContactCls" name="tempGCCompanyName"
                                               id="tempGCCompanyName"
                                               value="<?php echo htmlspecialchars($GCCompanyName) ?>"/>
                                        <input type="text" class="GCPrimContactCls form-control input-sm"
                                               name="GCCompanyName"
                                               id="GCCompanyName" value="<?php echo htmlspecialchars($GCCompanyName); ?>"
                                               TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                               onchange="allowToEditFileContacts('GCContactCls', 'GCPrimContactCls', 'Edit');"
                                               onclick="populateContactName('GCCName', this.value);">
                                    <?php } else {
                                        echo '<h5>' . $GCCompanyName . '</h5>';
                                    } ?>
                                </div>
                            </div>
                            <div class="form-group  row col-md-4">
                                <label class="font-weight-bold col-md-4">E-mail</label>
                                <div class="col-md-8">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="email" class="GCPrimContactCls form-control input-sm" name="GCEmail"
                                               id="GCEmail"
                                               value="<?php echo htmlspecialchars($GCEmail); ?>" placeholder=""
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               onchange="allowToEditFileContacts('GCContactCls', 'GCPrimContactCls', 'Edit');"
                                               autocomplete="off">
                                    <?php } else {
                                        echo '<h5>' . $GCEmail . '</h5>';
                                    } ?>
                                </div>
                            </div>
                            <div class="form-group  row col-md-4">
                                <label class="font-weight-bold col-md-4">Phone</label>
                                <div class="col-md-8">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text" class="GCPrimContactCls mask_phone form-control input-sm"
                                               name="GCPhone"
                                               id="GCPhone" value="<?php echo $GCPhone; ?>" placeholder=" "
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               onchange="allowToEditFileContacts('GCContactCls', 'GCPrimContactCls', 'Edit');"
                                               autocomplete="off">
                                    <?php } else {
                                        echo '<h5>' . Strings::formatPhoneNumber($GCPhone) . '</h5>';
                                    } ?>
                                </div>
                            </div>
                            <div class="form-group  row col-md-4">
                                <label class="font-weight-bold col-md-4">License</label>
                                <div class="col-md-8">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text" class="GCContactCls form-control input-sm" name="GCLicense"
                                               id="GCLicense"
                                               value="<?php echo htmlspecialchars($GCLicense); ?>" TABINDEX="<?php echo $tabIndex++; ?>">
                                    <?php } else {
                                        echo '<h5>' . $GCLicense . '</h5>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group row">
                            <label class="col-sm-6 font-weight-bold">Rehab / Construction Cost (by borrower)</label>
                            <div class="col-md-4 col-sm-6">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input class="form-control input-sm" type="text" name="rehabCost" id="rehabCost"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('rehabCost', 'fileHMLOInfo')) ?>"
                                               onblur="currencyConverter(this, this.value);"
                                               TABINDEX="<?php echo $tabIndex++; ?>" placeholder="0.00">
                                    </div>
                                <?php } else {
                                    echo '<h5>' . Currency::formatDollarAmountWithDecimal(Strings::showField('rehabCost', 'fileHMLOInfo')) . '</h5>';
                                } ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-6 font-weight-bold">After Rehab / Resale Value (by borrower)</label>
                            <div class="col-md-4 col-sm-6 ">
                                <?php if ($allowToEdit) { ?>

                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input class="form-control input-sm" type="text" name="rehabValByBor"
                                               id="rehabValByBor1"
                                               onblur="currencyConverter(this, this.value);"
                                               onkeyup="populateDualFieldForHMLO(this.value, 'loanModForm', 'rehabValByBor')"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('rehabValByBor', 'listingRealtorInfo')) ?>"
                                               TABINDEX="<?php echo $tabIndex++; ?>" placeholder="0.00">
                                    </div>

                                <?php } else {
                                    echo '<h5>' . Currency::formatDollarAmountWithDecimal(Strings::showField('rehabValByBor', 'listingRealtorInfo')) . '</h5>';
                                } ?>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-sm-6 font-weight-bold" for="rehabDuration">How long will Rehab / Construction take in
                                months?</label>
                            <div class="col-md-4 col-sm-6 ">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number" name="rehabDuration" id="rehabDuration"
                                           value="<?php echo round(Strings::replaceCommaValues(Strings::showField('rehabDuration', 'fileHMLOInfo'))); ?>"
                                           TABINDEX="<?php echo $tabIndex++; ?>" class="form-control input-sm">
                                <?php } else {
                                    echo '<h5>' . Strings::showField('rehabDuration', 'fileHMLOInfo') . '</h5>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 font-weight-bold">Please explain your Rehab/Construction plans<br>
                        <textarea class="form-control input-sm" rows="5" cols="50" name="rehabStrategyPlans"
                                  id="rehabStrategyPlans"
                                  TABINDEX="<?php echo $tabIndex++; ?>"><?php echo $rehabStrategyPlans ?></textarea>
                    </div>
                </div>


                <?php
                if ($isHMLO == 1) {
                    require __DIR__ . '/HMLOInitialListOfRepairs.php';
                }
                ?>
            </div>
        </div>
    </div>
<?php } ?>
