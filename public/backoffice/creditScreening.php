<?php
global $pcAcqualifyStatus, $acqualifyOptStatus, $multipleBranchesEnabled;

use models\composite\oPC\getacqualifyPCDetails;
use models\composite\oPC\getacqualifyUserDetails;
use models\cypher;
use models\PageVariables;
use models\standard\BaseHTML;
use models\standard\UserAccess;
use models\standard\Custify;


session_start();
require '../includes/util.php';
require '../includes/router.php';

UserAccess::CheckAdminUse();
require CONST_BO_PATH . 'initPageVariables.php';
require CONST_BO_PATH . 'getPageVariables.php';

echo BaseHTML::openPage('Credit Screening - ' . CONST_DOMAIN, 1, 1);

/* Dynamic $Breadcrumb */
$Breadcrumb = [];
$Breadcrumb['icon'] = 'fas fa-chart-bar icon-md';
$Breadcrumb['breadcrumbList'] = '';//array(array('href' => '#', 'title' => 'Sample home'), array('href' => '#', 'title' => 'Sample Child'));
$Breadcrumb['toolbarHTML'] = '';
/* End of Dynamic $Breadcrumb */
$Breadcrumb['title'] = 'Credit Screening - ';

require('../includesNew/_page-body-loader.php');
require('../includesNew/_layoutOpen.php');

$pcAcqualifyDetails = [];
$pcAcqualifyId = 0;
$userAcqualifyId = 0;
$softCreditPullStatus='';

$pcAcqualifyDetails = getacqualifyPCDetails::getReport(['pcid' => $_SESSION['PCID']]);  //i think this can be changed to $PCID but not in scope of my changes to this file, but custify is downstream and its using $PCID
$pcAcqualifyId = $pcAcqualifyDetails[0]['accountId'];

$acQualifyUserDetails = getacqualifyUserDetails::getReport(['lwUserId' => $_SESSION['userNumber'], 'userRole' => $_SESSION['userGroup']]);
$userAcqualifyId = $acQualifyUserDetails[0]['userId'];

/*$listAccounts = $oAcqualify->listAccounts(array());
pr(json_decode($listAccounts));
$listUsers = $oAcqualify->listUsers(array());
pr(json_decode($listUsers));

$branchUsers['data'] = array("accountId" => 240);
$listBranch = $oAcqualify->listBranches($branchUsers);
pr(json_decode($listBranch));
//echo count($acQualifyUserDetails) . " ** " . $pcAcqualifyId . " ** " . $pcAcqualifyStatus;
exit;*/
?>
<?php if ($pcAcqualifyStatus > 0) {

    if ($pcAcqualifyId > 0 && $userAcqualifyId > 0 && $acqualifyOptStatus > 0) {
        $softCreditPullStatus = "setupButNotConfigured";
        ?>
        <div class="card card-custom creditScreeningCard  " id="modal-content-id">
            <div class="card-header card-header-tabs-line ">
                <div class="card-title">
                    <h3 class="card-label">Credit Screening</h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);"
                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                       data-card-tool="toggle"
                       data-section="creditScreeningCard"
                       data-toggle="tooltip" data-placement="top" title=""
                       data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body   creditScreeningCard_body ">
                <?php
                if ($_SESSION['userRole'] == 'Manager') {
                    $minCreditScore = 0;
                    $minCreditScore = $pcAcqualifyDetails[0]['minCreditScore'];
                    $pcAcqualifyId = $pcAcqualifyDetails[0]['accountId'];
                    $saveUrl = CONST_URL_POPS . 'updateSoftPull.php'; ?>
                    <form name="updateCreditScreening" class="form" id="updateCreditScreening" method="POST"
                          action="<?php echo $saveUrl; ?>">
                        <input type="hidden" name="pcAcqualifyId" id="pcAcqualifyId"
                               value="<?php echo cypher::myEncryption($pcAcqualifyId); ?>">
                        <input type="hidden" name="PCID" id="PCID"
                               value="<?php echo cypher::myEncryption($_SESSION['PCID']); ?>">
                        <div class="row">
                            <label class="col-md-2 font-weight-bold align-self-center" for="minCreditScore">Score
                                Threshold:
                                <i class="tooltipClass fas fa-info-circle text-primary "
                                   title="Anyone under this threshold will be offered credit restoration services to improve their credit."></i>
                            </label>
                            <div class="col-md-2">
                                <input type="number" class="form-control input-sm" name="minCreditScore"
                                       id="minCreditScore" value="<?php echo $minCreditScore ?>">
                            </div>
                            <div class="col-md-1">
                                <input type="submit" name="Update" value="Update" class="btn btn-primary">
                            </div>
                        </div>
                        <div class="row d-none">
                            <label class="col-md-5 font-weight-bold" for="multipleBranchesEnabled">Min Credit
                                Score:</label>
                            <div class="col-md-7">
                                <input type="text" class="form-control input-sm"
                                       name="multipleBranchesEnabled"
                                       id="multipleBranchesEnabled"
                                       value="<?php echo $multipleBranchesEnabled ?>">
                            </div>
                        </div>
                    </form>
                <?php }
                ?>
                <div class="embed-responsive embed-responsive-16by9 mt-4">
                    <iframe src="https://acqualify.com/login-by-key?loginKey=<?php echo $acQualifyUserDetails[0]['loginKey']; ?>"
                            name="top" width="100%" frameborder="0" scrolling="yes">
                    </iframe>
                </div>
            </div>
        </div>
        <?php
    } else if ($pcAcqualifyId == '0' || $userAcqualifyId == 0 || $acqualifyOptStatus == '0') {
        $softCreditPullStatus = "notSetup";
        ?>
        <div class="row  h-100  justify-content-center align-items-center">
            <div class="col-md-3">
            </div>
            <div class="col-md-6">
                <div class="card card-custom creditScreeningCard  " id="modal-content-id">
                    <div class="card-body p-15  creditScreeningCard_body ">
                        <p class="font-weight-bold text-center font-size-h1">Unlock Lead Intelligence</p>
                        <p class="text-center font-size-h6 mt-8">
                            Run free soft credit checks (no SSN needed) to qualify leads in seconds.
                            Save time and money on unneeded apps and credit reports!
                        </p>
                        <p class="text-center font-size-h6">
                            Leads with poor credit get referred to Acqualify's credit restoration
                            program and you'll get notified as their scores improve!
                        </p>
                        <div class="row mt-10">
                            <div class="col-md-8">
                                <p class=" px-5 text-center font-size-h6">
                                    By Clicking "Activate", I accept accept Acqualify's
                                    <a target="_blank"
                                       href="https://staging.acqualify.com/user-agreement">
                                        <u> Terms Of Service</u>
                                    </a>
                                    <br>
                                    <a target="_blank"
                                       href="https://staging.acqualify.com/privacy-policy">
                                        <u> Privacy Policy</u>
                                    </a>
                                    and
                                    <a target="_blank"
                                       href="https://acqualify.com/how-it-works">
                                        <u> Frequently Asked Questions</u>
                                    </a>
                                </p>
                            </div>
                            <div class="col-md-4">
                                <p class=" px-2 text-left">
                                    <button class="btn btn-primary" id="activateNow">Activate Now</button>
                                </p>
                            </div>
                        </div>
                        <p class="text-center mt-10">
                            Powered by <img src="/assets/images/Acqualify-logo-revised.png" height="30">
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
            </div>
        </div>
    <?php }

} else {
    $softCreditPullStatus = "operational";
    ?>
    <div class="row  h-100  justify-content-center align-items-center">
        <div class="col-md-3">
        </div>
        <div class="col-md-6">
            <div class="card card-custom creditScreeningCard  " id="modal-content-id">
                <div class="card-body p-15  creditScreeningCard_body ">
                    <p class="font-weight-bold text-center font-size-h1">Unlock Lead Intelligence</p>
                    <p class="text-center font-size-h6 mt-8">
                        Run free soft credit checks (no SSN needed) to qualify leads in seconds.
                        Save time and money on unneeded apps and credit reports!
                    </p>
                    <p class="text-center font-size-h6">
                        Leads with poor credit get referred to Acqualify's credit restoration
                        program and you'll get notified as their scores improve!
                    </p>
                    <div class="row mt-10">
                        <div class="col-md-12">
                            <p class=" px-5 text-center font-size-h6">
                                By Clicking "Activate", I accept accept Acqualify's
                                <a target="_blank"
                                   href="https://staging.acqualify.com/user-agreement">
                                    <u> Terms Of Service</u>
                                </a>
                                <br>
                                <a target="_blank"
                                   href="https://staging.acqualify.com/privacy-policy">
                                    <u> Privacy Policy</u>
                                </a>
                                and
                                <a target="_blank"
                                   href="https://acqualify.com/how-it-works">
                                    <u> Frequently Asked Questions</u>
                                </a>
                            </p>
                        </div>
                        <div class="col-md-12">
                            <p class=" px-2 text-center">
                                <button class="btn btn-primary " disabled>Activate Now</button>
                            </p>
                        </div>
                        <div class="col-md-12">
                            <p class=" px-2 text-danger">
                                In order to enable this feature free of charge, Please comment on our LinkedIn post
                                saying <a target="_blank"
                                          href="https://www.linkedin.com/feed/update/urn:li:activity:6803319049758613504">“Please
                                    activate this in my account"</a>
                            </p>
                        </div>
                    </div>
                    <p class="text-center mt-10">
                        Powered by <img src="/assets/images/Acqualify-logo-revised.png" height="30">
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
        </div>
    </div>
<?php } ?>

<?php
require('../includesNew/_layoutClose.php');
require 'adminFooter.php';
echo Custify::creditPullFooter($softCreditPullStatus);
echo BaseHTML::closePage();
require('../includesNew/_customPage.php');
?>
<script>
    $('#activateNow').click(function () {
        $.ajax({
            type: 'POST',
            url: '../pops/activateCreditScreeening.php',
            data: jQuery.param({
                'PCID': '<?php echo cypher::myEncryption($_SESSION['PCID']);?>',
                'pcAcqualifyStatus': '<?php echo cypher::myEncryption($pcAcqualifyStatus);?>'
            }),
            beforeSend: function () {
                BlockDiv('modal-content-id');
            },
            complete: function () {
                UnBlockDiv('modal-content-id');
            },
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            success: function (response, status, xhr) {
                KTApp.unblock('#modal-content-id');
                res = JSON.parse(response);
                if (res.code == 100) {
                    toastrNotification(res.msg, 'success');
                    location.reload();
                } else if (res.code == 101) {
                    if (res.msg != '') {
                        toastrNotification(res.msg, 'error');
                    }
                }
                location.reload();
            }
        });
        return false;
    });


    var LWFormControls = function () {
        var _formSubmitValidation = function () {

            var formIdToSubmit = $('#updateCreditScreening');
            formIdToSubmit.validate({
                ignore: ".ignoreValidation",
                rules: {
                    minCreditScore: {
                        required: true,
                        number: true
                    }
                },
                messages: {
                    minCreditScore: "Please Enter Min Credit Score",
                },
                errorElement: "em",
                errorPlacement: function (error, element) {
                    // Add the `invalid-feedback` class to the error element
                    error.addClass("invalid-feedback");

                    error.insertAfter(element);

                },
                highlight: function (element, errorClass, validClass) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).addClass("is-valid").removeClass("is-invalid");
                },
                submitHandler: function (form) {

                    ajaxUrl = $(formIdToSubmit).attr('action');
                    var formData = $(formIdToSubmit).serialize();

                    var ajaxRequest = $.ajax({
                        url: ajaxUrl,
                        type: "POST",
                        data: formData,
                        beforeSend: function () {
                            BlockDiv('modal-content-id');
                        },
                        complete: function () {
                            UnBlockDiv('modal-content-id');
                        },
                        success: function (response, status, xhr) {
                            UnBlockDiv('modal-content-id');
                            res = JSON.parse(response);
                            if (res.code == 100) {
                                toastrNotification(res.msg, 'success');
                            } else {
                                toastrNotification(res.msg, 'error');
                            }
                            location.reload();
                        },
                        error: function (jqXhr, textStatus, errorMessage) {
                            toastrNotification(errorMessage, 'error');
                        }
                    });
                }
            });
        }
        return {
            // public functions
            init: function () {
                _formSubmitValidation();
            }
        };
    }();
    $(document).ready(function () {
        LWFormControls.init();
    });
</script>

<!-- creditScreening.php -->