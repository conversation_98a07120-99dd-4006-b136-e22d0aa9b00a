<?php
//global variables
global $userRole, $fileType, $PCModuleInfo;
global $loggedInUserPCID, $userGroup, $isPCHMLO, $allowToCFPBSubmitForPC;
global $PCID, $selectedColumns, $PCStatusInfoForTab, $defaultTabOnPipeline, $searchFilter;

use models\constants\gl\glPCID;
use models\Controllers\backoffice\myPipeline;
use models\Controllers\backoffice\myPipelineColumns;
use models\Security;
use models\Request;
use models\standard\Arrays;

?>
<input type="hidden" id="filterColumnOrder" name="filterColumnOrder"
       value="<?php echo htmlspecialchars(Request::GetClean('filterColumnOrder')) ?? ''; ?>"/>

<!--begin::Dropdown--><!--begin::Dropdown-->
<div class="dropdown tooltipClass" id="showHidColumnsDropdown" title="Show/Hide Columns">
    <a class="tooltipClass card-title btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1 "
       data-toggle="dropdown"
       aria-haspopup="true"
       aria-expanded="false"><i class="flaticon-interface-6 text-primary"></i></a>
    <div class="dropdown-menu p-0 m-0 bg-warning-o-10 dropdown-menu-right dropdown-menu-anim-up dropdown-menu-lg"
         id="showHidColumnsDropdown1">
        <div class="card card-custom card-stretch p-2" id="kt_page_stretched_card">
            <div class="card-header card-header-tabs-line">
                <div class="card-title">
                    <ul class="nav nav-tabs nav-bold nav-tabs-line">
                        <li class="nav-item">
                            <a class="nav-link active py-1" data-toggle="tab" href="#kt_tab_pane_1_4">
                                <span class="nav-icon"><i class="icon-xl fas fa-columns"></i></span>
                                <span class="nav-text">Columns</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-1" data-toggle="tab" href="#kt_tab_pane_2_4">
                                <span class="nav-icon"><i class="fas fa-arrows-alt-v"></i></span>
                                <span class="nav-text">Columns Order</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-1" data-toggle="tab" href="#kt_tab_pane_3_4">
                                <span class="nav-icon"><i class="fas fa-filter text-primary"></i></span>
                                <span class="nav-text">Search Filter</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="card-body p-0 ">
                <div class="p-2">
                    <button type="button"
                            class="label label-primary label-md label-inline font-weight-bolder mr-2 applyColumns"
                            onclick="myPipelineColumns.submitForm();"
                    >Update Current View
                    </button>
                    <button type="button"
                            class="label label-primary label-md label-inline font-weight-bolder mr-2 applyColumns"
                            id="applyColumns"
                    >Save As Default View
                    </button>
                    <button type="button" class="label label-danger label-md label-inline font-weight-bolder mr-2"
                            data-user_number="<?php echo myPipelineColumns::$enc_userNumber; ?>"
                            data-pcid="<?php echo myPipelineColumns::$enc_PCID; ?>"
                            data-user_group="<?php echo myPipelineColumns::$enc_userGroup; ?>"
                            id="resetUserPreferences"
                    >Reset Default View
                    </button>
                </div>
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="kt_tab_pane_1_4" role="tabpanel"
                         aria-labelledby="kt_tab_pane_1_4">
                        <div class="card-scroll px-4">
                            <div class="row">
                                <div class="col-md-8">
                                </div>
                                <div class="col-md-4 text-right">
                                    <a id="pipeline-columns-close"
                                       class="btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass d-none"
                                       data-original-title="Click to close">
                                        <i class="fas fa-times"></i>
                                    </a>
                                </div>
                            </div>
                            <?php foreach (myPipelineColumns::$pipelineUserPreferencesArray as $header => $columns) { ?>
                                <div class="row ">
                                    <label class="col-md-12 px-2 py-2 bg-primary-o-20 mb-0 font-weight-bold border font-size-h6"><?php echo $header; ?></label>
                                </div>
                                <div class="row form-group">
                                    <?php foreach ($columns as $column => $display) {

                                        if($column == 'lenderInternalNotes' && $PCID == glPCID::PCID_PROD_CV3) { $display = 'Funding Comments'; } ?>
                                        <div class="col-md-4 border py-1">
                                            <div class="checkbox-inline">
                                                <label for="pipeline_column_<?php echo Security::Hash($header . $column); ?>"
                                                       class="checkbox">
                                                    <input type="checkbox"
                                                           class="newCheck showHideColumnsCheckbox pipelineColumnOption"
                                                           name="headerColumn[]"
                                                           value="<?php echo $column; ?>"
                                                           data-label="<?php echo $display; ?>"
                                                        <?php echo in_array($column, myPipelineColumns::$selectedColumns) ? 'checked' : ''; ?>
                                                           id="pipeline_column_<?php echo Security::Hash($header . $column); ?>">
                                                    <span></span>
                                                    <?php echo $display; ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php } ?>
                                </div>
                            <?php } ?>

                            <div class="row">
                                <label class="col-md-4 font-weight-bold" for="defaultTabOnPipeline">Default
                                    Tab On Pipeline Open</label>
                                <div class="col-md-8 pipeline-columns">
                                    <select name="defaultTabOnPipeline"
                                            id="defaultTabOnPipeline"
                                            onchange="setDefaultTabOnPipeline(this.value);"
                                            class=" form-control chzn-select"
                                            data-placeholder="Please Select Default Tab On Pipeline Load">
                                        <option></option>
                                        <option value="All">All</option>
                                        <?php foreach (myPipelineColumns::$PCStatusInfoArray as $PSID => $primaryStatus) { ?>
                                            <option value="<?php echo $PSID ?>" <?php echo Arrays::isSelected($PSID, myPipeline::$defaultTabOnPipeline) ?>><?php echo $primaryStatus; ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="kt_tab_pane_2_4" role="tabpanel"
                         aria-labelledby="kt_tab_pane_2_4">
                        <div class="row">
                            <div class="col-md-12 ">
                                <div class="card-scroll " style="height: 600px;overflow: scroll;">
                                    <table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                                        <thead class="thead-light">
                                        <tr>
                                            <th>Column name</th>
                                            <th>Column Order</th>
                                        </tr>
                                        </thead>
                                        <tbody id="sortPipelineColumns" class="ui-sortable-helper">
                                        <?php
                                        $i = 1;
                                        foreach (myPipelineColumns::$selectedColumnsOrder as $column => $order) {
                                            if (myPipelineColumns::$selectedColumnsAll[$column]) {
                                                ?>
                                                <tr id="pipeline_column_order_<?php echo $column; ?>"
                                                    class="pipeline_columns_class"
                                                    data-column="<?php echo $column; ?>">
                                                    <td class="p-2 "><?php echo myPipelineColumns::$selectedColumnsAll[$column]; ?></td>
                                                    <td class="orderNumber text-center"><?php echo $i++; ?></td>
                                                </tr>
                                            <?php }
                                        } ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="kt_tab_pane_3_4" role="tabpanel"
                         aria-labelledby="kt_tab_pane_3_4">
                        <div class="card-scroll px-4">
                            <?php foreach (myPipeline::$searchFilter as $group => $filters) { ?>
                                <div class="row ">
                                    <label class="col-md-12 px-2 py-2 bg-primary-o-20 mb-0 font-weight-bold border font-size-h6"><?php echo $group; ?></label>
                                    <?php foreach ($filters as $filter => $settings) { ?>
                                        <div class="col-md-4 border py-1 px-2 <?php echo $settings['class'] ?? ''; ?>">
                                            <div class="checkbox-inline">
                                                <label for="eachFilterId_<?php echo $filter; ?>" class="checkbox">
                                                    <input type="checkbox" <?php echo $settings['status']; ?>
                                                           class="searchFilterClass" name="searchFilter[]"
                                                           value="<?php echo $filter; ?>"
                                                           id="eachFilterId_<?php echo $filter; ?>"/>
                                                    <span></span><?php echo $settings['text']; ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php } ?>
                                </div>
                            <?php } ?>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<!--end::Dropdown-->

<script src="<?php echo CONST_BO_URL; ?>myPipeline/js/myPipelineColumns.js?<?php echo CONST_JS_VERSION; ?>"></script>

