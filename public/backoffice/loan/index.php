<?php

use models\MVC;

session_start();

require_once __DIR__ . '/../../includes/util.php';
require __DIR__ . '/../initPageVariables.php';
require __DIR__ . '/../getPageVariables.php';

CONST MASTERPAGE_DEFAULT = 'default.php';
CONST MASTERPAGE_SERVICING = 'servicing.php';
CONST MASTERPAGE_ORIGINATION = 'origination.php';
const MASTERPAGE_BLANK = 'blank.php';
const MASTERPAGE_POPUP = 'popup.php';
const MASTERPAGE_PUBLIC = 'public.php';

MVC::Init(__DIR__);

MVC::Execute();
