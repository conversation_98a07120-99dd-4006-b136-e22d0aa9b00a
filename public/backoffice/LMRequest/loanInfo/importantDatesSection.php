<?php
global $fileTab,$HMLOLoanInfoSectionsDisp;

use models\constants\gl\glDate;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\loanForm;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\PageVariables;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$ischk = count($IMPDATESSecArr = BaseHTML::sectionAccess2(['sId' => 'IMPDATES', 'opt' => $fileTab, 'activeTab' => LMRequest::$activeTab])) > 0 ? 'checked' : '';

if (PageVariables::$publicUser == 1) {
    $ischk = LMRequest::$LMRId > 0 && $ischk != '' ? 'checked' : '';
}
?>
<div class="card card-custom loanInfoDatesCard IMPDATES <?php if (trim($ischk) == 'checked') {
    echo 'secShow';
} else {
    echo 'secHide';
} ?>" id="loanInfoDatesCard" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label"><?php echo BaseHTML::getSectionHeading('IMPDATES'); ?> </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('IMPDATES')) != '') { ?>&nbsp;
            <i class="popoverClass fas fa-info-circle text-primary "
               data-html="true"
               data-content="<?php echo BaseHTML::getSectionTooltip('IMPDATES'); ?>"></i>
            <?php } ?></h3>
        </div>
        <div class="card-toolbar">
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                    data-card-tool="toggle"
                    data-section="loanInfoDatesCard"
                    data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body loanInfoDatesCard_body ">
        <div class="row">
            <?php loanForm::pushSectionID('IMPDATES'); ?>
            <div class="dateClsSeparator  col-md-3 desiredClosingDate_disp <?php echo loanForm::showField('desiredClosingDate'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('desiredClosingDate', 'col-md-12 font-weight-bold label_highlight'); ?>
                    <div class="col-md-12">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend desiredClosingDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <!--  data-date-start-date="05/18/2024"-->
                                <input type="text"
                                       class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredClosingDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?> dateNewClass"
                                       name="desiredClosingDate"
                                       id="desiredClosingDate"
                                       data-future-date="true"
                                       data-start-date="<?php echo glDate::getFutureDateOnly(); ?>"
                                       value="<?php echo LoanInfo::$desiredCloseDate ?>"
                                       TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                       autocomplete="off" placeholder="MM/DD/YYYY"/>
                            </div>
                        <?php } else {
                            echo '<b>' . LoanInfo::$desiredCloseDate . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="dateClsSeparator col-md-3 <?php echo loanForm::showField('fundingDate'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label2(
                        'fundingDate',
                        'col-md-12 label_highlight font-weight-bold'
                    ); ?>
                    <div class="col-md-12">
                        <?php
                        if (LMRequest::$allowToEdit) {
                            ?>
                            <div class="input-group">
                                <div class="input-group-prepend fundingDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                </div>
                                <input
                                        class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'fundingDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?> dateNewClass"
                                        type="text" name="fundingDate" id="fundingDate"
                                        value="<?php echo Dates::formatDateWithRE(Strings::showField('fundingDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y'); ?>"
                                        TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="MM/DD/YYYY" <?php echo BaseHTML::fieldAccess(['fNm' => 'fundingDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'I']); ?>>
                            </div>
                        <?php } else {
                            echo "<div class=\"left\"><b>" . Dates::formatDateWithRE(Strings::showField('fundingDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y') . '</b></div>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="dateClsSeparator col-md-3 PSAClosingDateClass <?php echo loanForm::showField('PSAClosingDate'); ?>"
                 style="<?php if (LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::PURCHASE) {
                     echo 'display:none;';
                 } ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('PSAClosingDate', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <?php if (LMRequest::$allowToEdit) {
                            echo loanForm::date(
                                'PSAClosingDate',
                                LMRequest::$allowToEdit,
                                LoanInfo::$tabIndex++,
                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->PSAClosingDate ?? '',
                                ' dateNewClass '
                            );
                        } else {
                            echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->PSAClosingDate, 'YMD', 'm/d/Y') . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <?php if (!PageVariables::$publicUser) { ?>
                <div class="dateClsSeparator rateLockFields col-md-3  <?php echo loanForm::showField('rateLockDate'); ?>">
                    <div class="row form-group ">
                        <?php
                        echo loanForm::label(
                            'rateLockDate',
                            'col-md-12 ',
                            '',
                            loanForm::changeLog(
                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                'rateLockDate',
                                \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                'Rate Lock Date'
                            ),
                        );
                        ?>
                        <div class="col-md-12">
                            <?php if (LMRequest::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend rateLockDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                    </div>
                                    <input
                                            type="text"
                                            name="rateLockDate"
                                            id="rateLockDate"
                                            value="<?php echo Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockDate, 'YMD', 'm/d/Y'); ?>"
                                            tabindex=""
                                            class="form-control input-sm dateNewClass"
                                            autocomplete="off"
                                            placeholder="MM/DD/YYYY">
                                </div>
                            <?php } else {
                                echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockDate, 'YMD', 'm/d/Y') . '</b>';
                            } ?>
                        </div>
                    </div>
                </div>
                <div class="dateClsSeparator rateLockFields col-md-3  <?php echo loanForm::showField('rateLockExpirationDate'); ?>">
                    <div class="row form-group">
                        <?php
                        $rateLockDateLabel = loanForm::$formFields['BORF']['rateLockDate']->fieldLabel;
                        $rateLockPeriodLabel = loanForm::$formFields['BORF']['rateLockPeriod']->fieldLabel;

                        $rateLockExpirationDateToolTip = $rateLockDateLabel . ' + ' . $rateLockPeriodLabel . ' + 30';
                        echo loanForm::label(
                            'rateLockExpirationDate',
                            'col-md-12 ',
                            $rateLockExpirationDateToolTip,
                            loanForm::changeLog(
                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                'rateLockExpirationDate',
                                \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                'Rate Lock Expiration Date'
                            ),
                        );
                        ?>
                        <div class="col-md-12">
                            <?php if (LMRequest::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend rateLockExpirationDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                    </div>
                                    <input
                                            type="text"
                                            name="rateLockExpirationDate"
                                            id="rateLockExpirationDate"
                                            readonly
                                            value="<?php echo Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExpirationDate, 'YMD', 'm/d/Y'); ?>"
                                            tabindex=""
                                            class="form-control input-sm"
                                            autocomplete="off"
                                            placeholder="MM/DD/YYYY">
                                </div>
                            <?php } else {
                                echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExpirationDate, 'YMD', 'm/d/Y') . '</b>';
                            } ?>
                        </div>
                    </div>
                </div>
                <div class="dateClsSeparator col-md-3 loanTermExpireDate_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTermExpireDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'D']); ?>">
                    <div class="row form-group">
                        <?php
                        echo loanForm::label(
                            'loanTermExpireDate',
                            'col-md-12 font-weight-bold label_highlight',
                            '',
                            loanForm::changeLog(
                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                'loanTermExpireDate',
                                tblFileHMLONewLoanInfo::class
                            )
                        );
                        ?>
                        <div class="col-md-12">
                            <?php
                            if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) {
                                ?>
                                <div class="input-group">
                                    <div class="input-group-prepend loanTermExpireDate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                    </div>
                                    <input
                                            class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTermExpireDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?> dateNewClass"
                                            type="text" name="loanTermExpireDate" id="loanTermExpireDate"
                                            value="<?php echo LoanInfo::$loanTermExpireDate ?>"
                                            TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="MM/DD/YYYY" <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTermExpireDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'I']); ?>>
                                </div>
                            <?php } else {
                                echo "<div class=\"left\"><b>" . LoanInfo::$loanTermExpireDate . '</b></div>';
                            } ?>
                        </div>
                    </div>
                </div>
                <div class="dateClsSeparator col-md-3 daysUntilClose_disp  <?php echo BaseHTML::fieldAccess(['fNm' => 'daysUntilClose', 'sArr' => $IMPDATESSecArr, 'opt' => 'D']); ?>">
                    <div class="row form-group">
                        <?php
                        echo loanForm::label2(
                            'daysUntilClose',
                            'col-md-12 font-weight-bold'
                        );
                        ?>
                        <div class="col-md-12">
                            <?php if (LMRequest::$allowToEdit) { ?>
                                <div class="input-group">
                                    <input type="number"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'daysUntilClose', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?>"
                                           name="daysUntilClose"
                                           id="daysUntilClose" readonly
                                           value="<?php echo LoanInfo::$daysUntilClose ?>"
                                           TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                           autocomplete="off"
                                           placeholder="0" <?php echo BaseHTML::fieldAccess(['fNm' => 'daysUntilClose', 'sArr' => $IMPDATESSecArr, 'opt' => 'I']); ?>/>
                                    <div class="input-group-append">
                                        <span class="input-group-text">Days</span>
                                    </div>
                                </div>
                            <?php } else {
                                echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LoanInfo::$daysUntilClose) . '</b>';
                            } ?>
                        </div>
                    </div>
                </div>
                <div class="dateClsSeparator col-md-3 purchaseCloseDate_mirror_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'purchaseCloseDate_mirror', 'sArr' => $IMPDATESSecArr, 'opt' => 'D']); ?>">
                    <div class="row form-group">
                        <?php
                        echo loanForm::label(
                            'purchaseCloseDate_mirror',
                            'col-md-12 ',
                        );
                        ?>
                        <div class="col-md-12">
                            <?php if (LMRequest::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend closingDate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                    </div>
                                    <input type="text"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'purchaseCloseDate_mirror', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?> dateNewClass "
                                           name="closingDate" id="closingDate"
                                           value="<?php echo LoanInfo::$purchaseCloseDate ?>"
                                           TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" autocomplete="off"
                                           onchange="populateDualDateForHMLONewLoan(this.value, 'loanModForm', 'datesigned');populateDualField(this.value, 'closingDate_1');getHMLOLoanInfoTotalMonthlyPayment();"
                                           placeholder="MM/DD/YYYY"/>
                                </div>
                            <?php } else {
                                echo '<b>' . LoanInfo::$purchaseCloseDate . '</b>';
                            } ?>
                        </div>
                    </div>
                </div>
                <div data-sectionid="<?php echo loanForm::getSectionID(); ?>"
                     class="dateClsSeparator col-md-3 firstPaymentDueDate_disp <?php echo loanForm::showField('firstPaymentDueDate'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('firstPaymentDueDate', 'col-md-12 font-weight-bold label_highlight'); ?>
                        <div class="col-md-12">
                            <?php if (LMRequest::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend trialPaymentDate1">
                                        <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                        </span>
                                    </div>
                                    <input type="text"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'firstPaymentDueDates', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?> dateNewClass"
                                           name="trialPaymentDate1" id="trialPaymentDate1"
                                           value="<?php echo LoanInfo::$trialPaymentDate1 ?>" autocomplete="off"
                                           maxlength="10" placeholder="MM/DD/YYYY"
                                           tabindex="<?php echo LoanInfo::$tabIndex++; ?>"/>
                                </div>
                                <?php
                            } else {
                                echo '<h5>' . LoanInfo::$trialPaymentDate1 . '</h5>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="dateClsSeparator col-md-3 maturityDate_disp <?php echo loanForm::showField('maturityDate'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('maturityDate', 'col-md-12 font-weight-bold label_highlight'); ?>
                    <div class="col-md-12">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend maturityDate">
                                        <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                        </span>
                                </div>
                                <input type="text" name="maturityDate" id="maturityDate"
                                       value="<?php echo Dates::formatDateWithRE(Strings::showField('maturityDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y'); ?> "
                                       TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'maturityDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?> dateNewClass"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'maturityDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'I']); ?>
                                       placeholder="MM/DD/YYYY">
                            </div>
                        <?php } else {
                            echo '<b>' . Dates::formatDateWithRE(Strings::showField('maturityDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y') . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="dateClsSeparator col-md-3 <?php echo loanForm::showField('buildingAnalysisDueDate'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('buildingAnalysisDueDate', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <?php if (LMRequest::$allowToEdit) {
                            echo loanForm::date(
                                'buildingAnalysisDueDate',
                                LMRequest::$allowToEdit,
                                1,
                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->buildingAnalysisDueDate ?? '',
                                ' dateNewClass '
                            );
                        } else {
                            echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->buildingAnalysisDueDate, 'YMD', 'm/d/Y') . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="dateClsSeparator col-md-3 transactionalFields resaleClosingDate_disp <?php echo loanForm::showField('resaleClosingDate'); ?>"
                 style="<?php echo HMLOLoanTermsCalculation::$transactionalFieldsDispOpt; ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('resaleClosingDate', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend resaleClosingDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                </div>
                                <input type="text"
                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'resaleClosingDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?> dateNewClass"
                                       name="resaleClosingDate" id="resaleClosingDate"
                                       value="<?php echo LoanInfo::$resaleClosingDate ?>"
                                       TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" autocomplete="off"
                                       placeholder="MM/DD/YYYY" <?php echo BaseHTML::fieldAccess(['fNm' => 'resaleClosingDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else {
                            echo '<b>$ ' . LoanInfo::$resaleClosingDate . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <?php if (!PageVariables::$publicUser) { ?>
                <div class="dateClsSeparator col-md-3 LOISentDate_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'LOISentDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'D']); ?>">
                    <div class="row form-group">
                        <?php
                        echo loanForm::label(
                            'LOISentDate',
                            'col-md-12 font-weight-bold label_highlight',
                            '',
                        );
                        ?>
                        <div class="col-md-12">
                            <?php
                            if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) {
                                ?>
                                <div class="input-group">
                                    <div class="input-group-prepend LOISentDate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                    </div>
                                    <input
                                            class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'LOISentDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'M']); ?> dateNewClass"
                                            type="text" name="LOISentDate" id="LOISentDate"
                                            value="<?php echo LoanInfo::$LOISentDate; ?>"
                                            TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="MM/DD/YYYY" <?php echo BaseHTML::fieldAccess(['fNm' => 'LOISentDate', 'sArr' => $IMPDATESSecArr, 'opt' => 'I']); ?>>
                                </div>
                            <?php } else {
                                echo "<div class=\"left\"><b>" . LoanInfo::$LOISentDate . '</b></div>';
                            } ?>
                        </div>
                    </div>
                </div>

            <?php } ?>
            <?php loanForm::popSectionID(); ?>
        </div>
    </div>
</div>
