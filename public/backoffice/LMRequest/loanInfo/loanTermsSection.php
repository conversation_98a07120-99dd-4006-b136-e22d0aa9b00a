<?php
global $fileTab, $borrowerActiveSectionDisp, $fldArr;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glRestrictCustomFields;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\loanForm;
use models\CustomField;
use models\HMLOLoanTermsCalculation;
use models\HMLOLoanTermsCalculation\LTC2Variables;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;

$secArr = BaseHTML::sectionAccess2(['sId' => 'LT', 'opt' => $fileTab]);
$glRestrictCustomFields = glRestrictCustomFields::$glRestrictCustomFields;
?>

<div class="card card-custom HMLOLoanInfoSections propAddress borrowerActiveSection loanTermsCard LT <?php if (count($secArr) <= 0) {
    echo 'secHide';
} ?>"
     id="loanTermsCard"
     style="<?php echo $borrowerActiveSectionDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label"><?php echo BaseHTML::getSectionHeading('LT'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('LT')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('LT'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                    data-card-tool="toggle"
                    data-section="loanTermsCard"
                    data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body loanTermsCard_body">
        <?php loanForm::pushSectionID('LT'); ?>
        <div class="row">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6 commercialTdFields transactionalTdFields transactionalTdFieldsNC maxAmtToPutDown_disp right  <?php echo loanForm::showField('maxAmtToPutDown'); ?>"
                                 style="<?php echo HMLOLoanTermsCalculation::$commercialFieldsTDDispOpt . HMLOLoanTermsCalculation::$commercialFieldsTDNCDispOpt ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('maxAmtToPutDown', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="text"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'maxAmtToPutDown', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="maxAmtToPutDown"
                                                       id="maxAmtToPutDown"
                                                       onblur="currencyConverter(this, this.value);calculateDownPaymentPercentage(); validateMinMaxLoanGuidelines();"
                                                       onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                       placeholder="0.00"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$maxAmtToPutDown) ?>"
                                                       autocomplete="off"
                                                    <?php echo glCustomJobForProcessingCompany::readOnlyFieldLoanInfo(LMRequest::$PCID, LMRequest::$activeTab); ?>
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo LTC2Variables::$readOnlyForLTC2Fields; ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'maxAmtToPutDown', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            </div>
                                        <?php } else {
                                            echo '<b>$ ' . HMLOLoanTermsCalculation::$maxAmtToPutDown . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 commercialTdFields  transactionalTdFields <?php echo loanForm::showField('downPaymentPercentage'); ?> transactionalTdFieldsNC downPaymentPercentage_disp right"
                                 style="<?php echo HMLOLoanTermsCalculation::$commercialFieldsTDDispOpt . HMLOLoanTermsCalculation::$commercialFieldsTDNCDispOpt ?>">
                                <div class="row">
                                    <?php echo loanForm::label('downPaymentPercentage', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'downPaymentPercentage', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="downPaymentPercentage"
                                                   id="downPaymentPercentage"
                                                   placeholder="0.0"
                                                   onchange="calculateDownPaymentByPercentage(); validateMinMaxLoanGuidelines();"
                                                   TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                                   max="100"
                                                   value="<?php echo round(floatval(HMLOLoanTermsCalculation::$downPaymentPercentage), 5) ?>"
                                                   maxlength="8"
                                                <?php echo LTC2Variables::$readOnlyForLTC2Fields; ?>
                                                <?php echo glCustomJobForProcessingCompany::readOnlyFieldLoanInfo(LMRequest::$PCID, LMRequest::$activeTab); ?>
                                                   onkeyup="validatePercentage(this)"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'downPaymentPercentage', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else {
                                            echo '<b>' . HMLOLoanTermsCalculation::$downPaymentPercentage . ' %</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 " style="border:1px;">
                        <div class="row">
                            <div class="col-md-12 hideInitialLoanAmountCV3 commercialFieldsTD commercialFieldsTDNew  transactionalTdFields <?php echo BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'D']); ?> transactionalTdFieldsNC  LOCTotalLoanAmt_disp"
                                 style="<?php echo HMLOLoanTermsCalculation::$commercialFieldsTDDispOpt . ' ' . HMLOLoanTermsCalculation::$commercialFieldsTDNCDispOpt ?>/* background-color: #F3F2D1*/">
                                <div class="row form-group mt-10">
                                    <?php echo loanForm::label('LOCTotalLoanAmt', 'col-md-6'); ?>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <span style="border-color: transparent;"
                                                  class="input-group-text form-control input-xl"
                                                  id="acquisitionPriceFinanced">
                                                            <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$acquisitionPriceFinanced) ?>
                                                        </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 rehabConsCls lineOfCreditProp   LOCTotalLoanAmt_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                                    style="<?php echo HMLOLoanTermsCalculation::$rehabConsCls ?><?php echo HMLOLoanTermsCalculation::$lineOfCreditProp ?>">
                                <div class="row form-group mt-10">
                                    <?php
                                    echo loanForm::label(
                                        'LOCTotalLoanAmt',
                                        'col-md-6 font-weight-bold justify-content-center align-self-center',
                                        'A. Input the Rehab/Construction Cost followed by the Rehab/Construction% Financed or <br>
                                                    B. Input the Rehab/Construction Cost followed by the Rehab/Construction Cost Financed',
                                        loanForm::changeLog(
                                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                            'CORTotalLoanAmt',
                                            tblFileHMLONewLoanInfo::class,
                                            'Initial Loan Amount',
                                        )
                                    );
                                    ?>
                                    <div class="col-md-6">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="text"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="CORTotalLoanAmt"
                                                       id="CORTotalLoanAmt"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$CORTotalLoanAmt) ?>"
                                                       onblur="currencyConverter(this, this.value);calculateCORefiLTVPercentage();"
                                                       placeholder="0.00"
                                                       TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                                       autocomplete="off"
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                    <?= HMLOLoanTermsCalculation::$readyOnlyFieldsForLTC2_RefinanceCategory; ?>
                                                />
                                            </div>
                                        <?php } else {
                                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$CORTotalLoanAmt) . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6 doesPropertyNeedRehabDispDiv
                                    <?php echo loanForm::showField('rehabCost', Currency::formatDollarAmountWithDecimal(Strings::showField('rehabCost', 'fileHMLOInfo')), !PageVariables::$publicUser ? true : false); ?> right "
                                 style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispDiv ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('rehabCost',
                                        'col-md-12 font-weight-bold justify-content-center align-self-center',
                                        '',
                                        loanForm::changeLog(
                                            LMRequest::myFileInfo()->fileHMLOInfo()->HMLOID,
                                            'rehabCost',
                                            \models\lendingwise\tblFileHMLO::class,
                                            loanForm::getFieldLabel('rehabCost')
                                        )
                                    ); ?>
                                    <div class="col-md-12">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="text"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabCost', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="rehabCost" id="rehabCost"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('rehabCost', 'fileHMLOInfo')) ?>"
                                                       onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                       onblur="currencyConverter(this, this.value);calculatePercentageRehabCostFinanced(); validateMinMaxLoanGuidelines();"
                                                       placeholder="0.00"
                                                    <?php
                                                    if (!(floatval(Strings::showField('rehabCost', 'fileHMLOInfo')) > 0
                                                        && !loanForm::isVisible('rehabCost'))) {
                                                        echo BaseHTML::fieldAccess(['fNm' => 'rehabCost', 'sArr' => $secArr, 'opt' => 'I']);
                                                    } ?> />
                                            </div>
                                        <?php } else { ?>
                                            <b>$ <?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('rehabCost', 'fileHMLOInfo')) ?></b>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 doesPropertyNeedRehabDispDiv <?php echo loanForm::showField('rehabCostPercentageFinanced', HMLOLoanTermsCalculation::$rehabCostPercentageFinanced, !PageVariables::$publicUser ? true : false); ?> right rehabCostPercentageFinanced_disp"
                                 style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('rehabCostPercentageFinanced',
                                        'col-md-12 font-weight-bold justify-content-center align-self-center',
                                        '',
                                        loanForm::changeLog(
                                            LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->HMLIID,
                                            'rehabCostPercentageFinanced',
                                            \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                            loanForm::getFieldLabel('rehabCostPercentageFinanced')
                                        )
                                    ); ?>
                                    <div class="col-md-12 ">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabCostPercentageFinanced', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rehabCostPercentageFinanced"
                                                   id="rehabCostPercentageFinanced"
                                                   onchange="calculateRehabCostFinancedByPercentage(); validateMinMaxLoanGuidelines();"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                   value="<?php echo Strings::formatNumber(HMLOLoanTermsCalculation::$rehabCostPercentageFinanced, 10); ?>"
                                                   onkeyup="validatePercentage(this)"
                                                <?php
                                                if (!(floatval(Strings::replaceCommaValues(HMLOLoanTermsCalculation::$rehabCostPercentageFinanced)) > 0
                                                    && !loanForm::isVisible('rehabCostPercentageFinanced'))) {
                                                    echo BaseHTML::fieldAccess(['fNm' => 'rehabCostPercentageFinanced', 'sArr' => $secArr, 'opt' => 'I']);
                                                }
                                                ?>>
                                        <?php } else { ?>
                                            <b><?php echo HMLOLoanTermsCalculation::$rehabCostPercentageFinanced ?>
                                                %</b>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6" style="border:1px;">
                        <div class="row">
                            <div class="col-md-12 doesPropertyNeedRehabDispDiv
                                        <?php echo loanForm::showField('rehabCostFinanced', HMLOLoanTermsCalculation::$rehabCostFinanced, !PageVariables::$publicUser ? true : false); ?> rehabCostFinanced_disp rehabCostFinancedDiv"
                                 style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispDiv; ?>">
                                <div class="row form-group mt-10">
                                    <?php
                                    echo loanForm::label2(
                                        'rehabCostFinanced',
                                        'col-md-6 font-weight-bold justify-content-center align-self-center',
                                        'The recommended way to calculate the amount for this field is to either:
                                            <br><br>
A. Input the Rehab/Construction Cost followed by the Rehab/Construction% Financed <br>
<b>OR</b> <br>
B. Input the Rehab/Construction Cost followed by the Rehab/Construction Cost Financed',
                                        loanForm::changeLog(
                                            LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->HMLIID,
                                            'rehabCostFinanced',
                                            \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                            loanForm::getFieldLabel('rehabCostFinanced')
                                        )
                                    );
                                    ?>
                                    <div class="col-md-6">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input class="form-control input-sm"
                                                       type="text"
                                                       name="rehabCostFinanced"
                                                       id="rehabCostFinanced"
                                                       placeholder="0.00"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                       onkeyup="return restrictAlphabetsLoanTerms(this)"
                                                       onblur="currencyConverter(this, this.value);calculateRehabValues();"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$rehabCostFinanced); ?>"
                                                    <?php
                                                    if (!(floatval(Strings::replaceCommaValues(HMLOLoanTermsCalculation::$rehabCostFinanced)) > 0
                                                        && !loanForm::isVisible('rehabCostFinanced'))) {
                                                        echo BaseHTML::fieldAccess(['fNm' => 'rehabCostFinanced', 'sArr' => $secArr, 'opt' => 'I']);
                                                    } ?>
                                                       autocomplete="off">
                                                <!--                                                <span class="input-group-text form-control input-sm"-->
                                                <!--                                                      id="rehabCostFinanced"-->
                                                <!--                                                      name="rehabCostFinanced">-->
                                                <!--                                                --><?php //echo Currency::formatDollarAmountWithDecimal($rehabCostFinanced); ?>
                                                <!--                                            </span>-->
                                            </div>
                                        <?php } else { ?>
                                            <h5><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$rehabCostFinanced); ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="row">

                    <?php if (!PageVariables::$publicUser) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('haveInterestreserve'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('haveInterestreserve', ' col-md-12'); ?>
                                <div class="col-md-12">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid "
                                                   for="haveInterestreserveYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="haveInterestreserve"
                                                       id="haveInterestreserveYes"
                                                       value="Yes"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('Yes', HMLOLoanTermsCalculation::$haveInterestreserve); ?>
                                                       onclick="showAndHideInterestReserve(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid "
                                                   for="haveInterestreserveNo">
                                                <input type="radio" name="haveInterestreserve"
                                                       id="haveInterestreserveNo"
                                                       value="No"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('No', HMLOLoanTermsCalculation::$haveInterestreserve); ?>
                                                       onclick="showAndHideInterestReserve(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                            </label></div>
                                    <?php } else { ?>
                                        <h5><?php echo HMLOLoanTermsCalculation::$haveInterestreserve; ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 addToTotalProjectValueClass"
                             style="<?php echo(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->haveInterestreserve != 'Yes' ? 'display:none;' : ''); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('addToTotalProjectValue', 'col-md-6'); ?>
                                <div class="col-md-12">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid "
                                                   for="addToTotalProjectValueYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="addToTotalProjectValue"
                                                       id="addToTotalProjectValueYes"
                                                       value="Yes"
                                                       onclick="calculateTotalProjectCost();"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('Yes', LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->addToTotalProjectValue); ?>
                                                ><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid "
                                                   for="addToTotalProjectValueNo">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="addToTotalProjectValue"
                                                       id="addToTotalProjectValueNo"
                                                       value="No"
                                                       onclick="calculateTotalProjectCost();"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('No', LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->addToTotalProjectValue); ?>
                                                ><span></span>No
                                            </label>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->addToTotalProjectValue; ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="col-md-12 propertyNeedRehabinitialTddisp  initialAdvance_disp <?php echo loanForm::showField('initialAdvance'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('initialAdvance', 'col-md-6 font-weight-bold'); ?>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'initialAdvance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               placeholder="0.00"
                                               name="initialAdvance" id="initialAdvance"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                               onkeyup="return restrictAlphabetsLoanTerms(this);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$initialAdvance); ?>"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                               placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'initialAdvance', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        <div class="input-group-append ">
                                                                <span class="input-group-text">
                                                                <i
                                                                        class="fa fa-info-circle tooltipClass text-primary"
                                                                        data-html="true"
                                                                        title="Initial Advance is the amount of money provided at closing for the Rehab or Construction. It is considered the 1st draw as well."></i></span>
                                        </div>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LoanInfo::$initialAdvance) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12  propertyNeedRehabinitialTddisp totalDrawsFunded totalDrawsFunded_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'totalDrawsFunded', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv; ?>">
                        <div class="row form-group">
                            <?php
                            echo loanForm::label2(
                                'totalDrawsFunded',
                                'col-md-6 '
                            ); ?>
                            <div class="col-md-6">
                                <h5>$
                                    <span id="totalDrawsFunded"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalDrawsFunded) ?></span>
                                </h5>
                            </div>
                        </div>
                    </div>
                    <?php
                    if (!PageVariables::$publicUser) { ?>
                        <div class="col-md-6  autoCalcTLAARVDisp doesPropertyNeedRehabDispDiv <?php echo BaseHTML::fieldAccess(['fNm' => 'autoCalTotalLoanAmount', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                             style="<?php echo HMLOLoanTermsCalculation::$autoCalcTLAARVDisp . ' ' . HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispDiv ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold ">
                                    <i data-html="true"
                                       class="fa fa-info-circle text-primary tooltipClass mr-2 autoCalARVToolTip"
                                       title="<?php echo HMLOLoanTermsCalculation::$autoCalARVToolTip; ?>"></i>
                                    Auto-calc
                                    Total
                                    Loan Amount based
                                    on ?
                                </label>
                                <div class="col-md-12">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid "
                                                   for="autoCalcTLAARVYes">
                                                <input type="radio"
                                                       name="autoCalcTLAARV"
                                                       value="Yes"
                                                       id="autoCalcTLAARVYes"
                                                       onclick="autoCalculateTotalLoanAmountARVNew(1);"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('Yes', HMLOLoanTermsCalculation::$autoCalcTLAARV); ?>>
                                                <span></span>ARV
                                            </label>
                                            <label class="radio radio-solid "
                                                   for="autoCalcTLAARVLTC">
                                                <input type="radio"
                                                       name="autoCalcTLAARV"
                                                       value="LTC"
                                                       id="autoCalcTLAARVLTC"
                                                       onclick="autoCalculateTotalLoanAmountARVNew(1);"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('LTC', HMLOLoanTermsCalculation::$autoCalcTLAARV); ?>>
                                                <span></span>LTC
                                            </label>
                                            <label class="radio radio-solid "
                                                   for="autoCalcTLAARVNo">
                                                <input type="radio"
                                                       name="autoCalcTLAARV"
                                                       value="No"
                                                       onclick="autoCalculateTotalLoanAmountARVNew();"
                                                       id="autoCalcTLAARVNo"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('No', HMLOLoanTermsCalculation::$autoCalcTLAARV); ?>>
                                                <span></span>NA
                                            </label>
                                            <label class="radio radio-solid "
                                                   for="autoCalcTLAARVLTC2">
                                                <input type="radio"
                                                       name="autoCalcTLAARV"
                                                       value="LTC2"
                                                       onclick="autoCalculateTotalLoanAmountARVNew();"
                                                       id="autoCalcTLAARVLTC2"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('LTC2', HMLOLoanTermsCalculation::$autoCalcTLAARV); ?>>
                                                <span></span>LTC-2
                                            </label>
                                        </div>
                                    <?php } else {
                                        echo '<h5>' . HMLOLoanTermsCalculation::$autoCalcTLAARV . '</h5>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 ">
                            <?php if (LMRequest::$allowToEdit) { ?>
                                <input type="text"
                                       name="maxArvPer"
                                       id="maxArvPer"
                                       value="<?php echo HMLOLoanTermsCalculation::$maxArvPer; ?>"
                                       placeholder="0.0"
                                       onkeyup="validatePercentage(this)"
                                       onchange="autoCalculateTotalLoanAmountARVNew();validateMinMaxLoanGuidelines();"
                                       class="form-control input-sm"
                                       autocomplete="off"
                                       style="<?php echo HMLOLoanTermsCalculation::$maxArvPerDisp; ?>"
                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                            <?php } else {
                                echo '<h5>' . HMLOLoanTermsCalculation::$maxArvPer . '</h5>';
                            } ?>
                            <?php if (LMRequest::$allowToEdit) { ?>
                                <input type="text" name="maxLTCPer" id="maxLTCPer"
                                       value="<?php echo HMLOLoanTermsCalculation::$maxLTCPer; ?>"
                                       placeholder="0.0"
                                       onkeyup="validatePercentage(this)"
                                       onblur="autoCalculateTotalLoanAmountARVNew();validateMinMaxLoanGuidelines();"
                                       class="form-control input-sm"
                                       autocomplete="off"
                                       style="<?php echo HMLOLoanTermsCalculation::$maxLTCPerDisp; ?>"
                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                            <?php } else {
                                echo '<h5>' . HMLOLoanTermsCalculation::$maxLTCPer . '</h5>';
                            } ?>
                        </div>
                        <div class="col-md-12  showDivForLTC2"
                             style="<?php echo HMLOLoanTermsCalculation::$showDivForLTC2; ?>">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row">
                                        <label class="font-weight-bold col-md-12 font-weight-bold justify-content-center align-self-center"
                                               for="LTC2_additionalReserveInterest">
                                            Additional Reserve Interest
                                            <i id="LTC2_additionalReserveInterestTooltip"
                                               class="fa fa-info-circle text-primary ml-2 popoverClass"
                                               data-html="true"
                                               data-formula="<?php echo LTC2Variables::$LTC2_additionalReserveInterestTooltip; ?>"
                                               data-content="<?php echo LTC2Variables::$LTC2_additionalReserveInterestTooltipWithValues ? (
                                                   LTC2Variables::$LTC2_additionalReserveInterestTooltip . '<hr>' . LTC2Variables::$LTC2_additionalReserveInterestTooltipWithValues) : LTC2Variables::$LTC2_additionalReserveInterestTooltip ?>"></i>

                                            <i id="LTC2_baseLoanAmountTooltip"
                                               class="fa fa-info-circle text-danger ml-2 popoverClass"
                                               data-html="true"
                                               data-formula="<?php echo LTC2Variables::$LTC2_baseLoanAmountTooltip; ?>"
                                               data-content="<?php echo LTC2Variables::$LTC2_baseLoanAmountTooltipWithValues ? (
                                                       LTC2Variables::$LTC2_baseLoanAmountTooltip . '<hr>' . LTC2Variables::$LTC2_baseLoanAmountTooltipWithValues) . '<hr>' . LTC2Variables::$LTC2_baseLoanAmountFormatted : LTC2Variables::$LTC2_baseLoanAmountTooltip ?>"></i>
                                        </label>
                                        <div class="col-md-12">
                                            <input type="text"
                                                <?php echo HMLOLoanTermsCalculation::$enableFieldsForLTC2; ?>
                                                   class="form-control input-sm enableFieldsForLTC2"
                                                   name="LTC2_additionalReserveInterest"
                                                   id="LTC2_additionalReserveInterest"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileCalculatedValues()->LTC2_additionalReserveInterest); ?>"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row">
                                        <label class="font-weight-bold col-md-12 font-weight-bold justify-content-center align-self-center"
                                               for="LTC2_additionalOriginationInterest">
                                            Additional Origination Interest
                                            <i id="LTC2_additionalOriginationInterestTooltip"
                                               class="fa fa-info-circle text-primary ml-2 popoverClass"
                                               data-html="true"
                                               data-formula="<?php echo LTC2Variables::$LTC2_additionalOriginationInterestTooltip; ?>"
                                               data-content="<?php echo LTC2Variables::$LTC2_additionalOriginationInterestTooltipWithValues ? (
                                                   LTC2Variables::$LTC2_additionalOriginationInterestTooltip . '<hr>' . LTC2Variables::$LTC2_additionalOriginationInterestTooltipWithValues) : LTC2Variables::$LTC2_additionalOriginationInterestTooltip ?>"></i>
                                        </label>
                                        <div class="col-md-12">
                                            <input type="text"
                                                <?php echo HMLOLoanTermsCalculation::$enableFieldsForLTC2; ?>
                                                   class="form-control input-sm enableFieldsForLTC2"
                                                   name="LTC2_additionalOriginationInterest"
                                                   id="LTC2_additionalOriginationInterest"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileCalculatedValues()->LTC2_additionalOriginationInterest); ?>"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <?php } ?>

                </div>

            </div>
            <div class="col-md-6" style="border:1px;">
                <div class="row">
<?php if(!PageVariables::$publicUser){?>
                    <div class="col-md-12  haveInterestreserveDiv <?php echo loanForm::showField('prepaidInterestReserve'); ?>">
                        <div class="row form-group mt-2">
                            <label class="col-md-6 font-weight-bold "
                                   for="prepaidInterestReserve"><?php echo BaseHTML::fieldAccess(['fNm' => 'prepaidInterestReserve', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                <i id="prepaidInterestReserveTip" data-html="true"
                                   class="fa fa-info-circle text-primary tooltipClass"
                                   title="If financed this will increase the total loan amount. If not financed then this will not increase the total loan amount."></i>
                            </label>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input class="form-control input-sm" type="text"
                                               name="prepaidInterestReserve"
                                               id="prepaidInterestReserve"
                                               placeholder="0.00"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                               onblur="currencyConverter(this, this.value);
                                                               updateLoanDetail();validateMinMaxLoanGuidelines();"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$prepaidInterestReserve); ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'prepaidInterestReserve', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo HMLOLoanTermsCalculation::$prepaidInterestReserve; ?></h5>
                                <?php } ?>

                                <div class="input-group">
                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfMonthsPrepaid', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                                        </span>
                                    </div>
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <input
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfMonthsPrepaid', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                type="number"
                                                name="noOfMonthsPrepaid"
                                                id="noOfMonthsPrepaid"
                                                onchange="updateLoanDetail();calculateSimpleARVPercentage('simpleARV', '');"
                                                tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                value="<?php echo HMLOLoanTermsCalculation::$noOfMonthsPrepaid; ?>"
                                                placeholder="# Months"
                                                max="12"
                                                autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfMonthsPrepaid', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <h5><?php echo HMLOLoanTermsCalculation::$noOfMonthsPrepaid; ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php } ?>
                    <div class="col-md-12  cashOutDiv payOffMortgage1_disp <?php echo loanForm::showField('payOffMortgage1'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$cashOutDiv ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('payOffMortgage1', 'col-md-6'); ?>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffMortgage1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               placeholder="0.00"
                                               name="payOffMortgage1" id="payOffMortgage1"
                                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$payOffMortgage1) ?>"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                               placeholder="0.00"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffMortgage1', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$payOffMortgage1) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 cashOutDiv payOffMortgage2_disp <?php echo loanForm::showField('payOffMortgage2'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$cashOutDiv ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('payOffMortgage2', 'col-md-6'); ?>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffMortgage2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="payOffMortgage2" id="payOffMortgage2"
                                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$payOffMortgage2) ?>"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                               placeholder="0.00"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffMortgage2', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$payOffMortgage2) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 cashOutDiv payOffOutstandingTaxes_disp <?php echo loanForm::showField('payOffOutstandingTaxes'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$cashOutDiv ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('payOffOutstandingTaxes', 'col-md-6'); ?>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffOutstandingTaxes', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="payOffOutstandingTaxes"
                                               id="payOffOutstandingTaxes"
                                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$payOffOutstandingTaxes) ?>"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                               placeholder="0.00"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffOutstandingTaxes', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$payOffOutstandingTaxes) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 closingCostFinanced_disp <?php echo loanForm::showField('closingCostFinanced', HMLOLoanTermsCalculation::$closingCostFinanced, !PageVariables::$publicUser ? true : false); ?>">
                        <div class="row form-group mt-2">
                            <?php
                            echo loanForm::label2(
                                'closingCostFinanced',
                                'col-md-6 font-weight-bold ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                    'closingCostFinanced',
                                    tblFileHMLONewLoanInfo::class,
                                    'Closing Costs Financed',
                                )
                            );
                            ?>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'closingCostFinanced', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="closingCostFinanced"
                                               id="closingCostFinanced"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$closingCostFinanced) ?>"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               autocomplete="off"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();validateMinMaxLoanGuidelines();"
                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                               placeholder="0.00"
                                            <?php
                                            if (!(floatval(HMLOLoanTermsCalculation::$closingCostFinanced) > 0
                                                && !loanForm::isVisible('closingCostFinanced'))) {
                                                echo BaseHTML::fieldAccess(['fNm' => 'closingCostFinanced', 'sArr' => $secArr, 'opt' => 'I']);
                                            }

                                            //echo BaseHTML::fieldAccess(['fNm' => 'closingCostFinanced', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$closingCostFinanced) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 propertyNeedRehabinitialTddisp  currentLoanBalance  currentLoanBalance_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'currentLoanBalance', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                         style="<?php if (HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv == 'display: flex;') echo 'display: block;'; ?>">
                        <div class="row form-group mt-2">
                            <label class="col-md-6 font-weight-bold"
                                   for="currentLoanBalance">
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'currentLoanBalance', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                <i id="currentLoanBalanceTooltip"
                                   data-html="true"
                                   class="fa fa-info-circle text-primary popoverClass"
                                   title="<?php echo BaseHTML::fieldAccess(['fNm' => 'currentLoanBalance', 'sArr' => $secArr, 'opt' => 'L']); ?>"
                                   data-formula="<?php echo HMLOLoanTermsCalculation::$currentLoanBalanceTooltip; ?>"
                                   data-content="<?php echo HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues ? (HMLOLoanTermsCalculation::$currentLoanBalanceTooltip . '<hr>' . HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues) : HMLOLoanTermsCalculation::$currentLoanBalanceTooltip; ?>"></i>
                            </label>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <span class="form-control"
                                          id="currentLoanBalance"
                                          name="currentLoanBalance"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$currentLoanBalance) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 LOCTotalLoanAmtHide <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'D']); ?>
                    <?php if (LMRequest::$PCID == 4666 && PageVariables::$publicUser == 1) { ?>  echo 'hide'; <?php } ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$LOCTotalLoanAmtHideDispOpt ?>/*background-color: #F3F2D1*/">
                        <div class="row form-group mt-2">
                            <label
                                    class="col-md-6  font-weight-bold justify-content-center align-self-center"
                                    for="totalLoanAmount1">
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                <i id="tLAToolTip"
                                   data-html="true"
                                   class="fa fa-info-circle text-primary ml-2 popoverClass <?php
                                   /* Customization for PCID = 4666 -  Notes Email - 33735 Story */
                                   if (LMRequest::$PCID == 4666) {
                                       echo 'hide';
                                   } ?>"
                                   title="Total Loan Amount"
                                   data-formula="<?php echo HMLOLoanTermsCalculation::$tLAToolTip; ?>"
                                   data-content="<?php echo HMLOLoanTermsCalculation::$tLAToolTipWithValues ? (
                                       HMLOLoanTermsCalculation::$tLAToolTip . '<hr>' . HMLOLoanTermsCalculation::$tLAToolTipWithValues) : HMLOLoanTermsCalculation::$tLAToolTip ?>"></i>
                            </label>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               name="totalLoanAmount"
                                               placeholder="0.00"
                                               id="totalLoanAmount1"
                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'M']); ?> form-control input-sm"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalLoanAmount) ?>"
                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                            <?php if ((LoanInfo::$typeOfHMLOLoanRequesting != 'Purchase'
                                                    && LoanInfo::$typeOfHMLOLoanRequesting != 'Commercial Purchase')
                                                || ($fileTab == 'FA'
                                                    && glCustomJobForProcessingCompany::isPC_CRB(LMRequest::$PCID))
                                            ) { ?>
                                                style="background-color: rgb(213, 213, 213);"
                                                readonly
                                            <?php } else { ?>
                                                onchange="updateLoanDetail('totalloanamount'); "
                                            <?php } ?>
                                            <?php echo LTC2Variables::$readOnlyForLTC2Fields; ?>
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalLoanAmount) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 hideFieldsForLTC2_RefinanceCategory"
                         style="<?= HMLOLoanTermsCalculation::$hideFieldsForLTC2_RefinanceCategory; ?>">
                        <div class=" rehabConsCls lineOfCreditProp LTVPercentageDisp  <?php echo loanForm::showField('ltv'); ?>"
                             style="<?php echo HMLOLoanTermsCalculation::$rehabConsCls ?><?php echo HMLOLoanTermsCalculation::$lineOfCreditProp; ?><?php echo HMLOLoanTermsCalculation::$LTVPercentageDisp; ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('ltv', 'col-md-6'); ?>
                                <div class="col-md-6">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <input type="text"
                                               name="CORefiLTVPercentage"
                                               id="CORefiLTVPercentage"
                                               onchange="calculateCORefiLoanAmtByLTVPercentage();"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               class="form-control input-sm <?php echo BaseHTML::checkMan($fldArr, $fileTab) ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'ltv', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                               value="<?php echo HMLOLoanTermsCalculation::$CORefiLTVPercentage ?>"
                                               maxlength="8"
                                               onkeyup="validatePercentage(this)" <?php echo BaseHTML::fieldAccess(['fNm' => 'ltv', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <?php } else {
                                        echo '<b> ' . HMLOLoanTermsCalculation::$CORefiLTVPercentage . ' %</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12  rehabConsCls lineOfCreditProp <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'D']); ?> "
                         style="<?php echo HMLOLoanTermsCalculation::$rehabConsCls; ?><?php echo HMLOLoanTermsCalculation::$lineOfCreditProp; ?>">
                        <div class="row form-group">
                            <label
                                    class="col-md-6 font-weight-bold justify-content-center align-self-center">
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                <i class="fa fa-info-circle text-primary popoverClass ml-2"
                                   data-html="true"
                                   id="totalLoanAmountToolTip"
                                   title="Total Loan Amount"
                                   data-formula="<?php echo HMLOLoanTermsCalculation::$tLAToolTip; ?>"
                                   data-content="<?php echo HMLOLoanTermsCalculation::$tLAToolTipWithValues ? (
                                       HMLOLoanTermsCalculation::$tLAToolTip . '<hr>' . HMLOLoanTermsCalculation::$tLAToolTipWithValues) : HMLOLoanTermsCalculation::$tLAToolTip ?>"></i>
                            </label>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <span class="form-control input-sm input-group-text totalLoanAmount"
                                          id="coTotalAmt">
                                                    <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalLoanAmount) ?>
                                                </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12  rehabConsCls totalCashOut_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'totalCashOut', 'sArr' => $secArr, 'opt' => 'D']); ?> "
                         style="<?php echo HMLOLoanTermsCalculation::$rehabConsCls; ?><?php echo HMLOLoanTermsCalculation::$totalCashOutDisp; ?>">
                        <div class="row form-group mt-2">
                            <label
                                    class="col-md-6">
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'totalCashOut', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                <i class="fa fa-info-circle text-primary popoverClass ml-2"
                                   data-html="true"
                                   id="totalCashOutToolTip"
                                   title="Total Cash Out"
                                   data-formula="<?php echo HMLOLoanTermsCalculation::$totalCashOutToolTip; ?>"
                                   data-content="<?php echo HMLOLoanTermsCalculation::$totalCashOutToolTipWithValues ? (
                                       HMLOLoanTermsCalculation::$totalCashOutToolTip . '<hr>' . HMLOLoanTermsCalculation::$totalCashOutToolTipWithValues) : HMLOLoanTermsCalculation::$totalCashOutToolTip ?>"></i>
                            </label>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <span class="form-control input-group-text input-sm"
                                          id="totalCashOut">
                                                    <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalCashOutAmt) ?>
                                                </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 LOCTotalLoanAmt LOCTotalLoanAmt_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$LOCTotalLoanAmtDispOpt ?>">
                        <div class="row form-group">
                            <label class="col-md-6 font-weight-bold"
                                   for="LOCTotalLoanAmt"><?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm totalLoanAmount <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="LOCTotalLoanAmt" id="LOCTotalLoanAmt"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$LOCTotalLoanAmt) ?>"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                               placeholder="0.00"
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$LOCTotalLoanAmt) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12   doesPropertyNeedRehabDispDiv totalProjectCost_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'totalProjectCost', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispDiv ?>">
                        <div class="row form-group mt-2">
                            <label
                                    class="col-md-6 font-weight-bold">
                                <span class="totProjectCost"><?php echo HMLOLoanTermsCalculation::$totProjectCostLbl ?></span>
                                <i id="TPCToolTip"
                                   class="fa fa-info-circle  text-primary ml-2 tooltipClass"
                                   data-html="true"
                                   data-formula="<?php echo HMLOLoanTermsCalculation::$TPCToolTip; ?>"
                                   title="<?php echo HMLOLoanTermsCalculation::$TPCToolTipWithValues ? (HMLOLoanTermsCalculation::$TPCToolTip . '<hr>' . HMLOLoanTermsCalculation::$TPCToolTipWithValues) : (HMLOLoanTermsCalculation::$TPCToolTip); ?>"></i>
                            </label>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <span class="form-control input-sm input-group-text"
                                          id="totalProjectCost">
                                                    <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalProjectCost) ?>
                                                </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="NewTPCDiv"
                         class="col-md-12 <?php echo BaseHTML::fieldAccess(['fNm' => 'totalProjectCostNew', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$NewTotalProjectCostDisp; ?>">
                        <div class="row form-group mt-2">
                            <label class="col-md-6 font-weight-bold">
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'totalProjectCostNew', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                <i id="NewTPCToolTip"
                                   class="fa fa-info-circle  text-primary ml-2 tooltipClass"
                                   data-html="true"
                                   data-formula="<?php echo HMLOLoanTermsCalculation::$NewTPCToolTip; ?>"
                                   title="<?php echo HMLOLoanTermsCalculation::$NewTPCToolTipWithValues ? (HMLOLoanTermsCalculation::$NewTPCToolTip . '<hr>' . HMLOLoanTermsCalculation::$NewTPCToolTipWithValues) : HMLOLoanTermsCalculation::$NewTPCToolTip; ?>"></i>
                            </label>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <span class="input-group-text form-control" id="NewTotalProjectCost">
                                                <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$NewTotalProjectCost) ?>
                                            </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=" col-md-12"> <!-- Second Section Start -->
                        <div class="   landValueCls landValue_disp <?php echo loanForm::showField('landValue'); ?>"
                             style="<?php echo LoanInfo::$landValueCls ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('landValue', 'col-md-6 '); ?>
                                <div class="col-md-6">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'landValue', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="landValue" id="landValue"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$landValue) ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   placeholder="0.00"
                                                   TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'landValue', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LoanInfo::$landValue) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                    </div> <!-- Second Div End -->
                    <?php
                    if (isset($glRestrictCustomFields[LMRequest::$PCID])) { //https://www.pivotaltracker.com/story/show/161218497
                        if (in_array('finalLoanAmt', $glRestrictCustomFields[LMRequest::$PCID])) { ?>
                            <div class="col-md-12 <?php echo loanForm::showField('finalLoanAmt'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('finalLoanAmt', 'col-md-6 '); ?>
                                    <div class="col-md-6">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <div class="input-group">
                                                <span class="input-group-addon">$</span>
                                                <input name="finalLoanAmt" id="finalLoanAmt"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'finalLoanAmt', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$finalLoanAmt); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'finalLoanAmt', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            </div>
                                        <?php } else { ?>
                                            <h5><?php echo HMLOLoanTermsCalculation::$finalLoanAmt ?></h5><?php } ?>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
        <div class="col-md-12 mt-4">
            <div class="row">
                <div class="col-12 col-md-3 col-lg-3 mb-2 propertyNeedRehabinitialTddisp simplearvpercentage_disp propertyNeedRehabinitialTddisp simplearvpercentage_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'simplearvpercentage', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                     style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv ?>">
                    <div class="card card-custom bg-light-green desktop">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label  m-0">
                                        <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           id="simpleARVTooltip"
                                           data-html="true"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$simpleARVTooltip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$simpleARVTooltipWithValues ? (HMLOLoanTermsCalculation::$simpleARVTooltip . '<hr>' . HMLOLoanTermsCalculation::$simpleARVTooltipWithValues) : HMLOLoanTermsCalculation::$simpleARVTooltip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'simplearvpercentage', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span id="simpleARV">
                                                    <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$simpleARV) ?>
                                                </span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-3 col-lg-3 mb-2 propertyNeedRehabinitialTddisp arvpercentage_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'arvpercentage', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                     style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv ?>">
                    <div class="card card-custom bg-light-green">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label m-0">
                                        <i id="fullARVTooltip"
                                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$fullARVTooltip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$fullARVTooltipWithValues ? (HMLOLoanTermsCalculation::$fullARVTooltip . '<hr>' . HMLOLoanTermsCalculation::$fullARVTooltipWithValues) : HMLOLoanTermsCalculation::$fullARVTooltip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'arvpercentage', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="ARV"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$ARV); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-2 acquisitionLTVTD acquisitionLTV_disp
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'acquisitionLTV', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                     style="<?php echo HMLOLoanTermsCalculation::$acquisitionLTVTD; ?>">
                    <div class="card card-custom bg-light-green desktop">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label m-0">
                                        <i id="acquisitionLTVTooltip"
                                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$acquisitionLTVTooltip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$acquisitionLTVTooltipWithValues ? (HMLOLoanTermsCalculation::$acquisitionLTVTooltip . '<hr>' . HMLOLoanTermsCalculation::$acquisitionLTVTooltipWithValues) : HMLOLoanTermsCalculation::$acquisitionLTVTooltip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'acquisitionLTV', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="acquisitionLTV"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$acquisitionLTV); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-3 mb-2 marketLTVTD marketLTV_disp
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'marketLTV', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                     style="<?php echo HMLOLoanTermsCalculation::$marketLTVTD; ?>">
                    <div class="card card-custom bg-light-green">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label m-0">
                                        <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           id="marketLTVToolTip"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$marketLTVToolTip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$marketLTVToolTipWithValues ? (HMLOLoanTermsCalculation::$marketLTVToolTip . '<hr>' . HMLOLoanTermsCalculation::$marketLTVToolTipWithValues) : HMLOLoanTermsCalculation::$marketLTVToolTip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'marketLTV', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="marketLTV"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$marketLTV); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-3 mb-2  Loan-to-Cost_disp
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost', 'sArr' => $secArr, 'opt' => 'D']); ?>">
                    <div class="card card-custom bg-light-green">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label">
                                        <i id="LTCToolTip"
                                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$LTCToolTip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$LTCToolTipWithValues ? (HMLOLoanTermsCalculation::$LTCToolTip . '<hr>' . HMLOLoanTermsCalculation::$LTCToolTipWithValues) : HMLOLoanTermsCalculation::$LTCToolTip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="Loan-to-Cost"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$LTC); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-3 mb-2 propertyNeedRehabinitialTddisp perRehabCostFinanced_disp
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'perRehabCostFinanced', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                     style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv; ?>">
                    <div class="card card-custom bg-light-green">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label">
                                        <i id="rehabCostFinancedTooltip"
                                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$rehabCostFinancedTooltip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$rehabCostFinancedTooltipWithValues ? (HMLOLoanTermsCalculation::$rehabCostFinancedTooltip . '<hr>' . HMLOLoanTermsCalculation::$rehabCostFinancedTooltipWithValues) : HMLOLoanTermsCalculation::$rehabCostFinancedTooltip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'perRehabCostFinanced', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="perRehabCostFinanced"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$perRehabCostFinanced); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-2  grossProfit_disp
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'grossProfit', 'sArr' => $secArr, 'opt' => 'D']);
                if (LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::PURCHASE
                    && LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE
                    && LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::DELAYED_PURCHASE
                    && LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE) {
                    echo ' d-none ';
                }
                ?>">
                    <div class="card card-custom bg-light-green">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label">
                                        <i id="grossProfitTooltip"
                                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$grossProfitTooltip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$grossProfitTooltipWithValues ? (HMLOLoanTermsCalculation::$grossProfitTooltip . '<hr>' . HMLOLoanTermsCalculation::$grossProfitTooltipWithValues) : HMLOLoanTermsCalculation::$grossProfitTooltip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'grossProfit', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        $
                                        <span
                                                id="grossProfit"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$grossProfit); ?></span>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-3 mb-2  grossProfitMargin_disp
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'grossProfitMargin', 'sArr' => $secArr, 'opt' => 'D']);
                if (LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::PURCHASE
                    && LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE
                    && LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::DELAYED_PURCHASE
                    && LoanInfo::$typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE) {
                    echo ' d-none ';
                }
                ?>">
                    <div class="card card-custom bg-light-green">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label">
                                        <i id="grossProfitMarginTooltip"
                                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$grossProfitMarginTooltip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$grossProfitMarginTooltipWithValues ? (HMLOLoanTermsCalculation::$grossProfitMarginTooltip . '<hr>' . HMLOLoanTermsCalculation::$grossProfitMarginTooltipWithValues) : HMLOLoanTermsCalculation::$grossProfitMarginTooltip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'grossProfitMargin', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="grossProfitMargin"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$grossProfitMargin); ?></span>
                                        %
                                    </h3>
                                </div>
                                `
                            </div>
                        </div>
                    </div>
                </div>
                <!-- New LTC (Fees & Cost)-->
                <div id="NewLTCDiv"
                     class="col-12 col-md-6 col-lg-3 mb-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-New', 'sArr' => $secArr, 'opt' => 'D']);
                     if(PageVariables::$publicUser){ echo 'd-none'; }
                     ?>">
                    <div class="card card-custom bg-light-green desktop">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label">
                                        <i id="NewLTCToolTip"
                                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$NewLTCToolTip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$NewLTCToolTipWithValues ? (HMLOLoanTermsCalculation::$NewLTCToolTip . '<hr>' . HMLOLoanTermsCalculation::$NewLTCToolTipWithValues) : HMLOLoanTermsCalculation::$NewLTCToolTip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-New', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span id="NewLoanToCost"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$NewLoanToCost); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                        class="col-12 col-md-6 col-lg-3 mb-2 LTCInitialLoanAmountCls <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-initial-Loan-Amount', 'sArr' => $secArr, 'opt' => 'D']); ?>">
                    <div class="card card-custom bg-light-green desktop">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label">
                                        <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           id="LTCInitialLoanAmountToolTip"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTipWithValues ? (HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTip . '<hr>' . HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTipWithValues) : HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-initial-Loan-Amount', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="LTCInitialLoanAmount"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$LTCInitialLoanAmount); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                        class="col-12 col-md-6 col-lg-3 LTCMarketValueCls mb-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-Market-Value', 'sArr' => $secArr, 'opt' => 'D']); ?>">
                    <div class="card card-custom bg-light-green desktop">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label">
                                        <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           id="LTCMarketValueToolTip"
                                           data-formula="<?php echo HMLOLoanTermsCalculation::$LTCMarketValueToolTip; ?>"
                                           title="<?php echo HMLOLoanTermsCalculation::$LTCMarketValueToolTipWithValues ? (HMLOLoanTermsCalculation::$LTCMarketValueToolTip . '<hr>' . HMLOLoanTermsCalculation::$LTCMarketValueToolTipWithValues) : HMLOLoanTermsCalculation::$LTCMarketValueToolTip; ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-Market-Value', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="LTCMarketValue"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$LTCMarketValue); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                        class="col-12 col-md-6 col-lg-3 LTCOriginalPurchasePriceCls mb-2 <?php echo HMLOLoanTermsCalculation::$LTCOriginalPurchasePriceCls; ?>
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-Original-Purchase-Price', 'sArr' => $secArr, 'opt' => 'D']); ?>">
                    <div class="card card-custom bg-light-green desktop">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <div class="row">
                                    <h3 class="card-label">
                                        <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                           data-html="true"
                                           id="LTCOriginalPurchasePriceToolTip"
                                           title="<?php echo htmlspecialchars(HMLOLoanTermsCalculation::$LTCOriginalPurchasePriceToolTip); ?>">
                                        </i>
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-Original-Purchase-Price', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                        <span
                                                id="LTCOriginalPurchasePriceValue"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$LTCOriginalPurchasePriceValue); ?></span>
                                        %
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <?php echo CustomField::RenderForTabSection(
                PageVariables::$PCID,
                tblFile::class,
                LMRequest::$LMRId,
                'LT',
                $fileTab,
                LMRequest::$activeTab,
                LMRequest::myFileInfo()->getFileTypes(),
                LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<?php loanForm::popSectionID(); ?>

