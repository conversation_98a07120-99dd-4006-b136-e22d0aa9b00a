<?php
global $fileTab, $HMLOPCAmortizationValPCLoanTerms, $isHMLO, $fldEditOpt, $lockedSections, $isEF, $reqForLoanProUnderwriting,
       $minRate, $maxRate, $includeCCF, $maxLTV, $minLoanAmount, $maxLoanAmount, $maxARV, $LGMaxLTC, $minMidFico, $maxMidFico, $minPropertyForFixFlop,
       $maxPropertyForFixFlop, $minPropertyForGrndConst, $maxPropertyForGrndConst,
       $maxPoints, $minPoints, $downPaymentPercent, $HMLOPCElgibleState, $propertyState, $stateArray, $fileRecordedDate,
       $ARVCALRELEASEDATE, $paydownamount, $guideLineMinSeasoningBusinessBankruptcyVal, $guideLineMinSeasoningForeclosureVal,
       $guideLineMinSeasoningPersonalBankruptcyVal,
       $guideLineMinTimeVal, $guideLineMinTimeVal, $minDSCR, $loanGuideLineId, $fileInfo, $lockedFile, $lockedSectionTxt;


use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPCID;
use models\constants\gl\glUserGroup;
use models\constants\packageId;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\cypher;
use models\PageVariables;
use models\standard\BaseHTML;
use models\standard\Strings;



if (in_array('Loan Terms', $lockedSections)) {
    $BackupAllowToEdit = LMRequest::$allowToEdit;
    $allowToEdit = false;
    LMRequest::$allowToEdit = false;
}
?>

<?php require CONST_BO_PATH .'LMRequest/loanInfo/loanSettingSection.php'; ?>

<div class="row">
    <div class="col-md-12">
        <?php require CONST_BO_PATH .'LMRequest/sections/loanSettingForm.php'; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <?php
        require CONST_BO_PATH.'LMRequest/loanInfo/importantDatesSection.php'; ?>
    </div>
</div>

<?php
require CONST_BO_PATH.'LMRequest/loanInfo/rehabSection.php';
require CONST_BO_PATH.'LMRequest/loanInfo/loanTermsSection.php';
?>

<?php
require_once CONST_BO_PATH .'LMRequest/sections/penaltyExtensionExitFeeSection.php';
require_once CONST_BO_PATH .'LMRequest/sections/refinanceMortgageForm.php';
require CONST_BO_PATH .'LMRequest/sections/feesAndCostSection.php';
require CONST_BO_PATH .'subjectPropertyCashFlow.php';
?>

<div class="row <?php if (LMRequest::$PCID == 4208 || LMRequest::$PCID == 4636 || LMRequest::$PCID == 4642) {
    echo 'd-none';
} ?>">
    <div class="col-md-6">
        <?php require_once CONST_BO_PATH .'LMRequest/sections/requiredLiquidityReservesSection.php'; ?>
    </div>
    <div class="col-md-6">
        <?php require_once CONST_BO_PATH .'LMRequest/sections/cashToCloseSection.php'; ?>
    </div>
</div>

<?php
require CONST_BO_PATH .'LMRequest/loanInfo/additionalLoanSettingsSection.php';
require CONST_BO_PATH .'LMRequest/loanInfo/additionalQuestions.php';
require CONST_BO_PATH .'estimatedProjectCost.php';
require CONST_BO_PATH .'loanOriginatorInfo.php'; ?>
<input type="hidden" name="isHMLOOpt" value="<?php echo $isHMLO ?>"/>
<input type="hidden" name="isEFOOpt" value="<?php echo $isEF; ?>"/>
<input type="hidden" name="isFeeUpdated" value="1"/>
<input type="hidden" name="midFico" id="midFicoScore" value="<?php echo LoanInfo::$midFicoScore; ?>"/>
<input type="hidden" name="fixflipProp" id="borNoOfREPropertiesCompleted"
       value="<?php echo LoanInfo::$borNoOfREPropertiesCompleted; ?>"/>
<input type="hidden" name="rehabGround" id="borRehabPropCompleted"
       value="<?php echo LoanInfo::$borRehabPropCompleted; ?>"/>
<input type="hidden" name="addRentableSqFt" id="addRentableSqFt"
       value="<?php echo Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyRentableSqFt; ?>">
<input type="hidden" name="noUnitsOccupied" id="noUnitsOccupied"
       value="<?php echo Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits; ?>">
<div class="card card-custom additionalQuestionCard ">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2(
            'additionalQuestion',
            true,
            true,
            '',
            'Additional Terms &amp; Requirements for Loan Processing &amp; Underwriting',
            PageVariables::$userRole == 'Manager' ? 'This section is controlled in system settings-->Custom Loan Guidelines. You can control the content based on loan program as well.' : ''
        ); ?>
    </div>
    <div class="card-body additionalQuestionCard_body">
        <div class="row  ">
            <div class="col-md-12">
                <?php echo loanForm::textarea(
                    'expectForDueDiligence',
                    LMRequest::$allowToEdit && PageVariables::$userGroup != glUserGroup::CLIENT,
                    LoanInfo::$tabIndex++,
                    Strings::showField('expectForDueDiligence', 'fileHMLOPropertyInfo') ? urldecode(Strings::showField('expectForDueDiligence', 'fileHMLOPropertyInfo')) : $reqForLoanProUnderwriting,
                    'tinyMceClass'
                ); ?>
            </div>
        </div>
    </div>
</div>

<?php if (LMRequest::$allowToEdit) { ?>
    <div style="text-align: center">
        <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveBtn" value="Save"
               tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
        <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveNextBtn" value="Save & Next"
               tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
               onclick="if(this.disabled==false) {return true;} else {return false;}">
        <?php
        if (LMRequest::$PCID == glPCID::PCID_CRB) {
            $package = glCustomJobForProcessingCompany::getTermSheetPackageId(LoanInfo::$fileloanProgram);
        } elseif (LMRequest::$PCID == glPCID::PCID_FYNF_LLC) {
            $package = packageId::FYNF_LLC_LOAN_TERM_SHEET;
        } elseif (glCustomJobForProcessingCompany::isPC_CentrifundLLC(LMRequest::$PCID)) {
            $package = packageId::TRANSACTION_SUMMARY;
        }
        if (empty($package)) {
            $package = packageId::LOAN_TERM_SHEET;
        }

        if ($isHMLO == 1 && LMRequest::$activeTab == 'HMLI') {
            $url = CONST_SITE_URL . 'package/pkgController.php?rId=' . cypher::myEncryption(LMRequest::File()->getTblFileResponse_by_LMRId()->LMRResponseId) . '&amp;bn=' . cypher::myEncryption(LMRequest::File()->brokerNumber) . '&amp;lId=' . cypher::myEncryption(LMRequest::$LMRId) . '&amp;pkgID=' . cypher::myEncryption($package) . '&opt=sample';
            ?>
            <a href="<?php echo $url; ?>" target="_blank" style="" class="btn btn-primary">
                <?php if (glCustomJobForProcessingCompany::isPC_CentrifundLLC(LMRequest::$PCID)) { ?>
                    View Transaction Summary
                <?php } else { ?>View Term Sheet<?php } ?></a>
            <?php
        }
        ?>
    </div>
<?php } ?>
<script>
    $('select.js-example-basic-multiple').select2();
</script>


