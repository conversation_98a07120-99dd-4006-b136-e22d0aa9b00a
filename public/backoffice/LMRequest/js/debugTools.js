class debugTools {
    static clearForm(formID)
    {
        let loanModForm = $('#' + formID);

        loanModForm.find('textarea').each(function () {
            $(this).val('');
        });

        loanModForm.find('select').each(function () {
            $(this).val('');
        });

        loanModForm.find('input').each(function () {
            let type = $(this).prop('type');
            let name = $(this).prop('name');
            let placeholder = $(this).prop('placeholder');
            if (type === 'hidden') {
                if (name === 'waterFront' || name === 'basementHome' || name === 'basementFinish' || name === 'garageHome') {
                    $(this).val('');
                } else {
                    return;
                }
            }

            if (type === 'submit') {
                return;
            }

            if (type === 'radio') {
                $("input:radio[name=\"" + name + "\"]").attr('checked', false);
                return;
            }

            if (type === "checkbox") {
                $(this).prop('checked', false);
                return;
            }

            $(this).val('');
        });
    }

    static fillForm(formID, testB)
    {
        let loanModForm = $('#' + formID);

        loanModForm.find('textarea').each(function () {
            if($(this).val().trim()) {
                return;
            }

            $(this).val(testB ? '$%$\\\/\/\'":!.abc456' : '$%$\\\/\/\'":!.abc123');
        });

        $('select').each(function () {
            let value = $(this).val();

            if (value) {
                return;
            }
            $(this).find('option').each(function () {
                let value = $(this).val();
                if (!value) {
                    return;
                }
                if ($(this).parent().val()) {
                    return;
                }
                $(this).parent().val(value);
            });
        });

        loanModForm.find('input').each(function () {
            let type = $(this).prop('type');
            let name = $(this).prop('name');
            let placeholder = $(this).prop('placeholder');
            if (type === 'hidden') {
                return;
            }

            if (type === 'radio') {
                let _radioElem = testB ? $("input:radio[name=\"" + name + "\"]:last") : $("input:radio[name=\"" + name + "\"]:first");

                _radioElem.click();
                _radioElem.attr('checked', true);
                return;
            }

            if (type === "checkbox") {
                $(this).prop('checked', true);
                return;
            }

            if (name === 'loanNumber') {
                return;
            }

            if (name === 'vacancyFactor') {
                $(this).val(testB ? 9 : 10);
                return;
            }

            if (name === 'vacancyFactorCommercial') {
                $(this).val(testB ? 9 : 10);
                return;
            }

            if (type === 'text') {
                if ($(this).val() && placeholder !== '0.00') {
                    return;
                }

                if ($(this).hasClass('zipCode')) {
                    $(this).val(testB ? '54321' : '12345');
                    return;
                }

                if ($(this).hasClass('mask_ssn')) {
                    $(this).val(testB ? '321214321' : '123121234');
                    return;
                }

                if ($(this).hasClass('mask_phone')
                    || $(this).hasClass('creditorAgentPhone_mask')
                    || $(this).hasClass('mask_cellnew')
                ) {
                    $(this).val(testB ? '32132143214321' : '12312312341234');
                    return;
                }

                if ($(this).hasClass('mask_ACTNumber')) {
                    $(this).val(testB ? '32132143214321' : '12312312341234');
                    return;
                }

                if (placeholder === 'MM/DD/YYYY') {
                    $(this).val(testB ? '1/1/2022' : '12/31/2022');
                    return;
                }

                if (placeholder === 'http://') {
                    $(this).val(testB ? 'http://www.siteb.com' : 'http://www.site.com');
                    return;
                }
                //                  (___) ___ - ____ Ext. ____
                if (placeholder === '(___) ___ - ____ Ext ____') {
                    $(this).val(testB ? '32132143214321' : '12312312341234');
                    return;
                }
                if(placeholder=== '___ - ___ - ____') {
                    $(this).val(testB ? '3213214321' : '1231231234');
                    return;
                }
                if (placeholder === '(___) ___ - ____') {
                    $(this).val(testB ? '3213214321' : '1231231234');
                    return;
                }
                if (placeholder === '(xxx)-xxx-xxxx') {
                    $(this).val(testB ? '3213214321' : '1231231234');
                    return;
                }
                if (placeholder === 'xxx-xxx-xxxx') {
                    $(this).val(testB ? '3213214321' : '1231231234');
                    return;
                }
                if (placeholder === '0.0') {
                    $(this).val(testB ? '21.99' : '12.99');
                    return;
                }

                if (placeholder === '0.00') {
                    $(this).val(testB ? '7,654,321.99' : '1,234,567.99');
                    return;
                }

                if (placeholder === 'CVV #') {
                    $(this).val(testB ? '4321' : '1234');
                    return;
                }
                if (placeholder === ' - Type Name Here - ') {
                    $(this).val(testB ? 'smith bob' : 'bob smith');
                    return;
                }

                if (placeholder === '___ - __ - ____') {
                    $(this).val(testB ? '321214321' : '123121234');
                    return;
                }

                if (placeholder === '__ - _______') {
                    $(this).val(testB ? '213214321' : '121231234');
                    return;
                }

                if (placeholder === '- Net -') {
                    $(this).val(testB ? '4321.99' : '1234.99');
                    return;
                }

                if (placeholder === '- Gross -') {
                    $(this).val(testB ? '4321.99' : '1234.99');
                    return;
                }

                $(this).val(testB ? '$%$\\\/\/\'":!.abc456' : '$%$\\\/\/\'":!.abc123');
                return;
            }
            if (type === 'number') {
                if (parseFloat($(this).val())) {
                    return;
                }

                if (placeholder === '0.00') {
                    console.log(name + ' invalid type');
                    $(this).val(testB ? '7321.99' : '1237.99');
                    return;
                }
                $(this).val(testB ? '4321' : '1234');
                return;
            }
            if (type === 'email') {
                $(this).val(testB ? '<EMAIL>' : '<EMAIL>');
                return;
            }
            if (type === 'date') {
                $(this).val(testB ? '2022-01-01' : '2022-12-31');
                return;
            }

            console.log(name + ' => ' + type + ' -- ' + placeholder);
        });
    }

    static clearLoanForm()
    {
        this.clearForm('loanModForm');
    }

    static fillLoanForm()
    {
        this.fillForm('loanModForm');
    }
}
