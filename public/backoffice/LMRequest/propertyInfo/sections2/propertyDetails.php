<?php

use models\constants\gl\glHMLOPresentOccupancy;
use models\constants\gl\glOccupancyArray;
use models\constants\gl\glPCID;
use models\constants\gl\glPropertyCondition;
use models\constants\gl\glUserRole;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;

use models\lendingwise\tblPropertiesDetails;
use models\myFileInfo;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$glHMLOPresentOccupancy = glHMLOPresentOccupancy::getForPCID(LMRequest::File()->FPCID);

$glPropertyCondition = glPropertyCondition::$glPropertyCondition;
$glOccupancyArray = glOccupancyArray::$glOccupancyArray;


$propertyEstimatedPropertyValue = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyEstimatedValue;
$annualPropertyTaxes = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->annualPropertyTaxes;
$propertyZillowValue = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyZillowValue;
$propertyPricePerDoor = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPricePerDoor;
$propertyURLLink1 = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyURLLink1;
$propertyURLLink2 = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyURLLink2;
$presentOccupancy = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy;
$propertyOccupancyNotes = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyOccupancyNotes;
$propertyCondition = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyCondition;
$propertyConditionNotes = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyConditionNotes;
$propertyYearRenovated = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyRenovatedYear;
$propertyOccupancy = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy;
$propertyTaxYear = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyTaxYear;
$propertyTaxDueDate = Dates::formatDateWithRE(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyTaxDueDate, 'YMD', 'm/d/Y');

$propertyPastDuePropertyTaxes = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPastDuePropertyTaxes;
$propertyPayoffAmt = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPayOffAmount;
$propertyAllocatedLoanAmount = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyAllocatedLoanAmount;
$propertyHMLOmlsnumber = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyMLSNumber;
$propertyPropAnnualInsurancePremiums = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyAnnualInsurancePremiums;
$propertyRehabToComplete = Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyRehabToComplete;

$propertyIsTaxesInsEscrowed = LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->isTaxesInsEscrowed;
$propertyAnnualPropTaxes1 = LMRequest::File()->getTblFilePropertyInfo_by_LMRId()->annualPropTaxes1;
$propertyTaxes1 = LMRequest::File()->getTblIncomeInfo_by_LMRId()->taxes1;

$refinanceBasedClass = ' refinanceBasedFieldsClass hide ';

if (in_array(Property::$typeOfHMLOLoanRequesting, [
    typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE,
    typeOfHMLOLoanRequesting::DELAYED_PURCHASE,
    typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE,
    typeOfHMLOLoanRequesting::REFINANCE,
])) {
    $refinanceBasedClass = ' refinanceBasedFieldsClass ';
}
?>
<div class="row PropertyDetails">
    <label class="col-lg-12 mb-4 px-4 py-2 bg-secondary font-weight-bold" id="propdetailstitle">
        <?php echo BaseHTML::getSubSectionHeading('PD', 'propDetailsSubSection'); ?>
    </label>

    <?php if (Property::$activeTab != 'CI') { ?>
        <div class="propdetails   col-md-6 estimatedPropertyValue_disp <?php echo loanForm::showField('estimatedPropertyValue'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('estimatedPropertyValue', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyEstimatedValue"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][estimatedPropertyValue]"
                                   id="estimatedPropertyValue"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($propertyEstimatedPropertyValue) ?>"
                                   size="13"
                                   maxlength="13"
                                   onblur="currencyConverter(this, this.value);propertyDetails.calculatePropertyLTV(this);propertyAnalysis.calculateQualifyingAllocatedLTV(this);"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   class="autosavePropertyDetails form-control input-sm propertyEstimatedValueEle <?php echo BaseHTML::fieldAccess(['fNm' => 'estimatedPropertyValue', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'estimatedPropertyValue', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal($propertyEstimatedPropertyValue); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>
        <!-- Estimated Property Value end MK -->


        <div class="propdetails   col-md-6 propertyAfterRepairValue_disp <?php echo loanForm::showField('propertyAfterRepairValue'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyAfterRepairValue', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyAfterRepairValue"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyAfterRepairValue]"
                                   id="propertyAfterRepairValue"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue) ?>"
                                   size="13"
                                   maxlength="13"
                                   onblur="currencyConverter(this, this.value);propertyDetails.calculateLoanToAfterRepairValue(this);"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   class="autosavePropertyDetails form-control input-sm propertyAfterRepairValueCalculate <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyAfterRepairValue', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyAfterRepairValue', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propdetails   col-md-6  propertyIsListedForSale_disp <?php echo loanForm::showField('propertyIsListedForSale'); ?> <?php echo $refinanceBasedClass; ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyIsListedForSale', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsListedForSaleYes_<?php echo Property::$propertyInfo->propertyId; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesDetails"
                                       data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                       data-column="propertyIsListedForSale"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsListedForSale]"
                                       id="propertyIsListedForSaleYes_<?php echo Property::$propertyInfo->propertyId; ?>"
                                       value="1"
                                       onclick="propertyDetails.showHideDiv('1','propertyListPrice_disp','propertyListDate_disp');"
                                       class="autosavePropertyDetails <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsListedForSale', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsListedForSale', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('1', Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyIsListedForSale); ?>/>
                                <span></span>
                                Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsListedForSaleNo_<?php echo Property::$propertyInfo->propertyId; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesDetails"
                                       data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                       data-column="propertyIsListedForSale"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsListedForSale]"
                                       id="propertyIsListedForSaleNo_<?php echo Property::$propertyInfo->propertyId; ?>"
                                       value="0"
                                       onclick="propertyDetails.showHideDiv('0','propertyListPrice_disp','propertyListDate_disp');"
                                       class="autosavePropertyDetails <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsListedForSale', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsListedForSale', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('0', Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyIsListedForSale); ?>/>
                                <span></span>
                                No
                            </label>
                        </div>
                    <?php } else { ?>
                        <b><?php echo Strings::booleanTextVal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyIsListedForSale); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propdetails   col-md-6 propertyListPrice_disp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'propertyIsListedForSale', 'sArr' => Property::$secArr, 'pv' => Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyIsListedForSale, 'av' => '1']); ?>  <?php echo loanForm::showField('propertyListPrice'); ?> <?php echo $refinanceBasedClass; ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyListPrice', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyListPrice"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyListPrice]"
                                   id="propertyListPrice"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyListPrice) ?>"
                                   size="13"
                                   maxlength="13"
                                   onblur="currencyConverter(this, this.value);"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyListPrice', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyListPrice', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyListPrice); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propdetails col-md-6 propertyListDate_disp <?php echo BaseHTML::parentFieldAccess(['fNm' => 'propertyIsListedForSale', 'sArr' => Property::$secArr, 'pv' => Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyIsListedForSale, 'av' => '1']); ?><?php echo $refinanceBasedClass; ?>
                <?php echo loanForm::showField('propertyListDate'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyListDate', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend ">
                            <span class="input-group-text">
                                <i class="fa fa-calendar text-primary"></i>
                            </span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyListDate"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyListDate]"
                                   id="propertyListDate"
                                   placeholder="MM/DD/YYYY"
                                   value="<?php echo Dates::formatDateWithRE(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyListDate, 'YMD', 'm/d/Y'); ?>"
                                   autocomplete="off"
                                   maxlength="10"
                                   size="12"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"
                                   class="autosavePropertyDetails dateNewClass form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyListDate', 'sArr' => Property::$secArr, 'opt' => 'M']); ?> "/>
                        </div>
                    <?php } else {
                        echo '<h5>' . Dates::formatDateWithRE(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyListDate, 'YMD', 'm/d/Y') . '</h5>';
                    } ?>
                </div>
            </div>
        </div>


        <div class="propdetails   col-md-6 propertyIsConstructionOrRehabComplete_disp <?php echo loanForm::showField('propertyIsConstructionOrRehabComplete'); ?> <?php echo $refinanceBasedClass; ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyIsConstructionOrRehabComplete', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsConstructionOrRehabCompleteYes_<?php echo Property::$propertyInfo->propertyId; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesDetails"
                                       data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                       data-column="propertyIsConstructionOrRehabComplete"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsConstructionOrRehabComplete]"
                                       id="propertyIsConstructionOrRehabCompleteYes_<?php echo Property::$propertyInfo->propertyId; ?>"
                                       value="1"
                                       class="autosavePropertyDetails <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsConstructionOrRehabComplete', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsConstructionOrRehabComplete', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('1', Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyIsConstructionOrRehabComplete); ?>/>
                                <span></span>
                                Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold"
                                   for="propertyIsConstructionOrRehabCompleteNo_<?php echo Property::$propertyInfo->propertyId; ?>">
                                <input type="radio"
                                       data-table="tblPropertiesDetails"
                                       data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                       data-column="propertyIsConstructionOrRehabComplete"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][propertyIsConstructionOrRehabComplete]"
                                       id="propertyIsConstructionOrRehabCompleteNo_<?php echo Property::$propertyInfo->propertyId; ?>"
                                       value="0"
                                       class="autosavePropertyDetails <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsConstructionOrRehabComplete', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyIsConstructionOrRehabComplete', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                    <?php echo Strings::isChecked('0', Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyIsConstructionOrRehabComplete); ?>/>
                                <span></span>
                                No
                            </label>
                        </div>
                    <?php } else { ?>
                        <b><?php echo Strings::booleanTextVal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyIsConstructionOrRehabComplete); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propdetails   col-md-6 propertyCostSpent_disp <?php echo loanForm::showField('propertyCostSpent'); ?> <?php echo $refinanceBasedClass; ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyCostSpent', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyCostSpent"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyCostSpent]"
                                   id="propertyCostSpent"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyCostSpent) ?>"
                                   size="13"
                                   maxlength="13"
                                   onblur="currencyConverter(this, this.value);"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyCostSpent', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyCostSpent', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyCostSpent); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propdetails   col-md-6 propertyPayOffLienRelease_disp <?php echo loanForm::showField('propertyPayOffLienRelease'); ?> <?php echo $refinanceBasedClass; ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyPayOffLienRelease', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyPayOffLienRelease"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyPayOffLienRelease]"
                                   id="propertyPayOffLienRelease"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPayOffLienRelease) ?>"
                                   size="13"
                                   maxlength="13"
                                   onblur="currencyConverter(this, this.value);"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyPayOffLienRelease', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyPayOffLienRelease', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPayOffLienRelease); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 propertyPurchasePrice_disp <?php echo loanForm::showField('propertyPurchasePrice'); ?>">

            <div class="row form-group ">
                <?php echo loanForm::label('propertyPurchasePrice', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyPurchasePrice"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyPurchasePrice]"
                                   id="propertyPurchasePrice"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPurchasePrice) ?>"
                                   size="13"
                                   maxlength="13"
                                   onblur="currencyConverter(this, this.value);propertyDetails.calculateLoanToPurchasePrice(this);"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   class="autosavePropertyDetails form-control input-sm propertyDetailsPurchasePriceEle <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyPurchasePrice', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyPurchasePrice', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPurchasePrice); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propdetails   col-md-6 propertyLTV_disp <?php echo loanForm::showField('propertyLTV'); ?>">
            <div class="row form-group">
                <?php
                echo loanForm::label('propertyLTV',
                    'col-md-5',
                    loanForm::getFieldLabel('propertyLTV') . '= ' . loanForm::getFieldLabel('allocatedLoanAmount') . ' / ' . loanForm::getFieldLabel('estimatedPropertyValue') . '* 100'
                ); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <input type="number"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyLTV"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyLTV]"
                                   id="propertyLTV"
                                   value="<?php echo(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyLTV) ?>"
                                   size="13"
                                   maxlength="13"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   readonly="readonly"
                                   onblur="currencyConverter(this, this.value);"
                                   class="autosavePropertyDetails form-control input-sm propertyLTVCalculate <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyLTV', 'sArr' => Property::$secArr, 'opt' => 'M']); ?> tooltipClass"
                                   title="<?php echo loanForm::getFieldLabel('propertyLTV') . '= ' . loanForm::getFieldLabel('allocatedLoanAmount') . ' / ' . loanForm::getFieldLabel('estimatedPropertyValue') . '* 100'; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyLTV', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    <?php } else { ?>
                        <b><?php echo(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyLTV); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propdetails   col-md-6 propertyLoanToPurchasePrice_disp <?php echo loanForm::showField('propertyLoanToPurchasePrice'); ?>  <?php echo (Property::$typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::PURCHASE) ? '' : ' hide '; ?>">
            <div class="row form-group ">
                <?php echo loanForm::label('propertyLoanToPurchasePrice',
                    'col-md-5',
                    loanForm::getFieldLabel('propertyLoanToPurchasePrice') . '= ' . loanForm::getFieldLabel('allocatedLoanAmount') . ' / ' . loanForm::getFieldLabel('propertyPurchasePrice') . '* 100'
                ); ?>
                <div class="col-md-7 ">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group ">
                            <input type="number"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyLoanToPurchasePrice"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyLoanToPurchasePrice]"
                                   id="propertyLoanToPurchasePrice"
                                   value="<?php echo(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyLoanToPurchasePrice) ?>"
                                   size="13"
                                   maxlength="13"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   onblur="currencyConverter(this, this.value);"
                                   readonly="readonly"
                                   class="autosavePropertyDetails form-control input-sm propertyLoanToPurchasePriceCalculate <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyLoanToPurchasePrice', 'sArr' => Property::$secArr, 'opt' => 'M']); ?> tooltipClass"
                                   title="<?php echo loanForm::getFieldLabel('propertyLoanToPurchasePrice') . ' = ' . loanForm::getFieldLabel('allocatedLoanAmount') . ' / ' . loanForm::getFieldLabel('propertyPurchasePrice') . '* 100'; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyLoanToPurchasePrice', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    <?php } else { ?>
                        <b><?php echo(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyLoanToPurchasePrice); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propdetails   col-md-6 propertyLoanToAfterRepairValue_disp <?php echo loanForm::showField('propertyLoanToAfterRepairValue'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyLoanToAfterRepairValue',
                    'col-md-5',
                    loanForm::getFieldLabel('propertyLoanToAfterRepairValue') . '= ' . loanForm::getFieldLabel('allocatedLoanAmount') . ' / ' . loanForm::getFieldLabel('propertyAfterRepairValue') . '* 100'
                ); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <input type="number"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyLoanToAfterRepairValue"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyLoanToAfterRepairValue]"
                                   id="propertyLoanToAfterRepairValue"
                                   value="<?php echo(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyLoanToAfterRepairValue) ?>"
                                   size="13"
                                   maxlength="13"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   readonly="readonly"
                                   onblur="currencyConverter(this, this.value);"
                                   class="autosavePropertyDetails form-control input-sm propertyLoanToAfterRepairValueCalculate <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyLoanToAfterRepairValue', 'sArr' => Property::$secArr, 'opt' => 'M']); ?> tooltipClass"
                                   title="<?php echo loanForm::getFieldLabel('propertyLoanToAfterRepairValue') . '= ' . loanForm::getFieldLabel('allocatedLoanAmount') . ' / ' . loanForm::getFieldLabel('propertyAfterRepairValue') . '* 100'; ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyLoanToAfterRepairValue', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    <?php } else { ?>
                        <b><?php echo(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyLoanToAfterRepairValue); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propdetails   col-md-6 propertyAcquisitionDate_disp <?php echo loanForm::showField('propertyAcquisitionDate'); ?> <?php echo $refinanceBasedClass; ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyAcquisitionDate', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">
                                    <i class="fa fa-calendar text-primary"></i>
                                </div>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyAcquisitionDate"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyAcquisitionDate]"
                                   id="propertyAcquisitionDate"
                                   placeholder="MM/DD/YYYY"
                                   value="<?php echo Dates::formatDateWithRE(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyAcquisitionDate, 'YMD', 'm/d/Y'); ?>"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   onkeyup="validatePercentage(this)"
                                   class="autosavePropertyDetails form-control input-sm dateNewClass
                                   <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyAcquisitionDate', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyAcquisitionDate', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Dates::formatDateWithRE(Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyAcquisitionDate, 'YMD', 'm/d/Y'); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


    <?php } ?>

    <div class="propdetails  col-md-6 zillowValue_disp <?php echo loanForm::showField('zillowValue'); ?> propertyTypeBasedZillow">
        <div class="row form-group">
            <?php echo loanForm::label('zillowValue', 'col-md-5',
                'Please click the "Get Appraisal Value" link to get your estimated home value.<br>Pick the lowest value to be safe.'); ?>
            <div class="col-md-7">
                <?php if (Property::$allowToEdit) { ?>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               data-table="tblPropertiesDetails"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                               data-column="propertyZillowValue"
                               name="properties[<?php echo Property::$propertyIndex; ?>][zillowValue]"
                               id="zillowValue"
                               placeholder="0.00"
                               class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'zillowValue', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                               onblur="currencyConverter(this, this.value);populateDualField(this.value, 'zillowValue_mirror')"
                               tabindex="<?php echo Property::$tabIndex++; ?>"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($propertyZillowValue); ?>"
                               maxlength="12"
                               size="4"
                               autocomplete="off">
                    </div>
                <?php } else {
                    echo '<h5>' . $propertyZillowValue . '</h5>';
                } ?>
                <?php if (Property::$hideThisField) { ?>
                    <a target="_blank"
                       id="zillowValueLink"
                       href="#"
                       class="font-size-sm zillowLink"
                       onclick="propertyDetails.buildurl(this)">Get Zillow Value</a>
                <?php } ?>
            </div>
        </div>
    </div>

    <div class="propchar pricePerDoor col-md-6 pricePerDoor_disp <?php echo loanForm::showField('pricePerDoor'); ?>">
        <div class="row form-group">
            <?php echo loanForm::label('pricePerDoor', 'col-md-5'); ?>
            <div class="col-md-7">
                <?php if (Property::$allowToEdit) { ?>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               data-table="tblPropertiesDetails"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                               data-column="propertyPricePerDoor"
                               name="properties[<?php echo Property::$propertyIndex; ?>][pricePerDoor]"
                               id="pricePerDoor"
                               placeholder="0.00"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($propertyPricePerDoor); ?>"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               onblur='currencyConverter(this, this.value);'
                               class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'pricePerDoor', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'pricePerDoor', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    </div>
                <?php } else { ?>
                    <b><?php echo $propertyPricePerDoor; ?></b>
                <?php } ?>
            </div>
        </div>
    </div>
    <!-- zillow value, Get zillow end MK -->

    <?php if (Property::$activeTab != 'CI') { ?>
        <?php if (Property::$hideThisField) { ?>
            <!-- URL link 1  to property start MK -->
            <div class="propdetails col-md-6 propertyURLLink1_disp <?php echo loanForm::showField('propertyURLLink1'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('propertyURLLink1', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if (Property::$allowToEdit) { ?>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyURLLink1"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyURLLink1', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>>"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyURLLink1]"
                                   id="propertyURLLink1"
                                   placeholder="https://"
                                   value="<?php echo htmlentities($propertyURLLink1); ?>"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyURLLink1', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                            <?php
                            if (trim($propertyURLLink1) != '') {
                                echo "<a class='propertyURLLink1 mt-2' href='" . Strings::processGivenURLString($propertyURLLink1) . "' target='_blank'>Click to view the Property URL Link1</a>";
                            }
                        } else {
                            echo '<h5>' . $propertyURLLink1 . '</h5>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        <?php } ?>
        <!-- URL link 1  to property end MK -->
        <?php if (Property::$hideThisField) { ?>

            <!-- URL link 2  to property start MK -->
            <div class="propdetails  col-md-6 propertyURLLink2_disp <?php echo loanForm::showField('propertyURLLink2'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('propertyURLLink2', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if (Property::$allowToEdit) { ?>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyURLLink2"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyURLLink2', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyURLLink2]"
                                   id="propertyURLLink2"
                                   placeholder="https://"
                                   value="<?php echo htmlentities($propertyURLLink2); ?>"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyURLLink2', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                            <?php
                            if (trim($propertyURLLink2) != '') {
                                echo "<a class='propertyURLLink2 mt-2' href='" . Strings::processGivenURLString($propertyURLLink2) . "' target='_blank'>Click to view the Property URL Link2</a>";
                            }
                        } else {
                            echo '<h5>' . $propertyURLLink2 . '</h5>';
                        }
                        ?>
                    </div>
                </div>
            </div>
            <!-- URL link 2  to property end MK -->
        <?php } ?>

    <?php } ?>

    <div class="propdetails  col-md-6 presentOccupancy_disp <?php echo loanForm::showField('presentOccupancy'); ?>">
        <div class="row form-group">

            <?php echo loanForm::label('presentOccupancy', 'col-md-5'); ?>
            <div class="col-md-7">
                <?php if (Property::$allowToEdit) { ?>
                    <select class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'presentOccupancy', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            data-table="tblPropertiesDetails"
                            data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                            data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                            data-column="propertyPresentOccupancy"
                            name="properties[<?php echo Property::$propertyIndex; ?>][presentOccupancy]"
                            id="presentOccupancy"
                            tabindex="<?php echo Property::$tabIndex++; ?>"
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'presentOccupancy', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                        <option value=''> - Select -</option>
                        <?php
                        foreach ($glHMLOPresentOccupancy as $eachPresentOccupancy) {
                            echo "<option value=\"" . $eachPresentOccupancy . "\" " . Arrays::isSelected($eachPresentOccupancy, $presentOccupancy) . '>' . $eachPresentOccupancy . '</option>';
                        }
                        ?>
                    </select>
                <?php } else {
                    echo '<h5>' . $presentOccupancy . '</h5>';
                } ?>
            </div>
        </div>
    </div>

    <?php if (Property::$activeTab != 'CI') { ?>
        <div class="propdetails col-md-6 occupancyNotes_disp <?php echo loanForm::showField('occupancyNotes'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('occupancyNotes', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <textarea
                                data-table="tblPropertiesDetails"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                data-column="propertyOccupancyNotes"
                                class="autosavePropertyDetails form-control input-sm validateMaxLength <?php echo BaseHTML::fieldAccess(['fNm' => 'occupancyNotes', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                name="properties[<?php echo Property::$propertyIndex; ?>][occupancyNotes]"
                                id="occupancyNotes"
                                TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                maxlength="<?php echo loanForm::getFieldLength('propertyOccupancyNotes','tblPropertiesDetails'); ?>"
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'occupancyNotes', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>><?php echo $propertyOccupancyNotes ?></textarea>
                    <?php } else {
                        echo '<h5>' . $propertyOccupancyNotes . '</h5>';
                    } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 propertyCondition_disp <?php echo loanForm::showField('propertyCondition'); ?>">
            <div class="row form-group">

                <?php echo loanForm::label('propertyCondition', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select
                                data-table="tblPropertiesDetails"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                data-column="propertyCondition"
                                class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyCondition', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                name="properties[<?php echo Property::$propertyIndex; ?>][propertyCondition]"
                                id="propertyCondition"
                                TABINDEX="<?php echo Property::$tabIndex++; ?>">
                            <option value=""> - Select -</option>
                            <?php
                            foreach ($glPropertyCondition as $eachPropertyCondition) {
                                echo "<option value=\"" . trim($eachPropertyCondition) . "\" " . Arrays::isSelected($eachPropertyCondition, $propertyCondition) . '>' . trim($eachPropertyCondition) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <b><?php echo $propertyCondition; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 conditionNotes_disp <?php echo loanForm::showField('conditionNotes'); ?>">
            <div class="row form-group">

                <?php echo loanForm::label('conditionNotes', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <textarea
                                data-table="tblPropertiesDetails"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                data-column="propertyConditionNotes"
                                class="autosavePropertyDetails form-control input-sm validateMaxLength <?php echo BaseHTML::fieldAccess(['fNm' => 'conditionNotes', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                name="properties[<?php echo Property::$propertyIndex; ?>][conditionNotes]"
                                id="conditionNotes"
                                TABINDEX="<?php echo Property::$tabIndex++; ?>"
                    <?php echo BaseHTML::fieldAccess(['fNm' => 'conditionNotes', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>><?php echo $propertyConditionNotes; ?></textarea>
                    <?php } else {
                        echo '<h5>' . $propertyConditionNotes . '</h5>';
                    } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 yearRenovated_disp <?php echo loanForm::showField('yearRenovated'); ?>">
            <div class="row form-group">

                <?php echo loanForm::label('yearRenovated', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesDetails"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                               data-column="propertyRenovatedYear"
                               name="properties[<?php echo Property::$propertyIndex; ?>][yearRenovated]"
                               id="yearRenovated"
                               value="<?php echo $propertyYearRenovated; ?>"
                               size="6"
                               maxlength="4"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               class="autosavePropertyDetails  form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'yearRenovated', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'yearRenovated', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?>
                        <b><?php echo $propertyYearRenovated; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 occupancy_disp <?php echo loanForm::showField('occupancy'); ?>">
            <div class="row form-group">

                <?php echo loanForm::label('occupancy', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <select name="properties[<?php echo Property::$propertyIndex; ?>][occupancy]"
                                data-table="tblPropertiesDetails"
                                data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                data-column="propertyPresentOccupancy"
                                id="occupancy"
                                onchange="principalResidenceServicerInfo();principalResidenceInfo(this.value);"
                                class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'occupancy', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                tabindex="<?php echo Property::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'occupancy', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>>
                            <option value=''> - Select -</option>
                            <?php
                            foreach ($glOccupancyArray as $eachOccupancy) {
                                echo "<option value=\"" . $eachOccupancy . "\" " . Arrays::isSelected($eachOccupancy, $propertyOccupancy) . '>' . $eachOccupancy . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else {
                        echo '<h5>' . $propertyOccupancy . '</h5>';
                    } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 taxYear_disp <?php echo loanForm::showField('taxYear'); ?>">
            <div class="row form-group">

                <?php echo loanForm::label('taxYear', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <input type="number"
                               data-table="tblPropertiesDetails"
                               data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                               data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                               data-column="propertyTaxYear"
                               name="properties[<?php echo Property::$propertyIndex; ?>][taxYear]"
                               id="taxYear"
                               value="<?php echo $propertyTaxYear; ?>"
                               size="6"
                               maxlength="7"
                               TABINDEX="<?php echo Property::$tabIndex++; ?>"
                               autocomplete="off"
                               class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'taxYear', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'taxYear', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?>
                        <b><?php echo $propertyTaxYear; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>


        <div class="propdetails col-md-6 annualPropertyTaxes_disp <?php echo loanForm::showField('annualPropertyTaxes'); ?>">
            <div class="row form-group">

                <?php echo loanForm::label('annualPropertyTaxes', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="annualPropertyTaxes"
                                   placeholder="0.00"
                                   class="autosavePropertyDetails form-control input-sm annualPropertyTaxesEle <?php echo BaseHTML::fieldAccess(['fNm' => 'annualPropertyTaxes',
                                       'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][annualPropertyTaxes]"
                                   id="annualPropertyTaxes"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($annualPropertyTaxes) ?>"
                                   size="13"
                                   maxlength="11"
                                   TABINDEX="<?php echo Property::$tabIndex++; ?>"
                                   onblur="currencyConverter(this, this.value);propertyDetails.updateAnnualPropertyTaxMirrorField(this);"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'annualPropertyTaxes', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                        <?php
                    } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($annualPropertyTaxes) ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>


        <?php loanForm::pushSectionID('PD'); ?>
        <div class="propdetails col-md-6 propertyTaxDueDate_disp
                <?php echo loanForm::showField('propertyTaxDueDate'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propertyTaxDueDate', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend ">
                            <span class="input-group-text">
                                <i class="fa fa-calendar text-primary"></i>
                            </span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyTaxDueDate"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyTaxDueDate]"
                                   id="propertyTaxDueDate"
                                   value="<?php echo $propertyTaxDueDate; ?>"
                                   autocomplete="off"
                                   maxlength="10"
                                   size="12"
                                   placeholder="MM/DD/YYYY"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"
                                   class="autosavePropertyDetails dateNewClass form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyTaxDueDate', 'sArr' => Property::$secArr, 'opt' => 'M']); ?> "/>
                        </div>
                    <?php } else {
                        echo '<h5>' . $propertyTaxDueDate . '</h5>';
                    } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 pastDuePropertyTaxes_disp
                <?php echo loanForm::showField('pastDuePropertyTaxes'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('pastDuePropertyTaxes', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="pastDuePropertyTaxes"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][pastDuePropertyTaxes]"
                                   id="pastDuePropertyTaxes"
                                   placeholder="0.00"
                                   value="<?php echo $propertyPastDuePropertyTaxes; ?>"
                                   size="6"
                                   maxlength="15"
                                   placeholder="0.00"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   onblur="currencyConverter(this, this.value);"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'pastDuePropertyTaxes', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'pastDuePropertyTaxes', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <b><?php echo $propertyPastDuePropertyTaxes; ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 propertyPayoffAmt_disp
                <?php echo loanForm::showField('propertyPayoffAmt'); ?>">
            <div class="row form-group">

                <?php echo loanForm::label('propertyPayoffAmt', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyPayoffAmt"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propertyPayoffAmt]"
                                   id="propertyPayoffAmt"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($propertyPayoffAmt); ?>"
                                   size="6"
                                   maxlength="15"
                                   placeholder="0.00"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   onblur="currencyConverter(this, this.value);"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyPayoffAmt', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyPayoffAmt', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal($propertyPayoffAmt); ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <?php if (!Property::$publicUser) { ?>
            <div class="propdetails col-md-6 allocatedLoanAmount_disp
                <?php echo loanForm::showField('allocatedLoanAmount'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('allocatedLoanAmount', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if (Property::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text"
                                       data-table="tblPropertiesDetails"
                                       data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                       data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                       data-column="propertyAllocatedLoanAmount"
                                       name="properties[<?php echo Property::$propertyIndex; ?>][allocatedLoanAmount]"
                                       id="allocatedLoanAmount"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($propertyAllocatedLoanAmount) ?>"
                                       size="6"
                                    <?php echo ($_SESSION['userGroup'] == glUserRole::CLIENT) ? 'readonly' : ''; ?>
                                       maxlength="15"
                                       placeholder="0.00"
                                       tabindex="<?php echo Property::$tabIndex++; ?>"
                                       autocomplete="off"
                                       onblur="currencyConverter(this, this.value); propertyDetails.calculatePropertyLTV(this);propertyDetails.calculateLoanToPurchasePrice(this);propertyDetails.calculateLoanToAfterRepairValue(this);propertyDetails.updateAllocatedLoanAmountMirrorField(this);"
                                       class="autosavePropertyDetails form-control input-sm propertyDetailsAllocatedLoanAmountEle <?php echo BaseHTML::fieldAccess(['fNm' => 'allocatedLoanAmount', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'allocatedLoanAmount', 'sArr' => Property::$secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <b><?php echo Currency::formatDollarAmountWithDecimal($propertyAllocatedLoanAmount) ?></b>
                        <?php } ?>
                    </div>
                </div>
            </div>
        <?php } ?>

        <?php if (Property::$hideThisField) { ?>
            <div class="propdetails col-md-6 HMLOmlsnumber_disp
                <?php echo loanForm::showField('HMLOmlsnumber'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('HMLOmlsnumber', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <?php if (Property::$allowToEdit) { ?>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyMLSNumber"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOmlsnumber', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][HMLOmlsnumber]"
                                   id="HMLOmlsnumber"
                                   value="<?php echo htmlspecialchars($propertyHMLOmlsnumber); ?>"
                                   maxlength="15"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOmlsnumber', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        <?php } else { ?>
                            <h5><?php echo $propertyHMLOmlsnumber; ?></h5>
                        <?php } ?>
                    </div>
                </div>
            </div>
        <?php } ?>

        <div class="propdetails col-md-6 propAnnualInsurancePremiums_disp
                <?php echo loanForm::showField('propAnnualInsurancePremiums'); ?>">
            <div class="row form-group">
                <?php echo loanForm::label('propAnnualInsurancePremiums', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if (Property::$allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyAnnualInsurancePremiums"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][propAnnualInsurancePremiums]"
                                   id="propAnnualInsurancePremiums"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($propertyPropAnnualInsurancePremiums); ?>"
                                   size="13"
                                   maxlength="13"
                                   onblur="currencyConverter(this, this.value);"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propAnnualInsurancePremiums', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'propAnnualInsurancePremiums', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        </div>
                    <?php } else { ?>
                        <b><?php echo Currency::formatDollarAmountWithDecimal($propertyPropAnnualInsurancePremiums) ?></b>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="propdetails col-md-6 rehabToComplete_disp
                <?php echo loanForm::showField('rehabToComplete'); ?>">
            <div class="row form-group">

                <?php echo loanForm::label('rehabToComplete', 'col-md-5'); ?>
                <div class="col-md-7">
                    <div class="input-group">
                        <?php if (Property::$allowToEdit) { ?>
                            <input type="number"
                                   data-table="tblPropertiesDetails"
                                   data-propertyId="<?php echo Property::$propertyInfo->propertyId; ?>"
                                   data-id="<?php echo Property::$propertyInfo->getTblPropertiesDetails_by_propertyId()->id; ?>"
                                   data-column="propertyRehabToComplete"
                                   class="autosavePropertyDetails form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabToComplete', 'sArr' => Property::$secArr, 'opt' => 'M']); ?>"
                                   name="properties[<?php echo Property::$propertyIndex; ?>][rehabToComplete]"
                                   id="rehabToComplete"
                                   onkeyup='return onlyNumber(this,event)'
                                   value="<?php echo $propertyRehabToComplete; ?>"
                                   maxlength="5"
                                   tabindex="<?php echo Property::$tabIndex++; ?>"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabToComplete', 'sArr' => Property::$secArr, 'opt' => 'I']); ?> />
                        <?php } else { ?>
                            <h5><?php echo $propertyRehabToComplete; ?></h5>
                        <?php } ?>
                        <div class="input-group-prepend">
                            <span class="input-group-text">In Months</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php } ?>
</div>
