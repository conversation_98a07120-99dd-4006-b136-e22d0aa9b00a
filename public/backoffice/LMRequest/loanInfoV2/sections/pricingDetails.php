<?php

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glRateLockPeriod;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanPropertySummary;
use models\Controllers\loanForm;
use models\lendingwise\tblLMRHUDItemsPayableLoan;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$glRateLockPeriod = glRateLockPeriod::$glRateLockPeriod;
$IMPDATESArray = BaseHTML::sectionAccess(['sId' => 'IMPDATES', 'opt' => LMRequest::$activeTab]);
$LSTArray = BaseHTML::sectionAccess(['sId' => 'LST', 'opt' => LMRequest::$activeTab]);

$cv3OriginationPoint = LMRequest::calOriginationPoints();
?>
<div class="card card-custom  ">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Pricing Details
            </h3>
        </div>
        <div class="card-toolbar">
            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                  data-card-tool="toggle"
                  data-section="pricingDetailsCard"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body pricingDetailsCard-body ">
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label class="font-weight-bold">
                        <?php
                        loanForm::pushSectionID('LST');
                        echo loanForm::getFieldLabel('lien1Rate');
                        ?>
                    </label>
                    <div class="">
                        <?php
                        if (LMRequest::$allowToEdit) {
                            /*loanInfoV2Form.calculateInterestOnlyMonthlyPayment();*/
                            ?>
                            <div class="input-group">
                                <input class="form-control input-sm "
                                       type="text"
                                       placeholder="0.0"
                                       name="lien1Rate"
                                       id="lien1Rate"
                                       onblur="loanInfoV2Form.updatedInterestRate();"
                                       value="<?php echo number_format(Strings::replaceCommaValues(LMRequest::myFileInfo()->LMRInfo()->lien1Rate), 3); ?>"
                                       autocomplete="off">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        <?php } else {
                            echo '<b>' . number_format(Strings::replaceCommaValues(LMRequest::myFileInfo()->LMRInfo()->lien1Rate), 3) . ' %</b>';
                        } ?>
                    </div>
                    <?php
                    loanForm::popSectionID();
                    ?>
                </div>
            </div>


            <?php loanForm::pushSectionID('IMPDATES'); ?>
            <div class="col-md-3 rateLockFields <?php echo loanForm::showField('rateLockDate'); ?>">
                <div class=" form-group ">
                    <?php
                    echo loanForm::label(
                        'rateLockDate',
                        '',
                        '',
                        loanForm::changeLog(
                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                            'rateLockDate',
                            \models\lendingwise\tblFileHMLONewLoanInfo::class,
                            'Rate Lock Date'
                        ),
                    );
                    ?>
                    <div class="">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend rateLockDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                </div>
                                <input type="text"
                                       name="rateLockDate"
                                       id="rateLockDate"
                                       value="<?php echo Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockDate, 'YMD', 'm/d/Y'); ?>"
                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockDate', 'sArr' => $IMPDATESArray, 'opt' => 'M']); ?> dateNewClass"
                                       autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockDate', 'sArr' => $IMPDATESArray, 'opt' => 'I']); ?>
                                       placeholder="MM/DD/YYYY">
                            </div>
                        <?php } else {
                            echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockDate, 'YMD', 'm/d/Y') . '</b>';
                        } ?>
                    </div>
                </div>
            </div>


            <div class=" col-md-3 rateLockFields <?php echo loanForm::showField('rateLockExpirationDate'); ?>">
                <div class=" form-group">
                    <?php
                    $rateLockDateLabel = loanForm::$formFields['BORF']['rateLockDate']->fieldLabel;
                    $rateLockPeriodLabel = loanForm::$formFields['BORF']['rateLockPeriod']->fieldLabel;

                    $rateLockExpirationDateToolTip = $rateLockDateLabel . ' + ' . $rateLockPeriodLabel;
                    if (glCustomJobForProcessingCompany::isPC_CV3(LMRequest::File()->FPCID)) {
                        $rateLockExpirationDateToolTip .= ' + 30';
                    }
                    echo loanForm::label(
                        'rateLockExpirationDate',
                        'col-md-12 ',
                        $rateLockExpirationDateToolTip,
                        loanForm::changeLog(
                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                            'rateLockExpirationDate',
                            \models\lendingwise\tblFileHMLONewLoanInfo::class,
                            'Rate Lock Expiration Date'
                        ),
                    );
                    ?>
                    <div class="col-md-12">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend rateLockExpirationDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                </div>
                                <input
                                        type="text"
                                        name="rateLockExpirationDate"
                                        id="rateLockExpirationDate"
                                        readonly
                                        value="<?php echo Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExpirationDate, 'YMD', 'm/d/Y'); ?>"
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockExpirationDate', 'sArr' => $IMPDATESArray, 'opt' => 'M']); ?>"
                                        autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockExpirationDate', 'sArr' => $IMPDATESArray, 'opt' => 'I']); ?>
                                        placeholder="MM/DD/YYYY">
                            </div>
                        <?php } else {
                            echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExpirationDate, 'YMD', 'm/d/Y') . '</b>';
                        } ?>
                    </div>
                </div>
            </div>

            <input type="hidden"
                   class="form-control col-md-3"
                   id="rateLockExtension"
                   value="<?php echo LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExtension; ?>">
            <?php loanForm::popSectionID(); ?>

            <?php loanForm::pushSectionID('LST'); ?>
            <div class="col-md-3  <?php echo loanForm::showField('rateLockPeriod'); ?>">
                <div class="row form-group">
                    <?php
                    echo loanForm::label(
                        'rateLockPeriod',
                        'col-md-12 ',
                        '',
                        loanForm::changeLog(
                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                            'rateLockPeriod',
                            \models\lendingwise\tblFileHMLONewLoanInfo::class,
                            'Rate Lock Period'
                        ),
                    );
                    ?>
                    <div class="col-md-12">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <select name="rateLockPeriod"
                                    id="rateLockPeriod"
                                    data-placeholder="Please Select <?php echo loanForm::getFieldLabel('rateLockPeriod'); ?>"
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockPeriod', 'sArr' => $LSTArray, 'opt' => 'M']); ?> chzn-select"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockPeriod', 'sArr' => $LSTArray, 'opt' => 'I']); ?>
                                    onchange="loanTerms.hideOrShowRateLockFields(this.value);">
                                <option value=""></option>
                                <?php foreach ($glRateLockPeriod as $eachRateLockPeriod) { ?>
                                    <option value="<?php echo $eachRateLockPeriod; ?>"
                                        <?php echo Arrays::isSelected($eachRateLockPeriod, LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockPeriod); ?>>
                                        <?php echo trim($eachRateLockPeriod); ?>
                                    </option>
                                <?php } ?>
                            </select>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>
        </div>

        <?php
        //   pr(LMRequest::myFileInfo()->getFileHUDBasicInfo()->interestPaymentHoldBackMonth);
        //  pr(LMRequest::myFileInfo()->getFileHUDBasicInfo()->interestPaymentHoldBackAmount);
        ?>
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label class="font-weight-bold"> Total Loan Interest Payment
                        <i class="fa fa-info-circle text-primary popoverClass"
                           data-html="true"
                           id="totalLoanInterestPaymentTooltip"
                           title="Total Loan Interest Payment"
                           data-formula="<?php echo loanPropertySummary::$totalLoanInterestPaymentTooltip; ?>"
                           data-content="<?php echo loanPropertySummary::$totalLoanInterestPaymentTooltipWithValues ? (
                               loanPropertySummary::$totalLoanInterestPaymentTooltip . '<hr>' . loanPropertySummary::$totalLoanInterestPaymentTooltipWithValues) : loanPropertySummary::$totalLoanInterestPaymentTooltip ?>"></i>
                    </label>
                    <div class="">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control input-sm "
                                       type="text"
                                       placeholder="0.0"
                                       name="totalLoanInterestPayment"
                                       id="totalLoanInterestPayment"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment); ?>"
                                       readonly
                                       autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b> $ ' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment) . ' </b>';
                        } ?>
                    </div>
                </div>

                <div class="form-group d-none">
                    <label class="font-weight-bold"> Interest-Only Monthly Payment
                        <i class="fa fa-info-circle text-primary hide"
                           data-toggle="tooltip"
                           data-trigger="hover"
                           data-html="true" title="(Total Loan Amount) * Interest Rate / 1200"></i>
                    </label>
                    <div class="">
                        <?php
                        //LMRequest::myFileInfo()->getLoanPropertySummary()->interestOnlyMonthlyPayment
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control input-sm "
                                       type="text"
                                       placeholder="0.0"
                                       name="interestOnlyMonthlyPayment"
                                       id="interestOnlyMonthlyPayment"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHUDBasicInfo()->interestPaymentHoldBackAmount); ?>"
                                       readonly
                                       disabled
                                       autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b> $ ' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHUDBasicInfo()->interestPaymentHoldBackAmount) . ' </b>';
                        } ?>
                    </div>
                    <?php
                    loanForm::popSectionID();
                    ?>
                </div>

            </div>

            <div class="col-md-5">
                <div class="form-group">
                    <label class="font-weight-bold"> Interest Payment Holdback </label>
                    <div class="">
                        <?php
                        $tblLMRHUDItemsPayableLoan_808 = tblLMRHUDItemsPayableLoan::Get([
                            'LMRID'   => LMRequest::myFileInfo()->LMRId,
                            'fieldID' => '808',
                        ]);
                        if (LMRequest::$allowToEdit) {
                            //LMRequest::myFileInfo()->getLoanPropertySummary()->interestPaymentHoldBackMonths
                            ?>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input-group">
                                        <div class="input-group-prepend tooltipClass" title="Months">
                                            <span class="input-group-text font-weight-boldest">M</span>
                                        </div>
                                        <input class="form-control input-sm "
                                               type="number"
                                               placeholder="0"
                                               min="0"
                                               name="interestPaymentHoldBackMonths"
                                               id="interestPaymentHoldBackMonths"
                                               onblur="/*loanInfoV2Form.interestPaymentHoldBack();*/"
                                               readonly
                                               value="<?php echo LMRequest::myFileInfo()->getFileHUDBasicInfo()->interestPaymentHoldBackMonth; ?>"
                                               autocomplete="off">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input class="form-control input-sm "
                                               type="text"
                                               placeholder="0.0"
                                               name="interestPaymentHoldBack"
                                               id="interestPaymentHoldBack"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal($tblLMRHUDItemsPayableLoan_808->borrowerSettlementValue); ?>"
                                               readonly
                                               autocomplete="off">
                                        <div class="input-group-append">
                                           <span class="input-group-text">
                                               <i class="fa fa-info-circle text-primary"
                                                  data-toggle="tooltip"
                                                  data-trigger="hover"
                                                  data-html="true"
                                                  title="Interest-Only Monthly Payment  * Interest Payment Holdback Months "></i>
                                           </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } else {
                            ?>
                            <b>Months: <?php echo LMRequest::myFileInfo()->getFileHUDBasicInfo()->interestPaymentHoldBackMonth; ?></b>
                            <b class="ml-10">$ <?php echo Currency::formatDollarAmountWithDecimal($tblLMRHUDItemsPayableLoan_808->borrowerSettlementValue); ?></b>
                        <?php } ?>
                    </div>
                </div>
            </div>


            <div class="col-md-4 d-none">
                <div class="form-group">
                    <label class="font-weight-bold">Taxes and Insurance Holdback</label>
                    <div class="">
                        <?php
                        $tblLMRHUDItemsPayableLoan_809 = tblLMRHUDItemsPayableLoan::Get([
                            'LMRId'   => LMRequest::$LMRId,
                            'fieldID' => 809,
                        ]);
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control input-sm "
                                       type="text"
                                       placeholder="0"
                                       id="HUD_BS_809"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($tblLMRHUDItemsPayableLoan_809->borrowerSettlementValue); ?>"
                                       readonly
                                       autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($tblLMRHUDItemsPayableLoan_809->borrowerSettlementValue) . ' </b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label class="font-weight-bold">Holdback</label>
                    <div class="">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control input-sm "
                                       type="text"
                                       placeholder="0"
                                       name="holdbackVal"
                                       id="holdbackVal"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(loanPropertySummary::$holdbackVal); ?>"
                                       readonly
                                       autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(loanPropertySummary::$holdbackVal) . ' </b>';
                        } ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="row form-group">
                    <label class="col-md-3 ">
                        Origination Points @
                    </label>
                    <div class="col-md-3">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="originationPointsRate"
                                        id="originationPointsRate"
                                        value="<?php echo LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->originationPointsRate; ?>"
                                        onblur="loanInfoV2Form.setOriginationValue();"
                                        autocomplete="off">
                                <div class="input-group-append">
                                    <span class="input-group-text">Points</span>
                                </div>
                            </div>
                        <?php } else {
                            echo '<b>' . LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->originationPointsRate . '</b>';
                        } ?>
                    </div>
                    <div class="col-md-5">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control input-sm "
                                       type="text"
                                       placeholder="0.0"
                                       name="originationPointsValue"
                                       id="originationPointsValue"
                                       onblur="currencyConverter(this, this.value);loanInfoV2Form.setOriginationPoints(); "
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->originationPointsValue); ?>"
                                       autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b> $' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->originationPointsValue) . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row form-group">
                    <label class="col-md-3 ">
                        Broker Points @
                    </label>
                    <div class="col-md-3">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="brokerPointsRate"
                                        id="brokerPointsRate"
                                        onblur="loanInfoV2Form.setBrokerPointsValue();"
                                        value="<?php echo LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerPointsRate; ?>"
                                        autocomplete="off">
                                <div class="input-group-append">
                                    <span class="input-group-text">Points</span>
                                </div>
                            </div>
                        <?php } else {
                            echo '<b>' . LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerPointsRate . '</b>';
                        } ?>
                    </div>
                    <div class="col-md-5">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="brokerPointsValue"
                                        id="brokerPointsValue"
                                        onblur="currencyConverter(this, this.value);loanInfoV2Form.setBrokerPointsRate();"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerPointsValue); ?>"
                                        autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerPointsValue) . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row form-group">
                    <label class="col-md-3 control-label">
                        Cv3 Origination Points @
                    </label>
                    <div class="col-md-3">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="cv3OriginationPoint"
                                        id="cv3OriginationPoint"
                                        value="<?php echo $cv3OriginationPoint; ?>"
                                        readonly
                                        autocomplete="off">
                                <div class="input-group-append">
                                    <span class="input-group-text">Points</span>
                                </div>
                            </div>
                        <?php } else {
                            echo '<b>' . $cv3OriginationPoint . '</b>';
                        } ?>
                    </div>
                    <div class="col-md-5">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="cv3OriginationAmount"
                                        id="cv3OriginationAmount"
                                        readonly
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3OriginationAmount); ?>"
                                        autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3OriginationAmount) . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row form-group">
                    <label class="col-md-3 ">
                        Broker Processing Fee
                    </label>
                    <div class="col-md-5">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-append">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="brokerProcessingFee"
                                        id="brokerProcessingFee"
                                        onblur="currencyConverter(this, this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerProcessingFee) ?>"
                                        autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerProcessingFee) . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row form-group">
                    <label class="col-md-3 control-label">
                        Referral Points / Broker Yield Spread Premium
                    </label>
                    <div class="col-md-3">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="cv3ReferralPoint"
                                        id="cv3ReferralPoint"
                                        value="<?php echo LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3ReferralPoint; ?>"
                                        onblur="loanInfoV2Form.cv3OriginationPoints();"
                                        autocomplete="off">
                                <div class="input-group-append">
                                    <span class="input-group-text">Points</span>
                                </div>
                            </div>
                        <?php } else {
                            echo '<b>' . LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3ReferralPoint . '</b>';
                        } ?>
                    </div>
                    <div class="col-md-5">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="cv3ReferralAmount"
                                        id="cv3ReferralAmount"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3ReferralAmount); ?>"
                                        onblur="loanInfoV2Form.cv3OriginationAmount();"
                                        autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3ReferralAmount) . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row form-group">
                    <label class="col-md-3 ">
                        Appraisal Fee
                    </label>
                    <div class="col-md-5">
                        <?php
                        if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-append">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input
                                        class="form-control input-sm "
                                        type="text"
                                        placeholder="0.0"
                                        name="appraisalFee"
                                        id="appraisalFee"
                                        onblur="currencyConverter(this, this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->appraisalFee); ?>"
                                        autocomplete="off">
                            </div>
                        <?php } else {
                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->appraisalFee) . '</b>';
                        } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
