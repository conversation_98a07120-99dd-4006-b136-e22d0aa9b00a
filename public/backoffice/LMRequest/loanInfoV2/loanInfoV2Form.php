<?php

use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanPropertySummary;
use models\Controllers\LMRequest\Property;
use models\Controllers\LMRequest\PropertyAnalysis;
use models\Controllers\loanForm;
use models\standard\BaseHTML;
use models\standard\Currency;

loanPropertySummary::init(LMRequest::$LMRId);

require 'sections/loanProgramPurpose.php';
require 'sections/creditInfo.php';
require 'sections/pricingDetails.php';

//loanInfoV2::getPropertiesAggregateValues(Property::$propertiesInfo);

//$propertyAnalysis->propertyMetrics

?>
<input type="hidden"
       title=""
       id="branchId"
       class="form-control tooltipClass"
       value="<?php echo LMRequest::myFileInfo()->tblFile()->FBRID; ?>"/>
<input type="hidden"
       title=""
       id="totalAsIsValue"
       class="form-control tooltipClass"
       value="<?php echo LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesAsIsValue; ?>"/>

<input type="hidden"
       id="totalAllocatedLoanAmount"
       class="form-control tooltipClass"
       title="Total Allocated Loan Amount"
       value="<?php echo(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesAllocatedLoanAmount); ?>">

<input type="hidden"
       id="propertyMetrics"
       class="form-control tooltipClass"
       title="Total Allocated Loan Amount"
       value="<?php echo (Property::$primaryPropertyInfo->getTblPropertiesAnalysis_by_propertyId()->propertyMetrics); ?>">

<input type="hidden"
       id="propertyAppraisalAsIsValue"
       class="form-control tooltipClass"
       title="Property Appraised As-is Value"
       value="<?php echo (Property::$primaryPropertyInfo->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalAsIsValue); ?>">

<input type="hidden"
       id="estimatedPropertyValue"
       class="form-control tooltipClass"
       title="Property Appraised As-is Value"
       value="<?php echo (Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyEstimatedValue); ?>">

<input type="hidden"
       class="form-control"
       id="propertyPurchasePrice"
       value="<?php echo Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPurchasePrice; ?>">

<input type="hidden"
       name="totalQualifyingLoanAmount"
       id="totalQualifyingLoanAmount"
       class="tooltipClass"
       title="Total Qualifying Loan Amount"
       value="<?php echo LMRequest::myFileInfo()->getLoanPropertySummary()->totalQualifyingLoanAmount; ?>"/>

<input type="hidden"
       data-calc="<?php echo LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount . '/' . LMRequest::myFileInfo()->getLoanPropertySummary()->totalQualifyingLoanAmount . '* 100'; ?>"
       name="bridgeLoanToValue"
       id="bridgeLoanToValue"
       class="tooltipClass"
       title="Loan To Value"
       value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->bridgeLoanToValue); ?>">

<input type="hidden"
       id="renovationQualifyingAmount"
       class="tooltipClass"
       title="Renovation Qualifying Amount"
       value="<?php echo PropertyAnalysis::calculateQualifyingLoanAmount(Property::$primaryPropertyInfo); ?>">

<input type="hidden"
       class="form-control"
       id="propertyAllocatedLoanAmount"
       value="<?php echo Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyAllocatedLoanAmount; ?>">

<input type="hidden"
       class="form-control"
       id="totalPropertiesPITIA"
       value="<?php echo LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesPITIA; ?>">

<input type="hidden"
       class="form-control"
       id="midFicoScore_LIV2"
       value="<?php echo LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore; ?>">

<input type="hidden"
       id="customTotalLoanAmountLogic"
       value="<?php echo loanPropertySummary::getCv3CustomTotalLoanAmountLogic(); ?>">

<?php
loanForm::popSectionID();
loanForm::pushSectionID('LT');

require 'sections/loanDetailsBridge.php';
require 'sections/loanDetailsBridgeRenovation.php';
require 'sections/loanDetailsBridgeRental.php';
require 'sections/loanDetailsGroundUpConstruction.php';
require 'sections/assetsSizer.php';
?>
<?php if (LMRequest::$allowToEdit) { ?>
    <div class="row my-4">
        <div class="col-md-12 text-center">
            <input type="submit" id="saveBtn"
                   class="btn btn-primary btnSave"
                   name="btnSave"
                   value="Save"
                   onclick="return this.disabled===false;">
            <input type="submit"
                   class="btn btn-primary btnSave"
                   name="btnSave"
                   value="Save & Next"
                   onclick="return this.disabled===false;">
        </div>
    </div>
    <?php
}
?>
<script src="/backoffice/LMRequest/loanInfoV2/js/loanInfoV2Form.js?<?php echo CONST_JS_VERSION; ?>"></script>
