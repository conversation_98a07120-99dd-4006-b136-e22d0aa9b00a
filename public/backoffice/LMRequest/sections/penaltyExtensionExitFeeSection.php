<?php
global $fileTab;

use models\composite\oHMLOInfo\fileExtensionOptions;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOPrePaymentPenalty;
use models\constants\gl\glPCID;
use models\constants\gl\glprePaymentPenalty;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\loanForm;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFileExtensionOptions;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;


$EEPSecArr = BaseHTML::sectionAccess(['sId' => 'EEP', 'opt' => $fileTab]); /* Field Access.. */

$glHMLOPrePaymentPenalty = glHMLOPrePaymentPenalty::$glHMLOPrePaymentPenalty;
$glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;

$extensionFields = fileExtensionOptions::$fileExtensionOptionsData ?? [];
if (!sizeof($extensionFields)) {
    $extensionFields = [
        new tblFileExtensionOptions(),
    ];
}
$prePaymentPenaltyResArr = glprePaymentPenalty::getPCLevelPrePaymentPenalty(LMRequest::$PCID, 'FC');
?>


<div class="card card-custom penaltyExtensionExitFeeCard EEP" id="penaltyExtensionExitFeeCard">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label"><?php echo BaseHTML::getSectionHeading('EEP'); ?> </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('EEP')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('EEP'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                    data-card-tool="toggle"
                    data-section="penaltyExtensionExitFeeCard"
                    data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body penaltyExtensionExitFeeCard_body">
        <div class="row">
            <?php loanForm::pushSectionID('EEP'); ?>
            <div class="col-md-6">
                <label class="col-md-12 bg-secondary mb-2 p-2 font-weight-bolder" id="loanExtensionFeesSubHeading">Extension
                    Fees</label>
                <div class="row">
                    <?php if (glCustomJobForProcessingCompany::accessToExtensionOptions(LMRequest::$PCID, PageVariables::$userNumber)) { ?>
                        <?php
                        $extFieldNo = 1;
                        foreach ($extensionFields as $extField) {
                            $class = $extFieldNo == 1 ? 'hide' : 'show';
                            $calculatePercentageExtensionOption = HMLOLoanTermsCalculation::calculatePercentageExtensionOption(HMLOLoanTermsCalculation::$totalLoanAmount, $extField->extensionOptionPercentage);
                            ?>
                            <div class="col-md-12 form-group extensionOptionPercentage_disp loanExitExtensionFeesFieldsCloneDiv"
                                 id="loanExitExtensionFeesFieldsCloneDiv_<?php echo $extFieldNo; ?>">
                                <input type="hidden"
                                       name="extensionOptionFields[<?php echo $extFieldNo; ?>][extensionOptionId]"
                                       id="extensionOptionId_<?php echo $extFieldNo; ?>"
                                       value="<?php echo $extField->id; ?>">
                                <div class="row">
                                    <?php echo loanForm::label(
                                        'extensionOptionPercentage',
                                        'col-md-3  ',
                                        '',
                                        $extFieldNo,
                                        $extFieldNo
                                    ); ?>
                                    <div class="col-md-8 ml-12">
                                        <div class="row">
                                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                                <input type="text"
                                                       placeholder="0.0"
                                                       name="extensionOptionFields[<?php echo $extFieldNo; ?>][extensionOptionPercentage]"
                                                       class="form-control col-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                                       id="extensionOptionPercentage_<?php echo $extFieldNo; ?>"
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $EEPSecArr, 'opt' => 'I']); ?>
                                                       value="<?php echo number_format($extField->extensionOptionPercentage, 3); ?>"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                       onchange="calculatePercentageExtensionOption('loanModForm', this);">
                                                <span class="mt-2">&nbsp;%&nbsp;Fee for&nbsp;</span>
                                                <select name="extensionOptionFields[<?php echo $extFieldNo; ?>][extensionOption]"
                                                        id="extensionOption_<?php echo $extFieldNo; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $EEPSecArr, 'opt' => 'I']); ?>
                                                        class="form-control col-4 <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                                        tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                                                    <option value=""> - Select -</option>
                                                    <?php
                                                    foreach ($glHMLOExtensionOption as $extKey => $extValue) {
                                                        $sOpt = '';
                                                        $sOpt = Arrays::isSelected($extKey, $extField->extensionOption);
                                                        echo "<option value=\"" . $extKey . "\" " . $sOpt . '>' . trim($extValue) . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                                <?php if (!in_array(LMRequest::$PCID, [glPCID::PCID_CRB])) { ?>
                                                    <span class="mt-2">&nbsp;@rate&nbsp;</span>
                                                    <input type="text"
                                                           name="extensionOptionFields[<?php echo $extFieldNo; ?>][extensionRatePercentage]"
                                                           id="extensionRatePercentage_<?php echo $extFieldNo; ?>"
                                                           placeholder="0.0"
                                                           class="form-control col-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                                           value="<?php echo Currency::formatRateAmountWithDecimal($extField->extensionRatePercentage); ?>"
                                                           tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                                                    <span class="mt-2">&nbsp;%&nbsp;&nbsp;&nbsp;</span>
                                                <?php } ?>
                                            <?php } else { ?>
                                                <label><?php echo $glHMLOExtensionOption[$extField->extensionOption]; ?></label>
                                            <?php } ?>
                                            <?php if (!in_array(LMRequest::$PCID, [glPCID::PCID_CRB, glPCID::PCID_DEV_DAVE])) { ?>
                                                <span class="mt-2">=&nbsp;</span>
                                                <span class="mt-2">
                                                $<span id="extensionOptionsAmt_<?php echo $extFieldNo; ?>" class="H5">
                                                    <?php echo Currency::formatDollarAmountWithDecimal($calculatePercentageExtensionOption); ?>
                                                </span>
                                            </span>
                                            <?php } ?>
                                            <div>
                                            <span class="btn btn-xs btn-success btn-icon ml-2 mt-2 cursor-pointer"
                                                  data-toggle="popover"
                                                  data-content="Click to add Extension Option (Max 5)"
                                                  onclick="feesAndCost.addExtension('loanExitExtensionFeesFieldsCloneDiv');"
                                                  data-original-title="" title="">
                                                <i class="icon-s fas fa-plus"></i>
                                            </span>
                                                <span class="loanExitExtensionFeesFieldsCloneDivRemove <?php echo $class; ?> ">
                                                <span class="btn btn-xs btn-danger btn-icon ml-2 mt-2 cursor-pointer"
                                                      data-toggle="popover"
                                                      data-content="Click to remove" data-original-title="" title=""
                                                      onclick="feesAndCost.removeExtension(this);"
                                                      data-clone-section="loanExitExtensionFeesFieldsCloneDiv"
                                                      data-inc-id="<?php echo $extFieldNo; ?>"
                                                      data-row-id="<?php echo $extField->id; ?>"
                                                      data-LMRId="<?php echo $extField->LMRId; ?>">
                                                    <i class="icon-s fas fa-minus"></i>
                                                </span>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php $extFieldNo++;
                        } ?>
                        <?php
                    } ?>
                </div>
            </div>
            <div class="col-md-6">
                <label class="col-md-12 bg-secondary mb-2 p-2 font-weight-bolder" id="loanExitFeesSubHeading">Exit
                    Fees</label>
                <div class="row loanExitExtensionFeesFields">
                    <div class="col-md-6 form-group">
                        <div class="form-group row exitFeePoints_disp <?php echo loanForm::showField('exitFeePoints'); ?>">
                            <?php echo loanForm::label('exitFeePoints', 'col-md-4  '); ?>
                            <div class="col-md-8">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <input type="text" name="exitFeePoints" id="exitFeePoints"
                                               placeholder="0.00"
                                               class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'exitFeePoints', 'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$exitFeePoints); ?>"
                                               onblur="currencyConverter(this, this.value);calculateExitAmount('points');"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'exitFeePoints', 'sArr' => $EEPSecArr, 'opt' => 'I']); ?>
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                %
                                            </span>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                    <label><?php echo Strings::showField('exitFeePoints', 'fileHMLONewLoanInfo'); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group">
                        <div class="form-group row exitFeeAmount_disp <?php echo loanForm::showField('exitFeeAmount'); ?>">
                            <?php echo loanForm::label('exitFeeAmount', 'col-md-4  '); ?>
                            <div class="col-md-8">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <input type="text" name="exitFeeAmount" id="exitFeeAmount"
                                           placeholder="0.00"
                                           class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'exitFeeAmount', 'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$exitFeeAmount); ?>"
                                           onblur="currencyConverter(this, this.value);calculateExitAmount('amount')"
                                           onkeyup="return restrictAlphabetsLoanTerms(this)"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'exitFeeAmount', 'sArr' => $EEPSecArr, 'opt' => 'I']); ?>
                                           tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                                <?php } else { ?>
                                    <label><?php echo Strings::showField('exitFeeAmount', 'fileHMLONewLoanInfo'); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-md-6">
                <label class="col-md-12 bg-secondary mb-2 p-2 font-weight-bolder" id="prepayPenaltySubHeading">Prepay
                    Penalty</label>
                <div class="row">
                    <div class="col-md-12 loanExitExtensionFeesFields">
                        <div class="row form-group">
                            <div class="col-md-12 form-group isTherePrePaymentPenalty_disp <?php echo loanForm::showField('isTherePrePaymentPenalty'); ?>">
                                <div class="row">
                                    <?php echo loanForm::label('isTherePrePaymentPenalty', 'col-md-8  '); ?>
                                    <div class="col-md-4">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <div class="radio-inline">
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="isTherePrePaymentPenaltyYes">
                                                    <input type="radio" name="isTherePrePaymentPenalty"
                                                           id="isTherePrePaymentPenaltyYes"
                                                           onclick="showAndHidePrePaymentPenalty(this.value, 'prePaymentPenaltyDisOpt');"
                                                           value="Yes"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $EEPSecArr, 'opt' => 'I']); ?>
                                                           tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php if (LoanInfo::$isTherePrePaymentPenalty == 'Yes') {
                                                        echo 'checked';
                                                    } ?>><span></span>Yes
                                                </label>
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="isTherePrePaymentPenaltyNo">
                                                    <input type="radio" name="isTherePrePaymentPenalty"
                                                           id="isTherePrePaymentPenaltyNo"
                                                           onclick="showAndHidePrePaymentPenalty(this.value, 'prePaymentPenaltyDisOpt');"
                                                           value="No"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $EEPSecArr, 'opt' => 'I']); ?>
                                                           tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php if (LoanInfo::$isTherePrePaymentPenalty == 'No') {
                                                        echo 'checked';
                                                    } ?>><span></span>No
                                                </label>
                                            </div>
                                        <?php } else { ?>
                                            <label><?php echo LoanInfo::$isTherePrePaymentPenalty; ?></label>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class="prePaymentPenaltyDisOpt col-md-12 form-group prePaymentPenaltyPercentage_child
                    <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $EEPSecArr,
                                                            'pv'  => LoanInfo::$isTherePrePaymentPenalty, 'av' => 'Yes']); ?>"
                                 style="<?php echo LoanInfo::$prepayentSectionDisplay; ?>">
                                <div class="row">
                                    <?php echo loanForm::label('prePaymentPenaltyPercentage', 'col-md-6  '); ?>
                                    <div class="col-md-6">
                                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                            <div class="row">
                                                <input type="text" name="prePaymentPenaltyPercentage"
                                                       placeholder="0.0"
                                                       class="form-control input-sm col-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'prePaymentPenaltyPercentage', 'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                                       id="prePaymentPenaltyPercentage" <?php echo BaseHTML::fieldAccess(['fNm' => 'prePaymentPenaltyPercentage', 'sArr' => $EEPSecArr, 'opt' => 'I']); ?>
                                                       value="<?php echo Currency::formatRateAmountWithDecimal(LoanInfo::$prePaymentPenaltyPercentage); ?>"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"><span
                                                        class="mt-2">&nbsp;%&nbsp;for&nbsp;</span>
                                                <select name="prePaymentPenalty"
                                                        id="prePaymentPenalty" <?php echo BaseHTML::fieldAccess(['fNm'  => 'prePaymentPenaltyPercentage',
                                                                                                                 'sArr' => $EEPSecArr, 'opt' => 'I']); ?>
                                                        class="form-control input-sm col-7 <?php echo BaseHTML::fieldAccess(['fNm'  => 'prePaymentPenaltyPercentage',
                                                                                                                             'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                                        TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>">
                                                    <option value=""> - Select -</option>
                                                    <?php
                                                    for ($i = 0; $i < count($glHMLOPrePaymentPenalty); $i++) {
                                                        $sOpt = '';
                                                        $prePayment = '';
                                                        $prePayment = trim($glHMLOPrePaymentPenalty[$i]);
                                                        $sOpt = Arrays::isSelected($prePayment, LoanInfo::$prePaymentPenalty);
                                                        echo "<option value=\"" . $prePayment . "\" " . $sOpt . '>' . $prePayment . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                        <?php } else { ?>
                                            <span
                                                    class="H5"><?php echo Currency::formatRateAmountWithDecimal(LoanInfo::$prePaymentPenaltyPercentage); ?></span> % for
                                            <span class="H5"><?php echo LoanInfo::$prePaymentPenalty ?></span>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class="prePaymentPenaltyDisOpt col-md-12 form-group prePaymentSelectVal_child
                    <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $EEPSecArr,
                                                            'pv'  => LoanInfo::$isTherePrePaymentPenalty, 'av' => 'Yes']); ?> "
                                 style="<?php echo LoanInfo::$prepayentSectionDisplay; ?>">
                                <div class="row">
                                    <?php echo loanForm::label('prePaymentSelectVal', 'col-md-6  '); ?>
                                    <div class="col-md-6">
                                        <div class="row">
                                            <?php
                                            echo loanForm::simpleHidden(
                                                'prePaymentSelectValHidden',
                                                implode(',', LoanInfo::$prePaymentSelectValArr ?? [])
                                            ); ?>
                                            <?php if (LMRequest::$allowToEdit) { ?>
                                                <select
                                                        class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm'  => 'prePaymentSelectVal',
                                                                                                                          'sArr' => $EEPSecArr, 'opt' => 'M']); ?>"
                                                        name="prePaymentSelectVal[]" id="prePaymentSelectVal"
                                                        data-placeholder="Please Select "
                                                        multiple="" <?php echo BaseHTML::fieldAccess(['fNm'  => 'prePaymentSelectVal',
                                                                                                      'sArr' => $EEPSecArr, 'opt' => 'I']); ?>>
                                                    <option></option>
                                                    <?php
                                                    for ($k = 0; $k < count($prePaymentPenaltyResArr); $k++) {
                                                        $sel = '';
                                                        if (in_array($prePaymentPenaltyResArr[$k], LoanInfo::$prePaymentSelectValArr ?? [])) $sel = ' selected ';
                                                        ?>
                                                        <option
                                                                value="<?php echo trim($prePaymentPenaltyResArr[$k]); ?>" <?php echo $sel; ?>><?php echo trim($prePaymentPenaltyResArr[$k]) ?></option>
                                                        <?php
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else { ?>
                                                <label><?php echo implode(',', LoanInfo::$prePaymentSelectValArr); ?></label>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php loanForm::popSectionID(); ?>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        let prePaymentOptionCheck = false;
        prePaymentOptionCheck = "<?php echo glCustomJobForProcessingCompany::removeAddOptionForPrePayment(LMRequest::$PCID); ?>";
        if (prePaymentOptionCheck) {
            $('#prePaymentSelectVal').chosen({
                'persistent_create_option': true,
                'skip_no_results': true
            });
        } else {
            $('#prePaymentSelectVal').chosen({
                'create_option': true,
                'persistent_create_option': true,
                'skip_no_results': true
            });

        }
    });
</script>
