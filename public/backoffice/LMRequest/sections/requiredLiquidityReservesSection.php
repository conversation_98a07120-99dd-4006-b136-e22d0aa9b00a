<?php
global $lockedSections, $isEF;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPaymentReserves;
use models\constants\gl\glRequiredConstruction;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\HMLOLoanTermsCalculation;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Strings;

$glPaymentReserves = glPaymentReserves::$glPaymentReserves;
$glRequiredConstruction = glRequiredConstruction::$glRequiredConstruction;
?>


<div class="card card-custom RequiredReservesCard">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Required Liquidity Reserves
            </h3>
            <i class="popoverClass fas fa-info-circle text-primary"
               data-html="true"
               data-content="This section is used to indicate required liquidity in the borrower's financial accounts.
                           For example a bank account. These amounts will not increase or be included in any loan amounts."></i>
        </div>
        <div class="card-toolbar ">
                        <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                              data-card-tool="toggle"
                              data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </span>
            <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                  data-card-tool="toggle"
                  data-section="RequiredReservesCard"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
        </div>
    </div>
    <div class="card-body RequiredReservesCard_body">
        <?php
        if (in_array('Required Reserves', $lockedSections)) {
            $BackupAllowToEdit = LMRequest::$allowToEdit;
            LMRequest::$allowToEdit = false;
        } ?>
        <table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
            <tr class="hideFieldsFor_SPREO"
                style="<?php echo HMLOLoanTermsCalculation::$hideFieldsFor_SPREO; ?>">
                <td width="60%">
                    <label class="font-weight-bold">
                        <i class="fa fa-info-circle text-primary"
                           data-toggle="tooltip"
                           data-trigger="hover"
                           data-html="true"
                           title=""
                           data-original-title="This amount is the monthly payment multiplied by the specified number of months."></i>
                        Interest / Payment Reserves
                    </label>
                    <?php if (!glCustomJobForProcessingCompany::isPC_CV3(LMRequest::$PCID)) { ?>
                        <input type="checkbox"
                               name="includeTaxesInsuranceHOA"
                               id="includeTaxesInsuranceHOA"
                               value="1"
                               onclick="calculatePaymentReserves();calculateTotalRequiredReserves();"
                            <?php echo Strings::isChecked(1, LoanInfo::$includeTaxesInsuranceHOA); ?>
                        >
                        Include Taxes, Insurance & HOA
                    <?php } ?>
                </td>
                <td width="20%">
                    <?php
                    if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                        <select name="paymentReserves"
                                id="paymentReserves"
                                class="form-control"
                                onchange="calculatePaymentReserves('loanModForm', 'paymentReservesAmt');calculateTotalRequiredReserves();"
                                tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                            <option value=""> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($glPaymentReserves); $i++) {
                                $payReserves = trim($glPaymentReserves[$i]);
                                $sOpt = Arrays::isSelected($payReserves, LoanInfo::$paymentReserves);
                                $payReservesMonth = $payReserves . ' Month';
                                if ($payReserves > 1) $payReservesMonth .= 's';
                                ?>
                                <option value="<?php echo $payReserves ?>" <?php echo $sOpt ?>><?php echo $payReservesMonth; ?></option>
                            <?php } ?>
                        </select>
                        <?php
                    } else {
                        if (LoanInfo::$paymentReserves == 0) {
                        } else {
                            echo LoanInfo::$paymentReserves . ' %';
                        }
                    }
                    ?>

                </td>
                <td width="20%"><h5>$
                        <span id="paymentReservesAmt"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$paymentReservesAmt) ?></span>
                    </h5></td>
            </tr>
            <tr class="hideFieldsFor_SPREO"
                style="<?php echo HMLOLoanTermsCalculation::$hideFieldsFor_SPREO; ?>">
                <td width="60%">
                    <label class="font-weight-bold">
                        <i id="lessPrePayInterestReserveToolTip"
                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                           data-html="true"
                           data-formula="<?php echo HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTip; ?>"
                           title="<?php echo HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTipWithValues
                               ? (HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTip . '<hr>' . HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTipWithValues)
                               : HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTip; ?>">
                        </i>
                        Less Pre-paid interest Reserve
                    </label>
                </td>
                <td width="20%"></td>
                <td width="20%">
                    <h5>
                        $
                        <span id="lessPrePayInterestReserve"><?php echo Currency::formatDollarAmountWithDecimalZeros(LoanInfo::$prepaidInterestReserve); ?></span>
                    </h5>
                </td>
            </tr>
            <tr class="hideFieldsFor_SPREO"
                style="<?php echo HMLOLoanTermsCalculation::$hideFieldsFor_SPREO; ?>">
                <td width="60%">
                    <label class="font-weight-bold">
                        <i id="totalInterestPaymentReserveRequiredToolTip"
                           class="fa fa-info-circle pt-3 tooltipClass text-primary"
                           data-html="true"
                           data-formula="<?php echo HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTip; ?>"
                           title="<?php echo HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTipWithValues
                               ? (HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTip . '<hr>' . HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTipWithValues)
                               : HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTip;
                           ?>">
                        </i>
                        Total Interest Payment Reserve Required
                    </label>
                </td>
                <td width="20%"></td>
                <td width="20%">
                    <h5>
                        $
                        <span id="totalInterestPaymentReserveRequired"><?php echo Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequired); ?></span>
                    </h5>
                </td>
            </tr>

            <?php
            if ($isEF != 1) {
                if (HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispDiv) {
                    $doesPropertyNeedRehabDispDiv = 'tableRow';
                }
                if (HMLOLoanTermsCalculation::$contigencyCls) {
                    $contigencyCls = 'tableRow';
                }
                ?>
                <tr class="even doesPropertyNeedRehabDispDiv"
                    style="<?php echo $doesPropertyNeedRehabDispDiv ?>">
                    <td class="contigencyCls"
                        width="60%"
                        style="<?php echo $contigencyCls ?>">
                        <label class="font-weight-bold hideFieldsFor_SPREO"
                               style="<?php echo HMLOLoanTermsCalculation::$hideFieldsFor_SPREO; ?>">
                            <i class="fa fa-info-circle text-primary"
                               data-toggle="tooltip"
                               data-trigger="hover"
                               data-html="true"
                               title="This amount is the specified % of a rehab amount not being included in the financing of the loan."></i>
                            % Required Construction /<br>Rehab Budget Not Financed
                        </label>
                    </td>
                    <td width="20%"
                        class="hideFieldsFor_SPREO"
                        style="<?php echo HMLOLoanTermsCalculation::$hideFieldsFor_SPREO; ?>">
                        <?php
                        if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <select name="requiredConstruction"
                                    id="requiredConstruction"
                                    class="form-control"
                                    onchange="calculateRequiredConstruction('requiredConstructionAmt');calculateTotalRequiredReserves();"
                                    tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                                <option value=""> - Select -</option>
                                <?php
                                foreach ($glRequiredConstruction as $eachRequiredConstruction) {
                                    $eachRequiredConstruction = trim($eachRequiredConstruction);
                                    $sOpt = Arrays::isSelected($eachRequiredConstruction, LoanInfo::$requiredConstruction);
                                    ?>
                                    <option value="<?php echo $eachRequiredConstruction ?>" <?php echo $sOpt ?>><?php echo $eachRequiredConstruction ?></option>
                                <?php } ?>
                            </select>
                            <?php
                        } else {
                            echo LoanInfo::$requiredConstruction ? LoanInfo::$requiredConstruction . ' %' : '';
                        }
                        ?>
                    </td>
                    <td class="contigencyCls"
                        style="<?php echo $contigencyCls ?>">
                        <h5 class="hideFieldsFor_SPREO"
                            style="<?php echo HMLOLoanTermsCalculation::$hideFieldsFor_SPREO; ?>">
                            $ <span
                                id="requiredConstructionAmt"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$requiredConstructionAmt) ?></span>
                        </h5>
                    </td>
                </tr>
                <tr class="doesPropertyNeedRehabDispDiv"
                    style="<?php echo $doesPropertyNeedRehabDispDiv ?>">
                    <td width="60%"
                        class="">
                        <label class="font-weight-bold">
                            <i class="fa fa-info-circle text-primary popoverClass"
                               data-trigger="hover"
                               data-html="true"
                               title="% Contingency Reserve"
                               id="contingencyReserveAmtTooltip"
                               data-content="<?=
                               HMLOLoanTermsCalculation::$contingencyReserveTooltipWithValues ?
                                   HMLOLoanTermsCalculation::$contingencyReserveTooltip . '<hr>' . HMLOLoanTermsCalculation::$contingencyReserveTooltipWithValues : HMLOLoanTermsCalculation::$contingencyReserveTooltip; ?>"></i>
                            % Contingency Reserve
                        </label>
                    </td>
                    <td width="20%">
                        <div class="hideFieldsFor_SPREO"
                             style="<?php echo HMLOLoanTermsCalculation::$hideFieldsFor_SPREO; ?>">
                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                <select name="contingencyReserve"
                                        id="contingencyReserve"
                                        class="form-control"
                                        onchange="loanCalculation.calculateContingencyReserve();calculateTotalRequiredReserves();"
                                        tabindex="<?php echo LoanInfo::$tabIndex++; ?>">
                                    <option value=""> - Select -</option>
                                    <?php
                                    foreach ($glRequiredConstruction as $contingency) {
                                        $sOpt = Arrays::isSelected($contingency, LoanInfo::$contingencyReserve); ?>
                                        <option value="<?php echo $contingency ?>" <?php echo $sOpt ?>><?php echo $contingency ?></option>
                                    <?php } ?>
                                </select>
                                <?php
                            } else {
                                echo LoanInfo::$contingencyReserve ? LoanInfo::$contingencyReserve . ' %' : '';
                            }
                            ?>
                        </div>
                    </td>
                    <td><h5>$
                            <span id="contingencyReserveAmt"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$contingencyReserveAmt) ?></span>
                        </h5>
                    </td>
                </tr>
            <?php } ?>
            <tr class="hideFieldsFor_SPREO"
                style="<?php echo HMLOLoanTermsCalculation::$hideFieldsFor_SPREO; ?>">
                <td width="60%">
                    <label class="font-weight-bold">
                        <i class="fa fa-info-circle text-primary"
                           data-toggle="tooltip"
                           data-trigger="hover"
                           data-html="true"
                           title=""
                           data-original-title="This amount is the specified % of the total loan amount."></i>
                        % of Total Loan Amount
                    </label>
                </td>
                <td width="20%">
                    <?php
                    if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                        <input type="text" class="form-control"
                               name="percentageTotalLoan"
                               placeholder="0.0"
                               id="percentageTotalLoan"
                               value="<?php echo LoanInfo::$percentageTotalLoan; ?>"
                               onblur="calculateTotalRequiredReserves();"
                        >
                        <?php
                    } else {
                        echo LoanInfo::$percentageTotalLoan ? LoanInfo::$percentageTotalLoan . ' %' : '';
                    }
                    ?>
                </td>
                <td width="20%"><h5>$
                        <span id="percentageTotalLoanAmount"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$percentageTotalLoanAmount) ?></span>
                    </h5></td>
            </tr>
            <tr class="even">
                <td colspan="2">
                    <label class="font-weight-bold">
                        <i id="totalRequiredReservesTooltip"
                           class="fa fa-info-circle text-primary popoverClass"
                           data-html="true"
                           data-formula="<?php echo HMLOLoanTermsCalculation::$totalRequiredReservesTooltip; ?>"
                           data-content="<?php echo HMLOLoanTermsCalculation::$totalRequiredReservesTooltipWithValues
                               ? (HMLOLoanTermsCalculation::$totalRequiredReservesTooltip . '<hr>' . HMLOLoanTermsCalculation::$totalRequiredReservesTooltipWithValues)
                               : HMLOLoanTermsCalculation::$totalRequiredReservesTooltip;
                           ?>">
                        </i>
                        Total Required Reserves</label>
                </td>
                <td class="h5">$<span id="totalRequiredReserves"
                                      class="pl-2"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalRequiredReserves) ?></span>
                </td>
            </tr>
        </table>
        <?php
        if (in_array('Required Reserves', $lockedSections)) {
            //$BackupAllowToEdit = $allowToEdit;
            LMRequest::$allowToEdit = $BackupAllowToEdit;
        }
        ?>
    </div>
</div>