<?php
global $lockedSections;


use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\HMLOLoanTermsCalculation;
use models\standard\Currency;
?>

<div class="card card-custom cashToCloseCard">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Cash-to-Close
            </h3>
        </div>
        <div class="card-toolbar">
                        <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                              data-card-tool="toggle"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                  data-card-tool="reload"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </span>
            <span class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                  data-card-tool="toggle"
                  data-section="cashToCloseCard"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
        </div>
    </div>

    <div class="card-body cashToCloseCard_body">
        <?php
        if (in_array('Required Reserves', $lockedSections)) {
            $BackupAllowToEdit = LMRequest::$allowToEdit;
            LMRequest::$allowToEdit = false;
        } ?>
        <table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
            <tr class="hideFieldsForLTC2_OR_SPREO"
                style="">
                <td><h5>Closing Costs Not Financed</h5></td>
                <td class="h5">$
                    <span id="closingCostNotFinanced"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$closingCostNotFinanced) ?></span>
                </td>
            </tr>
            <tr class="even downPaymentField hideFieldsForLTC2_OR_SPREO "
                style="<?php echo HMLOLoanTermsCalculation::$downPaymentFieldDispOpt; ?> ">
                <td><h5>Overall Down Payment</h5></td>
                <td class="h5">$
                    <span id="downPayment"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$maxAmtToPutDown) ?></span>
                </td>
            </tr>
            <tr class="hideFieldsForLTC2_OR_SPREO "
                style="">
                <td><h5>Earnest Deposit</h5></td>
                <td>
                    <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   name="earnestDeposit"
                                   id="earnestDeposit"
                                   class="form-control"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$earnestDeposit) ?>"
                                   size="20"
                                   TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                   autocomplete="off"
                                   onkeyup="return restrictAlphabetsLoanTerms(this)"
                                   onblur="currencyConverter(this, this.value);calculateTotalCashToClose();"
                            />

                        </div>
                    <?php } else { ?>
                        <h5>$ <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$earnestDeposit) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr class="even hideFieldsForLTC2_OR_SPREO "
                style="">
                <td><h5>Paid Outside Escrow</h5></td>
                <td>
                    <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   name="otherDownPayment"
                                   id="otherDownPayment"
                                   placeholder="0.00"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$otherDownPayment) ?>"
                                   size="20"
                                   TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                   autocomplete="off"
                                   class="form-control"
                                   onkeyup="return restrictAlphabetsLoanTerms(this)"
                                   onblur="currencyConverter(this, this.value);calculateTotalCashToClose();"
                            />
                        </div>
                    <?php } else { ?>
                        <h5>$ <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$otherDownPayment) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td class="h5">
                    <i class="fa fa-info-circle text-primary popoverClass ml-2"
                       data-html="true"
                       id="totalCashToCloseTooltip"
                       title="Total Cash to Close"
                       data-formula="<?php echo HMLOLoanTermsCalculation::$totalCashToCloseTooltip; ?>"
                       data-content="<?php echo HMLOLoanTermsCalculation::$totalCashToCloseTooltipWithValues ?
                           (HMLOLoanTermsCalculation::$totalCashToCloseTooltip . '<hr>' . HMLOLoanTermsCalculation::$totalCashToCloseTooltipWithValues)
                           : HMLOLoanTermsCalculation::$totalCashToCloseTooltip ?>"></i>
                    Total Cash to Close
                </td>
                <td class="h5">$
                    <span id="totalCashToClose"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalCashToClose) ?></span>
                </td>
            </tr>
        </table>

        <?php
        if (in_array('Required Reserves', $lockedSections)) {
            //$BackupAllowToEdit = $allowToEdit;
            LMRequest::$allowToEdit = $BackupAllowToEdit;
        }
        ?>
    </div>
</div>
