<?php

use models\constants\gl\glLienNumber;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\refinanceMortgage;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\lendingwise\tblRefinanceMortgage;
use models\cypher;
use models\lendingwise\tblVOMPayoffStatus;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

global $fieldsInfo, $secArr, $typeOfHMLOLoanRequesting, $refinanceSectionDispOpt, $allowToEdit, $LMRId, $fileTab, $tabIndex,
       $seekingCashRefinance, $seekingCashRefinanceDispOpt, $seekingCash, $seekingFund;

$refinanceMortgageInfo = refinanceMortgage::$refinanceMortgageInfo;
if (!sizeof($refinanceMortgageInfo ?? [])) {
    $refinanceMortgageInfo = [
        new tblRefinanceMortgage(),
    ];
}
$secArr = BaseHTML::sectionAccess2(['sId' => 'RCM', 'opt' => $fileTab]); // Get Active Fields only...
loanForm::pushSectionID('RCM');

?>

<!-- refinanceMortgageForm.php -->
<div class="card card-custom HMLOLoanInfoSections refinanceSection refinanceSectionCard RCM <?php if (count(Arrays::getValueFromArray('RCM', $fieldsInfo)) <= 0) {
    echo ' secHide ';
} ?> <?php echo BaseHTML::parentFieldAccess(['fNm' => 'typeOfHMLOLoanRequesting', 'sArr' => $secArr, 'pv' => $typeOfHMLOLoanRequesting, 'mv' => 'Rate & Term Refinance,Cash-Out / Refinance,Commercial Rate / Term Refinance,Commercial Cash Out Refinance,Refinance']); ?>"
     style="<?php echo $refinanceSectionDispOpt; ?>">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('RCM') ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('RCM')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('RCM'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar ">
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="refinanceSectionCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
            <?php if ($allowToEdit) { ?>
                <a href="javascript:void(0);"
                   class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass cloneFormButton "
                   title="Click to add new row">
                    <i class=" fas fa-plus  icon-md tooltipclass" title="Click to Add New Rent Roll"></i>
                </a>
            <?php } ?>
        </div>
    </div>
    <div class="card-body refinanceSectionCard_body">
        <?php $rcm_i = 1;
        foreach ($refinanceMortgageInfo as $item) { ?>
            <div class="card card-custom mb-2 RCMCloneSection" id="RCMCloneSectionId_<?php echo $rcm_i; ?>">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            <?php echo BaseHTML::getSectionHeading('RCM'); ?>
                            <span class="icrementClass"><?php echo $rcm_i; ?></span>
                        </h3>
                    </div>
                    <div class="card-toolbar ">
                        <a href="javascript:void(0);"
                           class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                           data-card-tool="toggle"
                           data-section="refinanceSectionCard"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                           data-card-tool="reload"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </a>
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none"
                           data-card-tool="remove"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                            <i class="ki ki-close icon-nm"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body ">
                    <div class="row">
                        <input type="hidden" name="rcmFields[<?php echo $rcm_i; ?>][id]"
                               value="<?php echo $item->id; ?>"/>
                        <div class="col-md-6 originalLienNumber_disp <?php echo loanForm::showField('originalLienNumber'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('originalLienNumber', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <select class="chzn-select form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'originalLienNumber', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                                data-placeholder="Please Select <?php echo BaseHTML::fieldAccess(['fNm' => 'originalLienNumber', 'sArr' => $secArr, 'opt' => 'L']); ?>"
                                                name="rcmFields[<?php echo $rcm_i; ?>][originalLienNumber]"
                                                id="originalLienNumber_<?php echo $rcm_i; ?>"
                                                TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'originalLienNumber', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""></option>
                                            <?php
                                            foreach (glLienNumber::$glLienNumber as $eachLienNumberOption) { ?>
                                                <option value="<?php echo $eachLienNumberOption; ?>" <?php echo Arrays::isSelected($item->originalLienNumber, $eachLienNumberOption) ?>><?php echo $eachLienNumberOption; ?></option>
                                            <?php } ?>
                                        </select>
                                    <?php } else { ?>
                                        <b><?php echo $item->originalLienNumber; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 originalPayOffAmount_disp <?php echo loanForm::showField('originalPayOffAmount'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('originalPayOffAmount', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPayOffAmount', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][originalPayOffAmount]"
                                                   id="originalPayOffAmount_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($item->originalPayOffAmount) ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   placeholder="0.00" TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPayOffAmount', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Currency::formatDollarAmountWithDecimal($item->originalPayOffAmount); ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 VOMStatus_disp <?php echo loanForm::showField('VOMStatus'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('VOMStatus', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <select class="chzn-select form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'VOMStatus', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                                data-placeholder="Please Select <?php echo BaseHTML::fieldAccess(['fNm' => 'VOMStatus', 'sArr' => $secArr, 'opt' => 'L']); ?>"
                                                name="rcmFields[<?php echo $rcm_i; ?>][VOMStatus]"
                                                id="VOMStatus_<?php echo $rcm_i; ?>"
                                                TABINDEX="<?php echo $tabIndex++; ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'VOMStatus', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""></option>
                                            <?php
                                            foreach (tblVOMPayoffStatus::options() as $key => $status) { ?>
                                                <option value="<?php echo $key; ?>" <?php echo Arrays::isSelected($item->VOMStatus, $key) ?>><?php echo $status; ?></option>
                                            <?php } ?>
                                        </select>
                                    <?php } else { ?>
                                        <b><?php echo tblVOMPayoffStatus::options()[$item->VOMStatus]; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 payoffStatus_disp <?php echo loanForm::showField('payoffStatus'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('payoffStatus', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <select class="chzn-select form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'payoffStatus', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                                data-placeholder="Please Select <?php echo BaseHTML::fieldAccess(['fNm' => 'payoffStatus', 'sArr' => $secArr, 'opt' => 'L']); ?>"
                                                name="rcmFields[<?php echo $rcm_i; ?>][payoffStatus]"
                                                id="payoffStatus_<?php echo $rcm_i; ?>"
                                                TABINDEX="<?php echo $tabIndex++; ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'payoffStatus', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""></option>
                                            <?php
                                            foreach (tblVOMPayoffStatus::options() as $key => $status) { ?>
                                                <option value="<?php echo $key; ?>" <?php echo Arrays::isSelected($item->payoffStatus, $key) ?>><?php echo $status; ?></option>
                                            <?php } ?>
                                        </select>
                                    <?php } else { ?>
                                        <b><?php echo tblVOMPayoffStatus::options()[$item->payoffStatus]; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 originalPurchaseDate_disp <?php echo loanForm::showField('originalPurchaseDate'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('originalPurchaseDate', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                    <span class="input-group-text originalPurchaseDate">
                                                        <i class="fa fa-calendar text-primary" aria-hidden="true"></i>
                                                    </span>
                                            </div>
                                            <input class="form-control dateNewClass input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPurchaseDate', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][originalPurchaseDate]"
                                                   id="originalPurchaseDate_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Dates::formatDateWithRE($item->originalPurchaseDate, 'YMD', 'm/d/Y'); ?>"
                                                   placeholder="MM/DD/YYYY"
                                                   TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                   onchange="populateDualField(this.value, 'originalPurchaseDate_mirror')" <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPurchaseDate', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        </div>
                                    <?php } else { ?>
                                        <b><?php echo Dates::formatDateWithRE($item->originalPurchaseDate, 'YMD', 'm/d/Y'); ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 refinanceCurrentLender_disp <?php echo loanForm::showField('refinanceCurrentLender'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('refinanceCurrentLender', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentLender', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               type="text"
                                               name="rcmFields[<?php echo $rcm_i; ?>][refinanceCurrentLender]"
                                               id="refinanceCurrentLender_<?php echo $rcm_i; ?>"
                                               value="<?php echo $item->refinanceCurrentLender; ?>"
                                               autocomplete="off"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               placeholder=" - Type Name Here - " <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentLender', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <b><?php echo $item->refinanceCurrentLender; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 refinanceCurrentLenderFullAddress_disp <?php echo loanForm::showField('refinanceCurrentLenderFullAddress'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('refinanceCurrentLenderFullAddress', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentLenderFullAddress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               type="text"
                                               name="rcmFields[<?php echo $rcm_i; ?>][refinanceCurrentLenderFullAddress]"
                                               id="refinanceCurrentLenderFullAddress_<?php echo $rcm_i; ?>"
                                               value="<?php echo $item->refinanceCurrentLenderFullAddress; ?>"
                                               autocomplete="off"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentLenderFullAddress', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <b><?php echo $item->refinanceCurrentLenderFullAddress; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 refinanceCurrentLenderEmail_disp <?php echo loanForm::showField('refinanceCurrentLenderEmail'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('refinanceCurrentLenderEmail', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentLenderEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               type="email"
                                               name="rcmFields[<?php echo $rcm_i; ?>][refinanceCurrentLenderEmail]"
                                               id="refinanceCurrentLenderEmail_<?php echo $rcm_i; ?>"
                                               value="<?php echo $item->refinanceCurrentLenderEmail; ?>"
                                               autocomplete="off"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentLenderEmail', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <b><?php echo $item->refinanceCurrentLenderEmail; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 originalPurchasePrice_disp <?php echo loanForm::showField('originalPurchasePrice');
                        if ($rcm_i > 1) {
                            echo ' d-none ';
                        } ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('originalPurchasePrice', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm LTCOriginalPurchasePriceField <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPurchasePrice', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][originalPurchasePrice]"
                                                   id="originalPurchasePrice_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($item->originalPurchasePrice); ?>"
                                                   onchange="currencyConverter(this, this.value);populateDualField(this.value, 'originalPurchasePrice_mirror');loanCalculation.calculateGrossProfit();"
                                                   placeholder="0.00" TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPurchasePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Currency::formatDollarAmountWithDecimal($item->originalPurchasePrice); ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 originalMaturityDate_disp <?php echo loanForm::showField('originalMaturityDate'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('originalMaturityDate', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend originalMaturityDate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm dateNewClass  <?php echo BaseHTML::fieldAccess(['fNm' => 'originalMaturityDate', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][originalMaturityDate]"
                                                   id="originalMaturityDate_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Dates::formatDateWithRE($item->originalMaturityDate, 'YMD', 'm/d/Y'); ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" placeholder="MM/DD/YYYY"/>
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Dates::formatDateWithRE($item->originalMaturityDate, 'YMD', 'm/d/Y'); ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 originalPrepayPenalty_disp <?php echo loanForm::showField('originalPrepayPenalty'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('originalPrepayPenalty', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPrepayPenalty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][originalPrepayPenalty]"
                                                   id="originalPrepayPenalty_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($item->originalPrepayPenalty) ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   placeholder="0.00" TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPrepayPenalty', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Currency::formatDollarAmountWithDecimal($item->originalPrepayPenalty) ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 originalPrepayPercentage_disp <?php echo loanForm::showField('originalPrepayPercentage'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('originalPrepayPercentage', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <input type="text"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][originalPrepayPercentage]"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPrepayPercentage', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   id="originalPrepayPercentage_<?php echo $rcm_i; ?>"
                                                   value="<?php echo number_format(Strings::replaceCommaValues($item->originalPrepayPercentage), 3); ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>" placeholder="0.00"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPrepayPercentage', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo number_format(Strings::replaceCommaValues($item->originalPrepayPercentage)); ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-6 costOfImprovementsMade_disp <?php echo loanForm::showField('costOfImprovementsMade'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('costOfImprovementsMade', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'costOfImprovementsMade', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][costOfImprovementsMade]"
                                                   id="costOfImprovementsMade_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($item->costOfImprovementsMade) ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   placeholder="0.00" TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'costOfImprovementsMade', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Currency::formatDollarAmountWithDecimal($item->costOfImprovementsMade) ?></b>
                                    <?php } ?>

                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 originalTaxesIncluded_disp <?php echo loanForm::showField('originalTaxesIncluded'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('originalTaxesIncluded', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid "
                                                   for="originalTaxesIncludedYes_<?php echo $rcm_i; ?>">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'originalTaxesIncluded', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="rcmFields[<?php echo $rcm_i; ?>][originalTaxesIncluded]"
                                                       value="Yes"
                                                       id="originalTaxesIncludedYes_<?php echo $rcm_i; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('Yes', $item->originalTaxesIncluded); ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'originalTaxesIncluded', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <span></span>Yes
                                            </label>
                                            <label class="radio radio-solid"
                                                   for="originalTaxesIncludedNo_<?php echo $rcm_i; ?>">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'originalTaxesIncluded', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="rcmFields[<?php echo $rcm_i; ?>][originalTaxesIncluded]"
                                                       value="No"
                                                       id="originalTaxesIncludedNo_<?php echo $rcm_i; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('No', $item->originalTaxesIncluded); ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'originalTaxesIncluded', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <span></span>No
                                            </label>
                                        </div>
                                    <?php } else { ?>
                                        <b> <?php echo $item->originalTaxesIncluded; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 refinanceMonthlyPayment_disp <?php echo loanForm::showField('refinanceMonthlyPayment'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('refinanceMonthlyPayment', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceMonthlyPayment', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][refinanceMonthlyPayment]"
                                                   id="refinanceMonthlyPayment_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($item->refinanceMonthlyPayment) ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   placeholder="0.00" TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceMonthlyPayment', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Currency::formatDollarAmountWithDecimal($item->refinanceMonthlyPayment) ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 refinanceCurrentRate_disp <?php echo loanForm::showField('refinanceCurrentRate'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('refinanceCurrentRate', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <input type="text"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][refinanceCurrentRate]"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentRate', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   id="refinanceCurrentRate_<?php echo $rcm_i; ?>"
                                                   value="<?php echo number_format(Strings::replaceCommaValues($item->refinanceCurrentRate), 3); ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>" placeholder="0.00"
                                                   autocomplete="off"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentRate', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    <?php } else { ?>
                                        <b><?php echo number_format(Strings::replaceCommaValues($item->refinanceCurrentRate), 3); ?>
                                            %</b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 refinanceCurrentLoanBalance <?php echo loanForm::showField('refinanceCurrentLoanBalance'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('refinanceCurrentLoanBalance', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentLoanBalance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][refinanceCurrentLoanBalance]"
                                                   id="refinanceCurrentLoanBalance_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($item->refinanceCurrentLoanBalance) ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   placeholder="0.00"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'refinanceCurrentLoanBalance', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Currency::formatDollarAmountWithDecimal($item->refinanceCurrentLoanBalance) ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 goodThroughDate_disp <?php echo loanForm::showField('goodThroughDate'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('goodThroughDate', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend goodThroughDate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm dateNewClass  <?php echo BaseHTML::fieldAccess(['fNm' => 'goodThroughDate', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][goodThroughDate]"
                                                   id="goodThroughDate_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Dates::formatDateWithRE($item->goodThroughDate, 'YMD', 'm/d/Y'); ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" placeholder="MM/DD/YYYY"/>
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Dates::formatDateWithRE($item->goodThroughDate, 'YMD', 'm/d/Y'); ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-6 subjectOriginalBalance <?php echo loanForm::showField('subjectOriginalBalance'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('subjectOriginalBalance', 'col-md-5', '', '', $rcm_i); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'subjectOriginalBalance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="rcmFields[<?php echo $rcm_i; ?>][subjectOriginalBalance]"
                                                   id="subjectOriginalBalance_<?php echo $rcm_i; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($item->subjectOriginalBalance) ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   placeholder="0.00"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'subjectOriginalBalance', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Currency::formatDollarAmountWithDecimal($item->subjectOriginalBalance) ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class=" col-md-12">
                            <label class="font-weight-bold" style="color: #0462b3"> Add New Mortgage </label>
                            <a class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass <?php if ($rcm_i < sizeof($refinanceMortgageInfo)) {
                                echo 'd-none';
                            } ?> cloneRCMForm addButton<?php echo $rcm_i; ?>"
                               href="javascript:void(0)"
                               data-clone-section="RCMCloneSection"
                               data-increment-section="icrementClass"
                               title="Click to add new Refinance.">
                                <i class=" icon-md fas fa-plus "></i>
                            </a>
                            <a class="btn btn-sm btn-danger btn-text-primary  btn-icon ml-2 tooltipClass removeRCMForm removeButton<?php echo $rcm_i; ?>"
                               href="javascript:void(0)"
                               data-clone-section="RCMCloneSection"
                               data-increment-section="icrementClass"
                               data-id="<?php echo cypher::myEncryption($item->id); ?>"
                               title="Click to Remove Refinance.">
                                <i class=" icon-md fas fa-minus-circle "></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php $rcm_i++;
        } ?>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'RCM',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<script src="/backoffice/LMRequest/js/refinanceMortgage.js?<?php echo CONST_JS_VERSION; ?>"></script>

<!-- refinanceMortgageForm.php -->
