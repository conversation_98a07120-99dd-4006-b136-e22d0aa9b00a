<?php
global $fileTab, $HMLOLoanInfoSectionsDisp, $allowToEdit, $tabIndex,
       $haveBorREInvestmentExperience, $borNoOfFlippingExperience, $borNoOfREPropertiesCompleted, $PCID,
       $flipPropCompletedLifetime, $clientExpProInfo, $clientDocsArray,
       $registerDate, $selClientId, $publicUser,
       $borborFlipExit, $stateArray,
       $haveBorRehabConstructionExperience, $borNoOfYearRehabExperience,
       $borRehabPropCompleted, $areBuilderDeveloper, $groundPropCompletedLifetime,
       $borResidedPresentAddr, $haveBorSellPropertie, $borNoOfProSellExperience,
       $borNoOfProSellCompleted, $sellPropCompletedLifetime, $clientGUExpInfo, $clientSellExpInfo,
       $borborSellExit, $haveBorProjectCurrentlyInProgress, $borNoOfProjectCurrently,
       $haveBorOwnInvestmentProperties, $borNoOfOwnProp, $areBorMemberOfInvestmentClub,
       $borClubName, $haveBorProfLicences, $borProfLicence, $borLicenseNo,
       $fullTimeRealEstateInvestor, $liquidAssets,
       $borPriInvesStrategyArray, $borPrimaryInvestmentStrategy, $overallRealEstateInvesExp,
       $borPrimaryInvestmentStrategyExplain, $amountOfFinancing, $amountOfFinancingTo,
       $typicalPurchasePrice, $typicalPurchasePriceTo, $typicalConstructionCosts,
       $typicalConstructionCostsTo, $typicalSalePrice, $typicalSalePriceTo,
       $constructionDrawsPerProject, $constructionDrawsPerProjectTo,
       $monthsPurchaseDateToFirstConst, $monthsPurchaseDateToFirstConstTo,
       $monthsPurchaseDateUntilConst, $monthsPurchaseDateUntilConstTo,
       $monthsPurchaseDateToSaleDate, $monthsPurchaseDateToSaleDateTo,
       $NoOfSuchProjects, $NoOfSuchProjectsTo, $geographicAreas, $dN,
       $doYouHireGC, $borOutcomeRE, $fileModuleInfo, $LMRId, $trackRecord;

global $hideThisField, $activeTab;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glExitValues;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\glOwnershipStatus;
use models\constants\gl\glPCID;
use models\constants\gl\glPrimaryInvestmentStrategyArray;
use models\constants\gl\glProfLicences;
use models\constants\gl\glPropertyTypeExp;
use models\constants\gl\glRestrictCustomFields;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\cypher;
use models\lendingwise\tblBorrowerExperienceTrackRecord;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$glExitValues = glExitValues::$glExitValues;
$glPropertyTypeExp = glPropertyTypeExp::$glPropertyTypeExp;
$glProfLicencesBusinessFunding = glProfLicences::$glProfLicencesForBusinessFunding;
$glProfLicences = glProfLicences::$glProfLicences;
$glPrimaryInvestmentStrategyArray = glPrimaryInvestmentStrategyArray::$glPrimaryInvestmentStrategyArray;
$glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;
$glRestrictCustomFields = glRestrictCustomFields::$glRestrictCustomFields;
$glOwnershipStatus = glOwnershipStatus::$glOwnershipStatus;
$countOfPastDeals = glCustomJobForProcessingCompany::getCountOfPastDeals($PCID) ?? 0;

$secArr = BaseHTML::sectionAccess2(['sId' => 'BE', 'opt' => $fileTab]); // Get Active Fields only...
loanForm::pushSectionID('BE');
?>
<!-- borrowerExperience.php -->
<div class="card card-custom  HMLOLoanInfoSections isClientInfo BECard BE <?php if (count($secArr) <= 0) {
    echo 'secHide';
} ?>"
     style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <!-- Fliping Experience. -->

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('BE'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('BE')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('BE'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">

            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary  d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="BECard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body BECard_body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group row  haveBorREInvestmentExperience_disp <?php echo loanForm::showField('haveBorREInvestmentExperience'); ?>">
                    <?php echo loanForm::label('haveBorREInvestmentExperience', 'col-md-6 '); ?>
                    <div class="col-md-3 no-padding">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold"
                                       for="haveBorREInvestmentExperienceYes">
                                    <input type="radio" name="haveBorREInvestmentExperience"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorREInvestmentExperience', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorREInvestmentExperienceYes" value="Yes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $haveBorREInvestmentExperience); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borRealEstateInvestmentDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorREInvestmentExperience', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="haveBorREInvestmentExperienceNo">
                                    <input type="radio" name="haveBorREInvestmentExperience"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorREInvestmentExperience', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorREInvestmentExperienceNo" value="No"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $haveBorREInvestmentExperience); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borRealEstateInvestmentDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorREInvestmentExperience', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                </label></div>
                        <?php } else { ?><h5><?php echo $haveBorREInvestmentExperience; ?></h5><?php } ?>
                    </div>
                </div>

                <div class="form-group borRealEstateInvestmentDiv <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveBorREInvestmentExperience', 'sArr' => $secArr, 'pv' => $haveBorREInvestmentExperience, 'av' => 'Yes']); ?>">
                    <div class="row">
                        <div class="col-md-6 borNoOfFlippingExperience_disp <?php echo loanForm::showField('borNoOfFlippingExperience'); ?>">
                            <div class="form-group row">
                            <?php echo loanForm::label('borNoOfFlippingExperience', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfFlippingExperience', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="borNoOfFlippingExperience" id="borNoOfFlippingExperience"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $borNoOfFlippingExperience; ?>"
                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfFlippingExperience', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?><h5><?php echo $borNoOfFlippingExperience; ?></h5><?php } ?>
                            </div>
                            </div>
                        </div>
                        <div class="col-md-6  borNoOfREPropertiesCompleted_disp <?php echo loanForm::showField('borNoOfREPropertiesCompleted'); ?>">
                            <div class="form-group row">
                            <?php echo loanForm::label('borNoOfREPropertiesCompleted', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number" min="0" oninput="validity.valid||(value='');"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfREPropertiesCompleted', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="borNoOfREPropertiesCompleted"
                                           onchange="validateMinMaxLoanGuidelines();"
                                           id="borNoOfREPropertiesCompleted" tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $borNoOfREPropertiesCompleted; ?>"
                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfREPropertiesCompleted', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?><h5><?php echo $borNoOfREPropertiesCompleted; ?></h5><?php } ?>
                            </div>
                            </div>
                        </div>
                    </div>
                    
                        <?php
                        if (isset($glRestrictCustomFields[$PCID])) { //https://www.pivotaltracker.com/story/show/161218497
                            if (in_array('flipPropCompletedLifetime', $glRestrictCustomFields[$PCID])) {
                                ?>
                                <div class="col-md-4  flipPropCompletedLifetime_disp <?php echo loanForm::showField('flipPropCompletedLifetime'); ?>">
                                    <?php echo loanForm::label('flipPropCompletedLifetime', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="number"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'flipPropCompletedLifetime', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="flipPropCompletedLifetime" id="flipPropCompletedLifetime"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $flipPropCompletedLifetime; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'flipPropCompletedLifetime', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?><h5><?php echo $flipPropCompletedLifetime; ?></h5><?php } ?>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                        ?>
                        <div class="form-group row borREAddress_disp <?php echo loanForm::showField('borREAddress'); ?>">

                            <div class="col-md-12 m-0 mb-2 px-0" id="borExppropdetailstitle">
                                <label class="bg-secondary  py-2  col-lg-12"><b><?php echo BaseHTML::fieldAccess(['fNm' => 'borREAddress', 'sArr' => $secArr, 'opt' => 'L']); ?></b></label>
                            </div>

                            <?php
                            for ($nop = 0; $nop < $countOfPastDeals; $nop++) {
                                $borFlipPropType = '';
                                $borFlipPurchaseDate = '';
                                $borFlipPurchasePrice = '';
                                $borFlipAmountFinanced = '';
                                $borFlipRehabBudget = '';
                                $borFlipEntityName = '';
                                $borFlipOwnership = '';
                                $borFlipOwnershipStatus = '';
                                $borFlipExit = '';
                                $borFlipSalePrice = '';
                                $borFlipSaleDate = '';
                                $borFlipMonthlyRent = '';
                                $borFlipAddress = '';
                                $borFlipCity = '';
                                $borFlipState = '';
                                $borFlipZip = '';
                                $borFlipUnit = '';
                                $borOutcomeRE = '';
                                $showSaleSec = 'display: none;';
                                $docCategory = 'Proof of sale (HUD)' . $nop;
                                $showMonthlyRent = 'display: none;';
                                $uploadDocUrl = '';
                                $cls = '';
                                $dir = '';
                                if ((($nop + 1) % 2) == 0) $cls = 'even';

                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipPropType = $clientExpProInfo[$nop]['propertyType'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipPurchaseDate = $clientExpProInfo[$nop]['purchaseDate'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipPurchasePrice = $clientExpProInfo[$nop]['purchasePrice'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipAmountFinanced = $clientExpProInfo[$nop]['amountFinanced'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipRehabBudget = $clientExpProInfo[$nop]['rehabBudget'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipEntityName = $clientExpProInfo[$nop]['entityName'];

                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipOwnership = $clientExpProInfo[$nop]['ownership'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipOwnershipStatus = $clientExpProInfo[$nop]['ownershipStatus'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipExit = $clientExpProInfo[$nop]['exitValues'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipSalePrice = $clientExpProInfo[$nop]['salePrice'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipSaleDate = $clientExpProInfo[$nop]['saleDate'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipMonthlyRent = $clientExpProInfo[$nop]['monthlyRent'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipAddress = $clientExpProInfo[$nop]['address'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipCity = $clientExpProInfo[$nop]['city'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipState = $clientExpProInfo[$nop]['state'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipZip = $clientExpProInfo[$nop]['zip'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borFlipUnit = $clientExpProInfo[$nop]['unit'];
                                if (array_key_exists($nop, $clientExpProInfo)) $borOutcomeRE = $clientExpProInfo[$nop]['Outcome'];

                                if (array_key_exists($docCategory, $clientDocsArray)) {
                                    if ($publicUser) {
                                        $registerDate = Strings::showField('registerDate', 'clientInfo');
                                        $selClientId = Strings::showField('clientId', 'LMRInfo');
                                    }
                                    $docName = $clientDocsArray[$docCategory]['fileName'];
                                    $dN = $clientDocsArray[$docCategory]['docName'];
                                    $fCreatedName = str_replace('-', '', $registerDate);
                                    $dir = CONST_PATH_CLIENT_DOC;
                                    $dir .= '/' . date('Y', strtotime($fCreatedName));
                                    $dir .= '/' . date('m', strtotime($fCreatedName));
                                    $dir .= '/' . date('d', strtotime($fCreatedName));
                                    $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption($dir . '/' . $selClientId) . '&opt=enc';
                                }
                                if ($borFlipExit == 'Sold') $showSaleSec = 'display: block;';
                                if ($borFlipExit == 'Rented') $showMonthlyRent = 'display: block;';

                                ?>
                                <div class="borrowerExperAlterClass py-4 px-2 <?php echo $cls ?>">
                                    <!-- Sub Section Start -->

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                        <label class="col-md-5  font-weight-bold">Property Type</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <select name="borFlipPropType[]" id="borFlipPropType<?php echo $nop; ?>"
                                                        class="form-control"
                                                        tabindex="<?php echo $tabIndex++; ?>">
                                                    <option value=''> - Select -</option>
                                                    <?php
                                                    $glPropertyTypeExpKeys = [];
                                                    $glPropertyTypeExpKeys = array_keys($glPropertyTypeExp);
                                                    for ($o = 0; $o < count($glPropertyTypeExpKeys); $o++) {
                                                        $propVal = $sOpt = $propKey = '';
                                                        $propKey = trim($glPropertyTypeExpKeys[$o]);
                                                        $sOpt = Arrays::isSelected($borFlipPropType, $propKey);

                                                        $opt = "<option value=\"" . $propKey . "\"  " . $sOpt . '>' . $glPropertyTypeExp[$propKey] . "</option>\n";
                                                        echo $opt;
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else {
                                                echo '<h5>' . Arrays::getArrayValue($borFlipPropType, $glPropertyTypeExp) . '</h5>';
                                            } ?>
                                        </div>
                                        </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                        <label class="col-md-5 font-weight-bold">Purchase date</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                            <span class="input-group-text">
                                                                <i class="fa fa-calendar text-primary"></i>
                                                            </span>
                                                    </div>
                                                    <input class="form-control dateNewClass" type="text" name="borFlipPurchaseDate[]"
                                                           id="borFlipPurchaseDate<?php echo $nop; ?>"
                                                           placeholder="MM/DD/YYYY"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo Dates::formatDateWithRE($borFlipPurchaseDate, 'YMD', 'm/d/Y'); ?>"
                                                           style="" autocomplete="off">
                                                </div>
                                            <?php } else { ?>
                                                <h5><?php echo Dates::formatDateWithRE($borFlipPurchaseDate, 'YMD', 'm/d/Y'); ?></h5><?php } ?>
                                            </div>
                                        </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                        <label class="col-md-5 font-weight-bold">Purchase Price</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borFlipPurchasePrice[]"
                                                       class="form-control"
                                                       id="borFlipPurchasePrice<?php echo $nop; ?>"
                                                       placeholder="0.00"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($borFlipPurchasePrice); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($borFlipPurchasePrice); ?></h5><?php } ?>
                                            </div>
                                         </div>
                                    </div>
                                    
                                    
                                        <?php if (!in_array($PCID, $glFirstRehabLending)) { ?>
                                            <div class="col-md-6">
                                            <div class="form-group row " id="borFlipAmountFinancedDiv<?php echo $nop; ?>">
                                                <label class="col-md-5 font-weight-bold"> Amount
                                                    Financed</label><div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control" type="text"
                                                           placeholder="0.00"
                                                           name="borFlipAmountFinanced[]"
                                                           id="borFlipAmountFinanced<?php echo $nop; ?>"
                                                           onblur="currencyConverter(this, this.value, 0);"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimalZeros($borFlipAmountFinanced); ?>"
                                                           autocomplete="off">
                                                <?php } else { ?>
                                                    <h5><?php echo Currency::formatDollarAmountWithDecimalZeros($borFlipAmountFinanced); ?></h5><?php } ?>
                                            </div>
                                            </div>
                                            </div>
                                        <?php } ?>

                                        <div class="col-md-6">           
                                        <div class=" form-group row ">
                                            <label class="col-md-5 font-weight-bold"> Rehab Budget </label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control" type="text" name="borFlipRehabBudget[]"
                                                       id="borFlipRehabBudget<?php echo $nop; ?>"
                                                       placeholder="0.00"
                                                       onblur="currencyConverter(this, this.value, 0);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimalZeros($borFlipRehabBudget); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?><h5><?php echo $borFlipRehabBudget; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                            </div>
                                        <?php if ($hideThisField) { ?>
                                            <div class="col-md-6">
                                            <div class="  form-group row ">
                                                <label class="col-md-5 font-weight-bold"> Holding Title As</label><div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control" type="text" name="borFlipEntityName[]"
                                                           id="borFlipEntityName<?php echo $nop; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo htmlentities($borFlipEntityName); ?>"
                                                           autocomplete="off">
                                                <?php } else { ?><h5><?php echo $borFlipEntityName; ?></h5><?php } ?>
                                            </div>
                                            </div>
                                            </div>
                                        <?php } ?>
                                        <div class="col-md-6">
                                        <div class=" form-group row ">
                                            <label class="col-md-5 font-weight-bold">
                                                Ownership %</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control" type="text" name="borFlipOwnership[]"
                                                       oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1');"
                                                       id="borFlipOwnership<?php echo $nop; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo $borFlipOwnership; ?>"
                                                       autocomplete="off">
                                            <?php } else { ?><h5><?php echo $borFlipOwnership; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <div class="col-md-6">
                                        <div class=" form-group row "><label class="col-md-5 font-weight-bold">Ownership
                                                Status</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <select name="borFlipOwnershipStatus[]"
                                                        id="borFlipOwnershipStatus<?php echo $nop; ?>"
                                                        onchange="hideAmountFinance(this.value, '<?php echo $nop; ?>','Flip')"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        class="form-control">
                                                    <option value=''> - Select -</option>
                                                    <?php
                                                    for ($i = 0; $i < count($glOwnershipStatus); $i++) {
                                                        $sOpt = '';
                                                        $sOpt = Arrays::isSelected($glOwnershipStatus[$i], $borFlipOwnershipStatus);
                                                        echo "<option value=\"" . $glOwnershipStatus[$i] . "\" " . $sOpt . '>' . $glOwnershipStatus[$i] . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else { ?><h5><?php echo $borFlipOwnershipStatus; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        
                                        <div class=" col-md-6 <?php if (glPCID::PCID_PROD_CV3 == $PCID) {
                                            echo ' d-none ';
                                        } ?>">
                                        
                                        <div class=" form-group row ">
                                            <label class="col-md-5 font-weight-bold">Exit</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <select name="borFlipExit[]" id="borFlipExit<?php echo $nop; ?>"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        class="form-control"
                                                        onchange="showHideExitPro(this.value,'<?php echo $nop; ?>');">
                                                    <option value=''> - Select -</option>
                                                    <?php
                                                    for ($i = 0; $i < count($glExitValues); $i++) {
                                                        $sOpt = '';
                                                        $tEV = $glExitValues[$i];
                                                        $sOpt = Arrays::isSelected($tEV, $borFlipExit);
                                                        echo "<option value=\"" . $glExitValues[$i] . "\" " . $sOpt . '>' . $glExitValues[$i] . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else { ?><h5><?php echo $borborFlipExit; ?></h5><?php } ?>
                                            </div>
                                        </div>
                                        </div>
                                        
                                        
                                        <div class="col-md-6" id="rentalDiv<?php echo $nop; ?>" style="<?php echo $showMonthlyRent; ?>">
                                        <div class=" form-group row ">
                                        <label class=" col-md-5  font-weight-bold">Monthly Rent</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borFlipMonthlyRent[]"
                                                       class="form-control"
                                                       placeholder="0.00"
                                                       id="borFlipMonthlyRent<?php echo $nop; ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($borFlipMonthlyRent); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($borFlipMonthlyRent); ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <div class="col-md-6" id="soldDiv<?php echo $nop; ?>"
                                             style="<?php echo $showSaleSec; ?>">
                                            <div class="form-group row">
                                                <label class="col-md-5 font-weight-bold">Sale
                                                        Price</label><div class="col-md-7">
                                                    <?php if ($allowToEdit) { ?>
                                                        <input type="text" name="borFlipSalePrice[]"
                                                               class="form-control"
                                                               placeholder="0.00"
                                                               id="borFlipSalePrice<?php echo $nop; ?>"
                                                               onblur="currencyConverter(this, this.value);"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               value="<?php echo Currency::formatDollarAmountWithDecimal($borFlipSalePrice); ?>"
                                                               autocomplete="off">
                                                    <?php } else { ?>
                                                        <h5><?php echo Currency::formatDollarAmountWithDecimal($borFlipSalePrice); ?></h5><?php } ?>
                                                </div>
                                            </div>
                                        </div>

                                                <div class=" col-md-6"><div class=" form-group row "><label class="col-md-5 font-weight-bold">Sale
                                                        Date</label><div class=" col-md-7">
                                                    <?php if ($allowToEdit) { ?>
                                                        <input type="text" name="borFlipSaleDate[]"
                                                               class="form-control dateNewClass"
                                                               placeholder="MM/DD/YYYY"
                                                               id="borFlipSaleDate<?php echo $nop; ?>"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               value="<?php echo Dates::formatDateWithRE($borFlipSaleDate, 'YMD', 'm/d/Y'); ?>"
                                                               autocomplete="off">
                                                    <?php } else { ?>
                                                        <h5><?php echo Dates::formatDateWithRE($borFlipSaleDate, 'YMD', 'm/d/Y'); ?></h5><?php } ?>
                                                </div>
                                            </div>
                                        </div>
                                    
                                    
                                        <script>
                                            $(document).ready(function() {
                                                $('#borFlipAddress<?php echo $nop; ?>').on('input', function() {
                                                    address_lookup.InitLegacy($(this));
                                                });
                                            });
                                        </script>
                                            <div class=" col-md-6"><div class=" form-group row "><label class="col-md-5 font-weight-bold">Address</label><div class=" col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control" type="text" name="borFlipAddress[]"
                                                           id="borFlipAddress<?php echo $nop; ?>"
                                                           data-address="borFlipAddress<?php echo $nop; ?>"
                                                           data-city="borFlipCity<?php echo $nop; ?>"
                                                           data-state="borFlipState<?php echo $nop; ?>"
                                                           data-zip="borFlipZip<?php echo $nop; ?>"
                                                           data-unit="borFlipUnit<?php echo $nop; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo htmlentities($borFlipAddress); ?>"
                                                           autocomplete="off"
                                                           placeholder="Address">
                                                <?php } else { ?><h5><?php echo $borFlipAddress; ?></h5><?php } ?>
                                                </div>
                                                </div>
                                            </div>
                                            <div class=" col-md-6"><div class=" form-group row "><label class="col-md-5 font-weight-bold">City</label><div class=" col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control" type="text" name="borFlipCity[]"
                                                           id="borFlipCity<?php echo $nop; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo htmlentities($borFlipCity); ?>"
                                                           autocomplete="off"
                                                           placeholder="City">
                                                <?php } else { ?><h5><?php echo $borFlipCity; ?></h5><?php } ?>
                                             </div>
                                            </div>
                                            </div>
                                            <div class=" col-md-6"><div class=" form-group row "><label class="col-md-5 font-weight-bold">State</label><div class=" col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <select name="borFlipState[]" id="borFlipState<?php echo $nop; ?>"
                                                        tabindex="<?php echo $tabIndex++; ?>" class="form-control">;
                                                    <option value=""> - Select -</option>
                                                    <?php
                                                    for ($s = 0; $s < count($stateArray); $s++) {
                                                        $sOpt = '';
                                                        $sOpt = Arrays::isSelected(trim($stateArray[$s]['stateCode']), $borFlipState);
                                                        echo "<option value=\"" . trim($stateArray[$s]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$s]['stateName']) . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else { ?><h5><?php echo $borFlipState; ?></h5><?php } ?>
                                                </div>
                                            </div>
                                            </div>
                                        <div class=" col-md-6"><div class=" form-group row ">            
                                        <label class="col-md-5 font-weight-bold">Zip</label><div class=" col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control zipCode" type="text" name="borFlipZip[]"
                                                       id="borFlipZip<?php echo $nop; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo htmlentities($borFlipZip); ?>"
                                                       autocomplete="off"
                                                       placeholder="Zip">
                                            <?php } else { ?><h5><?php echo $borFlipZip; ?></h5><?php } ?>
                                        </div>
                                            </div>
                                            </div>
                                        <div class=" col-md-6"><div class=" form-group row "> 
                                        <label class="col-md-5 font-weight-bold">Unit/Suite #</label><div class=" col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borFlipUnit[]"
                                                       id="borFlipUnit<?php echo $nop; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo htmlentities($borFlipUnit); ?>"
                                                       class="form-control"
                                                       autocomplete="off">
                                            <?php } else { ?><h5><?php echo $borFlipUnit; ?></h5><?php } ?>
                                        </div>
                                            </div>
                                            </div>
                                    
                                                
                                        <div class="col-md-6" id="soldDiv1<?php echo $nop; ?>" style="<?php echo $showSaleSec; ?>">
                                            <?php
                                           // echo "<div class=\"clearfix\"></div>";
                                            if (!in_array($PCID, $glFirstRehabLending)) { ?>
                                            <div class="form-group row"> 
                                                <label class="col-md-5 font-weight-bold">Outcome/Profit</label><div class=" col-md-7">
                                                    <?php if ($allowToEdit) { ?>
                                                        <input type="text" name="borOutcomeRE[]"
                                                               class="form-control"
                                                               placeholder="0.00"
                                                               id="borOutcomeRE<?php echo $nop; ?>"
                                                               onblur="currencyConverter(this, this.value);"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               value="<?php echo Currency::formatDollarAmountWithDecimal($borOutcomeRE); ?>"
                                                               autocomplete="off">
                                                    <?php } else { ?>
                                                        <h5><?php echo $borOutcomeRE . $nop; ?></h5><?php } ?>
                                                    </div>
                                            </div>
                                            <?php } ?>
                                        </div>
                                        <div class="col-md-4 mb-2 d-none">
                                            <label class="font-weight-bold">Upload HUD</label>
                                            <input type="file" name="proofOfSale[]" id="proofOfSale<?php echo $nop; ?>"
                                                   class="form-control"
                                                   tabindex="<?php echo $tabIndex++; ?>">
                                            <?php if ($hideThisField) { ?>
                                                <span class="FlipDocs_<?php echo $nop; ?>"><?php if ($uploadDocUrl != '') { ?>
                                                        <a href="<?php echo $uploadDocUrl; ?>"
                                                           target="_blank"><?php echo $dN; ?></a><?php } ?>
                                            </span>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div><!-- Sub Section Start -->
                            <?php } ?>
                        </div>
                    
                </div><!-- List 3 examples of past deals End. -->
            </div>
        </div>


        <div class="row">
            <div class="col-md-12">
                <div class="form-group row  haveBorRehabConstructionExperience_disp <?php echo loanForm::showField('haveBorRehabConstructionExperience'); ?>">
                    <?php echo loanForm::label('haveBorRehabConstructionExperience', 'col-md-6 '); ?>
                    <div class="col-md-3">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold"
                                       for="haveBorRehabConstructionExperienceYes">
                                    <input type="radio" name="haveBorRehabConstructionExperience"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorRehabConstructionExperience', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorRehabConstructionExperienceYes" value="Yes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $haveBorRehabConstructionExperience); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borRehabConstructionExperienceDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorRehabConstructionExperience', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold"
                                       for="haveBorRehabConstructionExperienceNo">
                                    <input type="radio" name="haveBorRehabConstructionExperience"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorRehabConstructionExperience', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorRehabConstructionExperienceNo" value="No"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $haveBorRehabConstructionExperience); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borRehabConstructionExperienceDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorRehabConstructionExperience', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                </label></div>

                        <?php } else { ?><h5><?php echo $haveBorRehabConstructionExperience; ?></h5><?php } ?>
                    </div>
                </div>


                <div class="form-group borRehabConstructionExperienceDiv <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveBorRehabConstructionExperience', 'sArr' => $secArr, 'pv' => $haveBorRehabConstructionExperience, 'av' => 'Yes']); ?>">
                    <div class="row">
                        <div class="col-md-6  borNoOfYearRehabExperience_disp <?php echo loanForm::showField('borNoOfYearRehabExperience'); ?>">
                            <div class="form-group row">
                            <?php echo loanForm::label('borNoOfYearRehabExperience', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfYearRehabExperience', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="borNoOfYearRehabExperience" id="borNoOfYearRehabExperience"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $borNoOfYearRehabExperience; ?>"
                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfYearRehabExperience', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?><h5><?php echo $borNoOfYearRehabExperience; ?></h5><?php } ?>
                            </div>
                            </div>
                        </div>
                        <div class="col-md-6 no-padding borRehabPropCompleted_disp <?php echo loanForm::showField('borRehabPropCompleted'); ?>">
                            <div class="form-group row">
                            <?php echo loanForm::label('borRehabPropCompleted', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borRehabPropCompleted', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="borRehabPropCompleted" id="borRehabPropCompleted"
                                           onchange="validateMinMaxLoanGuidelines();"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $borRehabPropCompleted; ?>"
                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borRehabPropCompleted', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?><h5><?php echo $borRehabPropCompleted; ?></h5><?php } ?>
                            </div>
                            </div>
                        </div>
                        <?php
                        if (isset($glRestrictCustomFields[$PCID])) { //https://www.pivotaltracker.com/story/show/161218497
                            if (in_array('groundPropCompletedLifetime', $glRestrictCustomFields[$PCID])) {
                                ?>
                                <div class="col-md-4  groundPropCompletedLifetime_disp <?php echo loanForm::showField('groundPropCompletedLifetime'); ?>">
                                    <?php echo loanForm::label('groundPropCompletedLifetime', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="number"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'groundPropCompletedLifetime', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="groundPropCompletedLifetime" id="groundPropCompletedLifetime"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $groundPropCompletedLifetime; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'groundPropCompletedLifetime', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?><h5><?php echo $groundPropCompletedLifetime; ?></h5><?php } ?>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                        ?>
                    </div>
                    
                        <div class="form-group row borRCAddress_disp <?php echo loanForm::showField('borRCAddress'); ?>">

                            <div class="col-md-12 m-0 mb-2 px-0" id="examplePastDetails">
                                <label class="bg-secondary  py-2  col-md-12"><b><?php echo BaseHTML::fieldAccess(['fNm' => 'borRCAddress', 'sArr' => $secArr, 'opt' => 'L']); ?></b></label>
                            </div>


                            <?php $gud = 3;
                            for ($nop = 0; $nop < $countOfPastDeals; $nop++) {
                                $borGUPropType = '';
                                $borGUPurchaseDate = '';
                                $borGUPurchasePrice = '';
                                $borGUAmountFinanced = '';
                                $borGURehabBudget = '';
                                $borGUEntityName = '';
                                $borGUOwnership = '';
                                $borGUOwnershipStatus = '';
                                $borGUExit = '';
                                $borGUSalePrice = '';
                                $borGUSaleDate = '';
                                $borGUMonthlyRent = '';
                                $borGUAddress = '';
                                $borGUCity = '';
                                $borGUState = '';
                                $borGUZip = '';
                                $borGUUnit = '';
                                $borOutcomeRC = '';
                                $showGUSaleSec = 'display: none;';
                                $docCategory = 'Proof of sale (HUD)' . $gud;
                                $cls = '';
                                $showGURentalSec = 'display: none;';
                                $uploadDocUrl = '';
                                $dir = '';
                                if ((($nop + 1) % 2) == 0) $cls = 'even';

                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUPropType = $clientGUExpInfo[$nop]['propertyType'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUPurchaseDate = $clientGUExpInfo[$nop]['purchaseDate'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUPurchasePrice = $clientGUExpInfo[$nop]['purchasePrice'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUAmountFinanced = $clientGUExpInfo[$nop]['amountFinanced'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGURehabBudget = $clientGUExpInfo[$nop]['rehabBudget'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUEntityName = $clientGUExpInfo[$nop]['entityName'];

                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUOwnership = $clientGUExpInfo[$nop]['ownership'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUOwnershipStatus = $clientGUExpInfo[$nop]['ownershipStatus'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUExit = $clientGUExpInfo[$nop]['exitValues'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUSalePrice = $clientGUExpInfo[$nop]['salePrice'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUSaleDate = $clientGUExpInfo[$nop]['saleDate'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUMonthlyRent = $clientGUExpInfo[$nop]['monthlyRent'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUAddress = $clientGUExpInfo[$nop]['address'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUCity = $clientGUExpInfo[$nop]['city'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUState = $clientGUExpInfo[$nop]['state'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUZip = $clientGUExpInfo[$nop]['zip'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borGUUnit = $clientGUExpInfo[$nop]['unit'];
                                if (array_key_exists($nop, $clientGUExpInfo)) $borOutcomeRC = $clientGUExpInfo[$nop]['Outcome'];

                                if (array_key_exists($docCategory, $clientDocsArray)) {
                                    $docName = $clientDocsArray[$docCategory]['fileName'];
                                    $dN = $clientDocsArray[$docCategory]['docName'];
                                    $fCreatedName = str_replace('-', '', $registerDate);
                                    $dir = CONST_PATH_CLIENT_DOC;
                                    $dir .= '/' . date('Y', strtotime($fCreatedName));
                                    $dir .= '/' . date('m', strtotime($fCreatedName));
                                    $dir .= '/' . date('d', strtotime($fCreatedName));
                                    $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption($dir . '/' . $selClientId) . '&opt=enc';
                                }
                                $gud++;
                                if ($borGUExit == 'Sold') $showGUSaleSec = 'display: block;';
                                if ($borGUExit == 'Rented') $showGURentalSec = 'display: block;';
                                ?>
                                <div class="borrowerExperAlterClass py-4 px-2 ">
                                    <div class="row <?php echo $cls ?>"> <!-- Sub Section Start -->
                                        <div class="col-md-6"><div class="form-group row">
                                            <label class="col-md-5 font-weight-bold">Property Type</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <select name="borGUPropType[]" id="borGUPropType<?php echo $nop; ?>"
                                                        tabindex="<?php echo $tabIndex++; ?>" class="form-control">
                                                    <option value=''> - Select -</option>
                                                    <?php
                                                    $glPropertyTypeExpKeys = [];
                                                    $glPropertyTypeExpKeys = array_keys($glPropertyTypeExp);
                                                    for ($o = 0; $o < count($glPropertyTypeExpKeys); $o++) {
                                                        $propVal = $sOpt = $propKey = '';
                                                        $propKey = trim($glPropertyTypeExpKeys[$o]);
                                                        $sOpt = Arrays::isSelected($borGUPropType, $propKey);
                                                        $opt = "<option value=\"" . $propKey . "\"  " . $sOpt . '>' . $glPropertyTypeExp[$propKey] . "</option>\n";
                                                        echo $opt;
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else {
                                                echo '<h5>' . Arrays::getArrayValue($borGUPropType, $glPropertyTypeExp) . '</h5>';
                                            } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold"> Purchase
                                                date</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <i class="fa fa-calendar text-primary"></i>
                                                        </span>
                                                    </div>
                                                    <input class="form-control dateNewClass" type="text" name="borGUPurchaseDate[]"
                                                           id="borGUPurchaseDate<?php echo $nop; ?>"
                                                           placeholder="MM/DD/YYYY"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo Dates::formatDateWithRE($borGUPurchaseDate, 'YMD', 'm/d/Y'); ?>"
                                                           autocomplete="off">
                                                </div>
                                            <?php } else { ?>
                                                <h5><?php echo Dates::formatDateWithRE($borGUPurchaseDate, 'YMD', 'm/d/Y'); ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold"> Purchase
                                                Price</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borGUPurchasePrice[]"
                                                       class="form-control"
                                                       placeholder="0.00"
                                                       id="borGUPurchasePrice<?php echo $nop; ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($borGUPurchasePrice); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?><h5><?php echo $borGUPurchasePrice; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <?php if (!in_array($PCID, $glFirstRehabLending)) { ?>
                                            <div class="col-md-6" id="borGUAmountFinancedDiv<?php echo $nop; ?>">
                                                <div class="form-group row">
                                                <label class="col-md-5 font-weight-bold">Amount
                                                    Financed</label><div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control" type="text" name="borGUAmountFinanced[]"
                                                           id="borGUAmountFinanced<?php echo $nop; ?>"
                                                           placeholder="0.00"
                                                           onblur="currencyConverter(this, this.value);"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($borGUAmountFinanced); ?>"
                                                           autocomplete="off">
                                                <?php } else { ?>
                                                    <h5><?php echo Currency::formatDollarAmountWithDecimal($borGUAmountFinanced); ?></h5><?php } ?>
                                            </div>
                                            </div>
                                            </div>
                                        <?php } ?>
                                        <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">Rehab Budget</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control" type="text" name="borGURehabBudget[]"
                                                       placeholder="0.00"
                                                       id="borGURehabBudget<?php echo $nop; ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($borGURehabBudget); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($borGURehabBudget); ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>
                                        <?php if ($hideThisField) { ?>
                                            <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">Holding Title
                                                    As</label><div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control" type="text" name="borGUEntityName[]"
                                                           id="borGUEntityName<?php echo $nop; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo htmlentities($borGUEntityName); ?>"
                                                           autocomplete="off">
                                                <?php } else { ?><h5><?php echo $borGUEntityName; ?></h5><?php } ?>
                                            </div>
                                            </div>
                                            </div>
                                        <?php } ?>

                                        <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">Ownership %</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control" type="number" name="borGUOwnership[]"
                                                       id="borGUOwnership<?php echo $nop; ?>"
                                                       oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1');"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo $borGUOwnership; ?>"
                                                       autocomplete="off">
                                            <?php } else { ?><h5><?php echo $borGUOwnership; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <div class=" col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">Ownership
                                                Status</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <select name="borGUOwnershipStatus[]"
                                                        id="borGUOwnershipStatus<?php echo $nop; ?>"
                                                        onchange="hideAmountFinance(this.value, '<?php echo $nop; ?>','GU')"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        class="form-control">
                                                    <option value=''> - Select -</option>
                                                    <?php
                                                    for ($i = 0; $i < count($glOwnershipStatus); $i++) {
                                                        $sOpt = '';
                                                        $sOpt = Arrays::isSelected($glOwnershipStatus[$i], $borGUOwnershipStatus);
                                                        echo "<option value=\"" . $glOwnershipStatus[$i] . "\" " . $sOpt . '>' . $glOwnershipStatus[$i] . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else { ?><h5><?php echo $borGUOwnershipStatus; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <div class="col-md-6 <?php if (glPCID::PCID_PROD_CV3 == $PCID) {
                                            echo ' d-none ';
                                        } ?>">
                                        <div class="form-group row">
                                            <label class="col-md-5 font-weight-bold">Exit</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <select class="form-control" name="borGUExit[]"
                                                        id="borGUExit<?php echo $nop; ?>"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        onchange="showHideGUExitPro(this.value, '<?php echo $nop; ?>')">
                                                    <option value=''> - Select -</option>
                                                    <?php
                                                    for ($i = 0; $i < count($glExitValues); $i++) {
                                                        $sOpt = '';
                                                        $tEV = $glExitValues[$i];
                                                        $sOpt = Arrays::isSelected($tEV, $borGUExit);
                                                        echo "<option value=\"" . $glExitValues[$i] . "\" " . $sOpt . '>' . $glExitValues[$i] . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else { ?><h5><?php echo $borGUExit; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>


                                        <div class="col-md-6" id="GURentalDiv<?php echo $nop; ?>"
                                             style="<?php echo $showGURentalSec; ?>"><div class="form-group row"><label class="col-md-5 font-weight-bold">Monthly
                                                Rent</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borGUMonthlyRent[]"
                                                       class="form-control"
                                                       placeholder="0.00"
                                                       id="borGUMonthlyRent<?php echo $nop; ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($borGUMonthlyRent); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($borGUMonthlyRent); ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>


                                        <div class="col-md-6" id="GUSaleDiv<?php echo $nop; ?>"
                                             style="<?php echo $showGUSaleSec; ?>">
                                            <div class="form-group row"><label class="col-md-5 font-weight-bold">Sale Price</label><div class="col-md-7">
                                                    <?php if ($allowToEdit) { ?>
                                                        <input type="text" name="borGUSalePrice[]" class="form-control"
                                                               placeholder="0.00"
                                                               id="borGUSalePrice<?php echo $nop; ?>"
                                                               onblur="currencyConverter(this, this.value);"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               value="<?php echo $borGUSalePrice; ?>"
                                                               autocomplete="off">
                                                    <?php } else { ?><h5><?php echo $borGUSalePrice; ?></h5><?php } ?>
                                            </div>
                                            </div>
                                        </div>

                                                <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">Sale
                                                        Date</label><div class="col-md-7">
                                                    <?php if ($allowToEdit) { ?>
                                                        <div class="input-group">
                                                            <div class="input-group-prepend">
                                                                <span class="input-group-text">
                                                                    <i class="fa fa-calendar text-primary"></i>
                                                                </span>
                                                            </div>
                                                            <input type="text" name="borGUSaleDate[]"
                                                                   class="form-control dateNewClass"
                                                                   placeholder="MM/DD/YYYY"
                                                                   id="borGUSaleDate<?php echo $nop; ?>"
                                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                                   value="<?php echo Dates::formatDateWithRE($borGUSaleDate, 'YMD', 'm/d/Y'); ?>"
                                                                   autocomplete="off">
                                                        </div>
                                                    <?php } else { ?>
                                                        <h5><?php echo Dates::formatDateWithRE($borGUSaleDate, 'YMD', 'm/d/Y'); ?></h5><?php } ?>
                                                </div>
                                            </div>
                                        </div>

                                        <script>
                                            $(document).ready(function() {
                                                $('#borGUAddress<?php echo $nop; ?>').on('input', function() {
                                                    address_lookup.InitLegacy($(this));
                                                });
                                            });
                                        </script>
                                        <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">Address</label><div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control"
                                                           type="text"
                                                           name="borGUAddress[]"
                                                           id="borGUAddress<?php echo $nop; ?>"
                                                           data-address="borGUAddress<?php echo $nop; ?>"
                                                           data-city="borGUCity<?php echo $nop; ?>"
                                                           data-state="borGUState<?php echo $nop; ?>"
                                                           data-zip="borGUZip<?php echo $nop; ?>"
                                                           data-unit="borGUUnit<?php echo $nop; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo htmlentities($borGUAddress); ?>"
                                                           autocomplete="off"
                                                           placeholder="Address">
                                                <?php } else { ?><h5><?php echo $borGUAddress; ?></h5><?php } ?>
                                            </div>
                                            </div>
                                        </div>
                                            <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">City</label><div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control" type="text" name="borGUCity[]"
                                                           id="borGUCity<?php echo $nop; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo htmlentities($borGUCity); ?>"
                                                           autocomplete="off"
                                                           placeholder="City">
                                                <?php } else { ?><h5><?php echo $borGUCity; ?></h5><?php } ?>
                                            </div>
                                            </div>
                                            </div>
                                        <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">State</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <select name="borGUState[]" id="borGUState<?php echo $nop; ?>"
                                                        tabindex="<?php echo $tabIndex++; ?>" class="form-control">;
                                                    <option value=""> - Select -</option>
                                                    <?php
                                                    for ($s = 0; $s < count($stateArray); $s++) {
                                                        $sOpt = '';
                                                        $sOpt = Arrays::isSelected(trim($stateArray[$s]['stateCode']), $borGUState);
                                                        echo "<option value=\"" . trim($stateArray[$s]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$s]['stateName']) . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                            <?php } else { ?><h5><?php echo $borGUState; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <div class="col-md-6"><div class="form-group row"><label class="col-md-5 font-weight-bold">Zip</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input class="form-control zipCode" type="text" name="borGUZip[]"
                                                       id="borGUZip<?php echo $nop; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo htmlentities($borGUZip); ?>"
                                                       autocomplete="off"
                                                       placeholder="Zip">
                                            <?php } else { ?><h5><?php echo $borGUZip; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>

                                        <div class="col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Unit/Suite #</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borGUUnit[]" id="borGUUnit<?php echo $nop; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo htmlentities($borGUUnit); ?>"
                                                       class="form-control"
                                                       autocomplete="off">
                                            <?php } else { ?><h5><?php echo $borGUUnit; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>


                                        <div class="col-md-6" id="GUSaleDiv1<?php echo $nop; ?>"
                                             style="<?php echo $showGUSaleSec; ?>">
                                            <?php
                                            //echo "<div class=\"clear\"></div>";
                                            if (!in_array($PCID, $glFirstRehabLending)) {
                                                ?>
                                                <div class="form-group row"><label
                                                            class="font-weight-bold col-md-5">Outcome/Profit</label><div class="col-md-7">
                                                    <?php if ($allowToEdit) { ?>
                                                        <input type="text" name="borOutcomeRC[]"
                                                               class="form-control"
                                                               placeholder="0.00"
                                                               id="borOutcomeRC<?php echo $nop; ?>"
                                                               onblur="currencyConverter(this, this.value);"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               value="<?php echo Currency::formatDollarAmountWithDecimal($borOutcomeRC) ?>"
                                                               autocomplete="off">
                                                    <?php } else { ?>
                                                        <h5><?php echo $borOutcomeRC . $nop; ?></h5><?php } ?>
                                                </div>
                                                </div>
                                            <?php } ?>

                                        </div>

                                        <div class=" col-md-4 mb-2 d-none"><label class="font-weight-bold">Upload
                                                HUD</label>
                                            <input type="file" name="proofOfSale[]" id="proofOfSale<?php echo $nop; ?>"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   class="form-control">
                                            <span class="GUDocs_<?php echo $nop; ?>"><?php if ($uploadDocUrl != '') { ?>
                                                    <a
                                                    href="<?php echo $uploadDocUrl; ?>"
                                                    target="_blank"><?php echo $dN; ?></a><?php } ?></span>
                                        </div>

                                    </div> <!-- Sub Section End -->
                                </div>
                                <?php
                            }
                            ?>
                        </div>
                    
                    <div class="row">
                        <div class=" col-md-12">
                            <div class="form-group  row  areBuilderDeveloper_disp <?php echo loanForm::showField('areBuilderDeveloper'); ?>">
                                <?php echo loanForm::label('areBuilderDeveloper', 'col-md-6'); ?>
                                <div class="col-md-3">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="areBuilderDeveloperYes">
                                                <input type="radio" name="areBuilderDeveloper"
                                                       id="areBuilderDeveloperYes"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'areBuilderDeveloper', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       onclick="showAndHideBuilderDeveloper(this.value, 'areBuilderDeveloperDiv');"
                                                       value="Yes"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $areBuilderDeveloper); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'areBuilderDeveloper', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="areBuilderDeveloperNo">
                                                <input type="radio" name="areBuilderDeveloper"
                                                       id="areBuilderDeveloperNo"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'areBuilderDeveloper', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       onclick="showAndHideBuilderDeveloper(this.value, 'areBuilderDeveloperDiv');"
                                                       value="No"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $areBuilderDeveloper); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'areBuilderDeveloper', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                            </label>
                                        </div>
                                    <?php } else { ?><h5><?php echo $areBuilderDeveloper; ?></h5><?php } ?>
                                </div>
                            </div>
                            <div class="areBuilderDeveloperDiv <?php echo BaseHTML::parentFieldAccess(['fNm' => 'areBuilderDeveloper', 'sArr' => $secArr, 'pv' => $areBuilderDeveloper, 'av' => 'Yes']); ?>">
                                <div class="form-group  row  doYouHireGC_disp <?php echo loanForm::showField('doYouHireGC'); ?>">
                                    <?php echo loanForm::label('doYouHireGC', 'col-md-6 '); ?>
                                    <div class="col-md-3">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="radio-inline">
                                                <label class="radio radio-solid font-weight-bold" for="doYouHireGCYes">
                                                    <input type="radio" name="doYouHireGC" id="doYouHireGCYes"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'doYouHireGC', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           value="Yes"
                                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $doYouHireGC); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'doYouHireGC', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                                </label>
                                                <label class="radio radio-solid font-weight-bold" for="doYouHireGCNo">
                                                    <input type="radio" name="doYouHireGC" id="doYouHireGCNo"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'doYouHireGC', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           value="No"
                                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $doYouHireGC); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'doYouHireGC', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                                </label>
                                            </div>
                                        <?php } else { ?><h5><?php echo $doYouHireGC; ?></h5><?php } ?>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="row">
            <?php /*            Does borrower have experience selling properties without construction (e.g., wholesaling)?*/ ?>
            <!-- Selling Experience. -->
            <div class="col-md-12">
                <div class="form-group row haveBorSellPropertie_disp <?php echo loanForm::showField('haveBorSellPropertie'); ?>">
                    <?php echo loanForm::label('haveBorSellPropertie', 'col-md-6 '); ?>
                    <div class="col-md-3">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="haveBorSellPropertieYes">
                                    <input type="radio" name="haveBorSellPropertie"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorSellPropertie', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorSellPropertieYes" value="Yes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $haveBorSellPropertie); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'haveBorSellPropertieDiv');"><span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="haveBorSellPropertieNo">
                                    <input type="radio" name="haveBorSellPropertie"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorSellPropertie', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorSellPropertieNo" value="No"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $haveBorSellPropertie); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'haveBorSellPropertieDiv');"><span></span>No
                                </label></div>

                        <?php } else { ?><h5><?php echo $haveBorSellPropertie; ?></h5><?php } ?>
                    </div>
                </div>

                <div class="form-group haveBorSellPropertieDiv  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveBorSellPropertie', 'sArr' => $secArr, 'pv' => $haveBorSellPropertie, 'av' => 'Yes']); ?>">

                    <div class="row">

                        <div class="col-md-6 borNoOfProSellExperience_disp <?php echo loanForm::showField('borNoOfProSellExperience'); ?>">
                            <div class="form-group row">
                            <?php echo loanForm::label('borNoOfProSellExperience', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfProSellExperience', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="borNoOfProSellExperience" id="borNoOfProSellExperience"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $borNoOfProSellExperience; ?>"
                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfProSellExperience', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?><h5><?php echo $borNoOfProSellExperience; ?></h5><?php } ?>
                            </div>
                            </div>
                        </div>
                        <div class="col-md-6 borNoOfProSellCompleted_disp <?php echo loanForm::showField('borNoOfProSellCompleted'); ?>">
                            <div class="form-group row">
                            <?php echo loanForm::label('borNoOfProSellCompleted', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfProSellCompleted', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           name="borNoOfProSellCompleted" id="borNoOfProSellCompleted"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $borNoOfProSellCompleted; ?>"
                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfProSellCompleted', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?><h5><?php echo $borNoOfProSellCompleted; ?></h5><?php } ?>
                            </div>
                            </div>
                        </div>
                        <?php
                        if (isset($glRestrictCustomFields[$PCID])) { //https://www.pivotaltracker.com/story/show/161218497
                            if (in_array('sellPropCompletedLifetime', $glRestrictCustomFields[$PCID])) {
                                ?>
                                <div class="col-md-4 no-padding sellPropCompletedLifetime_disp <?php echo loanForm::showField('sellPropCompletedLifetime'); ?>">
                                    <?php echo loanForm::label('sellPropCompletedLifetime', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'sellPropCompletedLifetime', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="sellPropCompletedLifetime" id="sellPropCompletedLifetime"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $sellPropCompletedLifetime; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'sellPropCompletedLifetime', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?><h5><?php echo $sellPropCompletedLifetime; ?></h5><?php } ?>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                        ?>
                    </div>

                    <div class="form-group row borPast3DealForSell_disp <?php echo loanForm::showField('borPast3DealForSell'); ?>">


                        <div class="col-md-12 m-0 mb-2 px-0" id="examplePastDetails2">
                            <label class="bg-secondary  py-2  col-md-12"><b><?php echo BaseHTML::fieldAccess(['fNm' => 'borPast3DealForSell', 'sArr' => $secArr, 'opt' => 'L']); ?></b></label>
                        </div>


                        <?php
                        $docNm = 6;
                        for ($nop = 0; $nop < $countOfPastDeals; $nop++) {
                            $borSellPropType = '';
                            $borSellPurchaseDate = '';
                            $borSellPurchasePrice = '';
                            $borSellAmountFinanced = '';
                            $borSellRehabBudget = '';
                            $borSellEntityName = '';
                            $borSellOwnership = '';
                            $borSellOwnershipStatus = '';
                            $borSellExit = '';
                            $borSellSalePrice = '';
                            $borSellSaleDate = '';
                            $borSellMonthlyRent = '';
                            $borSellAddress = '';
                            $borSellCity = '';
                            $borSellState = '';
                            $borSellZip = '';
                            $borSellUnit = '';
                            $borSellOutcomeRE = '';
                            $showSellSaleSec = 'display: none;';
                            $docCategory = 'Proof of sale (HUD)' . $docNm;
                            $showSellMonthlyRent = 'display: none;';
                            $uploadDocUrl = '';
                            $cls = '';
                            $dir = '';
                            if ((($nop + 1) % 2) == 0) $cls = 'even';

                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellPropType = $clientSellExpInfo[$nop]['propertyType'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellPurchaseDate = $clientSellExpInfo[$nop]['purchaseDate'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellPurchasePrice = $clientSellExpInfo[$nop]['purchasePrice'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellAmountFinanced = $clientSellExpInfo[$nop]['amountFinanced'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellRehabBudget = $clientSellExpInfo[$nop]['rehabBudget'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellEntityName = $clientSellExpInfo[$nop]['entityName'];

                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellOwnership = $clientSellExpInfo[$nop]['ownership'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellOwnershipStatus = $clientSellExpInfo[$nop]['ownershipStatus'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellExit = $clientSellExpInfo[$nop]['exitValues'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellSalePrice = $clientSellExpInfo[$nop]['salePrice'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellSaleDate = $clientSellExpInfo[$nop]['saleDate'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellMonthlyRent = $clientSellExpInfo[$nop]['monthlyRent'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellAddress = $clientSellExpInfo[$nop]['address'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellCity = $clientSellExpInfo[$nop]['city'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellState = $clientSellExpInfo[$nop]['state'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellZip = $clientSellExpInfo[$nop]['zip'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellUnit = $clientSellExpInfo[$nop]['unit'];
                            if (array_key_exists($nop, $clientSellExpInfo)) $borSellOutcomeRE = $clientSellExpInfo[$nop]['Outcome'];

                            if (array_key_exists($docCategory, $clientDocsArray)) {
                                $docName = $clientDocsArray[$docCategory]['fileName'];
                                $dN = $clientDocsArray[$docCategory]['docName'];
                                $fCreatedName = str_replace('-', '', $registerDate);
                                $dir = CONST_PATH_CLIENT_DOC;
                                $dir .= '/' . date('Y', strtotime($fCreatedName));
                                $dir .= '/' . date('m', strtotime($fCreatedName));
                                $dir .= '/' . date('d', strtotime($fCreatedName));
                                $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption($dir . '/' . $selClientId) . '&opt=enc';
                            }
                            if ($borSellExit == 'Sold') $showSellSaleSec = 'display: block;';
                            if ($borSellExit == 'Rented') $showSellMonthlyRent = 'display: block;';
                            $docNm++;

                            ?>
                            <div class="borrowerExperAlterClass py-4 px-2 ">
                                <div class="row <?php echo $cls ?>"> <!-- Clone Section Start -->

                                    <div class=" col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Property Type</label><div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <select name="borSellPropType[]" id="borSellPropType<?php echo $nop; ?>"
                                                    class="form-control"
                                                    tabindex="<?php echo $tabIndex++; ?>">
                                                <option value=''> - Select -</option>
                                                <?php
                                                $glPropertyTypeExpKeys = [];
                                                $glPropertyTypeExpKeys = array_keys($glPropertyTypeExp);
                                                for ($o = 0; $o < count($glPropertyTypeExpKeys); $o++) {
                                                    $propVal = $sOpt = $propKey = '';
                                                    $propKey = trim($glPropertyTypeExpKeys[$o]);
                                                    $sOpt = Arrays::isSelected($borSellPropType, $propKey);

                                                    $opt = "<option value=\"" . $propKey . "\"  " . $sOpt . '>' . $glPropertyTypeExp[$propKey] . "</option>\n";
                                                    echo $opt;
                                                }
                                                ?>
                                            </select>
                                        <?php } else {
                                            echo '<h5>' . Arrays::getArrayValue($borSellPropType, $glPropertyTypeExp) . '</h5>';
                                        } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <div class="col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Purchase date</label><div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                                </div>
                                                <input type="text" name="borSellPurchaseDate[]" class="form-control dateNewClass"
                                                       id="borSellPurchaseDate<?php echo $nop; ?>"
                                                       placeholder="MM/DD/YYYY"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Dates::formatDateWithRE($borSellPurchaseDate, 'YMD', 'm/d/Y'); ?>"
                                                       style="" autocomplete="off">
                                            </div>
                                        <?php } else { ?>
                                            <h5><?php echo Dates::formatDateWithRE($borSellPurchaseDate, 'YMD', 'm/d/Y'); ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <div class="col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Purchase
                                            Price</label><div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text" name="borSellPurchasePrice[]"
                                                   class="form-control"
                                                   placeholder="0.00"
                                                   id="borSellPurchasePrice<?php echo $nop; ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($borSellPurchasePrice); ?>"
                                                   autocomplete="off">
                                        <?php } else { ?>
                                            <h5><?php echo Currency::formatDollarAmountWithDecimal($borSellPurchasePrice); ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    
                                    <?php if (!in_array($PCID, $glFirstRehabLending)) { ?>
                                        <div class="col-md-6"
                                             id="borSellAmountFinancedDiv<?php echo $nop; ?>">
                                            <div class="form-group row"><label class="font-weight-bold col-md-5">Amount
                                                Financed</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borSellAmountFinanced[]"
                                                       placeholder="0.00"
                                                       class="form-control"
                                                       id="borSellAmountFinanced<?php echo $nop; ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($borSellAmountFinanced); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($borSellAmountFinanced); ?></h5><?php } ?>
                                        </div>
                                        </div>
                                    </div>
                                    <?php } ?>

                                    <div class=" col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Rehab
                                            Budget</label><div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text" name="borSellRehabBudget[]"
                                                   class="form-control"
                                                   placeholder="0.00"
                                                   id="borSellRehabBudget<?php echo $nop; ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($borSellRehabBudget); ?>"
                                                   autocomplete="off">
                                        <?php } else { ?><h5><?php echo $borSellRehabBudget; ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <?php if ($hideThisField) { ?>
                                        <div class=" col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Holding Title
                                                As</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borSellEntityName[]"
                                                       id="borSellEntityName<?php echo $nop; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       class="form-control"
                                                       value="<?php echo htmlentities($borSellEntityName); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?><h5><?php echo $borSellEntityName; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                    </div>
                                    <?php } ?>
                                    <div class=" col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Ownership %</label><div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text" name="borSellOwnership[]"
                                                   id="borSellOwnership<?php echo $nop; ?>"
                                                   oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1');"
                                                   class="form-control"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $borSellOwnership; ?>"
                                                   autocomplete="off">
                                        <?php } else { ?><h5><?php echo $borSellOwnership; ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <div class=" col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Ownership
                                            Status</label><div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <select name="borSellOwnershipStatus[]"
                                                    id="borSellOwnershipStatus<?php echo $nop; ?>"
                                                    onchange="hideAmountFinance(this.value, '<?php echo $nop; ?>','Sell')"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    class="form-control">
                                                <option value=''> - Select -</option>
                                                <?php
                                                for ($i = 0; $i < count($glOwnershipStatus); $i++) {
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelected($glOwnershipStatus[$i], $borSellOwnershipStatus);
                                                    echo "<option value=\"" . $glOwnershipStatus[$i] . "\" " . $sOpt . '>' . $glOwnershipStatus[$i] . '</option>';
                                                }
                                                ?>
                                            </select>
                                        <?php } else { ?><h5><?php echo $borSellOwnershipStatus; ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <div class=" col-md-6 <?php if (glPCID::PCID_PROD_CV3 == $PCID) {
                                        echo ' d-none ';
                                    } ?>"><div class="form-group row">
                                        <label class="font-weight-bold col-md-5">Exit</label><div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <select name="borSellExit[]" id="borSellExit<?php echo $nop; ?>"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    class="form-control"
                                                    onchange="showHideSellExitPro(this.value, '<?php echo $nop; ?>');">
                                                <option value=''> - Select -</option>
                                                <?php
                                                for ($i = 0; $i < count($glExitValues); $i++) {
                                                    $sOpt = '';
                                                    $tEV = $glExitValues[$i];
                                                    $sOpt = Arrays::isSelected($tEV, $borSellExit);
                                                    echo "<option value=\"" . $glExitValues[$i] . "\" " . $sOpt . '>' . $glExitValues[$i] . '</option>';
                                                }
                                                ?>
                                            </select>
                                        <?php } else { ?><h5><?php echo $borborSellExit; ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <div class="col-md-6" id="sellRentalDiv<?php echo $nop; ?>"
                                         style="<?php echo $showSellMonthlyRent; ?>">
                                        <div class="form-group row"><label class="font-weight-bold col-md-5">Monthly
                                                Rent</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borSellMonthlyRent[]"
                                                       class="form-control"
                                                       placeholder="0.00"
                                                       id="borSellMonthlyRent<?php echo $nop; ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($borSellMonthlyRent); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($borSellMonthlyRent); ?></h5><?php } ?>
                                        </div>
                                    </div>
                                    </div>
                                    

                                    <div class="col-md-6" id="sellSaleDiv<?php echo $nop; ?>"
                                         style="<?php echo $showSellSaleSec; ?>">
                                        <div class="form-group row"><label class="font-weight-bold col-md-5">Sale
                                                Price</label>
                                            <div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borSellSalePrice[]"
                                                       id="borSellSalePrice<?php echo $nop; ?>"
                                                       class="form-control"
                                                       placeholder="0.00"
                                                       onblur="currencyConverter(this, this.value);"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($borSellSalePrice); ?>"
                                                       autocomplete="off">
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($borSellSalePrice); ?></h5><?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                        <div class=" col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Sale
                                                Date</label><div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <i class="fa fa-calendar text-primary"></i>
                                                        </span>
                                                    </div>
                                                    <input type="text" name="borSellSaleDate[]"
                                                           id="borSellSaleDate<?php echo $nop; ?>"
                                                           class="form-control dateNewClass"
                                                           placeholder="MM/DD/YYYY"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo Dates::formatDateWithRE($borSellSaleDate, 'YMD', 'm/d/Y'); ?>"
                                                           autocomplete="off">
                                                </div>
                                            <?php } else { ?>
                                                <h5><?php echo Dates::formatDateWithRE($borSellSaleDate, 'YMD', 'm/d/Y'); ?></h5><?php } ?>
                                        </div>
                                    </div>
                                    </div>

                                    <?php if ($hideThisField) { ?>
                                        <div class="col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Address</label>
                                        <div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <script>
                                                    $(document).ready(function() {
                                                        $('#borSellAddress<?php echo $nop; ?>').on('input', function() {
                                                            address_lookup.InitLegacy($(this));
                                                        });
                                                    });
                                                </script>
                                                <input type="text" name="borSellAddress[]"
                                                       id="borSellAddress<?php echo $nop; ?>"
                                                       data-address="borSellAddress<?php echo $nop; ?>"
                                                       data-city="borSellCity<?php echo $nop; ?>"
                                                       data-state="borSellState<?php echo $nop; ?>"
                                                       data-zip="borSellZip<?php echo $nop; ?>"
                                                       data-unit="borSellUnit<?php echo $nop; ?>"
                                                       class="form-control"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo htmlentities($borSellAddress); ?>"
                                                       autocomplete="off"
                                                       placeholder="Address">
                                            <?php } else { ?><h5><?php echo $borSellAddress; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>
                                    <?php } ?>

                                    <?php if ($hideThisField) { ?>
                                        <div class="col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">City</label>
                                        <div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="borSellCity[]"
                                                       id="borSellCity<?php echo $nop; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       class="form-control"
                                                       value="<?php echo htmlentities($borSellCity); ?>"
                                                       autocomplete="off"
                                                       placeholder="City">
                                            <?php } else { ?><h5><?php echo $borSellCity; ?></h5><?php } ?>
                                        </div>
                                        </div>
                                        </div>
                                    <?php } ?>

                                    <div class="col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">State</label>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <select name="borSellState[]" id="borSellState<?php echo $nop; ?>"
                                                    tabindex="<?php echo $tabIndex++; ?>" class="form-control">;
                                                <option value=""> - Select -</option>
                                                <?php
                                                for ($s = 0; $s < count($stateArray); $s++) {
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelected(trim($stateArray[$s]['stateCode']), $borSellState);
                                                    echo "<option value=\"" . trim($stateArray[$s]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$s]['stateName']) . '</option>';
                                                }
                                                ?>
                                            </select>
                                        <?php } else { ?><h5><?php echo $borSellState; ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <div class="col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Zip</label>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text" name="borSellZip[]" id="borSellZip<?php echo $nop; ?>"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo htmlentities($borSellZip); ?>"
                                                   class="form-control zipCode"
                                                   autocomplete="off"
                                                   placeholder="Zip">
                                        <?php } else { ?><h5><?php echo $borSellZip; ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <div class="col-md-6"><div class="form-group row"><label class="font-weight-bold col-md-5">Unit/Suite #</label>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text" name="borSellUnit[]" id="borSellUnit<?php echo $nop; ?>"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo htmlentities($borSellUnit); ?>"
                                                   class="form-control"
                                                   autocomplete="off">
                                        <?php } else { ?><h5><?php echo $borSellUnit; ?></h5><?php } ?>
                                    </div>
                                    </div>
                                    </div>

                                    <div class="col-md-6" id="sellSaleDiv1<?php echo $nop; ?>"
                                         style="<?php echo $showSellSaleSec; ?>">
                                        <?php
                                       // echo "<div class=\"clear\"></div>";
                                        if (!in_array($PCID, $glFirstRehabLending)) {
                                            ?>
                                            <div class="form-group row"><label
                                                        class="font-weight-bold col-md-5">Outcome/Profit</label><div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input type="text" name="borSellOutcomeRE[]"
                                                           placeholder="0.00"
                                                           class="form-control"
                                                           id="borSellOutcomeRE<?php echo $nop; ?>"
                                                           onblur="currencyConverter(this, this.value);"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($borSellOutcomeRE); ?>"
                                                           autocomplete="off">
                                                <?php } else { ?>
                                                    <h5><?php echo $borSellOutcomeRE . $nop; ?></h5><?php } ?>
                                            </div>
                                            </div>
                                        <?php } ?>
                                    </div>

                                    <div class=" col-md-4 d-none">Upload HUD
                                        <input type="file" name="proofOfSale[]" id="proofOfSale<?php echo $nop; ?>"
                                               class="form-control" tabindex="<?php echo $tabIndex++; ?>">
                                        <span class="SellDocs_<?php echo $nop; ?>"><?php if ($uploadDocUrl != '') { ?><a
                                                href="<?php echo $uploadDocUrl; ?>"
                                                target="_blank"><?php echo $dN; ?></a><?php } ?></span>
                                    </div>

                                </div> <!-- Clone Section End -->
                            </div>
                        <?php } ?>
                    </div>
                </div>
                <!-- Sell Experience End -->
            </div>
        </div>


        <div class="row">
            <div class="col-md-12">
                <div class="form-group row haveBorProjectCurrentlyInProgress_disp <?php echo loanForm::showField('haveBorProjectCurrentlyInProgress'); ?>">
                    <?php echo loanForm::label('haveBorProjectCurrentlyInProgress', 'col-md-6 '); ?>
                    <div class="col-md-3 ">
                        <?php if ($allowToEdit) { ?>

                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold"
                                       for="haveBorProjectCurrentlyInProgressYes">
                                    <input type="radio" name="haveBorProjectCurrentlyInProgress"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorProjectCurrentlyInProgress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorProjectCurrentlyInProgressYes" value="Yes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $haveBorProjectCurrentlyInProgress); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borProjectsCurrentlyProgressDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorProjectCurrentlyInProgress', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                </label>

                                <label class="radio radio-solid font-weight-bold"
                                       for="haveBorProjectCurrentlyInProgressNo">
                                    <input type="radio" name="haveBorProjectCurrentlyInProgress"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorProjectCurrentlyInProgress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorProjectCurrentlyInProgressNo" value="No"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $haveBorProjectCurrentlyInProgress); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borProjectsCurrentlyProgressDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorProjectCurrentlyInProgress', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                </label></div>
                        <?php } else { ?><h5><?php echo $haveBorProjectCurrentlyInProgress; ?></h5><?php } ?>
                    </div>
                </div>
                

                <div class="form-group borProjectsCurrentlyProgressDiv  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveBorProjectCurrentlyInProgress', 'sArr' => $secArr, 'pv' => $haveBorProjectCurrentlyInProgress, 'av' => 'Yes']); ?>">
                    <div class="row">
                    <div class="col-md-6 borProjectsCurrentlyProgressDiv borNoOfProjectCurrently_disp ">
                        <div class="form-group row">
                            <?php echo loanForm::label('borNoOfProjectCurrently', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number" name="borNoOfProjectCurrently" id="borNoOfProjectCurrently"
                                           class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfProjectCurrently', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $borNoOfProjectCurrently; ?>"
                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfProjectCurrently', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?>
                                    <h5><?php echo $borNoOfProjectCurrently; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group row haveBorOwnInvestmentProperties_disp <?php echo loanForm::showField('haveBorOwnInvestmentProperties'); ?>">
                    <?php echo loanForm::label('haveBorOwnInvestmentProperties', 'col-md-6 '); ?>
                    <div class="col-md-3">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold"
                                       for="haveBorOwnInvestmentPropertiesYes">
                                    <input type="radio" name="haveBorOwnInvestmentProperties"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorOwnInvestmentProperties', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorOwnInvestmentPropertiesYes" value="Yes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $haveBorOwnInvestmentProperties); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borOwnInvestmentPropertiesDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorOwnInvestmentProperties', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes</label>

                                <label class="radio radio-solid font-weight-bold"
                                       for="haveBorOwnInvestmentPropertiesNo">
                                    <input type="radio" name="haveBorOwnInvestmentProperties"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorOwnInvestmentProperties', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorOwnInvestmentPropertiesNo" value="No"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $haveBorOwnInvestmentProperties); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borOwnInvestmentPropertiesDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorOwnInvestmentProperties', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                </label></div>
                        <?php } else { ?><h5><?php echo $haveBorOwnInvestmentProperties; ?></h5><?php } ?>
                    </div>
                </div>

                <div class="form-group borOwnInvestmentPropertiesDiv  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveBorOwnInvestmentProperties', 'sArr' => $secArr, 'pv' => $haveBorOwnInvestmentProperties, 'av' => 'Yes']); ?>">
                    <div class="row">
                    <div class="col-md-6 borOwnInvestmentPropertiesDiv borNoOfOwnProp_disp ">
                        <div class="form-group row">
                            <?php echo loanForm::label('borNoOfOwnProp', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="number" name="borNoOfOwnProp" id="borNoOfOwnProp"
                                           class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfOwnProp', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $borNoOfOwnProp; ?>"
                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfOwnProp', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <?php } else { ?><h5><?php echo $borNoOfOwnProp; ?></h5><?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-md-12">
                <div class="form-group row  areBorMemberOfInvestmentClub_disp <?php echo loanForm::showField('areBorMemberOfInvestmentClub'); ?>">
                    <?php echo loanForm::label('areBorMemberOfInvestmentClub', 'col-md-6 '); ?>
                    <div class="col-md-3 ">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="areBorMemberOfInvestmentClubYes">
                                    <input type="radio" name="areBorMemberOfInvestmentClub"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'areBorMemberOfInvestmentClub', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="areBorMemberOfInvestmentClubYes" value="Yes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $areBorMemberOfInvestmentClub); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borMemberOfInvestmentClubDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'areBorMemberOfInvestmentClub', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="areBorMemberOfInvestmentClubNo">
                                    <input type="radio" name="areBorMemberOfInvestmentClub"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'areBorMemberOfInvestmentClub', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="areBorMemberOfInvestmentClubNo" value="No"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $areBorMemberOfInvestmentClub); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borMemberOfInvestmentClubDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'areBorMemberOfInvestmentClub', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No

                                </label></div>
                        <?php } else { ?><h5><?php echo $areBorMemberOfInvestmentClub; ?></h5><?php } ?>
                    </div>
                </div>
                <?php if ($hideThisField) { ?>
                    <div class="form-group row  borMemberOfInvestmentClubDiv  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'areBorMemberOfInvestmentClub', 'sArr' => $secArr, 'pv' => $areBorMemberOfInvestmentClub, 'av' => 'Yes']); ?>">
                        <div class="form-group col-md-12  borMemberOfInvestmentClubDiv borClubName_disp ">
                            <div class="row">
                                <?php echo loanForm::label('borClubName', 'col-md-6 '); ?>
                                <div class="col-md-2 no-padding">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text" name="borClubName" id="borClubName"
                                               class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'borClubName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               tabindex="<?php echo $tabIndex++; ?>" value="<?php echo htmlentities($borClubName); ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borClubName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?><h5><?php echo htmlentities($borClubName); ?></h5><?php } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group row  haveBorProfLicences_disp <?php echo loanForm::showField('haveBorProfLicences'); ?>">
                    <?php echo loanForm::label('haveBorProfLicences', 'col-md-6 '); ?>
                    <div class="col-md-3 no-padding">
                        <?php if ($allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="haveBorProfLicencesYes">
                                    <input type="radio" name="haveBorProfLicences"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorProfLicences', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorProfLicencesYes" value="Yes"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $haveBorProfLicences); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borHaveProfLicencesDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorProfLicences', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes</label>
                                <label class="radio radio-solid font-weight-bold" for="haveBorProfLicencesNo">
                                    <input type="radio" name="haveBorProfLicences"
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorProfLicences', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           id="haveBorProfLicencesNo" value="No"
                                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $haveBorProfLicences); ?>
                                           onclick="showAndHideborrowerUnderExperience(this.value, 'borHaveProfLicencesDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveBorProfLicences', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                </label></div>
                        <?php } else { ?><h5><?php echo $haveBorProfLicences; ?></h5><?php } ?>
                    </div>
                </div>

                <div class="form-group borHaveProfLicencesDiv <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveBorProfLicences', 'sArr' => $secArr, 'pv' => $haveBorProfLicences, 'av' => 'Yes']); ?>">
                    <div class="row">            
                    <div class="col-md-6 borHaveProfLicencesDiv borProfLicence_disp ">
                        <div class="form-group row">
                        <?php echo loanForm::label('borProfLicence', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <select name="borProfLicence" id="borProfLicence"
                                        class="form-control input-sm <?php echo loanForm::showField('borProfLicence'); ?>"
                                        tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'borProfLicence', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <option value=''> - Select -</option>
                                    <?php
                                    for ($p = 0; $p < count($glProfLicences); $p++) {
                                        $sOpt = '';
                                        $tPE = $glProfLicences[$p];
                                        $sOpt = Arrays::isSelected($tPE, $borProfLicence);
                                        echo "<option class='nonBusinessFundingLicenceClass' value=\"" . $glProfLicences[$p] . "\" " . $sOpt . '>' . $glProfLicences[$p] . '</option>';
                                    }
                                    if (in_array('loc', array_column($fileModuleInfo, 'moduleCode')) || !$LMRId) {
                                        for ($p = 0; $p < count($glProfLicencesBusinessFunding); $p++) {
                                            $sOpt = '';
                                            $tPE = $glProfLicencesBusinessFunding[$p];
                                            $sOpt = Arrays::isSelected($tPE, $borProfLicence);
                                            echo "<option class='businessFundingLicenceClass' value=\"" . $glProfLicencesBusinessFunding[$p] . "\" " . $sOpt . '>' . $glProfLicencesBusinessFunding[$p] . '</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            <?php } else { ?><h5><?php echo $borProfLicence; ?></h5><?php } ?>
                        </div>
                        </div>
                    </div>

                    <div class="col-md-6  borLicenseNo_disp <?php echo loanForm::showField('borLicenseNo'); ?>">
                        <div class="form-group row">
                        <?php echo loanForm::label('borLicenseNo', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="borLicenseNo" id="borLicenseNo"
                                       class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'borLicenseNo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo htmlentities($borLicenseNo); ?>"
                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borLicenseNo', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <?php } else { ?><h5><?php echo $borLicenseNo; ?></h5><?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 fullTimeRealEstateInvestor_disp <?php echo loanForm::showField('fullTimeRealEstateInvestor'); ?>">
                <div class="form-group row">
                <?php echo loanForm::label('fullTimeRealEstateInvestor', 'col-md-5'); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold" for="fullTimeRealEstateInvestorYes">
                                <input type="radio" name="fullTimeRealEstateInvestor"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'fullTimeRealEstateInvestor', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       id="fullTimeRealEstateInvestorYes" value="Yes"
                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $fullTimeRealEstateInvestor); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'fullTimeRealEstateInvestor', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="fullTimeRealEstateInvestorNo">
                                <input type="radio" name="fullTimeRealEstateInvestor"
                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'fullTimeRealEstateInvestor', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       id="fullTimeRealEstateInvestorNo" value="No"
                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $fullTimeRealEstateInvestor); ?>
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'fullTimeRealEstateInvestor', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                            </label></div>
                    <?php } else { ?><h5><?php echo $fullTimeRealEstateInvestor; ?></h5><?php } ?>
                </div>
                </div>
            </div>

            <div class="col-md-6 liquidAssets_disp <?php echo loanForm::showField('liquidAssets'); ?>">
                <div class="form-group row">
                <?php echo loanForm::label('liquidAssets', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="liquidAssets" id="liquidAssets"
                               class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'liquidAssets', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($liquidAssets); ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'liquidAssets', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($liquidAssets); ?></h5><?php } ?>
                </div>
                </div>
            </div>


            <div class=" col-md-6  borPrimaryInvestmentStrategy_disp <?php echo loanForm::showField('borPrimaryInvestmentStrategy'); ?>">
                <div class="form-group row">
                <?php echo loanForm::label('borPrimaryInvestmentStrategy', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="hidden" name="borPrimaryInvestmentStrategyHidden" id="borPrimaryInvestmentStrategyHidden"
                               value="<?php echo implode(',', $borPriInvesStrategyArray); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'borPrimaryInvestmentStrategy', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                        <select data-placeholder=" - Select - " name="borPrimaryInvestmentStrategy[]"
                                id="borPrimaryInvestmentStrategy" tabindex="<?php echo $tabIndex++; ?>"
                                class="chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'borPrimaryInvestmentStrategy', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                multiple=""
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'borPrimaryInvestmentStrategy', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <?php
                            $primaryInvestmentStrategyKeyArray = [];
                            $primaryInvestmentStrategyKeyArray = array_keys($glPrimaryInvestmentStrategyArray);
                            for ($se = 0; $se < count($primaryInvestmentStrategyKeyArray); $se++) {
                                $sOpt = '';
                                $borPrimaryInvestmentStrategy = '';
                                $borPrimaryInvestmentStrategy = trim($primaryInvestmentStrategyKeyArray[$se]);
                                if (in_array($borPrimaryInvestmentStrategy, $borPriInvesStrategyArray)) $sOpt = 'selected';
                                echo "<option value=\"" . trim($borPrimaryInvestmentStrategy) . "\" " . $sOpt . '>' . $glPrimaryInvestmentStrategyArray[$borPrimaryInvestmentStrategy] . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $glPrimaryInvestmentStrategyArray[$borPrimaryInvestmentStrategy]; ?></h5><?php } ?>
                </div>
                </div>
            </div>

            <div class="col-md-6 overallRealEstateInvesExp_disp <?php echo loanForm::showField('overallRealEstateInvesExp'); ?>">
                <div class="form-group row">
                <?php echo loanForm::label('overallRealEstateInvesExp', 'col-md-5 '); ?>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <textarea
                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'overallRealEstateInvesExp', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                name="overallRealEstateInvesExp" id="overallRealEstateInvesExp"
                                tabindex="<?php echo $tabIndex++; ?>"
                                placeholder="Explain about Your Overall Real Estate Investment Experience" <?php echo BaseHTML::fieldAccess(['fNm' => 'overallRealEstateInvesExp', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $overallRealEstateInvesExp ?></textarea>
                    <?php } else { ?><h5><?php echo $overallRealEstateInvesExp; ?></h5><?php } ?>
                </div>
                </div>
            </div>


            <div id="borPriInvesStrategyDiv"
                 class="form-group row col-md-6 no-padding borPrimaryInvestmentStrategyExplain_disp <?php echo loanForm::showField('borPrimaryInvestmentStrategyExplain'); ?>">
                <?php echo loanForm::label('borPrimaryInvestmentStrategyExplain', 'col-md-6 '); ?>
                <div class="col-md-6 no-padding">
                    <?php if ($allowToEdit) { ?>
                        <textarea
                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borPrimaryInvestmentStrategyExplain', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                name="borPrimaryInvestmentStrategyExplain" id="borPrimaryInvestmentStrategyExplain"
                                tabindex="<?php echo $tabIndex++; ?>"
                                placeholder="Explain about Primary investment strategy" <?php echo BaseHTML::fieldAccess(['fNm' => 'borPrimaryInvestmentStrategyExplain', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $borPrimaryInvestmentStrategyExplain ?></textarea>
                    <?php } else { ?><h5><?php echo $borPrimaryInvestmentStrategyExplain; ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6  <?php echo loanForm::showField('trackRecord'); ?>">
                <?php echo loanForm::label('trackRecord', 'col-md-6 '); ?>
                <div class="col-md-6 no-padding">
                    <?php if ($allowToEdit) {
                        echo loanForm::select(
                            'trackRecord',
                            LMRequest::$allowToEdit,
                            1,
                            LMRequest::$LMRId ? LMRequest::File()->gettblFileHMLOExperience_by_fileID()->trackRecord : $trackRecord,
                            tblBorrowerExperienceTrackRecord::options(),
                            '',
                            ' chzn-select ',
                            ' ',
                            'Please Select '.loanForm::getFieldLabel('trackRecord')
                        );
                        } else { ?>
                        <h5><?php echo tblBorrowerExperienceTrackRecord::options()[LMRequest::$LMRId ? LMRequest::File()->gettblFileHMLOExperience_by_fileID()->trackRecord : $trackRecord]; ?></h5><?php } ?>
                </div>
            </div>

            <?php echo CustomField::RenderForTabSection(
                PageVariables::$PCID,
                tblFile::class,
                LMRequest::$LMRId,
                'BE',
                $fileTab,
                $activeTab,
                LMRequest::myFileInfo()->getFileTypes(),
                LMRequest::myFileInfo()->getLoanPrograms()
            ); ?>

        </div>
    </div>
</div>
<!-- Experience section End -->


<!-- Typical Transactions section Start -->
<?php
$secArr = BaseHTML::sectionAccess2(['sId' => 'TT', 'opt' => $fileTab]);
loanForm::pushSectionID('TT');

?>
<div class="card card-custom  HMLOLoanInfoSections TTCard TT <?php if (count($secArr) <= 0) {
    echo 'secHide';
} ?>"
     style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('TT'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('TT')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('TT'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">

            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>

            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="TTCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body TTCard_body">
        <div class="row">
            <div class="form-group row col-md-6 amountOfFinancing_disp <?php echo loanForm::showField('amountOfFinancing'); ?>">
                <?php echo loanForm::label('amountOfFinancing', 'col-md-12 '); ?>
                <div class="col-md-5">

                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
            <span class="input-group-text">$
            </span></div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'amountOfFinancing', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="amountOfFinancing" id="amountOfFinancing"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($amountOfFinancing) ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" maxlength="13"
                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'amountOfFinancing', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($amountOfFinancing); ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('amountOfFinancing', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
            <span class="input-group-text">$
            </span></div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'amountOfFinancing', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="amountOfFinancingTo" id="amountOfFinancingTo"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($amountOfFinancingTo) ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" maxlength="13"
                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'amountOfFinancing', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($amountOfFinancingTo); ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 typicalPurchasePrice_disp <?php echo loanForm::showField('typicalPurchasePrice'); ?>">
                <?php echo loanForm::label('typicalPurchasePrice', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
            <span class="input-group-text">$
            </span></div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalPurchasePrice', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="typicalPurchasePrice" id="typicalPurchasePrice"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($typicalPurchasePrice) ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" maxlength="13"
                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalPurchasePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($typicalPurchasePrice); ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('typicalPurchasePrice', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
            <span class="input-group-text">$
            </span></div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalPurchasePrice', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="typicalPurchasePriceTo" id="typicalPurchasePriceTo"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($typicalPurchasePriceTo) ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" maxlength="13"
                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalPurchasePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($typicalPurchasePriceTo); ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 typicalConstructionCosts_disp <?php echo loanForm::showField('typicalConstructionCosts'); ?>">
                <?php echo loanForm::label('typicalConstructionCosts', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
            <span class="input-group-text">$
            </span></div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalConstructionCosts', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="typicalConstructionCosts" id="typicalConstructionCosts"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($typicalConstructionCosts) ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" maxlength="13"
                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalConstructionCosts', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($typicalConstructionCosts); ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('typicalConstructionCosts', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
            <span class="input-group-text">$
            </span></div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalConstructionCosts', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="typicalConstructionCostsTo" id="typicalConstructionCostsTo"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($typicalConstructionCostsTo) ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" maxlength="13"
                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalConstructionCosts', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($typicalConstructionCostsTo); ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 typicalSalePrice_disp <?php echo loanForm::showField('typicalSalePrice'); ?>">
                <?php echo loanForm::label('typicalSalePrice', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
            <span class="input-group-text">$
            </span></div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalSalePrice', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="typicalSalePrice" id="typicalSalePrice"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($typicalSalePrice) ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" maxlength="13"
                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalSalePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($typicalSalePrice); ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('typicalSalePrice', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
            <span class="input-group-text">$
            </span></div>
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalSalePrice', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text" name="typicalSalePriceTo" id="typicalSalePriceTo"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($typicalSalePriceTo) ?>"
                                   tabindex="<?php echo $tabIndex++; ?>" maxlength="13"
                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalSalePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($typicalSalePriceTo); ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 constructionDrawsPerProject_disp <?php echo loanForm::showField('constructionDrawsPerProject'); ?>">
                <?php echo loanForm::label('constructionDrawsPerProject', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'constructionDrawsPerProject', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="constructionDrawsPerProject" id="constructionDrawsPerProject"
                               value="<?php echo $constructionDrawsPerProject ?>" tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'typicalSalePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?><h5><?php echo $constructionDrawsPerProject; ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('constructionDrawsPerProject', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'constructionDrawsPerProject', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="constructionDrawsPerProjectTo" id="constructionDrawsPerProjectTo"
                               value="<?php echo $constructionDrawsPerProjectTo; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'constructionDrawsPerProject', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?><h5><?php echo $constructionDrawsPerProjectTo; ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 monthsPurchaseDateToFirstConst_disp <?php echo loanForm::showField('monthsPurchaseDateToFirstConst'); ?>">
                <?php echo loanForm::label('monthsPurchaseDateToFirstConst', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateToFirstConst', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="monthsPurchaseDateToFirstConst" id="monthsPurchaseDateToFirstConst"
                               value="<?php echo $monthsPurchaseDateToFirstConst; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateToFirstConst', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?><h5><?php echo $monthsPurchaseDateToFirstConst; ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('monthsPurchaseDateToFirstConst', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateToFirstConst', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="monthsPurchaseDateToFirstConstTo"
                               id="monthsPurchaseDateToFirstConstTo"
                               value="<?php echo $monthsPurchaseDateToFirstConstTo; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateToFirstConst', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?><h5><?php echo $monthsPurchaseDateToFirstConstTo; ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 monthsPurchaseDateUntilConst_disp <?php echo loanForm::showField('monthsPurchaseDateUntilConst'); ?>">
                <?php echo loanForm::label('monthsPurchaseDateUntilConst', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateUntilConst', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="monthsPurchaseDateUntilConst" id="monthsPurchaseDateUntilConst"
                               value="<?php echo $monthsPurchaseDateUntilConst; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateUntilConst', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?><h5><?php echo $monthsPurchaseDateUntilConst; ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('monthsPurchaseDateUntilConst', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateUntilConst', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="monthsPurchaseDateUntilConstTo" id="monthsPurchaseDateUntilConstTo"
                               value="<?php echo $monthsPurchaseDateUntilConstTo; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateUntilConst', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    <?php } else { ?><h5><?php echo $monthsPurchaseDateUntilConstTo; ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 monthsPurchaseDateToSaleDate_disp <?php echo loanForm::showField('monthsPurchaseDateToSaleDate'); ?>">
                <?php echo loanForm::label('monthsPurchaseDateToSaleDate', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateToSaleDate', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="monthsPurchaseDateToSaleDate" id="monthsPurchaseDateToSaleDate"
                               value="<?php echo $monthsPurchaseDateToSaleDate; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateToSaleDate', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?><h5><?php echo $monthsPurchaseDateToSaleDate; ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('monthsPurchaseDateToSaleDate', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateToSaleDate', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="monthsPurchaseDateToSaleDateTo" id="monthsPurchaseDateToSaleDateTo"
                               value="<?php echo $monthsPurchaseDateToSaleDateTo; ?>"
                               tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'monthsPurchaseDateToSaleDate', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?><h5><?php echo $monthsPurchaseDateToSaleDateTo; ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 NoOfSuchProjects_disp <?php echo loanForm::showField('NoOfSuchProjects'); ?>">
                <?php echo loanForm::label('NoOfSuchProjects', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'NoOfSuchProjects', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="NoOfSuchProjects" id="NoOfSuchProjects"
                               value="<?php echo $NoOfSuchProjects ?>" tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'NoOfSuchProjects', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?><h5><?php echo $NoOfSuchProjects; ?></h5><?php } ?>
                </div>
                <div class="col-md-1">To</div>
                <div class="col-md-5">
                    <?php echo loanForm::label('NoOfSuchProjects', 'hidden', '', 'To', 'To'); ?>
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'NoOfSuchProjects', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number" name="NoOfSuchProjectsTo" id="NoOfSuchProjectsTo"
                               value="<?php echo $NoOfSuchProjectsTo ?>" tabindex="<?php echo $tabIndex++; ?>"
                               maxlength="13" <?php echo BaseHTML::fieldAccess(['fNm' => 'NoOfSuchProjects', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    <?php } else { ?><h5><?php echo $NoOfSuchProjects; ?></h5><?php } ?>
                </div>
            </div>

            <div class="form-group row col-md-6 geographicAreas_disp <?php echo loanForm::showField('geographicAreas'); ?>">
                <?php echo loanForm::label('geographicAreas', 'col-md-12 '); ?>
                <div class="col-md-5">
                    <?php if ($allowToEdit) { ?>
                        <select data-placeholder=" - Select - " name="geographicAreas[]" id="geographicAreas"
                                tabindex="<?php echo $tabIndex++; ?>"
                                class="chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'geographicAreas', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                multiple=""
                                style="width:250px;" <?php echo BaseHTML::fieldAccess(['fNm' => 'geographicAreas', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <?php
                            for ($st = 0; $st < count($stateArray); $st++) {
                                $sOpt = '';
                                if (in_array($stateArray[$st]['stateCode'], $geographicAreas)) $sOpt = 'selected';
                                echo "<option value=\"" . $stateArray[$st]['stateCode'] . "\" " . $sOpt . '>' . $stateArray[$st]['stateName'] . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?><h5><?php echo implode(',', $geographicAreas); ?></h5><?php } ?>
                </div>

            </div>

            <?php echo CustomField::RenderForTabSection(
                PageVariables::$PCID,
                tblFile::class,
                LMRequest::$LMRId,
                'TT',
                $fileTab,
                $activeTab,
                LMRequest::myFileInfo()->getFileTypes(),
                LMRequest::myFileInfo()->getLoanPrograms()
            ); ?>
</div></div>
        </div>
    </div>
</div>
<?php ?>
<!-- Typical  Transactions section End -->
<style>
    .borrowerExperAlterClass:nth-of-type(odd) {
        background-color: #f3f6f9;
    }

    .borrowerExperAlterClass:nth-of-type(even) {
        background-color: #fff;
    }

</style>

<!-- borrowerExperience.php -->
