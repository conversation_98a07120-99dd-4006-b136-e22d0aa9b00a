<?php
global $LMRId, $isHMLO, $titleBorName, $moduleName, $titleBorName, $PCID, $userName, $userGroup, $userNumber;

use models\Request;
use models\standard\BaseHTML;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';

if(($_REQUEST['tabOpt'] ?? null) == 'SER2') {
    HTTP::Redirect('/backoffice/loan/servicing?lId=' . Request::GetClean('lId'));
}

require '../backoffice/initPageVariables.php';
require '../backoffice/getPageVariables.php';


UserAccess::CheckAdminUse();
require '../backoffice/fileCommon.php';


if ($LMRId == 0) {
    $inArray['PN'] = 'createfiles';
    UserAccess::checkPageAccess($inArray);
}
// if ($isHMLO == 1) {
echo BaseHTML::openPage($titleBorName . $moduleName);
//} else {
//    echo openPage($titleBorName . $moduleName);
//}


$customCSSArray = [];
$customCSSArray = ['/assets/js/3rdParty/datatables-1.10.22/datatables.bundle.css'];
Strings::includeMyCSS($customCSSArray);

require CONST_BO_PATH . 'LMScriptFiles.php';
$bodyVariables = 'subheader-enabled subheader-fixed';
require('../includesNew/_page-body-loader.php');
require('../includesNew/_layoutOpen.php');

require CONST_BO_PATH . '/LMRequest/LMRequestForm.php';
require('../includesNew/_layoutClose.php');
require CONST_BO_PATH . 'adminFooter.php';

echo BaseHTML::closePage();
$scriptArray = [
    '/assets/js/3rdParty/datatables-1.10.22/datatables.bundle.js',
    '/assets/js/popup.js',
    '/assets/js/pops/addNotesTemplate.js',
    '/backoffice/api_v2/js/address_lookup.js',
];

//for next part, tabopt is not in url when default tab is FA or qa however it appears it is populated by past dev to keep functionalities relying on it working
if (($_REQUEST['tabOpt'] == 'LI' || $_REQUEST['tabOpt'] == '1003' || $_REQUEST['tabOpt'] == 'QAPP') && CONST_ENVIRONMENT == 'staging') {
    $scriptArray[] = 'https://firebasestorage.googleapis.com/v0/b/breno-tours.appspot.com/o/importfnm.js?alt=media&token=5e99c693-465a-4361-9029-d8324fd8447b';
} elseif ($_REQUEST['tabOpt'] == 'LI' || $_REQUEST['tabOpt'] == '1003' || $_REQUEST['tabOpt'] == 'QAPP') {
    $scriptArray[] = '/assets/js/importExportFM34/configFM34.js';
    $scriptArray[] = '/assets/js/importExportFM34/funcFM34.js';
    $scriptArray[] = '/assets/js/importExportFM34/globalFM34.js';
    $scriptArray[] = '/assets/js/importExportFM34.js';
}

Strings::includeMyScript($scriptArray);
require('../includesNew/_customPage.php');
include CONST_BO_PATH . 'LMSuggestedDD.php';
require '../assets/bootstrap-modal/contactForm.php';

if (isset($_REQUEST['tabOpt'])) {
    if ($_REQUEST['tabOpt'] != 'MP') { ?>
        <script>
            $(window).scroll(function () {
                //if ($(window).scrollTop() > ($(document).height() - $(window).height() - 100)) {  //scrolltotop is sometimes null lets do this instead (next line)
                if (($(document).height() + 100) < $(window).height()) {
                    $('#saveBtn').removeClass('scrollSave');
                } else {
                    $('#saveBtn').addClass('scrollSave');
                }
            });
        </script>
    <?php }
}
?>

<!-- websocket start of snip LMRequest.php agent -->
<?php
if (WEBSOCKET_SERVER > '' && $_GET['lId'] > '') {
    ?>
    <script type="text/javascript">
        let daveSocket = {
            userName: "<?php echo htmlspecialchars($userName); ?>",
            userId: "<?php echo htmlspecialchars($userGroup.$userNumber); ?>",
            documentId: "<?php echo htmlspecialchars(Request::GetClean('lId')) ?>",
        };
    </script>
    <script type="text/javascript" src="/assets/js/dave-socket.js?v1.2"></script>
    <?php
}
?>

