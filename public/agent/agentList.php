<?php

use models\composite\oBroker\listAllAgentPreferredLMRAE;
use models\composite\oBroker\listAllAgentsLoanOfficer;
use models\constants\brokerStatusArray;
use models\constants\gl\glPCID;
use models\cypher;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Strings;
use models\standard\UserAccess;
use pages\backoffice\brokers\brokers;
use pages\backoffice\brokers\classes\BrokerReport;

session_start();

require '../includes/util.php';
require '../includes/router.php';
include CONST_ROOT_PATH . 'backoffice/initPageVariables.php';
include CONST_ROOT_PATH . 'backoffice/getPageVariables.php';

UserAccess::CheckAdminUse();

if (!(PageVariables::$externalBroker == 1 && PageVariables::$userRole == 'Agent')) {
    $redirectFile = CONST_SITE_URL . 'unauthorizedPage.php';
    echo "<script type=\"text/javascript\" >" .
        " window.location.href = '" . $redirectFile . "'" .
        '</script>';
    exit();
}

$searchTerm = ($_POST['searchTerm'] ?? null) ? Request::GetClean('searchTerm') : null;
$state = ($_POST['state'] ?? null) ? Request::GetClean('state') : null;
$branchId = ($_POST['branchId'] ?? null) ? Request::GetClean('branchId') : null;
$agentStatus = ($_POST['agentStatus'] ?? null) ? Request::GetClean('agentStatus') : null;
$alogin = ($_POST['alogin'] ?? null) ? Request::GetClean('alogin') : null;

$pageNumber = 1;
$noOfRecordsPerPage = 20;
if (isset($_REQUEST['pageNumber'])) {
    $pageNumber = Request::GetClean('pageNumber');
}
$stateArray = Arrays::fetchStates();

echo BaseHTML::openPage('Broker List - ' . CONST_DOMAIN);

/* Dynamic $Breadcrumb */
$Breadcrumb = [];
$Breadcrumb['icon'] = 'fas fa-users icon-md';
$Breadcrumb['breadcrumbList'] = '';//array(array('href' => '#', 'title' => 'Sample home'), array('href' => '#', 'title' => 'Sample Child'));
$Breadcrumb['toolbarHTML'] = '';
/* End of Dynamic $Breadcrumb */
$Breadcrumb['title'] = 'Broker List';

require('../includesNew/_page-body-loader.php');
require('../includesNew/_layoutOpen.php');

if (PageVariables::$PCID == glPCID::PCID_PROD_CV3) {
    $brokerStatusArray = brokerStatusArray::$brokerStatusArrayCV3;
} else {
    $brokerStatusArray = brokerStatusArray::$brokerStatusArray;
}
?>

<div class="card card-custom">
    <div class="card-header card-header-tabs-line">
        <div class="card-title">
            <h3 class="card-label"><?php echo $Breadcrumb['title']; ?>
                <i
                        class="manualPopover fas fa-info-circle text-primary " data-html="true"
                        data-content='
                            <div class="row p-2">
                                <div class="col-md-6"><i class="fas fa-user-check"></i> - denotes active Employee(s)</div>
                                <div class="col-md-6"><i class="fas fa-user-times"></i> - denotes Inactive Employee(s)</div>
                            </div>
                            <div class="row  p-2">
                                <div class="col-md-6"><i class="fas fa-user-check"></i> - Click to send email campaign</div>
                                <div class="col-md-6"><i class="flaticon-refresh"></i> - Click to change</div>
                            </div>
                            <div class="row  p-2">
                                <div class="col-md-6"><i class="flaticon2-check-mark"></i> - Allowed to login</div>
                                <div class="col-md-6"><i class="flaticon2-delete"></i> - Not allowed to login</div>
                            </div>'></i>
            </h3>
        </div>
        <div class="card-toolbar">
            <div title="Search"
                 class="tooltipClass  btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1 "
                 data-toggle="collapse" data-target="#agentSearchCollapse">
                <i class="flaticon-search text-primary"></i>
            </div>
            <a href="javascript:void(0);" data-href="<?php echo CONST_URL_POPS; ?>addNewBroker.php"
               data-toggle='modal' data-target='#exampleModal1' data-wsize='modal-xxl'
               data-name="Add/View Broker" title="Add/View Broker"
               class="btn btn btn-sm btn-success mx-2"
               data-id='loanOfficerId=<?php echo cypher::myEncryption(PageVariables::$userNumber); ?>&PCID=<?php echo cypher::myEncryption(PageVariables::$PCID) ?>&externalBroker=0&userRole=<?php echo cypher::myEncryption(PageVariables::$userRole); ?>&userGroup=<?php echo cypher::myEncryption(PageVariables::$userGroup); ?>'>
                Create Broker
            </a>
        </div>
    </div>

    <div class="card-body">
        <?php
        $brokerList = [];
        $branchesListArray = [];
        $branchIdsArray = [];
        $branchesListArrayByBrokerNumber = listAllAgentPreferredLMRAE::getReport([
            'brokerNumber' => PageVariables::$userNumber,
            'PCID'         => PageVariables::$PCID,
        ]);
        if ($branchesListArrayByBrokerNumber[PageVariables::$userNumber]) {
            $branchesListArray = $branchesListArrayByBrokerNumber[PageVariables::$userNumber];
            foreach ($branchesListArray as $eachBranch) {
                $branchIdsArray[] = $eachBranch['BID'];
            }
        }


        $ip['PCID'] = PageVariables::$PCID;
        $ip['externalBroker'] = 0;
        $ip['brokerNumber'] = PageVariables::$userNumber;
        $ip['loanOfficerId'] = PageVariables::$userNumber;
        $ip['userGroup'] = 'Agent';
        $ip['state'] = $state;
        $ip['agentStatus'] = $agentStatus;
        $ip['alogin'] = $alogin;
        $ip['searchTerm'] = $searchTerm;
        $ip['branchIds'] = (trim($branchId) != '') ? $branchId : implode(',', $branchIdsArray);
        $ip['opt'] = 'limit';
        $ip['noOfRecordsPerPage'] = $noOfRecordsPerPage;
        $ip['allowToSeeAllBrokers'] = PageVariables::$allowToSeeAllBrokers;
        $ip['pageNumber'] = ($pageNumber - 1) * $noOfRecordsPerPage;
        $brokerInfo = listAllAgentsLoanOfficer::getReport($ip);

        $noOfRecords = ($brokerInfo['noOfRecords'][0]['cnt']);
        $brokerList = $brokerInfo['agentInfo'];

        if ($noOfRecords > $noOfRecordsPerPage) {
            $recNumbStart = ($pageNumber - 1) * $noOfRecordsPerPage - 1;
            $temp = $recNumbStart + 1;
        }

        $recNumbStart = ($pageNumber - 1) * $noOfRecordsPerPage + 1;
        $recNumbEnd = $recNumbStart + $noOfRecordsPerPage - 1;
        if ($recNumbEnd > $noOfRecords) {
            $recNumbEnd = $noOfRecords;
        }
        $recNumbStart = $recNumbStart - 1;
        $serialNo = $recNumbStart + 1;
        ?>
        <div class="accordion accordion-toggle-arrow" id="agentSearchAccordion">
            <div id="agentSearchCollapse" class="collapse" data-parent="#agentSearchAccordion">
                <div class="card-body">
                    <form id="brokerForm" name="brokerForm" action="" method="post">
                        <input type="hidden" name="pageNumber" id="pageNumber"
                               value="<?php echo htmlspecialchars($pageNumber); ?>">
                        <input type="hidden" name="userId" id="userId"
                               value="<?php echo cypher::myEncryption(PageVariables::$userNumber) ?>">
                        <input type="hidden" name="userType" id="userType"
                               value="<?php echo cypher::myEncryption(PageVariables::$userGroup) ?>">
                        <input type="hidden" name="toUType" id="toUType"
                               value="<?php echo cypher::myEncryption('Agent') ?>">
                        <input type="hidden" name="userRole" id="userRole"
                               value="<?php echo cypher::myEncryption(PageVariables::$userRole) ?>">
                        <input type="hidden" name="allAgentCnt" id="allAgentCnt"
                               value="<?php echo count($brokerList ?? []); ?>"/>
                        <input type="hidden" name="agentType" id="agentType" value="0"/>
                        <div class="row">
                            <div class="col-lg-3 mb-2 d-none">
                                <label class="text-dark-50 font-weight-bold  mb-0" for="agentStatus">Status</label>
                                <div class="input-group">
                                    <select name="agentStatus" id="agentStatus" disabled
                                            class="form-control form-controller-solid  datatable-input">
                                        <option value="1" <?php echo Arrays::isSelected($agentStatus, '1'); ?>> Active
                                        </option>
                                        <option value="0" <?php echo Arrays::isSelected($agentStatus, '0'); ?>>
                                            Deleted
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3 mb-2">
                                <label class="text-dark-50 font-weight-bold  mb-0" for="alogin">Login Status</label>
                                <div class="input-group">
                                    <select name="alogin" id="alogin"
                                            class="form-control form-controller-solid  datatable-input">
                                        <option value=""> - All Login -</option>
                                        <option value="1" <?php echo htmlspecialchars(Arrays::isSelected($alogin, '1')); ?>>
                                            With Login
                                        </option>
                                        <option value="0" <?php echo htmlspecialchars(Arrays::isSelected($alogin, '0')); ?>>
                                            Without Login
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3 mb-2">
                                <label class="text-dark-50 font-weight-bold  mb-0" for="branchId">Branch</label>
                                <div class="input-group">
                                    <select name="branchId"
                                            id="branchId"
                                            class="form-control form-controller-solid  datatable-input">
                                        <option value=" "> - Select Branch -</option>
                                        <?php
                                        foreach ($branchesListArray as $eachBranch) { ?>
                                            <option value="<?php echo $eachBranch['BID']; ?>" <?php echo htmlspecialchars(Arrays::isSelected($eachBranch['BID'], $branchId)) ?>><?php echo $eachBranch['LMRExecutive']; ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-3 mb-2">
                                <label class="text-dark-50 font-weight-bold mb-0" for="state">State</label>
                                <div class="input-group">
                                    <select name="state"
                                            id="state"
                                            class="form-control form-controller-solid  datatable-input"
                                            onchange="populateStateTimeZone('newBrokerForm', 'state', 'timeZone');">
                                        <option value=" "> - Select State -</option>
                                        <?php
                                        foreach ($stateArray as $eachState) { ?>
                                            <option value="<?php echo $eachState['stateCode']; ?>" <?php echo Arrays::isSelected($eachState['stateCode'], htmlspecialchars($state)) ?>><?php echo $eachState['stateName']; ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-3 mb-2">
                                <label class="text-dark-50 font-weight-bold  mb-0" for="searchTerm">Type To
                                    Search</label>
                                <div class="input-group">
                                    <input type="text" name="searchTerm" id="searchTerm"
                                           class="form-control form-controller-solid  datatable-input"
                                           value="<?php echo Strings::stripQuote(htmlspecialchars($searchTerm)) ?>"
                                           size="40"
                                           placeholder="Type text to Search in Broker Name / Email">
                                </div>
                            </div>
                            <div class="col-lg-2 <?php if (PageVariables::$userRole == 'Super') { ?>offset-md-9<?php } ?> mb-2">
                                <label class="text-dark-50 font-weight-bold  mb-0" for="searchbutton">&nbsp;</label>
                                <button class="btn btn-primary btn-primary--icon form-control" name="but_search"
                                        id="but_search"
                                        value="Search" type="submit">
                                    <span><i class="la la-search"></i><span>Search</span></span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive">
                    <table style="width:100%"
                           class="table table-hover  table-bordered table-condensed table-sm table-vertical-center ">
                        <thead class="thead-light">
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Company</th>
                            <th>Status</th>
                            <th class="text-center">Action</th>
                            <?php if (PageVariables::$allowEmailCampaign && PageVariables::$isPCAllowEmailCampaign) { ?>
                                <th>
                                    <table class="table table-borderless">
                                        <tbody>
                                        <tr>
                                            <td class="p-0">
                                                <div class="checkbox-inline">
                                                    <label class="checkbox checkbox-outline checkbox-square">
                                                        <input class="newCheck tooltipClass" type="checkbox"
                                                               title="Click to select all (<?php echo brokers::$Filter->perPage; ?>) Agent below."
                                                               id="selectEmail"
                                                               onchange="brokers.selectBelowAgentForEmailTemplate(false)"><span></span>
                                                        Select Agents In Page</label>
                                                </div>
                                            </td>
                                            <td class="p-0">
                                                <div class="checkbox-inline">
                                                    <label for="selectAllEmail"
                                                           class="checkbox checkbox-outline checkbox-square"><input
                                                                class="newCheck tooltipClass" type="checkbox"
                                                                name="selectAllEmail"
                                                                id="selectAllEmail"
                                                                title="Click to select all Agents below."
                                                                onchange="brokers.selectBelowAgentForEmailTemplate(true)"><span></span>All</label>
                                                </div>
                                            </td>
                                            <td class="p-0 ">
                                                <!-- email_schedule Removed From Below Class -->
                                                <span class="btn1 d-none cursor-pointer"
                                                      id="agentEmailBtn"
                                                      data-href="<?php echo CONST_URL_POPS; ?>sendAgentEmailCampaign.php"
                                                      data-wsize='modal-xl'
                                                      data-name='Send Email Campaign'
                                                      data-toggle='modal' data-target='#exampleModal1'
                                                      data-id=''>
                                            <i class="ki ki-solid-plus icon-nm"></i>
                                        </span>
                                                <span data-name="Send Email Campaign"
                                                      class="cursor-pointer"
                                                      data-id=""
                                                      onclick="brokers.selectedAgentsForMassEmail()">
                                            <span
                                                    class="svg-icon  svg-icon  svg-icon-2x"><!--begin::Svg Icon | path:C:\wamp64\www\keenthemes\themes\metronic\theme\html\demo1\dist/../src/media/svg/icons\Communication\Mail.svg--><svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="24px"
                                                        height="24px"
                                                        viewBox="0 0 24 24">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M5,6 L19,6 C20.1045695,6 21,6.8954305 21,8 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,8 C3,6.8954305 3.8954305,6 5,6 Z M18.1444251,7.83964668 L12,11.1481833 L5.85557487,7.83964668 C5.4908718,7.6432681 5.03602525,7.77972206 4.83964668,8.14442513 C4.6432681,8.5091282 4.77972206,8.96397475 5.14442513,9.16035332 L11.6444251,12.6603533 C11.8664074,12.7798822 12.1335926,12.7798822 12.3555749,12.6603533 L18.8555749,9.16035332 C19.2202779,8.96397475 19.3567319,8.5091282 19.1603533,8.14442513 C18.9639747,7.77972206 18.5091282,7.6432681 18.1444251,7.83964668 Z"
              fill="#000000"/>
    </g>
</svg><!--end::Svg Icon--></span>
                                        </span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </th>
                            <?php } ?>
                        </tr>
                        </thead>
                        <?php
                        if (PageVariables::$userNumber > 0) {
                            $index = 1;
                            foreach ($brokerList as $eachBroker) {
                                $eachBrokerName = $eachBroker['brokerName'];
                                $eachBrokerEmail = $eachBroker['bEmail'];
                                $eachBrokerCompany = $eachBroker['company'];
                                $eachBrokerId = ($eachBroker['brokerNumber']);
                                $brokerStatus = ($eachBroker['brokerStatus'] ?? 0);
                                $isOwnedByLoanOfficer = $eachBroker['isOwnedByLoanOfficer'] ?? 0;
                                ?>
                                <tr
                                        data-agentid="<?php echo $eachBrokerId; ?>"
                                >
                                    <td><?php echo $eachBrokerName; ?></td>
                                    <td><?php echo $eachBrokerEmail ?></td>
                                    <td><?php echo $eachBrokerCompany; ?></td>
                                    <td><?php echo $brokerStatusArray[$brokerStatus]; ?></td>
                                    <td class="text-center">
                                        <?php if ($isOwnedByLoanOfficer) { ?>
                                            <a href="javascript:void(0);"
                                               data-href="<?php echo CONST_URL_POPS; ?>addNewBroker.php"
                                               data-toggle='modal' data-target='#exampleModal1' data-wsize='modal-xxl'
                                               data-name="Edit Broker" title="Add/View Broker"
                                               class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass "
                                               data-id='loanOfficerId=<?php echo cypher::myEncryption(PageVariables::$userNumber); ?>&PCID=<?php echo cypher::myEncryption(PageVariables::$PCID) ?>&externalBroker=0&userRole=<?php echo cypher::myEncryption(PageVariables::$userRole); ?>&userGroup=<?php echo cypher::myEncryption(PageVariables::$userGroup); ?>&agentId=<?php echo $eachBrokerId; ?>'>
                                                <i class="fa fa-edit"></i>
                                            </a>
                                        <?php } ?>
                                    </td>
                                    <?php if (PageVariables::$allowEmailCampaign && PageVariables::$isPCAllowEmailCampaign) { ?>
                                        <td class="text-center">
                                            <label for="agentID_<?php echo $index ?>">
                                                <input
                                                        class="newCheck agentList"
                                                        type="checkbox"
                                                        name="agentID[]"
                                                        id="agentID_<?php echo $index ?>"
                                                        value="<?php echo $eachBrokerId; ?>">
                                                <span></span>
                                            </label>
                                        </td>
                                    <?php } ?>
                                </tr>
                                <?php $index++;
                            }
                        } ?>
                    </table>
                </div>
            </div>
            <?php if ($recNumbEnd > 0) { ?>
                <div class="col-md-12 text-right">
                    <?php $myForm = 'brokerForm';
                    require '../backoffice/pageLinks.php';
                    ?>
                </div>
            <?php } ?>
        </div>
    </div>
</div>
<?php
require('../includesNew/_layoutClose.php');
include CONST_BO_PATH . 'adminFooter.php';
echo BaseHTML::closePage();

Strings::includeMyScript([
    '/assets/js/models/Dates.js',
    '/assets/js/models/formValue.js',
    '/assets/js/fileCommon.js',
    '/assets/js/createAgent.js',
    '/assets/js/3rdParty/jquery-autocomplete/jquery.autocomplete_min.js',
    '/backoffice/brokers/js/brokers.js',
]);
require('../includesNew/_customPage.php');
?>
