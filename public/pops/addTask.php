<?php

use models\cypher;
use models\pops\addTask;
use models\Request;
use models\standard\Strings;
use models\standard\Custify;

global $userGroup, $allowToCreateTasks, $PCID, $userRole, $userNumber, $userTimeZone, $allowCFPBAuditing, $clsOpt;

require 'popsConfig.php';

$DIYUser = 0;
$taskId = 0;
if (isset($_REQUEST['DIYUser'])) $DIYUser = Request::GetClean('DIYUser');
if (isset($_REQUEST['taskId'])) $taskId = cypher::myDecryption(Request::GetClean('taskId'));

if ($DIYUser != '1') {
    /* Open for DIY users */
    require 'checkSessionValue.php';
    /** Redirect to login page when the user sessions end **/
}

if (!($userGroup == 'CFPB Auditor' || $userGroup == 'Auditor Manager' || $allowToCreateTasks || $DIYUser == '1' || $taskId > 0)) {
    $redirectFile = CONST_SITE_URL . 'unauthorizedPage.php';
    echo "<script type=\"text/javascript\" >" .
        " window.location.href = '" . $redirectFile . "'" .
        '</script>';
    exit();
}
$LMRId = 0;
$taskOpt = 0;
$curDateWtDtFormat = '';
$taskOpt = '';
$taskStatus = '';
$comments = '';
$divId = '';
$subject = '';
$dueDate = '';
$reminderDate = '';
$priorityLevel = '';
$opt = '';
$dt = '';
$loanSalesDate = '';
$opt1 = '';
$notesID = 0;
$actionpage = '';
$isAutomationTask = '';
$autoTaskNotAssign = '';
$autoTaskComments = '';

if (isset($_REQUEST['dueDate'])) $dueDate = cypher::myDecryption(Request::GetClean('dueDate'));
if (isset($_REQUEST['LMRId'])) $LMRId = cypher::myDecryption(Request::GetClean('LMRId'));
if (isset($_REQUEST['taskOpt'])) $taskOpt = cypher::myDecryption(Request::GetClean('taskOpt'));
if (isset($_REQUEST['taskStatus'])) $taskStatus = cypher::myDecryption(Request::GetClean('taskStatus'));
if (isset($_REQUEST['subject'])) $subject = cypher::myDecryption(Request::GetClean('subject'));
if (isset($_REQUEST['reminderDate'])) $reminderDate = cypher::myDecryption(Request::GetClean('reminderDate'));
if (isset($_REQUEST['priorityLevel'])) $priorityLevel = cypher::myDecryption(Request::GetClean('priorityLevel'));
if (isset($_REQUEST['opt'])) $opt = Request::GetClean('opt');
if (isset($_REQUEST['opt1'])) $opt1 = Request::GetClean('opt1');
if (isset($_REQUEST['dt'])) $dt = Request::GetClean('dt');
if (isset($_REQUEST['divId'])) $divId = Request::GetClean('divId');
if (isset($_REQUEST['loanSalesDate'])) $loanSalesDate = Request::GetClean('loanSalesDate');
if (isset($_REQUEST['notesID'])) $notesID = cypher::myDecryption(Request::GetClean('notesID'));
if (isset($_REQUEST['actionpage'])) $actionpage = Request::GetClean('actionpage');
if (isset($_REQUEST['isAutomationTask'])) $isAutomationTask = Request::GetClean('isAutomationTask');
if (isset($_REQUEST['autoTaskNotAssign'])) $autoTaskNotAssign = Request::GetClean('autoTaskNotAssign');


$inArray = [
    'PCID' => $PCID,
    'taskId' => $taskId,
    'LMRId' => $LMRId,
    'taskOpt' => $taskOpt,
    'dueDate' => $dueDate,
    'userRole' => $userRole,
    'userNumber' => $userNumber,
    'taskStatus' => $taskStatus,
    'subject' => $subject,
    'reminderDate' => $reminderDate,
    'priorityLevel' => $priorityLevel,
    'opt' => $opt,
    'dt' => $dt,
    'DIYUser' => $DIYUser,
    'divId' => $divId,
    'loanSalesDate' => $loanSalesDate,
    'userTimeZone' => $userTimeZone,
    'allowCFPBAuditing' => $allowCFPBAuditing,
    'userGroup' => $userGroup,
    'opt1' => $opt1,
    'notesID' => $notesID,
    'actionpage' => $actionpage,
    'isAutomationTask' => $isAutomationTask,
];
//Automation Task has No User Assign
//So we need to get the values from url (table join will not work)
if ($isAutomationTask && $autoTaskNotAssign) {
    if (isset($_REQUEST['comments'])) $comments = cypher::myDecryption($_REQUEST['comments']);

    //pass the values
    $inArray['isAutomationTask'] = $isAutomationTask;
    $inArray['autoTaskNotAssign'] = $autoTaskNotAssign;
    //$inArray['autoTaskComments'] = $autoTaskComments;
}

$addTask = new addTask();
echo $addTask->getFormHtml(1, $inArray);

?>
<script>
    $('.popupSubmit').show();

    jQuery.validator.addMethod(
        "validateUploadFormNew",
        function (value, element) {
            console.log({func: 'validateUploadFormNew'});
            if ($("#selectedClient").prop('checked') == false && $("#branchIds").prop('checked') == false && $("#agentIds").prop('checked') == false && $('#employeeIds').val() == '') {
                if ($('#secondaryAgentIds').length > 0) {
                    if ($('#secondaryAgentIds').prop('checked') == false) {
                        toastrNotification('Please assign task', 'error');
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    toastrNotification('Please Assign task', 'error');
                    return false;
                }
            } else {
                return true;
            }
        },
        jQuery.validator.messages.validateUploadFormNew
    );

    jQuery.validator.addMethod("dateTimerValidation", function (value, element) {
        return dateClass.validateDateTime(element, true);
    }, "Please Enter Valid Date Time");

    var LWFormControls = function () {
        console.log({func: 'LWFormControls'});
        let _formSubmitValidation = function () {
            console.log({func: '_formSubmitValidation'});
            let formIdToSubmit = $('#addTaskForm');
            formIdToSubmit.validate({
                ignore: ".ignoreValidation",
                rules: {
                    taskSubj: "required",
                    taskStatus: "required",
                    'validationField': {
                        validateUploadFormNew: true,
                    },
                    'dueDate' : {
                        required : true,
                        dateTimerValidation : true
                    },
                    'reminderDate' : {
                        dateTimerValidation : true
                    },
                },
                messages: {
                    taskSubj: "Please Enter The Subject",
                    taskStatus: "Please Select The Task Status",
                    'validationField': {
                        validateUploadFormNew: "",
                    },
                    'dueDate': {
                        required : "Please Enter Due Date",
                        dateTimerValidation: "",
                    },
                    'reminderDate': {
                        dateTimerValidation: "",
                    },
                },
                errorElement: "em",
                errorPlacement: function (error, element) {
                    // Add the `invalid-feedback` class to the error element
                    error.addClass("invalid-feedback");

                    if (element.prop("type") === "checkbox") {
                        error.insertAfter(element.next("label"));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function (element) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function (element) {
                    $(element).addClass("is-valid").removeClass("is-invalid");
                },
                submitHandler: function () {
                    console.log({func: 'submitHandler'});

                    let ajaxUrl = $(formIdToSubmit).attr('action');
                    let formData = $(formIdToSubmit).serialize();

                    console.log({
                        ajaxUrl: ajaxUrl,
                        formData: formData,
                    });

                    $.ajax({
                        url: ajaxUrl,
                        type: "POST",
                        data: formData,
                        beforeSend: function () {
                            BlockDiv('modal-content-id');
                        },
                        complete: function () {
                            UnBlockDiv('modal-content-id');
                        },
                        success: function (response) {
                            closeModal();
                            let res = null;
                            try {
                                res = JSON.parse(response);
                            } catch (e) {
                                res = response;
                            }
                            console.log({
                                success: 'success',
                                res: res,
                            });
                            if (parseInt(res.code) === 100) {
                                toastrNotification(res.msg, 'success');
                                setTimeout(function () {
                                    location.reload();
                                }, 100);
                            } else {
                                toastrNotification(res.msg, 'error');
                            }
                        },
                        error: function (jqXhr, textStatus, errorMessage) {
                            toastrNotification(errorMessage, 'error');
                        }
                    });
                }
            });
        }
        return {
            // public functions
            init: function () {
                _formSubmitValidation();
            }
        };
    }();
    $(document).ready(function () {
        //console.log($("#reminderDate").datepicker('getDate'));
        LWFormControls.init();
        let today = new Date();
        let dd = String(today.getDate()).padStart(2, '0');
        let mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
        let yyyy = today.getFullYear();
        today = mm + '/' + dd + '/' + yyyy;

        let dueDate = $("#dueDate2");
        dueDate.datetimepicker({
            autoclose: false,
            //calendarWeeks : true,
            //minDate: new Date(),     //got rid of this it was erasing earlier dates
            //disabledDates: [new Date()],
            dateFormat: 'mm/dd/yy',
            timeFormat: 'hh:mm:am',
            todayHighlight: true,
            onChangeMonthYear: function (year, month, inst) {
                //  setTimeout("setServerTimeToSchedule()", 100);
            },
        });
        dueDate.click(function () {
            $(this).datetimepicker('show');
        });
        dueDate.on("change.datetimepicker", function (e) {
        });

        let reminderDate = $("#reminderDate2");
        reminderDate.datetimepicker({
            autoclose: false,
            //calendarWeeks : true,
            //minDate: today,  //removed as it was messing up dates that are passed
            //disabledDates: [new Date()],
            dateFormat: 'mm/dd/yy',
            timeFormat: 'hh:mm:am',
            todayHighlight: true,
            onChangeMonthYear: function () {
                console.log('onChangeMonthYear');
            },
            onChange: function () {
                //   console.log('Called');
            },

        });
        reminderDate.click(function () {
            $(this).datetimepicker('show');
            // setServerTimeToSchedule();
        });
        reminderDate.on("change.datetimepicker", function (e) {
        });

    });
</script>
<?php echo Custify::addTaskFooter();?>