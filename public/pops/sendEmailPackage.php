<?php

use models\composite\oBinder\getBinderDocName;
use models\composite\oBinder\getBinderDocument;
use models\composite\oBranch\getBranchUploadedDoc;
use models\composite\oCustomDocs\getAllDynamicDatas;
use models\composite\oCustomDocs\sendPCCustomDocsContent;
use models\composite\oCustomDocs\SubstituteDynamicTagsForEmail;
use models\composite\oFax\faxPackageToUser;
use models\composite\oFile\getFileInfo;
use models\composite\oFileDoc\getSendFileDoc;
use models\composite\oFileDoc\saveFileDocument;
use models\composite\oFileEmail\sendClientAppInDocToNVAPC;
use models\composite\oFileEmail\sendEmailPackage;
use models\composite\oFileUpdate\saveFileNotes;
use models\composite\oFileUpdate\savePreApprovalPkgDetails;
use models\composite\oFileUpdate\updateLenderCallBackDate;
use models\composite\oPackage\getLibPackage;
use models\composite\oPC\getPCUploadDocs;
use models\composite\oTrust\getMyCreditorName;
use models\composite\oTrust\getTrustDocDetails2;
use models\composite\oTrust\saveTrustEmailTo;
use models\composite\oTrust\trustDocWrapper;
use models\constants\fileTypesAllowedToFax;
use models\constants\gl\glCloudDocType;
use models\constants\gl\glMimeTypes;
use models\FileStorage;
use models\cypher;
use models\standard\Strings;
use models\standard\UserAccess;
use models\UploadServer;


global $userGroup, $userNumber, $userName, $isSysNotesPrivate;
global $resFileSizeInfo, $userEmail;

require 'popsConfig.php';

UserAccess::checkReferrerPgs(['url' => 'LMRequest.php']);

$fileTypesAllowedToFax = fileTypesAllowedToFax::$fileTypesAllowedToFax;
$glMimeTypes = glMimeTypes::$glMimeTypes;
$glCloudDocType = glCloudDocType::$glCloudDocType;

UserAccess::CheckAdminUse();

$employeeIdArray = [];
$notifyEmployeeIds = '';
$sendNotifyEmployee = '';
$notifyMemberIds = [];
$emailSubject = '';
$emailMessage = '';
$borrowerEmail = '';
$coBorrowerEmail = '';
$executiveEmail = '';
$brokerEmail = '';
$UType = '';
$VUID = '';
$PCID = '';
$sendNotifyBorEmail = '';
$sendNotifyCoBorEmail = '';
$sendNotifyAEEmail = '';
$sendNotifyEmail = '';
$sendNotifyEmployee = '';
$empEmailCnt = '';
$memberEmaiCnt = '';
$LMRId = 0;
$toAddress = '';
$toAddressArray = [];
$docRecipientEmailArray = [];
$responseId = 0;
$exeId = 0;
$empId = 0;
$brId = 0;
$msg = '';
$dynamuURL = '';
$selPKGID = '';
$selectedPkgArray = [];
$pkgAllSelectedArray = [];
$notesDocNames = '';
$notesDocNames1 = '';
$attachmentArray = [];
$ndnCnt = 0;
$fileCreatedDate = '';
$ndnCnt1 = 0;
$notesDocNames2 = '';
$ndnCnt2 = 0;
$selectedBranchPkgArray = [];
$executiveId = 0;
$selBPKGID = '';
$selTxnID = '';
$branchDocArray = [];
$ndnCnt4 = 0;
$ndnCnt5 = 0;
$res = 0;
$branchDocKeyArray = [];
$trustDocArray = [];
$trustDocKeyArray = [];
$selCPKGID = '';
$selCWPKGID = '';
$brokerNumber = 0;
$resendTxnID = '';
$notesDocNames3 = '';
$ndnCnt3 = 0;
$selectedETxnIDArray = [];
$selRPKGID = '';
$custDocMsg = '';
$selBinPKGID = '';
$selectedBinderPkgArray = [];
$binderDocsArray = [];
$eSignDocIds = '';
$dIds = '';
$binderDocsNameArray = [];
$d1 = 0;
$d2 = 0;
$esignedDocsArray = [];
$uploadDocsArray = [];
$receiverName = '';
$notes = '';
$selectedUpDocArray = [];
$tempDocKeyInfoArray = [];
$tempDocArray = [];
$selAttachedPkgArray = [];
$allowToSendFax = 0;
$faxMsg = '';
$allowToSendEmail = 0;
$CCIDs = 0;
$selectedCustomPkgArray = $selectedCustomWordPkgArray = [];
$borrowerLName = '';
$borrowerFName = '';
$clientName = '';
$fileDetails = [];
$PkgInfoArray = [];
$clientInfoArray = [];
$selPCPKGID = '';
$payeeEmail = '';
$sendTo = '';
$selectedPCPkgArray = [];
$pcDocKeyArray = [];
$nonBorrowerEmail = '';
$pcDocArray = [];
$myInputArray = [];
$selCreditorAttachedPKGID = 0;
$selCreditorEsignPKGID = 0;
$selectedCreditorEsignPkgArray = [];
$attachedCreditorPkgArray = [];
$cloudDocArray = [];
$CIDs = [];
$memberArray = [];
$fileDtlArray = [];
$oldFPCID = 0;
$actionOpt = '';
$isSLM = 0;
$selUpDocID = 0;
$useCoverPage = 1;
$displayLender = 1;
$displayPropertyAdd = 1;
$firstBuyerEmail = '';
$secondBuyerEmail = '';
$thirdBuyerEmail = '';
$selAttachedPkg = '';
$getFileDocPathInfoArray = [];

if ($borrowerEmail != '') $sendNotifyBorEmail = 'yes';
if ($coBorrowerEmail != '') $sendNotifyCoBorEmail = 'yes';
if (isset($_POST['lId'])) $LMRId = trim($_POST['lId']);
if (isset($_POST['rId'])) $responseId = trim($_POST['rId']);
if (isset($_POST['emailSubject'])) $emailSubject = Strings::stripQuote($_POST['emailSubject']);
if (isset($_POST['emailMessage'])) $emailMessage = Strings::stripQuote($_POST['emailMessage']);
if (isset($_POST['borrowerEmail'])) $borrowerEmail = trim($_POST['borrowerEmail']);
if (isset($_POST['VUType'])) $UType = trim($_POST['VUType']);
if (isset($_POST['VUID'])) $VUID = trim($_POST['VUID']);
if (isset($_POST['AEProcCompId'])) $PCID = trim($_POST['AEProcCompId']);
if (isset($_POST['borrowerEmail'])) $borrowerEmail = trim($_POST['borrowerEmail']);
if (isset($_POST['coBorrowerEmail'])) $coBorrowerEmail = trim($_POST['coBorrowerEmail']);
if (isset($_POST['brokerEmail'])) $brokerEmail = trim($_POST['brokerEmail']);
if (isset($_POST['executiveEmail'])) $executiveEmail = trim($_POST['executiveEmail']);
if (isset($_POST['employeeId'])) $employeeIdArray = $_POST['employeeId'];
if (isset($_POST['selPKGID'])) $selPKGID = $_POST['selPKGID'];
if (isset($_POST['selBPKGID'])) $selBPKGID = $_POST['selBPKGID'];
if (isset($_POST['selTxnID'])) $selTxnID = $_POST['selTxnID'];
if (isset($_POST['selCPKGID'])) $selCPKGID = $_POST['selCPKGID'];
if (isset($_POST['selCWPKGID'])) $selCWPKGID = $_POST['selCWPKGID'];
if (isset($_POST['selAttachedPkg'])) $selAttachedPkg = $_POST['selAttachedPkg'];
if (isset($_POST['eId'])) $executiveId = $_POST['eId'];
if (isset($_POST['bNo'])) $brokerNumber = $_POST['bNo'];
if (isset($_POST['fileCreatedDate'])) $fileCreatedDate = $_POST['fileCreatedDate'];
if (isset($_POST['resendTxnID'])) $resendTxnID = $_POST['resendTxnID'];
if (isset($_POST['selRPKGID'])) $selRPKGID = $_POST['selRPKGID'];
if (isset($_POST['selBinPKGID'])) $selBinPKGID = $_POST['selBinPKGID'];
if (isset($_POST['selUpDocID'])) $selUpDocID = $_POST['selUpDocID'];
if (isset($_POST['borrowerLName'])) $borrowerLName = $_POST['borrowerLName'];
if (isset($_POST['selPCPKGID'])) $selPCPKGID = $_POST['selPCPKGID'];
if (isset($_POST['isSLM'])) $isSLM = $_POST['isSLM'];
if (isset($_POST['useCoverPage'])) $useCoverPage = $_POST['useCoverPage'];
if (isset($_POST['displayLender'])) $displayLender = $_POST['displayLender'];
if (isset($_POST['displayPropertyAdd'])) $displayPropertyAdd = $_POST['displayPropertyAdd'];
if (isset($_POST['selCreditorAttachedPKGID'])) $selCreditorAttachedPKGID = $_POST['selCreditorAttachedPKGID'];
if (isset($_POST['selCreditorEsignPKGID'])) $selCreditorEsignPKGID = $_POST['selCreditorEsignPKGID'];
if (isset($_POST['nonBorrowerEmail'])) $nonBorrowerEmail = trim($_POST['nonBorrowerEmail']);
if (isset($_POST['payeeEmail'])) $payeeEmail = trim($_POST['payeeEmail']);
if (isset($_POST['firstBuyerEmail'])) $firstBuyerEmail = trim($_POST['firstBuyerEmail']);
if (isset($_POST['secondBuyerEmail'])) $secondBuyerEmail = trim($_POST['secondBuyerEmail']);
if (isset($_POST['thirdBuyerEmail'])) $thirdBuyerEmail = trim($_POST['thirdBuyerEmail']);
if (isset($_POST['CIDs'])) $CIDs = $_POST['CIDs']; //File Contacts added (#154749877).
if (isset($_POST['actionOpt'])) $actionOpt = trim($_POST['actionOpt']);
if (isset($_POST['member'])) $memberArray = $_POST['member'];
if ($selPKGID > 0) $selectedPkgArray = preg_split('/[, ]+/', $selPKGID);
if ($selBPKGID > 0) $selectedBranchPkgArray = preg_split('/[, ]+/', $selBPKGID);
if ($selPCPKGID > 0) $selectedPCPkgArray = preg_split('/[, ]+/', $selPCPKGID);

if ($selCPKGID > 0) $selectedCustomPkgArray = preg_split('/[, ]+/', $selCPKGID);
if ($selCreditorEsignPKGID > 0) $selectedCreditorEsignPkgArray = preg_split('/[, ]+/', $selCreditorEsignPKGID);
if ($selCreditorAttachedPKGID > 0) $attachedCreditorPkgArray = preg_split('/[, ]+/', $selCreditorAttachedPKGID);
if ($selCWPKGID > 0) $selectedCustomWordPkgArray = preg_split('/[, ]+/', $selCWPKGID);
if ($selBinPKGID > 0) $selectedBinderPkgArray = preg_split('/[, ]+/', $selBinPKGID);
//    $CCIDs = explode(", ", $selectedCustomPkgArray);
$selAttachedPkgArray = preg_split('/[, ]+/', $selAttachedPkg);
if ($selUpDocID > 0) $selectedUpDocArray = preg_split('/[, ]+/', $selUpDocID);
$selectedETxnIDArray = preg_split('/[, ]+/', $selTxnID);


$borFaxNo = '';
$coBorFaxNo = '';
$branchFaxInfo = '';
$lien1LenderFax = '';
$lien1Bank1RepFax = '';
$lien2Bank1RepFax = '';
$lien2LenderFax = '';
$brokerFaxNo = '';
$faxReceiverName = '';
$faxReceiverComp = '';
$faxReceiverNo = '';
$faxPop = 0;
$creditorInfoInfoArray = [];
$eSignedResultArray = [];
$eSignedReadyDocArray = [];
$eSignedActionArray = [];
$creditorESignedDoc = [];
$PCInfo = [];
$lenderEmail1 = '';
$lenderEmail2 = '';
$counselEmail = '';
$counselAttorneyFax = '';
if (isset($_POST['faxPop'])) $faxPop = trim($_POST['faxPop']);
if (isset($_POST['borFax'])) $borFaxNo = Strings::cleanPhoneNo(trim($_POST['borFax']));
if (isset($_POST['coBorFax'])) $coBorFaxNo = Strings::cleanPhoneNo(trim($_POST['coBorFax']));
if (isset($_POST['brokerFax'])) $brokerFaxNo = Strings::cleanPhoneNo(trim($_POST['brokerFax']));
if (isset($_POST['branchFax'])) $branchFaxNo = Strings::cleanPhoneNo(trim($_POST['branchFax']));
if (isset($_POST['lien1LenderFax'])) $lien1LenderFax = Strings::cleanPhoneNo(trim($_POST['lien1LenderFax']));
if (isset($_POST['lien1Bank1RepFax'])) $lien1Bank1RepFax = Strings::cleanPhoneNo(trim($_POST['lien1Bank1RepFax']));
if (isset($_POST['lien2LenderFax'])) $lien2LenderFax = Strings::cleanPhoneNo(trim($_POST['lien2LenderFax']));
if (isset($_POST['lien2Bank1RepFax'])) $lien2Bank1RepFax = Strings::cleanPhoneNo(trim($_POST['lien2Bank1RepFax']));
if (isset($_POST['faxReceiverName'])) $faxReceiverName = trim($_POST['faxReceiverName']);
if (isset($_POST['faxReceiverComp'])) $faxReceiverComp = trim($_POST['faxReceiverComp']);
if (isset($_POST['faxReceiverNo'])) $faxReceiverNo = Strings::cleanPhoneNo(trim($_POST['faxReceiverNo']));
if (isset($_POST['lenderEmail1'])) $lenderEmail1 = trim($_POST['lenderEmail1']);
if (isset($_POST['lenderEmail2'])) $lenderEmail2 = trim($_POST['lenderEmail2']);
if (isset($_POST['counselEmail'])) $counselEmail = trim($_POST['counselEmail']);
if (isset($_POST['counselAttorneyFax'])) $counselAttorneyFax = trim($_POST['counselAttorneyFax']);
// Allowed file size.

$ipArray['LMRID'] = $LMRId;

/*Upload the email attached document in db and emailAttach folder*/
$docArray = [];
$tempArray = [];
$emailAttachedDocIDs = [];
for ($i = 0; $i < count($_FILES['attachedFilesId']['name']); $i++) {
    $tmp_size = '';
    $tmp_name = '';
    $docName = '';
    $docCLType = '';

    $tmp_size = $_FILES['attachedFilesId']['size'][$i];
    $tmp_name = $_FILES['attachedFilesId']['tmp_name'][$i];

    if ($tmp_name != '' && $tmp_size > 0) {
        //save the filename
        $fileSrc_name = $_FILES['attachedFilesId']['name'][$i];
        $file_type = $_FILES['attachedFilesId']['type'][$i];
        $file_size = $_FILES['attachedFilesId']['size'][$i];
        $docName = $fileSrc_name;
        //$docCLType      = $_POST['docCLType_'.$PCMID];

        $tempArray = [
            'fileSrc_name' => $fileSrc_name,
            'tmp_name'     => $tmp_name,
            'file_type'    => $file_type,
            'docName'      => $docName,
            'docCategory'  => 'Attachment',
            'file_size'    => $file_size,
            //"docPCChecklistID" => $PCMID,
            //"docCLType"     => $docCLType
        ];
        $docArray[] = $tempArray;

    }
}

$tempDocName = '';
for ($m = 0; $m < count($docArray); $m++) {
    if (in_array($docArray[$m]['file_type'], $glMimeTypes)) {
        if ($docArray[$m]['file_size'] > CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED) {
            Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
        } else {
            if ($docArray[$m]['fileSrc_name'] != '') {
                $file_name = Strings::removeDisAllowedChars($docArray[$m]['fileSrc_name']);
                $info = pathinfo($file_name);
                $fileExtension = '';
                if (count($info) > 0) {
                    $fileExtension = $info['extension'];
                }
                $infoArray = [
                    'fileExtension'     => $fileExtension,
                    'LMRID'             => $LMRId,
                    'PCID'              => $PCID,
                    'userGroup'         => $userGroup,
                    'userNumber'        => $userNumber,
                    'borrowerLName'     => $borrowerLName,
                    'userName'          => $userName,
                    'uploadedBy'        => $userNumber,
                    'uploadingUserType' => $userGroup,
                    'docName'           => Strings::stripQuote($docArray[$m]['docName']),
                    'docCategory'       => $docArray[$m]['docCategory'],
                    'isSysNotesPrivate' => $isSysNotesPrivate,
                    'recordDate'        => $fileCreatedDate,
                    'oldFPCID'          => $PCID,
                    'tmpFileContent'    => base64_encode(FileStorage::getFile(dirname($docArray[$m]['tmp_name']) . '/' . basename($docArray[$m]['tmp_name']))),

                ];

                if ($m > 0) $tempDocName .= ', ';
                $tempDocName .= Strings::stripQuote($docArray[$m]['docName']);
                if (($m + 1) == count($docArray)) {
                    $infoArray['saveNotes'] = 1;
                    $infoArray['saveNotesDocs'] = $tempDocName;
                }
                $docId = 0;
                $res = '';

                $infoArray['emailAttachDocs'] = 1;

                $docId = saveFileDocument::getReport($infoArray);
                $emailAttachedDocIDs[] = $docId;

                if ($docId > 0) {
                    $infoArray['fileDocName'] = Strings::removeDisAllowedChars(Strings::stripQuote($docArray[$m]['docName'])) . '_' . Strings::removeDisAllowedChars($borrowerLName) . '_' . cypher::myEncryption($docId) . '.' . $fileExtension;
                    $res = UploadServer::upload($infoArray);
                }
            }
        }
    } else {
        Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
    }
}
/*Upload the email attached document in db and emailAttach folder End*/

$fileDetails = getFileInfo::getReport(['LMRId' => $LMRId]);
$PkgInfoArray = [];
if (count($fileDetails) > 0) {
    $PkgInfoArray = $fileDetails[$LMRId];
}
if (count($PkgInfoArray) > 0) {
    $clientInfoArray = $PkgInfoArray['LMRInfo'];
    $creditorInfoInfoArray = $PkgInfoArray['creditorInfo'];
    $PCInfo = $PkgInfoArray['PCInfo'];
}
if (count($clientInfoArray) > 0) {
    $borrowerFName = $clientInfoArray['borrowerFName'];
    $borrowerLName = $clientInfoArray['borrowerLName'];
    $clientName = ucwords($borrowerFName . ' ' . $borrowerLName);
    $oldFPCID = trim($clientInfoArray['oldFPCID']);
}
if (count($PCInfo) > 0) {
    $isSysNotesPrivate = trim($PCInfo['isSysNotesPrivate']);
}
$tempDocArray = getSendFileDoc::getReport($ipArray);
$tempDocInfoArray = [];
if (array_key_exists('docArray', $tempDocArray)) $tempDocInfoArray = $tempDocArray['docArray'];
/** Fetch all Branch services requested **/
$tempDocKeyInfoArray = array_keys($tempDocInfoArray);
if (trim($resendTxnID) != '') {
    $inputArray['PKGID'] = $selRPKGID;
    $selectedPkgArray = preg_split('/[, ]+/', $selRPKGID);
} else {
    $inputArray['PKGID'] = $selPKGID;
}
if ($selAttachedPkg != '') {
    if ((count($selectedPkgArray) > 0) || trim($resendTxnID) != '') {
        $inputArray['PKGID'] = $inputArray['PKGID'] . ',' . $selAttachedPkg;
    } else {
        $inputArray['PKGID'] = $selAttachedPkg;
    }
    $selAttachedPkgArray = preg_split('/[, ]+/', $selAttachedPkg);
}
if ((trim($selRPKGID) != '' && trim($selRPKGID) > 0) || (trim($selPKGID) != '' && trim($selPKGID) > 0) || (trim($selAttachedPkg) != '' && trim($selAttachedPkg) > 0)) $pkgAllSelectedArray = getLibPackage::getReport($inputArray);
$ip = [];
$CustomDocInfoArray = [];
$customDocKeyArray = [];
if (count($selectedCustomPkgArray) > 0 || count($selectedCustomWordPkgArray) > 0) {
    $ip['PCID'] = $PCID;

    $CustomDocInfoArray = sendPCCustomDocsContent::getReport($ip);
    if (count($CustomDocInfoArray) > 0) $customDocKeyArray = array_keys($CustomDocInfoArray);
}

if (count($selectedBinderPkgArray) > 0) {
    $iArray = ['LMRId' => $LMRId];
    $binderDocsArray = getBinderDocument::getReport($iArray);
    $binderDocsKeysArray = [];
    if (count($binderDocsArray) > 0) $binderDocsKeysArray = array_keys($binderDocsArray);
    for ($bd = 0; $bd < count($binderDocsKeysArray); $bd++) {
        $dId = 0;
        $eSignDocId = 0;
        if (array_key_exists('eSignDoc', $binderDocsArray[$binderDocsKeysArray[$bd]])) {
            $eSignDocId = $binderDocsArray[$binderDocsKeysArray[$bd]]['eSignDoc']['docIds'];
            if ($eSignDocId != '') {
                if ($d1 > 0) $eSignDocIds .= ',';
                $eSignDocIds .= $eSignDocId;
                $d1++;
            }
        }
        if (array_key_exists('userDoc', $binderDocsArray[$binderDocsKeysArray[$bd]])) {
            $dId = $binderDocsArray[$binderDocsKeysArray[$bd]]['userDoc']['docIds'];
            if ($dId != '') {
                if ($d2 > 0) $dIds .= ',';
                $dIds .= $dId;
                $d2++;
            }
        }
    }
    if ($d1 > 0 || $d2 > 0) {
        $inArray['dIds'] = $dIds;
        $inArray['eSignDocIds'] = $eSignDocIds;
        $inArray['LMRId'] = $LMRId;

        $binderDocsNameArray = getBinderDocName::getReport($inArray);
        if (count($binderDocsNameArray) > 0) {
            $esignedDocsArray = $binderDocsNameArray['result1'];
            $uploadDocsArray = $binderDocsNameArray['result2'];
        }
    }
}


$tempFileCreatedDate = '';
$tempFileCreatedDate = str_replace('-', '', $fileCreatedDate);
$dest = $oldFPCID . '/' . date('Y', strtotime($tempFileCreatedDate)) . '/' . date('m', strtotime($tempFileCreatedDate)) . '/' . date('d', strtotime($tempFileCreatedDate));
for ($s1 = 0; $s1 < count($selectedPkgArray); $s1++) {
    $PKGID = 0;
    $PKGID = trim($selectedPkgArray[$s1]);
    if (array_key_exists($PKGID, $pkgAllSelectedArray)) {
        if (trim($pkgAllSelectedArray[$PKGID]['packageType']) != 'static') {
            $inArray['userNumber'] = $userNumber;
            $inArray['userGroup'] = $userGroup;
            $inArray['LMRId'] = $LMRId;
            $inArray['pkgID'] = $PKGID;
            $inArray['responseId'] = $responseId;
            $inArray['printOutput'] = 'n';
            $inArray['actions'] = 'Emailed';
            $inArray['attach'] = 'y';
            $inArray['PCID'] = $PCID;
            generateAppropriatePkg($inArray);
        }
    }
}
$ndnCnt9 = 0;
for ($s = 0; $s < count($attachedCreditorPkgArray); $s++) {
    $inArray['userNumber'] = $userNumber;
    $inArray['userGroup'] = $userGroup;
    $inArray['LMRId'] = $LMRId;
    $inArray['pkgID'] = CONST_DEBTVAL_DOCID;
    $inArray['responseId'] = $responseId;
    $inArray['printOutput'] = 'n';
    $inArray['actions'] = 'Emailed';
    $inArray['attach'] = 'y';
    $inArray['CRID'] = $attachedCreditorPkgArray[$s];
    generateAppropriatePkg($inArray);
    $creditorName = '';
    for ($i = 0; $i < count($creditorInfoInfoArray); $i++) {
        if ($creditorInfoInfoArray[$i]['CIID'] == $attachedCreditorPkgArray[$s]) {
            $creditorName = ucwords($creditorInfoInfoArray[$i]['creditorName']);
            break;
        }
    }
    if ($faxPop == '1') {
        $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/'
            . Strings::removeDisAllowedChars(stripslashes('Debt Validation - ' . $creditorName)) . '_'
            . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_'
            . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption(CONST_DEBTVAL_DOCID)) . '_'
            . trim(cypher::myEncryption($attachedCreditorPkgArray[$s])) . '.pdf';
    } else {
        $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/'
            . Strings::removeDisAllowedChars(stripslashes('Debt Validation - ' . $creditorName)) . '_'
            . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_'
            . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption(CONST_DEBTVAL_DOCID)) . '_'
            . trim(cypher::myEncryption($attachedCreditorPkgArray[$s])) . '.pdf{Debt Validation - '
            . Strings::removeDisAllowedChars(stripslashes($creditorName)) . '.pdf';
        if ($ndnCnt9 > 0) {
            $notesDocNames .= ', ';
        }
        $notesDocNames .= trim('Debt Validation - ' . $creditorName);
        $ndnCnt9++;
    }
    unset($inArray['CRID']);
}
$ndnCnt10 = 0;
for ($s = 0; $s < count($selectedCreditorEsignPkgArray); $s++) {
    $signedDocInArray['responseId'] = $responseId;
    $signedDocInArray['LMRId'] = $LMRId;
    $signedDocInArray['packageId'] = CONST_DEBTVAL_DOCID;
    $signedDocInArray['UID'] = $userNumber;
    $signedDocInArray['UType'] = $userGroup;
    $signedDocInArray['userName'] = $userName;
    $signedDocInArray['savePdf'] = 'yes';
    $signedDocInArray['pkgType'] = 'LibPack';
    $signedDocInArray['CRID'] = trim($selectedCreditorEsignPkgArray[$s]);
    $creditorName = '';
    for ($i = 0; $i < count($creditorInfoInfoArray); $i++) {
        if ($creditorInfoInfoArray[$i]['CIID'] == $selectedCreditorEsignPkgArray[$s]) {
            $creditorName = ucwords($creditorInfoInfoArray[$i]['creditorName']);
            break;
        }
    }
//        $signedDocInArray['resendTxnID'] = $resendTxnID;
    $eSignedResultArray = trustDocWrapper::getReport($signedDocInArray);
    $eSignedTxnID = 0;
    $eSignedActionID = 0;
    if (count($eSignedResultArray) > 0) {
        $eSignedTxnID = trim($eSignedResultArray['txnID']);
        $eSignedActionID = trim($eSignedResultArray['actionID']);
        $res = $eSignedResultArray['res'];
        if (count($res) > 0) {
            $rsCnt = trim($res['rsCnt']);
            $esignDoc = trim($res['esignDoc']);
        }
    }
    $creditorESignedDoc[trim($selectedCreditorEsignPkgArray[$s])]['txnID'] = $eSignedTxnID;
    $creditorESignedDoc[trim($selectedCreditorEsignPkgArray[$s])]['pkgName'] = trim('Debt Validation - ' . $creditorName);
    $creditorESignedDoc[trim($selectedCreditorEsignPkgArray[$s])]['PKGID'] = CONST_DEBTVAL_DOCID;
    $eSignedActionArray[$eSignedActionID] = $eSignedActionID;
    if ($ndnCnt10 > 0) {
        $notesDocNames .= ', ';
    }
    $notesDocNames .= trim('Debt Validation - ' . $creditorName);
    $ndnCnt10++;
}
$ndnCnt8 = 0;
$binderDocMsg = '';
if (count($selectedBinderPkgArray) > 0) {
    for ($s9 = 0; $s9 < count($selectedBinderPkgArray); $s9++) {
        $BID = 0;
        $BID = trim($selectedBinderPkgArray[$s9]);
        if (array_key_exists($BID, $binderDocsArray)) {
            $docIds = 0;
            $binderDocName = '';
            $binderName = '';
            $binderEsignName = '';
            if (array_key_exists('eSignDoc', $binderDocsArray[$BID])) {
                $binderEsignName = $binderDocsArray[$BID]['eSignDoc']['binderName'];
            }
            if (array_key_exists('userDoc', $binderDocsArray[$BID])) {
                $binderDocName = $binderDocsArray[$BID]['userDoc']['binderName'];
            }
            if (array_key_exists('autoGDoc', $binderDocsArray[$BID])) { // Binder doc name not added notes previously.
                $binderDocName = $binderDocsArray[$BID]['autoGDoc']['binderName'];
            }
            if ($binderEsignName == '') {
                $binderName = $binderDocName;
            } else {
                $binderName = $binderEsignName;
            }

            $fileDtlArray['file_path']['BD']['file_path' . $s9] = $dest . '/' . $LMRId . '/binder/bd_' . cypher::myEncryption($BID) . '.pdf';
            /** Getting file path for the binder upload docs**/
            $uploadDocUrl = CONST_SITE_URL . 'binder/binderController.php?file=' . cypher::myEncryption($binderName) . '&lId=' . cypher::myEncryption($LMRId) . '&amp;BID=' . cypher::myEncryption($BID);
            $fileDtlArray['url']['BD']['url_' . $s9] = "<p><a rel=\"nofollow\" target=\"_blank\" href=" . $uploadDocUrl . '>Click to view ' . ucwords($binderName) . '</a></p>';
            /** Getting binder upload doc url **/

            if ($faxPop == '1') {
                $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . CONST_FOLDER_BINDER . 'bd_' . cypher::myEncryption($BID) . '.pdf';
            } else {
                $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . CONST_FOLDER_BINDER . 'bd_' . cypher::myEncryption($BID) . '.pdf{' . $binderName . '.pdf';
                $fileDtlArray['attachment']['BD'][] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . CONST_FOLDER_BINDER . 'bd_' . cypher::myEncryption($BID) . '.pdf{' . $binderName . '.pdf';
            }
            if ($ndnCnt8 > 0) {
                $notesDocNames .= ', ';
            }
            $notesDocNames .= trim($binderName);
            $ndnCnt8++;
        }
    }
    $fileDtlArray['file_path']['BD']['cnt'] = count($selectedBinderPkgArray);
}

if (count($pkgAllSelectedArray) > 0) {
    for ($s5 = 0; $s5 < count($selAttachedPkgArray); $s5++) {
        $PKGID = 0;
        $PKGID = trim($selAttachedPkgArray[$s5]);
        if (array_key_exists($PKGID, $pkgAllSelectedArray)) {
            $pkgArray = [];
            $docId = 0;
            $docName = '';
            $esign = '';
            $actions = '';
            $res = [];
            $esignDoc = '';
            $rsCnt = 0;
            $packageType = '';
            $selectedPkgUrl = '';
            $pkgArray = $pkgAllSelectedArray[$PKGID];
            $docName = $pkgArray['pkgName'];
            $docId = $pkgArray['PKGID'];
            $esign = $pkgArray['esign'];
            $packageType = $pkgArray['packageType'];
            $selectedPkgUrl = trim($pkgArray['filePath']);
            if ($packageType != 'static') {
                $inArray['userNumber'] = $userNumber;
                $inArray['userGroup'] = $userGroup;
                $inArray['LMRId'] = $LMRId;
                $inArray['pkgID'] = $docId;
                $inArray['responseId'] = $responseId;
                $inArray['printOutput'] = 'n';
                $inArray['actions'] = 'Emailed';
                $inArray['attach'] = 'y';
                $inArray['PCID'] = $PCID;
                generateAppropriatePkg($inArray);
            }
            if ($faxPop == '1') {
                if ($packageType == 'static') $attachmentArray[] = CONST_FOLDER_LIB_DOC . $selectedPkgUrl;
                else $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . Strings::removeDisAllowedChars(stripslashes($docName)) . '_' . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($docId)) . '.pdf';
            } else {
                $fname = Strings::removeDisAllowedChars($clientName) . '-' . Strings::removeDisAllowedChars($docName) . '.pdf';
                $uploadDocUrl = CONST_SITE_URL . 'package/pkgController.php?file=' . $fname . '&amp;rId=' . cypher::myEncryption($responseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($docId) . '&opt=sample';
                $dynamuURL .= "<p><a rel=\"nofollow\" target=\"_blank\" href=" . $uploadDocUrl . '>Click to view ' . ucwords($docName) . '</a></p>';
                /** Getting autogenerated doc url **/
            }
            if ($ndnCnt8 > 0) {
                $notesDocNames .= ', ';
            }
            $notesDocNames .= trim($docName);
            $ndnCnt8++;
        }
    }
}


// Selected Doc Non Esign Documents//
if (count($selectedUpDocArray) > 0) {
    $updocInfo = [];
    for ($s9 = 0; $s9 < count($selectedUpDocArray); $s9++) {
        $DID = 0;
        $DID = trim($selectedUpDocArray[$s9]);


        if (array_key_exists($DID, $tempDocInfoArray)) {
            for ($h = 0; $h < count($tempDocInfoArray); $h++) {
                $docID = 0;
                $docName = '';
                $displayDocName = '';
                $dType = '';
                $t1 = $tempDocKeyInfoArray[$h];
                $chkOpt = '';
                $fileType = '';
                $docUrl = '';
                if ($DID == $t1) {
                    $docName = $tempDocInfoArray[$DID][0]['docName'];
                    $displayDocName = $tempDocInfoArray[$DID][0]['displayDocName'];
                    $docID = $tempDocInfoArray[$DID][0]['docID'];
                    $fileType = strtolower($tempDocInfoArray[$DID][0]['fileType']);
                    $dType = $tempDocInfoArray[$DID][0]['dType'];
                    $docUrl = $tempDocInfoArray[$DID][0]['docUrl'];
                    $docCategory = $tempDocInfoArray[$DID][0]['docCategory'];
                    $fp = $dest . '/' . $LMRId . '/';

                    if ($docCategory == 'Appraisal1' || $docCategory == 'Appraisal2'
                        || $docCategory == 'BPO1' || $docCategory == 'BPO2'
                        || $docCategory == 'BPO3' || $docCategory == 'Title Report'
                        || $docCategory == 'Property Insurance Coverage1'
                        || $docCategory == 'Property Insurance Coverage2'
                        || $docCategory == 'Property Insurance Coverage3'
                        || $docCategory == 'Property Insurance Coverage4'
                        || $docCategory == 'Property Insurance Coverage5'
                        || $docCategory == 'Property Insurance Coverage'
                        || $docCategory == 'AVM1' || $docCategory == 'AVM2'
                        || $docCategory == 'AVM3') {
                        $fp .= CONST_FOLDER_PROPERTY;
                    } else {
                        $fp .= CONST_UPLOAD_DOC_FOLDER;
                    }
                    $fp .= $docName;

                    $fileDtlArray['file_path']['UD']['file_path' . $s9] = $fp;
                    /** Getting file path **/
                    $fiUsl = str_replace($docName, '', $fp);
                    $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fiUsl) . '&opt=enc';
                    /** Getting selected upload doc url **/
                    $fileDtlArray['url']['UD']['url_' . $s9] = "<p><a rel=\"nofollow\" target=\"_blank\" href=" . $uploadDocUrl . '>Click to view ' . ucwords($displayDocName) . '</a></p>';
                    /** Getting url **/
                    if (trim($displayDocName) == '') $displayDocName = $docName;
                    /**
                     * Description  : Cloud Documents Integration in https://www.theloanpost.com
                     **/
                    if (array_key_exists($dType, $glCloudDocType)) {
                        $cloudDocArray[] = ['displayDocName' => $displayDocName, 'docUrl' => $docUrl];
                        if ($ndnCnt8 > 0) {
                            $notesDocNames .= ', ';
                        }
                        $notesDocNames .= trim($displayDocName);
                        $ndnCnt8++;
                    } else {
                        if ($faxPop == '1') {
                            if (in_array($fileType, $fileTypesAllowedToFax)) {
                                $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . CONST_UPLOAD_DOC_FOLDER . $docName;
                                if ($ndnCnt8 > 0) {
                                    $notesDocNames .= ', ';
                                }
                                $notesDocNames .= trim($displayDocName);
                                $ndnCnt8++;
                            }
                        } else {
                            //array_push($updocInfo, $docName);
                            $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . CONST_UPLOAD_DOC_FOLDER . $docName . '{' . $displayDocName . '.' . $fileType;
                            $fileDtlArray['attachment']['UD'][] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . CONST_UPLOAD_DOC_FOLDER . $docName . '{' . $displayDocName . '.' . $fileType;
                            /** Getting attachment array **/
                            if ($ndnCnt8 > 0) {
                                $notesDocNames .= ', ';
                            }
                            $notesDocNames .= trim($displayDocName);
                            $ndnCnt8++;
                        }
                    }
                }
            }
        }
    }
    $fileDtlArray['file_path']['UD']['cnt'] = count($selectedUpDocArray);
}
/*Email Attachment Doc*/
if (count($emailAttachedDocIDs) > 0) {
    $emailAttachedDocInfo = [];
    for ($e = 0; $e < count($emailAttachedDocIDs); $e++) {
        $DID = 0;
        $DID = trim($emailAttachedDocIDs[$e]);


        if (array_key_exists($DID, $tempDocInfoArray)) {
            for ($h = 0; $h < count($tempDocInfoArray); $h++) {
                $docID = 0;
                $docName = '';
                $displayDocName = '';
                $dType = '';
                $t1 = $tempDocKeyInfoArray[$h];
                $chkOpt = '';
                $fileType = '';
                $docUrl = '';
                if ($DID == $t1) {
                    $docName = $tempDocInfoArray[$DID][0]['docName'];
                    $displayDocName = $tempDocInfoArray[$DID][0]['displayDocName'];
                    $docID = $tempDocInfoArray[$DID][0]['docID'];
                    $fileType = strtolower($tempDocInfoArray[$DID][0]['fileType']);
                    $dType = $tempDocInfoArray[$DID][0]['dType'];
                    $docUrl = $tempDocInfoArray[$DID][0]['docUrl'];
                    $docCategory = $tempDocInfoArray[$DID][0]['docCategory'];
                    $fp = $dest . '/' . $LMRId . '/';
                    $fp .= 'emailAttach/';
                    $fp .= $docName;

                    $fileDtlArray['file_path']['AD']['file_path' . $e] = $fp;
                    /** Getting file path **/
                    $fiUsl = str_replace($docName, '', $fp);
                    $attachedDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fiUsl) . '&opt=enc';
                    /** Getting selected upload doc url **/
                    $fileDtlArray['url']['AD']['url_' . $e] = "<p><a rel=\"nofollow\" target=\"_blank\" href=" . $attachedDocUrl . '>Click to view ' . ucwords($displayDocName) . '</a></p>';
                    /** Getting url **/
                    if (trim($displayDocName) == '') $displayDocName = $docName;
                    /**
                     * Description  : Cloud Documents Integration in https://www.theloanpost.com
                     **/
                    if (array_key_exists($dType, $glCloudDocType)) {
                        $cloudDocArray[] = ['displayDocName' => $displayDocName, 'docUrl' => $docUrl];
                        if ($ndnCnt8 > 0) {
                            $notesDocNames .= ', ';
                        }
                        $notesDocNames .= trim($displayDocName);
                        $ndnCnt8++;
                    } else {
                        if ($faxPop == '1') {
                            if (in_array($fileType, $fileTypesAllowedToFax)) {
                                $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/emailAttach/' . $docName;
                                if ($ndnCnt8 > 0) {
                                    $notesDocNames .= ', ';
                                }
                                $notesDocNames .= trim($displayDocName);
                                $ndnCnt8++;
                            }
                        } else {
                            //array_push($updocInfo, $docName);
                            $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/emailAttach/' . $docName . '{' . $displayDocName . '.' . $fileType;
                            $fileDtlArray['attachment']['AD'][] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/emailAttach/' . $docName . '{' . $displayDocName . '.' . $fileType;
                            /** Getting attachment array **/
                            if ($ndnCnt8 > 0) {
                                $notesDocNames .= ', ';
                            }
                            $notesDocNames .= trim($displayDocName);
                            $ndnCnt8++;
                        }
                    }
                }
            }
        }
    }
    $fileDtlArray['file_path']['AD']['cnt'] = count($emailAttachedDocIDs);
}

$eSignCreditorNameArray = [];


if ($LMRId > 0 && trim($selTxnID != '')) {
    $inDocArray['actions'] = 'e-signed';
    $inDocArray['LMRID'] = $LMRId;
    $inDocArray['txnIDs'] = "'" . implode("','", $selectedETxnIDArray) . "'";
    $inDocArray['keyNeeded'] = 'txnID';
    $inDocArray['fStatus'] = 'All';
    $trustDocArray = getTrustDocDetails2::getReport($inDocArray);
    $trustDocKeyArray = array_keys($trustDocArray);
    $eSignCreditorNameArray = getMyCreditorName::getReport(['txnID' => "'" . implode("','", $selectedETxnIDArray) . "'"]);
}
$inputArray = [
    'executiveId'       => $executiveId,
    'attachWithEmail'   => '',
    'publishInPQPortal' => '',
    'publishDoument'    => '1',

];
$branchDocArray = getBranchUploadedDoc::getReport($inputArray);
$branchDocKeyArray = array_keys($branchDocArray);
$myInputArray = ['PCID' => $PCID, 'key' => 'key'];
$pcDocArray = getPCUploadDocs::getReport($myInputArray);
$pcDocKeyArray = array_keys($pcDocArray);

if (count($pkgAllSelectedArray) > 0) {
    for ($s5 = 0; $s5 < count($selectedPkgArray); $s5++) {
        $PKGID = 0;
        $pkgArray = [];
        $docId = 0;
        $docName = '';
        $esign = '';
        $actions = '';
        $res = [];
        $esignDoc = 0;
        $rsCnt = 0;
        $eSignedResultArray = [];
        $PKGID = trim($selectedPkgArray[$s5]);
        if (array_key_exists($PKGID, $pkgAllSelectedArray)) {
            $pkgArray = [];
            $docId = 0;
            $docName = '';
            $esign = '';
            $actions = '';
            $res = [];
            $esignDoc = 0;
            $rsCnt = 0;
            $packageType = '';
            $selectedPkgUrl = '';
            $pkgArray = $pkgAllSelectedArray[$PKGID];
            $docName = $pkgArray['pkgName'];
            $docId = $pkgArray['PKGID'];
            $esign = $pkgArray['esign'];
            $packageType = $pkgArray['packageType'];
            $selectedPkgUrl = trim($pkgArray['filePath']);
            if ($faxPop == '1') {
                $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . Strings::removeDisAllowedChars(stripslashes($docName)) . '_' . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($docId)) . '.pdf';
            } else {
                if ($esign == '1') {
                    $signedDocInArray['responseId'] = $responseId;
                    $signedDocInArray['LMRId'] = $LMRId;
                    $signedDocInArray['packageId'] = $docId;
                    $signedDocInArray['UID'] = $userNumber;
                    $signedDocInArray['UType'] = $userGroup;
                    $signedDocInArray['userName'] = $userName;
                    $signedDocInArray['savePdf'] = 'yes';
                    $signedDocInArray['pkgType'] = 'LibPack';
                    $signedDocInArray['resendTxnID'] = $resendTxnID;
                    $eSignedResultArray = trustDocWrapper::getReport($signedDocInArray);
                    $eSignedTxnID = 0;
                    $eSignedActionID = 0;
                    if (count($eSignedResultArray) > 0) {
                        $eSignedTxnID = trim($eSignedResultArray['txnID']);
                        $eSignedActionID = trim($eSignedResultArray['actionID']);
                        $res = $eSignedResultArray['res'];
                        if (count($res) > 0) {
                            $rsCnt = trim($res['rsCnt']);
                            $esignDoc = trim($res['esignDoc']);
                        }
                    }
                    $eSignedReadyDocArray[$docId]['txnID'] = $eSignedTxnID;
                    $eSignedActionArray[$eSignedActionID] = $eSignedActionID;
                } else if ($esign == '0') {
                    if ($packageType == 'static') {
                        $path_info = pathinfo(CONST_PATH_LIBS_DOC . $selectedPkgUrl);
                        $fileExtension = '';
                        if (count($path_info) > 0) {
                            if (array_key_exists('extension', $path_info)) $fileExtension = trim($path_info['extension']);
                        }
                        $attachmentArray[] = CONST_FOLDER_LIB_DOC . $selectedPkgUrl . '{' . $docName . '.' . $fileExtension;
                    } else {
                        $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . Strings::removeDisAllowedChars(stripslashes($docName)) . '_' . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($docId)) . '.pdf{' . trim($docName) . '.pdf';
                    }
                }
            }
            if ($rsCnt > 0) {
                if ($esignDoc > 0) {
                    if ($ndnCnt1 > 0) $notesDocNames1 .= ', ';
                    $notesDocNames1 .= trim($docName);
                    $ndnCnt1++;
                    $msg = ' is RESENT for e-sign. The earlier e-signed document is deleted.';
                    /** Re-sent the document already signed **/
                } else {
                    if ($ndnCnt2 > 0) {
                        $notesDocNames2 .= ', ';
                    }
                    $notesDocNames2 .= trim($docName);
                    $ndnCnt2++;
                }
            } else if (trim($resendTxnID) != '') {
                if ($ndnCnt3 > 0) $notesDocNames3 .= ', ';
                $notesDocNames3 .= trim($docName);
                $ndnCnt3++;
            } else {
                if ($ndnCnt8 > 0) {
                    $notesDocNames .= ', ';
                }
                $notesDocNames .= trim($docName);
                $ndnCnt8++;
            }
        }
    }
}


$bDocMsg = '';
if (count($selectedBranchPkgArray) > 0) {
    for ($s6 = 0; $s6 < count($selectedBranchPkgArray); $s6++) {
        $BPKGID = 0;
        $BPKGID = trim($selectedBranchPkgArray[$s6]);
        if (array_key_exists($BPKGID, $branchDocArray)) {
            $tempDArray = [];
            $tempDArray = $branchDocArray[$BPKGID];
            for ($f = 0; $f < count($tempDArray); $f++) {
                $bdocId = 0;
                $bDocName = '';
                $displayDocName = '';
                $chkOpt = '';
                $fileType = '';
                $bDocName = $tempDArray[$f]['docName'];
                $bdocId = $tempDArray[$f]['docId'];
                $displayDocName = $tempDArray[$f]['displayDocName'];
                $fileType = strtolower($tempDArray[$f]['fileType']);
                if (trim($displayDocName) == '') $displayDocName = $bDocName;
                $fileDtlArray['file_path']['BUD']['file_path' . $s6] = $bDocName;
                /** Getting branch uploaded doc path **/
                $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($bDocName) . '&fd=' . cypher::myEncryption(CONST_FOLDER_BRANCH_DOC) . '&opt=enc';
                $fileDtlArray['url']['BUD']['url_' . $s6] = "<p><a rel=\"nofollow\" target=\"_blank\" href=" . $uploadDocUrl . '>Click to view ' . ucwords($bDocName) . '</a></p>';
                /** Getting branch upload doc url **/
                if ($faxPop == '1') {
                    if (in_array($fileType, $fileTypesAllowedToFax)) {
                        $attachmentArray[] = CONST_FOLDER_BRANCH_DOC . $bDocName . '';
                        if ($ndnCnt8 > 0) {
                            $notesDocNames .= ', ';
                        }
                        $notesDocNames .= trim($displayDocName);
                        $ndnCnt8++;
                    }
                } else {
                    $attachmentArray[] = CONST_FOLDER_BRANCH_DOC . $bDocName . '{' . $displayDocName . '.' . $fileType;
                    $fileDtlArray['attachment']['BUD'][] = CONST_FOLDER_BRANCH_DOC . $bDocName . '{' . $displayDocName . '.' . $fileType;
                    if ($ndnCnt8 > 0) {
                        $notesDocNames .= ', ';
                    }
                    $notesDocNames .= trim($displayDocName);
                    $ndnCnt8++;
                }
            }
        }
    }
    $fileDtlArray['file_path']['BUD']['cnt'] = count($selectedBranchPkgArray);
}
/* PC Upload Doc */
if (count($selectedPCPkgArray) > 0) {
    for ($s6 = 0; $s6 < count($selectedPCPkgArray); $s6++) {
        $PCPKGID = 0;
        $PCPKGID = trim($selectedPCPkgArray[$s6]);
        if (array_key_exists($PCPKGID, $pcDocArray)) {
            $tempDArray = [];
            $tempDArray = $pcDocArray[$PCPKGID];
            for ($f = 0; $f < count($tempDArray); $f++) {
                $bdocId = 0;
                $bDocName = '';
                $displayDocName = '';
                $chkOpt = '';
                $fileType = '';
                $pcDocName = $tempDArray[$f]['docName'];
                $pcdocId = $tempDArray[$f]['DID'];
                $displayDocName = $tempDArray[$f]['displayDocName'];
                $fileType = strtolower($tempDArray[$f]['fileType']);
                if (trim($displayDocName) == '') $displayDocName = $pcDocName;
                $fileDtlArray['file_path']['PUD']['file_path' . $s6] = $oldFPCID . '/' . $pcDocName;
                /** Getting pc uploaded doc path **/
                $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($pcDocName) . '&fd=' . cypher::myEncryption(CONST_PATH_PC_UP_DOC . $PCID) . '&opt=enc';
                $fileDtlArray['url']['PUD']['url_' . $s6] = "<p><a rel=\"nofollow\" target=\"_blank\" href=" . $uploadDocUrl . '>Click to view ' . ucwords($pcDocName) . '</a></p>';
                /** Getting pc uploaded doc url **/
                if ($faxPop == '1') {
                    if (in_array($fileType, $fileTypesAllowedToFax)) {
                        $attachmentArray[] = CONST_PATH_PC_UP_DOC . $PCID . '/' . $pcDocName;
                        if ($ndnCnt8 > 0) {
                            $notesDocNames .= ', ';
                        }
                        $notesDocNames .= trim($displayDocName);
                        $ndnCnt8++;
                    }
                } else {
                    $attachmentArray[] = CONST_PATH_PC_UP_DOC . $PCID . '/' . $pcDocName . '{' . $displayDocName . '.' . $fileType;
                    $fileDtlArray['attachment']['PUD'][] = CONST_PATH_PC_UP_DOC . $PCID . '/' . $pcDocName . '{' . $displayDocName . '.' . $fileType;
                    if ($ndnCnt8 > 0) {
                        $notesDocNames .= ', ';
                    }
                    $notesDocNames .= trim($displayDocName);
                    $ndnCnt8++;
                }
            }
        }
    }
    $fileDtlArray['file_path']['PUD']['cnt'] = count($selectedPCPkgArray);
}
/* PC Upload Doc */
$eDocMsg = '';
$esingFN = '';
if (count($selectedETxnIDArray) > 0) {
    for ($s7 = 0; $s7 < count($selectedETxnIDArray); $s7++) {
        $ETxnID = 0;
        $ETxnID = trim($selectedETxnIDArray[$s7]);
        if (array_key_exists($ETxnID, $trustDocArray)) {
            $tempTrustArray = [];
            $tempTrustArray = $trustDocArray[$ETxnID];
            for ($f = 0; $f < count($tempTrustArray); $f++) {
                $edocId = 0;
                $eDocName = '';
                $fldName = '';
                //if ($ETxnID == $t2) {
                $eDocName = $tempTrustArray[$f]['packageName'];
                $edocId = $tempTrustArray[$f]['docID'];
                $fldName = 'trustDocs/' . cypher::myEncryption($LMRId);
                if (count($eSignCreditorNameArray) > 0) {
                    if (array_key_exists($ETxnID, $eSignCreditorNameArray)) {
                        $eDocName .= ' - ' . ucwords(trim($eSignCreditorNameArray[$ETxnID]['creditorName']));
                    }
                }
                $fileDtlArray['file_path']['ED']['file_path' . $s7] = cypher::myEncryption($LMRId) . '/' . $ETxnID . '.pdf';
                /** Getting Esigned doc path **/
                $trustUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($ETxnID . '.pdf') . '&fd=' . cypher::myEncryption($fldName) . '&opt=enc';
                $fileDtlArray['url']['ED']['url_' . $s7] = "<p><a rel=\"nofollow\" target=\"_blank\" href=" . $trustUrl . '>Click to view ' . ucwords($eDocName) . '</a></p>';
                /** Getting Esigned uploaded doc url **/
                if ($faxPop == '1') {
                    $attachmentArray[] = CONST_PATH_TRUST_DOCS . trim(cypher::myEncryption($LMRId)) . '/' . trim($ETxnID) . '.pdf';
                } else {
                    $attachmentArray[] = CONST_PATH_TRUST_DOCS . trim(cypher::myEncryption($LMRId)) . '/' . trim($ETxnID) . '.pdf{' . $eDocName . '.pdf';
                }
                if ($ndnCnt8 > 0) {
                    $notesDocNames .= ', ';
                }
                $notesDocNames .= trim($eDocName);
                $ndnCnt8++;
            }
        }
    }
}
$fileDtlArray['file_path']['ED']['cnt'] = count($selectedETxnIDArray);

$ndnCnt7 = 0;
if (count($selectedCustomPkgArray) > 0) {
    for ($s8 = 0; $s8 < count($selectedCustomPkgArray); $s8++) {

        $CPKGID = 0;
        $CPKGID = trim($selectedCustomPkgArray[$s8]);

        if (count($CustomDocInfoArray) > 0) {
            if (array_key_exists($CPKGID, $CustomDocInfoArray)) {
                for ($h = 0; $h < count($CustomDocInfoArray); $h++) {
                    $CCID = 0;
                    $t1 = $customDocKeyArray[$h];
                    $chkOpt = '';
                    if ($CPKGID == $t1) {
                        /* Get Dynamic Tag Values.. */
                        $infoArray['LMRID'] = $LMRId;
                        $infoArray['typeOpt'] = 'EmailContent';

                        $customTempInfoArray = getAllDynamicDatas::getReport($infoArray);
                        $customTempInfoArray['LMRID'] = $LMRId;
                        $customTempInfoArray['DOCCONTENT'] = $CustomDocInfoArray[$CPKGID][0]['docTitle'];
                        $customTempInfoArray['LMRID'] = $LMRId;
                        $packageName = SubstituteDynamicTagsForEmail::getReport($customTempInfoArray);
                        $packageName = Strings::processEmailString($packageName);
                        $docTitle = $CustomDocInfoArray[$CPKGID][0]['docTitle'];
                        $CCID = $CustomDocInfoArray[$CPKGID][0]['CCID'];

                        if ($faxPop == '1') {
                            $attachmentArray[] = CONST_PATH_PC_UP_DOC . $PCID . '/' . Strings::removeDisAllowedChars(stripslashes($docTitle)) . '_' . Strings::removeDisAllowedChars(stripslashes($borrowerLName)) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($CCID)) . '.pdf';
                        }

                        $custFN = '/' . Strings::removeDisAllowedChars(stripslashes($docTitle)) . '_' . Strings::removeDisAllowedChars(stripslashes($borrowerLName)) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($CCID)) . '.pdf';
                        /**
                         * Google docs attachments
                         */
                        if ($CustomDocInfoArray[$CPKGID][0]['googleDocsId'] != '') {
                            $fname = Strings::removeDisAllowedChars($borrowerLName) . '-' . Strings::removeDisAllowedChars($packageName);
                            // luid = Logged In User ID.
                            // lur	= Logged In User Role.
                            // lugr	= Logged In User Group.
                            $url = CONST_CRON_SERVER_URL . 'backoffice/loan/create_custom_doc?file=' . $fname . '&amp;rId=' . cypher::myEncryption($responseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($CCID) . '&amp;opt=1lien&amp;luid=' . cypher::myEncryption(Strings::GetSess('userNumber')) . '&amp;lur=' . cypher::myEncryption(Strings::GetSess('userRole')) . '&amp;lugr=' . cypher::myEncryption(Strings::GetSess('userGroup'));

                            $custDocMsg .= "<p><a rel=\"nofollow\" target=\"_blank\" href=\"" . $url . "\">Click to view " . ucwords($packageName) . '</a></p>';
                        } else {
                            $custDocMsg .= "<p><a rel=\"nofollow\" target=\"_blank\" href=\"" . CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($custFN) . '&fd=' . cypher::myEncryption(CONST_PATH_PC_UP_DOC . $PCID . '/') . "&opt=enc\">Click to view " . ucwords($packageName) . '</a></p>';
                        }
                        if ($ndnCnt8 > 0) $notesDocNames .= ', ';

                        $notesDocNames .= trim($docTitle);
                        $ndnCnt8++;
                    }
                }
            }
        }
    }
}

if (count($selectedCustomWordPkgArray) > 0) {
    for ($s8 = 0; $s8 < count($selectedCustomWordPkgArray); $s8++) {

        $CPKGID = 0;
        $CPKGID = trim($selectedCustomWordPkgArray[$s8]);
        if (count($CustomDocInfoArray) > 0) {

            if (array_key_exists($CPKGID, $CustomDocInfoArray)) {

                for ($h = 0; $h < count($CustomDocInfoArray); $h++) {

                    $CCID = 0;
                    $t1 = $customDocKeyArray[$h];
                    $chkOpt = '';
                    if ($CPKGID == $t1) {

                        $docTitle = $CustomDocInfoArray[$CPKGID][0]['docTitle'];
                        $CCID = $CustomDocInfoArray[$CPKGID][0]['CCID'];
                        if ($faxPop == '1') {
                            $attachmentArray[] = CONST_PATH_PC_UP_DOC . $PCID . '/' . Strings::removeDisAllowedChars(stripslashes($docTitle)) . '_' . Strings::removeDisAllowedChars(stripslashes($borrowerLName)) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($CCID)) . '.doc';
                        }

                        $custFN = '/' . Strings::removeDisAllowedChars(stripslashes($docTitle)) . '_' . Strings::removeDisAllowedChars(stripslashes($borrowerLName)) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($CCID)) . '.doc';

                        /**
                         * Google docs attachments
                         */
                        if ($CustomDocInfoArray[$CPKGID][0]['googleDocsId'] != '') {
                            $fname = Strings::removeDisAllowedChars($borrowerLName) . '-' . Strings::removeDisAllowedChars($docTitle);
                            // luid = Logged In User ID.
                            // lur	= Logged In User Role.
                            // lugr	= Logged In User Group.
                            if ($CustomDocInfoArray[$CPKGID][0]['docType'] == 'csv') {
                                $myEncryption = cypher::myEncryption('C');
                            } elseif ($CustomDocInfoArray[$CPKGID][0]['docType'] == 'xlsx') {
                                $myEncryption = cypher::myEncryption('X');
                            } else {
                                $myEncryption = cypher::myEncryption('D');
                            }

                            $url = CONST_CRON_SERVER_URL . 'backoffice/loan/create_custom_doc?file=' . $fname . '&amp;rId=' . cypher::myEncryption($responseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($CCID) . '&amp;opt=1lien&opt=enc&open=' . $myEncryption . '&amp;luid=' . cypher::myEncryption(Strings::GetSess('userNumber')) . '&amp;lur=' . cypher::myEncryption(Strings::GetSess('userRole')) . '&amp;lugr=' . cypher::myEncryption(Strings::GetSess('userGroup'));

                            $custDocMsg .= "<p><a rel=\"nofollow\" target=\"_blank\" href=\"" . $url . "\">Click to view " . ucwords($docTitle) . '</a></p>';
                        } else {
                            $custDocMsg .= "<p><a rel=\"nofollow\" target=\"_blank\" href=\"" . CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($custFN) . '&fd=' . cypher::myEncryption(CONST_PATH_PC_UP_DOC . $PCID . '/') . "&opt=enc\">Click to view " . ucwords($docTitle) . '</a></p>';
                        }

                        if ($ndnCnt8 > 0) {
                            $notesDocNames .= ', ';
                        }
                        $notesDocNames .= trim($docTitle);
                        $ndnCnt8++;
                    }
                }
            }
        }
    }
}

/**
 * Get all attachement file size in ORG server.
 */
$getFileDocPathInfoArray = $fileDtlArray['file_path'];
$resFileSizeInfo = UploadServer::getUploadedFileInfo($getFileDocPathInfoArray);
$totalFileSize = 0;
$isEmailLimtExceed = 0;
foreach ($resFileSizeInfo as $sizeInfoK => $sizeInfoV) {
    for ($si = 0; $si < count($sizeInfoV); $si++) {
        $totalFileSize += (int)(trim($sizeInfoV['file_size_' . $si]));
    }
}
/**
 * Check Email max email attachement size is exceed.
 */
if ($totalFileSize > CONST_EMAIL_MAX_FILE_SIZE_ALLOWED) {
    foreach ($resFileSizeInfo as $sizeInfoK => $sizeInfoV) {
        for ($si = 0; $si < count($sizeInfoV); $si++) {
            foreach ($fileDtlArray as $fKey => $fVal) {
                if ($fKey == 'url') {
                    $dynamuURL .= $fVal[$sizeInfoK]['url_' . $si] . '<br>';
                }
                if ($fKey == 'attachment') {
                    unset($fileDtlArray['attachment'][$sizeInfoK][$si]);
                }
            }
        }
    }
    $isEmailLimtExceed = 1;
    if ($faxPop != '1') {
        $attachmentArray = [];
    }
}

$attachmentArray = array_unique($attachmentArray);

if (isset($_POST['docRecipientEmail'])) {
    $docRecipientEmail = trim($_POST['docRecipientEmail']);
    $docRecipientEmailArray = explode(';', $docRecipientEmail);
}
$empEmailCnt = count($employeeIdArray); //print_r($empEmailCnt);exit();
for ($ur = 0; $ur < $empEmailCnt; $ur++) {
    $empId = $employeeIdArray[$ur];
    if ($empId != '') {
        if ($ur > 0) $notifyEmployeeIds .= ', ';
        $notifyEmployeeIds .= $empId;
    }
}


if ($faxPop == '1') {
    $ip = [
        'LMRId'              => $LMRId,
        'borFax'             => $borFaxNo,
        'coBorFax'           => $coBorFaxNo,
        'brokerFax'          => $brokerFaxNo,
        'branchFax'          => $branchFaxNo,
        'lien1LenderFax'     => $lien1LenderFax,
        'lien1Bank1RepFax'   => $lien1Bank1RepFax,
        'lien2LenderFax'     => $lien2LenderFax,
        'lien2Bank1RepFax'   => $lien2Bank1RepFax,
        'faxReceiverName'    => $faxReceiverName,
        'faxReceiverComp'    => $faxReceiverComp,
        'faxReceiverNo'      => $faxReceiverNo,
        'notifyEmployeeIds'  => $notifyEmployeeIds,
        'emailSubject'       => $emailSubject,
        'emailMessage'       => $emailMessage,
        'userRole'           => $UType,
        'userNo'             => $VUID,
        'userGroup'          => $userGroup,
        'userName'           => $userName,
        'userEmail'          => $userEmail,
        'userNumber'         => $userNumber,
        'PCID'               => $PCID,
        'attachment'         => array_unique($attachmentArray),
        'notesDocNames'      => $notesDocNames,
        'fileCreatedDate'    => $fileCreatedDate,
        'oldFPCID'           => $oldFPCID,
        'responseId'         => $responseId,
        'useCoverPage'       => $useCoverPage,
        'borrowerLName'      => $borrowerLName,
        'displayLender'      => $displayLender,
        'displayPropertyAdd' => $displayPropertyAdd,
        'counselAttorneyFax' => $counselAttorneyFax,
        'CIDs'               => $CIDs,
        'memberArray'        => $memberArray,
    ];

    $resultArray = faxPackageToUser::getReport($ip);
    if (count($resultArray) > 0) {
        $allowToSendFax = $resultArray['allowToSendFax'];
        $notes = $resultArray['notes'];
        if (array_key_exists('allowToSendEmail', $resultArray)) $allowToSendEmail = trim($resultArray['allowToSendEmail']);
        if (array_key_exists('faxMsg', $resultArray)) $faxMsg = trim($resultArray['faxMsg']);
        if ($allowToSendFax == 0 && $allowToSendEmail == 1) {
            /** If attachment is greater than 18 MB. & file types are allowed to send. send them as an email **/
            $ip = [
                'LMRId'                => $LMRId,
                'borrowerEmail'        => $borrowerEmail,
                'coBorrowerEmail'      => $coBorrowerEmail,
                'brokerEmail'          => $brokerEmail,
                'LMRExecutiveEmail'    => $executiveEmail,
                'notifyEmployeeIds'    => $notifyEmployeeIds,
                'docRecipientEmail'    => $docRecipientEmailArray,
                'emailSubject'         => $emailSubject,
                'emailMessage'         => $emailMessage,
                'userRole'             => $UType,
                'userNo'               => $VUID,
                'userGroup'            => $userGroup,
                'userName'             => $userName,
                'userEmail'            => $userEmail,
                'PCID'                 => $PCID,
                'attachment'           => array_unique($attachmentArray),
                'eSignedReadyDocArray' => [],
                'pkgAllSelectedArray'  => [],
                'custDocMsg'           => '',
                'eDocMsg'              => '',
                'branchDocMsg'         => '',
                'CIDs'                 => $CIDs,
                'memberArray'          => $memberArray,
                'tabOption'            => 'docsTab',
            ];
            if ($isEmailLimtExceed > 0) $ip['attachment'] = []; // Check Email max email attachment size is exceed.

            $resultArray = sendEmailPackage::getReport($ip);
        }
        /* Customization request from PC = NVA Financial on May 02, 2016, While Faxing (606) FD Retainer docs, email the PC */
        if ((in_array('606', $selAttachedPkgArray) && ($PCID == '2' || $PCID == '820'))) {

            sendClientAppInDocToNVAPC::getReport(['LMRId' => $LMRId]);
        }
    }
} else {
    $ip = [
        'LMRId'                => $LMRId,
        'borrowerEmail'        => $borrowerEmail,
        'coBorrowerEmail'      => $coBorrowerEmail,
        'brokerEmail'          => $brokerEmail,
        'LMRExecutiveEmail'    => $executiveEmail,
        'notifyEmployeeIds'    => $notifyEmployeeIds,
        'docRecipientEmail'    => $docRecipientEmailArray,
        'emailSubject'         => $emailSubject,
        'emailMessage'         => $emailMessage . $dynamuURL,
        'userRole'             => $UType,
        'userNo'               => $VUID,
        'userGroup'            => $userGroup,
        'userName'             => $userName,
        'userEmail'            => $userEmail,
        'PCID'                 => $PCID,
        'attachment'           => $attachmentArray,
        'eSignedReadyDocArray' => $eSignedReadyDocArray,
        'creditorESignedDoc'   => $creditorESignedDoc,
        'pkgAllSelectedArray'  => $pkgAllSelectedArray,
        'custDocMsg'           => $custDocMsg,
        'eDocMsg'              => $eDocMsg,
        'branchDocMsg'         => '',
        'lenderEmail1'         => $lenderEmail1,
        'lenderEmail2'         => $lenderEmail2,
        'counselEmail'         => $counselEmail,
        'nonBorrowerEmail'     => $nonBorrowerEmail,
        'payeeEmail'           => $payeeEmail,
        'firstBuyerEmail'      => $firstBuyerEmail,
        'secondBuyerEmail'     => $secondBuyerEmail,
        'thirdBuyerEmail'      => $thirdBuyerEmail,
        'cloudDocuments'       => $cloudDocArray,
        'CIDs'                 => $CIDs,
        'memberArray'          => $memberArray,
        'tabOption'            => 'docsTab',
    ];


    $resultArray = sendEmailPackage::getReport($ip);
    if ((in_array('562', $selectedPkgArray) || in_array('562', $selAttachedPkgArray)) && $isSLM == 1) {
        updateLenderCallBackDate::getReport(['LMRId' => $LMRId, 'lenderCallBack' => date('Y-m-d')]);
    }
    /**
     ** Description    : Pre Approval Package Last Action
     ** Developer    : Suresh
     ** Author            : AwataSoftsys
     ** Date                : April 4, 2017
     **/
    if (in_array('766', CONST_PRE_APPROVAL_PACKAGE_IDS)
        || in_array('791', CONST_PRE_APPROVAL_PACKAGE_IDS)
        || in_array('773', CONST_PRE_APPROVAL_PACKAGE_IDS)) {
        $ipArray = [];
        $ipArray['LMRId'] = $LMRId;
        $ipArray['docID'] = $selPKGID;
        $ipArray['UID'] = $userNumber;
        $ipArray['UType'] = $userGroup;
        savePreApprovalPkgDetails::getReport($ipArray);
    }
}
$receiverName = '';
$recipientArray = [];
$mailStatus = 0;
if (count($resultArray) > 0) {
    $mailStatus = $resultArray['mailStatus'];
    $notes = '';
    $recipientArray = $resultArray['recipientArray'];
    if (array_key_exists('receiverName', $resultArray)) $receiverName = $resultArray['receiverName'];
    if (array_key_exists('sendTo', $resultArray)) $sendTo = $resultArray['sendTo'];

    if ($faxPop == '1') $sendTo = $resultArray['notes'];
}
$eSignedActionKeyArray = [];
if (count($eSignedActionArray) > 0) $eSignedActionKeyArray = array_keys($eSignedActionArray);


for ($ea = 0; $ea < count($eSignedActionKeyArray); $ea++) {
    $actionArray = [];
    $actionArray['SAID'] = $eSignedActionKeyArray[$ea];
    $actionArray['recipientArray'] = $recipientArray;
    saveTrustEmailTo::getReport($actionArray);
}
$clientId = 0;
if ($userGroup == 'Super') {
    $exeId = 0;
    $empId = 0;
    $brId = 0;
    $clientId = 0;
} else if ($userGroup == 'Employee') {
    $exeId = 0;
    $empId = $VUID;
    $brId = 0;
    $clientId = 0;
} else if ($userGroup == 'Agent') {
    $exeId = 0;
    $empId = 0;
    $brId = $VUID;
    $clientId = 0;
} else if ($userGroup == 'Branch') {
    $exeId = $VUID;
    $empId = 0;
    $brId = 0;
    $clientId = 0;
} else if ($userGroup == 'Client') {
    $exeId = 0;
    $empId = 0;
    $brId = 0;
    $clientId = $VUID;
}

$appenStr = '';
if ($notesDocNames1 != '') {
    $notes = $notesDocNames1;
    $appenStr = ' ';
    if ($notesDocNames != '') $notesDocNames .= ', ';
    $notesDocNames .= $notesDocNames1;
}
if ($notesDocNames3 != '') {
    $notes = 'Resent the email link for the document ' . $notesDocNames3 . ' by ' . $userName . '. ';
    $appenStr = '<br>';
    if ($notesDocNames != '') $notesDocNames .= ', ';
    $notesDocNames .= $notesDocNames3;
}
if ($msg != '') {
    $notes .= $appenStr . $msg;
    $appenStr = '<br>';
}
if ($notesDocNames2 != '') {
    $notes .= $appenStr . $notesDocNames2 . ' document resent by ' . $userName . '.';
    /** Re-sent the document not signed only emailed **/
    $appenStr = '<br>';
    if ($notesDocNames != '') $notesDocNames .= ', ';
    $notesDocNames .= $notesDocNames2;
}

if ($notesDocNames != '') {
    $notes .= $appenStr . 'Sent the following docs: ' . addslashes($notesDocNames) . '<br>Sent the following email: <br>Subject: ' . addslashes($emailSubject);
}
if ($PCID == 854 && trim($notes) == '') { /* As per Daniel request, remove the restriction for the law offices PC to select the documents while sending emails on Sep 4, 2015. */
    $notes = Strings::stripQuote($emailMessage) . '<br>Sent to ' . $receiverName;
}
if ($emailSubject != '' && $notesDocNames == '' && $notesDocNames2 == '') $notes = 'Sent the following email: ' . $emailSubject; // added the notes in send email button On Mar 10, 2018 by Venky
$inArray = [
    'processorNotes'    => $notes,
    'fileID'            => $LMRId,
    'executiveId'       => $exeId,
    'brokerNumber'      => $brId,
    'employeeId'        => $empId,
    'clientId'          => $clientId,
    'notesInfo'         => $sendTo,
    'privateNotes'      => $isSysNotesPrivate,
    'signExecutiveName' => ' ',
    'isSysNotes'        => 1,
    'displayIn'         => 'BO',
];


$resultArray = saveFileNotes::getReport($inArray);


if ($mailStatus > 0) {
    if (trim($faxMsg) != '') Strings::SetSess('msg', $faxMsg);
    else Strings::SetSess('msg', 'Package has been emailed/faxed.');
} else {
    if (trim($faxMsg) != '') Strings::SetSess('msg', $faxMsg);
    else Strings::SetSess('msg', 'File Size is too large.');
}
header('Location:' . $_SERVER['HTTP_REFERER']);
exit();

