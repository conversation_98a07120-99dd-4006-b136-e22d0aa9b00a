<?php

require 'googleAnalytics.php';

use models\constants\gl\glCustomJobForProcessingCompany;
use models\lendingwise\tblProcessingCompany;
use models\PageVariables;
use models\standard\Custify;

$PCID = PageVariables::$PCID;
$pcInfo = tblProcessingCompany::Get(['PCID' => $PCID]);
?>
<div id="sessionTimeoutWarning"></div>
<script src="/login/js/backoffice/footer.js?<?php echo CONST_JS_VERSION; ?>"></script>
<script>
    $(function () {
        footer.Upscope_uniqueId = "undefined"
        footer.Upscope_identities = ["<?php echo htmlentities(PageVariables::$userFName) . " " . htmlentities(PageVariables::$userLName)?>", "<?php echo htmlentities(PageVariables::$processorAssignedCompany); ?>"]
        footer.Init();
    });
</script>


<?php if (($_ENV['BUGSNAGJS_ENABLED'] ?? null) == 'true') { ?>
    <script src="//d2wy8f7a9ursnm.cloudfront.net/v7/bugsnag.min.js"></script>
    <script>Bugsnag.start('<?php echo $_ENV['BUGSNAGJS_API_KEY'] ?? '' ?>')</script>
<?php } ?>


<?php
if (!glCustomJobForProcessingCompany::hideWalkthrough(intval($PCID)) && PageVariables::$userRole && (in_array('HMLO', PageVariables::$myModulesArray) || in_array('loc', PageVariables::$myModulesArray))) {
    $help = $pcInfo->VIPSupport == 1 ? 'VIPhelp' : 'help';
    ?>
    <script>
        (function () {
            let url = new URL(window.location.href);
            let params = new URLSearchParams(url.search);

            // Append your parameter if it doesn't already exist
            if (!params.has('supp')) {
                params.append('supp', '<?php echo $help?>');
                url.search = params.toString();

                // Use history.replaceState to update the URL without refreshing the page
                window.history.replaceState({}, '', url.toString());
            }
        })();
    </script>
    <?php
    if (CONST_ENVIRONMENT == 'production') {
        ?>
        <script type="text/javascript">
            (function (w, d, u) {
                w.$productFruits = w.$productFruits || [];
                w.productFruits = w.productFruits || {};
                w.productFruits.scrV = '2';
                let a = d.getElementsByTagName('head')[0];
                let r = d.createElement('script');
                r.async = 1;
                r.src = u;
                a.appendChild(r);
            })(window, document, 'https://app.productfruits.com/static/script.js');
        </script>
        <script type="text/javascript">
            $productFruits.push(['init', 'cGKfZz5ie3gELThR', 'en', {
                username: '<?php echo htmlentities(PageVariables::$userFName) . " " . htmlentities(PageVariables::$userLName)?>',
                email: '<?php echo PageVariables::$userEmail?>',
                firstname: '<?php echo htmlentities(PageVariables::$userFName)?>',
                role: '<?php echo htmlentities(PageVariables::$userRole)?>',
                signUpAt: '<?php echo $pcInfo->recordDate; ?>',
                props: {
                    pcid: '<?php echo $PCID?>',
                    vipsupp: <?php echo $pcInfo->VIPSupport ?? '0'; ?>,
                    docwiz: <?php echo $pcInfo->docWizard ?? '0'; ?>,
                    marketpl: <?php echo $pcInfo->allowPCToMarketPlace ?? '0'; ?>,
                    automation: <?php echo $pcInfo->allowAutomation ?? '0'; ?>,
                    filetypes: '<?php echo json_encode(PageVariables::$myModulesArray) ?? []; ?>',
                    // myCustomProp3: ['first', 'second'],
                }
            }]);
        </script>

        <script type="text/javascript" id="hs-script-loader" async defer
                src="//js-na1.hs-scripts.com/21536117.js"></script>
        <style> #hubspot-messages-iframe-container iframe {
                right: 100px !important;
            }</style>

        <?php
    }
}
?>
<?php
if (CONST_ENVIRONMENT == 'production' && $PCID && PageVariables::$userNumber && PageVariables::$userEmail && $pcInfo) {
    echo Custify::getCustifyScript($PCID, $pcInfo);
    echo Custify::trackSaves();
}
?>
<?php
if (!empty($_SESSION['package_overlay'])) {
    ?>
    <!-- only loaded when debug enabled -->
    <link rel="stylesheet" href="/assetsNew/css/package_overlay.css">
    <script src="/assetsNew/js/package_overlay.js"></script>
    <?php
}
?>