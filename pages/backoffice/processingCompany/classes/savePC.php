<?php

namespace pages\backoffice\processingCompany\classes;

use alowareApi;
use models\composite\oPC\assignAllStdPkgToUser;
use models\composite\oPC\deleteFaxUserServerInfo;
use models\composite\oPC\deleteProcessingCompanyFee;
use models\composite\oPC\deleteUserServerInfo;
use models\composite\oPC\getQuickAppCustomFormFields;
use models\composite\oPC\saveAllowAccessIP;
use models\composite\oPC\saveEmailReportEvent;
use models\composite\oPC\saveFaxUserServerInfo;
use models\composite\oPC\savePCAuditor;
use models\composite\oPC\savePCInfo;
use models\composite\oPC\saveUserServerInfo;
use models\composite\oPC\sendNewPCAlertToAdmin;
use models\composite\oPC\sendNewPCAlertToCustomer;
use models\composite\oPC\updateCFPBSettingsForUsers;
use models\composite\oPC\updateModulesByPCMgr;
use models\composite\oPC\updatePCLogo;
use models\composite\oPC\updatePCModules;
use models\composite\oPC\updatePCServiceType;
use models\composite\oPC\updateProcessingCompanyFee;
use models\cypher;
use models\Database2;
use models\Request;
use models\standard\Arrays;
use models\standard\Strings;
use models\types\strongType;

class savePC extends strongType
{
    public static function Post(
        ?string $userRole,
        ?int    $userNumber,
        ?int    $webhook,
        ?string $userGroup,
        ?array  $globalWithUSTimeZones,
        ?string $qryDelModule,
        ?string $glQuickAppTabSection
    ): string
    {
        global $attorneyEmail, $publicUser, $PCID, $acctType, $paymentErrorStatus, $processingCompanyId, $webhook, $attorneyFName, $attorneyMName, $attorneyLName;
        global $phoneNumber, $cell, $fax, $userGroup, $modulesArray, $processingCompanyName, $processingCompanyWebsite, $PCServiceTypeArray, $brokerNumber;

        $processingCompanyName = '';
        $attorneyFName = '';
        $attorneyMName = '';
        $attorneyLName = '';
        $processingCompanyId = 0;
        $phoneNumber = '';
        $fax = '';
        $cell = '';
        $attorneyEmail = '';
        $logoName = '';
        $processingCompanyWebsite = '';
        $LMRFeeId = '';
        $feeInfoArray = [];
        $newCnt = 0;
        $allowToCreateAloware = 0;
        $timeZone = '';
        $LMRFileSubStatusArray = [];
        $alowareInfo = [];
        $LMRFileSubStatus = '';
        $primaryLoanModStatusArray = [];
        $PKGIDArray = [];
        $allowIPAccess = '';
        $PCARBDuration = '';
        $resultArray = [];
        $allowToCreateNew = 0;
        $FCIDArray = [];
        $employeeResultArray = [];
        $allowToCreateNewStaff = 0;
        $allowToUpdateStaff = 0;
        $branchResultArray = [];
        $executiveId = 0;
        $regnForArray = [];
        $serverInfoUserSetting = 0;
        $hostName = '';
        $userName = '';
        $pwd = '';
        $portNo = '';
        $senderId = 0;
        $replyTo = '';
        $bounceMail = '';
        $ESID = 0;
        $moduleType = '';
        $modulesArray = [];
        $isPLO = 0;
        $sentUpDate = '';
        $SID = 0;
        $allowToSend = 0;
        $faxInfoUserSetting = 0;
        $eFaxUrl = '';
        $eFaxHName = '';
        $eFaxUserName = '';
        $eFaxPwd = '';
        $FSID = 0;
        $brokerNumber = 0;
        $executiveIds = '';
        $employeeId = 0;
        $updateCount = 0;
        $insertCount = 0;
        $allowToCFPBSubmitForPC = 0;
        $primaryLoanModStatus = '';
        $auditor = '';
        $AID = 0;
        $LMRClientType = '';
        $btnValue = '';
        $isNewPC = 0;
        $eFaxCompany = '';
        $eFaxServiceProvider = 'Vitelity';
        $tempPCServiceTypeArray = [];
        $PCServiceTypeArray = [];
        $notesTypeArray = [];
        $alowareErr = '';


        if (isset($_POST['pcId'])) $processingCompanyId = intval(cypher::myDecryption(Request::GetClean('pcId')));
        if (isset($_POST['processingCompanyName'])) $processingCompanyName = trim($_POST['processingCompanyName']);
        if (isset($_POST['attorneyFName'])) $attorneyFName = trim($_POST['attorneyFName']);
        if (isset($_POST['attorneyMName'])) $attorneyMName = trim($_POST['attorneyMName']);
        if (isset($_POST['attorneyLName'])) $attorneyLName = trim($_POST['attorneyLName']);
        if (isset($_POST['phoneNumber'])) $phoneNumber = trim($_POST['phoneNumber']);
        if (isset($_POST['fax'])) $fax = trim($_POST['fax']);
        if (isset($_POST['attorneyEmail'])) $attorneyEmail = trim($_POST['attorneyEmail']);
        if (isset($_POST['timeZone'])) $timeZone = trim($_POST['timeZone']);
        if (isset($_POST['PKGID'])) $PKGIDArray = $_POST['PKGID'];
        if (isset($_POST['processingCompanyWebsite'])) $processingCompanyWebsite = trim($_POST['processingCompanyWebsite']);
        if (isset($_POST['serverInfoUserSetting'])) $serverInfoUserSetting = trim($_POST['serverInfoUserSetting']);
        if (isset($_POST['allowToCreateAloware'])) $allowToCreateAloware = trim($_POST['allowToCreateAloware']);
        if (isset($_POST['FCID'])) $FCIDArray = $_POST['FCID'];
        if (isset($_POST['auditor'])) $auditor = $_POST['auditor'];
        if (isset($_POST['AID'])) $AID = $_POST['AID'];
        if (isset($_POST['hostName'])) $hostName = trim($_POST['hostName']);
        if (isset($_POST['userName'])) $userName = trim($_POST['userName']);
        if (isset($_POST['pwd'])) $pwd = trim($_POST['pwd']);
        if (isset($_POST['portNo'])) $portNo = trim($_POST['portNo']);
        if (isset($_POST['replyTo'])) $replyTo = trim($_POST['replyTo']);
        if (isset($_POST['bounceMail'])) $bounceMail = trim($_POST['bounceMail']);
        if (isset($_POST['ESID'])) $ESID = trim($_POST['ESID']);
        if (isset($_POST['cellNumber'])) $cell = trim($_POST['cellNumber']);
        if (isset($_POST['isPLO'])) $isPLO = trim($_POST['isPLO']);
        if (isset($_POST['sentUpDate'])) $sentUpDate = trim($_POST['sentUpDate']);
        if (isset($_POST['SID'])) $SID = trim($_POST['SID']);
        if (isset($_POST['allowToSend'])) $allowToSend = trim($_POST['allowToSend']);
        if (isset($_POST['faxInfoUserSetting'])) $faxInfoUserSetting = trim($_POST['faxInfoUserSetting']);
        if (isset($_POST['eFaxHName'])) $eFaxHName = trim($_POST['eFaxHName']);
        if (isset($_POST['eFaxUserName'])) $eFaxUserName = trim($_POST['eFaxUserName']);
        if (isset($_POST['eFaxPwd'])) $eFaxPwd = trim($_POST['eFaxPwd']);
        if (isset($_POST['FSID'])) $FSID = trim($_POST['FSID']);

        $purchasedPlan = $_POST['purchasedPlan'] ?? '';
        $websitepurchased = $_POST['websitepurchased'] ?? 0;

        if (isset($_POST['selFileSubStatus'])) {
            $LMRFileSubStatus = trim($_POST['selFileSubStatus']);
            $LMRFileSubStatusArray = ['LMRFileSubStatus' => $LMRFileSubStatus];
        }
        if (isset($_POST['selPrimaryLoanModStatus'])) {
            $primaryLoanModStatus = trim($_POST['selPrimaryLoanModStatus']);
            $primaryLoanModStatusArray = ['primaryLoanModStatus' => $primaryLoanModStatus];
        }
        if (isset($_POST['moduleType'])) $modulesArray = $_POST['moduleType'];
        if ($webhook == 1) {
            $modulesArray = explode(',', $_POST['modules']);
            for ($m = 0; $m < count($modulesArray); $m++) {
                $_POST['services_' . $modulesArray[$m]] = explode(',', $_POST['service_' . $modulesArray[$m]]);
            }
        }

        if (isset($_POST['LMRFee'])) $feeInfoArray = $_POST['LMRFee'];
        if (isset($_POST['notesType'])) $notesTypeArray = $_POST['notesType'];

        if (in_array('GE', $notesTypeArray)) {
            doNothing();
        } else {
            $notesTypeArray = array_merge(['GE'], $notesTypeArray);
        }
        $notesTypeInfoArray = implode(',', $notesTypeArray);

        if ($webhook == 1) {
            $notesTypeInfoArray = $_POST['defaultNotesType'] ?? '';
            $defaultBillingFee = $_POST['defaultBillingFee'] ?? '';
            $feeInfoArray = explode(',', $defaultBillingFee);
        }
        if (isset($_POST['allowToCFPBSubmitForPC'])) {
            $allowToCFPBSubmitForPC = trim($_POST['allowToCFPBSubmitForPC']);
        }
        if (isset($_POST['eFaxCompany'])) {
            $eFaxCompany = trim($_POST['eFaxCompany']);
        }
        if (isset($_POST['eFaxServiceProvider'])) {
            $eFaxServiceProvider = trim($_POST['eFaxServiceProvider']);
        }
        if (isset($_REQUEST['saveProcessingCompany'])) {
            $btnValue = trim($_REQUEST['saveProcessingCompany']);
        }
        $cell = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $cell);
        $phoneNumber = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $phoneNumber);
        $fax = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $fax);

        if ($userGroup == 'Employee' && $processingCompanyId > 0) {
            $params['PCID'] = $processingCompanyId;
            $pqry = ' select * from tblProcessingCompany where PCID = :PCID ;';
            $rowSel = Database2::getInstance()->queryData($pqry, $params, null, true);
            $auditor = $rowSel['auditor'];
        }

        $infoArray = ['processingCompanyId'   => $processingCompanyId,
                      'processingCompanyName' => $processingCompanyName,
                      'attorneyFName'         => $attorneyFName,
                      'attorneyLName'         => $attorneyLName,
                      'attorneyMName'         => $attorneyMName,
                      'fax'                   => $fax,
                      'attorneyCell'          => $cell,
                      'attorneyEmail'         => $attorneyEmail,
                      'notesTypeInfoArray'    => $notesTypeInfoArray,
        ];
        if (isset($_POST['auditor'])) $infoArray['auditor'] = $auditor;
        if (isset($_POST['AID'])) $infoArray['AID'] = $AID;

        $infoArray['trackerEnabled'] = isset($_POST['trackerEnabled']) ? $_POST['trackerEnabled'] : 0;


        $resultArray = savePCInfo::getReport($infoArray);
        $processingCompanyId = $resultArray['PCID'];
        $PCID = $processingCompanyId;
        $resArray = savePCAuditor::getReport($infoArray);

        /**
         * Card # :530
         * Description      : Aloware integration
         * Functionality    : Admin user creation process.
         */
        if (($resultArray['updateCount'] == 1 || $resultArray['insertCount'] == 1) && $allowToCreateAloware == 1) {
            $aloware = new alowareApi();
            $resaloware = $aloware->getAlowareInfo($processingCompanyId);

            if (empty($resaloware)) {
                $request['internal_company_id'] = $processingCompanyId;
                $request['company_name'] = $processingCompanyName;
                $request['company_country'] = 'US';
                $request['internal_user_id'] = $AID;
                $request['user_name'] = $attorneyFName;
                $request['user_phone_number'] = $cell;
                $request['user_email'] = $attorneyEmail;
                $request['user_timezone'] = Arrays::getArrayValue($timeZone, $globalWithUSTimeZones);
                $creteCompanyJson = $aloware->creteUser($request);

                $creteCompanyJsonDecrypt = json_decode($creteCompanyJson, true);
                if (isset($creteCompanyJsonDecrypt['api_token'])) {
                    $alowareInfo['PCID'] = $processingCompanyId;
                    $alowareInfo['api_token'] = $creteCompanyJsonDecrypt['api_token'];
                    $alowareinfo = $aloware->saveAloware($alowareInfo);
                }
                if (isset($creteCompanyJsonDecrypt['errors'])) {
                    foreach ($creteCompanyJsonDecrypt['errors'] as $errKey => $errValue) {
                        $alowareErr .= $errValue[0] . '<br>';
                    }
                }
            }
        }
        /* Aloware end */

        if (count($resultArray) > 0) {
            if (array_key_exists('updateCount', $resultArray)) $updateCount = $resultArray['updateCount'];
            if (array_key_exists('insertCount', $resultArray)) $insertCount = $resultArray['insertCount'];
            if (array_key_exists('PCID', $resultArray)) $processingCompanyId = $resultArray['PCID'];
            $isInsert = $resultArray['isInsert'] ?? 'No';
            if ($updateCount > 0) {
                Strings::SetSess('msg', 'Updated Successfully.');
            } else if ($insertCount > 0) {
                $isNewPC = 1;
                Strings::SetSess('msg', 'Inserted Successfully.');
                if ($userRole == 'Super' || $userRole == 'Sales') {
                    $ip = ['PCID' => $processingCompanyId, 'UType' => 'PC'];
                    assignAllStdPkgToUser::getReport($ip);
                }
            } else {
                Strings::SetSess('msg', 'Error While Updating.');
            }
            if ($userRole == 'Manager') {
                if (isset($_POST['moduleTypes'])) {
                    $modulesArray = explode(', ', $_POST['moduleTypes']);
                }
            }

            for ($i = 0; $i < count($modulesArray); $i++) {
                $moduleCode = '';
                $moduleCode = trim($modulesArray[$i]);

                if (isset($_POST['services_' . $moduleCode])) $tempPCServiceTypeArray = $_POST['services_' . $moduleCode];
                $PCServiceTypeArray[$i]['serviceType'] = $tempPCServiceTypeArray;
                $PCServiceTypeArray[$i]['moduleType'] = $moduleCode;
            }
            $qryModules = ' SELECT modulecode FROM tblPCModules WHERE activeStatus = 1 AND PCID = ' . $processingCompanyId;
            $resultModules = Database2::getInstance()->fetchRecords(['qry' => $qryModules]);
            $currentModules = [];
            $pc_selected_deselect_modules = [];
            $qryInsModules = '';
            $qryDelModules = '';
            foreach ($resultModules as $moduleData) {
                $currentModules[] = $moduleData['modulecode'];
            }
            $pc_selected_deselect_modules['selected'] = array_diff($modulesArray, $currentModules);
            $pc_selected_deselect_modules['deselected'] = array_diff($currentModules, $modulesArray);
            if (count($pc_selected_deselect_modules['deselected']) > 0) {

                foreach ($pc_selected_deselect_modules['deselected'] as $arrIndex => $del_filetype) {

                    $qryDelModules .= " UPDATE tblFieldsQuickApp
            SET fileType =  replace_string_in_set('" . $del_filetype . "', fileType)
            WHERE PCID = " . $processingCompanyId . ' ; ';

                }
            }
            if (count($pc_selected_deselect_modules['selected']) > 0) {

                foreach ($pc_selected_deselect_modules['selected'] as $arrIndex => $sel_filetype) {
                    $qryInsModules .= " UPDATE tblFieldsQuickApp
                SET fileType = CONCAT(replace_string_in_set('" . $sel_filetype . "', fileType), '" . $sel_filetype . "')
                WHERE PCID = " . $processingCompanyId . '; ';
                }
            }
            if ($qryDelModule != '') {
                Database2::getInstance()->executeQuery($qryDelModules);
            }
            if ($qryInsModules != '') {
                Database2::getInstance()->executeQuery($qryInsModules);
            }
            if ($userRole == 'Super') {
                if (count($modulesArray) > 0) {
                    $ip['PCID'] = $processingCompanyId;
                    $ip['moduleType'] = $modulesArray;
                    updatePCModules::getReport($ip); // refactored
                    if ($userRole == 'Super') {
                        doNothing();
                    } else {
                        Strings::SetSess('PCLMRClientType', str_replace(',', ', ', $moduleType));
                    }
                }
            } else if ($userRole == 'Manager') {
                if (count($modulesArray) > 0) {
                    $ip['PCID'] = $processingCompanyId;
                    $ip['moduleType'] = $modulesArray;
                    updateModulesByPCMgr::getReport($ip);
                    if ($userRole == 'Manager') {
                        doNothing();
                    } else {
                        Strings::SetSess('PCLMRClientType', str_replace(',', ', ', $moduleType));
                    }
                }
            }
            /**
             *
             * Description     : Changed the position bcz of before create a PC selected Service type
             * Author          : Awatasoftsys
             * Developer       : Viji, Venkatesh
             * Date            : April 03, 2017
             **/

            if (count($PCServiceTypeArray) > 0) {
                $ip['PCID'] = $processingCompanyId;
                $ip['LMRClientType'] = $PCServiceTypeArray;
                $requiredDocsArrayNew = Arrays::buildKeyByValue($ip['LMRClientType'], 'moduleType');
                $moduleCodeNewArray = [];
                foreach ($requiredDocsArrayNew as $keyssssNew => $vss) {
                    $servicetypevalue = $vss[0]['serviceType'];
                    $servicetypedata = is_array($servicetypevalue) ? implode(',', $servicetypevalue) : '';
                    $moduleCodeNewArray[$keyssssNew] = $servicetypedata;
                }
                $ip['moduleCodeNewArrayService'] = $moduleCodeNewArray;

                updatePCServiceType::getReport($ip);
                if ($userRole == 'Super') {
                    doNothing();
                } else {
                    Strings::SetSess('PCLMRClientType', str_replace(',', ', ', $LMRClientType));
                }
                /* written by deepthi  to insert the loan programs in the workflow step additional condition which are associated to the PC while PC creation time */
                if ($isInsert == 'Yes') {
                    $queryWFServiceType = '    INSERT INTO tblPCWorkflowStepServiceType ( WFSID,WFStepServiceType)
               SELECT  wfs.WFSID , wfst.WFStepServiceType FROM tblPCWorkflowSteps wfs
               INNER JOIN tblPCWorkflow wf ON wf.WFID= wfs.WFID AND PCID = ' . $processingCompanyId . '
               INNER JOIN tblWorkflowStepServiceTypeMaster wfst ON wfst.WFSID = wfs.WFSID_X
               WHERE wfst.WFStepServiceType IN ( SELECT  LMRClientType FROM tblProcessingCompanyLMRClientType WHERE pcid = ' . $processingCompanyId . ' );';
                    $rsWFServiceType = Database2::getInstance()->executeQuery($queryWFServiceType);

                    $psQry = 'SELECT PSMID,PSID_X, PSID,displayorder,moduleCode FROM  tblPCPrimeStatus  tps
                     INNER JOIN   tblPCPrimeStatusModules tpsm ON    tpsm.primestatusid =     tps.  PSID_X
                     WHERE tps.PCID= ' . $processingCompanyId . ' AND  tpsm.PCID= 0 ';
                    $psres = Database2::getInstance()->fetchRecords(['qry' => $psQry]);

                    $updateQry = '';
                    for ($i = 0; $i < count($psres); $i++) {
                        if ($psres[$i]['displayorder'] != '') {
                            $updateQry .= ' update tblPCPrimeStatusModules set displayOrder = ' . $psres[$i]['displayorder'] . ' where primeStatusId = ' . $psres[$i]['PSID'] . " and moduleCode = '" . $psres[$i]['moduleCode'] . "' ; ";
                        }
                    }

                    if ($updateQry != '') {
                        Database2::getInstance()->executeQuery($updateQry);
                    }

                    //written by deepthi to send the <NAME_EMAIL> on pc creation time
                    $password = ($webhook == 1) ? 'Lendwise2024!' : $processingCompanyId;
                    $PCEmailArray = ['PCID' => $processingCompanyId, 'regnFrom' => 'TLPNew', 'purchasedPlan' => $purchasedPlan, 'password' => $password, 'isPLO' => $isPLO, 'allowPCToMarketPlace' => $_POST['allowPCToMarketPlace'], 'websitepurchased' => $websitepurchased];
                    sendNewPCAlertToAdmin::getReport($PCEmailArray);
                    if ($isPLO == 0 && $websitepurchased == 0) {
                        doNothing();
                    } else {
                        sendNewPCAlertToCustomer::getReport($PCEmailArray);
                    }

                    $ffModuleArray = [];
                    for ($m = 0; $m < count($modulesArray); $m++) {
                        $ffModuleArray[$m]['moduleCode'] = $modulesArray[$m];
                    }
                    // insert form fields against pc
                    $fieldsResultArray = getQuickAppCustomFormFields::getReport([
                        'assignedPCID'   => $processingCompanyId,
                        'fileTypeSearch' => [],
                        'myOpt'          => '',
                        'module'         => $ffModuleArray,
                        'sectionSearch'  => [],
                        'sectionsArr'    => $glQuickAppTabSection]);
                }
            }


            /**
             *
             * Description     : Create a new PC
             * Author          : Awatasoftsys
             * Developer       : Viji, Venkatesh
             * Date            : Feb 13, 2017
             **/

            if ($isNewPC == 1) {
                if (trim($attorneyEmail)) {

                    require CONST_BO_PATH . 'autoCreateUsersForPC.php';
                    /** Auto create agent, branch & employee while PC Create **/
                }
            }


            if (count($feeInfoArray) > 0) {
                $ip['PCID'] = $processingCompanyId;
                $ip['LMRFeeId'] = $feeInfoArray;
                updateProcessingCompanyFee::getReport($ip); // refactored
            }
            if (count($feeInfoArray) == 0) {
                deleteProcessingCompanyFee::getReport($processingCompanyId); // refactored
            }
            if ($faxInfoUserSetting == 1) {
                $inpArray['eFaxHName'] = $eFaxHName;
                $inpArray['PCID'] = $processingCompanyId;
                $inpArray['eFaxCompany'] = $eFaxCompany;
                $inpArray['eFaxUserName'] = $eFaxUserName;
                $inpArray['eFaxPwd'] = $eFaxPwd;
                $inpArray['FSID'] = $FSID;

                $inpArray['eFaxServiceProvider'] = $eFaxServiceProvider;

                saveFaxUserServerInfo::getReport($inpArray); // refactored
            } else if ($FSID > 0) {

                deleteFaxUserServerInfo::getReport($FSID); // refactored
            }


            /* Story 20109 EMail Failed Report  */
            //mailEvent
            if (isset($_REQUEST['eventType'])) {
                $mailInputArray = [];
                $mailInputArray['PCID'] = $processingCompanyId;
                $mailInputArray['eventType'] = implode(',', $_REQUEST['eventType']);
                $mailInputArray['emailEventStatus'] = $_REQUEST['emailEventStatus'];
                saveEmailReportEvent::getReport($mailInputArray); // refactored
            }
        }
        if ($userRole == 'Super') {
            if ($serverInfoUserSetting == 1) {
                $myServerInfoArray['hostName'] = $hostName;
                $myServerInfoArray['userName'] = $userName;
                $myServerInfoArray['pwd'] = $pwd;
                $myServerInfoArray['portNo'] = $portNo;
                $myServerInfoArray['senderId'] = $processingCompanyId;
                $myServerInfoArray['senderUserType'] = 'Employee';
                $myServerInfoArray['replyTo'] = $replyTo;
                $myServerInfoArray['bounceMail'] = $bounceMail;
                $myServerInfoArray['ESID'] = $ESID;
                saveUserServerInfo::getReport($myServerInfoArray); // refactored
            } else if ($ESID > 0) {
                deleteUserServerInfo::getReport($ESID); // refactored
            }
        }

        if ($userGroup == 'Super') {
            if (isset($_POST['allowIPAccess'])) {
                $allowIPAccess = trim($_POST['allowIPAccess']);
            }
            $inpArray = [
                'PCID'     => $processingCompanyId,
                'userRole' => $userGroup,
                'IPs'      => $allowIPAccess,
                'UID'      => $userNumber
            ];
            if ($processingCompanyId) {
                $Count = saveAllowAccessIP::getReport($inpArray); // refactored
                if ($Count > 0) {
                    Strings::SetSess('msg', 'Updated Successfully.');
                }
            }

            if ($processingCompanyId > 0 && $allowToCFPBSubmitForPC == 1) { /* As per Daniel request, Allow to access CFPB Tab = Yes, then set all their users Allow to access CFPB Tab, Allow to view CFP pipeline = Yes */
                updateCFPBSettingsForUsers::getReport($processingCompanyId); // refactored
            }
        }


        if ($processingCompanyId > 0) {

            $mimeTypeArray = ['image/gif', /**** gif *****/
                'image/jpeg', 'image/pjpeg', /**** jpeg *****/
                'image/png', 'image/x-png'/**** png *****/
            ];


            $file_type = $_FILES['logoName']['type'];
            if ($file_type != '') {
                if (in_array($file_type, $mimeTypeArray)) {
                    if ($_FILES['logoName']['name'] != '') {
                        if (!(is_dir(CONST_PATH_PC_LOGO))) mkdir(CONST_PATH_PC_LOGO);
                        $specialCharacters = ['%', '+', '&', '@', '?', '#', '*', '$', '^', '!'];
                        $spaces = [' ', '  ', '   ', '    ', '     '];
                        $file_name = str_replace($spaces, '_', $_FILES['logoName']['name']);
                        $file_name = str_replace($specialCharacters, '', $file_name);
                        $logo = 'logo_PC_' . $processingCompanyId . '_' . $file_name;
                        $fileTempName = $_FILES['logoName']['tmp_name'];
                        $logoExit = file_exists(CONST_PATH_PC_LOGO . $logo);
                        if ($logoExit) unlink(CONST_PATH_PC_LOGO . basename($logo));
                        $pos = strrpos($logo, '.');
                        $replaceString = substr($logo, $pos + 1);
                        $logo = str_replace($replaceString, 'jpg', $logo);
                        $fileName = CONST_PATH_PC_LOGO . $logo;
                        if ($file_type == 'image/gif') {
                            convertGIF2JPG($fileTempName, $fileName, 'logo');
                        } elseif (($file_type == 'image/png') || ($file_type == 'image/x-png')) {
                            convertpng2JPG($fileTempName, $fileName, 'logo');
                        } else {
                            resizeJPGImg($fileTempName, $fileName, 'logo');
                        }
                        $infoArray['procCompLogo'] = $logo;
                    }
                    $infoArray['PCID'] = $processingCompanyId;
                    $upCnt = updatePCLogo::getReport($infoArray); // refactored
                    if ($upCnt > 0 && trim($logo) != '' && $userRole == 'Manager') Strings::SetSess('userLogo', $logo);
                } else {
                    Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
                }
            }
            if (array_key_exists('procCompLogo', $infoArray) || array_key_exists('procCompFooterLogo', $infoArray)) {
                $infoArray['PCID'] = $processingCompanyId;
                $upCnt = updatePCLogo::getReport($infoArray); // refactored
                if ($upCnt > 0 && trim($logo) != '' && $userRole == 'Manager') Strings::SetSess('userLogo', $logo);
            }

        }


        if ($alowareErr != '') Strings::SetSess('msg', $alowareErr); // Aloware Error messages.
        if ($webhook == 1) {
            if ($insertCount > 0) {
                echo 'PC created successfully.';
            } elseif ($updateCount > 0 || $Count > 0) {
                echo 'Update successfully';
            } else {
                echo 'Error while creating PC.';
            }
        }

        if ($btnValue == 'Save & Next') {
            $tabNumb = '2';
        } else {
            $tabNumb = '1';
        }

        return '/backoffice/createProcessingCompany.php?pcId=' . cypher::myEncryption($processingCompanyId) . '&tabNumb=' . $tabNumb;
    }
}
