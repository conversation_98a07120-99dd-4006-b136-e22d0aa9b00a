<?php

namespace pages\backoffice\businessEntity;

use models\composite\oFile\getFileEntityMembers;
use models\constants\borrowerBusinessEntity;
use models\constants\gl\glDate;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\tblMemberOfficerInfo;
use models\PageVariables;
use models\portals\BackofficePage;
use models\Request;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;

class businessEntity extends BackofficePage
{

    public static ?string $number = null;
    public static ?string $html = null;
    public static ?string $fileRow = null;
    public static ?int $publicUser = null;

    public static function Init()
    {
        PageVariables::$hideNotices = true; // hide the debug output for form fields

        parent::Init();

    }

    public static function Post()
    {
        $activeTab = cypher::myDecryption(Request::GetClean('activeTab')) ?? '';
        switch ($activeTab) {
            case 'QAPP':
                $activeTab = 'QA';
                break;
            case 'LI':
                $activeTab = 'FA';
                break;
            default:
                $activeTab = 'BO';
                break;
        }
        self::$fileRow = Request::GetClean('fileRow');
        self::$publicUser = intval(Request::GetClean('publicUser'));
        loanForm::init(PageVariables::$PCID, $activeTab);
        loanForm::$fileTab = $activeTab;
        loanForm::pushSectionID('BEN');
        $number = Request::GetClean('number');
        $LMRId = cypher::myDecryption(Request::GetClean('lmrid'));
        $memberId = cypher::myDecryption(Request::GetClean('memberId'));
        self::$html = '';
        $rootEntityMember = getFileEntityMembers::getRootMembers((int)$LMRId, (int)$memberId); //rootMembers
        if (!$rootEntityMember) {
            for ($i = 1; $i <= $number; $i++) {
                $table = new tblMemberOfficerInfo();
                $table->parent_id = intval($memberId); //new data
                self::$html .= self::getAccordion(0, $i, $table);
            }
        }
        loanForm::popSectionID();
        HTTP::ExitJSON([
            'html' => self::$html
        ]);
    }
    public static function getAccordion($inc, $i, $item): string
    {
        //$inc = $inc + 1;
        $ml = $inc * 5;
        $row = $inc = $inc + 1;
        if (!$item->memberType) {
            $item->memberType = 'Individual'; //Default
        }
        $accordionBorderClass = ($i <= 2) ? 'border border-secondary' : '';
        $individualChecked = $item->memberType == 'Individual' ? 'checked=checked' : '';
        $entityChecked = $item->memberType == 'Entity' ? 'checked=checked' : '';

        $memberPersonalGuaranteeYes = $item->memberPersonalGuarantee == 'Yes' ? 'checked=checked' : '';
        $memberPersonalGuaranteeNo = $item->memberPersonalGuarantee == 'No' ? 'checked=checked' : '';
        $memberAuthorizedSignerYes = $item->memberAuthorizedSigner == 'Yes' ? 'checked=checked' : '';
        $memberAuthorizedSignerNo = $item->memberAuthorizedSigner == 'No' ? 'checked=checked' : '';
        $memberCitizenshipUSCitizen = $item->memberCitizenship == 'U.S. Citizen' ? 'checked=checked' : '';
        $memberCitizenshipPermResident = $item->memberCitizenship == 'Perm Resident Alien' ? 'checked=checked' : '';
        $memberCitizenshipNonPermResident = $item->memberCitizenship == 'Non-Perm Resident Alien' ? 'checked=checked' : '';
        $memberCitizenshipForeignNational = $item->memberCitizenship == 'Foreign National' ? 'checked=checked' : '';


        //Hide or Show fields based on member type and form field configuration
        $searchString = 'secHide';
        $memberTitleHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberTitle'), $searchString)) ? 'secHide' : '';
        $memberAnnualSalaryHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberAnnualSalary'), $searchString)) ? 'secHide' : '';
        $memberSSNHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberSSN'), $searchString)) ? 'secHide' : '';
        $memberDOBHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberDOB'), $searchString)) ? 'secHide' : '';
        $memberCreditScoreHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCreditScore'), $searchString)) ? 'secHide' : '';
        $memberEmailHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberEmail'), $searchString)) ? 'secHide' : '';
        $memberDriversLicenseHideShow  = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberDriversLicense'), $searchString)) ? 'secHide' : '';
        $memberDriversLicenseStateHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberDriversLicenseState'), $searchString)) ? 'secHide' : '';
        $memberTinHideShow = ($item->memberType != 'Entity' || strpos(loanForm::showField('memberTin'), $searchString)) ? 'hide' : '';
        $memberPersonalGuaranteeHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberPersonalGuarantee'), $searchString)) ? 'secHide' : '';
        $memberAuthorizedSignerHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberAuthorizedSigner'), $searchString)) ? 'secHide' : '';
        $memberCitizenshipHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCitizenship'), $searchString)) ? 'secHide' : '';
        $memberMaritalStatusHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberMaritalStatus'), $searchString)) ? 'secHide' : '';
        $memberMarriageDateHideShow = (
            $item->memberType == 'Entity'
            || $item->memberMaritalStatus == 'Unmarried'
            || strpos(loanForm::showField('memberMarriageDate'), $searchString)
        ) ? 'secHide' : '';
        $memberDivorceDateHideShow = (
            $item->memberType == 'Entity'
            || $item->memberMaritalStatus == 'Unmarried'
            || $item->memberMaritalStatus == 'Married'
            || strpos(loanForm::showField('memberDivorceDate'), $searchString)
        ) ? 'secHide' : '';
        $memberMaidenNameHideShow = (
            $item->memberType == 'Entity'
            || $item->memberMaritalStatus == 'Unmarried'
            || strpos(loanForm::showField('memberMaidenName'), $searchString)
        ) ? 'secHide' : '';
        $memberSpouseNameHideShow = (
            $item->memberType == 'Entity'
            || $item->memberMaritalStatus == 'Unmarried'
            || strpos(loanForm::showField('memberSpouseName'), $searchString)
        ) ? 'secHide' : '';

        $memberCreditScoreDateHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCreditScoreDate'), $searchString)) ? 'secHide' : '';
        $memberRentOrOwnHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberRentOrOwn'), $searchString)) ? 'secHide' : '';
        $memberMonthlyRentOrMortgageHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberMonthlyRentOrMortgage'), $searchString)) ? 'secHide' : '';
        $memberDateMovedAddressHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberDateMovedAddress'), $searchString)) ? 'secHide' : '';
        $memberRealEstateValueHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberRealEstateValue'), $searchString)) ? 'secHide' : '';
        $memberRetirementAccountBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberRetirementAccountBalance'), $searchString)) ? 'secHide' : '';
        $memberCashSavingsStocksBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCashSavingsStocksBalance'), $searchString)) ? 'secHide' : '';
        $memberCreditCardBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCreditCardBalance'), $searchString)) ? 'secHide' : '';
        $memberMortgageBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberMortgageBalance'), $searchString)) ? 'secHide' : '';
        $memberAutoLoanBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberAutoLoanBalance'), $searchString)) ? 'secHide' : '';
        $memberTotalNetWorthHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberTotalNetWorth'), $searchString)) ? 'secHide' : '';

        $howManyMemberOfficerHideShow = $item->memberType != 'Entity' ? 'hide' : ''; //Entity only


        $html = '<div class="accordion ml-' . $ml . ' ' . $accordionBorderClass . '" id="accordionExample_' . $inc . '_' . $i . '">
    <div class="card" id="accordion_' . $inc . '_' . $i . '">
        <div class="card-header d-flex">
            <div class="col-md-6 card-title" data-toggle="collapse" data-target="#collapseOne_' . $inc . '_' . $i . '">
                Member/Officer #' . $i . '
            </div>
            <div class="col-md-6 text-right">
                <span class="btn btn-sm btn-danger btn-icon m-2 tooltipClass"
                    title="" data-toggle="tooltip" data-placement="top"
                    data-original-title="Click to remove Individual/Entity info"
                    data-rem-accordion="accordionExample_' . $inc . '_' . $i . '"
                    data-rem-tableid="' . cypher::myEncryption($item->memberId) . '"
                    data-rem-lmrid="' . cypher::myEncryption($item->LMRID) . '"
                    data-rem-rowid="' . $inc . '_' . $i . '"
                    data-rem-level="parent"
                    onclick="BusinessEntitySection.removeEntityMemberFieldsCV3(this);"
                    >
                        <i class="icon-1x fas fa-minus-circle"></i>
                </span>
                <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon m-2 toggleClass"
                      data-card-tool="toggle"
                      data-target="#collapseOne_' . $inc . '_' . $i . '"
                      data-toggle="collapse"
                      title=""
                      data-original-title="Toggle Card">
                    <i class="icon-1x ki ki-arrow-down icon-nm"></i>
                </span>
            </div>
        </div>
        <div id="collapseOne_' . $inc . '_' . $i . '" class="collapse" data-parent="#accordionExample_' . $inc . '_' . $i . '">
            <div class="card-body">
                <div class="row">
                    <input type="hidden" 
                        name="members[' . $inc . '][entityMember][' . $i . '][memberId]" 
                        id="memberId' . $inc . '_' . $i . '"
                        value="' . $item->memberId . '">
                        <input type="hidden" 
                        name="members[' . $inc . '][entityMember][' . $i . '][parent_id]"
                        id="parent_id_' . $inc . '_' . $i . '"
                        value="' . $item->parent_id . '">
                    <div class="col-md-3">
                        <div class="radio-inline">
                            <label class="radio">
                                <input
                                type="radio"
                                name="members[' . $inc . '][entityMember][' . $i . '][memberType]"
                                value="Individual"
                                id="Individual_' . $inc . '_' . $i . '"
                                ' . $individualChecked . '
                                onchange="BusinessEntitySection.memberTypeShowHideFieldsCV3(' . "'Individual_$inc" . "_" . "$i'" . ',this);"
                                >
                                <span></span>Individual
                            </label>
                            <label class="radio">
                                <input
                                type="radio"
                                name="members[' . $inc . '][entityMember][' . $i . '][memberType]"
                                value="Entity"
                                id="Entity_' . $inc . '_' . $i . '"
                                ' . $entityChecked . '
                                onchange="BusinessEntitySection.memberTypeShowHideFieldsCV3(' . "'Entity_$inc" . "_" . "$i'" . ',this);"
                                >
                                <span></span>Entity
                            </label>';
        if (self::$fileRow != 'Insert' || !self::$publicUser) {
            $html .= '<span class="font-weight-bold text-danger hide" id="new_member_' . $inc . '_' . $i . '">
                                Please save to be able to add members to this entity.
                            </span>';
        }
        $html .= '</div>
                    </div>
                    <div class="col-md-3 ' . loanForm::showField('memberName') . ' " id="">
                        <div class="form-group">
                            ' . loanForm::label('memberName') . '
                            <div class="">
                                <input name="members[' . $inc . '][entityMember][' . $i . '][memberName]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberName') . ' "
                                        type="text" autocomplete="off"
                                        value="' . $item->memberName . '"
                                        id="memberName' . $inc . '_' . $i . '" ' . loanForm::isEnabled('memberName') . '>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberTitleHideShow . ' " id="memberTitle_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberTitle') . '
                            <div class="">
                                <input name="members[' . $inc . '][entityMember][' . $i . '][memberTitle]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberTitle') . ' "
                                        type="text" autocomplete="off"
                                        value="' . $item->memberTitle . '"
                                        id="memberTitle' . $inc . '_' . $i . '" ' . loanForm::isEnabled('memberTitle') . '>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . loanForm::showField('memberCategory') . ' " id="memberCategory_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCategory') . '
                            <div class="">
                                <select name="members[' . $inc . '][entityMember][' . $i . '][memberCategory]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberCategory') . ' "
                                        id="memberCategory' . $inc . '_' . $i . '" ' . loanForm::isEnabled('memberCategory') . '>
                                    <option value="">-Select-</option>';
                                    foreach (borrowerBusinessEntity::getEntityTypes() as $entityMemberCategory) {
                                        $html .= '<option value="' . $entityMemberCategory . '">' . $entityMemberCategory . '</option>';
                                    }
                            $html .=    '</select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . loanForm::showField('memberOwnership') . ' " id="">
                        <div class="form-group">
                            ' . loanForm::label('memberOwnership') . '
                            <div class="">
                                <div class="input-group">
                                    <input name="members[' . $inc . '][entityMember][' . $i . '][memberOwnership]"
                                    class="form-control input-sm memberOwnership_' . $inc . ' ' . loanForm::isMandatory('memberOwnership') . '"
                                    type="text"  autocomplete="off"
                                    value="' . $item->memberOwnership . '"
                                    id="memberOwnership' . $inc . '_' . $i . '"
                                    ' . loanForm::isEnabled('memberOwnership') . '
                                    onblur="BusinessEntitySection.calculateOwnerShip(this)">
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            %
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberAnnualSalaryHideShow . ' " id="memberAnnualSalary_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberAnnualSalary') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberAnnualSalary]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberAnnualSalary') . '"
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberAnnualSalary) . '"
                                        ' . loanForm::isEnabled('memberAnnualSalary') . '
                                        id="memberAnnualSalary' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . loanForm::showField('memberAddress') . ' " id="">
                        <div class="form-group">
                            ' . loanForm::label('memberAddress') . '
                            <script>
                                $(document).ready(function() {
                                    $("#memberAddress' . $inc . '_' . $i . '").on("input", function() {
                                        address_lookup.InitLegacy($(this));
                                    });
                                });
                            </script>
                            <div class="">
                                <input name="members[' . $inc . '][entityMember][' . $i . '][memberAddress]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberAddress') . '"
                                        type="text"  autocomplete="off"
                                        value="' . $item->memberAddress . '"
                                        ' . loanForm::isEnabled('memberAddress') . '
                                        id="memberAddress' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . loanForm::showField('memberPhone') . ' " id="">
                        <div class="form-group">
                            ' . loanForm::label('memberPhone') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberPhone]"
                                        class="form-control input-sm mask_phone ' . loanForm::isMandatory('memberPhone') . ' "
                                        type="text"  autocomplete="off"
                                        placeholder="(___) ___ - ____ Ext ____"
                                        value="' . Strings::formatPhoneNumber($item->memberPhone) . '"
                                        ' . loanForm::isEnabled('memberPhone') . '
                                        id="memberPhone' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . loanForm::showField('memberCell') . ' " id="">
                        <div class="form-group">
                            ' . loanForm::label('memberCell') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCell]"
                                        class="form-control input-sm mask_cellnew ' . loanForm::isMandatory('memberCell') . ' "
                                        type="text"  autocomplete="off"
                                        placeholder="(___) ___ - ____"
                                        value="' . Strings::formatCellNumber($item->memberCell) . '"
                                        ' . loanForm::isEnabled('memberCell') . '
                                        id="memberCell' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberSSNHideShow . ' " id="memberSSN_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberSSN') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberSSN]"
                                        class="form-control input-sm mask_ssn ' . loanForm::isMandatory('memberSSN') . ' "
                                        type="text"  autocomplete="off"
                                        placeholder="___-__-____"
                                        value="' . Strings::formatSSNNumber($item->memberSSN) . '"
                                        ' . loanForm::isEnabled('memberSSN') . '
                                        id="memberSSN' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDOBHideShow . ' " id="memberDOB_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDOB') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberDOB]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDOB') . '"
                                        type="text" autocomplete="off"
                                        placeholder="MM/DD/YYYY"
                                        data-date-dob-start-date="' . glDate::getMinRequirementDate() . '"
                                        data-date-dob-end-date="' . glDate::getMaxRequirementDate() . '"
                                        value="' . Dates::formatDateWithRE($item->memberDOB, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberDOB') . '
                                        id="memberDOB' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberEmailHideShow . ' " id="memberEmail_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberEmail') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberEmail]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberEmail') . ' "
                                        type="text"  autocomplete="off"
                                        value="' . $item->memberEmail . '"
                                        ' . loanForm::isEnabled('memberEmail') . '
                                        id="memberEmail' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCreditScoreHideShow . ' " id="memberCreditScore_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCreditScore') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCreditScore]"
                                        class="form-control input-sm memberCreditScore ' . loanForm::isMandatory('memberCreditScore') . ' "
                                        type="text"  autocomplete="off"
                                        value="' . $item->memberCreditScore . '"
                                        ' . loanForm::isEnabled('memberCreditScore') . '
                                        id="memberCreditScore' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCreditScoreDateHideShow . ' " id="memberCreditScoreDate_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCreditScoreDate') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCreditScoreDate]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberCreditScoreDate') . '"
                                        type="text" autocomplete="off"
                                        placeholder="MM/DD/YYYY"                                        
                                        value="' . Dates::formatDateWithRE($item->memberCreditScoreDate, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberCreditScoreDate') . '
                                        id="memberDOB' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberRentOrOwnHideShow . ' " id="memberRentOrOwn_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberRentOrOwn') . '
                            <div class="">
                                <select name="members[' . $inc . '][entityMember][' . $i . '][memberRentOrOwn]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberRentOrOwn') . ' "
                                        ' . loanForm::isEnabled('memberRentOrOwn') . '
                                        id="memberRentOrOwn' . $inc . '_' . $i . '">
                                    <option value="">-Select-</option>
                                    <option value="Rent">Rent</option>
                                    <option value="Own">Own</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMonthlyRentOrMortgageHideShow . ' " id="memberMonthlyRentOrMortgage_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMonthlyRentOrMortgage') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberMonthlyRentOrMortgage]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberMonthlyRentOrMortgage') . '"
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberMonthlyRentOrMortgage) . '"
                                        ' . loanForm::isEnabled('memberMonthlyRentOrMortgage') . '
                                        id="memberMonthlyRentOrMortgage' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDateMovedAddressHideShow . ' " id="memberDateMovedAddress_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDateMovedAddress') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberDateMovedAddress]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDateMovedAddress') . '"
                                        type="text" autocomplete="off"
                                        placeholder="MM/DD/YYYY"                                        
                                        value="' . Dates::formatDateWithRE($item->memberDateMovedAddress, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberDateMovedAddress') . '
                                        id="memberDateMovedAddress' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberRealEstateValueHideShow . ' " id="memberRealEstateValue_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberRealEstateValue') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberRealEstateValue]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberRealEstateValue') . '"
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberRealEstateValue) . '"
                                        data-index = "' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberRealEstateValue') . '
                                        id="memberRealEstateValue' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberRetirementAccountBalanceHideShow . ' " id="memberRetirementAccountBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberRetirementAccountBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberRetirementAccountBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberRetirementAccountBalance') . '"
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberRetirementAccountBalance) . '"
                                        data-index = "' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberRetirementAccountBalance') . '
                                        id="memberRetirementAccountBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCashSavingsStocksBalanceHideShow . ' " id="memberCashSavingsStocksBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCashSavingsStocksBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCashSavingsStocksBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberCashSavingsStocksBalance') . '"
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberCashSavingsStocksBalance) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberCashSavingsStocksBalance') . '
                                        id="memberCashSavingsStocksBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCreditCardBalanceHideShow . ' " id="memberCreditCardBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCreditCardBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCreditCardBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberCreditCardBalance') . '"
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberCreditCardBalance) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberCreditCardBalance') . '
                                        id="memberCreditCardBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMortgageBalanceHideShow . ' " id="memberMortgageBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMortgageBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberMortgageBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberMortgageBalance') . '"
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberMortgageBalance) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberMortgageBalance') . '
                                        id="memberMortgageBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberAutoLoanBalanceHideShow . ' " id="memberAutoLoanBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberAutoLoanBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberAutoLoanBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberAutoLoanBalance') . '"
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberAutoLoanBalance) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberAutoLoanBalance') . '
                                        id="memberAutoLoanBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberTotalNetWorthHideShow . ' " id="memberTotalNetWorth_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberTotalNetWorth') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input class="form-control input-sm" readonly
                                        type="text"  autocomplete="off" onblur="currencyConverter(this, this.value)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberTotalNetWorth) . '"
                                        ' . loanForm::isEnabled('memberTotalNetWorth') . '
                                        id="memberTotalNetWorth' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>                    
                    <div class="col-md-3 ' . $memberMaritalStatusHideShow . ' " id="memberMaritalStatus_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMaritalStatus') . '
                            <div class="">
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusUnmarried_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberMaritalStatus') . '" 
                                        data-index="' . $inc . '_' . $i . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberMaritalStatus]"
                                        id="memberMaritalStatusUnmarried_' . $inc . '_' . $i . '"
                                        value="Unmarried" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                                        ' . ($item->memberMaritalStatus == 'Unmarried' ? 'checked=checked' : '') . '
                                        ' . loanForm::isEnabled('memberMaritalStatus') . '
                                        ><span></span>Unmarried
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusMarried_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberMaritalStatus') . '" 
                                        data-index="' . $inc . '_' . $i . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberMaritalStatus]"
                                        id="memberMaritalStatusMarried_' . $inc . '_' . $i . '"
                                        value="Married" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                                        ' . ($item->memberMaritalStatus == 'Married' ? 'checked=checked' : '') . '
                                        ' . loanForm::isEnabled('memberMaritalStatus') . '
                                        ><span></span>Married
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusSeparated_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberMaritalStatus') . '" 
                                        data-index="' . $inc . '_' . $i . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberMaritalStatus]"
                                        id="memberMaritalStatusSeparated_' . $inc . '_' . $i . '"
                                        value="Separated" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                                        ' . ($item->memberMaritalStatus == 'Separated' ? 'checked=checked' : '') . '
                                        ' . loanForm::isEnabled('memberMaritalStatus') . '
                                        ><span></span>Separated
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMarriageDateHideShow . ' " id="memberMarriageDate_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMarriageDate') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberMarriageDate]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberMarriageDate') . '"
                                        type="text"
                                        placeholder="MM/DD/YYYY"
                                        value="' . Dates::formatDateWithRE($item->memberMarriageDate, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberMarriageDate') . '
                                        id="memberMarriageDate' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDivorceDateHideShow . ' " id="memberDivorceDate_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDivorceDate') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberDivorceDate]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDivorceDate') . '"
                                        type="text"
                                        placeholder="MM/DD/YYYY"
                                        value="' . Dates::formatDateWithRE($item->memberDivorceDate, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberDivorceDate') . '
                                        id="memberDivorceDate' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMaidenNameHideShow . ' " id="memberMaidenName_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMaidenName') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberMaidenName]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberMaidenName') . ' "
                                        type="text"
                                        value="' . $item->memberMaidenName . '"
                                        ' . loanForm::isEnabled('memberMaidenName') . '
                                        id="memberEmail' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberSpouseNameHideShow . ' " id="memberSpouseName_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberSpouseName') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberSpouseName]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberSpouseName') . ' "
                                        type="text"
                                        value="' . $item->memberSpouseName . '"
                                        ' . loanForm::isEnabled('memberSpouseName') . '
                                        id="memberEmail' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDriversLicenseStateHideShow . ' " id="memberDriversLicenseState_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDriversLicenseState') . '
                            <div class="">
                                <select name="members[' . $inc . '][entityMember][' . $i . '][memberDriversLicenseState]"
                                class="form-control input-sm ' . loanForm::isMandatory('memberDriversLicenseState') . '"
                                    ' . loanForm::isEnabled('memberDriversLicenseState') . '
                                id="memberDriversLicenseState' . $inc . '_' . $i . '">
                                    <option value="">-Select-</option>';
                                    foreach(Arrays::fetchStates() as $dlState) {
                                        $html .= '<option value="' . $dlState['stateCode'] . '">' . $dlState['stateName'] . '</option>';
                                    }
                            $html .= '</select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDriversLicenseHideShow . ' " id="memberDriversLicense_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDriversLicense') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberDriversLicense]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberDriversLicense') . ' "
                                        type="text"  autocomplete="off"
                                        value="' . $item->memberDriversLicense . '"
                                        ' . loanForm::isEnabled('memberDriversLicense') . '
                                        id="memberDriversLicense' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberTinHideShow . ' " id="memberTin_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberTin') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberTin]"
                                        class="form-control input-sm mask_ein ' . loanForm::isMandatory('memberTin') . ' "
                                        type="text"
                                        autocomplete="off"
                                        placeholder="__-_______"
                                        value="' . $item->memberTin . '"
                                        ' . loanForm::isEnabled('memberTin') . '
                                        id="memberTin' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberPersonalGuaranteeHideShow . ' " id="memberPersonalGuarantee_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberPersonalGuarantee') . '
                            <div class="">
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="memberPersonalGuaranteeYes_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberPersonalGuarantee') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberPersonalGuarantee]"
                                        id="memberPersonalGuaranteeYes_' . $inc . '_' . $i . '"
                                        ' . $memberPersonalGuaranteeYes . '
                                        ' . loanForm::isEnabled('memberPersonalGuarantee') . '
                                        value="Yes"><span></span>Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberPersonalGuaranteeNo_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberPersonalGuarantee') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberPersonalGuarantee]"
                                        id="memberPersonalGuaranteeNo_' . $inc . '_' . $i . '"
                                        ' . $memberPersonalGuaranteeNo . '
                                        ' . loanForm::isEnabled('memberPersonalGuarantee') . '
                                        value="No"><span></span>No
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberAuthorizedSignerHideShow . ' " id="memberAuthorizedSigner_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberAuthorizedSigner') . '
                            <div class="">
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="memberAuthorizedSignerYes_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberAuthorizedSigner') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberAuthorizedSigner]"
                                        id="memberAuthorizedSignerYes_' . $inc . '_' . $i . '"
                                        ' . $memberAuthorizedSignerYes . '
                                        ' . loanForm::isEnabled('memberAuthorizedSigner') . '
                                        value="Yes"><span></span>Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberAuthorizedSignerNo_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberAuthorizedSigner') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberAuthorizedSigner]"
                                        id="memberAuthorizedSignerNo_' . $inc . '_' . $i . '"
                                        ' . $memberAuthorizedSignerNo . '
                                        ' . loanForm::isEnabled('memberAuthorizedSigner') . '
                                        value="No"><span></span>No
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCitizenshipHideShow . '  " id="memberCitizenship_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCitizenship') . '
                            <div class="">
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="memberCitizenship_us_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberCitizenship') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberCitizenship]"
                                        id="memberCitizenship_us_' . $inc . '_' . $i . '"
                                        ' . $memberCitizenshipUSCitizen . '
                                        ' . loanForm::isEnabled('memberCitizenship') . '
                                        value="U.S. Citizen"><span></span>U.S.
                                        Citizen
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberCitizenship_pra_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberCitizenship') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberCitizenship]"
                                        id="memberCitizenship_pra_' . $inc . '_' . $i . '"
                                        ' . $memberCitizenshipPermResident . '
                                        ' . loanForm::isEnabled('memberCitizenship') . '
                                        value="Perm Resident Alien"><span></span>Perm
                                        Resident
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberCitizenship_npra_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberCitizenship') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberCitizenship]"
                                        id="memberCitizenship_npra_' . $inc . '_' . $i . '"
                                        ' . $memberCitizenshipNonPermResident . '
                                        ' . loanForm::isEnabled('memberCitizenship') . '
                                        value="Non-Perm Resident Alien"><span></span>Non-Perm
                                        Resident
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberCitizenship_fn_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberCitizenship') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberCitizenship]"
                                        id="memberCitizenship_fn_' . $inc . '_' . $i . '"
                                        ' . $memberCitizenshipForeignNational . '
                                        ' . loanForm::isEnabled('memberCitizenship') . '
                                        value="Foreign National"><span></span>Foreign
                                        National
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>';
        if ($item->memberId) {
            $html .= '<div class="row">
                        <div class="col-md-6 ' . $howManyMemberOfficerHideShow . ' " id="howManyMemberOfficer_' . $inc . '_' . $i . '">
                            <div class="form-group row">
                                <label class="col-md-10 font-weight-bold">
                                    How many members/officers are there with 20%+ ownership?
                                </label>
                                <div class="col-md-2">
                                    <input
                                            class="form-control input-sm"
                                            type="text"  autocomplete="off"
                                            name=""
                                            value=""
                                            id="formContainer_' . $row . '_' . $i . '"
                                            data-row = "' . $row . '"
                                            data-inc="' . $i . '"
                                            data-lmrid="' . cypher::myEncryption($item->LMRID) . '"
                                            data-memberId="' . cypher::myEncryption($item->memberId) . '"
                                            onchange="BusinessEntitySection.parentEntityMemberFields(this, ' . $i . ')"
                                            >
                                </div>
                            </div>
                        </div>
            </div>';
        }
        $html.='<div id="formContainer_' . $row . '_' . $i . '"></div>';
        $html.='</div>
        </div>
    </div>
</div>';
        return $html;
    }

}
