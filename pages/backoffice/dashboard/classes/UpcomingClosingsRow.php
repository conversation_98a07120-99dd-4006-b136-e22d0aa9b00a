<?php

namespace pages\backoffice\dashboard\classes;

use models\cypher;
use models\PageVariables;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class UpcomingClosingsRow extends strongType
{
    public ?string $serviceType = null;
    public ?string $processorCommentsNotesFinal = null;
    public ?string $primaryStatus = null;
    public ?string $typeOfHMLOLoanRequesting = null;
    public ?string $closedDate = null;
    public ?float $loanAmt = null;
    public ?string $borrowerLink = null;
    public ?string $infoAddressCreateDate = null;
    public ?string $assignedStaffText = null;
    public ?string $assignBranchAgentText = null;
    public ?int $LMRId = null;
    public ?string $processorCommentHeaderButton = null;
    public ?string $clientName = null;
    public ?int $LMRExecutiveId = null;
    public ?int $brokerNumber = null;
    public ?string $targetClosingDate = null;
    public ?float $initailLoanAmount = null;

    public static function fromUpcomingClosingsDTO(UpcomingClosingsDTO $item): UpcomingClosingsRow
    {
        $serviceType = '';
        $assignedStaffForFile = [];

        $closedDate = $item->closingDate;
        $targetClosingDate = $item->targetClosingDate;
        $borrowerName = $item->borrowerName;
        $borrowerLName = $item->borrowerLName;
        $entityName = $item->entityName;
        $clientType = $item->ClientType;
        $primaryStatus = $item->primaryStatus;
        $createdDate = $item->recordDate;
        $LMRId = $item->LMRId;
        $brokerNumber = $item->brokerNumber;
        $secondaryBrokerNumber = $item->secondaryBrokerNumber;
        $LMRExecutiveId = $item->FBRID;
        if (!in_array($clientType, ['ablc', 'bloc', 'e', 'ff', 'mca', 'SBA', 'sbad', 'WC'])) {  //sc8366  //hide transaction type
            $typeOfHMLOLoanRequesting = $item->typeOfHMLOLoanRequesting;
        } else {
            $typeOfHMLOLoanRequesting = '';
        }
        $closedDate = Dates::StandardDate($closedDate, '');
        $targetClosingDate = Dates::StandardDate($targetClosingDate, '');
        $createdDate = Dates::StandardDate($createdDate, '');

        $clientName = ucwords($borrowerLName . ' ' . $borrowerName);

        if ($entityName != '') $clientName .= '(Entity Name: ' . ucwords($entityName) . ')';

        $subjectPropAddress = ucfirst(Strings::arrayToString([
            $item->propertyAddress,
            $item->propertyUnit,
            $item->propertyCity,
            Strings::convertState($item->propertyState),
            $item->propertyZipCode,
        ]));

        $subjectPropAddress = $subjectPropAddress ? $subjectPropAddress . '<br>' : '';

        if (PageVariables::$userRole == 'Branch') {
            $fileUrl = CONST_URL_BRSSL . 'LMRequest.php?eId=' . cypher::myEncryption($LMRExecutiveId) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&tabOpt=LI';
        } else if (PageVariables::$userRole == 'Agent') {
            $fileUrl = CONST_URL_AG_SSL . 'LMRequest.php?eId=' . cypher::myEncryption($LMRExecutiveId) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&tabOpt=LI&amp;eOpt=1';
        } else {
            $fileUrl = CONST_URL_BOSSL . 'LMRequest.php?eId=' . cypher::myEncryption($LMRExecutiveId) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&tabOpt=LI';
        }

        $borrowerLink = $clientName;

        if ($fileUrl != '') {
            $borrowerLink = "<a data-lmrid=\"$LMRId\" target=\"_blank\" class=\"text-primary font-weight-bold text-hover-primary mb-1 font-size-h7\" href=\"" . $fileUrl . "\" style=\"text-decoration:none\" alt=\"Open file\" title=\"Open file\">" . $clientName . '</a>';
        }
        $infoAddressCreateDate = '<span class="text-muted font-size-h9">' . $subjectPropAddress . 'Created:' . $createdDate . '</span>';

        foreach (UpcomingClosingsReport::$PCServiceTypeArray as $type) {
            if ($clientType == $type->LMRClientType) {
                $serviceType = $type->serviceType;
            }
        }

        /** Assigned Employee, Branch and Agent **/
        $assignedStaffText = '<b>Click to View/Assign Employee(s)</b><hr>';
        $assignBranchAgentText = '';
        if (array_key_exists($LMRId, UpcomingClosingsReport::$assignedStaffArray)) {
            $assignedStaffForFile = UpcomingClosingsReport::$assignedStaffArray[$LMRId];
        }
        if (count($assignedStaffForFile) > 0) {
            foreach ($assignedStaffForFile as $staff) {
                $assignedStaffText .= ucwords($staff->processorName) . ' (<b>' . ucwords($staff->role) . '</b>)<br>';
            }
        } else {
            $assignedStaffText .= 'No Assigned Employee(s)';
        }

        if (array_key_exists($LMRExecutiveId, UpcomingClosingsReport::$assignedBranchesArray)) {
            $assignBranchAgentText .= ucwords(UpcomingClosingsReport::$assignedBranchesArray[$LMRExecutiveId]->LMRExecutive) . ' (<b> Branch </b>)<br>';
        }
        if (array_key_exists($brokerNumber, UpcomingClosingsReport::$assignedAgentsArray)) {
            $assignBranchAgentText .= ucwords(UpcomingClosingsReport::$assignedAgentsArray[$brokerNumber]->agentName) . ' (<b> Broker </b>)<br>';
        }

        if (array_key_exists($secondaryBrokerNumber, UpcomingClosingsReport::$assignedLoanOfficersArray)) {
            $assignBranchAgentText .= ucwords(UpcomingClosingsReport::$assignedLoanOfficersArray[$secondaryBrokerNumber]->agentName) . ' (<b> Loan Officer </b>)<br>';
        }
        /** Assigned Employee, Branch and Agent **/


        $commentIcon = $item->CommentCount ? 'fa-comments' : 'fa-comment-medical';

        $encLMRId = cypher::myEncryption($LMRId);
        $encPCID = cypher::myEncryption($item->FPCID);
        $encViewPrivateNotes = cypher::myEncryption(PageVariables::$viewPrivateNotes);
        $encViewPublicNotes = cypher::myEncryption(PageVariables::$viewPublicNotes);

        $processorCommentHeaderButton = '
            <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 notes_last_10"
                    data-loaded="0"
                    data-type="upcoming"
                    data-lmrid="' . $encLMRId . '"
                    data-pcid="' . $encPCID . '"
                    data-public="' . $encViewPrivateNotes . '"
                    data-private="' . $encViewPublicNotes . '"
                    href="#"                                            
                    data-href="' . CONST_URL_POPS . 'addNotes.php"
                    data-wsize = "modal-xl"
                    data-name="File: ' . htmlentities($borrowerName . ' ' . $borrowerLName) . '> Add Note"
                    data-toggle="modal" 
                    data-target="#exampleModal1"
                    data-id="exID=' . cypher::myEncryption($LMRExecutiveId) . '&LMRId=' . cypher::myEncryption($LMRId) . '&showSaveBtn=1">                                    <i class="icon-md fas ' . $commentIcon . ' "></i>
 </a>';

        $processorCommentHeaderButtonAdd = '
            <a class="btn btn-xs btn-primary btn-text-primary btn-hover-primary btn-icon mr-2 tooltipClass notes_last_10 "
                                                        title="Add Notes" 
                    data-loaded="0"
                    data-type="upcoming"
                    data-lmrid="' . $encLMRId . '"
                    data-pcid="' . $encPCID . '"
                    data-public="' . $encViewPrivateNotes . '"
                    data-private="' . $encViewPublicNotes . '"
                    href="#"                                            
                    data-href="' . CONST_URL_POPS . 'addNotes.php"
                    data-wsize = "modal-xl"
                    data-name="File: ' . htmlentities($borrowerName . ' ' . $borrowerLName) . '> Add Note"
                    data-toggle="modal" 
                    data-target="#exampleModal1"
                    data-id="exID=' . cypher::myEncryption($LMRExecutiveId) . '&LMRId=' . cypher::myEncryption($LMRId) . '&showSaveBtn=1">    
                                                    <i class="icon-sm fas fa-comment-medical "></i>
</a>';

        $processorCommentsNotesFinal = '<div class="card card-custom">
 <div class="card-header">
 <div class="card-title">
    <h3 class="card-label">Latest 25 notes are listed below</h3>
             ' . str_replace('notes_last_10', '', $processorCommentHeaderButtonAdd) . '
 </div>
        <div class="card-toolbar">
        ' . (PageVariables::$showSysGenNote ? '
        <div class="switch switch-sm switch-icon">
                                <label class="">Show system generated notes</label>
                                <label class="font-weight-bold">
                                    <input type="checkbox"
                                           class="form-control"
                                           onchange="fileCommon.showHideSysGenNotes(this);"
                                           checked>
                                    <span></span>
                                </label>
                            </div>' : '') . '
        </div>
 </div>
 <div class="card-body p-0" id="notes_last_10_upcoming_' . $encLMRId . '">Loading...</div>
</div>';


        $t = new self();
        $t->serviceType = $serviceType;
        $t->processorCommentsNotesFinal = $item->CommentCount ? $processorCommentsNotesFinal : '';
        $t->primaryStatus = $primaryStatus;
        $t->typeOfHMLOLoanRequesting = $typeOfHMLOLoanRequesting;
        $t->closedDate = $closedDate;
        $t->loanAmt = $item->totalAmt;
        $t->initailLoanAmount = $item->initailLoanAmount;
        $t->borrowerLink = $borrowerLink;
        $t->infoAddressCreateDate = $infoAddressCreateDate;
        $t->assignedStaffText = $assignedStaffText;
        $t->assignBranchAgentText = $assignBranchAgentText;
        $t->LMRId = $LMRId;
        $t->processorCommentHeaderButton = $processorCommentHeaderButton;
        $t->clientName = $clientName;
        $t->LMRExecutiveId = $LMRExecutiveId;
        $t->brokerNumber = $brokerNumber;
        $t->targetClosingDate = $targetClosingDate;
        return $t;
    }
}
