<?php

namespace pages\backoffice\dashboard\classes;

use models\APIHelper;
use models\cypher;
use models\Database2;
use models\standard\Strings;
use models\types\strongType;

class UpcomingClosingsDTO extends strongType
{
    public ?int $LMRId = null;
    public ?int $FPCID = null;
    public ?string $enc_borrowerName = null;
    public ?string $enc_borrowerLName = null;
    public ?string $borrowerName = null;
    public ?string $borrowerLName = null;
    public ?string $entityName = null;
    public ?string $lastUpdatedDate = null;
    public ?string $recordDate = null;
    public ?int $primeStatusId = null;
    public ?string $secondaryBrokerNumber = null;
    public ?string $brokerNumber = null;
    public ?int $FBRID = null;
    public ?string $propertyAddress = null;
    public ?string $ClientType = null;
    public ?string $primaryStatus = null;
    public ?string $typeOfHMLOLoanRequesting = null;
    public ?float $totalAmt = null;
    public ?string $closingDate = null;
    public ?string $myOpt = null;
    public ?int $CommentCount = null;
    public ?string $propertyCity = null;
    public ?string $propertyState = null;
    public ?string $propertyZipCode = null;
    public ?string $propertyUnit = null;
    public ?string $targetClosingDate = null;
    public ?float $initailLoanAmount = null;

    /**
     * @param int $PCID
     * @param array|null $branchIDs
     * @param array|null $agentIDs
     * @param int $viewPublicNotes
     * @param int $viewPrivateNotes
     * @return self[]
     */
    public static function getReport(
        int    $PCID,
        ?array $branchIDs,
        ?array $agentIDs,
        int $viewPublicNotes,
        int $viewPrivateNotes
    ): array
    {
        $sql = APIHelper::getSQL(__DIR__ . '/sql/UpcomingClosings.sql');
        $params = [
            'PCID' => $PCID,
            'branchIDs' => $branchIDs ? sizeof($branchIDs) : null,
            'agentIDs' => $agentIDs ? sizeof($agentIDs) : null,
            'viewPublicNotes' => $viewPublicNotes,
            'viewPrivateNotes' => $viewPrivateNotes,
        ];

        if($branchIDs) {
            $sql = str_replace('\'--branchIDs--\'',Database2::GetPlaceholders(sizeof($branchIDs), ':branchID', true),$sql);
            foreach($branchIDs as $i => $id) {
                $params['branchID' . $i] = $id;
            }
        }

        if($agentIDs) {
            $sql = str_replace('\'--agentIDs--\'',Database2::GetPlaceholders(sizeof($agentIDs), ':agentID', true),$sql);
            foreach($agentIDs as $i => $id) {
                $params['agentID' . $i] = $id;
            }
        }

        return Database2::getInstance()->queryData($sql, $params, function($row) {
            $t = new self($row);
            $t->borrowerName = cypher::myDecryption($t->enc_borrowerName);
            $t->borrowerLName = cypher::myDecryption($t->enc_borrowerLName);
            return $t;
        });
    }
}