SELECT t100.LMRId
     , t100.FPCID
     , t100.enc_borrowerName
     , t100.enc_borrowerLName
     , t160.entityName
     , t120.lastUpdatedDate
     , t100.recordDate
     , t110.primeStatusId
     , t100.secondary<PERSON>roke<PERSON><PERSON><PERSON><PERSON>
     , t100.broker<PERSON>umber
     , t100.FBRID
     , t180.propertyAddress
     , t180.propertyCity
     , t180.propertyState
     , t180.propertyZipCode
     , t180.propertyUnit
     , t130.ClientType
     , t140.primaryStatus
     , t150.typeOfHMLOLoanRequesting
     , (
     SELECT COUNT(*) FROM tblLMRProcessorComments t1000
                     WHERE t1000.fileID = t100.LMRId
                       AND (t1000.displayIn = 'NH' OR t1000.displayIn = 'BO' OR t1000.displayIn = 'SH')
                       AND ((@viewPrivateNotes = 1 AND @viewPublicNotes = 1 AND (t1000.private = 0 OR t1000.private = 1))
                         OR (@viewPrivateNotes = 1 AND @viewPublicNotes = 0 AND t1000.private = 1)
                         OR (@viewPrivateNotes = 0 AND @viewPublicNotes = 1 AND t1000.private = 0))
       ) AS CommentCount
     , t150.totalLoanAmount     AS totalAmt
     , t170.closingDate         AS closingDate
     , NULL                     AS borrowerName
     , NULL                     AS borrowerLName
     , t190.targetClosingDate   AS targetClosingDate
     , t200.InitialLoanAmount   As initailLoanAmount

FROM tblFile t100
         JOIN tblFileResponse t110 ON t110.LMRId = t100.LMRId AND t110.activeStatus = t100.activeStatus
         JOIN tblFileUpdatedDate t120 ON t100.LMRId = t120.fileID AND t100.activeStatus = 1
         JOIN tblLMRClientType t130 ON t100.LMRId = t130.LMRId
         JOIN tblPCPrimeStatus t140 ON t140.PSID = t110.primeStatusId AND t140.activeStatus = 1
         LEFT JOIN tblFileHMLONewLoanInfo t150 ON t100.LMRId = t150.fileID AND t110.LMRId = t150.fileID
         LEFT JOIN tblFileHMLOBusinessEntity t160 ON t100.LMRId = t160.fileID AND t110.LMRId = t160.fileID
         LEFT JOIN tblQAInfo t170 ON t170.LMRId = t100.LMRId
         LEFT JOIN tblProperties t180 ON t180.LMRId = t100.LMRId
         LEFT JOIN tblFileHMLO AS t190 ON t100.LMRId = t190.fileID
         LEFT JOIN tblFileCalculatedValues AS t200 ON t200.LMRId = t100.LMRId

WHERE t100.FPCID = @PCID
  AND (t170.closingDate >= CURDATE() OR t190.targetClosingDate >= CURDATE())
  AND (@branchIDs IS NULL OR t100.FBRID IN ('--branchIDs--'))
  AND (
        @agentIDs IS NULL OR t100.brokerNumber IN ('--agentIDs--')
        OR t100.secondaryBrokerNumber IN ('--agentIDs--')
      )
GROUP BY t100.LMRId
ORDER BY t170.closingDate, t100.LMRId
LIMIT 20
;