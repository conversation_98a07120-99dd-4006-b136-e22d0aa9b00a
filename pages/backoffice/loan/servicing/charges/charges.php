<?php

namespace pages\backoffice\loan\servicing\charges;

use models\Controllers\backoffice\servicingForm;
use models\lendingwise\tblCharges;
use models\portals\Breadcrumb;
use models\portals\ServicingPage;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;

class charges extends ServicingPage
{
    public static function Init()
    {
        parent::Init();

        Breadcrumb::$title = servicingForm::$loan->LMRId . ': ' . servicingForm::$loan->borrowerName . ' - Servicing';
    }

    public static function Post()
    {
        switch($_REQUEST['a'] ?? null) {
            case 'loadCharges':
                self::loadCharges();
                break;

            case 'deleteCharges':
                self::deleteCharges();
                break;

            case 'saveCharges':
                self::saveCharges();
                break;
        }
    }

    private static function saveCharges()
    {
        $charges_tblChargesId = $_REQUEST['charges_tblChargesId'] ?? null;
        $charges_description = $_REQUEST['charges_description'] ?? null;
        $charges_amount = Strings::toNumber($_REQUEST['charges_amount'] ?? null);
        $charges_dueDate = $_REQUEST['charges_dueDate'] ?? null;
        $charges_months = $_REQUEST['charges_months'] ?? null;

        $item = new tblCharges();
        $item->tblChargesId = $charges_tblChargesId ?: null;
        $item->LMRId = self::$LMRId;
        $item->dueDate = Dates::Datestamp($charges_dueDate);
        $item->description = $charges_description;
        $item->amount = $charges_amount;
        if (!$item->sourceKey) {
            $item->sourceKey = 'manual';
        }

        $item->Save();

        if (!$charges_tblChargesId && $charges_months) {
            for ($j = 1; $j < $charges_months; $j++) {
                $dueDate = strtotime(Dates::Datestamp($charges_dueDate));
                $dueDate = strtotime('+' . $j . ' months', $dueDate);

                $item = new tblCharges();
                $item->tblChargesId = null;
                $item->LMRId = self::$LMRId;
                $item->dueDate = $dueDate;
                $item->description = $charges_description;
                $item->amount = $charges_amount;
                if (!$item->sourceKey) {
                    $item->sourceKey = 'manual';
                }

                $item->Save();
            }
        }

        servicingForm::convert(false);
        HTTP::ExitJSON(['success' => true]);
    }

    private static function deleteCharges()
    {
        $id = $_REQUEST['charges_tblChargesId'] ?? null;
        if ($id) {
            $item = tblCharges::Get(['tblChargesId' => $id]);
            if (!$item || $item->LMRId !== self::$LMRId) {
                return;
            }
        } else {
            return;
        }

        $item->Delete();

        servicingForm::convert(false);
        HTTP::ExitJSON($item->toArray());
    }

    private static function loadCharges()
    {
        $id = $_REQUEST['charges_tblChargesId'] ?? null;
        if ($id) {
            $item = tblCharges::Get(['tblChargesId' => $id]);
            if (!$item || $item->LMRId !== self::$LMRId) {
                return;
            }
        } else {
            $item = new tblCharges();
        }

        HTTP::ExitJSON($item->toArray());
    }
}