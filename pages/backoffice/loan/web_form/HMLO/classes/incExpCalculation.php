<?php

namespace pages\backoffice\loan\web_form\HMLO\classes;

use DateTime;
use models\composite\calEffectiveGrossIncome;
use models\composite\oClient\getPCClientEntityInfo;
use models\composite\proposalFormula;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glDefaultBrokerInHMLOPC;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\glHMLOPresentOccupancy;
use models\constants\gl\glPCID;
use models\constants\gl\glprePaymentPenalty;
use models\constants\gl\glTypeOfHMLOLoanRequestingCahFlow;
use models\Controllers\backoffice\LMRequest;
use models\HMLOLoanTermsCalculation;
use models\myFileInfo\FileContacts\FileContactsEntry;
use models\PageVariables;
use models\servicing\LoanTerms;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class incExpCalculation extends strongType
{
    public static function Init()
    {
        $water1 = null;

        $glprePaymentPenalty = glprePaymentPenalty::getPrePaymentPenalty(LMRequest::$PCID);
        $glHMLOPresentOccupancy = (LMRequest::$PCID == glPCID::PCID_PROD_CV3) ? glHMLOPresentOccupancy::$glHMLOPresentOccupancyCV3 : glHMLOPresentOccupancy::$glHMLOPresentOccupancy;

        $glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;
        $glDefaultBrokerInHMLOPC = glDefaultBrokerInHMLOPC::$glDefaultBrokerInHMLOPC;

        if (PageVariables::$publicUser == 1) {
            if (in_array(PageVariables::$assignedPCID, $glDefaultBrokerInHMLOPC)) {
                $REBroker = 'No';
                $REBrokerYesBtn = 'disabled';
            }
        }

        $myFileInfo = LMRequest::myFileInfo();

        if ($myFileInfo->isHMLO()) {
            $isHMLOSelOpt = $myFileInfo->isHMLO();
        }

        $fileHMLOInfo = $myFileInfo->fileHMLOInfo();
        $fileHMLOAssetsInfo = $myFileInfo->fileHMLOAssetsInfo();
        $LMRInfo = $myFileInfo->LMRInfo();
        $fileContacts = $myFileInfo->fileContacts();
        $listingRealtorInfo = $myFileInfo->listingRealtorInfo();
        $fileHMLOPropertyInfo = $myFileInfo->fileHMLOPropertyInfo();
        $BrokerInfo = $myFileInfo->BrokerInfo();
        $moduleRequested = $myFileInfo->branchModuleInfo();
        $docArray = $myFileInfo->docArray();
        $LMRClientTypeInfo = $myFileInfo->LMRClientTypeInfo();
        $servicesRequested = $myFileInfo->branchClientTypeInfo();
        $fileModuleInfo = $myFileInfo->fileModuleInfo();
        $fileHMLOBackGroundInfo = $myFileInfo->fileHMLOBackGroundInfo();
        $incomeInfo = $myFileInfo->incomeInfo();
        $fileAdditionalGuarantorsInfo = $myFileInfo->AddGuarantorsInfo();
        $QAInfo = $myFileInfo->QAInfo();
        $fileExpFilpGroundUp = $myFileInfo->fileExpFilpGroundUp();
        $clientDocsArray = $myFileInfo->clientDocsArray();
        $fileHMLOEntityInfo = $myFileInfo->fileHMLOEntityInfo();
        $fileMemberOfficerInfo = $myFileInfo->fileMemberOfficerInfo();
        $fileHMLONewLoanInfo = $myFileInfo->fileHMLONewLoanInfo();
        $BranchInfo = $myFileInfo->BranchInfo();
        $PCInfo = $myFileInfo->PCInfo();
        $propertyValuationDocInfo = $myFileInfo->propertyValuation();
        $fileLOScheduleRealInfo = $myFileInfo->fileLOScheduleRealInfo();
        $propertyCountyInfo = $myFileInfo->propertyCountyInfo();
        $FileProInfo = $myFileInfo->FileProInfo();
        $fileBudgetAndDrawDoc = $myFileInfo->fileBudgetAndDrawDoc();
        $propertyValuationDocs = $myFileInfo->propertyValuationDocs();
        $filepaydownInfo = $myFileInfo->paydownInfo();
        $estimatedProjectCostArray = $myFileInfo->estimatedProjectCost();
        $creditorInfoArray = $myFileInfo->creditorInfo();
        $creditorInfoStatusArray = $myFileInfo->creditorInfoStatus();
        $collateralArray = $myFileInfo->collateralArray();
        $collateralValuesArray = $myFileInfo->collateralValuesArray();
        $propMgmtArray = $myFileInfo->propMgmtArray();
        $propMgmtDocsArray = $myFileInfo->propMgmtDocsArray();
        $fileHMLOEntityRefInfoArray = $myFileInfo->fileHMLOEntityRefInfo();
        $borrowerAlternateNamesArray = $myFileInfo->borrowerAlternateNames();
        $liabilitiesInfo = $myFileInfo->liabilitiesInfo();
        $fileHMLOExperienceInfo = $myFileInfo->fileHMLOExperienceInfo();

        $clientType = 'TBD';

        foreach ($LMRClientTypeInfo as $i => $item) {
            $clientType = $item->ClientType;
        }

        if ($LMRInfo) {
            $lien1Rate = $LMRInfo->lien1Rate;
            $lien1Terms = $LMRInfo->lien1Terms;
            $SID = LMRequest::myFileInfo()->RESTInfo()->SID;
            $maritalStatus = $LMRInfo->maritalStatus;
            $occupancy = $LMRInfo->occupancy;
        }

        $annualPremium = $fileHMLOPropertyInfo->annualPremium;
        $brokerName = ucwords($myFileInfo->BrokerInfo()->firstName . ' ' . $myFileInfo->BrokerInfo()->lastName);

        $secondaryBrokerName = ucwords($myFileInfo->SecondaryBrokerInfo()->firstName . ' ' . $myFileInfo->SecondaryBrokerInfo()->lastName);

        $purchaseCloseDate = $QAInfo->closingDate; /* Merge closing Date in Admin tab with Purchase / Target Closing Date in Loan info tab on Jul 26, 2017 - Ticket ID: **********/

        /** Broker Info Section **/
        $brokerFName = ucwords($myFileInfo->BrokerInfo()->firstName);
        $brokerLName = ucwords($myFileInfo->BrokerInfo()->lastName);
        $brokerCompany = ucwords($myFileInfo->BrokerInfo()->company);
        $brokerEmail = $myFileInfo->BrokerInfo()->email;
        $brokerPhone = $myFileInfo->BrokerInfo()->phoneNumber;
        $allowEditToIRAgent = $myFileInfo->BrokerInfo()->allowEditToIR;

        $brokerPhoneNumberArray = Strings::splitPhoneNumber($brokerPhone);
        if (count($brokerPhoneNumberArray) > 0) {
            $brokerPhNo1 = trim($brokerPhoneNumberArray['No1']);
            $brokerPhNo2 = trim($brokerPhoneNumberArray['No2']);
            $brokerPhNo3 = trim($brokerPhoneNumberArray['No3']);
            $brokerExt = trim($brokerPhoneNumberArray['Ext']);
        }
        /** Broker Info End **/


        /** Secondary Broker Info Section **/
        $secondaryBrokerFName = ucwords($myFileInfo->SecondaryBrokerInfo()->firstName);
        $secondaryBrokerLName = ucwords($myFileInfo->SecondaryBrokerInfo()->lastName);
        $secondaryBrokerCompany = ucwords($myFileInfo->SecondaryBrokerInfo()->company);
        $secondaryBrokerEmail = $myFileInfo->SecondaryBrokerInfo()->email;
        $secondaryBrokerPhone = $myFileInfo->SecondaryBrokerInfo()->phoneNumber;
        $secondaryBallowEditToIRAgent = $myFileInfo->SecondaryBrokerInfo()->allowEditToIR;

        $secondaryBrokerPhoneNumberArray = Strings::splitPhoneNumber($secondaryBrokerPhone);
        if (count($secondaryBrokerPhoneNumberArray) > 0) {
            $secondaryBokerPhNo1 = trim($secondaryBrokerPhoneNumberArray['No1']);
            $secondaryBrokerPhNo2 = trim($secondaryBrokerPhoneNumberArray['No2']);
            $secondaryBrokerPhNo3 = trim($secondaryBrokerPhoneNumberArray['No3']);
            $secondaryBrokerExt = trim($secondaryBrokerPhoneNumberArray['Ext']);
        }
        /** Secondary Broker Info End **/


        /** Branch Info Section **/
        if ($BranchInfo) {
            $allowEditToIRBranch = LMRequest::myFileInfo()->BranchInfo()->allowEditToIR;
        }

        /** Branch Info End **/

        /** Pc Info End **/


        /** Borrower & Co-Borrower Info Start **/
        $borrowerName = $LMRInfo->borrowerName;
        $borrowerFName = $LMRInfo->borrowerName;
        $borrowerMName = $LMRInfo->borrowerMName;
        $borrowerLName = $LMRInfo->borrowerLName;
        $borrowerEmail = $LMRInfo->borrowerEmail;
        $borrowerSecondaryEmail = $LMRInfo->borrowerSecondaryEmail;
        $cellNumber = $LMRInfo->cellNumber == '' ? $myFileInfo->clientInfo()->clientCell : $LMRInfo->cellNumber;
        $borrFax = $LMRInfo->fax;
        $ssnNumber = $LMRInfo->ssnNumber == '' ? $myFileInfo->clientInfo()->ssnNumber : $LMRInfo->ssnNumber;
        $isCoBorrower = $LMRInfo->isCoBorrower;
        $mailingAddress = $LMRInfo->mailingAddress;
        $mailingCity = $LMRInfo->mailingCity;
        $mailingState = $LMRInfo->mailingState;
        $mailingZip = $LMRInfo->mailingZip;
        $coBorrowerFName = $LMRInfo->coBorrowerFName;
        $coBorrowerLName = $LMRInfo->coBorrowerLName;
        $coBorrowerEmail = $LMRInfo->coBorrowerEmail;
        $coBCellNumber = $LMRInfo->coBCellNumber;
        $coBorrowerFax = $LMRInfo->coBFax;
        $coBSsnNumber = $LMRInfo->coBSsnNumber;
        $coBorrowerMailingAddress = $LMRInfo->coBorrowerMailingAddress;
        $coBorrowerMailingCity = $LMRInfo->coBorrowerMailingCity;
        $coBorrowerMailingState = $LMRInfo->coBorrowerMailingState;
        $coBorrowerMailingZip = $LMRInfo->coBorrowerMailingZip;
        $coBorPreviousState = $LMRInfo->coBorPreviousState;
        $phoneNumber = $LMRInfo->phoneNumber;
        $coBPhoneNumber = $LMRInfo->coBPhoneNumber;
        $serviceProvider = $LMRInfo->serviceProvider;
        $coBServiceProvider = $LMRInfo->coBServiceProvider;
        $marriageDate = $LMRInfo->marriageDate;
        $divorceDate = $LMRInfo->divorceDate;

        $presentAddress = LMRequest::myFileInfo()->file2Info()->presentAddress;
        $presentCity = LMRequest::myFileInfo()->file2Info()->presentCity;
        $presentState = LMRequest::myFileInfo()->file2Info()->presentState;
        $presentZip = LMRequest::myFileInfo()->file2Info()->presentZip;
        $presentPropLengthTime = LMRequest::myFileInfo()->file2Info()->presentPropLengthTime;
        $previousPropLengthTime = LMRequest::myFileInfo()->file2Info()->previousPropLengthTime;
        $borMailingPropType = LMRequest::myFileInfo()->file2Info()->borMailingPropType;
        $borPresentPropType = LMRequest::myFileInfo()->file2Info()->borPresentPropType;
        $presentUnit = LMRequest::myFileInfo()->file2Info()->presentUnit;
        $presentCountry = LMRequest::myFileInfo()->file2Info()->presentCountry;
        $presentPropLengthMonths = LMRequest::myFileInfo()->file2Info()->presentPropLengthMonths;
        $currentRPM = LMRequest::myFileInfo()->file2Info()->currentRPM;
        $previousUnit = LMRequest::myFileInfo()->file2Info()->previousUnit;
        $previousCountry = LMRequest::myFileInfo()->file2Info()->previousCountry;
        $previousPropLengthMonths = LMRequest::myFileInfo()->file2Info()->previousPropLengthMonths;
        $previousRPM = LMRequest::myFileInfo()->file2Info()->previousRPM;

        $coBPresentAddress = LMRequest::myFileInfo()->file2Info()->coBPresentAddress;
        $coBPresentCity = LMRequest::myFileInfo()->file2Info()->coBPresentCity;
        $coBPresentState = LMRequest::myFileInfo()->file2Info()->coBPresentState;
        $coBPresentZip = LMRequest::myFileInfo()->file2Info()->coBPresentZip;
        $borrowerDOB = $LMRInfo->borrowerDOB ? $LMRInfo->borrowerDOB : $myFileInfo->clientInfo()->borrowerDOB;
        $borrowerPOB = $LMRInfo->borrowerPOB ? $LMRInfo->borrowerPOB : $myFileInfo->clientInfo()->borrowerPOB;
        $coBorrowerDOB = $LMRInfo->coBorrowerDOB;
        $coborrowerPOB = $LMRInfo->coborrowerPOB;
        $receivedDate = $LMRInfo->receivedDate;
        $guarantorNotes = LMRequest::myFileInfo()->file2Info()->guarantorNotes;
        $methodOfContact = LMRequest::myFileInfo()->file2Info()->methodOfContact ? LMRequest::myFileInfo()->file2Info()->methodOfContact : $myFileInfo->clientInfo()->methodOfContact;
        $borResidedPresentAddr = LMRequest::myFileInfo()->fileLoanOriginationInfo()->borResidedPresentAddr;
        $coBorMailingAddrAsPresent = LMRequest::myFileInfo()->file2Info()->coBorMailingAddrAsPresent;
        $coBResidedPresentAddr = LMRequest::myFileInfo()->fileLoanOriginationInfo()->coBResidedPresentAddr;
        $coBPresentPropType = LMRequest::myFileInfo()->file2Info()->coBPresentPropType;
        $presentPropLengthTimeCoBor = LMRequest::myFileInfo()->file2Info()->presentPropLengthTimeCoBor;
        $mailingAddrAsPresent = LMRequest::myFileInfo()->file2Info()->mailingAddrAsPresent;
        $coBorMailingAddrAsPresent = LMRequest::myFileInfo()->file2Info()->coBorMailingAddrAsPresent;
        $coBFormerPropType = LMRequest::myFileInfo()->file2Info()->coBFormerPropType;
        $borFormerPropType = LMRequest::myFileInfo()->file2Info()->borFormerPropType;
        $coBMailingPropType = LMRequest::myFileInfo()->file2Info()->coBMailingPropType;
        $workNumber = $LMRInfo->workNumber;
        $altPhoneNumber = $LMRInfo->altPhoneNumber;
        $borComment = LMRequest::myFileInfo()->LOExplanationInfo()->borComment; //Exit Strategy Explanation

        $propertyAddress = LMRequest::File()->getPrimaryProperty()->propertyAddress;  // Property address
        $propertyCountry = LMRequest::File()->getPrimaryProperty()->propertyCountry;
        $propertyUnit = LMRequest::File()->getPrimaryProperty()->propertyUnit;
        $propertyCondition = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyCondition;  //Subject Property Condition
        $conditionNotes = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyConditionNotes;  //Condition Notes
        $propertyZip = LMRequest::File()->getPrimaryProperty()->propertyZipCode;
        $propertyCity = LMRequest::File()->getPrimaryProperty()->propertyCity;
        $propertyState = LMRequest::File()->getPrimaryProperty()->propertyState;
        $driverLicenseState = $LMRInfo->driverLicenseState ?: $myFileInfo->clientInfo()->driverLicenseState;
        $driverLicenseNumber = $LMRInfo->driverLicenseNumber ?: $myFileInfo->clientInfo()->driverLicenseNumber;
        $coBorDriverLicenseState = $LMRInfo->coBorDriverLicenseState;
        $coBorDriverLicenseNumber = $LMRInfo->coBorDriverLicenseNumber;
        $maritalStatusCoBor = $LMRInfo->maritalStatusCoBor;

        if (trim($mailingAddrAsPresent) == '') {
            $mailingAddrAsPresent = 0;
        }

        if (trim($coBorMailingAddrAsPresent) == '') {
            $coBorMailingAddrAsPresent = 0;
        }

        $HOA2Contacts = $fileContacts->HOA2;

        if ($HOA2Contacts) {
            $HOA2ContactID = trim($HOA2Contacts->CID);
            $HOA2ContactName = stripslashes(trim($HOA2Contacts->contactName));
            $HOA2CompanyName = trim($HOA2Contacts->companyName);
        }

        $propertyManagementInfo = $fileContacts->PM;

        if ($propertyManagementInfo) {
            $propMgmntContactID = trim($propertyManagementInfo->CID);
            $propMgmntContactPerson = $tempPropMgmntContactName = trim($propertyManagementInfo->contactName);
            $propMgmntCompany = trim($propertyManagementInfo->companyName);
            $propMgmntContactEmail = trim($propertyManagementInfo->email);
            $propMgmntAddress = trim($propertyManagementInfo->address);
            $propMgmntCity = trim($propertyManagementInfo->city);
            $propMgmntState = trim($propertyManagementInfo->state);
            $propMgmntZip = trim($propertyManagementInfo->zip);
            $propMgmntNotes = trim($propertyManagementInfo->description);
            $propMgmntPhone = trim($propertyManagementInfo->phone);
        }
        $propMgmntPhoneArray = [];
        $propMgmntPhNo1 = '';
        $propMgmntPhNo2 = '';
        $propMgmntPhNo3 = '';
        $propMgmntPhExt = '';
        $propMgmntPhoneArray = Strings::splitPhoneNumber($propMgmntPhone);
        if (count($propMgmntPhoneArray) > 0) {
            $propMgmntPhNo1 = $propMgmntPhoneArray['No1'];
            $propMgmntPhNo2 = $propMgmntPhoneArray['No2'];
            $propMgmntPhNo3 = $propMgmntPhoneArray['No3'];
            $propMgmntPhExt = $propMgmntPhoneArray['Ext'];
        }

        if ($QAInfo) {
            $QAID = $QAInfo->QAID;
            $bankruptcyDispositionStatus = $QAInfo->bankruptcyDispositionStatus;
            $saleForHowLong = $QAInfo->saleForHowLong;
            $PublishBInfo = $QAInfo->PublishBInfo;
            $BEthnicity = $QAInfo->BEthnicity ?: $myFileInfo->clientInfo()->ethnicity;
            $BRace = $QAInfo->BRace ?: $myFileInfo->clientInfo()->race;
            $BGender = $QAInfo->BGender ?: $myFileInfo->clientInfo()->gender;
            $BVeteran = $QAInfo->BVeteran ?: $myFileInfo->clientInfo()->veteran;
            $bFiEthnicity = $QAInfo->bFiEthnicity ?: $myFileInfo->clientInfo()->FIEthnicity;
            $bFiEthnicitySub = $QAInfo->bFiEthnicitySub ?: $myFileInfo->clientInfo()->FIEthnicitySub;
            $bFiEthnicitySubOther = $QAInfo->bFiEthnicitySubOther ?: $myFileInfo->clientInfo()->FIEthnicitySubOther;
            $bFiSex = $QAInfo->bFiSex ?: $myFileInfo->clientInfo()->FISex;
            $bFiRace = $QAInfo->bFiRace ?: $myFileInfo->clientInfo()->FIRace;
            $bFiRaceSub = $QAInfo->bFiRaceSub ?: $myFileInfo->clientInfo()->FIRaceSub;
            $bFiRaceAsianOther = $QAInfo->bFiRaceAsianOther ?: $myFileInfo->clientInfo()->FIRaceAsianOther;
            $bFiRacePacificOther = $QAInfo->bFiRacePacificOther ?: $myFileInfo->clientInfo()->FIRacePacificOther;
            $bDemoInfo = $QAInfo->bDemoInfo ?: $myFileInfo->clientInfo()->DemoInfo;
            $PublishCBInfo = $QAInfo->PublishCBInfo;
            $CBEthnicity = $QAInfo->CBEthnicity;
            $CBRace = $QAInfo->CBRace;
            $CBGender = $QAInfo->CBGender;
            $desiredCloseDate = $QAInfo->desiredClosingDate;

            $CBVeteran = $QAInfo->CBVeteran;
            $CBFiEthnicity = $QAInfo->CBFiEthnicity;
            $CBEthnicitySub = $QAInfo->CBEthnicitySub;
            $CBEthnicitySubOther = $QAInfo->CBEthnicitySubOther;
            $CBFiGender = $QAInfo->CBFiGender;
            $CBFiRace = $QAInfo->CBFiRace;
            $CBRaceSub = $QAInfo->CBRaceSub;
            $CBRaceAsianOther = $QAInfo->CBRaceAsianOther;
            $CBRacePacificOther = $QAInfo->CBRacePacificOther;
            $CBDDemoInfo = $QAInfo->CBDDemoInfo;
        }

        if ($methodOfContact) {
            $methodContactArray = explode(',', $methodOfContact);
        }
        $borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'YMD', 'm/d/Y');
        $coBorrowerDOB = Dates::formatDateWithRE($coBorrowerDOB, 'YMD', 'm/d/Y');

        if (Dates::IsEmpty($desiredCloseDate)) {
            $desiredCloseDate = '';
        } else {
            $desiredCloseDate = Dates::formatDateWithRE($desiredCloseDate, 'YMD', 'm/d/Y');
        }

        $fax1 = '';
        $fax2 = '';
        $fax3 = '';
        $faxArray = Strings::splitPhoneNumber($borrFax);
        if (count($faxArray) > 0) {
            $fax1 = trim($faxArray['No1']);
            $fax2 = trim($faxArray['No2']);
            $fax3 = trim($faxArray['No3']);
        }
        $HOPhone = '';
        $HOFax = '';
        $HOPhNoArray = [];
        $HOPhone = LMRequest::myFileInfo()->listingRealtorInfo()->HOPhone;
        $HOFax = LMRequest::myFileInfo()->listingRealtorInfo()->HOFax;
        $HOPhNoArray = Strings::splitPhoneNumber($HOPhone);
        $HOPhNo1 = '';
        $HOPhNo2 = '';
        $HOPhNo3 = '';
        if (count($HOPhNoArray) > 0) {
            $HOPhNo1 = $HOPhNoArray['No1'];
            $HOPhNo2 = $HOPhNoArray['No2'];
            $HOPhNo3 = $HOPhNoArray['No3'];
            $HOPhExt = substr($HOPhone, 10, 5);
        }
        $HOFaxNo1 = '';
        $HOFaxNo2 = '';
        $HOFaxNo3 = '';
        $HOFaxArray = Strings::splitPhoneNumber($HOFax);
        if (count($HOFaxArray) > 0) {
            $HOFaxNo1 = $HOFaxArray['No1'];
            $HOFaxNo2 = $HOFaxArray['No2'];
            $HOFaxNo3 = $HOFaxArray['No3'];
        }
        $coBFax1 = '';
        $coBFax2 = '';
        $coBFax3 = '';
        $coBFaxArray = Strings::splitPhoneNumber($coBorrowerFax);
        if (count($coBFaxArray) > 0) {
            $coBFax1 = trim($coBFaxArray['No1']);
            $coBFax2 = trim($coBFaxArray['No2']);
            $coBFax3 = trim($coBFaxArray['No3']);
        }
        /*$coBSsn1 = '';	$coBSsn2 = '';	$coBSsn3 = '';
        $coBSsnNumberArray = splitSSNNumber($coBSsnNumber);
        if(count($coBSsnNumberArray) > 0) {
            $coBSsn1	= trim($coBSsnNumberArray['No1']);
            $coBSsn2	= trim($coBSsnNumberArray['No2']);
            $coBSsn3	= trim($coBSsnNumberArray['No3']);
        }*/
        if (Dates::IsEmpty($marriageDate)) {
            $marriageDate = '';
        } else {
            $marriageDate = Dates::formatDateWithRE($marriageDate, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty($divorceDate)) {
            $divorceDate = '';
        } else {
            $divorceDate = Dates::formatDateWithRE($divorceDate, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty($purchaseCloseDate)) {
            $purchaseCloseDate = '';
        } else {
            $purchaseCloseDate = Dates::formatDateWithRE($purchaseCloseDate, 'YMD', 'm/d/Y');
        }


        /** Borrower Info End **/
        if ($fileHMLOInfo) {
            $REBroker = $fileHMLOInfo->REBroker;
            $borCreditScoreRange = $fileHMLOInfo->borCreditScoreRange;
            $midFicoScore = $fileHMLOInfo->midFicoScore;
            $coBorCreditScoreRange = $fileHMLOInfo->coBorCreditScoreRange;
            $borExperianScore = $fileHMLOInfo->borExperianScore;
            $borEquifaxScore = $fileHMLOInfo->borEquifaxScore;
            $borTransunionScore = $fileHMLOInfo->borTransunionScore;
            $coBorExperianScore = $fileHMLOInfo->coBorExperianScore;
            $coBorEquifaxScore = $fileHMLOInfo->coBorEquifaxScore;
            $midFicoScoreCoBor = $fileHMLOInfo->midFicoScoreCoBor;
            $coBorTransunionScore = $fileHMLOInfo->coBorTransunionScore;
            $borrowerCitizenship = $fileHMLOInfo->borrowerCitizenship;
            $coBorrowerCitizenship = $fileHMLOInfo->coBorrowerCitizenship;
            $isServicingMember = $fileHMLOInfo->isServicingMember;
            $servicingMemberInfo = $fileHMLOInfo->servicingMemberInfo;
            $agesOfDependent = $fileHMLOInfo->agesOfDependent;
            $numberOfDependents = $fileHMLOInfo->numberOfDependents;
            $defaultDrawFee = $fileHMLOInfo->defaultDrawFee;
            $drawFundCalOnDrawsFee = $fileHMLOInfo->drawFundCalOnDrawsFee;
        }
        /** Borrower Background Info Start **/
        $isBorUSCitizen = $fileHMLOBackGroundInfo->isBorUSCitizen;
        if ($borrowerCitizenship == 'U.S. Citizen') {
            $isBorUSCitizen = 'Yes';
        } else if ($borrowerCitizenship) {
            $isBorUSCitizen = 'No';
        }
        $isBorDecalredBankruptPastYears = $fileHMLOBackGroundInfo->isBorDecalredBankruptPastYears;
        $isAnyBorOutstandingJudgements = $fileHMLOBackGroundInfo->isAnyBorOutstandingJudgements;
        $hasBorAnyActiveLawsuits = $fileHMLOBackGroundInfo->hasBorAnyActiveLawsuits;
        $hasBorPropertyTaxLiens = $fileHMLOBackGroundInfo->hasBorPropertyTaxLiens;
        $hasBorObligatedInForeclosure = $fileHMLOBackGroundInfo->hasBorObligatedInForeclosure;
        $isBorPresenltyDelinquent = $fileHMLOBackGroundInfo->isBorPresenltyDelinquent;
        $haveBorOtherFraudRelatedCrimes = $fileHMLOBackGroundInfo->haveBorOtherFraudRelatedCrimes;

        $borDecalredBankruptExpln = $fileHMLOBackGroundInfo->borDecalredBankruptExpln;
        $personalBankruptcy = $fileHMLOBackGroundInfo->personalBankruptcy;
        $statusForeclosure = $fileHMLOBackGroundInfo->statusForeclosure;
        $borOutstandingJudgementsExpln = $fileHMLOBackGroundInfo->borOutstandingJudgementsExpln;
        $borActiveLawsuitsExpln = $fileHMLOBackGroundInfo->borActiveLawsuitsExpln;
        $borPropertyTaxLiensExpln = $fileHMLOBackGroundInfo->borPropertyTaxLiensExpln;
        $borObligatedInForeclosureExpln = $fileHMLOBackGroundInfo->borObligatedInForeclosureExpln;
        $borDelinquentExpln = $fileHMLOBackGroundInfo->borDelinquentExpln;
        $borOtherFraudRelatedCrimesExpln = $fileHMLOBackGroundInfo->borOtherFraudRelatedCrimesExpln;
        $borBackgroundExplanation = $fileHMLOBackGroundInfo->borBackgroundExplanation;
        $borDesignatedBeneficiaryAgreement = $fileHMLOBackGroundInfo->borDesignatedBeneficiaryAgreement;
        $borDesignatedBeneficiaryAgreementExpln = $fileHMLOBackGroundInfo->borDesignatedBeneficiaryAgreementExpln;

        $isCoBorUSCitizen = $fileHMLOBackGroundInfo->isCoBorUSCitizen;
        if ($coBorrowerCitizenship == 'U.S. Citizen') {
            $isCoBorUSCitizen = 'Yes';
        } elseif ($coBorrowerCitizenship) {
            $isCoBorUSCitizen = 'No';
        }
        $isCoBorDecalredBankruptPastYears = $fileHMLOBackGroundInfo->isCoBorDecalredBankruptPastYears;
        $isAnyCoBorOutstandingJudgements = $fileHMLOBackGroundInfo->isAnyCoBorOutstandingJudgements;
        $hasCoBorAnyActiveLawsuits = $fileHMLOBackGroundInfo->hasCoBorAnyActiveLawsuits;
        $hasCoBorPropertyTaxLiens = $fileHMLOBackGroundInfo->hasCoBorPropertyTaxLiens;
        $hasCoBorObligatedInForeclosure = $fileHMLOBackGroundInfo->hasCoBorObligatedInForeclosure;
        $isCoBorPresenltyDelinquent = $fileHMLOBackGroundInfo->isCoBorPresenltyDelinquent;
        $haveCoBorOtherFraudRelatedCrimes = $fileHMLOBackGroundInfo->haveCoBorOtherFraudRelatedCrimes;

        $coBorDecalredBankruptExpln = $fileHMLOBackGroundInfo->coBorDecalredBankruptExpln;
        $coBorOutstandingJudgementsExpln = $fileHMLOBackGroundInfo->coBorOutstandingJudgementsExpln;
        $coBorActiveLawsuitsExpln = $fileHMLOBackGroundInfo->coBorActiveLawsuitsExpln;
        $coBorPropertyTaxLiensExpln = $fileHMLOBackGroundInfo->coBorPropertyTaxLiensExpln;
        $coBorObligatedInForeclosureExpln = $fileHMLOBackGroundInfo->coBorObligatedInForeclosureExpln;
        $coBorDelinquentExpln = $fileHMLOBackGroundInfo->coBorDelinquentExpln;
        $coBorOtherFraudRelatedCrimesExpln = $fileHMLOBackGroundInfo->coBorOtherFraudRelatedCrimesExpln;
        $coBorBackgroundExplanation = $fileHMLOBackGroundInfo->coBorBackgroundExplanation;

        $coBorDesignatedBeneficiaryAgreement = $fileHMLOBackGroundInfo->coBorDesignatedBeneficiaryAgreement;
        $coBorDesignatedBeneficiaryAgreementExpln = $fileHMLOBackGroundInfo->coBorDesignatedBeneficiaryAgreementExpln;
        $isCoBorIntendToOccupyPropAsPRI = $fileHMLOBackGroundInfo->isCoBorIntendToOccupyPropAsPRI;
        $marriedToBor = $fileHMLOBackGroundInfo->marriedToBor;

        $isBorBorrowedDownPayment = $fileHMLOBackGroundInfo->isBorBorrowedDownPayment;
        $isBorIntendToOccupyPropAsPRI = $fileHMLOBackGroundInfo->isBorIntendToOccupyPropAsPRI;
        $isBorPersonallyGuaranteeLoan = $fileHMLOBackGroundInfo->isBorPersonallyGuaranteeLoan;
        $borBorrowedDownPaymentExpln = $fileHMLOBackGroundInfo->borBorrowedDownPaymentExpln;
        $borOrigin = $fileHMLOBackGroundInfo->borOrigin;
        $borVisaStatus = $fileHMLOBackGroundInfo->borVisaStatus;


        /** Borrower Experience Info Start **/
        $haveBorREInvestmentExperience = $fileHMLOExperienceInfo->haveBorREInvestmentExperience;
        $haveBorRehabConstructionExperience = $fileHMLOExperienceInfo->haveBorRehabConstructionExperience;
        $haveBorProjectCurrentlyInProgress = $fileHMLOExperienceInfo->haveBorProjectCurrentlyInProgress;
        $haveBorOwnInvestmentProperties = $fileHMLOExperienceInfo->haveBorOwnInvestmentProperties;
        $areBorMemberOfInvestmentClub = $fileHMLOExperienceInfo->areBorMemberOfInvestmentClub;

        $borNoOfREPropertiesCompleted = $fileHMLOExperienceInfo->borNoOfREPropertiesCompleted;
        $borNoOfFlippingExperience = $fileHMLOExperienceInfo->borNoOfFlippingExperience;
        $coBorNoOfFlippingExperience = $fileHMLOExperienceInfo->coBorNoOfFlippingExperience;
        $borNoOfYearRehabExperience = $fileHMLOExperienceInfo->borNoOfYearRehabExperience;
        $borRehabPropCompleted = $fileHMLOExperienceInfo->borRehabPropCompleted;
        $borNoOfProjectCurrently = $fileHMLOExperienceInfo->borNoOfProjectCurrently;
        $borNoOfOwnProp = $fileHMLOExperienceInfo->borNoOfOwnProp;
        $borClubName = $fileHMLOExperienceInfo->borClubName;
        $liquidAssets = $fileHMLOExperienceInfo->liquidAssets;
        $areBuilderDeveloper = $fileHMLOExperienceInfo->areBuilderDeveloper;
        $doYouHireGC = $fileHMLOExperienceInfo->doYouHireGC;

        $haveCoBorREInvestmentExperience = $fileHMLOExperienceInfo->haveCoBorREInvestmentExperience;
        $haveCoBorRehabConstructionExperience = $fileHMLOExperienceInfo->haveCoBorRehabConstructionExperience;
        $haveCoBorProjectCurrentlyInProgress = $fileHMLOExperienceInfo->haveCoBorProjectCurrentlyInProgress;
        $haveCoBorOwnInvestmentProperties = $fileHMLOExperienceInfo->haveCoBorOwnInvestmentProperties;
        $areCoBorMemberOfInvestmentClub = $fileHMLOExperienceInfo->areCoBorMemberOfInvestmentClub;
        $haveCoBorProfLicences = $fileHMLOExperienceInfo->haveCoBorProfLicences;
        $coBorProfLicence = $fileHMLOExperienceInfo->coBorProfLicence;
        $coBorLicenseNo = $fileHMLOExperienceInfo->coBorLicenseNo;

        $coBorNoOfREPropertiesCompleted = $fileHMLOExperienceInfo->coBorNoOfREPropertiesCompleted;
        $coBorNoOfYearRehabExperience = $fileHMLOExperienceInfo->coBorNoOfYearRehabExperience;
        $coBorRehabPropCompleted = $fileHMLOExperienceInfo->coBorRehabPropCompleted;
        $coBorNoOfProjectCurrently = $fileHMLOExperienceInfo->coBorNoOfProjectCurrently;
        $coBorNoOfOwnProp = $fileHMLOExperienceInfo->coBorNoOfOwnProp;
        $coBorClubName = $fileHMLOExperienceInfo->coBorClubName;

        $coBorREAddress1 = $fileHMLOExperienceInfo->coBorREAddress1;
        $coBorREAddress2 = $fileHMLOExperienceInfo->coBorREAddress2;
        $coBorREAddress3 = $fileHMLOExperienceInfo->coBorREAddress3;
        $coBorOutcomeRE1 = $fileHMLOExperienceInfo->coBorOutcomeRE1;
        $coBorOutcomeRE2 = $fileHMLOExperienceInfo->coBorOutcomeRE2;
        $coBorOutcomeRE3 = $fileHMLOExperienceInfo->coBorOutcomeRE3;

        $coBorRCAddress1 = $fileHMLOExperienceInfo->coBorRCAddress1;
        $coBorRCAddress2 = $fileHMLOExperienceInfo->coBorRCAddress2;
        $coBorRCAddress3 = $fileHMLOExperienceInfo->coBorRCAddress3;
        $coBorRCOutcome1 = $fileHMLOExperienceInfo->coBorRCOutcome1;
        $coBorRCOutcome2 = $fileHMLOExperienceInfo->coBorRCOutcome2;
        $coBorRCOutcome3 = $fileHMLOExperienceInfo->coBorRCOutcome3;
        $borNoOfSquareFeet = $fileHMLONewLoanInfo->borNoOfSquareFeet;
        $haveBorSquareFootage = $fileHMLONewLoanInfo->haveBorSquareFootage;
        $prepaidInterestReserve = $fileHMLONewLoanInfo->prepaidInterestReserve;
        $noOfMonthsPrepaid = $fileHMLONewLoanInfo->noOfMonthsPrepaid;
        $haveInterestreserve = $fileHMLONewLoanInfo->haveInterestreserve;
        $haveBorProfLicences = $fileHMLOExperienceInfo->haveBorProfLicences;
        $borProfLicence = $fileHMLOExperienceInfo->borProfLicence;
        $fullTimeRealEstateInvestor = $fileHMLOExperienceInfo->fullTimeRealEstateInvestor;
        $coFullTimeRealEstateInvestor = $fileHMLOExperienceInfo->coFullTimeRealEstateInvestor;
        $coBorliquidReserves = $fileHMLOExperienceInfo->coBorliquidReserves;
        $ownedFreeAndClear = $fileHMLONewLoanInfo->ownedFreeAndClear;
        $ownedSameEntity = $fileHMLONewLoanInfo->ownedSameEntity;
        $isLoanPaymentAmt = $fileHMLONewLoanInfo->isLoanPaymentAmt;
        $refinanceCurrentLoanBalance = $fileHMLONewLoanInfo->refinanceCurrentLoanBalance;
        $approvedAcquisition = $fileHMLONewLoanInfo->approvedAcquisition;

        $haveBorSellPropertie = $fileHMLOExperienceInfo->haveBorSellPropertie;
        $borNoOfProSellExperience = $fileHMLOExperienceInfo->borNoOfProSellExperience;
        $borNoOfProSellCompleted = $fileHMLOExperienceInfo->borNoOfProSellCompleted;

        $haveCoBorSellPropertie = $fileHMLOExperienceInfo->haveCoBorSellPropertie;
        $coBorNoOfProSellExperience = $fileHMLOExperienceInfo->coBorNoOfProSellExperience;
        $coBorNoOfProSellCompleted = $fileHMLOExperienceInfo->coBorNoOfProSellCompleted;

        $coBorSellAddress1 = $fileHMLOExperienceInfo->coBorSellAddress1;
        $coBorSellAddress2 = $fileHMLOExperienceInfo->coBorSellAddress2;
        $coBorSellAddress3 = $fileHMLOExperienceInfo->coBorSellAddress3;

        $coBorSellOutcome1 = $fileHMLOExperienceInfo->coBorSellOutcome1;
        $coBorSellOutcome2 = $fileHMLOExperienceInfo->coBorSellOutcome2;
        $coBorSellOutcome3 = $fileHMLOExperienceInfo->coBorSellOutcome3;

        $amountOfFinancing = $fileHMLOExperienceInfo->amountOfFinancing;
        $amountOfFinancingTo = $fileHMLOExperienceInfo->amountOfFinancingTo;
        $typicalPurchasePrice = $fileHMLOExperienceInfo->typicalPurchasePrice;
        $typicalPurchasePriceTo = $fileHMLOExperienceInfo->typicalPurchasePriceTo;
        $typicalConstructionCosts = $fileHMLOExperienceInfo->typicalConstructionCosts;
        $typicalConstructionCostsTo = $fileHMLOExperienceInfo->typicalConstructionCostsTo;
        $typicalSalePrice = $fileHMLOExperienceInfo->typicalSalePrice;
        $typicalSalePriceTo = $fileHMLOExperienceInfo->typicalSalePriceTo;

        $constructionDrawsPerProject = $fileHMLOExperienceInfo->constructionDrawsPerProject;
        $constructionDrawsPerProjectTo = $fileHMLOExperienceInfo->constructionDrawsPerProjectTo;
        $monthsPurchaseDateToFirstConst = $fileHMLOExperienceInfo->monthsPurchaseDateToFirstConst;
        $monthsPurchaseDateToFirstConstTo = $fileHMLOExperienceInfo->monthsPurchaseDateToFirstConstTo;
        $monthsPurchaseDateUntilConst = $fileHMLOExperienceInfo->monthsPurchaseDateUntilConst;
        $monthsPurchaseDateUntilConstTo = $fileHMLOExperienceInfo->monthsPurchaseDateUntilConstTo;
        $monthsPurchaseDateToSaleDate = $fileHMLOExperienceInfo->monthsPurchaseDateToSaleDate;
        $monthsPurchaseDateToSaleDateTo = $fileHMLOExperienceInfo->monthsPurchaseDateToSaleDateTo;
        $NoOfSuchProjects = $fileHMLOExperienceInfo->NoOfSuchProjects;
        $NoOfSuchProjectsTo = $fileHMLOExperienceInfo->NoOfSuchProjectsTo;
        $borPrimaryInvestmentStrategy = $fileHMLOExperienceInfo->borPrimaryInvestmentStrategy;
        $borPrimaryInvestmentStrategyExplain = $fileHMLOExperienceInfo->borPrimaryInvestmentStrategyExplain;

        $coBorPrimaryInvestmentStrategy = $fileHMLOExperienceInfo->coBorPrimaryInvestmentStrategy;
        $coBorPrimaryInvestmentStrategyExplain = $fileHMLOExperienceInfo->coBorPrimaryInvestmentStrategyExplain;
        $borLicenseNo = $fileHMLOExperienceInfo->borLicenseNo;
        $flipPropCompletedLifetime = $fileHMLOExperienceInfo->flipPropCompletedLifetime;
        $groundPropCompletedLifetime = $fileHMLOExperienceInfo->groundPropCompletedLifetime;
        $sellPropCompletedLifetime = $fileHMLOExperienceInfo->sellPropCompletedLifetime;
        $overallRealEstateInvesExp = $fileHMLOExperienceInfo->overallRealEstateInvesExp;

        $ownPropertyFreeAndClear = $fileHMLONewLoanInfo->ownPropertyFreeAndClear;
        $lien1MaturityDate = Dates::formatDateWithRE($fileHMLONewLoanInfo->lien1MaturityDate, 'YMD', 'm/d/Y');
        $lien2MaturityDate = Dates::formatDateWithRE($fileHMLONewLoanInfo->lien2MaturityDate, 'YMD', 'm/d/Y');
        $lean1CurrentDefault = $fileHMLONewLoanInfo->lean1CurrentDefault;
        $lean2CurrentDefault = $fileHMLONewLoanInfo->lean2CurrentDefault;
        $desiredLoanAmount = $fileHMLONewLoanInfo->desiredLoanAmount;
        $earnestDeposit = $fileHMLONewLoanInfo->earnestDeposit;
        $otherDownPayment = $fileHMLONewLoanInfo->otherDownPayment;

        /* Multi Select Value */
        if ($borPrimaryInvestmentStrategy) $borPriInvesStrategyArray = explode(',', $borPrimaryInvestmentStrategy);
        if ($coBorPrimaryInvestmentStrategy) $coBorPriInvesStrategyArray = explode(',', $coBorPrimaryInvestmentStrategy);
            $geographicAreas = explode(',', $fileHMLOExperienceInfo->geographicAreas);


        /** Borrower Experience Info End **/
        $assetCheckingAccounts = '';
        $taxes1 = $incomeInfo->taxes1;
        $taxes1LastPaid = $incomeInfo->taxes1LastPaid;
        $taxes1LastAmount = $incomeInfo->taxes1LastAmount;

        $lien1Payment = $LMRInfo->lien1Payment;
        $isTaxesInsEscrowed = $fileHMLONewLoanInfo->isTaxesInsEscrowed;
        $spcf_hoafees = $fileHMLONewLoanInfo->spcf_hoafees;

        $assetsInfo = $myFileInfo->AssetsInfo() ?? null;
        $FilePropInfo = $myFileInfo->FilePropInfo() ?? null;
        $fileLOAssetsInfo = $myFileInfo->fileLOAssetsInfo() ?? null;
        // $glMultiPropertyCountry = $myFileInfo->multiPropertyCountry() ?? null;

        /** Income and Exp calculation Page **/

        $legalDescription = urldecode(LMRequest::File()->getPrimaryProperty()->propertyLegalDescription);
        $district = LMRequest::File()->getPrimaryProperty()->propertyDistrict;
        $section = LMRequest::File()->getPrimaryProperty()->propertySection;
        $block = LMRequest::File()->getPrimaryProperty()->propertyBlock;
        $lot = LMRequest::File()->getPrimaryProperty()->propertyLot;
        $parcelNo = LMRequest::File()->getPrimaryProperty()->propertyParcelNumber;
        $municipality = LMRequest::File()->getPrimaryProperty()->propertyMunicipality;


        $LBInfo = LMRequest::File()->getPrimaryProperty()->getTblPropertiesAccess_by_propertyId()->propertyAccessLockBoxInfo;
        $HMLOmlsnumber = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyMLSNumber;
        $presentOccupancy = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy;
        $LBContactName = LMRequest::File()->getPrimaryProperty()->getTblPropertiesAccess_by_propertyId()->propertyAccessName;
        $propConstructionType = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyConstructionType;
        $propConstructionMethod = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyConstructionMethod;
        $LBContactPhone = LMRequest::File()->getPrimaryProperty()->getTblPropertiesAccess_by_propertyId()->propertyAccessPhone;
        $LBContactEmail = LMRequest::File()->getPrimaryProperty()->getTblPropertiesAccess_by_propertyId()->propertyAccessEmail;
        $obtainInteriorAccess = LMRequest::File()->getPrimaryProperty()->getTblPropertiesAccess_by_propertyId()->propertyAccessObtainInteriorAccess;
        $yearBuilt = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyYearBuilt;
        $occupancyNotes = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyOccupancyNotes;
        $rehabToComplete = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyRehabToComplete;
        $addRentableSqFt = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyRentableSqFt;
        $noOfParking = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfParkings;
        $currentCommercialTenantOccupancyRate = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyCommercialTenantOccupancyRate;

        $anchorTenant = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyIsAnchorTenant;
        $anchorTenantName = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyAnchorTenantName;
        $cre = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyCRE;
        $resi = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyRESI;
        $propertyClass = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyClass;
        $addNoOfStories = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfStories;
        $leaseType = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyLeaseType;
        $yearRenovated = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyRenovatedYear;
        $pastDuePropertyTaxes = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyPastDuePropertyTaxes;
        $propertySqFt = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertySqFt;
        $adjustedSqFt = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyAdjustedSqFt;

        $basementHome = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyIsHomeHaveBasement;
        $yearPurchased = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyYearPurchased;
        $howManyBedRoom = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfBedRooms;
        $howManyBathRoom = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfBathRooms;
        $howManyHalfBathRoom = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfHalfBathRooms;

        $noOfBuildings = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfBuildings;
        $ownerOccupancyPercentage = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyOwnerOccupancy;

        $propertyTaxDueDate = LMRequest::File()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyTaxDueDate;


        if ($FilePropInfo) {
            $isHouseProperty = $FilePropInfo->isHouseProperty;
        }


        /** Entity Info Section **/
        $entityName = $fileHMLOEntityInfo->entityName;
        $entityType = $fileHMLOEntityInfo->entityType;
        $borrowerType = $fileHMLOEntityInfo->borrowerType;
        $trustType = $fileHMLOEntityInfo->trustType;
        $retirementEntity = $fileHMLOEntityInfo->retirementEntity;
        $businessCategory = $fileHMLOEntityInfo->businessCategory;
        $businessType = $fileHMLOEntityInfo->businessType;
        $productTypeOrServiceSold = $fileHMLOEntityInfo->productTypeOrServiceSold;
        $terminalOrMakeModel = $fileHMLOEntityInfo->terminalOrMakeModel;
        $businessPhone = $fileHMLOEntityInfo->businessPhone;
        $startDateAtLocation = $fileHMLOEntityInfo->startDateAtLocation;
        $entityPropertyOwnerShip = $fileHMLOEntityInfo->entityPropertyOwnerShip;
        $valueOfProperty = $fileHMLOEntityInfo->valueOfProperty;
        $totalDebtOnProperty = $fileHMLOEntityInfo->totalDebtOnProperty;
        $nameOfLenders = $fileHMLOEntityInfo->nameOfLenders;
        $landlordMortagageContactName = $fileHMLOEntityInfo->landlordMortagageContactName;
        $landlordMortagagePhone = $fileHMLOEntityInfo->landlordMortagagePhone;
        $rentMortagagePayment = $fileHMLOEntityInfo->rentMortagagePayment;
        $avgMonthlyCreditcardSale = $fileHMLOEntityInfo->avgMonthlyCreditcardSale;
        $avgTotalMonthlySale = $fileHMLOEntityInfo->avgTotalMonthlySale;
        $annualGrossSales = $fileHMLOEntityInfo->annualGrossSales;
        $annualGrossProfit = $fileHMLOEntityInfo->annualGrossProfit;
        $ordinaryBusinessIncome = $fileHMLOEntityInfo->ordinaryBusinessIncome;
        $ENINo = $fileHMLOEntityInfo->ENINo;
        $naicsCode = $fileHMLOEntityInfo->naicsCode;
        $entityAddress = $fileHMLOEntityInfo->entityAddress;
        $entityCity = $fileHMLOEntityInfo->entityCity;
        $entityState = $fileHMLOEntityInfo->entityState;
        $entityZip = $fileHMLOEntityInfo->entityZip;
        $entityLocation = $fileHMLOEntityInfo->entityLocation;
        $entityStateOfFormation = $fileHMLOEntityInfo->entityStateOfFormation;
        $statesRegisterdIn = $fileHMLOEntityInfo->statesRegisterdIn;
        $entityNotes = $fileHMLOEntityInfo->entityNotes;
        $corporateSecretaryName = $fileHMLOEntityInfo->corporateSecretaryName;
        $borrowerUnderEntity = $fileHMLOEntityInfo->borrowerUnderEntity ? $fileHMLOEntityInfo->borrowerUnderEntity : 'Yes'; ////default yes, as the dependency is removed.
        $entityWebsite = $fileHMLOEntityInfo->entityWebsite;
        $sameAsEntityAddr = $fileHMLOEntityInfo->sameAsEntityAddr;
        $entityBillAddress = $fileHMLOEntityInfo->entityBillAddress;
        $entityBillCity = $fileHMLOEntityInfo->entityBillCity;
        $entityBillState = $fileHMLOEntityInfo->entityBillState;
        $entityBillZip = $fileHMLOEntityInfo->entityBillZip;
        $sameAsEntityAddr = $fileHMLOEntityInfo->sameAsEntityAddr;
        $businessTypeEF = $fileHMLOEntityInfo->businessTypeEF;

        $tradeName = $fileHMLOEntityInfo->tradeName;
        $crossCorporateGuarantor = $fileHMLOEntityInfo->crossCorporateGuarantor;
        $noOfEmployees = $fileHMLOEntityInfo->noOfEmployees;
        $noOfEmployeesAfterLoan = $fileHMLOEntityInfo->noOfEmployeesAfterLoan;
        $grossAnnualRevenues = $fileHMLOEntityInfo->grossAnnualRevenues;
        $grossIncomeLastYear = $fileHMLOEntityInfo->grossIncomeLastYear;
        $netIncomeLastYear = $fileHMLOEntityInfo->netIncomeLastYear;
        $grossIncome2YearsAgo = $fileHMLOEntityInfo->grossIncome2YearsAgo;
        $netIncome2YearsAgo = $fileHMLOEntityInfo->netIncome2YearsAgo;
        $averageBankBalance = $fileHMLOEntityInfo->averageBankBalance;
        $entityService = $fileHMLOEntityInfo->entityService;
        $entityProduct = $fileHMLOEntityInfo->entityProduct;
        $entityB2B = $fileHMLOEntityInfo->entityB2B;
        $entityB2C = $fileHMLOEntityInfo->entityB2C;
        $benBusinessHomeBased = $fileHMLOEntityInfo->benBusinessHomeBased;
        $benCardProcessorBank = $fileHMLOEntityInfo->benCardProcessorBank;
        $benCreditCardPayments = $fileHMLOEntityInfo->benCreditCardPayments;
        $benChargeSalesTax = $fileHMLOEntityInfo->benChargeSalesTax;
        $benEmployeesPaid = $fileHMLOEntityInfo->benEmployeesPaid;
        $benBusinessLocation = $fileHMLOEntityInfo->benBusinessLocation;
        $benHowManyLocation = $fileHMLOEntityInfo->benHowManyLocation;
        $benOtherLocation = $fileHMLOEntityInfo->benOtherLocation;
        $benBusinessFranchise = $fileHMLOEntityInfo->benBusinessFranchise;
        $benNameOfFranchise = $fileHMLOEntityInfo->benNameOfFranchise;
        $benPointOfContact = $fileHMLOEntityInfo->benPointOfContact;
        $benPointOfContactPhone = $fileHMLOEntityInfo->benPointOfContactPhone;
        $benPointOfContactEmail = $fileHMLOEntityInfo->benPointOfContactEmail;
        $benWebsiteForFranchise = $fileHMLOEntityInfo->benWebsiteForFranchise;
        $businessDescription = $fileHMLOEntityInfo->businessDescription;
        $merchantProcessingBankName = $fileHMLOEntityInfo->merchantProcessingBankName;
        $organizationalRef = $fileHMLOEntityInfo->organizationalRef;
        $dateOfFormation = $fileHMLOEntityInfo->dateOfFormation;
        $minTimeInBusiness = $fileHMLOEntityInfo->minTimeInBusiness;
        $recentNSFs = $fileHMLOEntityInfo->recentNSFs;
        $hasBusinessBankruptcy = $fileHMLOEntityInfo->hasBusinessBankruptcy;
        $businessBankruptcy = $fileHMLOEntityInfo->businessBankruptcy;
        $CBEID = $fileHMLOEntityInfo->CBEID;
        $dateOfOperatingAgreement = $fileHMLOEntityInfo->dateOfOperatingAgreement;
        $isBusinessSeasonal = $fileHMLOEntityInfo->isBusinessSeasonal;
        $isBusinessSeasonalPeakMonth = $fileHMLOEntityInfo->isBusinessSeasonalPeakMonth;
        $businessReference = $fileHMLOEntityInfo->businessReference;

        if (Dates::IsEmpty($dateOfFormation)) {
            $dateOfFormation = '';
        } else {
            $dateOfFormation = Dates::formatDateWithRE($dateOfFormation, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty($startDateAtLocation)) {
            $startDateAtLocation = '';
        } else {
            $startDateAtLocation = Dates::formatDateWithRE($startDateAtLocation, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty($dateOfOperatingAgreement)) {
            $dateOfOperatingAgreement = '';
        } else {
            $dateOfOperatingAgreement = Dates::formatDateWithRE($dateOfOperatingAgreement, 'YMD', 'm/d/Y');
        }

        if ($fileHMLONewLoanInfo) {

            $assumability = $fileHMLONewLoanInfo->assumability;
            $rehabCostFinanced = $fileHMLONewLoanInfo->rehabCostFinanced;
            $originationPointsRate = $fileHMLONewLoanInfo->originationPointsRate;
            $originationPointsValue = $fileHMLONewLoanInfo->originationPointsValue;
            $processingFee = $fileHMLONewLoanInfo->processingFee;
            $brokerPointsRate = $fileHMLONewLoanInfo->brokerPointsRate;
            $brokerPointsValue = $fileHMLONewLoanInfo->brokerPointsValue;
            $appraisalFee = $fileHMLONewLoanInfo->appraisalFee;
            $applicationFee = $fileHMLONewLoanInfo->applicationFee;
            $drawsSetUpFee = $fileHMLONewLoanInfo->drawsSetUpFee;
            $estdTitleClosingFee = $fileHMLONewLoanInfo->estdTitleClosingFee;
            $miscellaneousFee = $fileHMLONewLoanInfo->miscellaneousFee;
            $closingCostFinanced = $fileHMLONewLoanInfo->closingCostFinanced;
            $extensionOption = $fileHMLONewLoanInfo->extensionOption;
            $loanTermExpireDate = $fileHMLONewLoanInfo->loanTermExpireDate;
            $HMLOLender = $fileHMLONewLoanInfo->HMLOLender;
            $drawsFee = $fileHMLONewLoanInfo->drawsFee;
            $interestReserves = $fileHMLONewLoanInfo->interestReserves;
            $percentageOfBudget = $fileHMLONewLoanInfo->percentageOfBudget;
            $originalPurchaseDate = $fileHMLONewLoanInfo->originalPurchaseDate;
            $originalPurchasePrice = $fileHMLONewLoanInfo->originalPurchasePrice;
            $costOfImprovementsMade = $fileHMLONewLoanInfo->costOfImprovementsMade;
            $payOffMortgage1 = $fileHMLONewLoanInfo->payOffMortgage1;
            $payOffMortgage2 = $fileHMLONewLoanInfo->payOffMortgage2;
            $payOffOutstandingTaxes = $fileHMLONewLoanInfo->payOffOutstandingTaxes;
            $payOffOtherOutstandingAmounts = $fileHMLONewLoanInfo->payOffOtherOutstandingAmounts;
            $refinanceCurrentLender = $fileHMLONewLoanInfo->refinanceCurrentLender;
            $refinanceCurrentRate = $fileHMLONewLoanInfo->refinanceCurrentRate;
            $refinanceMonthlyPayment = $fileHMLONewLoanInfo->refinanceMonthlyPayment;
            $CashOut = $fileHMLONewLoanInfo->CashOut;
            $additionalPropertyRestrictions = $fileHMLONewLoanInfo->additionalPropertyRestrictions;
            $restrictionsExplain = $fileHMLONewLoanInfo->restrictionsExplain;
            $prePaymentPenaltyPercentage = $fileHMLONewLoanInfo->prePaymentPenaltyPercentage;
            $prePaymentSelectVal = $fileHMLONewLoanInfo->prePaymentSelectVal;
            $prePaymentSelectValArr = explode(',', $prePaymentSelectVal);
            $amortizationType = $fileHMLONewLoanInfo->amortizationType;
            $noOfPropertiesAcquiring = $fileHMLONewLoanInfo->noOfPropertiesAcquiring;
            $cashOutAmt = $fileHMLONewLoanInfo->cashOutAmt;
            $datesigned = $fileHMLONewLoanInfo->datesigned;
            $resalePrice = $fileHMLONewLoanInfo->resalePrice;
            $resaleClosingDate = $fileHMLONewLoanInfo->resaleClosingDate;
            $initialAdvance = $fileHMLONewLoanInfo->initialAdvance;
            $secondaryFinancingAmount = $fileHMLONewLoanInfo->secondaryFinancingAmount;

            $valuationBPOFee = $fileHMLONewLoanInfo->valuationBPOFee;
            $valuationCMAFee = $fileHMLONewLoanInfo->valuationCMAFee;
            $valuationAVEFee = $fileHMLONewLoanInfo->valuationAVEFee;
            $valuationAVMFee = $fileHMLONewLoanInfo->valuationAVMFee;
            $creditReportFee = $fileHMLONewLoanInfo->creditReportFee;
            $creditCheckFee = $fileHMLONewLoanInfo->creditCheckFee;
            $employmentVerificationFee = $fileHMLONewLoanInfo->employmentVerificationFee;
            $backgroundCheckFee = $fileHMLONewLoanInfo->backgroundCheckFee;
            $taxReturnOrderFee = $fileHMLONewLoanInfo->taxReturnOrderFee;
            $floodCertificateFee = $fileHMLONewLoanInfo->floodCertificateFee;
            $loanOriginationFee = $fileHMLONewLoanInfo->loanOriginationFee;
            $documentPreparationFee = $fileHMLONewLoanInfo->documentPreparationFee;
            $wireFee = $fileHMLONewLoanInfo->wireFee;
            $servicingSetUpFee = $fileHMLONewLoanInfo->servicingSetUpFee;
            $taxServiceFee = $fileHMLONewLoanInfo->taxServiceFee;
            $floodServiceFee = $fileHMLONewLoanInfo->floodServiceFee;
            $constructionHoldbackFee = $fileHMLONewLoanInfo->constructionHoldbackFee;
            $thirdPartyFees = $fileHMLONewLoanInfo->thirdPartyFees;
            $otherFee = $fileHMLONewLoanInfo->otherFee;
            $taxImpoundsMonth = $fileHMLONewLoanInfo->taxImpoundsMonth;
            $taxImpoundsMonthAmt = $fileHMLONewLoanInfo->taxImpoundsMonthAmt;
            $taxImpoundsFee = $fileHMLONewLoanInfo->taxImpoundsFee;
            $insImpoundsMonth = $fileHMLONewLoanInfo->insImpoundsMonth;
            $insImpoundsMonthAmt = $fileHMLONewLoanInfo->insImpoundsMonthAmt;
            $insImpoundsFee = $fileHMLONewLoanInfo->insImpoundsFee;
            $interestChargedFromDate = $fileHMLONewLoanInfo->interestChargedFromDate;
            $interestChargedEndDate = $fileHMLONewLoanInfo->interestChargedEndDate;
            $costOfImprovementsToBeMade = $fileHMLONewLoanInfo->costOfImprovementsToBeMade;
            $LOCTotalLoanAmt = $fileHMLONewLoanInfo->LOCTotalLoanAmt;
            $rehabCostPercentageFinanced = $fileHMLONewLoanInfo->rehabCostPercentageFinanced;
            $downPaymentPercentage = $fileHMLONewLoanInfo->downPaymentPercentage;
            $CORTotalLoanAmt = $fileHMLONewLoanInfo->CORTotalLoanAmt;
            $CORefiLTVPercentage = $fileHMLONewLoanInfo->CORefiLTVPercentage;
            $PAExpirationDate = $fileHMLONewLoanInfo->PAExpirationDate;
            $includeCCF = $fileHMLONewLoanInfo->includeCCF;
            $isOwnLand = $fileHMLONewLoanInfo->isOwnLand;
            $landValue = $fileHMLONewLoanInfo->landValue;
            $desiredLoanAmount = $fileHMLONewLoanInfo->desiredLoanAmount;
            $desiredInterestRateRangeFrom = $fileHMLONewLoanInfo->desiredInterestRateRangeFrom;
            $desiredInterestRateRangeTo =$fileHMLONewLoanInfo->desiredInterestRateRangeTo;

            $inspectionFees =$fileHMLONewLoanInfo->inspectionFees;
            $projectFeasibility =$fileHMLONewLoanInfo->projectFeasibility;
            $dueDiligence =$fileHMLONewLoanInfo->dueDiligence;
            $UccLienSearch = $fileHMLONewLoanInfo->UccLienSearch;
            $closingCostFinancingFee =$fileHMLONewLoanInfo->closingCostFinancingFee;
            $extensionOptionPercentage = $fileHMLONewLoanInfo->extensionOptionPercentage;
            $extensionRatePercentage = $fileHMLONewLoanInfo->extensionRatePercentage;

            $noUnitsOccupied = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits;
            $stabilizedRate = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyStabilizedRate;
            $calcInrBasedOnMonthlyPayment = $fileHMLONewLoanInfo->calcInrBasedOnMonthlyPayment;


            if (Dates::IsEmpty($originalPurchaseDate)) {
                $originalPurchaseDate = '';
            } else {
                $originalPurchaseDate = Dates::formatDateWithRE($originalPurchaseDate, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($datesigned)) {
                $datesigned = '';
            } else {
                $datesigned = Dates::formatDateWithRE($datesigned, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($resaleClosingDate)) {
                $resaleClosingDate = '';
            } else {
                $resaleClosingDate = Dates::formatDateWithRE($resaleClosingDate, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($loanTermExpireDate)) {
                $loanTermExpireDate = '';
            } else {
                $loanTermExpireDate = Dates::formatDateWithRE($loanTermExpireDate, 'YMD', 'm/d/Y');
            }
            if ($additionalPropertyRestrictions == 'Yes') {
                $additionalPropertyRestrictionsDispOpt = 'display: table-row;';
            }
            if (Dates::IsEmpty($PAExpirationDate)) {
                $PAExpirationDate = '';
            } else {
                $PAExpirationDate = Dates::formatDateWithRE($PAExpirationDate, 'YMD', 'm/d/Y');
            }
        }

        $borrowerCallBack = LMRequest::myFileInfo()->ResponseInfo()->borrowerCallBack;
        $welcomeCallDate = LMRequest::myFileInfo()->ResponseInfo()->welcomeCallDate;
        $projectName = LMRequest::myFileInfo()->ResponseInfo()->projectName;
        $leadSource = LMRequest::myFileInfo()->ResponseInfo()->leadSource;

        if (Dates::IsEmpty($borrowerCallBack)) {
            $borrowerCallBack = '';
        } else {
            $borrowerCallBack = Dates::formatDateWithRE($borrowerCallBack, 'YMD', 'm/d/Y');
        }

        if (Dates::IsEmpty($receivedDate)) {
            $receivedDate = '';
        } else {
            $receivedDate = Dates::formatDateWithRE($receivedDate, 'YMD', 'm/d/Y');
        }

        if (Dates::IsEmpty($welcomeCallDate)) {
            $welcomeCallDate = '';
        } else {
            $welcomeCallDate = Dates::formatDateWithRE($welcomeCallDate, 'YMD', 'm/d/Y');
        }
        if ($fileHMLOPropertyInfo) {

            $typeOfHMLOLoanRequesting = trim($fileHMLOPropertyInfo->typeOfHMLOLoanRequesting);
            $lienPosition = $fileHMLOPropertyInfo->lienPosition;
            $loanTerm = $fileHMLOPropertyInfo->loanTerm;
            $propertyNeedRehab = $fileHMLOPropertyInfo->propertyNeedRehab;
            $isThisGroundUpConstruction = $fileHMLOPropertyInfo->isThisGroundUpConstruction;
            $lotStatus = $fileHMLOPropertyInfo->lotStatus;
            $lotPurchasePrice = $fileHMLOPropertyInfo->lotPurchasePrice;
            $currentLotMarket = $fileHMLOPropertyInfo->currentLotMarket;
            $acceptedPurchase = $fileHMLOPropertyInfo->acceptedPurchase;
            $requiredLoanAmount = $fileHMLOPropertyInfo->requiredLoanAmount;
            $exitStrategy = $fileHMLOPropertyInfo->exitStrategy;
            $rehabCost = LMRequest::File()->getTblFileHMLORehabInfo_by_fileID()[0]->rehabCost;
            $rehabRepairDetails = LMRequest::File()->getTblFileHMLORehabInfo_by_fileID()[0]->rehabRepairDetails;
            $neededToCompleteRehab = LMRequest::File()->getTblFileHMLORehabInfo_by_fileID()[0]->neededToCompleteRehab;
            $rehabCompanyName = LMRequest::File()->getTblFileHMLORehabInfo_by_fileID()[0]->rehabCompanyName;
            $rehabContractorName = LMRequest::File()->getTblFileHMLORehabInfo_by_fileID()[0]->rehabContractorName;
            $rehabGCLicense = LMRequest::File()->getTblFileHMLORehabInfo_by_fileID()[0]->rehabGCLicense;
            $rehabContractorEmail = LMRequest::File()->getTblFileHMLORehabInfo_by_fileID()[0]->rehabContractorEmail;
            $rehabContractorPhone = LMRequest::File()->getTblFileHMLORehabInfo_by_fileID()[0]->rehabContractorPhone;
            $rehabStrategyPlans = $fileHMLOPropertyInfo->rehabStrategyPlans;
            $annualPremium = $fileHMLOPropertyInfo->annualPremium;
            $isHiredPerformRehab = $fileHMLOPropertyInfo->isHiredPerformRehab;
            $prePaymentPenalty = $fileHMLOPropertyInfo->prePaymentPenalty;
            $propEquity = $fileHMLOPropertyInfo->propEquity;
            $maxAmtToPutDown = $fileHMLOPropertyInfo->maxAmtToPutDown;
            $approvedLoanAmt = $fileHMLOPropertyInfo->approvedLoanAmt;
            $HMLOEstateHeldIn = $fileHMLOPropertyInfo->HMLOEstateHeldIn;
            $isBlanketLoan = $fileHMLOPropertyInfo->isBlanketLoan;
            $paymentReserves = $fileHMLOPropertyInfo->paymentReserves;
            $requiredConstruction = $fileHMLOPropertyInfo->requiredConstruction;
            $contingencyReserve = $fileHMLOPropertyInfo->contingencyReserve;
            $typeOfSale = $fileHMLOPropertyInfo->typeOfSale;
            $docTypeLoanterms = $fileHMLOPropertyInfo->docType;
            $assetCollateralized = $fileHMLOPropertyInfo->assetCollateralized;
            $propDetailsProcess = $fileHMLOPropertyInfo->propDetailsProcess;

            $involvedPurchase = $fileHMLOPropertyInfo->involvedPurchase;
            $wholesaleFee = $fileHMLOPropertyInfo->wholesaleFee;
            $seekingCashRefinance = $fileHMLOPropertyInfo->seekingCashRefinance;
            $seekingCash = $fileHMLOPropertyInfo->seekingCash;
            $seekingFund = $fileHMLOPropertyInfo->seekingFund;
            $isAdditionalGuarantors = $fileHMLOPropertyInfo->isAdditionalGuarantors;
            $referringParty = $fileHMLOPropertyInfo->referringParty;
            $hereAbout = $fileHMLOPropertyInfo->hereAbout;
            $minLiabilityCoverage = $fileHMLOPropertyInfo->minLiabilityCoverage;
            $proInsPolicyNo = $fileHMLOPropertyInfo->proInsPolicyNo;
            $floodInsurance1 = $incomeInfo->floodInsurance1;
            $proInsPolicyExpDate = $fileHMLOPropertyInfo->proInsPolicyExpDate;
            $proInsPolicyEffDate = $fileHMLOPropertyInfo->proInsPolicyEffDate;
            $propIncNotes = $fileHMLOPropertyInfo->propIncNotes;
            $proInsCarrier = $fileHMLOPropertyInfo->proInsCarrier;
            $desiredFundingAmount = $fileHMLOPropertyInfo->desiredFundingAmount;
            $purposeOfLoan = $fileHMLOPropertyInfo->purposeOfLoan;
            $useOfFunds = $fileHMLOPropertyInfo->useOfFunds;
            $haveCurrentLoanBal = $fileHMLOPropertyInfo->haveCurrentLoanBal;
            $balance = $fileHMLOPropertyInfo->balance;
            $doYouHaveInvoiceToFactor = $fileHMLOPropertyInfo->haveInvoiceToFactor;
            $invoiceFactorAmount = $fileHMLOPropertyInfo->amount;
            $amount = $fileHMLOPropertyInfo->amount;
            $heldWith = $fileHMLOPropertyInfo->heldWith;

            $isTherePrePaymentPenalty = $fileHMLOPropertyInfo->isTherePrePaymentPenalty;
            $balloonPayment = $fileHMLOPropertyInfo->balloonPayment;
            $prePayExcessOf20percent = $fileHMLOPropertyInfo->prePayExcessOf20percent;
            $limitedOrNot = $fileHMLOPropertyInfo->limitedOrNot;
            $loanMadeWholly = $fileHMLOPropertyInfo->loanMadeWholly;
            $securityInstrument = $fileHMLOPropertyInfo->securityInstrument;
            $rentalIncomePerMonth = $fileHMLOPropertyInfo->rentalIncomePerMonth;

            $rehabToBeMade = $fileHMLOPropertyInfo->rehabToBeMade;
            $rehabTime = $fileHMLOPropertyInfo->rehabTime;
            $isSubjectUnderConst = $fileHMLOPropertyInfo->isSubjectUnderConst;
            $areKnownHazards = $fileHMLOPropertyInfo->areKnownHazards;
            $areProReports = $fileHMLOPropertyInfo->areProReports;
            $isSubjectSS = $fileHMLOPropertyInfo->isSubjectSS;
            $changeInCircumstance = $fileHMLOPropertyInfo->changeInCircumstance;
            $changeDescription = $fileHMLOPropertyInfo->changeDescription;
            $useOfProceeds = $fileHMLOPropertyInfo->useOfProceeds;

            if ($isTherePrePaymentPenalty == 'Yes') $prepayentSectionDisplay = 'display: block;';
        }
        if (Dates::IsEmpty($propertyTaxDueDate)) {
            $propertyTaxDueDate = '';
        } else {
            $propertyTaxDueDate = Dates::formatDateWithRE($propertyTaxDueDate, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty($proInsPolicyExpDate)) {
            $proInsPolicyExpDate = '';
        } else {
            $proInsPolicyExpDate = Dates::formatDateWithRE($proInsPolicyExpDate, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty($proInsPolicyEffDate)) {
            $proInsPolicyEffDate = '';
        } else {
            $proInsPolicyEffDate = Dates::formatDateWithRE($proInsPolicyEffDate, 'YMD', 'm/d/Y');
        }


        $titleAttorneyContacts = $fileContacts->Attorney;

        if ($titleAttorneyContacts) {
            $titleAttorneyID = trim($titleAttorneyContacts->CID);
            $titleAttorneyName = trim($titleAttorneyContacts->contactName);
            $titleAttorneyFirmName = trim($titleAttorneyContacts->companyName);
            $titleAttorneyPhone = trim($titleAttorneyContacts->phone);
            $titleAttorneyEmail = trim($titleAttorneyContacts->email);
            $titleAttorneyAddress = trim($titleAttorneyContacts->address);
            $titleAttorneyCity = trim($titleAttorneyContacts->city);
            $titleAttorneyState = trim($titleAttorneyContacts->state);
            $titleAttorneyZip = trim($titleAttorneyContacts->zip);
            $titleAttorneyBarNo = trim($titleAttorneyContacts->barNo);
        }

        $disableInsuranceCompanyContacts = '';
        if ($fileContacts->InsuranceRep) {
            $disableInsuranceCompanyContacts = 'disabled';
            $insuranceCompanyContacts = $fileContacts->InsuranceRep;
        }

        $insuranceAgentInfoArray = [];
        $attorneysInfoArray = [];
        $escrowsInfoArray = [];
        $financialAdvisorArray = [];
        $accountantArray = [];
        if ($fileContacts->multiContact) {
            if (array_key_exists('Insurance Rep', $fileContacts->multiContact)) {
                $disableInsuranceCompanyContacts = 'disabled';
                $insuranceAgentInfoArray = $fileContacts->multiContact['Insurance Rep'];
                if (count($insuranceAgentInfoArray) > 0) {
                    usort($insuranceAgentInfoArray, function (FileContactsEntry $a, FileContactsEntry $b) {
                        return $a->SID - $b->SID;
                    });
                }
            }
            if (array_key_exists('Attorney', $fileContacts->multiContact)) {
                $attorneysInfoArray = $fileContacts->multiContact['Attorney'];
                if (count($attorneysInfoArray) > 0) {
                    usort($attorneysInfoArray, function (FileContactsEntry $a, FileContactsEntry $b) {
                        return $a->SID - $b->SID;
                    });
                }
            }
            if (array_key_exists('Escrow', $fileContacts->multiContact)) {
                $escrowsInfoArray = $fileContacts->multiContact['Escrow'];
                if (count($escrowsInfoArray) > 0) {
                    usort($escrowsInfoArray, function (FileContactsEntry $a, FileContactsEntry $b) {
                        return $a->SID - $b->SID;
                    });
                }
            }
            if (array_key_exists('Title Rep', $fileContacts->multiContact)) {
                $titlesInfoArray = $fileContacts->multiContact['Title Rep'];
                if (count($titlesInfoArray) > 0) {
                    usort($titlesInfoArray, function (FileContactsEntry $a, FileContactsEntry $b) {
                        return $a->SID - $b->SID;
                    });
                }
            }

            if (array_key_exists('Financial Advisor', $fileContacts->multiContact)) {
                $financialAdvisorArray = $fileContacts->multiContact['Financial Advisor'];
                if (count($financialAdvisorArray) > 0) {
                    usort($financialAdvisorArray, function (FileContactsEntry $a, FileContactsEntry $b) {
                        return $a->SID - $b->SID;
                    });
                }
            }
            if (array_key_exists('Accountant', $fileContacts->multiContact)) {
                $accountantArray = $fileContacts->multiContact['Accountant'];
                if (count($accountantArray) > 0) {
                    usort($accountantArray, function (FileContactsEntry $a, FileContactsEntry $b) {
                        return $a->SID - $b->SID;
                    });
                }
            }
        }

        if ($insuranceCompanyContacts) {
            $insuranceCompanyID = trim($insuranceCompanyContacts->CID);
            $proInsFirstName = trim($insuranceCompanyContacts->contactName);
            $proInsName = trim($insuranceCompanyContacts->companyName);
            $proIncCell = trim($insuranceCompanyContacts->cell);
            $proIncFax = trim($insuranceCompanyContacts->fax);
            $proIncTollFree = trim($insuranceCompanyContacts->tollFree);
            $proIncRepNotes = trim($insuranceCompanyContacts->description);
            $proInsLastName = trim($insuranceCompanyContacts->contactLName);
            $proIncWebsite = trim($insuranceCompanyContacts->website);
            $proIncEmail = trim($insuranceCompanyContacts->email);
            $proIncPhone = trim($insuranceCompanyContacts->phone);
            $proInsAddress = trim($insuranceCompanyContacts->address);
            $proInsCity = trim($insuranceCompanyContacts->city);
            $proInsState = trim($insuranceCompanyContacts->state);
            $proInsZip = trim($insuranceCompanyContacts->zip);
        }

        $titleContacts = $fileContacts->TitleRep;


        if ($titleContacts) {
            $representativeID = trim($titleContacts->CID);
            $titleContactName = trim($titleContacts->contactName);
            $titleCompanyName = trim($titleContacts->companyName);
            $titleCell = trim($titleContacts->cell);
            $titletollFree = trim($titleContacts->tollFree);
            $titleNotes = trim($titleContacts->description);
            $titleContactLName = trim($titleContacts->contactLName);
        }
        $titleReportDate = $FilePropInfo->titleReportDate;
        $titlePhoneNumber = LMRequest::myFileInfo()->listingRealtorInfo()->titleCompanyPhoneNumber;
        $titleFax = LMRequest::myFileInfo()->listingRealtorInfo()->sales2Fax;

        if (Dates::IsEmpty($titleReportDate)) {
            $titleReportDate = '';
        } else {
            $titleReportDate = Dates::formatDateWithRE($titleReportDate, 'YMD', 'm/d/Y');
        }

        $escrowContacts = $fileContacts->EscrowOfficer;

        if ($escrowContacts) {
            $escrowID = trim($escrowContacts->CID);
            $escrowOfficer = trim($escrowContacts->contactName);
            $escrowOfficerLName = trim($escrowContacts->contactLName);
            $escrowOfficerFirmName = trim($escrowContacts->companyName);
            $escrowOfficerEmail = trim($escrowContacts->email);
            $escrowOfficerPhone = trim($escrowContacts->phone);
            $escrowOfficertollFree = trim($escrowContacts->tollFree);
            $escrowOfficerFax = trim($escrowContacts->fax);
            $escrowOfficerCell = trim($escrowContacts->cell);
            $escrowNo = trim($escrowContacts->barNo);
            $escrowAddress = trim($escrowContacts->address);
            $escrowCity = trim($escrowContacts->city);
            $escrowState = trim($escrowContacts->state);
            $escrowZip = trim($escrowContacts->zip);

        }
        $escrowcFax1 = $escrowcFax2 = $escrowcFax3 = '';
        $escrowCellNo1 = $escrowCellNo2 = $escrowCellNo3 = '';
        $escrowOfficerFaxArray = Strings::splitPhoneNumber($escrowOfficerFax);
        if (count($escrowOfficerFaxArray) > 0) {
            $escrowFax1 = $escrowOfficerFaxArray['No1'];
            $escrowFax2 = $escrowOfficerFaxArray['No2'];
            $escrowFax3 = $escrowOfficerFaxArray['No3'];
        }


        $escrowOfficerCellArray = Strings::splitPhoneNumber($escrowOfficerCell);
        if (count($escrowOfficerCellArray) > 0) {
            $escrowCellNo1 = $escrowOfficerCellArray['No1'];
            $escrowCellNo2 = $escrowOfficerCellArray['No2'];
            $escrowCellNo3 = $escrowOfficerCellArray['No3'];
        }

        if ($lien1Terms == '') {
            if (!in_array(LMRequest::$PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                $lien1Terms = LoanTerms::INTEREST_ONLY;
            }
        }

        if ($prePaymentPenalty == '') {
            $prePaymentPenalty = 'None';
        }

        if ($lienPosition == '') {
            $lienPosition = '1';
        }

        $disabledInputForClient = 1;

        $clientExpProInfo = $fileExpFilpGroundUp['Flip'] ?? null;
        $clientGUExpInfo = $fileExpFilpGroundUp['Gup'] ?? null;
        $clientSellExpInfo = $fileExpFilpGroundUp['Sell'] ?? null;

        $previousState = $LMRInfo->previousState;
        $PCClientEntityInfoArray = getPCClientEntityInfo::getReport([
            'PCID' => LMRequest::$PCID,
            'CID'  => LMRequest::File()->clientId,
        ]);

        if (in_array(LMRequest::$PCID, $glFirstRehabLending)) {
            unset($glHMLOPresentOccupancy[1]);
            unset($glHMLOPresentOccupancy[2]);
            unset($glHMLOPresentOccupancy[3]);
            $glHMLOPresentOccupancy = array_values($glHMLOPresentOccupancy);
        }

        $escrowContacts = $fileContacts->Escrow;

        if ($escrowContacts) {
            $escrowID = trim($escrowContacts->CID);
            $escrowOfficer = trim($escrowContacts->contactName);
            $escrowOfficerLName = trim($escrowContacts->contactLName);
            $escrowOfficerFirmName = trim($escrowContacts->companyName);
            $escrowOfficerEmail = trim($escrowContacts->email);
            $escrowOfficerPhone = trim($escrowContacts->phone);
            $escrowOfficertollFree = trim($escrowContacts->tollFree);
            $escrowOfficerFax = trim($escrowContacts->fax);
            $escrowOfficerCell = trim($escrowContacts->cell);
            $escrowNo = trim($escrowContacts->barNo);
            $escrowAddress = trim($escrowContacts->address);
            $escrowCity = trim($escrowContacts->city);
            $escrowState = trim($escrowContacts->state);
            $escrowZip = trim($escrowContacts->zip);
        }
        $escrowcFax1 = $escrowcFax2 = $escrowcFax3 = '';
        $escrowCellNo1 = $escrowCellNo2 = $escrowCellNo3 = '';
        $escrowOfficerFaxArray = Strings::splitPhoneNumber($escrowOfficerFax);
        if (count($escrowOfficerFaxArray) > 0) {
            $escrowFax1 = $escrowOfficerFaxArray['No1'];
            $escrowFax2 = $escrowOfficerFaxArray['No2'];
            $escrowFax3 = $escrowOfficerFaxArray['No3'];
        }
        $escrowOfficerCellArray = Strings::splitPhoneNumber($escrowOfficerCell);
        if (count($escrowOfficerCellArray) > 0) {
            $escrowCellNo1 = $escrowOfficerCellArray['No1'];
            $escrowCellNo2 = $escrowOfficerCellArray['No2'];
            $escrowCellNo3 = $escrowOfficerCellArray['No3'];
        }
        /* Estimated Project Cost */
        if ($estimatedProjectCostArray) {
            $landAcquisition = trim($estimatedProjectCostArray->landAcquisition);
            $newBuildingConstruction = trim($estimatedProjectCostArray->newBuildingConstruction);
            $constructionContingency = trim($estimatedProjectCostArray->constructionContingency);
            $businessAcquisition = trim($estimatedProjectCostArray->businessAcquisition);
            $landAndBusinessAcquisition = trim($estimatedProjectCostArray->landAndBusinessAcquisition);
            $buildingOrLeasehold = trim($estimatedProjectCostArray->buildingOrLeasehold);
            $acquisitionOfMachineryEquipment = trim($estimatedProjectCostArray->acquisitionOfMachineryEquipment);
            $acquisitionOfFurnitureFixtures = trim($estimatedProjectCostArray->acquisitionOfFurnitureFixtures);
            $inventoryPurchase = trim($estimatedProjectCostArray->inventoryPurchase);
            $workingCapital = trim($estimatedProjectCostArray->workingCapital);
            $refinancingExistingBusinessDebt = trim($estimatedProjectCostArray->refinancingExistingBusinessDebt);
            $franchiseFee = trim($estimatedProjectCostArray->franchiseFee);
            $closingCost = trim($estimatedProjectCostArray->closingCost);
            $otherCostText = trim($estimatedProjectCostArray->otherCostText); //no FF control
            $otherCost = trim($estimatedProjectCostArray->otherCost);
            $otherCost2 = trim($estimatedProjectCostArray->otherCost2);
            $otherCostText2 = trim($estimatedProjectCostArray->otherCostText2); //no FF control
            $lessOwnerEquityToBeInjected = trim($estimatedProjectCostArray->lessOwnerEquityToBeInjected);
            $lessSellerCarryBack = trim($estimatedProjectCostArray->lessSellerCarryBack);

            $estimatedProjectCost = Strings::replaceCommaValues($landAcquisition) + Strings::replaceCommaValues($newBuildingConstruction) + Strings::replaceCommaValues($constructionContingency) +
                Strings::replaceCommaValues($businessAcquisition) + Strings::replaceCommaValues($landAndBusinessAcquisition) + Strings::replaceCommaValues($buildingOrLeasehold) + Strings::replaceCommaValues($acquisitionOfMachineryEquipment) +
                Strings::replaceCommaValues($acquisitionOfFurnitureFixtures) + Strings::replaceCommaValues($inventoryPurchase) + Strings::replaceCommaValues($workingCapital) + Strings::replaceCommaValues($refinancingExistingBusinessDebt) +
                Strings::replaceCommaValues($otherCost) + Strings::replaceCommaValues($otherCost2) + Strings::replaceCommaValues($franchiseFee) + Strings::replaceCommaValues($closingCost);
            $estProjectCost = round($estimatedProjectCost, 2);

            $loanRequestedForProject = ($estimatedProjectCost - (Strings::replaceCommaValues($lessOwnerEquityToBeInjected) + Strings::replaceCommaValues($lessSellerCarryBack)));
            $loanRequestedForProject = round($loanRequestedForProject, 2);
        }
        /* Estimated Project Cost */
        /* Creditors / Liabilities (Non-Real Estate) */
        /* This is a multiple / add more fields section hence data added in UI directly*/
        /* Creditors / Liabilities (Non-Real Estate) */

        /* Collateral */
        if ($collateralArray) {
            $commercialRealEstate = trim($collateralArray->commercialRealEstate);
            $furnitureFixtures = trim($collateralArray->furnitureFixtures);
            $residentialRealEstate = trim($collateralArray->residentialRealEstate);
            $vehicle = trim($collateralArray->vehicle);
            $inventory = trim($collateralArray->inventory);
            $usedEquipmentMachinery = trim($collateralArray->usedEquipmentMachinery);
            $accountsReceivable = trim($collateralArray->accountsReceivable);
            $vacantLand = trim($collateralArray->vacantLand);
            $cash = trim($collateralArray->cash);
            $leaseholdImprovement = trim($collateralArray->leaseholdImprovement);
            $investmentAccount = trim($collateralArray->investmentAccount);
            $newEquipmentMachinery = trim($collateralArray->newEquipmentMachinery);
            $retirementAccount = trim($collateralArray->retirementAccount);
            $other = trim($collateralArray->other);
            $none = trim($collateralArray->noneField);
            $collateralDescription = trim($collateralArray->collateralDescription);

            if ($collateralArray->commercialRealEstateDate) {
                $commercialRealEstateDate = DateTime::createFromFormat('Y-m-d', $collateralArray->commercialRealEstateDate)->format('Y/m/d');
            }
            $commercialRealEstateDesc = trim($collateralArray->commercialRealEstateDesc);


            if ($collateralArray->vehiclesDate) {
                $vehiclesDate = DateTime::createFromFormat('Y-m-d', $collateralArray->vehiclesDate)->format('Y/m/d');
            }
            $vehiclesDesc = trim($collateralArray->vehiclesDesc);

            if ($collateralArray->accountsReceivableDate) {
                $accountsReceivableDate = DateTime::createFromFormat('Y-m-d', $collateralArray->accountsReceivableDate)->format('Y/m/d');
            }

            $leaseholdImprovementsDesc = trim($collateralArray->leaseholdImprovementsDesc);
            $newEquipmentMachineryDesc = trim($collateralArray->newEquipmentMachineryDesc);
            $furnitureFixturesDesc = trim($collateralArray->furnitureFixturesDesc);

            if ($collateralArray->inventoryDate) {
                $inventoryDate = DateTime::createFromFormat('Y-m-d', $collateralArray->inventoryDate)->format('Y/m/d');
            }
            if ($collateralArray->vacantLandDate) {
                $vacantLandDate = DateTime::createFromFormat('Y-m-d', $collateralArray->vacantLandDate)->format('Y/m/d');
            }
            $vacantLandDesc = trim($collateralArray->vacantLandDesc);
            $investmentAccountsDesc = trim($collateralArray->investmentAccountsDesc);

            if ($collateralArray->otherDate) {
                $otherDate = DateTime::createFromFormat('Y-m-d', $collateralArray->otherDate)->format('Y/m/d');
            }
            $otherDesc = trim($collateralArray->otherDesc);

            if ($collateralArray->residentialRealEstateDate) {
                $residentialRealEstateDate = DateTime::createFromFormat('Y-m-d', $collateralArray->residentialRealEstateDate)->format('Y/m/d');
            }
            $residentialRealEstateDesc = trim($collateralArray->residentialRealEstateDesc);
            $usedEquipmentMachineryDesc = trim($collateralArray->usedEquipmentMachineryDesc);
            $retirementAccountDesc = trim($collateralArray->retirementAccountDesc);
        }
        /* Collateral */

        /* Admin Info */
        $hearingDate = $QAInfo->hearingDate;
        if (Dates::IsEmpty($hearingDate)) {
            $hearingDate = '';
        } else {
            $hearingDate = Dates::formatDateWithRE($hearingDate, 'YMD', 'm/d/Y');
        }
        /* Admin Info */

        /* Project Management */
        if ($propMgmtArray) {
            $propMgmtRowId = trim($propMgmtArray->id);
            $pmSubjectMarketArea = trim($propMgmtArray->pmSubjectMarketArea);
            $pmSubjectMarketAreaExpl = trim($propMgmtArray->pmSubjectMarketAreaExpl);
            $pmRealEstatePropertyManagement = trim($propMgmtArray->pmRealEstatePropertyManagement);
            $pmTenant = trim($propMgmtArray->pmTenant);
            $pmTenantExpl = trim($propMgmtArray->pmTenantExpl);
            $pmAuditsPastYear = trim($propMgmtArray->pmAuditsPastYear);
            $pmAuditsPastYearExpl = trim($propMgmtArray->pmAuditsPastYearExpl);
            $pmLeaseTerms = trim($propMgmtArray->pmLeaseTerms);
            $pmLeaseTermsExpl = trim($propMgmtArray->pmLeaseTermsExpl);
            $pmIllegalActivities = trim($propMgmtArray->pmIllegalActivities);
            $pmIllegalActivitiesExpl = trim($propMgmtArray->pmIllegalActivitiesExpl);
            $pmMaterialDeferred = trim($propMgmtArray->pmMaterialDeferred);
            $pmMaterialDeferredExpl = trim($propMgmtArray->pmMaterialDeferredExpl);
            $pmPermitViolation = trim($propMgmtArray->pmPermitViolation);
            $pmPermitViolationExpl = trim($propMgmtArray->pmPermitViolationExpl);
            $pmTenantDelinquencies = trim($propMgmtArray->pmTenantDelinquencies);
            $pmTenantDelinquenciesExpl = trim($propMgmtArray->pmTenantDelinquenciesExpl);
            $pmCondemnationProceeding = trim($propMgmtArray->pmCondemnationProceeding);
            $pmCondemnationProceedingExpl = trim($propMgmtArray->pmCondemnationProceedingExpl);
            $pmAffordableHousing = trim($propMgmtArray->pmAffordableHousing);
            $pmAffordableHousingExpl = trim($propMgmtArray->pmAffordableHousingExpl);
            $pmRentControl = trim($propMgmtArray->pmRentControl);
            $pmRentControlExpl = trim($propMgmtArray->pmRentControlExpl);
            $pmFairHousingACT = trim($propMgmtArray->pmFairHousingACT);
            $pmFairHousingACTExpl = trim($propMgmtArray->pmFairHousingACTExpl);
            $pmAmericanDisability = trim($propMgmtArray->pmAmericanDisability);
            $pmAmericanDisabilityExpl = trim($propMgmtArray->pmAmericanDisabilityExpl);
            $pmHazardReductionAct = trim($propMgmtArray->pmHazardReductionAct);
            $pmHazardReductionActExpl = trim($propMgmtArray->pmHazardReductionActExpl);
            $pmPracticesAct = trim($propMgmtArray->pmPracticesAct);
            $pmPracticesActExpl = trim($propMgmtArray->pmPracticesActExpl);
            $pmOverallExperienceDesc = trim($propMgmtArray->pmOverallExperienceDesc);
        }
        /* Project Management */

        $prePaymentPenaltyResArr = glprePaymentPenalty::getPCLevelPrePaymentPenalty(LMRequest::$PCID, 'FC');

        if (!isset($activeTab)) $activeTab = 'BO';
        if ($activeTab == 'QAPP') $fileTab = 'QA';
        if ($activeTab == 'LI') $fileTab = 'FA';

        $incomeInfo = LMRequest::myFileInfo()->incomeInfo();
        $tempFileModuleInfo = LMRequest::myFileInfo()->fileModuleInfo();


        foreach ($tempFileModuleInfo as $m => $item) {
            if (trim($item->moduleCode) == 'HMLO') {
                /** Hard / Private Money LOS Section on Feb 04, 2017 **/
                $isHMLO = 1;
                break;
            }
        }
        $isTaxesInsEscrowed = '';
        if ($isHMLO == 1) {
            $floodInsurance1 = '';
            $annualPremium = 0;
            $totalInsurance = '';
            $HMLORealEstateTaxes = 0;
        } else {
            //this variable is moved here, as it is reseting the value for isTaxesInsEscrowed if it is declared at the top (prev code).
            $isTaxesInsEscrowed = '';
        }

        if ($incomeInfo) {

            $commissionOrBonus1 = trim($incomeInfo->commissionOrBonus1);
            $overtime1 = trim($incomeInfo->overtime1);
            $tipsMiscIncome1 = trim($incomeInfo->tipsMiscIncome1);
            $netMonthlyIncome1 = trim($incomeInfo->netMonthlyIncome1);
            $grossIncome2 = trim($incomeInfo->grossIncome2);
            $commissionOrBonus2 = trim($incomeInfo->commissionOrBonus2);
            $overtime2 = trim($incomeInfo->overtime2);
            $tipsMiscIncome2 = trim($incomeInfo->tipsMiscIncome2);
            $netMonthlyIncome2 = trim($incomeInfo->netMonthlyIncome2);
            $socialSecurity1 = trim($incomeInfo->socialSecurity1);
            $netSocialSecurity1 = trim($incomeInfo->netSocialSecurity1);
            $pensionOrRetirement1 = trim($incomeInfo->pensionOrRetirement1);
            $netPensionOrRetirement1 = trim($incomeInfo->netPensionOrRetirement1);
            $disability1 = trim($incomeInfo->disability1);
            $netDisability1 = trim($incomeInfo->netDisability1);
            $rental1 = trim($incomeInfo->rental1);
            $netRental1 = trim($incomeInfo->netRental1);
            $unemployment1 = trim($incomeInfo->unemployment1);
            $netUnemployment1 = trim($incomeInfo->netUnemployment1);
            $earnedInterest1 = trim($incomeInfo->earnedInterest1);
            $netEarnedInterest1 = trim($incomeInfo->netEarnedInterest1);
            $roomRental1 = trim($incomeInfo->roomRental1);
            $netRoomRental1 = trim($incomeInfo->netRoomRental1);
            $secondJobIncome1 = trim($incomeInfo->secondJobIncome1);
            $netSecondJobIncome1 = trim($incomeInfo->netSecondJobIncome1);
            $sonOrDaughter1 = trim($incomeInfo->sonOrDaughter1);
            $parents1 = trim($incomeInfo->parents1);
            $childSupportOrAlimony1 = trim($incomeInfo->childSupportOrAlimony1);
            $otherHouseHold1 = trim($incomeInfo->otherHouseHold1);
            $foodStampWelfare1 = trim($incomeInfo->foodStampWelfare1);
            $socialSecurity2 = trim($incomeInfo->socialSecurity2);
            $netSocialSecurity2 = trim($incomeInfo->netSocialSecurity2);
            $pensionOrRetirement2 = trim($incomeInfo->pensionOrRetirement2);
            $netPensionOrRetirement2 = trim($incomeInfo->netPensionOrRetirement2);
            $disability2 = trim($incomeInfo->disability2);
            $netDisability2 = trim($incomeInfo->netDisability2);
            $rental2 = trim($incomeInfo->rental2);
            $netRental2 = trim($incomeInfo->netRental2);
            $unemployment2 = trim($incomeInfo->unemployment2);
            $netUnemployment2 = trim($incomeInfo->netUnemployment2);
            $earnedInterest2 = trim($incomeInfo->earnedInterest2);
            $netEarnedInterest2 = trim($incomeInfo->netEarnedInterest2);
            $roomRental2 = trim($incomeInfo->roomRental2);
            $netRoomRental2 = trim($incomeInfo->netRoomRental2);
            $secondJobIncome2 = trim($incomeInfo->secondJobIncome2);
            $netSecondJobIncome2 = trim($incomeInfo->netSecondJobIncome2);
            $sonOrDaughter2 = trim($incomeInfo->sonOrDaughter2);
            $parents2 = trim($incomeInfo->parents2);
            $childSupportOrAlimony2 = trim($incomeInfo->childSupportOrAlimony2);
            $otherHouseHold2 = trim($incomeInfo->otherHouseHold2);
            $foodStampWelfare2 = trim($incomeInfo->foodStampWelfare2);
            $HOAFees1 = trim($incomeInfo->HOAFees1);
            $creditCards1 = trim($incomeInfo->creditCards1);
            $creditCardsBalance1 = trim($incomeInfo->creditCardsBalance1);
            $autoLoan1 = trim($incomeInfo->autoLoan1);
            $autoLoanBalance1 = trim($incomeInfo->autoLoanBalance1);
            $unsecuredLoans1 = trim($incomeInfo->unsecuredLoans1);
            $unsecuredLoanBalance1 = trim($incomeInfo->unsecuredLoanBalance1);
            $otherMortgage1 = trim($incomeInfo->otherMortgage1);
            $otherMortgageBalance1 = trim($incomeInfo->otherMortgageBalance1);
            $studentLoans1 = trim($incomeInfo->studentLoans1);
            $studentLoansBalance1 = trim($incomeInfo->studentLoansBalance1);
            $careAmt1 = trim($incomeInfo->careAmt1);
            $allInsurance1 = trim($incomeInfo->allInsurance1);
            $groceries1 = trim($incomeInfo->groceries1);
            $carExpenses1 = trim($incomeInfo->carExpenses1);
            $medicalBill1 = trim($incomeInfo->medicalBill1);
            $entertainment1 = trim($incomeInfo->entertainment1);
            $other1 = trim($incomeInfo->other1);
            $donation1 = trim($incomeInfo->donation1);
            $pets1 = trim($incomeInfo->pets1);
            $monthlyParking1 = trim($incomeInfo->monthlyParking1);
            $unionDues1 = trim($incomeInfo->unionDues1);
            $personalLoan1 = trim($incomeInfo->personalLoan1);
            $lunchPurchased1 = trim($incomeInfo->lunchPurchased1);
            $rentalExp1 = trim($incomeInfo->rentalExp1);
            $creditCards2 = trim($incomeInfo->creditCards2);
            $creditCardsBalance2 = trim($incomeInfo->creditCardsBalance2);
            $autoLoan2 = trim($incomeInfo->autoLoan2);
            $autoLoanBalance2 = trim($incomeInfo->autoLoanBalance2);
            $unsecuredLoans2 = trim($incomeInfo->unsecuredLoans2);
            $unsecuredLoanBalance2 = trim($incomeInfo->unsecuredLoanBalance2);
            $otherMortgage2 = trim($incomeInfo->otherMortgage2);
            $otherMortgageBalance2 = trim($incomeInfo->otherMortgageBalance2);
            $studentLoans2 = trim($incomeInfo->studentLoans2);
            $studentLoansBalance2 = trim($incomeInfo->studentLoansBalance2);
            $careAmt2 = trim($incomeInfo->careAmt2);
            $allInsurance2 = trim($incomeInfo->allInsurance2);
            $groceries2 = trim($incomeInfo->groceries2);
            $carExpenses2 = trim($incomeInfo->carExpenses2);
            $medicalBill2 = trim($incomeInfo->medicalBill2);
            $entertainment2 = trim($incomeInfo->entertainment2);
            $other2 = trim($incomeInfo->other2);
            $donation2 = trim($incomeInfo->donation2);
            $pets2 = trim($incomeInfo->pets2);
            $monthlyParking2 = trim($incomeInfo->monthlyParking2);
            $unionDues2 = trim($incomeInfo->unionDues2);
            $personalLoan2 = trim($incomeInfo->personalLoan2);
            $lunchPurchased2 = trim($incomeInfo->lunchPurchased2);
            $rentalExp2 = trim($incomeInfo->rentalExp2);
            $cable1 = trim($incomeInfo->cable1);
            $electricity1 = trim($incomeInfo->electricity1);
            $natural1 = trim($incomeInfo->natural1);
            $primaryBorrowerPhone = trim($incomeInfo->primaryBorrowerPhone);
            $water1 = trim($incomeInfo->water1);
            $internet1 = trim($incomeInfo->internet1);
            $dryCleaning1 = trim($incomeInfo->dryCleaning1);
            $utilityOther1 = trim($incomeInfo->utilityOther1);
            $cable2 = trim($incomeInfo->cable2);
            $electricity2 = trim($incomeInfo->electricity2);
            $natural2 = trim($incomeInfo->natural2);
            $coBorrowerPhone = trim($incomeInfo->coBorrowerPhone);
            $water2 = trim($incomeInfo->water2);
            $internet2 = trim($incomeInfo->internet2);
            $dryCleaning2 = trim($incomeInfo->dryCleaning2);
            $utilityOther2 = trim($incomeInfo->utilityOther2);
            $grossIncome1 = trim($incomeInfo->grossIncome1);
            $childSupportOrAlimonyMonthly2 = trim($incomeInfo->childSupportOrAlimonyMonthly2);
            $childSupportOrAlimonyMonthly1 = trim($incomeInfo->childSupportOrAlimonyMonthly1);
            $childSupportOrAlimonyMonthlyBalance1 = trim($incomeInfo->childSupportOrAlimonyMonthlyBalance1);
            $otherBalance1 = trim($incomeInfo->otherBalance1);
            $childSupportOrAlimonyMonthlyBalance2 = trim($incomeInfo->childSupportOrAlimonyMonthlyBalance2);
            $otherBalance2 = trim($incomeInfo->otherBalance2);

            $capitalGains1 = trim($incomeInfo->capitalGains1);
            $militaryIncome1 = trim($incomeInfo->militaryIncome1);
            $partnership1 = trim($incomeInfo->partnership1);
            $militaryIncome1 = trim($incomeInfo->militaryIncome1);
            $otherIncomeDescription1 = trim($incomeInfo->otherIncomeDescription1);
            $capitalGains2 = trim($incomeInfo->capitalGains2);
            $partnership2 = trim($incomeInfo->partnership2);
            $otherIncomeDescription2 = trim($incomeInfo->otherIncomeDescription2);
            $expFedTax = trim($incomeInfo->expFedTax);
            $expFedTaxOwed = trim($incomeInfo->expFedTaxOwed);
            $expStateTax = trim($incomeInfo->expStateTax);
            $expStateTaxOwed = trim($incomeInfo->expStateTaxOwed);
            $expRentalPay = trim($incomeInfo->expRentalPay);
            $expRentalPayOwed = trim($incomeInfo->expRentalPayOwed);
            $expMortgPayResi = trim($incomeInfo->expMortgPayResi);
            $expMortgPayResiOwed = trim($incomeInfo->expMortgPayResi);
            $expMortgPayInvest = trim($incomeInfo->expMortgPayInvest);
            $expMortgPayInvestOwed = trim($incomeInfo->expMortgPayInvestOwed);
            $expPropTaxResi = trim($incomeInfo->expPropTaxResi);
            $expPropTaxResiOwed = trim($incomeInfo->expPropTaxResiOwed);
            $expPropTaxInvest = trim($incomeInfo->expPropTaxInvest);
            $expPropTaxInvestOwed = trim($incomeInfo->expPropTaxInvestOwed);
            $expLoanPayments = trim($incomeInfo->expLoanPayments);
            $expLoanPaymentsOwed = trim($incomeInfo->expLoanPaymentsOwed);
            $expIns = trim($incomeInfo->expIns);
            $expInsOwed = trim($incomeInfo->expInsOwed);
            $expInvestments = trim($incomeInfo->expInvestments);
            $expInvestmentsOwed = trim($incomeInfo->expInvestmentsOwed);
            $expTuition = trim($incomeInfo->expTuition);
            $expTuitionOwed = trim($incomeInfo->expTuitionOwed);
            $expOtherLiving = trim($incomeInfo->expOtherLiving);
            $expOtherLivingOwed = trim($incomeInfo->expOtherLivingOwed);
            $expMedical = trim($incomeInfo->expMedical);
            $expMedicalOwed = trim($incomeInfo->expMedicalOwed);
        }

        $lien1Payment = $LMRInfo->lien1Payment;
        $lien2Payment = $LMRInfo->lien2Payment;
        $floodInsurance1 = $incomeInfo->floodInsurance1;
        if ($floodInsurance1 == '') $floodInsurance1 = 0;
        $annualPremium = $fileHMLOPropertyInfo->annualPremium;

        if ($isHMLO == 1) {

            $HMLORealEstateTaxes = proposalFormula::calculateHMLORealEstateTaxes($taxes1, $isTaxesInsEscrowed);
            $totalInsurance = proposalFormula::calculateTotalInsurance($floodInsurance1, $annualPremium);

            $primTotalNetHouseHoldIncome = Strings::replaceCommaValues($grossIncome1)
                + Strings::replaceCommaValues($commissionOrBonus1) + Strings::replaceCommaValues($overtime1)
                + Strings::replaceCommaValues($netRental1) + Strings::replaceCommaValues($netEarnedInterest1)
                + Strings::replaceCommaValues($otherHouseHold1) + Strings::replaceCommaValues($capitalGains1)
                + Strings::replaceCommaValues($partnership1) + Strings::replaceCommaValues($militaryIncome1);

            if ($LMRInfo->isCoBorrower == 1 || $isCoBorrower == 1) {

                $coTotalNetHouseHoldIncome = Strings::replaceCommaValues($grossIncome2)
                    + Strings::replaceCommaValues($commissionOrBonus2) + Strings::replaceCommaValues($overtime2)
                    + Strings::replaceCommaValues($netRental2) + Strings::replaceCommaValues($netEarnedInterest2)
                    + Strings::replaceCommaValues($otherHouseHold2) + Strings::replaceCommaValues($capitalGains2)
                    + Strings::replaceCommaValues($partnership2);
            }
        } else {
            $primTotalGrossIncome = Strings::replaceCommaValues($grossIncome1)
                + Strings::replaceCommaValues($commissionOrBonus1)
                + Strings::replaceCommaValues($overtime1)
                + Strings::replaceCommaValues($tipsMiscIncome1) + Strings::replaceCommaValues($militaryIncome1);

            $primTotalHouseHoldIncome = Strings::replaceCommaValues($primTotalGrossIncome) + Strings::replaceCommaValues($socialSecurity1)
                + Strings::replaceCommaValues($pensionOrRetirement1) + Strings::replaceCommaValues($disability1)
                + Strings::replaceCommaValues($childSupportOrAlimony1) + Strings::replaceCommaValues($rental1)
                + Strings::replaceCommaValues($earnedInterest1) + Strings::replaceCommaValues($sonOrDaughter1)
                + Strings::replaceCommaValues($parents1) + Strings::replaceCommaValues($unemployment1)
                + Strings::replaceCommaValues($otherHouseHold1) + Strings::replaceCommaValues($roomRental1)
                + Strings::replaceCommaValues($secondJobIncome1) + Strings::replaceCommaValues($foodStampWelfare1);

            $primTotalNetHouseHoldIncome = Strings::replaceCommaValues($netMonthlyIncome1) + Strings::replaceCommaValues($netSocialSecurity1)
                + Strings::replaceCommaValues($netPensionOrRetirement1) + Strings::replaceCommaValues($netDisability1)
                + Strings::replaceCommaValues($netRental1) + Strings::replaceCommaValues($netEarnedInterest1)
                + Strings::replaceCommaValues($netUnemployment1) + Strings::replaceCommaValues($netRoomRental1)
                + Strings::replaceCommaValues($netSecondJobIncome1) + Strings::replaceCommaValues($sonOrDaughter1)
                + Strings::replaceCommaValues($parents1) + Strings::replaceCommaValues($childSupportOrAlimony1)
                + Strings::replaceCommaValues($otherHouseHold1) + Strings::replaceCommaValues($foodStampWelfare1)
                + Strings::replaceCommaValues($capitalGains1) + Strings::replaceCommaValues($partnership1);

            if ($LMRInfo->isCoBorrower == 1 || $isCoBorrower == 1) {

                $coTotalGrossIncome = Strings::replaceCommaValues($grossIncome2)
                    + Strings::replaceCommaValues($commissionOrBonus2)
                    + Strings::replaceCommaValues($overtime2)
                    + Strings::replaceCommaValues($tipsMiscIncome2);

                $coTotalHouseHoldIncome = Strings::replaceCommaValues($coTotalGrossIncome) + Strings::replaceCommaValues($socialSecurity2)
                    + Strings::replaceCommaValues($pensionOrRetirement2) + Strings::replaceCommaValues($disability2)
                    + Strings::replaceCommaValues($childSupportOrAlimony2) + Strings::replaceCommaValues($rental2)
                    + Strings::replaceCommaValues($earnedInterest2) + Strings::replaceCommaValues($sonOrDaughter2)
                    + Strings::replaceCommaValues($parents2) + Strings::replaceCommaValues($unemployment2)
                    + Strings::replaceCommaValues($otherHouseHold2) + Strings::replaceCommaValues($roomRental2)
                    + Strings::replaceCommaValues($secondJobIncome2) + Strings::replaceCommaValues($foodStampWelfare2);

                $coTotalNetHouseHoldIncome = Strings::replaceCommaValues($netMonthlyIncome2) + Strings::replaceCommaValues($netSocialSecurity2)
                    + Strings::replaceCommaValues($netPensionOrRetirement2) + Strings::replaceCommaValues($netDisability2)
                    + Strings::replaceCommaValues($netRental2) + Strings::replaceCommaValues($netEarnedInterest2)
                    + Strings::replaceCommaValues($netUnemployment2) + Strings::replaceCommaValues($netRoomRental2)
                    + Strings::replaceCommaValues($netSecondJobIncome2) + Strings::replaceCommaValues($sonOrDaughter2)
                    + Strings::replaceCommaValues($parents2) + Strings::replaceCommaValues($childSupportOrAlimony2)
                    + Strings::replaceCommaValues($otherHouseHold2) + Strings::replaceCommaValues($foodStampWelfare2)
                    + Strings::replaceCommaValues($capitalGains2) + Strings::replaceCommaValues($partnership2);
            }

        }  /* NOT HMLO module */
        /*** Calculate New gross monthly household income - You can use this variable in income info tab, LM Proposal tab ***/

// Debug($coTotalNetHouseHoldIncome);
        $totalGrossMonthlyHouseHoldIncome = $primTotalHouseHoldIncome + $coTotalHouseHoldIncome;
        $totalHouseHoldIncome = $primTotalNetHouseHoldIncome + $coTotalNetHouseHoldIncome;
        $primaryMortgage1 = Strings::replaceCommaValues($lien1Payment) + Strings::replaceCommaValues($lien2Payment);
        $primTotalHouseHoldExpenses = Strings::replaceCommaValues($primaryMortgage1) + Strings::replaceCommaValues($HOAFees1);
        if ($isHMLO == 1) {
            $primTotalHouseHoldExpenses += Strings::replaceCommaValues($HMLORealEstateTaxes) + Strings::replaceCommaValues($totalInsurance);
        } else {
            $primTotalHouseHoldExpenses += Strings::replaceCommaValues($incomeInfo->taxes1)
                + Strings::replaceCommaValues($incomeInfo->insurance1)
                + Strings::replaceCommaValues($incomeInfo->floodInsurance1);
        }
        $primTotalHouseHoldExpenses += Strings::replaceCommaValues($otherMortgage1)
            + Strings::replaceCommaValues($creditCards1)
            + Strings::replaceCommaValues($autoLoan1)
            + Strings::replaceCommaValues($childSupportOrAlimonyMonthly1)
            + Strings::replaceCommaValues($unsecuredLoans1)
            + Strings::replaceCommaValues($studentLoans1)
            + Strings::replaceCommaValues($careAmt1)
            + Strings::replaceCommaValues($allInsurance1)
            + Strings::replaceCommaValues($groceries1)
            + Strings::replaceCommaValues($carExpenses1)
            + Strings::replaceCommaValues($medicalBill1)
            + Strings::replaceCommaValues($entertainment1)
            + Strings::replaceCommaValues($other1)
            + Strings::replaceCommaValues($cable1)
            + Strings::replaceCommaValues($natural1)
            + Strings::replaceCommaValues($water1)
            + Strings::replaceCommaValues($internet1)
            + Strings::replaceCommaValues($utilityOther1)
            + Strings::replaceCommaValues($electricity1)
            + Strings::replaceCommaValues($primaryBorrowerPhone)
            + Strings::replaceCommaValues($incomeInfo->mortgageInsurance1)
            + Strings::replaceCommaValues($donation1)
            + Strings::replaceCommaValues($pets1)
            + Strings::replaceCommaValues($monthlyParking1)
            + Strings::replaceCommaValues($unionDues1)
            + Strings::replaceCommaValues($personalLoan1)
            + Strings::replaceCommaValues($dryCleaning1)
            + Strings::replaceCommaValues($lunchPurchased1)
            + Strings::replaceCommaValues($rentalExp1)
            + Strings::replaceCommaValues($expFedTax)
            + Strings::replaceCommaValues($expStateTax)
            + Strings::replaceCommaValues($expRentalPay)
            + Strings::replaceCommaValues($expMortgPayResi)
            + Strings::replaceCommaValues($expMortgPayInvest)
            + Strings::replaceCommaValues($expPropTaxResi)
            + Strings::replaceCommaValues($expPropTaxInvest)
            + Strings::replaceCommaValues($expLoanPayments)
            + Strings::replaceCommaValues($expIns)
            + Strings::replaceCommaValues($expInvestments)
            + Strings::replaceCommaValues($expTuition)
            + Strings::replaceCommaValues($expOtherLiving)
            + Strings::replaceCommaValues($expMedical);
        // + Strings::replaceCommaValues($expOther);

        if ($LMRInfo->isCoBorrower == 1 || $isCoBorrower == 1) {
            $coTotalHouseHoldExpenses = Strings::replaceCommaValues($otherMortgage2)
                + Strings::replaceCommaValues($creditCards2)
                + Strings::replaceCommaValues($autoLoan2)
                + Strings::replaceCommaValues($childSupportOrAlimonyMonthly2)
                + Strings::replaceCommaValues($unsecuredLoans2)
                + Strings::replaceCommaValues($studentLoans2)
                + Strings::replaceCommaValues($careAmt2)
                + Strings::replaceCommaValues($allInsurance2)
                + Strings::replaceCommaValues($groceries2)
                + Strings::replaceCommaValues($carExpenses2)
                + Strings::replaceCommaValues($medicalBill2)
                + Strings::replaceCommaValues($entertainment2)
                + Strings::replaceCommaValues($other2)
                + Strings::replaceCommaValues($cable2)
                + Strings::replaceCommaValues($natural2)
                + Strings::replaceCommaValues($water2)
                + Strings::replaceCommaValues($internet2)
                + Strings::replaceCommaValues($utilityOther2)
                + Strings::replaceCommaValues($electricity2)
                + Strings::replaceCommaValues($coBorrowerPhone)
                + Strings::replaceCommaValues($donation2)
                + Strings::replaceCommaValues($pets2)
                + Strings::replaceCommaValues($monthlyParking2)
                + Strings::replaceCommaValues($unionDues2)
                + Strings::replaceCommaValues($personalLoan2)
                + Strings::replaceCommaValues($dryCleaning2)
                + Strings::replaceCommaValues($lunchPurchased2)
                + Strings::replaceCommaValues($rentalExp2);
        }
        $totalHouseHoldExpenses = $primTotalHouseHoldExpenses + $coTotalHouseHoldExpenses;

        $totalDisposableIncome = $totalHouseHoldIncome - $totalHouseHoldExpenses;
        $totalHouseHoldIncome = round($totalHouseHoldIncome, 2);
        $totalHouseHoldExpenses = round($totalHouseHoldExpenses, 2);
        $totalDisposableIncome = round($totalDisposableIncome, 2);
        $primTotalGrossIncome = round($primTotalGrossIncome, 2);
        $coTotalGrossIncome = round($coTotalGrossIncome, 2);
        $primTotalNetHouseHoldIncome = round($primTotalNetHouseHoldIncome, 2);
        $coTotalNetHouseHoldIncome = round($coTotalNetHouseHoldIncome, 2);
        $primTotalHouseHoldExpenses = round($primTotalHouseHoldExpenses, 2);
        $coTotalHouseHoldExpenses = round($coTotalHouseHoldExpenses, 2);

        /**Desc: Asset Calculation for Quick App, Full App and BackOffice
         * Ref : 161184944
         * Date: Oct 23, 2018**/

        $networthOfBusinessOwned = 0;
        $otherAssets = $vestedInterest = $automobilesOwned3x = $automobilesOwned3x1 = '';
        if ($fileLOAssetsInfo) {
            $networthOfBusinessOwned = trim($fileLOAssetsInfo->networthOfBusinessOwned);
            $stocksBondsComNameNumberDesc = trim($fileLOAssetsInfo->stocksBondsComNameNumberDesc);
            $faceAmount = trim($fileLOAssetsInfo->faceAmount);
            $automobilesOwned3x = trim($fileLOAssetsInfo->automobilesOwned3x);
            $automobilesOwned3x1 = trim($fileLOAssetsInfo->automobilesOwned3x1);
            $otherAssets = trim($fileLOAssetsInfo->otherAssets);
            $vestedInterest = trim($fileLOAssetsInfo->vestedInterest);
        }

        if ($assetsInfo) {
            $assetId = trim($assetsInfo->assetID);
            $assetCheckingAccounts = trim($assetsInfo->assetCheckingAccounts);
            $assetSavingMoneyMarket = trim($assetsInfo->assetSavingMoneyMarket);
            $assetStocks = trim($assetsInfo->assetStocks);
            $assetStocksOwed = trim($assetsInfo->assetStocksOwed);
            $assetIRAAccounts = trim($assetsInfo->assetIRAAccounts);
            $assetIRAAccountsOwed = trim($assetsInfo->assetIRAAccountsOwed);
            $assetESPOAccounts = trim($assetsInfo->assetESPOAccounts);
            $assetESPOAccountsOwed = trim($assetsInfo->assetESPOAccountsOwed);
            $assetHome = trim($assetsInfo->assetHome);
            $assetORE = trim($assetsInfo->assetORE);
            $assetSR = trim($assetsInfo->assetSR);
            $assetCars = trim($assetsInfo->assetCars);
            $assetLifeInsurance = trim($assetsInfo->assetLifeInsurance);
            $assetLifeInsuranceOwed = trim($assetsInfo->assetLifeInsuranceOwed);
            $assetTotalCashBankAcc = trim($assetsInfo->assetTotalCashBankAcc);
            $assetTotalRetirementValue = trim($assetsInfo->assetTotalRetirementValue);
            $assetAvailabilityLinesCredit = trim($assetsInfo->assetAvailabilityLinesCredit);
            $assetAvailabilityLinesCreditOwed = trim($assetsInfo->assetAvailabilityLinesCreditOwed);
            $assetOther = trim($assetsInfo->assetOther);
            $assetCash = trim($assetsInfo->assetCash);
            $assetHomeOwed = trim($assetsInfo->assetHomeOwed);
            $assetOREOwed = trim($assetsInfo->assetOREOwed);
            $assetSROwed = trim($assetsInfo->assetSROwed);
            $assetCarsOwed = trim($assetsInfo->assetCarsOwed);
            $otherAmtOwed = trim($assetsInfo->otherAmtOwed);
            $assetAccount = trim($assetsInfo->assetAccount);
            $assetAccountOwd = trim($assetsInfo->assetAccountOwd);
            $assetNonMarketableSecurities = trim($assetsInfo->assetNonMarketableSecurities);
            $assetNonMarketableSecuritiesOwd = trim($assetsInfo->assetNonMarketableSecuritiesOwd);
            $notesPayableToBanksOthersOwed = trim($assetsInfo->notesPayableToBanksOthersOwed);
            $installmentAccountOwed = trim($assetsInfo->installmentAccountOwed);
            $revolvingDebtOwed = trim($assetsInfo->revolvingDebtOwed);
            $unpaidPayableTaxesOwed = trim($assetsInfo->unpaidPayableTaxesOwed);
            $otherLiabilitiesOwed = trim($assetsInfo->otherLiabilitiesOwed);
            $assetSecNotesOwd = trim($assetsInfo->assetSecNotesOwd);
            $assetUnsecNotesOwd = trim($assetsInfo->assetUnsecNotesOwd);
            $assetAcctPayableOwd = trim($assetsInfo->assetAcctPayableOwd);
            $assetMarginOwd = trim($assetsInfo->assetMarginOwd);
            $networthOfBusinessOwned = trim($assetsInfo->networthOfBusinessOwned);
            $otherAssets = trim($assetsInfo->otherAssets);
            $otherDescription = trim($assetsInfo->otherDesc);
            $otherLiabilityDetails = trim($assetsInfo->otherLiabilityDetails);
            $unpaidPayableTaxesDesc = trim($assetsInfo->unpaidPayableTaxesDesc);
        }
        $totalAssets = Strings::replaceCommaValues($assetCheckingAccounts) + Strings::replaceCommaValues($assetSavingMoneyMarket);
        $totalAssets += Strings::replaceCommaValues($assetStocks) + Strings::replaceCommaValues($assetIRAAccounts);
        $totalAssets += Strings::replaceCommaValues($assetESPOAccounts) + Strings::replaceCommaValues($assetHome);
        $totalAssets += Strings::replaceCommaValues($assetORE) + Strings::replaceCommaValues($assetSR) + Strings::replaceCommaValues($assetCars);
        $totalAssets += Strings::replaceCommaValues($assetLifeInsurance) + Strings::replaceCommaValues($assetOther);
        $totalAssets += Strings::replaceCommaValues($assetTotalCashBankAcc) + Strings::replaceCommaValues($assetTotalRetirementValue) + Strings::replaceCommaValues($assetAvailabilityLinesCredit);
        $totalAssets += Strings::replaceCommaValues($assetCash) + Strings::replaceCommaValues($networthOfBusinessOwned) + Strings::replaceCommaValues($otherAssets) + Strings::replaceCommaValues($automobilesOwned3x);
        $totalAssets += Strings::replaceCommaValues($assetAccount) + Strings::replaceCommaValues($assetNonMarketableSecurities);
        $totalAssets = round($totalAssets, 2);
        $totalAssetsOwed = 0;
        $totalAssetsOwed += Strings::replaceCommaValues($assetStocksOwed) + Strings::replaceCommaValues($assetHomeOwed);
        $totalAssetsOwed += Strings::replaceCommaValues($assetOREOwed) + Strings::replaceCommaValues($assetSROwed) + Strings::replaceCommaValues($assetCarsOwed) + Strings::replaceCommaValues($otherAmtOwed) + Strings::replaceCommaValues($automobilesOwned3x1);
        $totalAssetsOwed += Strings::replaceCommaValues($assetAccountOwd) + Strings::replaceCommaValues($assetNonMarketableSecuritiesOwd);
        $totalAssetsOwed += Strings::replaceCommaValues($assetIRAAccountsOwed) + Strings::replaceCommaValues($assetESPOAccountsOwed) + Strings::replaceCommaValues($assetLifeInsuranceOwed) + Strings::replaceCommaValues($assetAvailabilityLinesCreditOwed);
        $totalAssetsOwed += Strings::replaceCommaValues($notesPayableToBanksOthersOwed) + Strings::replaceCommaValues($installmentAccountOwed) + Strings::replaceCommaValues($revolvingDebtOwed) + Strings::replaceCommaValues($unpaidPayableTaxesOwed) + Strings::replaceCommaValues($otherLiabilitiesOwed) + Strings::replaceCommaValues($assetSecNotesOwd) + Strings::replaceCommaValues($assetUnsecNotesOwd) + Strings::replaceCommaValues($assetAcctPayableOwd) + Strings::replaceCommaValues($assetMarginOwd);
        $totalAssetsOwed = round($totalAssetsOwed, 2);
        $totalAssetsNetValue = Strings::replaceCommaValues($totalAssets) - Strings::replaceCommaValues($totalAssetsOwed);
        $totalAutoMobiles = Strings::replaceCommaValues($automobilesOwned3x1) + Strings::replaceCommaValues($assetCars) + Strings::replaceCommaValues($assetCarsOwed);
        $totalAssetsNetValue = round($totalAssetsNetValue, 2);
        $totalRealEstateValue = Strings::replaceCommaValues($assetHome) +
            Strings::replaceCommaValues($assetORE) + Strings::replaceCommaValues($assetSR);
        $totalRealEstateValue = round($totalRealEstateValue, 2);


        $fileHMLONewLoanInfoCashFlow = LMRequest::myFileInfo()->fileHMLONewLoanInfo();
        $LMRInfoCashFlow = LMRequest::myFileInfo()->LMRInfo();
        $FilePropInfoCashFlow = LMRequest::myFileInfo()->FilePropInfo();

        if ($fileHMLONewLoanInfoCashFlow) {
            $actualRentsInPlace = $fileHMLONewLoanInfoCashFlow->actualRentsInPlace;
            $vacancyFactor = $fileHMLONewLoanInfoCashFlow->vacancyFactor;
            $reserveFactoron = $fileHMLONewLoanInfoCashFlow->reserveFactoron;
            $reserveFactor = $fileHMLONewLoanInfoCashFlow->reserveFactor;

            if (!$reserveFactor || $reserveFactor == 0.00) {
                $reserveFactor = 0;
            }
            $lessActualExpenses = $fileHMLONewLoanInfoCashFlow->lessActualExpenses;
            $grossAnnualRentLargestTenant = $fileHMLONewLoanInfoCashFlow->grossAnnualRentLargestTenant;

            $actualRentsInPlaceCommercial = $fileHMLONewLoanInfoCashFlow->actualRentsInPlaceCommercial;
            $vacancyFactorCommercial = $fileHMLONewLoanInfoCashFlow->vacancyFactorCommercial;


            $waterSewer = $fileHMLONewLoanInfoCashFlow->waterSewer;
            $electricity = $fileHMLONewLoanInfoCashFlow->electricity;
            $gas = $fileHMLONewLoanInfoCashFlow->gas;
            $repairsMaintenance = $fileHMLONewLoanInfoCashFlow->repairsMaintenance;
            $legal = $fileHMLONewLoanInfoCashFlow->legal;
            $payroll = $fileHMLONewLoanInfoCashFlow->payroll;
            $misc = $fileHMLONewLoanInfoCashFlow->misc;
            $commonAreaUtilities = $fileHMLONewLoanInfoCashFlow->commonAreaUtilities;
            $elevatorMaintenance = $fileHMLONewLoanInfoCashFlow->elevatorMaintenance;
            $replacementReserves = $fileHMLONewLoanInfoCashFlow->replacementReserves;
            $other = $fileHMLONewLoanInfoCashFlow->other;
            $tenantReimursements = $fileHMLONewLoanInfoCashFlow->tenantReimursements;
            $managementExpense = $fileHMLONewLoanInfoCashFlow->managementExpense;
            $managementExpensePercentage = $fileHMLONewLoanInfoCashFlow->managementExpensePercentage;
            $actualRentsInPlaceCheckbox = $fileHMLONewLoanInfoCashFlow->actualRentsInPlaceCheckbox;
            $actualRentsInPlaceCommercialCheckbox = $fileHMLONewLoanInfoCashFlow->actualRentsInPlaceCommercialCheckbox;

            $tenantContribution = $fileHMLONewLoanInfoCashFlow->tenantContribution;
            $tenantContributionVacancyRate = $fileHMLONewLoanInfoCashFlow->tenantContributionVacancyRate;
            $otherIncome = $fileHMLONewLoanInfoCashFlow->otherIncome;
            $otherIncomeVacancyRate = $fileHMLONewLoanInfoCashFlow->otherIncomeVacancyRate;


            $spcf_taxes1_cal = Strings::replaceCommaValues($taxes1);
            $spcf_annualPremium_cal = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->annualPremium;
            $spcf_hoafees_cal = $fileHMLONewLoanInfoCashFlow->spcf_hoafees;
        }

        $homeValue = $LMRInfoCashFlow->homeValue;

        if ($reserveFactoron == 1) {
            $reserveValue = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyRentableSqFt;
            if ($reserveValue == '') $reserveValue = 0;
        } else if ($reserveFactoron == 2) {
            $reserveValue = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits;
        }
        $homeValueForCashFlow = Strings::replaceCommaValues($LMRInfoCashFlow->homeValue);
        $addRentableSqFtForCashFlow = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyRentableSqFt;
        $noUnitsOccupiedForCashFlow = LMRequest::File()->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits;
        $typeOfHMLOLoanRequestingCahFlow = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting;
        $costBasisCashFlow = LMRequest::myFileInfo()->listingRealtorInfo()->costBasis;

        $vacancy = ($vacancyFactor * $actualRentsInPlace) / 100.0;
        $vacancyCommercial = ($vacancyFactorCommercial * $actualRentsInPlaceCommercial) / 100.0;
        $vacancyOtherIncome = ($otherIncome * $otherIncomeVacancyRate) / 100.0;
        $vacancyTenantContribution = ($tenantContribution * $tenantContributionVacancyRate) / 100.0;

        $effectiveGrossIncome = calEffectiveGrossIncome::getEffectiveGrossIncome($actualRentsInPlace,
            $actualRentsInPlaceCommercial,
            $otherIncome,
            $tenantContribution,
            $vacancy,
            $vacancyCommercial,
            $vacancyOtherIncome,
            $vacancyTenantContribution
        );


        $netOperatingIncome = proposalFormula::calculateNetOperatingIncome($actualRentsInPlace,
            $actualRentsInPlaceCommercial,
            $tenantContribution,
            $otherIncome,
            $vacancy,
            $vacancyCommercial,
            $vacancyOtherIncome,
            $vacancyTenantContribution,
            $lessActualExpenses,
            $waterSewer,
            $electricity,
            $gas,
            $repairsMaintenance,
            $legal,
            $payroll,
            $misc,
            $commonAreaUtilities,
            $elevatorMaintenance,
            $replacementReserves,
            $other,
            $tenantReimursements,
            $managementExpense,
            $spcf_taxes1_cal,
            $spcf_annualPremium_cal,
            $spcf_hoafees_cal);

        $reserves = floatval($reserveFactor) * floatval($reserveValue);

        $netOperatingIncome = Strings::replaceCommaValues($netOperatingIncome);

        $serviceDebt = $netOperatingIncome - Strings::replaceCommaValues($reserves);


        if ($netOperatingIncome > 0
            && $homeValueForCashFlow > 0
            && ($typeOfHMLOLoanRequestingCahFlow == glTypeOfHMLOLoanRequestingCahFlow::CONST_CF_Commercial_Cash_Out_Refinance
                || $typeOfHMLOLoanRequestingCahFlow == glTypeOfHMLOLoanRequestingCahFlow::CONST_CF_Commercial_Rate_Term_Refinance)
        ) {
            $capRate = ($netOperatingIncome / $homeValueForCashFlow) * 100.0;
            $capRateToolTip = 'Cap rate = (Net Operating Income / As-Is value) *100';
        }

        if ($netOperatingIncome > 0
            && $costBasisCashFlow > 0
            && ($typeOfHMLOLoanRequestingCahFlow == glTypeOfHMLOLoanRequestingCahFlow::CONST_CF_Commercial_Purchase)) {
            $capRate = ($netOperatingIncome / $costBasisCashFlow) * 100.0;
            $capRateToolTip = 'Cap Rate = (Net Operating Income / Purchase Price) * 100';
        }

        if (!$capRateToolTip) {
            $capRateToolTip = 'Cap rate = (Net Operating Income / As-Is value) * 100 
    [if transaction type=Commercial rate/term or Commercial Cash out refi]  
    If Transaction type= Commercial Purchase (Cap Rate= (Net Operating Income / Purchase Price) *100)';
        }

        $debtServiceRatio = proposalFormula::calculateDebtServiceRatio(HMLOLoanTermsCalculation::$totalMonthlyPayment, $netOperatingIncome);

        $glDefaultBrokerInHMLOPC = glDefaultBrokerInHMLOPC::$glDefaultBrokerInHMLOPC;




        if (trim($borCreditScoreRange) == '720') $borCreditScoreRange = '720 +';
        if ($borNoOfOwnProp) $haveBorOwnInvestmentProperties = 'Yes';

        $REBroker = 'No';

        $totalAssets = Strings::replaceCommaValues($assetCheckingAccounts) + Strings::replaceCommaValues($assetSavingMoneyMarket);
        $totalAssets += Strings::replaceCommaValues($assetStocks) + Strings::replaceCommaValues($assetIRAAccounts);
        $totalAssets += Strings::replaceCommaValues($assetESPOAccounts) + Strings::replaceCommaValues($assetHome);
        $totalAssets += Strings::replaceCommaValues($assetORE) + Strings::replaceCommaValues($assetSR) + Strings::replaceCommaValues($assetCars);
        $totalAssets += Strings::replaceCommaValues($assetLifeInsurance) + Strings::replaceCommaValues($assetOther);
        $totalAssets += Strings::replaceCommaValues($assetTotalCashBankAcc) + Strings::replaceCommaValues($assetTotalRetirementValue) + Strings::replaceCommaValues($assetAvailabilityLinesCredit);
        $totalAssets += Strings::replaceCommaValues($assetCash) + Strings::replaceCommaValues($networthOfBusinessOwned) + Strings::replaceCommaValues($otherAssets) + Strings::replaceCommaValues($automobilesOwned3x);
        $totalAssets = round($totalAssets, 2);
        $totalAssetsOwed += Strings::replaceCommaValues($assetHomeOwed);
        $totalAssetsOwed += Strings::replaceCommaValues($assetOREOwed) + Strings::replaceCommaValues($assetSROwed) + Strings::replaceCommaValues($assetCarsOwed) + Strings::replaceCommaValues($otherAmtOwed) + Strings::replaceCommaValues($automobilesOwned3x1);

        $totalAssetsOwed = round($totalAssetsOwed, 2);

        $totalAssetsNetValue = Strings::replaceCommaValues($totalAssets) - Strings::replaceCommaValues($totalAssetsOwed);

        $totalAutoMobiles = Strings::replaceCommaValues($automobilesOwned3x1) + Strings::replaceCommaValues($assetCars) + Strings::replaceCommaValues($assetCarsOwed);
        $totalAssetsNetValue = round($totalAssetsNetValue, 2);


        /** Loan Info Section **/

        if ($lien1Terms == '') {
            if (!in_array(LMRequest::$PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                $lien1Terms = LoanTerms::INTEREST_ONLY;
            }
        }
        if ($prePaymentPenalty == '') $prePaymentPenalty = 'None';
        if ($lienPosition == '') $lienPosition = '1';
    }
}