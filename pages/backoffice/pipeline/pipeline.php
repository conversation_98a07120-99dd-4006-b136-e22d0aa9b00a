<?php

namespace pages\backoffice\pipeline;

use models\cypher;
use models\lendingwise\tblCustomField;
use models\lendingwise\tblSavedView;
use models\PageVariables;
use models\Pagination;
use models\portals\BackofficePage;
use models\portals\Breadcrumb;
use models\Session;
use models\standard\HTTP;
use models\types\simpleExcel;
use models\types\simpleExcel_Column;
use pages\backoffice\pipeline\classes\FieldCombined;
use pages\backoffice\pipeline\classes\FileData;
use pages\backoffice\pipeline\classes\MasterValues;
use pages\backoffice\pipeline\classes\SavedViews;
use pages\backoffice\pipeline\classes\Search;
use pages\backoffice\pipeline\classes\SearchForm;
use stdClass;

class pipeline extends BackofficePage
{
    /**
     * @var FieldCombined[][]
     */
    public static ?array $CustomFields = null;

    /**
     * @var FieldCombined[]
     */
    public static ?array $CustomFieldsByHash = null;

    public static ?array $masterValues = null;

    public static ?array $Search = null;

    public static ?array $LMRIDs = null;

    public static ?array $ShowFields = null;

    /**
     * @var FileData[]
     */
    public static ?array $Loans = null;

    public static ?int $pageNumber = null;

    public static ?int $Records = null;
    public static ?int $PerPage = null;
    public static ?int $PageNumber = null;

    public static ?string $Pagination = null;

    public static ?SavedViews $SavedViews = null;

    public static function Init()
    {
        parent::Init();

        self::setMasterPage(MASTERPAGE_DEFAULT);

        Breadcrumb::$title = 'Pipeline';
        Breadcrumb::$icon = 'fas fa-home icon-md';
        Breadcrumb::$breadcrumbList = [];
        Breadcrumb::$toolbarHTML = '';

    }

    public static function Get()
    {
        if ($_REQUEST['reset'] ?? null) {
            Session::Clear('PipelineSearch');
            Session::Clear('PipelineLMRIDs');
            HTTP::Redirect('/backoffice/pipeline');
        }

        self::$CustomFields = FieldCombined::getReport(PageVariables::$PCID);
        self::$CustomFieldsByHash = [];
        foreach(self::$CustomFields as $section => $items) {
            foreach($items as $item) {
                self::$CustomFieldsByHash[$item->fieldHash] = $item;
            }
        }

        self::$Search = Session::Get('PipelineSearch');
        SearchForm::Init(
            self::$Search,
            PageVariables::$userRole,
            PageVariables::$userGroup,
            PageVariables::$userNumber,
            PageVariables::$PCID,
            PageVariables::$externalBroker,
            PageVariables::$userSeeBilling,
            PageVariables::$allowCFPBAuditing
        );

        self::$PerPage = SearchForm::$SearchFields->noOfRecordsPerPage ?: 25;
        self::$pageNumber = ($_REQUEST['page'] ?? 1) - 1;

        if(!self::$Search || Session::Get('PipelinePage') != self::$pageNumber) {
            Session::Set('PipelineLMRIDs', Search::getReport(
                PageVariables::$PCID,
                SearchForm::$SearchFields,
                self::$PerPage,
                self::$PerPage * self::$pageNumber
            ));

            Session::Set('PipelineCount', Search::getCount(
                PageVariables::$PCID,
                SearchForm::$SearchFields
            ));

            Session::Set('PipelinePage', self::$pageNumber);
        }

        self::$Records = Session::Get('PipelineCount');
        self::$LMRIDs = Session::Get('PipelineLMRIDs');
        self::$ShowFields = Session::Get('PipelineShowFields');

        if (
            !isset(self::$ShowFields['CustomField'])
            || !is_array(self::$ShowFields['CustomField'])
        ) {
            self::$ShowFields['CustomField'] = [];
        }

        self::$Loans = FileData::getReport(
            PageVariables::$PCID,
            self::$LMRIDs,
            self::$ShowFields['CustomField']
        );



        self::$Pagination = Pagination::Render(
            pipeline::$pageNumber,
            pipeline::$Records,
            pipeline::$PerPage,
            5
        );

        self::$masterValues = MasterValues::getReport(
            self::$LMRIDs,
            self::$CustomFieldsByHash,
            self::$ShowFields['CustomField']
        );

        self::$SavedViews = SavedViews::getReport(
            PageVariables::$userNumber,
            PageVariables::$userGroup
        );
    }

    public static function Post()
    {
        switch ($_REQUEST['a']) {
            case 'loadPipelineSearch':
                $id = cypher::myDecryption($_REQUEST['loadPipelineSearch']);
                $view = tblSavedView::Get([
                    'id' => $id,
                    'userNumber' => PageVariables::$userNumber,
                    'userGroup' => PageVariables::$userGroup,
                ]);
                $_REQUEST = json_decode($view->postVars, true);
                Session::Set('PipelineSearch', $_REQUEST);

                Session::Set('PipelineSavedSearch', $view->name);

                self::Get();

                Session::Set('PipelineCount', Search::getCount(
                    PageVariables::$PCID,
                    SearchForm::$SearchFields
                ));

                Session::Set('PipelineLMRIDs', Search::getReport(
                    PageVariables::$PCID,
                    SearchForm::$SearchFields
                ));
                break;

            case 'search':
                Session::Set('PipelineSearch', $_REQUEST);

                self::Get();

                Session::Set('PipelineCount', Search::getCount(
                    PageVariables::$PCID,
                    SearchForm::$SearchFields
                ));

                Session::Set('PipelineLMRIDs', Search::getReport(
                    PageVariables::$PCID,
                    SearchForm::$SearchFields
                ));
                break;

            case 'showFields':
                Session::Set('PipelineShowFields', $_REQUEST);
                break;

            case 'exportXLS':
                $LMRIds = Search::getReport(
                    PageVariables::$PCID,
                    SearchForm::$SearchFields,
                    10000
                );
                self::export($LMRIds);

        }

        HTTP::ReloadPage();
    }

    public static function customField(string $id): ?FieldCombined
    {
        return self::$CustomFieldsByHash[$id] ?? null;
    }

    public static function customFieldValue(FileData $loan, string $id, bool $excel = false)
    {
        $field = self::customField($id);
        if (!$field->isCustom) {
            return self::$masterValues[$field->tableName][$loan->LMRId][$field->fieldName] ?? '';
        }

        $item = tblCustomField::getCached($field->fieldID);
        $cf = $loan->customField($field->fieldID);
        return $cf ? $cf->render($item, $excel) : '';
    }

    public static function export(?array $LMRIDs)
    {
        if (!$LMRIDs || !sizeof($LMRIDs)) {
            return;
        }

        self::$CustomFields = FieldCombined::getReport(PageVariables::$PCID);
        self::$CustomFieldsByHash = [];
        foreach(self::$CustomFields as $section => $items) {
            foreach($items as $item) {
                self::$CustomFieldsByHash[$item->fieldHash] = $item;
            }
        }

        self::$masterValues = MasterValues::getReport(
            $LMRIDs,
            self::$CustomFieldsByHash,
            $_REQUEST['CustomField']
        );

        $custom_fields = [];
        foreach($_REQUEST['CustomField'] ?? [] as $id) {
            if(stristr($id, 'master') !== false) {
                continue;
            }

            $parts = explode('_', $id);
            $custom_fields[] = $parts[1];
        }


        $Loans = FileData::getReport(
            PageVariables::$PCID,
            $LMRIDs,
            $custom_fields
        );

        $se = new simpleExcel();
        $se->Title = 'Pipeline Report';
        $se->Filename = 'PipelineReport.xlsx';
        $se->Columns = [];
        $se->Report = [];
        $se->Columns[] = new simpleExcel_Column('File ID', 'LMRId');
        $se->Columns[] = new simpleExcel_Column('Last Updated', 'lastUpdatedDate');
        foreach ($_REQUEST['CustomField'] ?? [] as $id) {
            $se->Columns[] = new simpleExcel_Column(self::customField($id)->fieldLabel, 'custom_value_' . $id);
        }


        foreach ($Loans as $item) {
            $record = new stdClass();
            $record->LMRId = $item->LMRId;
            $record->lastUpdatedDate = $item->lastUpdatedDate;

            foreach ($_REQUEST['CustomField'] ?? [] as $id) {
                $record->{'custom_value_' . $id} = self::customFieldValue($item, $id, true);
            }

            $se->Report[] = $record;
        }

        simpleExcel::ExportSpreadsheet($se);
    }
}
