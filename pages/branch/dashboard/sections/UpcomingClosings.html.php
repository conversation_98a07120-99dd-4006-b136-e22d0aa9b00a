<?php
namespace pages\branch\dashboard;

use models\Controllers\loanForm;
use models\cypher;
use models\PageVariables;
use models\standard\Strings;
use pages\backoffice\dashboard\classes\UpcomingClosingsReport;
use pages\backoffice\dashboard\classes\UpcomingClosingsRow;

?>

<?php
$headerColumnCount = 8;
if (count(UpcomingClosingsReport::$Report) > 0) {
    echo loanForm::simpleHidden(
        'allowToViewAutomationPopup',
        PageVariables::User()->allowToViewAutomationPopup
    );
    echo loanForm::simpleHidden(
        'userAutomationControlAccess',
        PageVariables::User()->allowToControlAutomationPopup ?? 0
    );
    ?>
    <div class="table-responsive-lg">
        <table id="upcomingclosingTableId" class="table table-hover table-head-custom table-head-bg table-vertical-center dataTable">
            <thead class="thead-light">
            <tr class="bg-gray-100 text-left">
                <th><span class="text-dark-75"> Borrower</span></th>
                <th><span class="text-dark-75"> Loan Amount</span></th>
                <th><span class="text-dark-75"> Initial Loan Amount</span></th>
                <th><span class="text-dark-75"> File Status</span></th>
                <th><span class="text-dark-75"> Target Closing Date</span></th>
                <th><span class="text-dark-75"> Actual Closing Date
                    <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true" data-toggle="tooltip" data-placement="top"
                       data-original-title="Any loan with a future date for actual closing date will display here."></i></span>
                </th>
                <?php if (count(UpcomingClosingsReport::$modulesInfoArray) == 1 && UpcomingClosingsReport::$modulesInfoArray[0]->moduleCode == 'loc') {
                    //do nothing
                } else {
                    $headerColumnCount = 9;
                    ?>
                    <th><span class="text-dark-75"> Transaction Type</span></th>
                <?php } ?>
                <th><span class="text-dark-75"> Loan Program</span></th>
                <th><span class="text-dark-75"> Action(s)</span></th>
            </tr>
            </thead>
            <tbody>
            <?php
            foreach (UpcomingClosingsReport::$Report as $h => $item) {
                $row = UpcomingClosingsRow::fromUpcomingClosingsDTO($item);
                ?>
                <tr class="<?php echo ' tr_bgColor_' . cypher::myEncryption($row->LMRId); ?> "
                    id="row-<?php echo $h ?>">
                    <td class="accordion-headerClick" id="td-folder-<?php echo $h ?>"
                        data-fileidlmr="<?php echo cypher::myEncryption($row->LMRId) ?>">
                        <div id="op-row-<?php echo $h ?>"> </div>
                        <div class="d-flex align-items-center mb-0">
                            <!--begin::Symbol-->
                            <div class="symbol symbol-40 symbol-light-warning mr-5 ml-5">
                                <a class="accordion-header folderIconClass accorder-folder text-danger with-children-tip"
                                   id="<?php echo cypher::myEncryption($row->LMRId) ?>"><i
                                        class="far fa-folder  icon-2x text-success  mar_for_icon"
                                        title="Click to Show/ Hide more info"
                                        id="<?php echo 'Folder_Icon_' . cypher::myEncryption($row->LMRId) ?>"></i></a>
                            </div>
                            <div class="d-flex flex-column">
                                <?php echo $row->borrowerLink; ?>
                                <?php echo $row->infoAddressCreateDate; ?>
                            </div>
                        </div>
                    </td>
                    <td class="p-5"><?php echo Strings::Currency($row->loanAmt); ?></td>
                    <td class="p-5"><?php echo Strings::Currency($row->initialloanAmount); ?></td>
                    <td class="p-2"><span class="text-break"><?php echo $row->primaryStatus; ?></span></td>
                    <td class="p-2"><?php echo $row->targetClosingDate; ?></td>
                    <td class="p-2"><?php echo $row->closedDate; ?></td>
                    <?php if (count(UpcomingClosingsReport::$modulesInfoArray) == 1 && UpcomingClosingsReport::$modulesInfoArray[0]->moduleCode == 'loc') {
                    } else { ?>
                        <td class="p-2">
                            <?php if ($row->typeOfHMLOLoanRequesting != '') { ?>
                                <?php echo $row->typeOfHMLOLoanRequesting; ?>
                            <?php } ?>
                        </td>
                    <?php } ?>
                    <td class="p-5">
                        <?php echo $row->serviceType; ?>
                    </td>
                    <td class="p-2">
                        <?php


                        if ((PageVariables::$viewPrivateNotes == 1 || PageVariables::$viewPublicNotes == 1) && $item->CommentCount) { ?>

                            <span id="divListNotes<?php echo $row->LMRId ?>">
                <span id="status-bar">
                    <span class="status-infos">
                        <span class="lireplace">
                            <?php echo $row->processorCommentHeaderButton; ?>
                            <div class="float-left result-block card-body p-0" data-slide-to="left"><span
                                    class="arrow"></span>
                                <?php echo $row->processorCommentsNotesFinal; ?>
                            </div>
                        </span>
                    </span>
                </span>
            </span>
                        <?php } else { ?>
                            <span data-toggle="tooltip" data-html="true" title="Click to Add notes"
                                  id="divListNotes<?php echo $row->LMRId ?>">
                                        <?php echo $row->processorCommentHeaderButton; ?>
                                        </span>
                        <?php } ?>


                        <span data-toggle="tooltip" data-html="true"
                              title="<?php echo $row->assignedStaffText; ?>"> <a
                                href="#"
                                class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1"
                                data-href="<?php echo CONST_URL_POPS; ?>assignEmployee.php"
                                data-name=" <?php echo htmlentities($row->clientName) ?> > Assign Employee(s)"
                                data-id="LMRId=<?php echo cypher::myEncryption($row->LMRId) ?>&amp;PCID=<?php echo cypher::myEncryption(PageVariables::$PCID) ?>&amp;BRID=<?php echo cypher::myEncryption($row->LMRExecutiveId) ?>"
                                data-toggle="modal" data-target="#exampleModal1">
                                    <i class="fas fa-users-cog"></i>
                                </a></span>

                        <span id="divListAssignEmp" style="display: none;"></span>

                        <span data-toggle="tooltip" data-html="true"
                              title="<?php echo $row->assignBranchAgentText; ?>">
                                    <a href="#"
                                       class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1"
                                       data-name="File: <?php echo htmlentities($row->clientName) ?> > Assign/View Loan Officer/Broker and Branch"
                                       <?php if (PageVariables::$userRole == 'REST'){
                                       }else { ?>data-href="<?php echo CONST_URL_POPS; ?>assignBrAg.php"
                                       data-id='LMRId=<?php echo cypher::myEncryption($row->LMRId) ?>&brokerNumber=<?php echo cypher::myEncryption($row->brokerNumber) ?>&LMRExecutiveId=<?php echo cypher::myEncryption($row->LMRExecutiveId) ?>&PCID=<?php echo cypher::myEncryption(PageVariables::$PCID) ?>&tabOpt=<?php echo cypher::myEncryption('Dash') ?>' <?php } ?>
                                   data-toggle="modal" data-target="#exampleModal1"> <i class="fas fa-user-cog"></i>
                                </a></span>
                    </td>
                </tr>
                <?php
            }
            ?>
            </tbody>
            <tfoot>
            <tr>
                <?php for ($tf = 0; $tf < $headerColumnCount; $tf++ ){
                    echo "<td></td>";
                } ?>
            </tr>
            </tfoot>
        </table>
    </div>
    <?php
} else { ?>
    <h4 class="display-5 text-center py-5"> No Loans have a closing date set</h4>
<?php }
?>
