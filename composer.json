{"name": "lendingwise/lendingwise", "description": "Lend Smart", "type": "project", "license": "Copyright 2019 Lend Smart Inc", "scripts": {"start": "xdg-open http://local.lendingwise.com && php -S local.lendingwise.com -t public", "open": "xdg-open http://local.lendingwise.com", "test": "phpunit"}, "require": {"php": "^7.4 || ^8.0 || ^8.3", "ext-bcmath": "*", "ext-mcrypt": "*", "ext-curl": "*", "ext-json": "*", "ext-openssl": "*", "ext-dba": "*", "ext-gd": "*", "ext-zip": "*", "ext-calendar": "*", "ext-simplexml": "*", "ext-libxml": "*", "ext-mysqli": "*", "ext-soap": "*", "ext-iconv": "*", "ext-intl": "*", "ext-fileinfo": "*", "ext-zlib": "*", "ext-exif": "*", "mjphaynes/php-resque": "dev-master#b6e703e", "monolog/monolog": "^2.5", "sendgrid/sendgrid": "~7", "sunra/dbug": "^0.2.1", "bugsnag/bugsnag": "^3.0", "guzzlehttp/guzzle": "~6.0", "vlucas/phpdotenv": "^4.1", "twilio/sdk": "^6.26", "firebase/php-jwt": "^6.0", "larapack/dd": "1.*", "tecnickcom/tcpdf": "^6.4", "econea/nusoap": "^0.9.11", "setasign/fpdi": "^2.3", "setasign/fpdf": "^1.8", "zircote/swagger-php": "^4.4", "phpoffice/phpspreadsheet": "^1.25.2", "cboden/ratchet": "^0.4.4", "phpseclib/phpseclib": "^3.0", "pdfcrowd/pdfcrowd": "^5.8", "chargebee/chargebee-php": "*", "phpseclib/mcrypt_compat": "^2.0", "symfony/error-handler": "^5.4"}, "autoload": {"psr-4": {"Automation\\": "Resque/Job/", "models\\": "models/", "tasks\\": "tasks/"}}, "autoload-dev": {"psr-4": {"tests\\": "tests/"}}, "require-dev": {"phpunit/phpunit": "^9.6"}}