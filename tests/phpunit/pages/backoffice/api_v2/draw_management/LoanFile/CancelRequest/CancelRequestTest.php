<?php

namespace tests\phpunit\pages\backoffice\api_v2\draw_management\LoanFile\CancelRequest;

use PHPUnit\Framework\TestCase;
use pages\backoffice\api_v2\draw_management\LoanFile\CancelRequest\CancelRequest;

/**
 * Tests the CancelRequest API endpoint.
 *
 * @covers \pages\backoffice\api_v2\draw_management\LoanFile\CancelRequest\CancelRequest
 */
class CancelRequestTest extends TestCase
{
    /**
     * Test that the CancelRequest class exists and extends the correct base class.
     */
    public function testClassExists(): void
    {
        $this->assertTrue(class_exists(CancelRequest::class));
        $this->assertTrue(is_subclass_of(CancelRequest::class, 'pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase'));
    }

    /**
     * Test that the Post method exists.
     */
    public function testPostMethodExists(): void
    {
        $this->assertTrue(method_exists(CancelRequest::class, 'Post'));
    }

    /**
     * Test that the getLMRIdFromDrawId method exists and is private.
     */
    public function testGetLMRIdFromDrawIdMethodExists(): void
    {
        $reflection = new \ReflectionClass(CancelRequest::class);
        $this->assertTrue($reflection->hasMethod('getLMRIdFromDrawId'));
        
        $method = $reflection->getMethod('getLMRIdFromDrawId');
        $this->assertTrue($method->isPrivate());
        $this->assertTrue($method->isStatic());
    }

    /**
     * Test getLMRIdFromDrawId with numeric drawId.
     */
    public function testGetLMRIdFromDrawIdWithNumericId(): void
    {
        $reflection = new \ReflectionClass(CancelRequest::class);
        $method = $reflection->getMethod('getLMRIdFromDrawId');
        $method->setAccessible(true);

        // Test with numeric ID - should return null since no actual database record exists
        $result = $method->invoke(null, 123);
        $this->assertNull($result);
    }

    /**
     * Test getLMRIdFromDrawId with invalid encrypted drawId.
     */
    public function testGetLMRIdFromDrawIdWithInvalidEncryptedId(): void
    {
        $reflection = new \ReflectionClass(CancelRequest::class);
        $method = $reflection->getMethod('getLMRIdFromDrawId');
        $method->setAccessible(true);

        // Test with invalid encrypted ID - should return null
        $result = $method->invoke(null, 'invalid_encrypted_id');
        $this->assertNull($result);
    }

    /**
     * Test that the class has proper namespace.
     */
    public function testClassNamespace(): void
    {
        $reflection = new \ReflectionClass(CancelRequest::class);
        $this->assertEquals('pages\backoffice\api_v2\draw_management\LoanFile\CancelRequest', $reflection->getNamespaceName());
    }

    /**
     * Test that the class is properly documented.
     */
    public function testClassDocumentation(): void
    {
        $reflection = new \ReflectionClass(CancelRequest::class);
        $docComment = $reflection->getDocComment();
        
        $this->assertNotFalse($docComment);
        $this->assertStringContainsString('API endpoint for canceling draw requests', $docComment);
        $this->assertStringContainsString('SOW revision requests', $docComment);
    }

    /**
     * Test that Post method is properly documented.
     */
    public function testPostMethodDocumentation(): void
    {
        $reflection = new \ReflectionClass(CancelRequest::class);
        $method = $reflection->getMethod('Post');
        $docComment = $method->getDocComment();
        
        $this->assertNotFalse($docComment);
        $this->assertStringContainsString('Handle POST requests', $docComment);
        $this->assertStringContainsString('type', $docComment);
        $this->assertStringContainsString('drawId', $docComment);
    }
}
