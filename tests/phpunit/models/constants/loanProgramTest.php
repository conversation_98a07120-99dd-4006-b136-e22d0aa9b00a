<?php

namespace tests\models\constants;

use models\constants\loanProgram;
use PHPUnit\Framework\TestCase;

/**
 * Tests the constants method of the loanProgram class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 *
 * @covers \models\constants\loanProgram::constants
 */
class loanProgramTest extends TestCase
{
  /**
   * Tests the constants method of the loanProgram class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testConstants()
  {
    $this->assertSame('BRL', loanProgram::BRIDGE_LOAN);
    $this->assertSame('Bri163', loanProgram::BRIDGE_TO_PERM);
    $this->assertSame('Bri869', loanProgram::PERM_LOAN);
    $this->assertSame('BRL', loanProgram::CV3_BRIDGE_LOAN);
    $this->assertSame('Ren496', loanProgram::CV3_RENTAL_LOAN);
    $this->assertSame('Bri879861', loanProgram::CV3_BRIDGE_LOAN_PORTFOLIO);
    $this->assertSame('Ren949', loanProgram::CV3_RENTAL_LOAN_PORTFOLIO);
    $this->assertSame('Bri996', loanProgram::CV3_BRIDGE_LOAN_RENOVATION);
    $this->assertSame('BRLGUC', loanProgram::CV3_GROUND_UP_CONSTRUCTION);
  }
}
