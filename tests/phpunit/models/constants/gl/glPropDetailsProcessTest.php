<?php

namespace models\constants\gl;

use PHPUnit\Framework\TestCase;

/**
 * Tests the glPropDetailsProcess method of the glPropDetailsProcess class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 */
class glPropDetailsProcessTest extends TestCase
{
  /**
   * Tests the glPropDetailsProcess method of the glPropDetailsProcess class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testGlPropDetailsProcess()
  {
    $expected = [
      'Looking for General Info',
      'Actively Looking For Property',
      'Identified a Property',
      'Have Property Under Contract',
      'I Own The Property/Land'
    ];

    $this->assertSame($expected, glPropDetailsProcess::glPropDetailsProcess);
    $this->assertSame($expected, glPropDetailsProcess::$glPropDetailsProcess);
  }
}
