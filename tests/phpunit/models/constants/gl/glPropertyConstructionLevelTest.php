<?php

namespace tests\models\constants\gl;

use models\constants\gl\glPropertyConstructionLevel;
use PHPUnit\Framework\TestCase;

/**
 * Tests the glPropertyConstructionLevelArray method of the glPropertyConstructionLevel class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 *
 * @covers \models\constants\gl\glPropertyConstructionLevel::glPropertyConstructionLevelArray
 */
class glPropertyConstructionLevelTest extends TestCase
{
  /**
   * Tests the glPropertyConstructionLevelArray method of the glPropertyConstructionLevel class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testGlPropertyConstructionLevelArray()
  {
    $expectedArray = [
      'Level 1: Light Scope',
      'Level 2: Medium Scope',
      'Level 3: Heavy Renovation/Gut',
      'Level 4: Footprint Expansion',
      'Level 5: Ground Up'
    ];

    $this->assertSame($expectedArray, glPropertyConstructionLevel::$glPropertyConstructionLevel);
  }
}
