<?php

namespace models;

use models\types\strongType;

class Pagination extends strongType
{
    public static function Render(
        ?int $page,
        ?int $noOfRecords,
        ?int $noOfRecordsPerPage,
        ?int $NumberOfLinksToShow
    ) {
        if(!$page) {
            $page = 0;
        }

        if(!$noOfRecords) {
            return '';
        }

        $recNumbStart = $page * $noOfRecordsPerPage;
        $recNumbEnd = $recNumbStart + $noOfRecordsPerPage;
        $pageNumber = ceil($recNumbStart / $noOfRecordsPerPage);

        $totalNoOfLinks = ceil($noOfRecords / $noOfRecordsPerPage) - 1;

        if($recNumbEnd > $noOfRecords) {
            $recNumbEnd = $noOfRecords;
        }
        ob_start();
        ?>
        <div class="card card-custom p-2" style="border: none !important;box-shadow: none !important;">
            <div class="card-body p-0">
                <div class="example-preview">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div class="d-flex align-items-right py-2">
                            <div class="text-muted">
                                Showing <?php echo number_format($noOfRecords > 0 ? $recNumbStart + 1 : 0 ); ?> to <?php echo number_format($recNumbEnd); ?>
                                of <?php echo number_format($noOfRecords) ?>
                                records.
                            </div>
                        </div>

                        <div class="d-flex flex-wrap py-2 mr-3">
                 <span class="align-items-right text-muted py-2 d-none">
                     <?php if ($noOfRecords > $noOfRecordsPerPage) { ?>&nbsp;&nbsp; Page #<?php } ?>
                    </span>
                            <?php
                            if ($noOfRecords > $noOfRecordsPerPage) {
                                $startLink = $pageNumber - $NumberOfLinksToShow;
                                if ($startLink < 0) {
                                    $startLink = 0;
                                }
                                $lastRecord = floor($totalNoOfLinks);

                                $endLink = $startLink + (2 * $NumberOfLinksToShow);
                                if ($endLink > $totalNoOfLinks) {
                                    $endLink = $totalNoOfLinks;
                                }
                                echo "<span class='cursor-pointer btn btn-icon btn-sm btn-light-primary mr-2 my-1 tooltipClass' title='First Page' onclick=\"pagination.updatePageNumber(1)\"><i class='ki ki-bold-double-arrow-back icon-xs'></i></span>";

                                if ($startLink > 1) {
                                    $prevNumb = $pageNumber - 1;
                                    echo "<span class='cursor-pointer btn btn-icon btn-sm btn-light-primary mr-2 my-1 tooltipClass' title='Previous Page' onclick=\"pagination.updatePageNumber(" . $prevNumb . ")\"><i class='ki ki-bold-arrow-back icon-xs'></i></span>";
                                }

                                for ($numb = $startLink; $numb <= $endLink; $numb++) {
                                    if ($numb == $pageNumber) {
                                        echo "<span class='cursor-pointer btn btn-icon btn-sm border-0 btn-hover-primary active mr-2 my-1 font-size-xs'>" . ($numb + 1) . '</span>';
                                    } else {
                                        echo "<span class='cursor-pointer btn btn-icon btn-sm border-0 btn-light mr-2 my-1 font-size-xs' onclick=\"pagination.updatePageNumber(" . $numb . ");\">" . ($numb + 1) . '</span>';

                                    }
                                }
                                if ($endLink < $totalNoOfLinks) {
                                    $nextNumb = $pageNumber + 1;
                                    echo "<span class='cursor-pointer btn btn-icon btn-sm border-0 btn-light mr-2 my-1 tooltipClass' title='Next Page'  onclick=\"pagination.updatePageNumber(" . $nextNumb . ")\"><i class='ki ki-bold-arrow-next icon-xs'></i></span>";

                                }
                                echo "<span class='cursor-pointer btn btn-icon btn-sm border-0 btn-light mr-2 my-1 tooltipClass'  title='Last Page' onclick=\"pagination.updatePageNumber(" . $lastRecord . ")\"><i class='ki ki-bold-double-arrow-next icon-xs'></i></span>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php
        return ob_get_clean();
    }
}