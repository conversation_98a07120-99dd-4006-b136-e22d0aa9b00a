<?php

namespace models\standard;

use models\composite\oPC\getPCBasicLoanInfo;
use models\constants\gl\glPCID;
use models\lendingwise\tblProcessingCompany;
use models\PageVariables;
use models\PCConfigDetails;
use models\types\strongType;
use models\UserCountDetails;

class Custify extends strongType
{
    public static ?int $activeEmployeeCount = null;
    public static ?int $inactiveEmployeeCount = null;
    public static ?int $loginRightEmployeeCount = null;

    public static ?int $activeBranchCount = null;
    public static ?int $inactiveBranchCount = null;
    public static ?int $loginRightBranchCount = null;

    public static ?int $activeBrokerCount = null;
    public static ?int $inactiveBrokerCount = null;
    public static ?int $loginRightBrokerCount = null;

    public static ?int $activeLoanOfficerCount = null;
    public static ?int $inactiveLoanOfficerCount = null;
    public static ?int $loginRightLoanOfficerCount = null;
    public static ?int $activeFileCount = null;
    public static ?int $inactiveFileCount = null;
    public static ?int $contactsCount = null;
    public static ?string $disabledDefaultLP = null;
    public static ?string $additionalLPAssigned = null;
    public static ?int $enabledInternalLoanProgramsCount = null;
    public static ?int $enabledLoanProgramsCount = null;
    public static ?int $PCCreatedLoanProgramsCount = null;
    public static ?int $PCCreatedInternalLoanProgramsCount = null;
    public static ?int $FAEnabledCount = null;
    public static ?int $FADisabledCount = null;
    public static ?int $QAEnabledCount = null;
    public static ?int $QADisabledCount = null;
    public static ?int $BOEnabledCount = null;
    public static ?int $BODisabledCount = null;
    public static ?int $activeDocWizardDocCount = null;
    public static ?int $inactiveDocWizardDocCount = null;
    public static ?int $activePCCreatedFileStatusCount = null;
    public static ?int $inactivePCCreatedFileStatusCount = null;
    public static ?int $inactiveFileStatusCount = null;
    public static ?int $totalInactiveFileStatusCount = null;

    public static ?int $activePCCreatedFileSubStatusCount = null;
    public static ?int $inactivePCCreatedFileSubStatusCount = null;
    public static ?int $inactiveFileSubStatusCount = null;
    public static ?int $totaInactiveFileSubStatusCount = null;
    public static ?int $PCCreatedRequiredDocsCount = null;
    public static ?int $webhookAutomationCount = null;
    public static ?int $tasksAutomationCount = null;
    public static ?int $automationPrimaryStatusTriggerCount = null;
    public static ?int $automationWorkflowTriggerCount = null;
    public static ?int $monthlyLoanFileCreationCount = null;
    public static ?int $activePCCreatedWorkflowStepsCount = null;
    public static ?int $inactivePCCreatedWorkflowStepsCount = null;
    public static ?int $inactiveWorkflowStepsCount = null;
    public static ?int $totalInactiveWorkflowStepsCount = null;
    public static ?int $stickyNotesCreatedCount = null;
    public static ?int $taskCreatedCount = null;
    public static ?int $serviceReportRequestedCount = null;
    public static ?int $customEmailTemplatesCount = null;
    public static ?int $autogeneratedDocsAccessCount = null;
    public static ?int $esignedDocsCount = null;
    public static ?int $ffEditCount = null;
    public static ?int $FAEditCount = null;
    public static ?int $QAEditCount = null;
    public static ?int $BOEditCount = null;
    public static ?int $customFFCount = null;
    public static ?int $automationNewLoanTriggerCount = null;
    public static ?int $creditPullCount = null;
    public static $lastStatisticsTrackerUpdate;
    public static $customerID;
    public static ?int $fileCreatedViaWebformCount = null;
    public static ?int $fileCreatedViaBOCount = null;
    public static ?int $fileCreatedViaBranchCount = null;
    public static ?int $fileCreatedViaBrokerCount = null;
    public static ?int $fileCreatedViaBorrowerCount = null;
    public static ?int $fileCreatedViaLoanOfficerCount = null;
    public static ?int $geraciSubmissionCount = null;
    public static ?int $fileEmailsSentCount = null;
    public static ?int $bulkEmailsSentCount = null;
    public static ?int $borrowerDocsCount = null;
    public static ?int $entityDocsCount = null;
    public static ?int $membersDocsCount = null;
    public static ?int $fileTabOrderEditCount = null;
    public static ?int $enabledTabsCount = null;
    public static ?int $disabledTabsCount = null;

    public static ?int $clientLoginCount = null;
    public static ?int $agentLoginCount = null;
    public static ?int $branchLoginCount = null;
    public static ?int $employeeLoginCount = null;
    public static ?int $defaultAuogeneratedDocsEnabledCount = null;
    public static ?string $fileTypeName = null;
    public static ?int $activePCCreatedWorkflowCount = null;
    public static ?int $inactivePCCreatedWorkflowCount = null;
    public static ?int $inactiveWorkflowCount = null;
    public static ?int $totalInactiveWorkflowCount = null;
    public static ?int $deletedRequiredDocsCount = null;
    public static ?string $branchLogoUploaded = null;


    /**
     * @param int $PCID
     * @param tblProcessingCompany $pcInfo
     * @return string|null
     * @throws \DateMalformedStringException
     */
    public static function getCustifyScript(int $PCID, tblProcessingCompany $pcInfo): ?string
    {
        if (($_ENV['CUSTIFY'] ?? 0) == 0 || $PCID == glPCID::PCID_PROD_CV3) {
            return null;
        }
        $script = <<<SCRIPT
<script type="text/javascript">
    var _ctrack = _ctrack || [];
    (function () {
        for (var t = ["identify", "track", "setInstance", "setAccount", "trackTime", "stopTrackTime", "debug", "setOptions", "triggerPendingAction", "closePendingAction"], a = function (t) {
            return function () {
                _ctrack.push([t].concat(Array.prototype.slice.call(arguments, 0)))
            }
        }, n = 0; n < t.length; n++) _ctrack[t[n]] = a(t[n])
        var d = document, g = d.createElement('script'), s = d.getElementsByTagName('script')[0];
        g.type = 'text/javascript';
        g.async = true;
        g.defer = true;
        g.src = 'https://assets.custify.com/assets/track.min.js';
        s.parentNode.insertBefore(g, s);
    })();
    _ctrack.setInstance("us");
    _ctrack.setAccount("67a0e96f5060f46599d06c7d");
    _ctrack.setOptions({
        createOrUpdateEntities: true
    });
</script>
SCRIPT;

        $userId = PageVariables::$userNumber;
        $email = PageVariables::$userEmail;
        // Only run full tracking if not already initialized
        if (
            empty($_SESSION['custify_initialized']) &&
            (
                !$pcInfo->lastStatisticsTrackerUpdate ||
                (strtotime($pcInfo->lastStatisticsTrackerUpdate) < (time() - 7200)) ||
                PageVariables::$PCID == glPCID::PCID_PROD_OSKAR ||
                PageVariables::$PCID == glPCID::PCID_PROD_OSKAR_ELITE
            )
        ) {
            //check company settings for new datetime field "lastMetrics" to see if last datetime is more than 2 hours ago, if yes do this following code, if no continue to else

            $pcInfo->lastStatisticsTrackerUpdate = Dates::Timestamp();
            $pcInfo->Save();

            $userCountDetails = UserCountDetails::getReport($PCID);
            self::$activeEmployeeCount = $userCountDetails['activeEmployeeCount'] ?? 0;
            self::$inactiveEmployeeCount = $userCountDetails['inactiveEmployeeCount'] ?? 0;
            self::$loginRightEmployeeCount = $userCountDetails['loginRightEmployeeCount'] ?? 0;
            self::$activeBranchCount = $userCountDetails['activeBranchCount'] ?? 0;
            self::$inactiveBranchCount = $userCountDetails['inactiveBranchCount'] ?? 0;
            self::$loginRightBranchCount = $userCountDetails['loginRightBranchCount'] ?? 0;
            self::$activeBrokerCount = $userCountDetails['activeBrokerCount'] ?? 0;
            self::$inactiveBrokerCount = $userCountDetails['inactiveBrokerCount'] ?? 0;
            self::$loginRightBrokerCount = $userCountDetails['loginRightBrokerCount'] ?? 0;
            self::$activeLoanOfficerCount = $userCountDetails['activeLoanOfficerCount'] ?? 0;
            self::$inactiveLoanOfficerCount = $userCountDetails['inactiveLoanOfficerCount'] ?? 0;
            self::$loginRightLoanOfficerCount = $userCountDetails['loginRightLoanOfficerCount'] ?? 0;
            self::$activeFileCount = $userCountDetails['activeFileCount'] ?? 0;
            self::$inactiveFileCount = $userCountDetails['inactiveFileCount'] ?? 0;
            self::$contactsCount = $userCountDetails['contactsCount'] ?? 0;

            $PCConfigDetails = PCConfigDetails::getReport($PCID);
            self::$disabledDefaultLP = $PCConfigDetails['disabledDefaultLP'] ?? 0;
            self::$additionalLPAssigned = $PCConfigDetails['additionalLPAssigned'] ?? 0;
            self::$enabledInternalLoanProgramsCount = $PCConfigDetails['enabledInternalLoanProgramsCount'] ?? 0;
            self::$enabledLoanProgramsCount = $PCConfigDetails['enabledLoanProgramsCount'] ?? 0;
            self::$PCCreatedLoanProgramsCount = $PCConfigDetails['PCCreatedLoanProgramsCount'] ?? 0;
            self::$PCCreatedInternalLoanProgramsCount = $PCConfigDetails['PCCreatedInternalLoanProgramsCount'] ?? 0;
            self::$FAEnabledCount = $PCConfigDetails['FAEnabledCount'] ?? 0;
            self::$FADisabledCount = $PCConfigDetails['FADisabledCount'] ?? 0;
            self::$QAEnabledCount = $PCConfigDetails['QAEnabledCount'] ?? 0;
            self::$QADisabledCount = $PCConfigDetails['QADisabledCount'] ?? 0;
            self::$BOEnabledCount = $PCConfigDetails['BOEnabledCount'] ?? 0;
            self::$BODisabledCount = $PCConfigDetails['BODisabledCount'] ?? 0;
            self::$activeDocWizardDocCount = $PCConfigDetails['activeDocWizardDocCount'] ?? 0;
            self::$inactiveDocWizardDocCount = $PCConfigDetails['inactiveDocWizardDocCount'] ?? 0;
            self::$activePCCreatedFileStatusCount = $PCConfigDetails['activePCCreatedFileStatusCount'] ?? 0;
            self::$inactivePCCreatedFileStatusCount = $PCConfigDetails['inactivePCCreatedFileStatusCount'] ?? 0;
            self::$inactiveFileStatusCount = $PCConfigDetails['inactiveFileStatusCount'] ?? 0;
            self::$totalInactiveFileStatusCount = $PCConfigDetails['totalInactiveFileStatusCount'] ?? 0;
            self::$activePCCreatedFileSubStatusCount = $PCConfigDetails['activePCCreatedFileSubStatusCount'] ?? 0;
            self::$inactivePCCreatedFileSubStatusCount = $PCConfigDetails['inactivePCCreatedFileSubStatusCount'] ?? 0;
            self::$inactiveFileSubStatusCount = $PCConfigDetails['inactiveFileSubStatusCount'] ?? 0;
            self::$totaInactiveFileSubStatusCount = $PCConfigDetails['totaInactiveFileSubStatusCount'] ?? 0;
            self::$PCCreatedRequiredDocsCount = $PCConfigDetails['PCCreatedRequiredDocsCount'] ?? 0;
            self::$webhookAutomationCount = $PCConfigDetails['webhookAutomationCount'] ?? 0;
            self::$tasksAutomationCount = $PCConfigDetails['tasksAutomationCount'] ?? 0;
            self::$automationPrimaryStatusTriggerCount = $PCConfigDetails['automationPrimaryStatusTriggerCount'] ?? 0;
            self::$automationWorkflowTriggerCount = $PCConfigDetails['automationWorkflowTriggerCount'] ?? 0;
            self::$monthlyLoanFileCreationCount = $PCConfigDetails['monthlyLoanFileCreationCount'] ?? 0;
            self::$activePCCreatedWorkflowStepsCount = $PCConfigDetails['activePCCreatedWorkflowStepsCount'] ?? 0;
            self::$inactivePCCreatedWorkflowStepsCount = $PCConfigDetails['inactivePCCreatedWorkflowStepsCount'] ?? 0;
            self::$inactiveWorkflowStepsCount = $PCConfigDetails['inactiveWorkflowStepsCount'] ?? 0;
            self::$totalInactiveWorkflowStepsCount = $PCConfigDetails['totalInactiveWorkflowStepsCount'] ?? 0;
            self::$stickyNotesCreatedCount = $PCConfigDetails['stickyNotesCreatedCount'] ?? 0;
            self::$taskCreatedCount = $PCConfigDetails['taskCreatedCount'] ?? 0;
            self::$serviceReportRequestedCount = $PCConfigDetails['serviceReportRequestedCount'] ?? 0;
            self::$customEmailTemplatesCount = $PCConfigDetails['customEmailTemplatesCount'] ?? 0;
            self::$autogeneratedDocsAccessCount = $PCConfigDetails['autogeneratedDocsAccessCount'] ?? 0;
            self::$esignedDocsCount = $PCConfigDetails['esignedDocsCount'] ?? 0;
            self::$ffEditCount = $PCConfigDetails['ffEditCount'] ?? 0;
            self::$FAEditCount = $PCConfigDetails['FAEditCount'] ?? 0;
            self::$QAEditCount = $PCConfigDetails['QAEditCount'] ?? 0;
            self::$BOEditCount = $PCConfigDetails['BOEditCount'] ?? 0;
            self::$customFFCount = $PCConfigDetails['customFFCount'] ?? 0;
            self::$automationNewLoanTriggerCount = $PCConfigDetails['automationNewLoanTriggerCount'] ?? 0;
            self::$creditPullCount = $PCConfigDetails['creditPullCount'] ?? 0;
            self::$lastStatisticsTrackerUpdate = $pcInfo->lastStatisticsTrackerUpdate;
            self::$customerID = $pcInfo->customerID;
            self::$fileCreatedViaWebformCount = $PCConfigDetails['fileCreatedViaWebformCount'] ?? 0;
            self::$fileCreatedViaBOCount = $PCConfigDetails['fileCreatedViaBOCount'] ?? 0;
            self::$fileCreatedViaBranchCount = $PCConfigDetails['fileCreatedViaBranchCount'] ?? 0;
            self::$fileCreatedViaBrokerCount = $PCConfigDetails['fileCreatedViaBrokerCount'] ?? 0;
            self::$fileCreatedViaBorrowerCount = $PCConfigDetails['fileCreatedViaBorrowerCount'] ?? 0;
            self::$fileCreatedViaLoanOfficerCount = $PCConfigDetails['fileCreatedViaLoanOfficerCount'] ?? 0;
            self::$geraciSubmissionCount = $PCConfigDetails['geraciSubmissionCount'] ?? 0;
            self::$fileEmailsSentCount = $PCConfigDetails['fileEmailsSentCount'] ?? 0;
            self::$bulkEmailsSentCount = $PCConfigDetails['bulkEmailsSentCount'] ?? 0;
            self::$borrowerDocsCount = $PCConfigDetails['borrowerDocsCount'] ?? 0;
            self::$entityDocsCount = $PCConfigDetails['entityDocsCount'] ?? 0;
            self::$membersDocsCount = $PCConfigDetails['membersDocsCount'] ?? 0;
            self::$fileTabOrderEditCount = $PCConfigDetails['fileTabOrderEditCount'] ?? 0;
            self::$enabledTabsCount = $PCConfigDetails['enabledTabsCount'] ?? 0;
            self::$disabledTabsCount = $PCConfigDetails['disabledTabsCount'] ?? 0;
            self::$clientLoginCount = $PCConfigDetails['clientLoginCount'] ?? 0;
            self::$agentLoginCount = $PCConfigDetails['agentLoginCount'] ?? 0;
            self::$branchLoginCount = $PCConfigDetails['branchLoginCount'] ?? 0;
            self::$employeeLoginCount = $PCConfigDetails['employeeLoginCount'] ?? 0;
            self::$defaultAuogeneratedDocsEnabledCount = $PCConfigDetails['defaultAuogeneratedDocsEnabledCount'] ?? 0;
            self::$fileTypeName = $PCConfigDetails['fileTypeName'] ?? 0;
            self::$activePCCreatedWorkflowCount = $PCConfigDetails['activePCCreatedWorkflowCount'] ?? 0;
            self::$inactivePCCreatedWorkflowCount = $PCConfigDetails['inactivePCCreatedWorkflowCount'] ?? 0;
            self::$inactiveWorkflowCount = $PCConfigDetails['inactiveWorkflowCount'] ?? 0;
            self::$totalInactiveWorkflowCount = $PCConfigDetails['totalInactiveWorkflowCount'] ?? 0;
            self::$deletedRequiredDocsCount = $PCConfigDetails['deletedRequiredDocsCount'] ?? 0;
            self::$branchLogoUploaded = $PCConfigDetails['branchLogoUploaded'] ?? 'No';


            $userName = htmlentities(PageVariables::$userName);
            $createdDate = PageVariables::$createdDate
                ? (new \DateTime(PageVariables::$createdDate))->modify('+1 day')->format('Y-m-d')
                : null;
            $accessLevel = PageVariables::$userGroup;

            $role = 'Unknown';
            if (PageVariables::$userGroup === 'Agent') {
                $role = ($_SESSION['externalBroker'] ?? 0) == 1 ? 'LoanOfficer' : 'Broker';
            } else {
                $role = $_SESSION['userRole'] ?? null;
            }

            $companyName = $pcInfo->processingCompanyName ?? null;
            $planType = $pcInfo->planType ?? null;
            $website = $pcInfo->processingCompanyWebsite ?? null;
            $chargebeeId = preg_replace('/^cb_/', '', $pcInfo->subscriberID) ?? null;
            $pcCreatedDate = $pcInfo->recordDate;
            if ($accessLevel == 'Branch') {
                //we can add their branch company for further identification
                $branchCompany = "branch_company: \"" . htmlentities($_SESSION['processorAssignedCompany']) . "\"";
            }
            $employee_number_active = self::$activeEmployeeCount;
            $employee_number_inactive = self::$inactiveEmployeeCount;
            $employee_number_login_rights = self::$loginRightEmployeeCount;
            $branch_number_active = self::$activeBranchCount;
            $branch_number_inactive = self::$inactiveBranchCount;
            $branch_number_login_rights = self::$loginRightBranchCount;
            $broker_number_active = self::$activeBrokerCount;
            $broker_number_inactive = self::$inactiveBrokerCount;
            $broker_number_login_rights = self::$loginRightBrokerCount;
            $lo_number_active = self::$activeLoanOfficerCount;
            $lo_number_inactive = self::$inactiveLoanOfficerCount;
            $lo_number_login_rights = self::$loginRightLoanOfficerCount;
            $activeFileCount = self::$activeFileCount;
            $contactsCount = self::$contactsCount;
            $loanProgramsCount = (PageVariables::$userRole !== 'Super') ? count(PageVariables::$myServicesArray) : null;  //lessdata
            $StddHMLOLoanProgramsEnabled = (PageVariables::$userRole !== 'Super') ? count(array_key_exists('HMLO', PageVariables::$moduleServiceArray) ? PageVariables::$moduleServiceArray['HMLO'] : []) : null;  //lessdata
            $StddBusinessFundingLoanProgramsEnabled = (PageVariables::$userRole !== 'Super') ? count(array_key_exists('loc', PageVariables::$moduleServiceArray) ? PageVariables::$moduleServiceArray['loc'] : []) : null;  //lessdata
            $guidelines = getPCBasicLoanInfo::getReport(['PCID' => $PCID]) ?? [];
            $customLoanGuidelinesCount = count($guidelines['basicInfo'] ?? []);
            $disabledDefaultLP = self::$disabledDefaultLP;
            $additionalLPAssigned = self::$additionalLPAssigned;
            $additionalLPAssignedCount = self::$additionalLPAssigned ? sizeof(explode(',', $additionalLPAssigned)) ?? 0 : 0;
            $disabledDefaultLPCount = self::$disabledDefaultLP ? sizeof(explode(',', $disabledDefaultLP)) ?? 0 : 0;
            $enabledInternalLoanProgramsCount = self::$enabledInternalLoanProgramsCount;
            $enabledLoanProgramsCount = self::$enabledLoanProgramsCount;
            $PCCreatedLoanProgramsCount = self::$PCCreatedLoanProgramsCount;
            $PCCreatedInternalLoanProgramsCount = self::$PCCreatedInternalLoanProgramsCount;
            $FAEnabledCount = self::$FAEnabledCount;
            $FADisabledCount = self::$FADisabledCount;
            $QAEnabledCount = self::$QAEnabledCount;
            $QADisabledCount = self::$QADisabledCount;
            $BOEnabledCount = self::$BOEnabledCount;
            $BODisabledCount = self::$BODisabledCount;
            $activeDocWizardDocCount = self::$activeDocWizardDocCount;
            $inactiveDocWizardDocCount = self::$inactiveDocWizardDocCount;
            $activePCCreatedFileStatusCount = self::$activePCCreatedFileStatusCount;
            $inactivePCCreatedFileStatusCount = self::$inactivePCCreatedFileStatusCount;
            $inactiveFileStatusCount = self::$inactiveFileStatusCount;
            $totalInactiveFileStatusCount = self::$totalInactiveFileStatusCount;
            $activePCCreatedFileSubStatusCount = self::$activePCCreatedFileSubStatusCount;
            $inactivePCCreatedFileSubStatusCount = self::$inactivePCCreatedFileSubStatusCount;
            $inactiveFileSubStatusCount = self::$inactiveFileSubStatusCount;
            $totaInactiveFileSubStatusCount = self::$totaInactiveFileSubStatusCount;

            $PCCreatedRequiredDocsCount = self::$PCCreatedRequiredDocsCount;
            $webhookAutomationCount = self::$webhookAutomationCount;
            $tasksAutomationCount = self::$tasksAutomationCount;
            $automationPrimaryStatusTriggerCount = self::$automationPrimaryStatusTriggerCount;
            $automationWorkflowTriggerCount = self::$automationWorkflowTriggerCount;
            $activePCCreatedWorkflowStepsCount = self::$activePCCreatedWorkflowStepsCount;
            $inactivePCCreatedWorkflowStepsCount = self::$inactivePCCreatedWorkflowStepsCount;
            $inactiveWorkflowStepsCount = self::$inactiveWorkflowStepsCount;
            $totalInactiveWorkflowStepsCount = self::$totalInactiveWorkflowStepsCount;
            $stickyNotesCreatedCount = self::$stickyNotesCreatedCount;
            $taskCreatedCount = self::$taskCreatedCount;
            $serviceReportRequestedCount = self::$serviceReportRequestedCount;
            $customEmailTemplatesCount = self::$customEmailTemplatesCount;
            $autogeneratedDocsAccessCount = self::$autogeneratedDocsAccessCount;
            $ffEditCount = self::$ffEditCount;
            $FAEditCount = self::$FAEditCount;
            $QAEditCount = self::$QAEditCount;
            $BOEditCount = self::$BOEditCount;
            $customFFCount = self::$customFFCount;
            $automationNewLoanTriggerCount = self::$automationNewLoanTriggerCount;
            $creditPullCount = self::$creditPullCount;
            $monthlyLoanFileCreationCount = self::$monthlyLoanFileCreationCount;
            $thirdPartyServicesEnabled = Pagevariables::$glThirdPartyServices ?? 0;
            $thirdPartyServicesCSR = Pagevariables::$thirdPartyServiceCSR ?? null;
            $haveWhiteLabel = Pagevariables::$isPLO ?? 0;
            $whiteLabelSendingEmail = Pagevariables::$glUseMyNameAndEmail ?? 0;
            $nestedEntities = Pagevariables::$allowNestedEntityMembers ?? 0;
            $autoGeneratedLoanNumber = Pagevariables::$showStartLoanNumber ?? 0;
            $lastStatisticsTrackerUpdate = self::$lastStatisticsTrackerUpdate;
            $customerID = self::$customerID;
            $fileCreatedViaWebformCount = self::$fileCreatedViaWebformCount;
            $fileCreatedViaBOCount = self::$fileCreatedViaBOCount;
            $fileCreatedViaBranchCount = self::$fileCreatedViaBranchCount;
            $fileCreatedViaBrokerCount = self::$fileCreatedViaBrokerCount;
            $fileCreatedViaBorrowerCount = self::$fileCreatedViaBorrowerCount;
            $fileCreatedViaLoanOfficerCount = self::$fileCreatedViaLoanOfficerCount;
            $geraciSubmissionCount = self::$geraciSubmissionCount;
            $fileEmailsSentCount = self::$fileEmailsSentCount;
            $bulkEmailsSentCount = self::$bulkEmailsSentCount;
            $borrowerDocsCount = self::$borrowerDocsCount;
            $entityDocsCount = self::$entityDocsCount;
            $membersDocsCount = self::$membersDocsCount;
            $fileTabOrderEditCount = self::$fileTabOrderEditCount;
            $enabledTabsCount = self::$enabledTabsCount;
            $disabledTabsCount = self::$disabledTabsCount;
            $clientLoginCount = self::$clientLoginCount;
            $agentLoginCount = self::$agentLoginCount;
            $branchLoginCount = self::$branchLoginCount;
            $employeeLoginCount = self::$employeeLoginCount;
            $defaultAuogeneratedDocsEnabledCount = self::$defaultAuogeneratedDocsEnabledCount;
            $companyLogoUploaded = PageVariables::$procCompLogo ? 'Yes' : 'No';
            $branchLogoUploaded = self::$branchLogoUploaded;
            $fileTypeName = self::$fileTypeName;
            $activePCCreatedWorkflowCount = self::$activePCCreatedWorkflowCount;
            $inactivePCCreatedWorkflowCount = self::$inactivePCCreatedWorkflowCount;
            $inactiveWorkflowCount = self::$inactiveWorkflowCount;
            $totalInactiveWorkflowCount = self::$totalInactiveWorkflowCount;
            $deletedRequiredDocsCount = self::$deletedRequiredDocsCount;


            // Add more clean vars here if needed
            $script .= <<<SCRIPT
<script type="text/javascript">
_ctrack.identify(
    {
        userId: "{$userId}",
        email: "{$email}",
        company_id: {$PCID}
    },
    {
        user: {
            name: "{$userName} ({$role})",
            signed_up_at: "{$createdDate}",
            custom_attributes: {
                access_level: "{$accessLevel}",
                role: "{$role}",
                {$branchCompany}
}
        },
        company: {
            name: "{$companyName}",
            plan: "{$planType}",
            website: "{$website}",
            custom_attributes: {
                employee_number_active: {$employee_number_active},
                employee_number_inactive: {$employee_number_inactive},
                employee_number_login_rights: {$employee_number_login_rights},
                branch_number_active: {$branch_number_active},
                branch_number_inactive: {$branch_number_inactive},
                branch_number_login_rights: {$branch_number_login_rights},
                broker_number_active: {$broker_number_active},
                broker_number_inactive: {$broker_number_inactive},
                broker_number_login_rights: {$broker_number_login_rights},
                lo_number_active: {$lo_number_active},
                lo_number_inactive: {$lo_number_inactive},
                lo_number_login_rights: {$lo_number_login_rights},
                total_loans: {$activeFileCount},
                chargebee_id: "{$chargebeeId}",
                pc_created_date: "{$pcCreatedDate}",
                contactsCount: {$contactsCount},
                loanProgramsCount: {$loanProgramsCount},
                customLoanGuidelinesCount: {$customLoanGuidelinesCount},
                disabledDefaultLP: "{$disabledDefaultLP}",
                disabledDefaultLPCount: {$disabledDefaultLPCount},
                additionalLPAssigned: "{$additionalLPAssigned}",
                additionalLPAssignedCount: {$additionalLPAssignedCount},
                enabledInternalLoanProgramsCount: {$enabledInternalLoanProgramsCount},
                enabledLoanProgramsCount: {$enabledLoanProgramsCount},
                PCCreatedLoanProgramsCount: {$PCCreatedLoanProgramsCount},
                PCCreatedInternalLoanProgramsCount: {$PCCreatedInternalLoanProgramsCount},
                FAFFEnabledCount: {$FAEnabledCount},
                FAFFDisabledCount: {$FADisabledCount},
                QAFFEnabledCount: {$QAEnabledCount},
                QAFFDisabledCount: {$QADisabledCount},
                BOFFEnabledCount: {$BOEnabledCount},
                BOFFDisabledCount: {$BODisabledCount},
                activeDocWizardDocCount: {$activeDocWizardDocCount},
                inactiveDocWizardDocCount: {$inactiveDocWizardDocCount},
                activePCCreatedFileStatusCount: {$activePCCreatedFileStatusCount},
                inactivePCCreatedFileStatusCount: {$inactivePCCreatedFileStatusCount},
                inactiveFileStatusCount: {$inactiveFileStatusCount},
                totalInactiveFileStatusCount: {$totalInactiveFileStatusCount},
                activePCCreatedFileSubStatusCount: {$activePCCreatedFileSubStatusCount},
                inactivePCCreatedFileSubStatusCount: {$inactivePCCreatedFileSubStatusCount},
                inactiveFileSubStatusCount: {$inactiveFileSubStatusCount},
                totaInactiveFileSubStatusCount: {$totaInactiveFileSubStatusCount},
                PCCreatedRequiredDocsCount: {$PCCreatedRequiredDocsCount},
                webhookAutomationCount: {$webhookAutomationCount},
                tasksAutomationCount: {$tasksAutomationCount},
                automationPrimaryStatusTriggerCount: {$automationPrimaryStatusTriggerCount},
                automationWorkflowTriggerCount: {$automationWorkflowTriggerCount},
                activePCCreatedWorkflowStepsCount: {$activePCCreatedWorkflowStepsCount},
                inactivePCCreatedWorkflowStepsCount: {$inactivePCCreatedWorkflowStepsCount},
                inactiveWorkflowStepsCount: {$inactiveWorkflowStepsCount},
                totalInactiveWorkflowStepsCount: {$totalInactiveWorkflowStepsCount},
                stickyNotesCreatedCount: {$stickyNotesCreatedCount},
                taskCreatedCount: {$taskCreatedCount},
                serviceReportRequestedCount: {$serviceReportRequestedCount},
                customEmailTemplatesCount: {$customEmailTemplatesCount},
                autogeneratedDocsAccessCount: {$autogeneratedDocsAccessCount},
                ffEditCount: {$ffEditCount},
                FAEditCount: {$FAEditCount},
                QAEditCount: {$QAEditCount},
                BOEditCount: {$BOEditCount},
                customFFCount: {$customFFCount},
                automationNewLoanTriggerCount: {$automationNewLoanTriggerCount},
                creditPullCount: {$creditPullCount},
                monthlyLoanFileCreationCount: {$monthlyLoanFileCreationCount},
                thirdPartyServicesEnabled: {$thirdPartyServicesEnabled},
                thirdPartyServicesCSR: "{$thirdPartyServicesCSR}",
                haveWhiteLabel: {$haveWhiteLabel},
                whiteLabelSendingEmail: {$whiteLabelSendingEmail},
                usingNestedEntities: {$nestedEntities},
                usingAutoGeneratedLoanNumber: {$autoGeneratedLoanNumber},
                custifyLastUpdated: "{$lastStatisticsTrackerUpdate}",
                customerID: "{$customerID}",
                fileCreatedViaWebformCount: {$fileCreatedViaWebformCount},
                fileCreatedViaBOCount: {$fileCreatedViaBOCount},
                fileCreatedViaBranchCount: {$fileCreatedViaBranchCount}, 
                fileCreatedViaBrokerCount: {$fileCreatedViaBrokerCount},
                fileCreatedViaBorrowerCount: {$fileCreatedViaBorrowerCount},
                fileCreatedViaLoanOfficerCount: {$fileCreatedViaLoanOfficerCount},
                geraciSubmissionCount: {$geraciSubmissionCount},
                fileEmailsSentCount: {$fileEmailsSentCount},
                bulkEmailsSentCount: {$bulkEmailsSentCount},
                borrowerDocsCount: {$borrowerDocsCount},
                entityDocsCount: {$entityDocsCount},
                membersDocsCount: {$membersDocsCount},
                fileTabOrderEditCount: {$fileTabOrderEditCount},
                enabledTabsCount: {$enabledTabsCount},
                disabledTabsCount: {$disabledTabsCount},
                clientLoginCount: {$clientLoginCount},
                agentLoginCount: {$agentLoginCount},
                branchLoginCount: {$branchLoginCount},
                employeeLoginCount: {$employeeLoginCount},
                defaultAuogeneratedDocsEnabledCount: {$defaultAuogeneratedDocsEnabledCount},
                companyLogoUploaded: "{$companyLogoUploaded}",
                branchLogoUploaded: "{$branchLogoUploaded}",
                fileTypeName: "{$fileTypeName}",
                activePCCreatedWorkflowCount: {$activePCCreatedWorkflowCount},
                inactivePCCreatedWorkflowCount: {$inactivePCCreatedWorkflowCount},
                inactiveWorkflowCount: {$inactiveWorkflowCount},
                totalInactiveWorkflowCount: {$totalInactiveWorkflowCount},
                deletedRequiredDocsCount: {$deletedRequiredDocsCount},
                StddHMLOLoanProgramsEnabled: {$StddHMLOLoanProgramsEnabled},
                StddBusinessFundingLoanProgramsEnabled: {$StddBusinessFundingLoanProgramsEnabled}
            }
        }
    }
);
</script>
SCRIPT;
            //now update the datetime field "lastMetrics" with now() time @deethi
            $_SESSION['custify_initialized'] = true;

        } else {
            $script .= <<<SCRIPT
<script type="text/javascript">
    _ctrack.identify(
        {
            userId: '{$userId}',
            email: '{$email}',
            company_id: {$PCID}
        }
    );
</script>
SCRIPT;
        }

        $uriChecks = [
            'loan/servicing/configure'                    => 'viewed_File_Servicing_2.0_configure_loan_tab',
            'loan/servicing/onboard'                      => 'viewed_File_Servicing_deep_links(using servicing->onboard page)',
            'loan/servicing/loan_info'                    => 'viewed_File_Servicing_deep_links(using servicing->Loan Info page)',
            'loan/servicing/payoff'                       => 'viewed_File_Servicing_deep_links(using servicing->payoff page)',
            'loan/servicing/payments'                     => 'viewed_File_Servicing_deep_links(using servicing->payments page)',
            'loan/servicing/draws'                        => 'viewed_File_Servicing_deep_links(using servicing->draws/paydowns page)',
            'loan/servicing/investors'                    => 'viewed_File_Servicing_deep_links(using servicing->investors page)',
            'loan/servicing/extensions'                   => 'viewed_File_Servicing_deep_links(using servicing->extensions page)',
            'loan/servicing/escrow'                       => 'viewed_File_Servicing_deep_links(using servicing->escrow page)',
            'loan/servicing/charges'                      => 'viewed_File_Servicing_deep_links(using servicing->charges page)',
            'loan/servicing/balances'                     => 'viewed_File_Servicing_deep_links(using servicing->balances page)',
            'loan/servicing/journal'                      => 'viewed_File_Servicing_deep_links(using servicing->journal page)',
            'loan/servicing/issues'                       => 'viewed_File_Servicing_deep_links(using servicing->issues page)',
            'loan/servicing/lender'                       => 'viewed_File_Servicing_deep_links(using servicing->lender page)',
            'loan/servicing/servicer'                     => 'viewed_File_Servicing_deep_links(using servicing->servicer page)',
            'loan/servicing/trustee'                      => 'viewed_File_Servicing_deep_links(using servicing->trustee page)',
            'loan/fci'                                    => 'viewed_File_Servicing_deep_links(using servicing->fci page)',
            'loan/fm34'                                   => 'viewed_Fannie_Mae_tab',
            'myCalendar.php'                              => 'viewed_Calendar_page',
            'taskList.php'                                => 'viewed_Task_List_page',
            'backoffice/servicing'                        => 'viewed_Reports>Servicing_page',
            'reports/thirdparty'                          => 'viewed_Reports>3rd_Party_page',
            'billingReport.php'                           => 'viewed_Reports>Billing/Payables_page',
            'employeeNotesReport.php'                     => 'viewed_Reports>Employee_Reports_page',
            'bounced_emails'                              => 'viewed_Reports>Bounced_Emails_page',
            'mail_queue_files'                            => 'viewed_Reports>Mail_Queue_File_Related_page',
            'mail_queue_marketing'                        => 'viewed_Reports>Mail_Queue_Marketing_Related_page',
            'faxedFiles.php'                              => 'viewed_Reports>Faxed_Files_page',
            'unSubscribeList.php'                         => 'viewed_Reports>Unsubscribe_List_page',
            'onBoardingChecklist.php'                     => 'viewed_Onboarding_Checklist_page',
            'biWeeklyMortgageCalc.php'                    => 'viewed_Biweekly_Mortgage_Calculator_page',
            'blendedRatePaymentCalc.php'                  => 'viewed_Blended_Rate_Payment_calculator_page',
            'debtConsolidationCalc.php'                   => 'viewed_Debt_Consolidation_Calculator_page',
            'interestCalc.php'                            => 'viewed_Interest_Calculator_page',
            'loanAmortizationCalc.php'                    => 'viewed_Loan_Amortization_Calculator_page',
            'loanCalc.php'                                => 'viewed_Loan_Calculator_page',
            'mortgageCalc.php'                            => 'viewed_Mortgage_Calculator_page',
            'pricingEngine.php'                           => 'viewed_Pricing_Engine_page',
            'processorList'                               => 'viewed_bo_user_list_page',
            'createProcessor.php'                         => 'viewed_create/edit_bo_user_page',
            'branchList.php'                              => 'viewed_branch_user_list_page',
            'createBranch.php'                            => 'viewed_create/edit_branch_info/user_page',
            'createBranch.php?tabNumb=1'                  => 'viewed_create/edit_branch_info/user_page',
            'createBranch.php?tabNumb=2'                  => 'viewed_branch_Docs_Library_page',
            'createBranch.php?tabNumb=9'                  => 'viewed_branch_Webform_Integration_page',
            'createBranch.php?tabNumb=11'                 => 'viewed_branch_Required_Docs_page',
            'brokers'                                     => 'viewed_broker_user_list_page',
            'createAgent.php'                             => 'viewed_create/edit_broker_agent_user_page',
            'loan_officers'                               => 'viewed_lo_user_list_page',
            'REGEX:/createAgent\.php.*[?&]agentType=1\b/' => 'viewed_create/edit_lo_user_page',
            'LMRequest.php?eOpt=0&cliType=PC&tabOpt=QAPP' => 'viewed_create_loan_QA_page',
            'LMRequest.php?eOpt=0&cliType=PC&tabOpt=LI'   => 'viewed_create_loan_FA_page',
            'LMRequest.php?eOpt=0'                        => 'viewed_create_loan_new_file_page',
            'myPipeline.php?empId='                       => 'viewed_my_assigned_pipeline_page',
            'myPipeline.php?fileType=b92575b6c7c7374f'    => 'viewed_H.O.M.E._pipeline_page',
            'myPipeline.php'                              => 'viewed_main_pipeline_page',
            'lenders'                                     => 'viewed_lenders/servicers_list_page',
            'lenders/create'                              => 'viewed_lenders_create/edit_page',
            'contacts'                                    => 'viewed_contacts_list_page',
            'contacts?create'                             => 'viewed_create_contacts_page_from_menu',  //there's also a modal from once you are in list view.  It will show as "Added or edited a contact"
            'clients'                                     => 'viewed_borrowers_list_page',
            'clientCreate.php'                            => 'viewed_borrowers_edit/create_page',
            //'createProcessingCompany.php'                 => 'viewed_settings>Company_Info_page',
        ];

// Sort the array keys by length, descending
        uksort($uriChecks, function ($a, $b) {
            return strlen($b) - strlen($a);
        });

        foreach ($uriChecks as $needle => $eventName) {
            $matched = false;

            if (strpos($needle, 'REGEX:') === 0) {
                $pattern = substr($needle, 6); // Remove 'REGEX:' prefix
                if (preg_match($pattern, $_SERVER['REQUEST_URI'])) {
                    $matched = true;
                }
            } else {
                if (strpos($_SERVER['REQUEST_URI'], $needle) !== false) {
                    $matched = true;
                }
            }

            if ($matched) {
                $script .= <<<SCRIPT
<script type="text/javascript">
    _ctrack.track("{$eventName}", {
        "page": "{$_SERVER['REQUEST_URI']}"
    });
</script>
SCRIPT;
                break; // Stop after first match
            }
        }


        $tabOpts = [
            'HUD'    => 'viewed_File_HUD_tab',
            'TA'     => 'viewed_File_Task_tab',
            'CON'    => 'viewed_File_Contacts_tab',
            'TPS'    => 'viewed_File_3rdparty_tab',
            'MP'     => 'viewed_File_Marketplace_tab',
            'SO'     => 'viewed_File_Offers_tab',
            'DOC'    => 'viewed_File_Docs_tab',
            'CI'     => 'viewed_File_Client_Info_tab',
            'MI'     => 'viewed_File_Mortgage_Info_tab',
            'PI'     => 'viewed_File_Property_Info_tab',
            'QA'     => 'viewed_File_Q+A_tab',
            'IE'     => 'viewed_File_Income&Expenses_tab',
            'SLM'    => 'viewed_File_Student_tab',
            'HA'     => 'viewed_File_Hardship_tab',
            'LA'     => 'viewed_File_Loan_Audit_tab',
            'LP'     => 'viewed_File_LM_Proposal_tab',
            'CFPB'   => 'viewed_File_CFPB_Audit_tab',
            'BC'     => 'viewed_File_Billing_&_Com_tab',
            'SS'     => 'viewed_File_Short_Sale_tab',
            'SSP'    => 'viewed_File_Short_Sale_Proposal_tab',
            'LE'     => 'viewed_File_Legal_tab',
            'ADMIN'  => 'viewed_File_Admin_tab',
            'AL'     => 'viewed_File_Assets_&_Liabilities_tab',
            'HR'     => 'viewed_File_HOME_Report_tab',
            'CW'     => 'viewed_File_Workflow_tab',
            'INT'    => 'viewed_File_Intake_tab',
            'ER'     => 'viewed_File_Estimated_Repairs_tab',
            'EXP'    => 'viewed_File_Explanation_tab',
            'RAM'    => 'viewed_File_RAM_tab',
            'DASH'   => 'viewed_File_SS_Dashboard_tab',
            'LSS'    => 'viewed_File_Summary_tab',
            'LI'     => 'viewed_File_Full_App_tab',
            'SER'    => 'viewed_File_Servicing_tab',
            'MFO'    => 'viewed_File_Merchant_Offers_tab',
            'HMLI'   => 'viewed_File_Loan_Info_tab',
            'LT'     => 'viewed_File_Loan_Terms_tab',
            'DA'     => 'viewed_File_Deal_Analysis_tab',
            'GOVT'   => 'viewed_File_HMDA_tab',
            'REST'   => 'viewed_File_REST_tab',
            'SP'     => 'viewed_File_SS_Proposal_tab',
            'SSS'    => 'viewed_File_Summary_tab',
            'FUEI'   => 'viewed_File_Entity_Info_tab',
            'FUCE'   => 'viewed_File_Credit_Enhancement_tab',
            'EF'     => 'viewed_File_Equipment_tab',
            'QAPP'   => 'viewed_File_Quick_App_tab',
            'BD'     => 'viewed_File_Budget_&_Draws_tab',
            'PE'     => 'viewed_File_Sell_Through_PeerStreet_tab',
            'AW'     => 'viewed_File_CallWise_tab',
            'LSTPGE' => 'viewed_File_Listing_tab',
            'MEMO'   => 'viewed_File_Credit_Memo_tab',
            'EM'     => 'viewed_File_Email_Sync_tab',
            'SER2'   => 'viewed_File_Servicing_2.0_tab',
            'PEN'    => 'viewed_File_Pricing_Engine_tab',
            '1003'   => 'viewed_File_1003_tab',
            'NOI'    => 'viewed_File_Adverse_Action_NOI_tab',
            'DS'     => 'viewed_File_Deal_Sizer_tab',
            'CD'     => 'viewed_File_Credit_Decision_tab',
            'LIV2'   => 'viewed_File_Loan_Info_V2_tab',
            'PCLT'   => 'viewed_File_Post-Closing_Loan_Trade_tab',
        ];

        if (strpos($_SERVER['REQUEST_URI'], 'LMRequest.php') !== false) {
            foreach ($tabOpts as $opt => $eventName) {
                if (preg_match("/\btabOpt={$opt}\b/", $_SERVER['REQUEST_URI'])) {
                    $script .= <<<SCRIPT
<script type="text/javascript">
    _ctrack.track("{$eventName}", {
        "page": "{$_SERVER['REQUEST_URI']}"
    });
</script>
SCRIPT;
                }
            }
        }

        //click events

        //stickey notes, global search, activity log etc code needs to be on every page
        $script .= <<<SCRIPT
<script type="text/javascript">
    $(document).ready(function () {
        const trackEvents = [
            { selector: '#stickeyNotes', event: 'Stickey Notes Clicked', meta: 'sticky icon clicked' },
            { selector: '#kt_quick_panel_toggle', event: 'Activity Feed Icon Clicked', meta: 'Activity Feed Icon clicked' },
            { selector: '#globalSearch', event: 'Global Search Clicked', meta: 'Global Search Icon clicked' },
            { selector: '#menu_releasenotes', event: 'Visited Release Notes', meta: 'Release Notes menu item clicked' },
            { selector: '.custifyEditContact', event: 'Edited a Contact', meta: 'edited a Contact' },
            { selector: '#exportData', event: 'Exported Client File Data', meta: 'Exported Client File Data' },
            { selector: '#exportNotesData', event: 'Exported Note History', meta: 'Exported Note History' },
            { selector: '#exportDataSaveReport', event: 'Saved as Report and Downloaded Client File Date', meta: 'Saved as Report and Downloaded Client File Date' }
        ];

        trackEvents.forEach(item => {
            $(item.selector).click(function () {
                _ctrack.track(item.event, {
                    "METADATA1": item.meta,
                });
            });
        });
    });
</script>
SCRIPT;

        //now page specific for loan file tabs
        if (strpos($_SERVER['REQUEST_URI'], 'LMRequest.php') !== false) {
            $script .= <<<SCRIPT
<script type="text/javascript">
    $(document).ready(function () {
        const trackEvents = [
            {
                selector: '#notesIcon',
                event: 'Notes Icon Clicked',
                meta: 'Notes Icon Clicked',
                trackHover: true,
                hoverEvent: 'Notes Icon Hovered',
                hoverMeta: 'Hovered over Notes Icon'
            },
            {
                selector: '#sendEmail',
                event: 'Send Email Icon Clicked',
                meta: 'Send Email icon clicked in loan file'
            },
            {
                selector: '#mailHistory',
                event: 'Email Sent History Clicked',
                meta: 'Email sent history icon clicked in loan file'
            },
            {
                selector: '#clientNotes',
                event: 'Client Notes Icon Clicked',
                meta: 'Client Notes Icon Clicked',
                trackHover: true,
                hoverEvent: 'Client Notes Icon Hovered',
                hoverMeta: 'Hovered over Client Notes Icon'
            },
            {
                selector: '#fm34Export',
                event: 'Fannie Mae Export Icon Clicked',
                meta: 'Fannie Mae Export Icon Clicked'
            },
            {
                selector: '#fm34Import',
                event: 'Fannie Mae Import Icon Clicked',
                meta: 'Fannie Mae Import Icon Clicked'
            },
            {
                selector: '#shareThisFile',
                event: 'Share This File Icon Clicked',
                meta: 'Share This File Icon Clicked'
            },
            {
                selector: '#favoriteRemove',
                event: 'remove from favorites Icon Clicked',
                meta: 'remove from favorites Icon Clicked'
            },
            {
                selector: '#favoriteAdd',
                event: 'add to favorites icon clicked (star)',
                meta: 'add to favorites icon clicked (star)'
            },
            {
                selector: '.custifyMPSearch',
                event: 'used marketplace',
                meta: 'used marketplace'
            },
        ];

        const hoverCooldowns = {};

        trackEvents.forEach(item => {
            // Always track click
            $(item.selector).on('click', function () {
                _ctrack.track(item.event, {
                    "METADATA1": item.meta,
                });
            });

            if (item.trackHover) {
                $(item.selector).each(function () {
                    const el = $(this);
                    let hoverTimer = null;
                    let lastTracked = 0;

                    el.hover(
                        function () {
                            const now = Date.now();
                            if (now - lastTracked < 30000) return;

                            hoverTimer = setTimeout(() => {
                                _ctrack.track(item.hoverEvent || (item.event + ' (hover)'), {
                                    "METADATA1": item.hoverMeta || ('Hovered - ' + item.meta),
                                });
                                lastTracked = Date.now();
                            }, 1000);
                        },
                        function () {
                            clearTimeout(hoverTimer);
                        }
                    );
                });
            }
        });
    });
</script>
SCRIPT;
        }


        return $script;
    }

    /**
     * @return string|null
     */
    public static function addTaskFooter(): ?string
    {
        $PCID = PageVariables::$PCID;
        if (($_ENV['CUSTIFY'] ?? 0) == 0 || $PCID == glPCID::PCID_PROD_CV3) {
            return null;
        }
        $script = <<<SCRIPT
<script type="text/javascript">
    _ctrack.track("viewed_create_client_task/reminder_popup_page", {
        "page": "{$_SERVER['REQUEST_URI']}"
    });
</script>
SCRIPT;

        return $script;
    }

    /**
     * @param string $softCreditPullStatus
     * @return string|null
     */
    public static function creditPullFooter(string $softCreditPullStatus): ?string
    {
        $PCID = PageVariables::$PCID;
        if (($_ENV['CUSTIFY'] ?? 0) == 0 || $PCID == glPCID::PCID_PROD_CV3) {
            return null;
        }
        if (strpos($_SERVER['REQUEST_URI'], 'creditScreening.php') !== false) {
            switch ($softCreditPullStatus) {
                case 'operational':
                    $script = <<<SCRIPT
<script type="text/javascript">
    _ctrack.track("viewed_credit_pulls_(operational)_page", {
        "page": "{$_SERVER['REQUEST_URI']}"
    });
</script>
SCRIPT;
                    break;

                case 'notSetup':
                    $script = <<<SCRIPT
<script type="text/javascript">
    _ctrack.track("viewed_credit_pulls_(locked/activate)_page", {
        "page": "{$_SERVER['REQUEST_URI']}"
    });
</script>
SCRIPT;
                    break;

                case 'setupButNotConfigured':
                    $script = <<<SCRIPT
<script type="text/javascript">
    _ctrack.track("viewed_credit_pulls_(unlocked_but_not_configured)_page", {
        "page": "{$_SERVER['REQUEST_URI']}"
    });
</script>
SCRIPT;
                    break;

                default:
                    // Optional: handle unknown statuses
                    break;
            }
        }

        return $script;
    }

    /**
     * @return string|null
     */
    public static function trackSaves(): ?string
    {
        $PCID = PageVariables::$PCID;
        if (($_ENV['CUSTIFY'] ?? 0) == 0 || $PCID == glPCID::PCID_PROD_CV3) {
            return null;
        }

        //dont load on some pages
        $requestUri = $_SERVER['REQUEST_URI'];

        $excludePatterns = [
            'LMRequest.php',
            'testpage2.php',
            // Regex patterns (use delimiters and anchors as needed)
            '#createProcessingCompany\.php\?[^ ]*tabNumb=1#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=2#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=3#',
//            '#createProcessingCompany\.php\?[^ ]*tabNumb=4#',  //req docs
//            '#createProcessingCompany\.php\?[^ ]*tabNumb=5#',  //workflow
//            '#createProcessingCompany\.php\?[^ ]*tabNumb=6#',  //status
//            '#createProcessingCompany\.php\?[^ ]*tabNumb=7#',    //substatus
            '#createProcessingCompany\.php\?[^ ]*tabNumb=8#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=9#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=10#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=11#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=12#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=13#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=14#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=15#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=16#',  //formfields
            '#createProcessingCompany\.php\?[^ ]*tabNumb=17#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=18#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=19#',
            '#createProcessingCompany\.php\?[^ ]*tabNumb=20#',
            '#createBranch\.php\?[^ ]*tabNumb=1#',
//            '#createBranch\.php\?[^ ]*tabNumb=2#',  //autogenerated docs needs breeakout for all tabs
            '#createBranch\.php\?[^ ]*tabNumb=3#',
            '#createBranch\.php\?[^ ]*tabNumb=4#',
            '#createBranch\.php\?[^ ]*tabNumb=5#',
            '#createBranch\.php\?[^ ]*tabNumb=6#',
            '#createBranch\.php\?[^ ]*tabNumb=7#',
            '#createBranch\.php\?[^ ]*tabNumb=8#',
//            '#createBranch\.php\?[^ ]*tabNumb=9#',  //web form integration
            '#createBranch\.php\?[^ ]*tabNumb=10#',
            '#createBranch\.php\?[^ ]*tabNumb=11#',
        ];

        $excluded = false;

        foreach ($excludePatterns as $pattern) {
            if (str_starts_with($pattern, '#') && str_ends_with($pattern, '#')) {
                if (preg_match($pattern, $requestUri)) {
                    $excluded = true;
                    break;
                }
            } else {
                if (strpos($requestUri, $pattern) !== false) {
                    $excluded = true;
                    break;
                }
            }
        }

        if ($excluded) {
            return '';
        }

        $script = <<<SCRIPT
<script type="text/javascript">
  $(document).ready(function () {
    const STORAGE_KEY = 'trackingPayload';
    const PREV_STORAGE_KEY = 'trackingPayload_prev';

    let currentPayload = JSON.parse(localStorage.getItem(STORAGE_KEY)) || {};
    let previousPayload = JSON.parse(localStorage.getItem(PREV_STORAGE_KEY)) || {};

    const added = {};
    const changed = {};

    Object.keys(currentPayload).forEach(function (key) {
      if (!(key in previousPayload)) {
        added[key] = currentPayload[key];
      } else if (currentPayload[key] !== previousPayload[key]) {
        changed[key] = currentPayload[key];
      }
    });

    if (Object.keys(added).length > 0 || Object.keys(changed).length > 0) {
      const toMetadata = function (obj) {
        return Object.entries(obj)
          .map(function (entry) {
            return '"' + entry[0] + '": ' + JSON.stringify(entry[1]);
          })
          .join(",\\n  ");
      };

      const addedMeta = toMetadata(added);
      const changedMeta = toMetadata(changed);

      const injectedScript = document.createElement("script");
      injectedScript.type = "text/javascript";
      
const urlParams = new URLSearchParams(window.location.search);
const tabNumb = urlParams.get('tabNumb');

// Remove "/backoffice/" from the pathname
let currentPath = window.location.pathname.replace('/backoffice/', '');

if (tabNumb !== null) {
  currentPath += `?tabNumb=\${tabNumb}`;
}

// Escape double quotes for safe use in injected script
currentPath = currentPath.replace(/"/g, '\\\\"');

// Replace with friendly name if matches specific pattern
if (currentPath === 'createProcessingCompany.php?tabNumb=4') {
  currentPath = 'Required Docs';
} else if (currentPath === 'createProcessingCompany.php?tabNumb=6') {
  currentPath = 'Statuses';
} else if (currentPath === 'createProcessingCompany.php?tabNumb=7') {
  currentPath = 'Sub_Statuses';
} else if (currentPath === 'createProcessingCompany.php?tabNumb=5') {
  currentPath = 'Workflows';
} else if (currentPath === 'createProcessingCompany.php?tabNumb=16') {
  currentPath = 'FormFields';
} else if (currentPath === 'createBranch.php?tabNumb=9') {
  currentPath = 'WebFormIntegration';
} else if (currentPath === 'createBranch.php?tabNumb=2') {
  currentPath = 'AutoGeneratedDocLibrary';
}

      let trackingText = "";

      if (Object.keys(added).length > 0) {
        //trackingText += "_ctrack.track(\\"Added Values - " + currentPath + "\\", {\\n  " + addedMeta + "\\n});\\n";
        trackingText += "_ctrack.track(\\"Added/Changed Values - " + currentPath + "\\", {\\n  " + addedMeta + "\\n});\\n";
        //lets make same event name for less work for oskar, this chunk of 9 lines can be reduced to 3 
      }
      if (Object.keys(changed).length > 0) {
        //trackingText += "_ctrack.track(\\"Changed Values - " + currentPath + "\\", {\\n  " + changedMeta + "\\n});\\n";
        trackingText += "_ctrack.track(\\"Added/Changed Values - " + currentPath + "\\", {\\n  " + addedMeta + "\\n});\\n";
      }

      trackingText += "localStorage.removeItem(\\"trackingPayload\\");\\n";
      trackingText += "localStorage.removeItem(\\"trackingPayload_prev\\");";

      injectedScript.text = trackingText;
      document.head.appendChild(injectedScript);
    }

    $(document).on('change input', 'form :input[name]', function () {
      const name = $(this).attr('name');
      let value;

      if ($(this).is(':checkbox')) {
        value = $(this).is(':checked') ? $(this).val() : null;
      } else if ($(this).is(':radio')) {
        if ($(this).is(':checked')) {
          value = $(this).val();
        } else {
          return;
        }
      } else {
        value = $(this).val();
      }

      if (value !== null) {
        currentPayload[name] = value;
      } else {
        delete currentPayload[name];
      }

      if (!localStorage.getItem(PREV_STORAGE_KEY)) {
        localStorage.setItem(PREV_STORAGE_KEY, JSON.stringify(currentPayload));
      }

      localStorage.setItem(STORAGE_KEY, JSON.stringify(currentPayload));
    });
  });
</script>
SCRIPT;

        return $script;
    }


}
