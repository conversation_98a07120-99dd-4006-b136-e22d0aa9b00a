<?php

namespace models\standard;

use DateInterval;
use DateMalformedStringException;
use DatePeriod;
use DateTime;
use DateTimeZone;
use Exception;
use models\constants\timeDiffArray;
use models\Controllers\backoffice\LMRequest;
use models\servicing\LoanTerms;

/**
 *
 */
class Dates
{

    public static function IsWeekend($date): bool
    {
        // https://stackoverflow.com/questions/4802335/checking-if-date-is-weekend-php
        return (date('N', strtotime(self::Datestamp($date))) >= 6);
    }

    public static function FancyTimestamp($date): ?string
    {
        return self::Datestamp($date, '', 'M j, Y h:i A');
    }

    public static function SignatureTimestamp()
    {
        return date('D M j Y H:i:s \G\M\T O (T)');
    }

    /**
     * @param string|null $StartDate
     * @param string|null $EndDate
     * @param bool $inclusive
     * @return array
     */
    public static function GetMonthsDays(
        ?string $StartDate,
        ?string $EndDate,
        bool    $inclusive = false
    ): array
    {
        // Create DateTime objects from the input dates
        try {
            if(is_numeric($StartDate)) {
                $StartDate = Dates::Timestamp($StartDate);
            }

            if(is_numeric($EndDate)) {
                $EndDate = Dates::Timestamp($EndDate);
            }

            $start = new DateTime($StartDate);
            $end = new DateTime($EndDate);

            if ($inclusive) {
                $end->add(DateInterval::createFromDateString('1 day'));
            }

            // Get the difference as a DateInterval object
            $diff = $start->diff($end);

            // Extract months and days from the DateInterval
            return [
                'months'     => $diff->y * 12 + $diff->m, // Convert years to months and add months
                'days'       => $diff->d,
                'actualDays' => $diff->days,
            ];

        } catch (Exception $e) {
            Debug('Bad Date Format', $e->getMessage());
        }

        return [
            'months'     => 0,
            'days'       => 0,
            'actualDays' => 0,
        ];
    }

    /**
     * @param $start_date
     * @param $end_date
     * @param bool $inclusive
     * @return int
     */
    public static function DaysDiff($start_date, $end_date, bool $inclusive = false): int
    {
        $start_date = strtotime(self::Datestamp($start_date));
        $end_date = strtotime(self::Datestamp($end_date));
        return intval(floor(($end_date - $start_date) / 3600.0 / 24.0) + ($inclusive ? 1 : 0));
    }

    /**
     * @param string|null $Start
     * @param string|null $End
     * @param bool $inclusive
     * @return int
     */
    public static function MonthsBetween(
        ?string $Start,
        ?string $End,
        bool    $inclusive = false
    ): int
    {
        try {
            $date1 = self::Datestamp($Start);
            $date2 = self::Datestamp($End);
            if ($inclusive) {
                $date2 = Dates::Datestamp(strtotime('+1 day', strtotime($date2)));
            }
            $d1 = new DateTime($date2);
            $d2 = new DateTime($date1);
            $Months = $d2->diff($d1);

            return (strtotime($date1) > strtotime($date2) ? -1 : 1)
                * ((($Months->y) * 12) + ($Months->m));
        } catch (Exception $e) {
            Debug($e);
        }
        return 0;
    }

    /**
     * @param $date
     * @param null $null
     * @return int|null
     */
    public static function DateToInt($date, $null = null)
    {
        if ($date instanceof DateTime) {
            $temp = $date->getTimestamp();
            $str = $date->format('Y-m-d H:i:s');

            if (!$temp && !$str) { // don't interpret 1970-01-01 as not set
                return $null;
            }
            return $temp;
        }

        if (!$date) {
            return $null;
        }

        if (!is_numeric($date)) {
            $date = strtotime($date);
        }

        return $date;
    }

    /**
     * @param null $date
     * @param null $null
     * @param string $format
     * @param null $offset
     * @return ?string
     */
    public static function Datestamp($date = null, $null = null, string $format = 'Y-m-d', $offset = null): ?string
    {
        if ($date === '0000-00-00') {
            return null;
        }

        if ($date === '0000-00-00 00:00:00') {
            return null;
        }

        if (!$format) {
            $format = 'Y-m-d';
        }

        if (is_null($null) && is_null($date)) {
            $date = time();
        }
        $date = self::DateToInt($date, $null);
        if ($date === $null) {
            return $null;
        }

        if (!$date && $null) {
            return $null;
        }

        if ($offset) {
            $date += $offset * 3600;
        }
        $date = date($format, $date);
        if ($date && $date[0] == '-') {
            $date = null;
        }
        return $date;
    }

    /**
     * @param null $date
     * @param null $null
     * @param bool $zeroIsNull
     * @return false|null|string
     */
    public static function StandardDate(
        $date = null,
        $null = null,
        bool $zeroIsNull = false
    ): ?string
    {
        if ($zeroIsNull && (
                $date === '0000-00-00'
                || $date === '0000-00-00 00:00:00'
            )) {
            return $null;
        }

        return self::Datestamp($date, $null, 'm/d/Y');
    }

    /**
     * @param null $date
     * @param null $null
     * @param bool $zeroIsNull
     * @return false|null|string
     */
    public static function StandardDateTime(
        $date = null,
        $null = null,
        bool $zeroIsNull = false
    ): ?string
    {
        if ($zeroIsNull && $date === '0000-00-00') {
            return $null;
        }

        return self::Datestamp($date, $null, 'n/j/Y h:ia');
    }

    /**
     * @param null $date
     * @param null $null
     * @param string $format
     * @return string
     */
    public static function Timestamp($date = null, $null = null, string $format = 'Y-m-d H:i:s'): ?string
    {
        if (!$format) {
            $format = 'Y-m-d H:i:s';
        }
        return self::Datestamp($date, $null, $format);
    }

    public static function dateFormatDatabase($inputDate): string
    {
        if (Dates::IsEmpty($inputDate)) {
            $outputDate = '0000-00-00';
        } else {
            $outputDate = self::formatDateWithRE($inputDate, 'MDY', 'Y-m-d');
        }
        return $outputDate;
    }

    /**
     * @param $fieldName
     * @param $hr
     * @param $min
     * @param $ampm
     * @param $tabIndex1
     * @param $tabIndex2
     * @param $tabIndex3
     * @return string
     */
    public static function displayTime($fieldName, $hr, $min, $ampm, $tabIndex1, $tabIndex2, $tabIndex3): string
    {
        //$strHour = "";
        $strHour = "<select class=\"form-control col-md-3\"  tabindex=\"" . $tabIndex1 . "\" id=\"" . $fieldName . "_hr\" name=\"" . $fieldName . "_hr\" size=\"1\" >";
        for ($h = 1; $h <= 12; $h++) {
            if ($h < 10) {
                $strHour .= "<option value=\"0" . $h . "\"";
            } else {
                $strHour .= "<option value=\"" . $h . "\"";
            }
            if ($hr == $h) {
                $strHour .= ' selected';
            }
            $strHour .= '>' . $h . '</option>';
        }
        $strHour .= '</select>&nbsp;';
        $strHour .= "<select class=\"form-control col-md-3\" tabindex=\"" . $tabIndex2 . "\" id=\"" . $fieldName . "_min\" name=\"" . $fieldName . "_min\" size=\"1\" >";
        for ($m = 0; $m <= 5; $m++) {
            if ($m == 0) {
                $strHour .= "<option value=\"0" . $m . "\"";
            } else {
                $strHour .= "<option value=\"" . ($m * 10) . "\"";
            }
            if (($min == $m * 10) || ($min > $m * 10)) {
                $strHour .= ' selected ';
            }
            if ($m == 0) {
                $strHour .= '>00</option>';
            } else {
                $strHour .= '>' . ($m * 10) . '</option>';
            }
        }
        $strHour .= '</select>&nbsp;';
        $strHour .= "<select class=\"form-control col-md-4 \" tabindex=\"" . $tabIndex3 . "\" id=\"" . $fieldName . "_ampm\"  name=\"" . $fieldName . "_ampm\" size=\"1\" >";
        $strHour .= "<option value=\"AM\"";
        if ($ampm == 'AM') $strHour .= ' selected ';
        $strHour .= '>AM</option>';
        $strHour .= "<option value=\"PM\"";
        if ($ampm == 'PM') $strHour .= ' selected ';
        $strHour .= '>PM</option>';
        $strHour .= '</select>';
        return $strHour;
    }

    /**
     * @param $myDt
     * @return false|string
     */
    public static function formatDateMDYHMA($myDt)
    {
        if ($myDt != '') {
            $myDt = explode(' ', $myDt);
            $dtArray = preg_split('/[\/.-]/', $myDt[0]);
            $timeArray = preg_split('/[\/.:-]/', $myDt[1]);
            $sy = $dtArray[0];
            $sm = $dtArray[1];
            $sd = $dtArray[2];
            $formattedDt = date('M j, Y h:i A', mktime($timeArray[0], $timeArray[1], $timeArray[2], date($sm), date($sd), date($sy)));
        } else {
            $formattedDt = '';
        }
        return $formattedDt;
    }

    /**
     * @param $myDt
     * @return false|string
     */
    public static function formatDateMDY($myDt)
    {
        $formattedDt = '';
        if ($myDt != '') {
            $myDt = explode(' ', $myDt);
            $dtArray = preg_split('/[\/.-]/', $myDt[0]);
            $sy = $dtArray[0];
            $sm = $dtArray[1];
            $sd = $dtArray[2];
            $formattedDt = date('M j, Y', mktime(0, 0, 0, date($sm), date($sd), date($sy)));
        } else {
            $formattedDt = '';
        }
        return $formattedDt;
    }

    /**
     * @param $date
     * @return false|string
     */
    public static function formatDateYYYYMMDDHIA($date)
    {
        $formattedDt = '';
        $dateREArray = [];
        $h = 0;
        $min = 0;
        $s = 00;
        $dateREArray = preg_split('/[-\.\/ ]/', $date);
        if (count($dateREArray) >= 4) {
            list($m, $d, $y, $hm, $a) = preg_split('/[-\.\/ ]/', $date);
            list($h, $min) = explode(':', $hm);
            if (strtolower($a) == 'pm') {
                if ($h < 12) {
                    $h = $h + 12;
                }
            }
            $formattedDt = self::formatDateTimeWithRE($date, $m, $d, $y, $h, $min, $s);
        }
        return $formattedDt;
    }

    /**
     * @param $myDt
     * @param $sm
     * @param $sd
     * @param $sy
     * @param $h
     * @param $m
     * @param $s
     * @return false|string
     */
    public static function formatDateTimeWithRE($myDt, $sm, $sd, $sy, $h, $m, $s)
    {
        $formattedDt = '';
        if ($myDt != '') {
            if (checkdate($sm, $sd, $sy)) {
                $formattedDt = date('Y-m-d H:i:s', mktime(date($h), date($m), date($s), date($sm), date($sd), date($sy)));
            }
        } else {
            $formattedDt = '';
        }
        return $formattedDt;
    }

    /**
     * @param $myDt
     * @return false|string
     */
    public static function formatDateMMDDYYYY($myDt)
    {
        $formattedDt = '';
        if ($myDt != '') {
            $myDt = explode(' ', $myDt);
            $dtArray = preg_split('/[\/.-]/', $myDt[0]);
            $sy = $dtArray[0];
            $sm = $dtArray[1];
            $sd = $dtArray[2];
            $formattedDt = date('m/d/Y', mktime(0, 0, 0, date($sm), date($sd), date($sy)));
        } else {
            $formattedDt = '';
        }
        return $formattedDt;
    }

    /**
     * @param $myDt
     * @return false|string
     */
    public static function formatDateYYYYMMDDWithRE($myDt)
    {
        $formattedDt = '';
        if (trim($myDt) != '') {
            $dateREArray = [];
            $dateREArray = preg_split('/[\/.-]/', $myDt);
            if (count($dateREArray) >= 3) {
                list($sm, $sd, $sy) = preg_split('/[\/.-]/', $myDt);
                if (checkdate($sm, $sd, $sy)) {
                    $formattedDt = date('Y-m-d', mktime(0, 0, 0, date($sm), date($sd), date($sy)));
                }
            }
        } else {
            $formattedDt = '';
        }
        return $formattedDt;
    }

    /**
     * @param $myDt
     * @return false|string
     */
    public static function formatDateMMDDYYYYWithRE($myDt)
    {
        $formattedDt = '';
        if (trim($myDt) != '') {
            $dateREArray = [];
            $dateREArray = preg_split('/[\/.-]/', $myDt);
            if (count($dateREArray) >= 3) {
                [$sy, $sm, $sd] = preg_split('/[\/.-]/', $myDt);
                if (checkdate($sm, $sd, $sy)) {
                    $formattedDt = date('m-d-Y', mktime(0, 0, 0, date($sm), date($sd), date($sy)));
                }
            }
        } else {
            $formattedDt = '';
        }
        return $formattedDt;
    }

    /**
     * @param $myDt
     * @return false|string
     */
    public static function formatDateMDYWithRE($myDt)
    {
        $formattedDt = '';
        if ($myDt != '') {
            $dateREArray = [];
            $dateREArray = preg_split('/[\/.-]/', $myDt);
            if (count($dateREArray) >= 3) {
                [$sy, $sm, $sd] = preg_split('[\/.-]', $myDt);
                if (checkdate($sm, $sd, $sy)) {
                    $formattedDt = date('M j, Y', mktime(0, 0, 0, date($sm), date($sd), date($sy)));
                }
            }
        } else {
            $formattedDt = '';
        }
        return $formattedDt;
    }

    /**
     * @param $myDt
     * @param $inFormat
     * @param $outFormat
     * @param string $defaultValue
     * @return false|string
     */
    public static function formatDateWithRE($myDt, $inFormat, $outFormat, string $defaultValue = '')
    {
        if (!is_string($myDt)) {
            return $defaultValue;
        }
        if (preg_match('/\d{4}-\d{2}-\d{2}/si', $myDt)) {
            if (stristr($myDt, '0000-00-00') !== false) {
                return $defaultValue;
            }
            return Dates::Timestamp($myDt, $defaultValue, $outFormat);
        }

        $formattedDt = $defaultValue;
        $hr = 0;
        $min = 0;
        $sec = 0;
        $sm = 0;
        $sd = 0;
        $sy = 0;
        if (
            trim($myDt)
            && (trim($myDt) != '0000-00-00 00:00:00')
            && (trim($myDt) != '00-00-0000 00:00:00')
            && (trim($myDt) != '00-00-0000')
            && (trim($myDt) != '0000-00-00'
            )
        ) {
            if ($inFormat == 'MDY') {
                @list($sm, $sd, $sy) = @preg_split('/[\/.-]/', $myDt);
            } else if ($inFormat == 'MY') { /* Change for Lead Receiver - Intake data for Client Document PC - May 15, 2015 */
                $sd = 1;
                @list($sm, $sy) = @preg_split('/[\/.-]/', $myDt);
            } else if ($inFormat == 'YMD') {
                [$sy, $sm, $sd] = preg_split('/[\/.-]/', $myDt);
            } else if ($inFormat == 'YMD_HMS') {
                $myDt = preg_split('/ +/', $myDt);
                @list($sy, $sm, $sd) = @preg_split('/[\/.-]/', $myDt[0]);
                @list($hr, $min, $sec) = @preg_split('/[\/.:-]/', $myDt[1]);
            } else if ($inFormat == 'MDY_HMS') {
                $myDt = preg_split('/ +/', $myDt);
                @list($sm, $sd, $sy) = @preg_split('/[\/.-]/', $myDt[0]);
                @list($hr, $min, $sec) = @preg_split('/[\/.:-]/', $myDt[1]);
            } else if ($inFormat == 'MDY_HMA') {
                @list($sm, $sd, $sy, $hm) = @preg_split('/[-\.\/ ]/', $myDt);
                @list($hr, $min) = @explode(':', $hm);
                $sm = intval($sm);
                $sd = intval($sd);
                $sy = intval($sy);
                if (@checkdate($sm, $sd, $sy)) {
                    $formattedDt = @date($outFormat, strtotime($myDt));
                }
            }
            $sm = intval($sm);
            $sd = intval($sd);
            $sy = intval($sy);
            if ($inFormat != 'MDY_HMA') {
                if (@checkdate($sm, $sd, $sy)) {
                    $formattedDt = @date($outFormat, mktime($hr, $min, $sec, date($sm), date($sd), date($sy)));
                }
            }
        }
        return $formattedDt;
    }

    /**
     * @param $myDt
     * @param $sm
     * @param $sd
     * @param $sy
     * @return false|string
     */

    public static function formatDateForLeads($myDt, $sm, $sd, $sy)
    {
        $sm = intval($sm);
        $sd = intval($sd);
        $sy = intval($sy);
        $formattedDt = '';
        if ($myDt != '') {
            if (checkdate($sm, $sd, $sy)) {
                $formattedDt = date('Y-m-d', mktime(0, 0, 0, $sm, $sd, $sy));
            }
        }
        return $formattedDt;
    }

    public static function validateDateWithRE($date, string $format = 'YYYY-MM-DD')
    {
        $formattedDt = '';
        $date = trim($date);
        switch ($format) {
            case 'YYYY/MM/DD':
            case 'YYYY-MM-DD':
                $dateREArray = preg_split('/[-\.\/ ]/', $date);
                if (count($dateREArray) >= 3) {
                    [$y, $m, $d] = preg_split('/[-\.\/ ]/', $date);
                    $formattedDt = self::formatDateForLeads($date, $m, $d, $y);
                }
                break;
            case 'YYYY/DD/MM':
            case 'YYYY-DD-MM':
                $dateREArray = preg_split('/[-\.\/ ]/', $date);
                if (count($dateREArray) >= 3) {
                    [$y, $d, $m] = preg_split('/[-\.\/ ]/', $date);
                    $formattedDt = self:: formatDateForLeads($date, $m, $d, $y);
                }
                break;
            case 'DD-MM-YYYY':
            case 'DD/MM/YYYY':
                $dateREArray = preg_split('/[-\.\/ ]/', $date);
                if (count($dateREArray) >= 3) {
                    [$d, $m, $y] = preg_split('/[-\.\/ ]/', $date);
                    $formattedDt = self:: formatDateForLeads($date, $m, $d, $y);
                }
                break;
            case 'MM-DD-YYYY':
            case 'MM/DD/YYYY':
            case 'MM/DD/YY':
                $dateREArray = preg_split('/[-\.\/ ]/', $date);
                if (count($dateREArray) >= 3) {
                    [$m, $d, $y] = preg_split('/[-\.\/ ]/', $date);
                    $formattedDt = self:: formatDateForLeads($date, $m, $d, $y);
                }
                break;
            case 'YYYYMMDD':
                $y = substr($date, 0, 4);
                $m = substr($date, 4, 2);
                $d = substr($date, 6, 2);
                if (($y != '') || ($m != '') || ($d != '')) {
                    $formattedDt = self:: formatDateForLeads($date, $m, $d, $y);
                }
                break;
            case 'YYYYDDMM':
                $y = substr($date, 0, 4);
                $d = substr($date, 4, 2);
                $m = substr($date, 6, 2);
                if (($y != '') || ($m != '') || ($d != '')) {
                    $formattedDt = self:: formatDateForLeads($date, $m, $d, $y);
                }
                break;
            default:
                $formattedDt = '';
        }
        return $formattedDt;
    }

    /**
     * @param $timeToConvert
     * @param $fromZone
     * @return string
     */
    public static function timeZoneConversionFromPHPTime($timeToConvert, $fromZone): string
    {
        try {
            $partialConverted = new DateTime($timeToConvert, new DateTimeZone($fromZone));
            $partialConverted->setTimezone(new DateTimeZone('America/New_York'));
            return $partialConverted->format('Y-m-d H:i:s');
        } catch (Exception $ex) {
            Debug($ex);
        }
        return $timeToConvert;
    }


    public static function timeZoneConversionParams(string $inputZone, string $outputZone, string $inputTime): string
    {
        $ipArray['inputZone'] = $inputZone;
        $ipArray['outputZone'] = $outputZone;
        $ipArray['inputTime'] = $inputTime;
        return self::timeZoneConversion($ipArray);
    }

    /**
     * @param $inArray
     * @return string
     */
    public static function timeZoneConversion($inArray): string
    {
        $timeDiffArray = timeDiffArray::$timeDiffArray;

        $outputTime = '0000-00-00 00:00:00';
        $dateREArray = [];
        $s = 00;
        $inputZone = trim($inArray['inputZone']);
        $outputZone = trim($inArray['outputZone']);
        $inputTime = trim($inArray['inputTime']);
        if ($outputZone == '') {
            $outputZone = CONST_SERVER_TIME_ZONE;
        }
        if ($inputZone == '') {
            $inputZone = CONST_SERVER_TIME_ZONE;
        }
        if (trim($inputZone) == trim($outputZone)) {
            $outputTime = $inputTime;
        } elseif (array_key_exists($outputZone, $timeDiffArray)) {
            if (!(($inputTime == '0000-00-00 00:00:00') || ($inputTime == '') || ($inputTime == NULL))) {
                $dateREArray = preg_split('/[-\.\/ ]/', $inputTime);
            }
            if (count($dateREArray) >= 3) {
                // note removed daylight savings calculation which was wrong and not updated since 2013 anyway - bdk 2022-09-26
                list($y, $m, $d, $hm) = preg_split('/[-\.\/ ]/', $inputTime);
                list($h, $min) = explode(':', $hm);
                if ($outputZone == CONST_SERVER_TIME_ZONE) {
                    $minuteDiff = trim($timeDiffArray[$inputZone] * 60);
                    /** Correction for daylight savings **/
                    $h = intval($h);
                    $s = intval($s);
                    $m = intval($m);
                    $d = intval($d);
                    $y = intval($y);
                    $outputTime = date('Y-m-d H:i:s', mktime($h, $min - $minuteDiff, $s, $m, $d, $y));
                } else {
                    $minuteDiff = (int)$timeDiffArray[$outputZone] * 60;
                    /** Correction for daylight savings **/
                    $outputTime = date('Y-m-d H:i:s', mktime($h, $min + $minuteDiff, $s, $m, $d, $y));
                }
            }
        }
        return $outputTime;
    }


    /**
     * @param $inArray
     * @return float|int|string
     * @throws Exception
     */
    public static function calculateRemainingMonths($inArray)
    {
        $termsInMonths = 0;
        $remainingMonths = '';
        $crntDate = date('Y-m-d');

        $loanOriginationDate = trim($inArray['loanOriginationDate']);
        $term = trim($inArray['terms']);
        if ($term == LoanTerms::INTEREST_ONLY) {
            $termsInMonths = 12;
        } elseif ($term == 'Remaining Months') {
            $termsInMonths = 0;
        } else {
            if ($term != '') {
                $pos = strpos($term, '/');
                if (($pos == 0) || ($pos == -1)) {
                    $pos = strpos($term, ' ');
                }
                $terms = substr($term, 0, $pos);
                if ($terms <= 0) {
                    $terms = 1;
                }
                if ($terms > 0) {
                    $termsInMonths = $terms * 12;
                }
            }
        }
        if (!Dates::IsEmpty($loanOriginationDate)) {
            $date1 = new DateTime($loanOriginationDate);
            $date2 = new DateTime($crntDate);
            $interval = date_diff($date1, $date2);
            if ($termsInMonths > 0) {
                $remainingMonths = $termsInMonths - (($interval->y * 12) + $interval->m);
            }
        }
        return $remainingMonths;
    }

    public static function calculateNoOfMonthsBehind($inArray)
    {
        $monthsBehind = 0;
        $lastPaymentMade = trim($inArray['lastPaymentMade']);
        $crntDate = date('Y-m-d');
        if (!Dates::IsEmpty($lastPaymentMade)) {
            $d1 = new DateTime($crntDate);
            $d2 = new DateTime($lastPaymentMade);

            $monthsBehind = $d1->diff($d2)->m + ($d1->diff($d2)->y * 12);
        }
        return $monthsBehind;
    }

    /**
     * @param $inArray
     * @return int
     */
    public static function calculateNoOfDaysForTwoDate($inArray): int
    {
        return self::DaysDiff($inArray['startDate'] ?? time(), $inArray['endDate'] ?? time(), $inArray['inclusive'] ?? true);
    }

    /**
     * @param $datetime
     * @param bool $full
     * @return string
     * @throws Exception
     */
    public static function time_elapsed_string($datetime, bool $full = false): string
    {
        $now = new DateTime();
        $ago = null;
        try {
            $ago = new DateTime($datetime);
        } catch (Exception $e) {
        }
        $diff = $now->diff($ago);

        $diff->w = floor($diff->d / 7);
        $diff->d -= $diff->w * 7;

        $string = [
            'y' => 'year',
            'm' => 'month',
            'w' => 'week',
            'd' => 'day',
            'h' => 'hour',
            'i' => 'minute',
            's' => 'second',
        ];
        foreach ($string as $k => &$v) {
            if ($diff->$k) {
                $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
            } else {
                unset($string[$k]);
            }
        }

        if (!$full) $string = array_slice($string, 0, 1);
        return $string ? implode(', ', $string) . ' ago' : 'just now';
    }

    /**
     * @param $date1
     * @param $date2
     * @return array
     */
    public static function getWeekdays($date1, $date2): array
    {
        if (!defined('SATURDAY')) define('SATURDAY', 6);
        if (!defined('SUNDAY')) define('SUNDAY', 0);


        //Array of all Easter Mondays in the given interval

        $yearStart = date('Y', strtotime($date1));
        $yearEnd = date('Y', strtotime($date2));

        for ($i = $yearStart; $i <= $yearEnd; $i++) {
            $easter = date('Y-m-d', easter_date($i));
            list($y, $m, $g) = explode('-', $easter);
            $monday = mktime(0, 0, 0, date($m), date($g) + 1, date($y));
            $easterMondays[] = $monday;
        }

        $start = strtotime($date1);
        $end = strtotime($date2);

        $daysDates = [];
        for ($i = $start; $i <= $end; $i = strtotime('+1 day', $i)) {
            $day = date('w', $i);  // 0=sun, 1=mon, ..., 6=sat
            $mmgg = date('Y-m-d', $i);
            $daysDates[] = [$mmgg, $day];

        }
        return $daysDates;
    }

    /**
     * @return float
     */
    public static function microtime_float(): float
    {
        return microtime(true);
    }

    public static function monthsArrayBetweenDates($startDate, $endDate = null, bool $first = false): array
    {
        if (!$startDate && !$endDate) {
            return [];
        }

        if ($endDate == '') {
            $endDate = date('Y-m-d');
        }

        $start = new DateTime($startDate);
        $start->modify('first day of this month');
        $end = new DateTime($endDate);// date('Y-m-d'));
        $end->modify('first day of next month');
        $interval = DateInterval::createFromDateString('1 month');
        $period = new DatePeriod($start, $interval, $end);

        $monthsBetween = [];
        foreach ($period as $dt) {
            $monthsBetween[] = $dt->format('Y-m') . ($first ? '-01' : '');
        }
        return $monthsBetween;
    }

    public static function DateWithTimeFormat($date)
    {
        $dateArray = explode(' ', $date);
        $date = $dateArray[0];
        if (count($dateArray) > 0) {
            if (array_key_exists('1', $dateArray)) $dateHrArray = explode(':', $dateArray[1]);

            if (count($dateHrArray) > 0) {
                $hr = $dateHrArray[0];
                $min = $dateHrArray[1];
                if ($dateHrArray[0] > 12) {
                    $hr = $hr - 12;
                    $ampm = 'PM';
                } else {
                    $ampm = 'AM';
                    if ($dateHrArray[0] == 12) {
                        $ampm = 'PM';
                    }
                }
            }
        }
        if ($date != '') {
            $date = $date . ' ' . $hr . ':' . $min . ' ' . $ampm;
        }
        return $date;
    }

    public static function IsEmpty(?string $date): bool
    {
        $date = trim($date);
        return !$date || $date == '0000-00-00 00:00:00' || $date == '0000-00-00';
    }
}
