<?php

namespace models;

use models\composite\oFileUpdate\updateFileLastUpdatedDate;
use models\constants\gl\glPCID;
use models\CopyFile\UploadedRequiredDocs;
use models\lendingwise\clientCreditScore;
use models\lendingwise\collateral;
use models\lendingwise\contingentLiabilities;
use models\lendingwise\estimatedProjectCost;
use models\lendingwise\tblACHInfo;
use models\lendingwise\tblAdditionalLienInfo;
use models\lendingwise\tblAddlnLienContactInfo;
use models\lendingwise\tblAssetsInfo;
use models\lendingwise\tblBinder;
use models\lendingwise\tblBorrowerAlternateNames;
use models\lendingwise\tblBoundDocs;
use models\lendingwise\tblBudgetAndDrawDoc;
use models\lendingwise\tblBudgetAndDraws;
use models\lendingwise\tblCollateralValues;
use models\lendingwise\tblCreditMemo;
use models\lendingwise\tblEquipmentInfo;
use models\lendingwise\tblEquipmentInformation;
use models\lendingwise\tblEscrow;
use models\lendingwise\tblFeeSchedule;
use models\lendingwise\tblFile;
use models\lendingwise\tblFile2;
use models\lendingwise\tblFile3;
use models\lendingwise\tblFileAdditionalGuarantors;
use models\lendingwise\tblFileAdditionalLoanPrograms;
use models\lendingwise\tblFileAdminInfo;
use models\lendingwise\tblFileCalculatedValues;
use models\lendingwise\tblFileChecklistModules;
use models\lendingwise\tblFileChecklistRequiredBy;
use models\lendingwise\tblFileClients;
use models\lendingwise\tblFileContacts;
use models\lendingwise\tblFileCopy;
use models\lendingwise\tblFileCourtInfo;
use models\lendingwise\tblFileExpFilpGroundUp;
use models\lendingwise\tblFileExtensionOptions;
use models\lendingwise\tblFileExteriorWorkInfo;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblFileHMLOAssetsInfo;
use models\lendingwise\tblFileHMLOBackGround;
use models\lendingwise\tblFileHMLOBusinessEntity;
use models\lendingwise\tblFileHMLOBusinessEntityRef;
use models\lendingwise\tblFileHMLOExperience;
use models\lendingwise\tblFileHMLOListOfRepairs;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblFileHMLORehabInfo;
use models\lendingwise\tblFileHUDBasicInfo;
use models\lendingwise\tblFileHUDSettlementCharges;
use models\lendingwise\tblFileHUDTransaction;
use models\lendingwise\tblFileHUDType;
use models\lendingwise\tblFileInteriorWorkInfo;
use models\lendingwise\tblFileInternalLoanPrograms;
use models\lendingwise\tblFileLegalBankruptcy;
use models\lendingwise\tblFileLoanOrigination;
use models\lendingwise\tblFileLOAssetsInfo;
use models\lendingwise\tblFileLOBorEmploymentInfo;
use models\lendingwise\tblFileLOChekingSavingInfo;
use models\lendingwise\tblFileLOCoBEmploymentInfo;
use models\lendingwise\tblFileLOExpensesInfo;
use models\lendingwise\tblFileLOExplanation;
use models\lendingwise\tblFileLOLiabilitiesInfo;
use models\lendingwise\tblFileLOOtherCredits;
use models\lendingwise\tblFileLOPropertyInfo;
use models\lendingwise\tblFileLOScheduleRealInfo;
use models\lendingwise\tblFileMFLoanTerms;
use models\lendingwise\tblFileModules;
use models\lendingwise\tblFileOtherWorkInfo;
use models\lendingwise\tblFilePayee;
use models\lendingwise\tblFilePreliminaryWorkInfo;
use models\lendingwise\tblFilePropertyInfo;
use models\lendingwise\tblFilePropertyInfo2;
use models\lendingwise\tblFileRentRoll;
use models\lendingwise\tblFileResponse;
use models\lendingwise\tblFileSBAQuestions;
use models\lendingwise\tblFileStreetData;
use models\lendingwise\tblFileUpdateAudit;
use models\lendingwise\tblFileUpdatedDate;
use models\lendingwise\tblFileUsers;
use models\lendingwise\tblFundingClosing;
use models\lendingwise\tblGiftsOrGrants;
use models\lendingwise\tblHAFASaleDocs;
use models\lendingwise\tblHAFATermsOfSale;
use models\lendingwise\tblHardship;
use models\lendingwise\tblHardshipDescription;
use models\lendingwise\tblIncomeInfo;
use models\lendingwise\tblInsuranceDetails;
use models\lendingwise\tblInvestorFunding;
use models\lendingwise\tblInvestorInfo;
use models\lendingwise\tblListingHistoryInfo;
use models\lendingwise\tblListingPageData;
use models\lendingwise\tblLMProposalSummary;
use models\lendingwise\tblLMRBilling;
use models\lendingwise\tblLMRBillingPayment;
use models\lendingwise\tblLMRCCInfo;
use models\lendingwise\tblLMRClientType;
use models\lendingwise\tblLMRCreditorInfo;
use models\lendingwise\tblLMRCreditorStatus;
use models\lendingwise\tblLMRFileDocs;
use models\lendingwise\tblLMRHUDAdditionalCharges;
use models\lendingwise\tblLMRHUDItemsPayableLoan;
use models\lendingwise\tblLMRHUDLenderToPay;
use models\lendingwise\tblLMRHUDReservesDeposit;
use models\lendingwise\tblLMRProcessorComments;
use models\lendingwise\tblLoanOriginatorInfo;
use models\lendingwise\tblLoanPropertySummary;
use models\lendingwise\tblLoanSetting;
use models\lendingwise\tblLoanSettingTerms;
use models\lendingwise\tblMemberOfficerInfo;
use models\lendingwise\tblMissingDocuments;
use models\lendingwise\tblMissingDocumentsStatusAudit;
use models\lendingwise\tblOtherNewMortgageLoansOnProperty;
use models\lendingwise\tblOtherProps;
use models\lendingwise\tblPartnerShips;
use models\lendingwise\tblPCChecklistUploadDocs;
use models\lendingwise\tblPrincipalResidenceInfo;
use models\lendingwise\tblProperties;
use models\lendingwise\tblPropertiesAccess;
use models\lendingwise\tblPropertiesAnalysis;
use models\lendingwise\tblPropertiesAppraiserDetails;
use models\lendingwise\tblPropertiesCharacteristics;
use models\lendingwise\tblPropertiesDetails;
use models\lendingwise\tblPropertiesFloodCertificates;
use models\lendingwise\tblPropertiesHOA;
use models\lendingwise\tblPropertyManagement;
use models\lendingwise\tblPropertyManagementDocs;
use models\lendingwise\tblProposalInfo;
use models\lendingwise\tblPropSellerInfo;
use models\lendingwise\tblQAInfo;
use models\lendingwise\tblQAOtherPropertyInfo;
use models\lendingwise\tblRecentSalesInfo;
use models\lendingwise\tblRecordFileStatus;
use models\lendingwise\tblRefinanceMortgage;
use models\lendingwise\tblRestInfo;
use models\lendingwise\tblSalesMethod;
use models\lendingwise\tblSbaOtherBusiness;
use models\lendingwise\tblShortSale;
use models\lendingwise\tblShortSale2;
use models\lendingwise\tblSSCMAAnalysis;
use models\lendingwise\tblSSProposalInfo;
use models\lendingwise\tblSSProposalSummary;
use models\lendingwise\tblSSRegnInfo;
use models\standard\Dates;
use models\types\strongType;

class CopyFile extends strongType
{
    public static ?string $fileFolderPath = null;
    public static ?string $copiedFileFolderPath = null;
    public static array $renameDocsArray = [];
    public ?string $LMRID = null;
    public ?string $docID = null;
    public ?string $docName = null;
    public ?string $recordDate = null;
    public ?string $FPCID = null;
    public ?string $oldFPCID = null;

    /**
     * @param int $LMRID
     * @param int $newStatusId
     * @param int|null $empId
     * @param int|null $branchId
     * @param int|null $agentId
     * @param int $isSysNotesPrivate
     * @param int $wDoc
     * @return int|null
     */
    public static function getReport(
        int  $LMRID,
        int  $newStatusId,
        ?int $empId,
        ?int $branchId,
        ?int $agentId,
        int  $isSysNotesPrivate,
        int  $wDoc
    ): ?int
    {
//        SET @qry = CONCAT("CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFile WHERE LMRId = ", LMRID, "; ");

        $tblFile = tblFile::Get(['LMRId' => $LMRID]);
        if (!$tblFile) {
            return null;
        }
        $fileCreatedDate = $tblFile->recordDate;
        $FPCID = $tblFile->FPCID;

        self::$fileFolderPath = UploadServer::getFileFolderPath($tblFile->LMRId, $tblFile->recordDate, $tblFile->FPCID);

        $tblFile->LMRId = null;


//    SET @qry = CONCAT('UPDATE ', tblName,
//        ' SET LMRId = NULL, borrowerName= CONCAT(borrowerName, " (copy)"), recordDate = CURDATE(), ',
//        "enc_borrowerName = '", enc_borrowerName, "', loanNumber='' WHERE LMRId = ", LMRID);

        $tblFile->borrowerName .= ' (copy)';
        $tblFile->recordDate = Dates::Timestamp();
        $tblFile->enc_borrowerName = cypher::myEncryption($tblFile->borrowerName);
        $tblFile->loanNumber = '';

//    SET @qry = CONCAT("INSERT INTO tblFile( SELECT * FROM ", tblName, "); ");

        $tblFile->Save();

//        PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET id = LAST_INSERT_ID();

        $id = $tblFile->LMRId;

        $tblFileCopy = new tblFileCopy();
        $tblFileCopy->origLMRId = $LMRID;
        $tblFileCopy->copyLMRId = $id;
        $tblFileCopy->Save();

        self::$copiedFileFolderPath = UploadServer::getFileFolderPath($tblFile->LMRId, $tblFile->recordDate, $tblFile->FPCID);

//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("UPDATE tblFile  SET  loanNumber = ", id, " WHERE LMRId = ", id, "  AND FPCID =  4975; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        if ($tblFile->FPCID == glPCID::PCID_PROD_CV3) {
            $tblFile->loanNumber = $id;
            $tblFile->Save();
        }
//
//    SET @qry =
//        CONCAT(" INSERT INTO tblFileUpdatedDate (fileID, lastUpdatedDate) values ('", id, "', '", cur_date, "') ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        // this should be handled with the trigger, but here, just in case
        $tblFileUpdatedDate = tblFileUpdatedDate::Get(['fileID' => $id]);
        if (!$tblFileUpdatedDate) {
            $tblFileUpdatedDate = new tblFileUpdatedDate();
        }
        $tblFileUpdatedDate->fileID = $id;
        $tblFileUpdatedDate->lastUpdatedDate = Dates::Timestamp();
        $tblFileUpdatedDate->Save();

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileResponse WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT('UPDATE ', tblName, ' SET LMRResponseId = NULL, LMRId = ', id, ', primeStatusId = ', newStatusId,
//        ', lastUpdatedDate = NOW() WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileResponse( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileResponse = tblFileResponse::Get(['LMRId' => $LMRID]);
        $check = tblFileResponse::Get(['LMRId' => $id]);
        $item = $check ?: $tblFileResponse;

        if(!$check) {
            $item->LMRResponseId = null;
        }

        $item->LMRId = $id;
        $item->primeStatusId = $newStatusId;
        $item->lastUpdatedDate = Dates::Timestamp();
        $item->Save();

//    SET @sql = CONCAT(' INSERT INTO tblRecordFileStatus(LMRID, statusID,statusType,UID, URole,branchID, recordDate)
//  VALUES (', id, ' , ', newStatusId, ', "Pri", "", "", "", CURDATE()); ');
//    PREPARE s FROM @sql; EXECUTE s; DEALLOCATE PREPARE s;
//

        $tblRecordFileStatus = new tblRecordFileStatus();
        $tblRecordFileStatus->LMRID = $id;
        $tblRecordFileStatus->statusID = $newStatusId;
        $tblRecordFileStatus->statusType = 'Pri';
        $tblRecordFileStatus->UID = 0;
        $tblRecordFileStatus->URole = '';
        $tblRecordFileStatus->branchID = 0;
        $tblRecordFileStatus->recordDate = Dates::Timestamp();
        $tblRecordFileStatus->Save();

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRClientType WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRClientType = tblLMRClientType::Get(['LMRID' => $LMRID]);
        $check = tblLMRClientType::Get(['LMRID' => $id]);
        $item = $check ?: $tblLMRClientType;


//    SET @qry = CONCAT('UPDATE ', tblName, ' SET CTID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRClientType( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;


        if(!$check) {
            $item->CTID = null;
        }
        $item->LMRID = $id;
        $item->Save();

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileModules WHERE fileID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;

        $tblFileModules = tblFileModules::GetAll(['fileID' => $LMRID]);

//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT('UPDATE ', tblName, ' SET FMID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileModules( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;


        foreach ($tblFileModules as $item) {
            $check = tblFileModules::Get([
                'moduleCode' => $item->moduleCode,
                'fileID' => $id,
            ]);

            $item = $check ?: $item;

            if(!$check) {
                $item->FMID = null;
            }

            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFile2 WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFile2 = tblFile2::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET F2ID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFile2( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFile2 as $item) {
            $item->F2ID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblIncomeInfo WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblIncomeInfo = tblIncomeInfo::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET IID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblIncomeInfo( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblIncomeInfo as $item) {
            $item->IID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblAssetsInfo WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblAssetsInfo = tblAssetsInfo::GetAll(['LMRID' => $LMRID]);


//    SET @qry = CONCAT('UPDATE ', tblName, ' SET assetID = NULL, LMRId = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblAssetsInfo( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblAssetsInfo as $item) {
            $item->assetID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFilePropertyInfo WHERE LMRId = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFilePropertyInfo = tblFilePropertyInfo::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET FPID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFilePropertyInfo( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFilePropertyInfo as $item) {
            $item->FPID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFilePropertyInfo2 WHERE LMRId = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFilePropertyInfo2 = tblFilePropertyInfo2::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFilePropertyInfo2( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFilePropertyInfo2 as $item) {
            $item->SID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHMLONewLoanInfo WHERE fileID = ", LMRID,
//            "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHMLONewLoanInfo = tblFileHMLONewLoanInfo::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HMLIID = NULL, fileID = ', id,
//        ' , currentLoanBalance = 0.00, initialLoanAmount = 0.00 WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHMLONewLoanInfo( SELECT * FROM ", tblName, " WHERE fileID = ", id,
//        ") LIMIT 1 ;");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHMLONewLoanInfo as $item) {
            $item->HMLIID = null;
            $item->fileID = $id;
            $item->currentLoanBalance = 0;
            $item->initialLoanAmount = 0;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblAdditionalLienInfo WHERE LMRID = ", LMRID,
//        " AND activeStatus = 1; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblAdditionalLienInfo = tblAdditionalLienInfo::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET refID = ALID WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT('UPDATE ', tblName, ' SET ALID = NULL, LMRId = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblAdditionalLienInfo( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;

        foreach ($tblAdditionalLienInfo as $item) {

//    SET alid = LAST_INSERT_ID();
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName,
//        " SELECT tc.SID, ta.ALID, tc.contactName, tc.addLienEmail, tc.addLienPhone, tc.addLienFax, tc.sn, tc.activeStatus FROM tblAdditionalLienInfo ta, tblAddlnLienContactInfo tc WHERE ta.LMRID = ",
//        id, " AND ta.refID = tc.LID AND ta.activeStatus = 1; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SID = NULL');
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblAddlnLienContactInfo( SELECT * FROM ", tblName, ")");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

            $item->refID = $item->ALID;
            $item->ALID = null;
            $item->LMRID = $id;
            $item->Save();
            $ALID = $item->ALID;

            $sql = '
                SELECT tc.SID
                     , ta.ALID AS LID
                     , tc.contactName
                     , tc.addLienEmail
                     , tc.addLienPhone
                     , tc.addLienFax
                     , tc.sn
                     , tc.activeStatus 
                FROM tblAdditionalLienInfo ta
                   , tblAddlnLienContactInfo tc 
                WHERE ta.LMRID = :id
                    AND ta.ALID = :ALID
                    AND ta.refID = tc.LID 
                    AND ta.activeStatus = 1            
            ';

            // tblAddlnLienContactInfo
            $res = Database2::getInstance()->queryData($sql, [
                'id'   => $id,
                'ALID' => $ALID,
            ], function ($row) {
                return new tblAddlnLienContactInfo($row);
            });

            foreach ($res as $subItem) {
                $subItem->Save();
            }
        }

        $tblOtherProps = tblOtherProps::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblOtherProps WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblOtherProps( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblOtherProps as $item) {
            $item->SID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRCreditorInfo WHERE LMRId = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRCreditorInfo = tblLMRCreditorInfo::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET CIID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRCreditorInfo( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMRCreditorInfo as $item) {

            $CIID = $item->CIID;
            $item->CIID = null;
            $item->LMRId = $id;
            $item->Save();

            $tblLMRCreditorStatus = tblLMRCreditorStatus::GetAll(['CIID' => $CIID]);
            foreach ($tblLMRCreditorStatus as $eachStatus) {
                $eachStatus->CSID = null;
                $eachStatus->CIID = $item->CIID;
                $eachStatus->Save();
            }

        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblQAInfo WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblQAInfo = tblQAInfo::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET QAID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblQAInfo( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblQAInfo as $item) {
            $item->QAID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblShortSale WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblShortSale = tblShortSale::GetAll(['LMRId' => $LMRID]);


//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SSID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblShortSale( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblShortSale as $item) {
            $item->SSID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblShortSale2 WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblShortSale2 = tblShortSale2::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblShortSale2( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblShortSale2 as $item) {
            $item->SID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblHardship WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblHardship = tblHardship::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblHardship( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblHardship as $item) {
            $item->HID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblHardshipDescription WHERE LMRID = ", LMRID,
//            "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblHardshipDescription = tblHardshipDescription::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HDID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblHardshipDescription( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblHardshipDescription as $item) {
            $item->HDID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHUDBasicInfo WHERE LMRID = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHUDBasicInfo = tblFileHUDBasicInfo::GetAll(['LMRID' => $LMRID]);


//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HBID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHUDBasicInfo( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHUDBasicInfo as $item) {
            $item->HBID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHUDType WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHUDType = tblFileHUDType::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HTID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHUDType( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHUDType as $item) {
            $item->HTID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHUDTransaction WHERE LMRID = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHUDTransaction = tblFileHUDTransaction::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HUDID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHUDTransaction( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHUDTransaction as $item) {
            $item->HUDID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHUDSettlementCharges WHERE LMRID = ",
//        LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHUDSettlementCharges = tblFileHUDSettlementCharges::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HSID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHUDSettlementCharges( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHUDSettlementCharges as $item) {
            $item->HSID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRHUDItemsPayableLoan WHERE LMRID = ",
//        LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRHUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HIID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRHUDItemsPayableLoan( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMRHUDItemsPayableLoan as $item) {
            $item->HIID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRHUDLenderToPay WHERE LMRID = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRHUDLenderToPay = tblLMRHUDLenderToPay::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HLID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRHUDLenderToPay( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMRHUDLenderToPay as $item) {
            $item->HLID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRHUDReservesDeposit WHERE LMRID = ", LMRID,
//            "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRHUDReservesDeposit = tblLMRHUDReservesDeposit::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HRID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRHUDReservesDeposit( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMRHUDReservesDeposit as $item) {
            $item->HRID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRHUDAdditionalCharges WHERE LMRID = ",
//        LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRHUDAdditionalCharges = tblLMRHUDAdditionalCharges::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HAID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRHUDAdditionalCharges( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMRHUDAdditionalCharges as $item) {
            $item->HAID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileUsers WHERE fileID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileUsers = tblFileUsers::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileUsers( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileUsers as $item) {
            $item->SID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblProposalInfo WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblProposalInfo = tblProposalInfo::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET PID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblProposalInfo( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblProposalInfo as $item) {
            $item->PID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMProposalSummary WHERE LMRID = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMProposalSummary = tblLMProposalSummary::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET LPSID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMProposalSummary( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMProposalSummary as $item) {
            $item->LPSID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblSSProposalInfo WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblSSProposalInfo = tblSSProposalInfo::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SSPID = NULL, LMRId = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblSSProposalInfo( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblSSProposalInfo as $item) {
            $item->SSPID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblSSProposalSummary WHERE LMRID = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblSSProposalSummary = tblSSProposalSummary::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SPSID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblSSProposalSummary( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblSSProposalSummary as $item) {
            $item->SPSID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblListingHistoryInfo WHERE LMRId = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblListingHistoryInfo = tblListingHistoryInfo::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET LID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblListingHistoryInfo( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblListingHistoryInfo as $item) {
            $item->LID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblRecentSalesInfo WHERE LMRId = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblRecentSalesInfo = tblRecentSalesInfo::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET RSID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblRecentSalesInfo( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblRecentSalesInfo as $item) {
            $item->RSID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblSSCMAAnalysis WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblSSCMAAnalysis = tblSSCMAAnalysis::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SCID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblSSCMAAnalysis( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblSSCMAAnalysis as $item) {
            $item->SCID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblHAFATermsOfSale WHERE LMRID = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblHAFATermsOfSale = tblHAFATermsOfSale::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HTSID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblHAFATermsOfSale( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblHAFATermsOfSale as $item) {
            $item->HTSID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    IF (wDoc > 0) THEN
        if ($wDoc > 0) {
//        SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblHAFASaleDocs WHERE LMRID = ", LMRID,
//        "; ");
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;

            $tblHAFASaleDocs = tblHAFASaleDocs::GetAll(['LMRID' => $LMRID]);

//        SET @qry = CONCAT('UPDATE ', tblName, ' SET HSDID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;
//        SET @qry = CONCAT("INSERT INTO tblHAFASaleDocs( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        SET @qry = CONCAT(" DROP TABLE ", tblName);
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;

            foreach ($tblHAFASaleDocs as $item) {
                $item->HSDID = null;
                $item->LMRID = $id;
                $item->Save();
            }

//        SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRFileDocs WHERE LMRID = ", LMRID,
//        "; ");
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;

            $tblLMRFileDocs = tblLMRFileDocs::GetAll(['LMRID' => $LMRID]);

            //        SET @qry = CONCAT('UPDATE ', tblName, ' SET docID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;
//        SET @qry = CONCAT("INSERT INTO tblLMRFileDocs( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        SET @qry = CONCAT(" DROP TABLE ", tblName);
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;

            foreach ($tblLMRFileDocs as $item) {
                $item->docID = null;
                $item->LMRID = $id;
                $item->Save();
            }

//        SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblPCChecklistUploadDocs WHERE fileID = ",
//            LMRID, "; ");
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;

            $tblPCChecklistUploadDocs = tblPCChecklistUploadDocs::GetAll(['fileID' => $LMRID]);

//        SET @qry = CONCAT('UPDATE ', tblName, ' SET PCUDID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;
//        SET @qry =
//        CONCAT("INSERT INTO tblPCChecklistUploadDocs( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        SET @qry = CONCAT(" DROP TABLE ", tblName);
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;

            foreach ($tblPCChecklistUploadDocs as $item) {
                $item->PCUDID = null;
                $item->fileID = $id;
                $item->Save();
            }


//    END IF;
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRBilling WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRBilling = tblLMRBilling::GetAll(['LMRId' => $LMRID]);

        //    SET @qry = CONCAT('UPDATE ', tblName, ' SET BID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRBilling( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMRBilling as $item) {
            $item->BID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRBillingPayment WHERE LMRId = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRBillingPayment = tblLMRBillingPayment::GetAll(['LMRId' => $LMRID]);

        //    SET @qry = CONCAT('UPDATE ', tblName, ' SET BPID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRBillingPayment( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMRBillingPayment as $item) {
            $item->BPID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblACHInfo WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblACHInfo = tblACHInfo::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET ACID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblACHInfo( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblACHInfo as $item) {
            $item->ACID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblRemoteUrl WHERE LMRId = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        //   $tblRemoteUrl = tblRemoteUrl::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET RUID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblRemoteUrl( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        /*      foreach ($tblRemoteUrl as $item) {
                  $item->RUID = null;
                  $item->LMRId = $id;
                  $item->Save();
              }*/

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFilePayee WHERE fileID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFilePayee = tblFilePayee::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET PID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFilePayee( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFilePayee as $item) {
            $item->PID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHMLOBusinessEntity WHERE fileID = ",
//        LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHMLOBusinessEntity = tblFileHMLOBusinessEntity::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HMLOBEID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHMLOBusinessEntity( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHMLOBusinessEntity as $item) {
            $item->HMLOBEID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblLMRCCInfo WHERE LMRID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblLMRCCInfo = tblLMRCCInfo::GetAll(['LMRID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET LCID = NULL, LMRID = ', id, ' WHERE LMRID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblLMRCCInfo( SELECT * FROM ", tblName, " WHERE LMRID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblLMRCCInfo as $item) {
            $item->LCID = null;
            $item->LMRID = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileContacts WHERE fileID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileContacts = tblFileContacts::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET SID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileContacts( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileContacts as $item) {
            $item->SID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHMLO WHERE fileID = ", LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHMLO = tblFileHMLO::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HMLOID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHMLO( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHMLO as $item) {
            $item->HMLOID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHMLOBackGround WHERE fileID = ", LMRID,
//            "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHMLOBackGround = tblFileHMLOBackGround::GetAll(['fileID' => $LMRID]);


//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HMLOBGID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHMLOBackGround( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHMLOBackGround as $item) {
            $item->HMLOBGID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileHMLOExperience WHERE fileID = ", LMRID,
//            "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHMLOExperience = tblFileHMLOExperience::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET HMLOEID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileHMLOExperience( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileHMLOExperience as $item) {
            $item->HMLOEID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileLOAssetsInfo WHERE fileID = ", LMRID,
//        "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileLOAssetsInfo = tblFileLOAssetsInfo::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET LOAID = NULL, fileID = ', id, ' WHERE fileID = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileLOAssetsInfo( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileLOAssetsInfo as $item) {
            $item->LOAID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry =
//        CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileExpFilpGroundUp WHERE LMRId = ", LMRID,
//            "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileExpFilpGroundUp = tblFileExpFilpGroundUp::GetAll(['LMRId' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET FFGID = NULL, LMRId = ', id, ' WHERE LMRId = ', LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileExpFilpGroundUp( SELECT * FROM ", tblName, " WHERE LMRId = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileExpFilpGroundUp as $item) {
            $item->FFGID = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(" CREATE TEMPORARY TABLE ", tblName, " SELECT * FROM tblFileLOScheduleRealInfo WHERE fileID = ",
//        LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileLOScheduleRealInfo = tblFileLOScheduleRealInfo::GetAll(['fileID' => $LMRID]);

//    SET @qry = CONCAT('UPDATE ', tblName, ' SET LOSRID = NULL, recordDate = NOW(), fileID = ', id, ' WHERE fileID = ',
//        LMRID);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;
//    SET @qry = CONCAT("INSERT INTO tblFileLOScheduleRealInfo( SELECT * FROM ", tblName, " WHERE fileID = ", id, "); ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    SET @qry = CONCAT(" DROP TABLE ", tblName);
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        foreach ($tblFileLOScheduleRealInfo as $item) {
            $item->LOSRID = null;
            $item->fileID = $id;
            $item->Save();
        }

//    SET @qry = CONCAT(
//        "INSERT INTO tblFileHMLOPropInfo (referringParty, fileID, recordDate, propertyNeedRehab, isBlanketLoan, activeStatus, checkDisplayTermSheet,typeOfHMLOLoanRequesting) SELECT referringParty, ",
//        id,
//        ", NOW(), propertyNeedRehab, isBlanketLoan, activeStatus, checkDisplayTermSheet , typeOfHMLOLoanRequesting FROM tblFileHMLOPropInfo WHERE fileID = ",
//        LMRID, "; ");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;
//    DEALLOCATE PREPARE stmt;

        $tblFileHMLOPropInfo = tblFileHMLOPropInfo::GetAll(['fileID' => $LMRID]);
        foreach ($tblFileHMLOPropInfo as $item) {
            $item->HMLOPID = null;
            $item->fileID = $id;
            $item->recordDate = Dates::Timestamp();
            $item->Save();
        }


//    SET @qry = CONCAT("INSERT INTO tblLMRProcessorComments (processorComments, fileID, executiveId, brokerNumber, employeeId, notesDate, private, isSysNotes)
//  values('File Copied.', '", id, "', '", branchId, "', '", agentId, "', '", empId, "', NOW(), '", isSysNotesPrivate,
//        "', '1')");
//    PREPARE stmt FROM @qry;
//    EXECUTE stmt;

        $tblLMRProcessorComments = new tblLMRProcessorComments();
        $tblLMRProcessorComments->processorComments = 'File Copied.';
        $tblLMRProcessorComments->fileID = $id;
        $tblLMRProcessorComments->executiveId = $branchId;
        $tblLMRProcessorComments->brokerNumber = $agentId;
        $tblLMRProcessorComments->employeeId = $empId;
        $tblLMRProcessorComments->notesDate = Dates::Timestamp();
        $tblLMRProcessorComments->private = $isSysNotesPrivate;
        $tblLMRProcessorComments->isSysNotes = 1;
        $tblLMRProcessorComments->Save();

        $tblFileUpdateAudit = new tblFileUpdateAudit();
        $tblFileUpdateAudit->fileID = $id;
        $tblFileUpdateAudit->UID = 0;
        $tblFileUpdateAudit->UType = 'Employee';
        $tblFileUpdateAudit->recordDate = Dates::Timestamp();
        $tblFileUpdateAudit->FAction = 'C';
        $tblFileUpdateAudit->Save();


        /* Borrower AlterNate Names */
        $tblBorrowerAlternateNames = tblBorrowerAlternateNames::GetAll(['LMRID' => $LMRID]);
        foreach ($tblBorrowerAlternateNames as $item) {
            $item->nameID = null;
            $item->LMRID = $id;
            $item->save();
        }

        /* sales method */
        $tblSalesMethod = tblSalesMethod::getAll(['LMRID' => $LMRID]);
        foreach ($tblSalesMethod as $item) {
            $item->SMID = null;
            $item->LMRID = $id;
            $item->save();
        }


        /* clientCreditScore */
        $clientCreditScore = clientCreditScore::getAll(['lmrid' => $LMRID]);
        foreach ($clientCreditScore as $item) {
            $item->id = null;
            $item->lmrid = $id;
            $item->Save();
        }

        /* collateral */
        $collateral = collateral::getAll(['lmrid' => $LMRID]);
        foreach ($collateral as $item) {
            $item->id = null;
            $item->lmrid = $id;
            $item->Save();
        }


        /* contingentLiabilities */
        $contingentLiabilities = contingentLiabilities::getAll(['lmrid' => $LMRID]);
        foreach ($contingentLiabilities as $item) {
            $item->id = null;
            $item->lmrid = $id;
            $item->Save();
        }


        /* estimatedProjectCost */
        $estimatedProjectCost = estimatedProjectCost::getAll(['lmrid' => $LMRID]);
        foreach ($estimatedProjectCost as $item) {
            $item->id = null;
            $item->lmrid = $id;
            $item->Save();
        }


        /* purchaseInfo */
        /*        $purchaseInfo =  purchaseInfo::getAll(['LMRID' => $LMRID]);
                foreach ($purchaseInfo as $item) {
                    $item->PID = null;
                    $item->LMRID = $id;
                    $item->Save();
                }*/


        if ($wDoc) {
            /* tblBinder */
            $tblBinder = tblBinder::getAll(['LMRID' => $LMRID]);
            foreach ($tblBinder as $item) {
                $binderId = $item->BID;
                $item->BID = null;
                $item->LMRID = $id;
                $item->Save();
                $newBinderId = $item->BID;
                self::$renameDocsArray[] = [
                    'oldDoc' => self::$copiedFileFolderPath . '/binder/' . 'bd_' . cypher::myEncryption($binderId) . '.pdf',
                    'newDoc' => self::$copiedFileFolderPath . '/binder/' . 'bd_' . cypher::myEncryption($newBinderId) . '.pdf',
                ];
                $tblBoundDocs = tblBoundDocs::getAll(['BID' => $binderId]);
                foreach ($tblBoundDocs as $boundDoc) {
                    $boundDoc->BDID = null;
                    $boundDoc->BID = $newBinderId;
                    $boundDoc->Save();
                }
            }
        }


        /* tblBudgetAndDraws */
        $tblBudgetAndDraws = tblBudgetAndDraws::getAll(['LMRId' => $LMRID]);
        foreach ($tblBudgetAndDraws as $item) {
            $budgetId = $item->BDID;

            $item->BDID = null;
            $item->LMRId = $id;
            $item->Save();
            $newBudgetId = $item->BDID;

            if ($wDoc) {
                $tblBudgetAndDrawDoc = tblBudgetAndDrawDoc::getAll(['BDID' => $budgetId]);
                foreach ($tblBudgetAndDrawDoc as $budgetDoc) {
                    $budgetDoc->BDDId = null;
                    $budgetDoc->LMRId = $id;
                    $budgetDoc->BDID = $newBudgetId;
                    $budgetDoc->Save();
                }
            }
        }


        /* tblCollateralValues */
        $tblCollateralValues = tblCollateralValues::getAll(['LMRID' => $LMRID]);
        foreach ($tblCollateralValues as $item) {
            $item->id = null;
            $item->LMRID = $id;
            $item->Save();
        }


        /* tblCollateralValues */
        $tblCreditMemo = tblCreditMemo::getAll(['LMRId' => $LMRID]);
        foreach ($tblCreditMemo as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->Save();
        }


        /* tblEquipmentInfo */
        $tblEquipmentInfo = tblEquipmentInfo::getAll(['LMRId' => $LMRID]);
        foreach ($tblEquipmentInfo as $item) {
            $item->EIID = null;
            $item->LMRId = $id;
            $item->Save();
        }

        /* Equipment Info */
        $tblEquipmentInformation = tblEquipmentInformation::getAll(['LMRID' => $LMRID]);
        foreach ($tblEquipmentInformation as $item) {
            $item->EIID = null;
            $item->LMRID = $id;
            $item->save();
        }


        /* tblEscrow */
        $tblEscrow = tblEscrow::getAll(['LMRId' => $LMRID]);
        foreach ($tblEscrow as $item) {
            $item->tblEscrowId = null;
            $item->LMRId = $id;
            $item->save();
        }

        /* Fee Schedule */
        $tblFeeSchedule = tblFeeSchedule::getAll(['LMRID' => $LMRID]);
        foreach ($tblFeeSchedule as $item) {
            $item->FSID = null;
            $item->LMRID = $id;
            $item->save();
        }


        /* tblFile3 */
        $tblFile3 = tblFile3::getAll(['LMRID' => $LMRID]);
        foreach ($tblFile3 as $item) {
            $item->F3ID = null;
            $item->LMRID = $id;
            $item->save();
        }


        /* Additional Guarantors */
        $tblFileAdditionalGuarantors = tblFileAdditionalGuarantors::getAll(['LMRId' => $LMRID]);
        foreach ($tblFileAdditionalGuarantors as $item) {
            $item->AGID = null;
            $item->LMRId = $id;
            $item->save();
        }


        /* tblFileAdditionalLoanPrograms*/
        $tblFileAdditionalLoanPrograms = tblFileAdditionalLoanPrograms::getAll(['LMRID' => $LMRID]);
        foreach ($tblFileAdditionalLoanPrograms as $item) {
            $item->ALPID = null;
            $item->LMRID = $id;
            $item->save();
        }


        /* tblFileAdditionalLoanPrograms*/
        $tblFileAdminInfo = tblFileAdminInfo::getAll(['LMRId' => $LMRID]);
        foreach ($tblFileAdminInfo as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->save();
        }

        /* tblFileCalculatedValues*/
        $tblFileCalculatedValues = tblFileCalculatedValues::getAll(['LMRId' => $LMRID]);
        foreach ($tblFileCalculatedValues as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->save();
        }


        /* tblFileChecklistModules*/
        $tblFileChecklistModules = tblFileChecklistModules::getAll(['fileID' => $LMRID]);
        foreach ($tblFileChecklistModules as $item) {
            $FMID = $item->FMID;
            $item->FMID = null;
            $item->fileID = $id;
            $item->save();

            $tblFileChecklistRequiredBy = tblFileChecklistRequiredBy::getAll(['FMID' => $FMID]);
            foreach ($tblFileChecklistRequiredBy as $eachFileRequiredBy) {
                $eachFileRequiredBy->FRID = null;
                $eachFileRequiredBy->FMID = $item->FMID;
                $eachFileRequiredBy->Save();
            }
        }


        /* tblFileClients*/
        $tblFileClients = tblFileClients::getAll(['fileID' => $LMRID]);
        foreach ($tblFileClients as $item) {
            $item->SID = null;
            $item->fileID = $id;
            $item->save();
        }

        /* tblFileCourtInfo*/
        $tblFileCourtInfo = tblFileCourtInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileCourtInfo as $item) {
            $item->CSID = null;
            $item->fileID = $id;
            $item->save();
        }


        /* tblFileExtensionOptions*/
        $tblFileExtensionOptions = tblFileExtensionOptions::getAll(['LMRId' => $LMRID]);
        foreach ($tblFileExtensionOptions as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->save();
        }


        /* tblFileExteriorWorkInfo*/
        $tblFileExteriorWorkInfo = tblFileExteriorWorkInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileExteriorWorkInfo as $item) {
            $item->SID = null;
            $item->fileID = $id;
            $item->save();
        }


        /* tblFileExteriorWorkInfo*/
        $tblFileHMLOAssetsInfo = tblFileHMLOAssetsInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileHMLOAssetsInfo as $item) {
            $item->HMLOAID = null;
            $item->fileID = $id;
            $item->save();
        }


        /* tblFileHMLOBusinessEntityRef*/
        $tblFileHMLOBusinessEntityRef = tblFileHMLOBusinessEntityRef::getAll(['fileID' => $LMRID]);
        foreach ($tblFileHMLOBusinessEntityRef as $item) {
            $item->HMLOBERID = null;
            $item->fileID = $id;
            $item->save();
        }


        /* tblFileHMLOListOfRepairs*/
        $tblFileHMLOListOfRepairs = tblFileHMLOListOfRepairs::getAll(['fileID' => $LMRID]);
        foreach ($tblFileHMLOListOfRepairs as $item) {
            $item->HMLORID = null;
            $item->fileID = $id;
            $item->save();
        }


        /* tblFileHMLORehabInfo*/
        $tblFileHMLORehabInfo = tblFileHMLORehabInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileHMLORehabInfo as $item) {
            $item->RID = null;
            $item->fileID = $id;
            $item->save();
        }


        /* tblFileInteriorWorkInfo*/
        $tblFileInteriorWorkInfo = tblFileInteriorWorkInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileInteriorWorkInfo as $item) {
            $item->SID = null;
            $item->fileID = $id;
            $item->save();
        }


        /* tblFileInternalLoanPrograms*/
        $tblFileInternalLoanPrograms = tblFileInternalLoanPrograms::getAll(['LMRID' => $LMRID]);
        foreach ($tblFileInternalLoanPrograms as $item) {
            $item->ILPID = null;
            $item->LMRID = $id;
            $item->save();
        }

        /* tblFileLegalBankruptcy*/
        $tblFileLegalBankruptcy = tblFileLegalBankruptcy::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLegalBankruptcy as $item) {
            $item->BRID = null;
            $item->fileID = $id;
            $item->save();
        }


        /* tblFileLoanOrigination*/
        $tblFileLoanOrigination = tblFileLoanOrigination::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLoanOrigination as $item) {
            $item->LOID = null;
            $item->fileID = $id;
            $item->save();
        }

        /* Additional Employment Information */
        $tblFileLOBorEmploymentInfo = tblFileLOBorEmploymentInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLOBorEmploymentInfo as $item) {
            $item->LOBEID = null;
            $item->fileID = $id;
            $item->Save();
        }

        /* tblFileLOChekingSavingInfo */
        $tblFileLOChekingSavingInfo = tblFileLOChekingSavingInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLOChekingSavingInfo as $item) {
            $item->LOCSID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFileLOCoBEmploymentInfo */
        $tblFileLOCoBEmploymentInfo = tblFileLOCoBEmploymentInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLOCoBEmploymentInfo as $item) {
            $item->LOCBID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFileLOExpensesInfo */
        $tblFileLOExpensesInfo = tblFileLOExpensesInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLOExpensesInfo as $item) {
            $item->LOEID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFileLOExplanation */
        $tblFileLOExplanation = tblFileLOExplanation::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLOExplanation as $item) {
            $item->EID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFileLOLiabilitiesInfo */
        $tblFileLOLiabilitiesInfo = tblFileLOLiabilitiesInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLOLiabilitiesInfo as $item) {
            $item->LOLID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFileLOOtherCredits */
        $tblFileLOOtherCredits = tblFileLOOtherCredits::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLOOtherCredits as $item) {
            $item->LOOCID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFileLOPropertyInfo */
        $tblFileLOPropertyInfo = tblFileLOPropertyInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileLOPropertyInfo as $item) {
            $item->LOPID = null;
            $item->fileID = $id;
            $item->Save();
        }

        /* tblFileMFLoanTerms */
        $tblFileMFLoanTerms = tblFileMFLoanTerms::getAll(['fileID' => $LMRID]);
        foreach ($tblFileMFLoanTerms as $item) {
            $item->LTID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFileOtherWorkInfo */
        $tblFileOtherWorkInfo = tblFileOtherWorkInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFileOtherWorkInfo as $item) {
            $item->SID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFilePreliminaryWorkInfo */
        $tblFilePreliminaryWorkInfo = tblFilePreliminaryWorkInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblFilePreliminaryWorkInfo as $item) {
            $item->SID = null;
            $item->fileID = $id;
            $item->Save();
        }

        /* SBA Questions */
        $tblFileSBAQuestions = tblFileSBAQuestions::getAll(['fileID' => $LMRID]);
        foreach ($tblFileSBAQuestions as $item) {
            $item->SBAID = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblFileStreetData */
        $tblFileStreetData = tblFileStreetData::getAll(['LMRID' => $LMRID]);
        foreach ($tblFileStreetData as $item) {
            $item->SID = null;
            $item->LMRID = $id;
            $item->Save();
        }

        /* tblFundingClosing */
        $tblFundingClosing = tblFundingClosing::getAll(['LMRId' => $LMRID]);
        foreach ($tblFundingClosing as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->Save();
        }

        /* Gifts Or Grants */
        $tblGiftsOrGrants = tblGiftsOrGrants::getAll(['LMRId' => $LMRID]);
        foreach ($tblGiftsOrGrants as $item) {
            $item->GID = null;
            $item->LMRId = $id;
            $item->Save();
        }


        if ($wDoc) {
            /* tblHAFASaleDocs */
            $tblHAFASaleDocs = tblHAFASaleDocs::getAll(['LMRID' => $LMRID]);
            foreach ($tblHAFASaleDocs as $item) {
                $item->HSDID = null;
                $item->LMRID = $id;
                $item->Save();
            }
        }


        /* tblHAFATermsOfSale */
        $tblHAFATermsOfSale = tblHAFATermsOfSale::getAll(['LMRID' => $LMRID]);
        foreach ($tblHAFATermsOfSale as $item) {
            $item->HTSID = null;
            $item->LMRID = $id;
            $item->Save();
        }

        /* tblInsuranceDetails */
        $tblInsuranceDetails = tblInsuranceDetails::getAll(['fileID' => $LMRID]);
        foreach ($tblInsuranceDetails as $item) {
            $item->id = null;
            $item->fileID = $id;
            $item->Save();
        }


        /* tblInvestorInfo */
        $tblInvestorInfo = tblInvestorInfo::getAll(['LMRId' => $LMRID]);
        foreach ($tblInvestorInfo as $item) {
            $InID = $item->InID;
            $item->InID = null;
            $item->LMRId = $id;
            $item->Save();
            $InIDNew = $item->InID;

            $tblInvestorFunding = tblInvestorFunding::getAll(['InID' => $InID]);
            foreach ($tblInvestorFunding as $eachFunding) {
                $eachFunding->id = null;
                $eachFunding->InID = $InIDNew;
                $item->Save();
            }
        }


        /* tblListingHistoryInfo */
        $tblListingHistoryInfo = tblListingHistoryInfo::getAll(['LMRId' => $LMRID]);
        foreach ($tblListingHistoryInfo as $item) {
            $item->LID = null;
            $item->LMRId = $id;
            $item->Save();
        }

        /* tblListingPageData */
        $tblListingPageData = tblListingPageData::getAll(['LMRId' => $LMRID]);
        foreach ($tblListingPageData as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->Save();
        }


        /* tblLMProposalSummary */
        $tblLMProposalSummary = tblLMProposalSummary::getAll(['LMRID' => $LMRID]);
        foreach ($tblLMProposalSummary as $item) {
            $item->LPSID = null;
            $item->LMRID = $id;
            $item->Save();
        }


        /* tblMemberOfficerInfo */
        $tblMemberOfficerInfo = tblMemberOfficerInfo::getAll(['LMRID' => $LMRID]);
        foreach ($tblMemberOfficerInfo as $item) {
            $item->memberId = null;
            $item->LMRID = $id;
            $item->Save();
        }


        /* tblOtherNewMortgageLoansOnProperty */
        $tblOtherNewMortgageLoansOnProperty = tblOtherNewMortgageLoansOnProperty::getAll(['LMRID' => $LMRID]);
        foreach ($tblOtherNewMortgageLoansOnProperty as $item) {
            $item->propId = null;
            $item->LMRID = $id;
            $item->Save();
        }


        /* tblOtherProps */
        $tblOtherProps = tblOtherProps::getAll(['LMRId' => $LMRID]);
        foreach ($tblOtherProps as $item) {
            $item->SID = null;
            $item->LMRId = $id;
            $item->Save();
        }


        $tblPartnerShips = tblPartnerShips::getAll(['LMRId' => $LMRID]);
        foreach ($tblPartnerShips as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->Save();
        }


        /* tblPayment */
        /*        $tblPayment = tblPayment::getAll(['LMRId' => $LMRID]);
                foreach ($tblPayment as $item) {
                    $item->tblPaymentId = null;
                    $item->LMRId = $id;
                    $item->Save();
                }*/


        /* tblPrincipalResidenceInfo */
        $tblPrincipalResidenceInfo = tblPrincipalResidenceInfo::getAll(['fileID' => $LMRID]);
        foreach ($tblPrincipalResidenceInfo as $item) {
            $item->SID = null;
            $item->fileID = $id;
            $item->Save();
        }

        /* Properties */

        $tblProperties = tblProperties::getAll(['LMRId' => $LMRID]);

        foreach ($tblProperties as $item) {
            $propertyId = $item->propertyId;
            $item->propertyId = null;
            $item->LMRId = $id;

            $newItem = new tblProperties();
            $newItem->fromData($item->toArray());
            $newItem->Save();
            $newPropertyId = $newItem->propertyId;

            $tblPropertiesAccess = tblPropertiesAccess::getAll(['propertyId' => $propertyId]);
            foreach ($tblPropertiesAccess as $eachPropertyAccess) {
                $eachPropertyAccess->id = null;
                $eachPropertyAccess->propertyId = $newPropertyId;
                $eachPropertyAccess->Save();
            }

            $tblPropertiesAnalysis = tblPropertiesAnalysis::getAll(['propertyId' => $propertyId]);
            foreach ($tblPropertiesAnalysis as $eachPropertyAnalysis) {
                $eachPropertyAnalysis->id = null;
                $eachPropertyAnalysis->propertyId = $newPropertyId;
                $eachPropertyAnalysis->Save();
            }

            $tblPropertiesAppraiserDetails = tblPropertiesAppraiserDetails::getAll(['propertyId' => $propertyId]);

            foreach ($tblPropertiesAppraiserDetails as $eachPropertyAppraiser) {
                $eachPropertyAppraiser->id = null;
                $eachPropertyAppraiser->appraiserId = $eachPropertyAppraiser->appraiserId ?: null;
                $eachPropertyAppraiser->propertyId = $newPropertyId;
                $eachPropertyAppraiser->Save();
            }


            $tblPropertiesCharacteristics = tblPropertiesCharacteristics::getAll(['propertyId' => $propertyId]);
            foreach ($tblPropertiesCharacteristics as $eachPropertyCharacter) {
                $eachPropertyCharacter->id = null;
                $eachPropertyCharacter->propertyId = $newPropertyId;
                $eachPropertyCharacter->Save();
            }


            $tblPropertiesDetails = tblPropertiesDetails::getAll(['propertyId' => $propertyId]);
            foreach ($tblPropertiesDetails as $eachPropertyDetail) {
                $eachPropertyDetail->id = null;
                $eachPropertyDetail->propertyId = $newPropertyId;
                $eachPropertyDetail->Save();
            }

            $tblPropertiesHOA = tblPropertiesHOA::getAll(['propertyId' => $propertyId]);
            foreach ($tblPropertiesHOA as $eachPropertyHOA) {
                $eachPropertyHOA->id = null;
                $eachPropertyHOA->primaryHOAId = $eachPropertyHOA->primaryHOAId ?: null;
                $eachPropertyHOA->secondaryHOAId = $eachPropertyHOA->secondaryHOAId ?: null;
                $eachPropertyHOA->propertyId = $newPropertyId;
                $eachPropertyHOA->Save();
            }

            $tblPropertiesFloodCertificates = tblPropertiesFloodCertificates::getAll(['propertyId' => $propertyId]);
            foreach ($tblPropertiesFloodCertificates as $eachPropertyFloodCertificate) {
                $eachPropertyFloodCertificate->id = null;
                $eachPropertyFloodCertificate->propertyId = $newPropertyId;
                $eachPropertyFloodCertificate->Save();
            }


            $tblFileRentRoll = tblFileRentRoll::getAll(['propertyId' => $propertyId, 'LMRId' => $LMRID]);
            foreach ($tblFileRentRoll as $eachPropertyRentroll) {
                $eachPropertyRentroll->id = null;
                $eachPropertyRentroll->propertyId = $newPropertyId;
                $eachPropertyRentroll->LMRId = $id;
                $eachPropertyRentroll->Save();
            }
        }


        /* Property Management */
        $tblPropertyManagement = tblPropertyManagement::getAll(['LMRId' => $LMRID]);
        foreach ($tblPropertyManagement as $item) {
            $tblPropertyManagementId = $item->id;
            $item->id = null;
            $item->PCID = $FPCID;
            $item->LMRId = $id;
            //$item->branchId = null;
            //$item->agentId = null;
            //$item->clientId = null;
            $item->save();

            if ($wDoc) {
                /* Property Management Docs */
                $tblPropertyManagementDocs = tblPropertyManagementDocs::getAll(['tblPropertyManagementId' => $tblPropertyManagementId]);
                if (sizeof($tblPropertyManagementDocs)) {
                    foreach ($tblPropertyManagementDocs as $eachDoc) {
                        $eachDoc->id = null;
                        $eachDoc->tblPropertyManagementId = $item->id;
                        $eachDoc->PCID = $FPCID;
                        $eachDoc->LMRId = $id;
                        $eachDoc->Save();
                    }
                }
            }
        }

        /* tblPropSellerInfo */
        $tblPropSellerInfo = tblPropSellerInfo::getAll(['LMRId' => $LMRID]);
        foreach ($tblPropSellerInfo as $item) {
            $item->SID = null;
            $item->LMRId = $id;
            $item->Save();
        }


        /* tblQAOtherPropertyInfo */
        $tblQAOtherPropertyInfo = tblQAOtherPropertyInfo::getAll(['LMRId' => $LMRID]);
        foreach ($tblQAOtherPropertyInfo as $item) {
            $item->QAPID = null;
            $item->LMRId = $id;
            $item->Save();
        }


        /* tblRefinanceMortgage */
        $tblRefinanceMortgage = tblRefinanceMortgage::getAll(['LMRId' => $LMRID]);
        foreach ($tblRefinanceMortgage as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->Save();
        }


        /* tblSalesMethod */
        $tblSalesMethod = tblSalesMethod::getAll(['LMRID' => $LMRID]);
        foreach ($tblSalesMethod as $item) {
            $item->SMID = null;
            $item->LMRID = $id;
            $item->Save();
        }

        /* tblSbaOtherBusiness */
        $tblSbaOtherBusiness = tblSbaOtherBusiness::getAll(['lmrid' => $LMRID]);
        foreach ($tblSbaOtherBusiness as $item) {
            $item->id = null;
            $item->lmrid = $id;
            $item->Save();
        }


        /* tblSSRegnInfo */
        $tblSSRegnInfo = tblSSRegnInfo::getAll(['LMRId' => $LMRID]);
        foreach ($tblSSRegnInfo as $item) {
            $item->SRID = null;
            $item->LMRId = $id;
            $item->Save();
        }


        $tblLoanSetting = tblLoanSetting::getAll(['LMRId' => $LMRID]);
        foreach ($tblLoanSetting as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->Save();
        }

        $tblLoanSettingTerms = tblLoanSettingTerms::getAll(['LMRId' => $LMRID]);
        foreach ($tblLoanSettingTerms as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->Save();
        }

        $tblLoanOriginatorInfo = tblLoanOriginatorInfo::getAll(['LMRID' => $LMRID]);
        foreach ($tblLoanOriginatorInfo as $item) {
            $item->id = null;
            $item->LMRID = $id;
            $item->Save();
        }


        $tblRestInfo = tblRestInfo::getAll(['LMRId' => $LMRID]);
        foreach ($tblRestInfo as $item) {
            $item->SID = null;
            $item->LMRId = $id;
            $item->Save();
        }

        $tblLoanPropertySummary = tblLoanPropertySummary::getAll(['LMRId' => $LMRID]);
        foreach ($tblLoanPropertySummary as $item) {
            $item->id = null;
            $item->LMRId = $id;
            $item->Save();
        }

//    IF (id > 0 && wDoc > 0) THEN
        if ($id && $wDoc) {
//        SET @qry = CONCAT(" SELECT doc.LMRID, doc.docID, doc.docName, tf.recordDate, tf.FPCID, tf.oldFPCID FROM tblLMRFileDocs doc, tblFile tf
//  WHERE tf.LMRID=doc.LMRID AND tf.activeStatus = 1 AND doc.LMRID = '", id, "' AND doc.activeStatus = 1 ");
//        PREPARE stmt FROM @qry;
//        EXECUTE stmt;
//        DEALLOCATE PREPARE stmt;

            UploadedRequiredDocs::update($LMRID, $id);

            /** Copy file docs **/
            $infoArray['sourceLMRID'] = $LMRID; //Original File ID
            $infoArray['LMRID'] = $id; //Copied(New) File ID
            $infoArray['recordDate'] = $tblFile->recordDate; //Copied File Record Date
            $infoArray['oldFPCID'] = $tblFile->oldFPCID;  //Copied File Old FPCID
            $infoArray['sourceRecordDate'] = str_replace('-', '', $fileCreatedDate);
            UploadServer::copyFile($infoArray);
            if (sizeof(self::$renameDocsArray)) {
                UploadServer::renameDocs(['Docs' => self::$renameDocsArray]);
            }

            $tblMissingDocumentsStatusAudit = tblMissingDocumentsStatusAudit::getAll(['fileID' => $LMRID]);
            foreach ($tblMissingDocumentsStatusAudit as $item) {
                $item->id = null;
                $item->fileID = $id;
                $item->Save();
            }

            $tblMissingDocuments = tblMissingDocuments::getAll(['fileID' => $LMRID]);
            foreach ($tblMissingDocuments as $item) {
                $item->MDID = null;
                $item->fileID = $id;
                $item->Save();
            }

//    END IF;
        }
        updateFileLastUpdatedDate::getReport([
            'fileID' => $id,
        ]);
        return $id;
    }
}
