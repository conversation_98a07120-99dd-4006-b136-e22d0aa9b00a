<?php

namespace models\HMLOLoanTermsCalculation;

use models\composite\calEffectiveGrossIncome;
use models\constants\accrualTypes;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanPropertySummary;
use models\inArrayData\fileHMLONewLoanInfoData;
use models\lendingwise\tblBudgetAndDraws;
use models\lendingwise\tblFileHMLOListOfRepairs;
use models\lendingwise\tblPrincipalPayDown;
use models\lendingwise\tblProcessingCompany;
use models\myFileInfo\netOperatingIncome;
use models\standard\Strings;
use models\types\strongType;

class LoanVariables extends strongType
{
    public $fileHMLOPropertyInfo;
    public $fileHMLONewLoanInfo;
    public $percentageTotalLoan;
    public $costBasis;
    public $rehabCost;
    public $assessedValue;
    public $costSpent;
    public $homeValue;
    public $lien1Payment;
    public $loanTerm;
    public $isLoanPaymentAmt;
    public $maxArvPer;
    public $maxLTCPer;
    public $aggregateDSCR;
    public $autoCalcTLAARV;
    public $extensionOptionPercentage;
    public $typeOfHMLOLoanRequesting;
    public $insImpoundsMonth;
    public $LOCTotalLoanAmt;
    public $costOfCapital;
    public $yieldSpread;
    public $annualPremium;
    public $origination_based_on_total_loan_amt;
    public $broker_based_on_total_loan_amt;
    public $lien1Rate;
    public $accrualType;
    public $perDiemAccrualType;
    public $lockOriginationValue;
    public $lockBrokerValue;
    public $CORTotalLoanAmt;
    public $CORefiLTVPercentage;
    public $rehabCostPercentageFinanced;
    public $currentLoanBalance;
    public $acquisitionPriceFinanced;
    public $finalLoanAmt;
    public $taxImpoundsFee;
    public $prepaidInterestReserve;
    public $haveInterestreserve;
    public $prepaidInterestReserveForCal;
    public $noOfMonthsPrepaid;
    public $earnestDeposit;
    public $otherDownPayment;
    public $downPaymentPercentage;
    public $totalLoanAmount;
    public $isTaxesInsEscrowed;
    public $originationPointsValue;
    public $brokerPointsValue;
    public $spcf_hoafees;
    public $brokerProcessingFee;
    public $budgetAndDrawsInfo;
    public $propertyNeedRehab;
    public $payDownInfo;
    public $totalMonthlyPayment;
    public $netOperatingIncome;
    public $nonInclusivePerDiem;
    public $amountPastDueOrOwed;
    public $payOffDate;
    public $paymentDue;
    public $mortgageOwner1;
    public $insurance1;
    public $HOAFees1;
    public $mortgageInsurance1;
    public $floodInsurance;
    public $effectiveGrossIncome;
    public $initialLoanAmount;
    public $PCID;
    public $TotalAutoMobiles;
    public $ApplicationsSubmitted;
    public $ApplicationsApproved;
    public $TotalAmountApproved;
    public $TotalOwed;
    public $TotalAssets;
    public $TotalRehabCost;
    public $originationPointsRate;
    public $brokerPointsRate;

    public static function setVarsFromMyInfo(array $myFileInfo, int $LMRId): LoanVariables
    {
        $loanVariables = new LoanVariables();

        if(!isset($myFileInfo['LMRData'][$LMRId])) {

            $temp = [];
            foreach($myFileInfo as $key => $value) {
                if($key == 'LMRInfo') {
                    $key = 'LMRData';
                }
                $temp[$key][$LMRId] = $value;
            }

            $myFileInfo = $temp;
        }

        if (isset($myFileInfo['fileHMLOPropertyInfo'])) {
            $loanVariables->fileHMLOPropertyInfo = $myFileInfo['fileHMLOPropertyInfo'];
        }


        if (isset($myFileInfo['fileHMLONewLoanInfo'])) {
            $loanVariables->fileHMLONewLoanInfo = $myFileInfo['fileHMLONewLoanInfo'][$LMRId];
        }

        if ($loanVariables->fileHMLOPropertyInfo) {
            $loanVariables->percentageTotalLoan = $loanVariables->fileHMLOPropertyInfo[$LMRId]['percentageTotalLoan'];
        }

//        DebugPlain($myFileInfo);

        $loanVariables->costBasis = Strings::replaceCommaValues($myFileInfo['listingRealtorInfo'][$LMRId]['costBasis']);
        $loanVariables->rehabCost = $myFileInfo['fileHMLOInfo'][$LMRId]['rehabCost'];
        $loanVariables->assessedValue = $myFileInfo['listingRealtorInfo'][$LMRId]['assessedValue'];
        $loanVariables->costSpent = Strings::replaceCommaValues($myFileInfo['listingRealtorInfo'][$LMRId]['costSpent']);
        $loanVariables->homeValue = Strings::replaceCommaValues($myFileInfo['LMRData'][$LMRId]['homeValue']);
        $loanVariables->lien1Payment = Strings::replaceCommaValues($myFileInfo['LMRData'][$LMRId]['lien1Payment']);
        $loanVariables->loanTerm = $myFileInfo['fileHMLOPropertyInfo'][$LMRId]['loanTerm'];
        $loanVariables->isLoanPaymentAmt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['isLoanPaymentAmt'];
        $loanVariables->maxArvPer = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['maxArvPer'];
        $loanVariables->maxLTCPer = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['maxLTCPer'];
        $loanVariables->aggregateDSCR = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['aggregateDSCR'];
        $loanVariables->autoCalcTLAARV = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['autoCalcTLAARV']; //No

        $loanVariables->extensionOptionPercentage = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['extensionOptionPercentage'];
        $loanVariables->typeOfHMLOLoanRequesting = $myFileInfo['fileHMLOPropertyInfo'][$LMRId]['typeOfHMLOLoanRequesting'];
        $loanVariables->insImpoundsMonth = Strings::replaceCommaValues($myFileInfo['fileHMLONewLoanInfo'][$LMRId]['insImpoundsMonth']);
        $loanVariables->LOCTotalLoanAmt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['LOCTotalLoanAmt'];
        $loanVariables->costOfCapital = Strings::replaceCommaValues($myFileInfo['fileHMLONewLoanInfo'][$LMRId]['costOfCapital']);
        $loanVariables->yieldSpread = Strings::replaceCommaValues($myFileInfo['fileHMLONewLoanInfo'][$LMRId]['yieldSpread']);
        $loanVariables->annualPremium = Strings::replaceCommaValues($myFileInfo['fileHMLOPropertyInfo'][$LMRId]['annualPremium']);
        $loanVariables->origination_based_on_total_loan_amt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['origination_based_on_total_loan_amt'];
        $loanVariables->broker_based_on_total_loan_amt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['broker_based_on_total_loan_amt'];
        $loanVariables->lien1Rate = Strings::replaceCommaValues($myFileInfo['LMRData'][$LMRId]['lien1Rate']);
        $loanVariables->accrualType = $myFileInfo['fileHMLOPropertyInfo'][$LMRId]['accrualType'];
        if (!$loanVariables->accrualType) {
            $loanVariables->accrualType = accrualTypes::ACCRUAL_TYPE_30_360;
        }
        $loanVariables->perDiemAccrualType = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['perDiemAccrualType'] ?? null;

        $loanVariables->lockOriginationValue = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['lockOriginationValue'];
        $loanVariables->lockBrokerValue = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['lockBrokerValue'];

        if ($myFileInfo['fileHMLONewLoanInfo'][$LMRId]) {
            $loanVariables->initialLoanAmount = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['initialLoanAmount'];
            $loanVariables->CORTotalLoanAmt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['CORTotalLoanAmt'];
            $loanVariables->CORefiLTVPercentage = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['CORefiLTVPercentage'];
            $loanVariables->rehabCostPercentageFinanced = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['rehabCostPercentageFinanced'];
            $loanVariables->currentLoanBalance = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['currentLoanBalance'];
            $loanVariables->acquisitionPriceFinanced = Strings::replaceCommaValues($myFileInfo['fileHMLONewLoanInfo'][$LMRId]['initialLoanAmount']);
            $loanVariables->finalLoanAmt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['finalLoanAmt'];
            $loanVariables->taxImpoundsFee = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['taxImpoundsFee'];
            $loanVariables->prepaidInterestReserve = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['prepaidInterestReserve'];
            $loanVariables->prepaidInterestReserveForCal = $loanVariables->prepaidInterestReserve;
            $loanVariables->haveInterestreserve = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['haveInterestreserve'];

            if ($loanVariables->haveInterestreserve == 'No' || $loanVariables->haveInterestreserve == '') {
                $loanVariables->prepaidInterestReserveForCal = 0;
            }
            $loanVariables->noOfMonthsPrepaid = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['noOfMonthsPrepaid'];
            $loanVariables->otherDownPayment = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['otherDownPayment'];
            $loanVariables->earnestDeposit = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['earnestDeposit'];
            $loanVariables->isLoanPaymentAmt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['isLoanPaymentAmt'];
            $loanVariables->maxArvPer = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['maxArvPer'];
            $loanVariables->maxLTCPer = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['maxLTCPer'];
            $loanVariables->autoCalcTLAARV = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['autoCalcTLAARV'];
            $loanVariables->extensionOptionPercentage = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['extensionOptionPercentage'];
            $loanVariables->insImpoundsMonth = Strings::replaceCommaValues($myFileInfo['fileHMLONewLoanInfo'][$LMRId]['insImpoundsMonth']);
            $loanVariables->downPaymentPercentage = Strings::replaceCommaValues($myFileInfo['fileHMLONewLoanInfo'][$LMRId]['downPaymentPercentage']);
            $loanVariables->LOCTotalLoanAmt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['LOCTotalLoanAmt'];
            $loanVariables->costOfCapital = Strings::replaceCommaValues($myFileInfo['fileHMLONewLoanInfo'][$LMRId]['costOfCapital']);
            $loanVariables->yieldSpread = Strings::replaceCommaValues($myFileInfo['fileHMLONewLoanInfo'][$LMRId]['yieldSpread']);
            $loanVariables->totalLoanAmount = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['totalLoanAmount'];

            $loanVariables->origination_based_on_total_loan_amt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['origination_based_on_total_loan_amt'];
            $loanVariables->broker_based_on_total_loan_amt = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['broker_based_on_total_loan_amt'];
            $loanVariables->isTaxesInsEscrowed = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['isTaxesInsEscrowed'];
            $loanVariables->originationPointsValue = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['originationPointsValue'];
            $loanVariables->brokerPointsValue = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['brokerPointsValue'];
            $loanVariables->spcf_hoafees = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['spcf_hoafees'];
            $loanVariables->brokerProcessingFee = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['brokerProcessingFee'];
            $loanVariables->originationPointsRate = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['originationPointsRate'];
            $loanVariables->brokerPointsRate = $myFileInfo['fileHMLONewLoanInfo'][$LMRId]['brokerPointsRate'];
        }

        $temp = $myFileInfo['budgetAndDrawsInfo'][$LMRId] ?? null;


        $loanVariables->budgetAndDrawsInfo = [];
        foreach($temp as $row) {
            unset($row['opt']);
            unset($row['myKey']);
            unset($row['myOpt']);
            $t = new tblBudgetAndDraws();
            unset($row['opt']);
            unset($row['myKey']);
            unset($row['myOpt']);
            $t->fromData($row);
            $loanVariables->budgetAndDrawsInfo[] = $t;
        }
        $loanVariables->PCID = $myFileInfo['LMRData'][$LMRId]['FPCID'];

        if ($myFileInfo['fileHMLOPropertyInfo'][$LMRId]) {
            $loanVariables->loanTerm = $myFileInfo['fileHMLOPropertyInfo'][$LMRId]['loanTerm'];
            $loanVariables->typeOfHMLOLoanRequesting = $myFileInfo['fileHMLOPropertyInfo'][$LMRId]['typeOfHMLOLoanRequesting'];
            $loanVariables->annualPremium = $myFileInfo['fileHMLOPropertyInfo'][$LMRId]['annualPremium'];
        }

        if (loanPropertySummary::showHidePropertyRehabCv3($loanVariables->PCID, $myFileInfo['LMRClientType'][$LMRId]['ClientType'])) {
            $loanVariables->propertyNeedRehab = 'Yes';
        }

        $temp = $myFileInfo['paydownInfo'][$LMRId] ?? null;
        $loanVariables->payDownInfo = [];
        foreach($temp as $row) {
            $t = new tblPrincipalPayDown();
            $t->fromData($row);
            $loanVariables->payDownInfo[] = $t;
        }

        $loanVariables->totalMonthlyPayment = $myFileInfo['LMRData'][$LMRId]['lien1Payment'];

        $loanVariables->netOperatingIncome = netOperatingIncome::getReport($LMRId)->netOperatingIncome;

        $pc = $loanVariables->PCID ? tblProcessingCompany::getCached($loanVariables->PCID) : new tblProcessingCompany();

        $loanVariables->nonInclusivePerDiem = $pc->nonInclusivePerDiem;

        $loanVariables->paymentDue = trim($myFileInfo['fileHMLOPropertyInfo'][$LMRId]['paymentDue']);
        $loanVariables->payOffDate = trim($myFileInfo['fileHMLOPropertyInfo'][$LMRId]['payOffDate']);

        $loanVariables->amountPastDueOrOwed = $myFileInfo['fileHMLOPropertyInfo'][$LMRId]['amountPastDueOrOwed'] ?? 0;

        if (isset($myFileInfo['AssetsInfo'][$LMRId])) {
            $loanVariables->TotalAssets = Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetCheckingAccounts'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetSavingMoneyMarket'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetStocks'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetIRAAccounts'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetESPOAccounts'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetHome'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetORE'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetSR'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetCars'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetLifeInsurance'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetOther'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetAccount'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetNonMarketableSecurities'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetTotalCashBankAcc'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetTotalRetirementValue'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetAvailabilityLinesCredit'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetCash'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['networthOfBusinessOwned'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['otherAssets'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['automobilesOwned3x']);

            $loanVariables->TotalOwed = Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetStocksOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetHomeOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetOREOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetSROwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetCarsOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['otherAmtOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['automobilesOwned3x1'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetAccountOwd'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetIRAAccountsOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetSecNotesOwd'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetUnsecNotesOwd'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetAcctPayableOwd'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetMarginOwd'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetNonMarketableSecuritiesOwd'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetESPOAccountsOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetLifeInsuranceOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetAvailabilityLinesCreditOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['notesPayableToBanksOthersOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['installmentAccountOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['revolvingDebtOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['unpaidPayableTaxesOwed'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['otherLiabilitiesOwed']);

            $loanVariables->TotalAutoMobiles = Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['automobilesOwned3x1'])
                + Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetCars'])
                - Strings::replaceCommaValues($myFileInfo['AssetsInfo'][$LMRId]['assetCarsOwed']);
        }

        $temp = $myFileInfo['fileHMLOListOfRepairs'][$LMRId] ?? null;
        unset($temp['opt']);
        unset($temp['myKey']);
        unset($temp['myOpt']);

        $repairs = null;
        if($temp) {
            $repairs = new tblFileHMLOListOfRepairs();
            $repairs->fromData($temp);
        }

        if ($repairs) {
            $loanVariables->TotalRehabCost = floatval($repairs->architectFees
                + $repairs->permitsFees
                + $repairs->demolitionTrashDumpsters
                + $repairs->exteriorRepairs
                + $repairs->termiteInspectionTreatment
                + $repairs->foundationStructuralReport
                + $repairs->roofing
                + $repairs->windows
                + $repairs->doors
                + $repairs->siding
                + $repairs->carpentry
                + $repairs->deckPorch
                + $repairs->drivewayWalkwayPatio
                + $repairs->landscaping
                + $repairs->exteriorRepairsOther
                + $repairs->HVACRough
                + $repairs->HVACFinish
                + $repairs->plumbingRough
                + $repairs->plumbingFixtures
                + $repairs->plumbingFinish
                + $repairs->electricalRough
                + $repairs->electricalFixtures
                + $repairs->electricalFinish
                + $repairs->sheetRock
                + $repairs->interiorRepairsDoors
                + $repairs->interiorRepairsCarpentry
                + $repairs->interiorRepairsOther1
                + $repairs->interiorRepairsOther2
                + $repairs->interiorRepairsOther3
                + $repairs->kitchenCabinets
                + $repairs->kitchenCountertops
                + $repairs->kitchenAppliances
                + $repairs->bath1
                + $repairs->bath2
                + $repairs->bath3
                + $repairs->interiorPainting
                + $repairs->exteriorPainting
                + $repairs->flooringCarpetVinyl
                + $repairs->flooringTile
                + $repairs->flooringHardwood
                + $repairs->finalCleanupOther1
                + $repairs->finalCleanupOther2
                + $repairs->finalCleanupOther3
                + $repairs->finalCleanupOther4
            );
        }

        // not seeing in myFileInfo
//        if (LMRequest::myFileInfo()->fileMFLoanTermsSummary()) {
//            $loanVariables->ApplicationsSubmitted = LMRequest::myFileInfo()->fileMFLoanTermsSummary()->MFLoanTermsInfoCnt;
//            $loanVariables->ApplicationsApproved = LMRequest::myFileInfo()->fileMFLoanTermsSummary()->noOfApprovedStatusCnt;
//            $loanVariables->TotalAmountApproved = LMRequest::myFileInfo()->fileMFLoanTermsSummary()->totalApprovedLoanAmt;
//        }

        $loanVariables->mortgageOwner1 = $myFileInfo['LMRData'][$LMRId]['mortgageOwner1'];
        $loanVariables->insurance1 = $myFileInfo['incomeInfo'][$LMRId]['insurance1'];
        $loanVariables->HOAFees1 = $myFileInfo['incomeInfo'][$LMRId]['HOAFees1'];
        $loanVariables->mortgageInsurance1 = $myFileInfo['incomeInfo'][$LMRId]['mortgageInsurance1'];
        $loanVariables->floodInsurance = $myFileInfo['incomeInfo'][$LMRId]['floodInsurance1'];

        $temp = $myFileInfo['fileHMLONewLoanInfo'][$LMRId];
        unset($temp['monthlyLoanAmount']); // comes from SBA table, don't need it here
        unset($temp['LMRId']);
        unset($temp['opt']);
        unset($temp['myKey']);
        unset($temp['myOpt']);
        $newLoanInfo = null;
        if($temp) {
            $newLoanInfo = new fileHMLONewLoanInfoData();
            $newLoanInfo->fromData($temp);
        }

        $loanVariables->effectiveGrossIncome = calEffectiveGrossIncome::getReport(null, $newLoanInfo);

        return $loanVariables;
    }
}
