<?php

namespace models\myFileInfo;

use models\Database2;
use models\types\strongType;

class PCCheckListInfo extends strongType
{
    public ?int $PCMID = null;
    public ?int $PCID = null;
    public ?string $docName = null;
    public ?int $dStatus = null;
    public ?string $serviceType = null;
    public ?string $moduleType = null;
    public ?string $createdDate = null;
    public ?int $displayOrder = null;
    public ?int $MFCID = null;
    public ?string $requiredFor = null;
    public ?string $PCMID_X = null;
    public ?string $description = null;
    public ?int $coBorrowerRelatedReqdoc = null;
    public ?int $rehabRelatedReqdoc = null;
    public ?int $noCrossCollRelatedReqdoc = null;
    public ?int $usCitizenRelatedReqdoc = null;
    public ?string $refDocName = null;
    public ?string $refDocUrl = null;
    public ?int $PRID = null;
    public ?string $requiredBy = null;
    public ?int $UID = null;
    public ?string $URole = null;
    public ?string $recordDate = null;
    public ?int $activeStatus = null;
    public ?string $notifyUsersOnUpload = null;
    public ?string $notifyEmail = null;
    public ?string $categoryId = null;
    public ?string $category = null;

    /**
     * @param int $PCID
     * @param fileModuleInfo[] $modules
     * @param string[] $clientTypes
     * @param int $externalBroker
     * @param string $UType
     * @return self[]
     */
    public static function getReport(
        int $PCID,
        array $modules,
        array $clientTypes,
        int $externalBroker,
        string $UType
    ): array
    {
        $params = [];
        $params['PCID'] = $PCID;
        foreach($modules as $i => $module) {
            $params['module' . $i] = $module->moduleCode;
        }
        foreach($clientTypes as $i => $clientType) {
            $params['clientType' . $i] = $clientType;
        }

        $qry = 'SELECT * FROM tblPCChecklistModules t1';
        if ($UType == 'Agent' && $externalBroker == 0) {
            $qry .= ' , tblPCChecklistRequiredBy t2 WHERE t1.PCMID = t2.PCMID AND t2.requiredBy = \'Broker\'';
        } elseif ($UType == 'Agent' && $externalBroker == 1) {
            $qry .= ' , tblPCChecklistRequiredBy t2 WHERE t1.PCMID = t2.PCMID AND t2.requiredBy = \'Loan Officer\'';
        } elseif ($UType == 'Branch') {
            $qry .= ' , tblPCChecklistRequiredBy t2 WHERE t1.PCMID = t2.PCMID AND t2.requiredBy = \'Branch\'';
        } elseif ($UType == 'Client') {
            $qry .= ' , tblPCChecklistRequiredBy t2 WHERE t1.PCMID = t2.PCMID AND t2.requiredBy = \'Borrower\'';
        } else {
            $qry .= ' , tblPCChecklistRequiredBy t2 WHERE t1.PCMID = t2.PCMID ';
        }

        if(sizeof($modules)) {
            $qry .= ' AND t1.moduleType IN (' . Database2::GetPlaceholders(sizeof($modules), ':module', true) . ')';
        }
        $qry .= ' AND t1.PCID = :PCID AND t1.dStatus = 1 ';
        if(sizeof($clientTypes)) {
            $qry .= ' AND t1.serviceType IN (' . Database2::GetPlaceholders(sizeof($clientTypes), ':clientType', true) . ') ';
        }

        if ($UType != 'Agent' && $UType != 'Branch' && $UType != 'Client') {
            $qry .= ' GROUP BY t1.`PCMID` ';
        }
        $qry .= ' ORDER BY t1.moduleType, t1.serviceType, t1.displayOrder, t1.docName;   ';

        return Database2::getInstance()->queryData($qry, $params, function($row) {
            return new self($row);
        });
    }
}
