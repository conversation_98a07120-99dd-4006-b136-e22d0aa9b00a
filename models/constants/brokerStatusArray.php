<?php

namespace models\constants;

use models\constants\gl\glPCID;

class brokerStatusArray
{
    public static array $brokerStatusArray = [
        1 => 'Pending',
        2 => 'Approved',
        3 => 'Denied',
        4 => 'Proceed with caution',
        5 => 'Cancelled',
        6 => 'Expired',
        7 => 'Terminated',
        9 => 'Approved with YSP',
    ];

    public static array $brokerStatusArrayCV3 = [
        1 => 'New',
        2 => 'Approved',
        3 => 'Denied',
        5 => 'Cancelled',
        6 => 'Expired',
        7 => 'Terminated',
        8 => 'Pending Approval',
        9 => 'Approved with YSP',
    ];

    public static function getForPCID(?int $PCID): array
    {
        return $PCID == glPCID::PCID_PROD_CV3 ? self::$brokerStatusArrayCV3 : self::$brokerStatusArray;
    }
}