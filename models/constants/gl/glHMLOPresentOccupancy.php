<?php

namespace models\constants\gl;

class glHMLOPresentOccupancy
{
    public static array $glHMLOPresentOccupancy = [
        'Vacant',
        'Rented',
        'Tenant Occupied',
        'Delinquent Renter',
        'Delinquent Owner',
        'Owner Occupied',
        'FHA Secondary Residence',
        'Seller Occupied',
    ];

    public static array $glHMLOPresentOccupancyCV3 = [
        'Vacant',
        'Tenant Occupied',
        'Owner Occupied',
        'FHA Secondary Residence',
        'Seller Occupied',
    ];

    public static function getForPCID(?int $PCID): array
    {
        return ($PCID == glPCID::PCID_PROD_CV3) ? glHMLOPresentOccupancy::$glHMLOPresentOccupancyCV3 :
            glHMLOPresentOccupancy::$glHMLOPresentOccupancy;
    }
}