<?php

namespace models\constants\gl;

use models\types\strongType;

class glHMLOCreditScoreRange extends strongType
{
    public static array $glHMLOCreditScoreRange = [
        '720 +',
        '680 - 719',
        '620 - 679',
        '600 - 619',
        '580 - 599',
        '580 or Below',
        'Foreign National- No Score'

    ];

    public static array $glCV3CreditScoreRange = [
        '800+',
        '780-799',
        '760-779',
        '740-759',
        '720-739',
        '720 +',
        '700-719',
        '700 +',
        '680 - 719',
        '680-699',
        '660-679',
        '650 - 699',
        '640-659',
        '620 - 679',
        '620 - 649',
        '620-639',
        '600 - 649',
        '600 - 619',
        'Less than 600',
        '580 - 599',
        '580 or Below',
        'Foreign National- No Score'
    ];
    public static array $glCCCreditScoreRange = [
        '800+',
        '760-799',
        '720-759',
        '680-719',
        '640-679',
        '600-639',
        '599 or Below',
        'Foreign National – No Score'
    ];

    public static array $glHMLOSpreoCreditScoreRange = [
        '740+',
        '701-739',
        '680-700',
        '680',
    ];

    public static array $glHMLOBuildersCapitalCreditScoreRange = [
        '740+',
        '720 +',
        '720-739',
        '700-719',
        '680-699',
        '680 - 719',
        '660-679',
        '640-659',
        'Below 640',
        '620 - 679',
        '600 - 619',
        '580 - 599',
        '580 or Below',
        'Foreign National- No Score'
    ];

    public static function getCreditScoreRange(?int $PCID): array
    {
        switch ($PCID) {
            case glPCID::PCID_PROD_CV3:
                return self::$glCV3CreditScoreRange;
            case glPCID::PCID_CONSISTENT_CAPTIAL:
            case glPCID::PCID_PROD_DAVE:
                return self::$glCCCreditScoreRange;
            case glPCID::PCID_SPREO_CAPITAL:
                return self::$glHMLOSpreoCreditScoreRange;
            case glPCID::PCID_BUILDERS_CAPITAL:
            case glPCID::PCID_DEV_DAVE:
                return self::$glHMLOBuildersCapitalCreditScoreRange;
            default:
                return self::$glHMLOCreditScoreRange;
        }

    }

    public static function getNumericScore(string $score): int
    {
        switch ($score) {
            case '720 +':
            case '700 +':
                return 700;
            case '680 - 719':
                return 719;
            case '650 - 699':
                return 699;
            case '620 - 679':
                return 679;
            case '600 - 649':
                return 649;
            case '600 - 619':
                return 619;
            case 'Less than 600':
                return 600;
            case '580 - 599':
                return 599;
            case '580 or Below':
                return 580;
            case 'Foreign National- No Score':
            default:
                return 0;
        }
    }
}