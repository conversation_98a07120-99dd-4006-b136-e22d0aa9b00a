<?php

namespace models\constants\gl;

class gltypeOfHMLOLoanRequesting
{
    const TYPE_PURCHASE = 'Purchase';
    const TYPE_RATE_TERM_REFINANCE = 'Rate & Term Refinance';
    const TYPE_CASH_OUT_REFINANCE = 'Cash-Out / Refinance';
    const TYPE_REFINANCE = 'Refinance';
    const TYPE_TRANSACTIONAL = 'Transactional';
    const TYPE_COMMERCIAL_PURCHASE = 'Commercial Purchase';
    const TYPE_COMMERCIAL_RATE_TERM_REFINANCE = 'Commercial Rate / Term Refinance';
    const TYPE_COMMERCIAL_CASH_OUT_REFINANCE = 'Commercial Cash Out Refinance';
    const TYPE_LINE_OF_CREDIT = 'Line of Credit';
    const TYPE_NEW_CONSTRUCTION_EXISTING_LAND = 'New Construction - Existing Land';
    const TYPE_DELAYED_PURCHASE = 'Delayed Purchase';

    const gltypeOfHMLOLoanRequesting = [
        self::TYPE_PURCHASE,
        self::TYPE_RATE_TERM_REFINANCE,
        self::TYPE_CASH_OUT_REFINANCE,
        self::TYPE_REFINANCE,
        self::TYPE_TRANSACTIONAL,
        self::TYPE_COMMERCIAL_PURCHASE,
        self::TYPE_COMMERCIAL_RATE_TERM_REFINANCE,
        self::TYPE_COMMERCIAL_CASH_OUT_REFINANCE,
        self::TYPE_LINE_OF_CREDIT,
        self::TYPE_NEW_CONSTRUCTION_EXISTING_LAND,
        self::TYPE_DELAYED_PURCHASE,
    ];

    public static array $gltypeOfHMLOLoanRequesting = self::gltypeOfHMLOLoanRequesting;

    public static array $cv3ExcludeTypeOfHMLOLoanRequesting = [
        self::TYPE_REFINANCE,
        self::TYPE_TRANSACTIONAL,
        self::TYPE_COMMERCIAL_PURCHASE,
        self::TYPE_COMMERCIAL_RATE_TERM_REFINANCE,
        self::TYPE_COMMERCIAL_CASH_OUT_REFINANCE,
        self::TYPE_LINE_OF_CREDIT,
        self::TYPE_NEW_CONSTRUCTION_EXISTING_LAND,
    ];

    public static function ConvertToFannieMae34(?string $type): string
    {
        switch ($type) {
//        New Constructions - Existing Land - Purchase
//        Delayed Purchase - Purchase
//        Purchase - Purchase
//        Commercial Purchase - Purchase

            case self::TYPE_NEW_CONSTRUCTION_EXISTING_LAND:
            case self::TYPE_DELAYED_PURCHASE:
            case self::TYPE_COMMERCIAL_PURCHASE:
            case self::TYPE_PURCHASE:
                return 'Purchase';

//        Rate & Term Refinance - Refinance
//        Cashout Refinance - Refinance
//        Refinance - Refinance
//        Commercial Rate/Term Refinance - Refinance
//        Commercial Cash Out Refinance - Refinance

            case self::TYPE_RATE_TERM_REFINANCE:
            case self::TYPE_CASH_OUT_REFINANCE:
            case self::TYPE_REFINANCE:
            case self::TYPE_COMMERCIAL_RATE_TERM_REFINANCE:
            case self::TYPE_COMMERCIAL_CASH_OUT_REFINANCE:
                return 'Refinance';
        }

        return $type;
    }
}
