<?php

namespace models\constants;

class formFieldDisabledFields
{
    /**
     * Don't control loan program field in Form fields
     * Story: https://app.clubhouse.io/lendingwise/story/1121/what-kind-of-loan-program-are-you-looking-for-inside-loan-programs-should-be-read-only
     */
    public static array $formFieldDisabledFields = [
        'LMRClientType', 'borrowerFName', 'primaryStatus'
    ];

    public static array $formFieldDisabledFieldsMandatory = [
        'LMRBroker', 'REBrokerEmail', 'REBrokerFirstName', 'REBrokerLastName', 'REBrokerCompany', 'brokerPhone'
    ];
}