<?php

namespace models\constants;

class paramValuesArray
{
    public static array $paramValuesArray = [
        'Admin Info' => [
            'PCName' => 'Company Name',
            'branchName' => 'Branch Name',
            'branchEmail' => 'Branch Email',
            'agentName' => 'Broker Name',
            'agentEmail' => 'Broker Email',
            'LMRID' => 'File ID',
            'loanNumber1' => 'Loan Number',
            'LMRServiceType' => 'Loan Program',
            'internalLoanProgram' => 'Internal Loan Program',
            'filestatus' => 'File Status',
            'filessubtatus' => 'Sub Status',
            'projectName' => 'Project Name',
            'receivedDate' => 'Received Date',
            'borrowerCallBack' => 'Borrower Call Back',
            'createdDate' => 'Create Date',
            'closingDate' => 'Actual Closing Date',
            'desiredClosingDate' => 'Desired Closing Date',
            'leadSource' => 'Lead Source',
            '3rdPartyFileId' => '3rd Party File Id'],
        'Borrower Info' => [
            'borrowerFName' => 'First Name',
            'borrowerLName' => 'Last Name',
            'borrowerEmail' => 'Email',
            'borPhone' => 'Phone',
            'borCell' => 'Cell',
            'borPropAddress' => 'Address',
            'propertyCity' => 'City',
            'propertyState' => 'State',
            'propertyZip' => 'Zip',],
        'Property Info' => [
            'borPropAddress' => 'Address',
            'propertyCity' => 'City',
            'propertyState' => 'State',
            'propertyZip' => 'Zip',
            'propertyType' => 'Property Type',
            'zillowValue' => 'Property Zillow Value',
            'estimatedPropValue' => 'Estimated Prop Value'],
        'Business Entity' => [
            'entityName' => 'Entity Name',
            'tradeName' => 'Trade Name',
            'entityAddress' => 'Address',
            'entityCity' => 'City',
            'entityState' => 'State',
            'entityZip' => 'Zip',],
        'Loan Info' => [
            'desiredLoanAmount' => 'Desired Loan Amount',
            'typeOfHMLOLoanRequesting' => 'Transaction Type',
            'lien1Rate' => 'Interest Rate',
            'needRehab' => 'Need Rehab/construction?',
            'costBasis' => 'Purchase Price',
            'homeValue' => 'Property value',
            'maxAmtToPutDown' => 'Down Payment',
            'totalLoanAmount' => 'Total Loan Amount',
            'ARV' => 'ARV',
            'rehabCostFinanced' => 'Rehab/construction cost'],
    ];
}