<?php

namespace models\portals;

use models\composite\LoanServicing;
use models\constants\gl\glUserGroup;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\servicingForm;
use models\cypher;
use models\JSCompiler;
use models\lendingwise\tblFile;
use models\MVC;
use models\PageVariables;
use models\Request;use models\standard\HTTP;

class ServicingPage extends BackofficePage
{
    public static ?LoanServicing $loanServicing = null;
    public static ?tblFile $loan = null;

    public static ?string $params = null;
    public static ?int $LMRId = null;

    public static function Init()
    {
        parent::Init();

        if(PageVariables::$userGroup == glUserGroup::AGENT && !PageVariables::$CurrentAgent->allowServicing) {
            HTTP::Redirect();
        }

        self::setMasterPage(MASTERPAGE_SERVICING);

        Breadcrumb::$title = 'Servicing';
        Breadcrumb::$icon = 'fas fa-home icon-md';
        Breadcrumb::$breadcrumbList = [];
        Breadcrumb::$toolbarHTML = '';

        JSCompiler::initDefaultModules();

        self::$params = 'eId=' . Request::Get('eId') . '&lId=' . Request::Get('lId') . '&rId=' . Request::Get('rId');

        $_REQUEST['tabOpt'] = 'SER2';
        self::$LMRId = intval(cypher::myDecryption($_REQUEST['lId']));

        if(!self::$LMRId) {
            return;
        }

        servicingForm::Init(self::$LMRId);
        LMRequest::setLMRId(self::$LMRId);

        if (!servicingForm::$isValid) {
            if(MVC::$web->class !== 'configure') {
                HTTP::Redirect('/backoffice/loan/servicing/configure?' . self::$params);
            }
        }

        servicingForm::Get();


        self::$loanServicing = servicingForm::$loanServicing;
        self::$loan = servicingForm::$loan;
    }
}