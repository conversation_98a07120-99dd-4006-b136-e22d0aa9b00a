SELECT t1.primaryStatus
     , IFNULL(t2.noOfDays, 0) AS nDays
     , t2.recordDate
     , t130.LMRID             AS LMRId
FROM tblPCPrimeStatus t1
         JOIN tblRecordFileStatus t2 ON t2.statusID = t1.PSID
         JOIN (SELECT MAX(rID) AS rID
                    , t100.LMRID
               FROM tblRecordFileStatus t100
               WHERE t100.LMRID IN ('--LMRIDs--')
               GROUP BY t100.LMRID) t130 ON t130.rID = t2.rID
WHERE t2.LMRID IN ('--LMRIDs--')

