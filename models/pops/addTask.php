<?php

namespace models\pops;

use models\composite\oBranch\getBranchAssignedEmployees;
use models\composite\oBranch\getBranches;
use models\composite\oBranch\getBranchesForAgent;
use models\composite\oBroker\getBranchBrokerList;
use models\composite\oBroker\listAllAgents;
use models\composite\oClient\getFileClientInfo;
use models\composite\oEmployee\getEmployeeAssignedBranches;
use models\composite\oEmployee\getPCEmployeeList;
use models\composite\oFile\getFileInfo;
use models\composite\oTask\getAutomationTasks;
use models\composite\oTask\getFileTasks;
use models\composite\oTask\getTaskDescription;
use models\constants\gl\glPCID;
use models\constants\taskStatusArray;
use models\constants\timeZoneArray;
use models\standard\Dates;

/**
 *
 */
class addTask
{


    /**
     * @param int $ajax
     * @param array $inArray
     * @return string
     */
    public function getFormHtml(int $ajax = 0, array $inArray = []): string
    {
        global $clsOpt;

        $taskStatusArray = taskStatusArray::$taskStatusArray;
        $timeZoneArray = timeZoneArray::$timeZoneArray;
        $jsVr = CONST_JS_VERSION;

        $formHtml1 = '';

        $saveUrl = CONST_URL_POPS . 'addTaskSave.php';

        $LMRInfoResultArray = [];
        $LMRInfoArray = [];
        $empInfoArray = [];
        $BrokerInfoArray = [];
        $BranchInfoArray = [];
        $taskAgentInfo = [];
        $borrowerName = '';
        $taskBranchInfo = [];
        $clientId = 0;
        $taskStatusVal = '';
        $LMRExecutive = '';
        $executiveId = 0;
        $taskEmpInfo = [];
        $brokerNumber = 0;
        $brokerName = '';
        $agentInfoArray = [];
        $LoanOfficerInfoArray = [];
        $branchList = [];
        $comments = '';
        $op = '';
        $LMRClientInfoArray = [];
        $chk = '';
        $hr = '';
        $min = '';
        $ampm = '';
        $hr_rm = '';
        $min_rm = '';
        $ampm_rm = '';
        $reminderDateHrArray = [];
        $assignedEmpArray = [];
        $assignedBranchArray = [];
        $assignedAgentArray = [];
        $dueDateHrArray = [];
        $notesID = 0;
        $assignedToClientId = 0;
        $AssignedStaffArray = [];
        $sendNH = 0;
        $sendNHChk = '';
        $branchIDs = '';
        $isSendLinkToUserChk = '';
        $isSendLinkToUser = 0;
        $sendSMSChk = '';
        $sendSMS = 0;

        $userRole = trim($inArray['userRole']);
        $userNumber = trim($inArray['userNumber']);
        $PCID = trim($inArray['PCID']);
        $LMRId = trim($inArray['LMRId']);
        $taskId = trim($inArray['taskId']);
        $taskOpt = trim($inArray['taskOpt']);
        $subject = trim($inArray['subject']);
        $dueDate = trim($inArray['dueDate']);
        $DIYUser = trim($inArray['DIYUser']);
        $opt = trim($inArray['opt']);
        $opt1 = trim($inArray['opt1']);
        $divId = trim($inArray['divId']);
        $loanSalesDate = trim($inArray['loanSalesDate']);
        $reminderDate = $inArray['reminderDate'];
        $userTimeZone = $inArray['userTimeZone'];
        $userGroup = $inArray['userGroup'];
        if (array_key_exists('notesID', $inArray)) $notesID = $inArray['notesID'];
        $isAutomationTask = $inArray['isAutomationTask'];
        $autoTaskNotAssign = $inArray['autoTaskNotAssign'];
        $optPipe = $opt;
        $dt = $inArray['dt'];

        $taskComments = null;

        if ($notesID > 0) {
            $notesDescResultArray = getTaskDescription::getReport(['notesID' => $notesID]);
            if (count($notesDescResultArray) > 0) {
                for ($h = 0; $h < count($notesDescResultArray); $h++) {
                    $taskComments = rawurldecode($notesDescResultArray[$h]['processorComments']);
                    $taskComments = str_replace('<br>', "\r\n", $taskComments);
                }
                if ($taskComments != '') $comments = $taskComments;
            }
        }

        if (Dates::IsEmpty($dueDate)) {
            if (trim($opt) == 'cal') {
                if (!Dates::IsEmpty($dt)) {
                    $dueDate = trim(Dates::formatDateWithRE($dt, 'YMD', 'm/d/Y'));
                }
            } else {
                $dueDate = '';
            }
            $tempDateTimeHrArray = [];

            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
            $ipArray['outputZone'] = $userTimeZone;
            $ipArray['inputTime'] = Dates::Timestamp();
            $tempDateTime = Dates::timeZoneConversion($ipArray);

            $tempDateTime = trim(Dates::formatDateWithRE($tempDateTime, 'YMD_HMS', 'm/d/Y H:i'));
            $tempDateTimeArray = explode(' ', $tempDateTime);
            if (count($tempDateTimeArray) > 0) {
                if (array_key_exists('1', $tempDateTimeArray)) $tempDateTimeHrArray = explode(':', $tempDateTimeArray[1]);

                if (count($tempDateTimeHrArray) > 0) {
                    $hr = $tempDateTimeHrArray[0];
                    $min = $tempDateTimeHrArray[1];
                    if ($tempDateTimeHrArray[0] > 12) {
                        $hr = $hr - 12;
                        $ampm = 'PM';
                    } else {
                        $ampm = 'AM';
                    }
                }
            }

        } else {
            $dueDate = trim(Dates::formatDateWithRE($dueDate, 'YMD_HMS', 'm/d/Y H:i'));
            $dueDateArray = explode(' ', $dueDate);
            $dueDate = $dueDateArray[0];
            if (count($dueDateArray) > 0) {
                if (array_key_exists('1', $dueDateArray)) $dueDateHrArray = explode(':', $dueDateArray[1]);

                if (count($dueDateHrArray) > 0) {
                    $hr = $dueDateHrArray[0];
                    $min = $dueDateHrArray[1];
                    if ($dueDateHrArray[0] > 12) {
                        $hr = $hr - 12;
                        $ampm = 'PM';
                    } else {
                        $ampm = 'AM';
                    }
                }
            }
        }

        if (Dates::IsEmpty($reminderDate)) {
            $reminderDate = '';

            $tempDateTimeHrArray = [];

            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
            $ipArray['outputZone'] = $userTimeZone;
            $ipArray['inputTime'] = Dates::Timestamp();
            $tempDateTime = Dates::timeZoneConversion($ipArray);

            $tempDateTime = trim(Dates::formatDateWithRE($tempDateTime, 'YMD_HMS', 'm/d/Y H:i'));
            $tempDateTimeArray = explode(' ', $tempDateTime);
            if (count($tempDateTimeArray) > 0) {
                if (array_key_exists('1', $tempDateTimeArray)) $tempDateTimeHrArray = explode(':', $tempDateTimeArray[1]);

                if (count($tempDateTimeHrArray) > 0) {
                    $hr_rm = $tempDateTimeHrArray[0];
                    $min_rm = $tempDateTimeHrArray[1];
                    if ($tempDateTimeHrArray[0] > 12) {
                        $hr_rm = $hr_rm - 12;
                        $ampm_rm = 'PM';
                    } else {
                        $ampm_rm = 'AM';
                    }
                }
            }
        } else {
            $reminderDate = trim(Dates::formatDateWithRE($reminderDate, 'YMD_HMS', 'm/d/Y H:i'));
            $reminderDateArray = explode(' ', $reminderDate);
            $reminderDate = $reminderDateArray[0];
            if (count($reminderDateArray) > 0) {

                if (array_key_exists('1', $reminderDateArray)) $reminderDateHrArray = explode(':', $reminderDateArray[1]);
                if (count($reminderDateHrArray) > 0) {
                    $hr_rm = $reminderDateHrArray[0];
                    $min_rm = $reminderDateHrArray[1];
                    if ($reminderDateHrArray[0] > 12) {
                        $hr_rm = $hr_rm - 12;
                        $ampm_rm = 'PM';
                    } else {
                        $ampm_rm = 'AM';
                    }
                }
            }
        }
        $priorityLevel = $inArray['priorityLevel'];

        $SecondaryBrokerInfoArray = [];

        if ($LMRId > 0) $LMRInfoResultArray = getFileInfo::getReport($inArray);
        if (array_key_exists($LMRId, $LMRInfoResultArray)) {
            $LMRInfoKeyArray = $LMRInfoResultArray[$LMRId];
            $LMRInfoArray = $LMRInfoKeyArray['LMRInfo'];
            $BranchInfoArray = $LMRInfoKeyArray['BranchInfo'];
            $BrokerInfoArray = $LMRInfoKeyArray['BrokerInfo'];
            $SecondaryBrokerInfoArray = $LMRInfoKeyArray['SecondaryBrokerInfo'];

            if (array_key_exists('AssignedStaffInfo', $LMRInfoKeyArray)) $AssignedStaffArray = $LMRInfoKeyArray['AssignedBOStaffInfo'];
        }
        $staffArray = [];
        for ($i = 0; $i < count($AssignedStaffArray); $i++) {
            $staffArray[trim($AssignedStaffArray[$i]['AID'])] = trim($AssignedStaffArray[$i]['AID']);
        }

        if (count($LMRInfoArray) > 0) {
            $borrowerName = $LMRInfoArray['borrowerName'];
            $borrowerLName = $LMRInfoArray['borrowerLName'];
            $borrowerName = trim($borrowerName . ' ' . $borrowerLName);
            $clientId = $LMRInfoArray['clientId'];
        }
        if (count($BranchInfoArray) > 0) {
            $LMRExecutive = trim($BranchInfoArray['LMRExecutive']);
            $executiveId = trim($BranchInfoArray['executiveId']);
            $PCID = trim($BranchInfoArray['processingCompanyId']);
        }
        if (count($BrokerInfoArray) > 0) {
            $brokerFName = $BrokerInfoArray['firstName'];
            $brokerLName = $BrokerInfoArray['lastName'];
            $brokerName = ucwords($brokerFName . ' ' . $brokerLName);
            $brokerNumber = $BrokerInfoArray['userNumber'];
        }
        $SecondarybrokerNumber = null;
        $SecondarybrokerName = null;
        if (count($SecondaryBrokerInfoArray ?? []) > 0) {
            $SecondarybrokerFName = $SecondaryBrokerInfoArray['firstName'];
            $SecondarybrokerLName = $SecondaryBrokerInfoArray['lastName'];
            $SecondarybrokerName = ucwords($SecondarybrokerFName . ' ' . $SecondarybrokerLName);
            $SecondarybrokerNumber = $SecondaryBrokerInfoArray['userNumber'];
        }
        $assignedEmpArray1 = [];
//        if ($userNumber > 0 && $userRole == 'Agent') {
//            $empInfoArray = $oBroker->listAllAgentEmployees(array('brokerNumber' => $userNumber, 'allowToCommunicate' => '1', 'opt' => 'noNeed'));
//        } else {
        if ($PCID > 0) {
            $ae = $userRole == 'Manager' ? 0 : 1;
            $ipArray = [
                'PCID' => $PCID,
                'activeStatus' => '1',
                'ae' => $ae,
            ];
            $empInfoArray = getPCEmployeeList::getReport($ipArray);
            $inp = ['PCID' => $PCID];
            $assignedEmpArray1 = getBranchAssignedEmployees::getReport($inp);
            if ($userGroup == 'CFPB Auditor' || $userGroup == 'Auditor Manager') {
                $auditoremployeeInfo = getPCEmployeeList::getReport(['CFPBPC' => $PCID, 'role' => "CFPB Auditor', 'Auditor Manager", 'activeStatus' => 1, 'onlyCFPBAuditor' => 1]);
                $empInfoArray = array_merge($auditoremployeeInfo, $empInfoArray);
            }
        }
        //}

        if ($dueDate != '') {
            $dueDate = $dueDate . ' ' . $hr . ':' . $min . ' ' . $ampm;
        }
        if ($reminderDate != '') {
            $reminderDate = $reminderDate . ' ' . $hr_rm . ':' . $min_rm . ' ' . $ampm_rm;
        }

        // $showTime = Dates::displayTime("dueTime", $hr, $min, $ampm, '6', '7', '8');
        // $showTime1 = Dates::displayTime("reminderTime", $hr_rm, $min_rm, $ampm_rm, '6', '7', '8');

        if ($LMRId == 0) {
            if ($userRole == 'Branch') {
                $inArray['executiveId'] = $userNumber;
                $LMRClientInfoArray = getFileClientInfo::getReport($inArray);
                $ip = ['execID' => $userNumber];
                $BranchInfoArray = getBranches::getReport($ip);
                if (count($BranchInfoArray) > 0) $branchList = $BranchInfoArray['branchList'];
                $ipArray = ['executiveId' => $userNumber];
                $agentInfoArray = getBranchBrokerList::getReport($ipArray);
            } else if ($userRole == 'Agent') {
                $inArray['brokerNumber'] = $userNumber;
                $LMRClientInfoArray = getFileClientInfo::getReport($inArray);

                $ip = ['brokerNumber' => $userNumber];
                $branchList = getBranchesForAgent::getReport($ip);
                $ip = ['agentId' => $userNumber];
                $agentInfo = \models\composite\oBroker\getMyDetails::getReport($ip);
                if (array_key_exists($userNumber, $agentInfo)) {
                    $brokerNumber = trim($agentInfo[$userNumber]['agentId']);
                    $brokerName = trim($agentInfo[$userNumber]['brokerName']);
                }

            } else if ($PCID > 0) {

                $assignedBranches = [];
                if ($userNumber > 0) {
                    $assignedBranches = getEmployeeAssignedBranches::getReport(['EID' => $userNumber]);
                }
                if (count($assignedBranches) > 0) {
                    if (array_key_exists($userNumber, $assignedBranches)) {
                        for ($pcc = 0; $pcc < count($assignedBranches[$userNumber]); $pcc++) {
                            if ($pcc > 0) {
                                $branchIDs .= ', ';
                            }
                            $branchIDs .= trim($assignedBranches[$userNumber][$pcc]['LMRAEID']);
                        }
                        $ip['execID'] = $branchIDs;
                        $ip['executiveId'] = $branchIDs;
                    }
                }
                $ip = ['PCID' => $PCID, 'userGroup' => $userGroup];
                if ($userRole != 'Manager') {
                    if ($branchIDs != '') {
                        $ip['branchIDs'] = $branchIDs;
                    }
                    $ip['executiveId'] = $branchIDs; //show only branch assigned data
                    $ip['execID'] = $branchIDs; //show only branch assigned data
                }
                $LMRClientInfoArray = getFileClientInfo::getReport($ip);
                $BranchInfoArray = getBranches::getReport($ip);
                if (count($BranchInfoArray) > 0) $branchList = $BranchInfoArray['branchList'];
                $ip['opt'] = 'list';
                $agentInfoArray = listAllAgents::getReport($ip);
            }
            if (count($agentInfoArray) > 0) {
                $agentInfoArrayTemp = [];
                $LoanOfficerInfoArrayTemp = [];
                foreach ($agentInfoArray as $eachAgentInfo) {
                    if ($eachAgentInfo['externalBroker'] == 0) {
                        $agentInfoArrayTemp[] = $eachAgentInfo;
                    }
                    if ($eachAgentInfo['externalBroker'] == 1) {
                        $LoanOfficerInfoArrayTemp[] = $eachAgentInfo;
                    }
                }
                $agentInfoArray = $agentInfoArrayTemp;
                $LoanOfficerInfoArray = $LoanOfficerInfoArrayTemp;
            }
        } else {
            $empInfoArray = $LMRInfoKeyArray['employeeInfo'];
        }
        $branchIDsArray = [];
        if ($branchIDs != '') {
            $branchIDsArray = explode(', ', $branchIDs);
        }
        $userNameArray = [];

        for ($pcc = 0; $pcc < count($empInfoArray); $pcc++) {
            $empId = ucwords(trim($empInfoArray[$pcc]['AID']));
            if (count($assignedEmpArray1) > 0) {
                if (array_key_exists($empId, $assignedEmpArray1) && $executiveId > 0) {
                    for ($j = 0; $j < count($assignedEmpArray1[$empId]); $j++) {
                        if ($executiveId == trim($assignedEmpArray1[$empId][$j]['LMRAEID'])) {
                            $userNameArray[] = $empInfoArray[$pcc];
                        }
                    }
                } else if (array_key_exists($empId, $assignedEmpArray1)) {
                    $t = 0;
                    for ($j = 0; $j < count($assignedEmpArray1[$empId]); $j++) {
                        if (in_array(trim($assignedEmpArray1[$empId][$j]['LMRAEID']), $branchIDsArray) && $t == 0) {
                            $userNameArray[] = $empInfoArray[$pcc];
                            $t++;
                        }
                    }
                } else {
                    $userNameArray[] = $empInfoArray[$pcc];
                }
            } else {
                $userNameArray[] = $empInfoArray[$pcc];
            }
        }
        $empInfoArray = $userNameArray;

        $ipArray['taskId'] = $inArray['taskId'];
        $ipArray['LMRId'] = $inArray['LMRId'];
        $ipArray['userGroup'] = $inArray['userGroup'];

        $lId = $inArray['LMRId'];
        $tId = $inArray['taskId'];
        if ($tId > 0) {
            $taskDetails = getFileTasks::getReport($ipArray);
            $taskArray = $taskDetails[$lId];
            $taskStatusVal = '';
            $priorityLevel = '';
            $comments = '';
            if (count($taskArray) > 0) {
                if (array_key_exists($tId, $taskArray['taskArray'])) {
                    $tempTaskArray = $taskArray['taskArray'][$tId];

                    for ($j = 0; $j < count($tempTaskArray); $j++) {

                        $subject = $tempTaskArray[$j]['subject'];
                        $subject = str_replace("\"", '&quot;', $subject);

                        $taskStatusVal = $tempTaskArray[$j]['taskStatus'];
                        $priorityLevel = $tempTaskArray[$j]['priorityLevel'];
                        $comments = $tempTaskArray[$j]['comments'];
                        $comments = str_replace('<br>', "\n", $comments);
                        //remove html tags
                        //$comments = strip_tags($comments);  //   //was killing the urls in comment
                        $isSendLinkToUser = $tempTaskArray[$j]['isSendLinkToUser'];
                        $sendSMS = $tempTaskArray[$j]['sendSMS'];
                        $sendNH = $tempTaskArray[$j]['sendNH'];
                        $sendNotificationForTaskCreation = $tempTaskArray[$j]['sendNotificationForTaskCreation'];

                        $dueDate1 = $tempTaskArray[$j]['dueDate'];

                        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                        $ipArray['outputZone'] = $userTimeZone;
                        $ipArray['inputTime'] = $dueDate1;
                        $dueDate1 = Dates::timeZoneConversion($ipArray);

                        $dueDate = trim(Dates::formatDateWithRE($dueDate1, 'YMD_HMS', 'm/d/Y H:i'));
                        $dueDateArray = explode(' ', $dueDate);
                        $dueDate = $dueDateArray[0];
                        if (count($dueDateArray) > 0) {
                            $dueDateHrArray = explode(':', $dueDateArray[1]);
                            $hr = $dueDateHrArray[0];
                            $min = $dueDateHrArray[1];
                            if ($dueDateHrArray[0] > 12) {
                                $hr = $hr - 12;
                                $ampm = 'PM';
                            } else {
                                $ampm = 'AM';
                            }
                            //  $dueDate = $dueDate.' '.$hr.':'.$min.' '.$ampm;
                        }

                        //                $dueDate1Array = explode(" ",$dueDate1);
                        //			$dueDate = Dates::formatDateWithRE($dueDate1Array[0], 'YMD', 'm/d/Y');
                        $reminderDate1 = $taskArray['taskArray'][$tId][0]['reminderDate'];

                        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                        $ipArray['outputZone'] = $userTimeZone;
                        $ipArray['inputTime'] = $reminderDate1;
                        $reminderDate1 = Dates::timeZoneConversion($ipArray);

                        $reminderDate = trim(Dates::formatDateWithRE($reminderDate1, 'YMD_HMS', 'm/d/Y H:i'));
                        $reminderDateArray = explode(' ', $reminderDate);
                        $reminderDate = $reminderDateArray[0];
                        if (count($reminderDateArray) > 0) {
                            if (array_key_exists('1', $reminderDateArray)) $reminderDateHrArray = explode(':', $reminderDateArray[1]);

                            if (count($reminderDateHrArray) > 0) {
                                $hr_rm = $reminderDateHrArray[0];
                                $min_rm = $reminderDateHrArray[1];
                                if ($reminderDateHrArray[0] > 12) {
                                    $hr_rm = $hr_rm - 12;
                                    $ampm_rm = 'PM';
                                } else {
                                    $ampm_rm = 'AM';
                                }
                            }
                        }

                        if (array_key_exists('fileID', $tempTaskArray[$j])) $lId = $tempTaskArray[$j]['fileID'];

                        $receiverUserType = trim($tempTaskArray[$j]['receiverUserType']);
                        $receiverId = trim($tempTaskArray[$j]['employeeId']);

                        if (($receiverUserType == 'Employee' || $receiverUserType == 'CFPB Auditor' || $receiverUserType == 'Auditor Manager') && $receiverId > 0) {
                            $assignedEmpArray[$receiverId] = $receiverId;
                        }
                        if ($receiverUserType == 'Branch' && $receiverId > 0) {
                            $assignedBranchArray[$receiverId] = $receiverId;
                        }
                        if ($receiverUserType == 'Agent' && $receiverId > 0) {
                            $assignedAgentArray[$receiverId] = $receiverId;
                        }
                        if ($receiverUserType == 'Client' && $receiverId > 0) {
                            $assignedToClientId = $receiverId;
                        }
                    }

                }
            }

            $taskBranchInfo = $taskArray['taskBranchInfo'];
            $taskAgentInfo = $taskArray['taskAgentInfo'];
            $taskClientInfo = $taskArray['taskClientInfo'];
            $taskEmpInfo = $taskArray['taskEmpInfo'];

            $clCnt1 = count($taskClientInfo);
            if ($clCnt1 > 0) {
                if ($lId > 0) {
                    $LMRId = $lId;
                    if (trim($lId) == trim($assignedToClientId)) $chk = 'checked';
                }
            }
        }

        if ($loanSalesDate != '' && $opt == 'QAForm') {
            $dueDate = $loanSalesDate;
            $reminderDate = $loanSalesDate;
        }
        if ($dueDate != '') {
            $dueDate = $dueDate . ' ' . $hr . ':' . $min . ' ' . $ampm;
        }
        if ($reminderDate != '') {
            $reminderDate = $reminderDate . ' ' . $hr_rm . ':' . $min_rm . ' ' . $ampm_rm;
        }

        $showTime = Dates::displayTime('dueTime', $hr, $min, $ampm, '6', '7', '8');
        $showTime1 = Dates::displayTime('reminderTime', $hr_rm, $min_rm, $ampm_rm, '6', '7', '8');
        $httprefi = $_SERVER['HTTP_REFERER'];

        $displayTimeZone = '';

        if (array_key_exists($userTimeZone, $timeZoneArray)) {
            $displayTimeZone = trim($timeZoneArray[$userTimeZone]);
        }


        /* Customization for pc 1495 = aspire processing on 15 Dec 2015*/
        if (($PCID == 1495 || $PCID == 2 || $PCID == 820) && $taskId == 0) {
            $sendSMS = 1;
        }
        if($PCID == glPCID::PCID_PROD_CV3){
            $sendNotificationForTaskCreation = 1;
        }

        if ($isSendLinkToUser == 1) {
            $isSendLinkToUserChk = 'checked';

        }
        if ($sendSMS == 1) {
            $sendSMSChk = 'checked';
        }
        if ($sendNH == 1) {
            $sendNHChk = 'checked';
        }
        if($sendNotificationForTaskCreation){
            $sendNotificationForTaskCreationChk = 'checked';
        }
        /** Customization for Automation Task with no assignee **/

        if ($isAutomationTask && $autoTaskNotAssign) {
            $priorityLevel = $inArray['priorityLevel'];
            $taskStatusVal = trim($inArray['taskStatus']);
            $autoTaskParams = ['taskId' => $taskId];
            $autoTasks = getAutomationTasks::getReport($autoTaskParams);
            $comments = $autoTasks[0]['comments'];
            //$comments = $inArray['autoTaskComments'];
            $comments = str_replace('<br>', "\r\n", $comments);
            //$comments = strip_tags($comments);   //was killing the urls in comment
        }

        $formHtml1 .= <<<EOT
<script>
function submitButton(){
      document.getElementById("myButton").disabled = true;
      setTimeout(function(){document.getElementById("myButton").disabled = false;},3000);
}
</script>
 <script type="text/javascript" src="/assets/js/task.js?{$jsVr}"></script>         
 <script type="text/javascript" src="/assets/js/popup.js?{$jsVr}"></script>         
 <form name="addTaskForm" id="addTaskForm" method="POST"  action="$saveUrl" >
	  <input type="hidden" name="httpReferer" value="$httprefi" />
      <input type="hidden" name="taskId" id="taskId" value="$taskId">
      <input type="hidden" name="opt" id="opt" value="$optPipe">
      <input type="hidden" name="opt1" id="opt1" value="$opt1">
	  <input type="hidden" id="taskOpt" name="taskOpt" value="$taskOpt">
      <input type="hidden" name="previousStatus" id="previousStatus" value="$taskStatusVal">
      <input type="hidden" name="DIYClientId" id="DIYClientId" value="$clientId">
      <input type="hidden" name="DIYUser" id="DIYUser" value="$DIYUser">
      <input type="hidden" name="divId" id="divId" value="$divId" >
      <input type="hidden" name="recordTimeZone" id="recordTimeZone" value="$userTimeZone" >
      
 <div class="card-body p-0">
EOT;
        if ($taskOpt != 'General') {
            $checkVal = 'checked';
            $checkVal1 = '';
            $dispVal = 'display:block';
            if ($LMRId > 0 && $taskId > 0) {
                $checkVal = 'checked';
                $checkVal1 = '';
            } else if ($taskId > 0) {
                $checkVal = '';
                $checkVal1 = 'checked';
                $dispVal = 'display:none';
            }
            if (trim($opt) == 'file' || trim($opt) == 'pipeline' || trim($opt) == 'QAForm') {
                $formHtml1 .= <<<EOT
<div class="form-group row ">
    <label class="font-weight-bold  col-lg-3" >Task/Reminder type:</label>        
    <div class="col-lg-9">Related to client</div>
</div>
EOT;
            } else {
                $formHtml1 .= <<<EOT
 <div class="form-group row ">
      <label class="font-weight-bold  col-lg-3" >Task/Reminder type:</label>
      <div class="col-lg-9">
            <div class="radio-inline">
                <label class="font-weight-bold radio">
                    <input type="radio" name="taskType" value=""  $checkVal onclick="showClientTaskdiv('block');">
                    <span></span> Related To Client
                </label>
                <label class="font-weight-bold radio">
                    <input type="radio" name="taskType" value=""  $checkVal1 onclick="showClientTaskdiv('none');">
                    <span></span> General Task/Reminder
                </label>
          </div>
	  </div>
</div>
EOT;
            }
            if (!$LMRId) {
                $formHtml1 .= <<<EOT
        <div class="form-group row ">
        <label class="font-weight-bold  col-lg-3" id="divRelatedToTask" style="$dispVal">Task related to client:</label>
        <div class="col-lg-7"  id="divRelatedToTask1" style="$dispVal">
        <input type="hidden" name="LMRId" id="LMRId" value="$assignedToClientId">
                  <select name="clientId_LMRId"
                          id="clientId_LMRId"
                          tabindex="1" 
                          class="form-control" 
                          onchange="displayBorrowerName(this);">
                    <option value=""> - Select - </option>
EOT;
                $lmCnt = count($LMRClientInfoArray);
                $chk = '';
                for ($i = 0; $i < $lmCnt; $i++) {
                    $sel = '';
                    $chk = '';
                    $clientLMRId = trim($LMRClientInfoArray[$i]['LMRId']);
                    $clientId_LMRId = trim($LMRClientInfoArray[$i]['clientIDTask']).'##'.trim($LMRClientInfoArray[$i]['LMRId']);
                    $clientName = ucwords(trim($LMRClientInfoArray[$i]['clientName']));
                    $clientEmail = ucwords(trim($LMRClientInfoArray[$i]['clientEmail']));
                    if (trim($clientLMRId) == trim($assignedToClientId)) {
                        $sel = 'selected';
                        $chk = 'checked';
                    }
                    $formHtml1 .= <<<EOT
                    <option value="$clientId_LMRId" $sel> $clientName ($clientEmail)</option>
EOT;
                }
                $formHtml1 .= <<<EOT
                  </select>
           	  </div>
        </div>
EOT;
            } else {
                $formHtml1 .= <<<EOT
      <input type="hidden" name="LMRId" id="LMRId" value="$LMRId">
EOT;

            }
            $formHtml1 .= <<<EOT
            <input type="hidden" id="validationField" name="validationField">
         <div class="form-group row ">
                <label class="font-weight-bold  col-lg-3" id="divAssignTaskClient" style="$dispVal">Assign task to client:</label>
                <div class='col-lg-7' id="divTaskClient" style="$dispVal">
                    <div class='checkbox-inline'>
                        <label class='checkbox checkbox-square'>
                                <input type="checkbox" name="selectedClient" id="selectedClient" $chk value="$clientId"><span></span>
                                <div id="selectedBorrowerName">$borrowerName</div>
                        </label>
                    </div>
                </div>
        </div>
EOT;
        }
        $parentPCText = '';
        if ($userRole == 'Auditor') {
            $parentPCText = 'parent PC ';
        }
        $formHtml1 .= <<<EOT
        
        <div class="form-group row ">
        <label class="font-weight-bold  col-lg-3" >Assign task to $parentPCText Employee(s):</label>
	    <div class='col-lg-7' >
		  <select  title="- Select Employee(s) -"  name="employeeIds[]"  id= "employeeIds" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true" data-size="5"  data-actions-box="true"  >
EOT;
        $empCnt = count($empInfoArray);
        $empCnt1 = 0;
        for ($e = 0; $e < $empCnt; $e++) {
            $selVal = '';
            $employeeName = ucwords(trim($empInfoArray[$e]['processorName']));
            $employeeId = trim($empInfoArray[$e]['AID']);
            $employeeRole = trim($empInfoArray[$e]['role']);
            $empCnt1 = count($taskEmpInfo);
            $clsName = 'text-dark-75';
            if (array_key_exists($employeeId, $staffArray)) {
                $clsName = 'bg-danger-o-40 text-white';
            }

            if ($employeeRole == 'CFPB Auditor' || $employeeRole == 'Auditor Manager') {
                $clsName = 'bg-warning-o-40 text-white';
            }

            if ($empCnt1 > 0) {
                for ($k = 0; $k < $empCnt1; $k++) {
                    if (array_key_exists($employeeId, $assignedEmpArray)) $selVal = 'selected';
                }
            }
            $formHtml1 .= <<<EOT
      	<option value="$employeeId" class="$clsName $clsOpt" $selVal data-subtext="$employeeRole" >$employeeName</option>
EOT;
        }
        $formHtml1 .= <<<EOT
</select>	
		    </div>
</div>
EOT;
        if ($userNumber == CONST_AUDITOR_2_ID) {
            $empInfoArray = [];
            if (CONST_AUDITOR_2_ID > 0) {
                $empInfoArray = getPCEmployeeList::getReport(['PCID' => '1244', 'activeStatus' => 1]);
            }
            $formHtml1 .= <<<EOT
        <div class="form-group row ">
        <label class="font-weight-bold  col-lg-3" >Assign task to Prodigy<br>Fulfillment Center PC employee(s):</label>
         	<div class="col-lg-7">
 			<div class="col-lg-12">
 			 <div class="col-lg-6">
EOT;
            $empCnt = count($empInfoArray);
            $empCnt1 = 0;
            for ($e = 0; $e < $empCnt; $e++) {
                $selVal = '';
                $employeeName = ucwords(trim($empInfoArray[$e]['processorName']));
                $employeeId = trim($empInfoArray[$e]['AID']);
                $employeeRole = trim($empInfoArray[$e]['role']);
                $empCnt1 = count($taskEmpInfo);
                $clsName = '#000000';
                if (array_key_exists($employeeId, $staffArray)) $clsName = '#ee0000';

                if ($empCnt1 > 0) {
                    for ($k = 0; $k < $empCnt1; $k++) {
                        if (array_key_exists($employeeId, $assignedEmpArray)) $selVal = 'checked';
                    }
                }
                $formHtml1 .= <<<EOT
<input type="checkbox" name="employeeId[]" id="employeeId" $selVal value="$employeeId" onclick="getMultiCheckValue('addTaskForm', 'employeeId', 'employeeIds');"> <span style="color:$clsName"><b>$employeeName </span> ($employeeRole)
EOT;
            }
            $formHtml1 .= <<<EOT
 </div>			
</div>
	</div>
</div>
EOT;
        }

        $formHtml1 .= <<<EOT
       <div class="form-group row ">
        <label class="font-weight-bold  col-lg-3" >Assign task to Broker(s):</label>
		<div class="col-lg-7">
EOT;
        $agCnt = count($agentInfoArray);
        $selAgVal = '';
        if ($agCnt > 0) {
            $formHtml1 .= <<<EOT
		   <select  title="- Select Broker(s) -"  name="agentIds[]"  id= "agentIds" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true" data-size="5"  data-actions-box="true"  >
EOT;
            for ($a = 0; $a < $agCnt; $a++) {
                $selAgVal = '';
                if ($userRole == 'Branch') {
                    $brokerName = ucwords(trim($agentInfoArray[$a]['brokerName']));
                } else {
                    $brokerName = ucwords(trim($agentInfoArray[$a]['bName']) . ' ' . trim($agentInfoArray[$a]['bLName']));
                }
                $brokerNumber = trim($agentInfoArray[$a]['brokerNumber']);
                $agCnt1 = count($taskAgentInfo);
                if ($agCnt1 > 0) {
                    for ($k = 0; $k < $agCnt1; $k++) {
                        if (array_key_exists($brokerNumber, $assignedAgentArray)) {
                            $selAgVal = 'selected';
                        }
                    }
                }
                $formHtml1 .= <<<EOT
<option value="$brokerNumber" $selAgVal>$brokerName</option>
EOT;
            }
            $formHtml1 .= <<<EOT
		</select>
EOT;
        } else {
            if (array_key_exists($brokerNumber, $assignedAgentArray)) $selAgVal = 'checked';
            $formHtml1 .= <<<EOT
  <div class='checkbox-inline'>
                <label class='checkbox checkbox-square'>
            <input type="checkbox" name="agentIds[]" id="agentIds"  $selAgVal value="$brokerNumber" > <span></span>$brokerName</label></div>
EOT;
        }
        $formHtml1 .= <<<EOT
</div>
		</div>
EOT;

        if ($LMRId == 0) {
            $agCnt = count($LoanOfficerInfoArray);
            if ($agCnt > 0) {
                $formHtml1 .= <<<EOT
		<!--Loan Officer -->
		   <div class="form-group row ">
        <label class="font-weight-bold  col-lg-3" >Assign task to Loan Officer: </label>
		<div class="col-lg-7">
EOT;
                $formHtml1 .= <<<EOT
<select  title="- Select Loan Officer(s) -"  name="secondaryAgentIds[]"  id= "secondaryAgentIds" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true" data-size="5"  data-actions-box="true" >
EOT;
                for ($a = 0; $a < $agCnt; $a++) {
                    $selAgVal = '';
                    if ($userRole == 'Branch') {
                        $brokerName = ucwords(trim($LoanOfficerInfoArray[$a]['brokerName']));
                    } else {
                        $brokerName = ucwords(trim($LoanOfficerInfoArray[$a]['bName']) . ' ' . trim($LoanOfficerInfoArray[$a]['bLName']));
                    }
                    $brokerNumber = trim($LoanOfficerInfoArray[$a]['brokerNumber']);
                    $agCnt1 = count($taskAgentInfo);
                    if ($agCnt1 > 0) {
                        for ($k = 0; $k < $agCnt1; $k++) {
                            if (array_key_exists($brokerNumber, $assignedAgentArray)) $selAgVal = 'selected';
                        }
                    }
                    $formHtml1 .= <<<EOT
<option value="$brokerNumber" $selAgVal>$brokerName</option>
EOT;
                }
                $formHtml1 .= <<<EOT
		 </select>
</div>
		</div>
<!--End Of Loan Officer -->
EOT;
            }
        } else {
            $selAgVal = '';

            if ($SecondarybrokerNumber != '' && $SecondarybrokerNumber > 0) {
                if (array_key_exists($SecondarybrokerNumber, $assignedAgentArray)) $selAgVal = 'checked';
                $formHtml1 .= <<<EOT
	   <div class="form-group row ">
        <label class="font-weight-bold  col-lg-3" >Assign task to Loan Officer:</label>
            <div class="col-lg-7">
                    <div class='checkbox-inline'>
                        <label class='checkbox checkbox-square'>    <input type="checkbox" name="secondaryAgentIds[]" id="secondaryAgentIds"  $selAgVal value="$SecondarybrokerNumber"><span></span>$SecondarybrokerName</label>
                    </div>
            </div>
        </div>
EOT;
            }
        }

        $formHtml1 .= <<<EOT
	   <div class="form-group row ">
        <label class="font-weight-bold  col-lg-3" >Assign task to Branch(s):</label>
		<div class="col-lg-7">
EOT;
        $brCnt = count($branchList);
        $selbrVal = '';
        if ($brCnt > 0) {
            $formHtml1 .= <<<EOT
<select  title="- Select Branch(s) -"  name="branchIds[]"  id= "branchIds" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true" data-size="5"  data-actions-box="true"  style="width:250px;">
EOT;
            for ($b = 0; $b < $brCnt; $b++) {
                $selbrVal = '';
                $LMRExecutive = ucwords(trim($branchList[$b]['LMRExecutive']));
                $executiveId = trim($branchList[$b]['executiveId']);
                $brCnt1 = count($taskBranchInfo);
                if ($brCnt1 > 0) {
                    for ($k = 0; $k < $empCnt1; $k++) {
                        if (array_key_exists($executiveId, $assignedBranchArray)) $selbrVal = 'selected';
                    }
                }
                $formHtml1 .= <<<EOT
<option value="$executiveId" $selbrVal>$LMRExecutive</option>
EOT;
            }
            $formHtml1 .= <<<EOT
</select>
EOT;
        } else {
            if (array_key_exists($executiveId, $assignedBranchArray)) $selbrVal = 'checked';
            $formHtml1 .= <<<EOT
    <div class='checkbox-inline'>
                <label class='checkbox checkbox-square'>
            <input type="checkbox" name="branchIds[]" id="branchIds" $selbrVal value="$executiveId"> <span></span>$LMRExecutive</label></div>
EOT;
        }
        $formHtml1 .= <<<EOT
	</div>
</div>

	   <div class="form-group row ">
            <label class="font-weight-bold  col-lg-3" >Subject:</label>
            <div class="col-lg-7">
                <input type="text" placeholder="Please Enter Subject" name="taskSubj" id="taskSubj" value="$subject" size="50" tabindex="3" class="form-control mandatory">
            </div>
		</div>
	
 <div class="form-group row ">
       <label class="font-weight-bold  col-lg-3 " >Due date:</label>
EOT;
        if (trim($dueDate) == '') {
            $dueDate = date('Y-m-d h:i A');
            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
            $ipArray['outputZone'] = $userTimeZone;
            $ipArray['inputTime'] = $dueDate;
            $dueDate = Dates::timeZoneConversion($ipArray);
            $dueDate = trim(Dates::formatDateWithRE($dueDate, 'YMD_HMS', 'm/d/Y H:i'));
            $dueDate = Dates::DateWithTimeFormat($dueDate);
        }
        if ($taskId == '0' && trim($reminderDate) == '') {
            $reminderDate = date('Y-m-d h:i A');
            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
            $ipArray['outputZone'] = $userTimeZone;
            $ipArray['inputTime'] = $reminderDate;
            $reminderDate = Dates::timeZoneConversion($ipArray);
            $reminderDate = trim(Dates::formatDateWithRE($reminderDate, 'YMD_HMS', 'm/d/Y H:i'));
            $reminderDate = Dates::DateWithTimeFormat($reminderDate);
        }
        $formHtml1 .= <<<EOT
		<div class="col-lg-7">			
		            <div class="input-group  " >
		      <input type="text" autocomplete="off" class="form-control datetimepicker-input  dateTimeClass" name="dueDate" id="dueDate"   placeholder="Select date & time" value="$dueDate" size="25" maxlength="20">
                <div class="input-group-append">
                    <span class="input-group-text">
                    <i class="ki ki-calendar"></i>
                    </span>
                </div>
            </div>
              <span class="form-text text-muted">$displayTimeZone,(MM-DD-YYYY HH:MM AM/PM 12 hours calendar)</span>
    </div>
</div>

 <div class="form-group row ">
       <label class="font-weight-bold  col-lg-3" >Reminder date:</label>
	<div class="col-lg-7">
	    <div class="input-group  " >
  	      <input type="text" autocomplete="off" class="form-control datetimepicker-input dateTimeClass" data-target=".dateTimeClass" name="reminderDate" id="reminderDate"    placeholder="Select date & time" value="$reminderDate" size="25" maxlength="20">
  	        <div class="input-group-append">
                    <span class="input-group-text">
                    <i class="ki ki-calendar"></i>
                    </span>
                </div>
            </div>
  	      
    <span class="form-text text-muted">$displayTimeZone * Reminder Date is required in order to generate an email.(MM-DD-YYYY HH:MM AM/PM 12 hours calendar)</span>
   </div>
</div>

 <div class="form-group row ">
       <label class="font-weight-bold  col-lg-3" >Status:</label>
	<div class="col-lg-7"><select name="taskStatus" id="taskStatus1" tabindex="9" class="form-control mandatory">
			  <option value=""> - Select - </option>
EOT;
        for ($ts = 0; $ts < count($taskStatusArray); $ts++) {
            $optVal = '';
            $tempTaskStatus = $taskStatusArray[$ts];
            if ($tempTaskStatus == $taskStatusVal) $optVal = 'selected';
            $formHtml1 .= <<<EOT
          <option value="$tempTaskStatus" $optVal>$tempTaskStatus</option>
EOT;
        }
        $formHtml1 .= <<<EOT
		  </select></div>
</div>      

 <div class="form-group row ">
       <label class="font-weight-bold  col-lg-3" >Priority:</label>
		<div class="col-lg-7">
  <div class="radio-inline">
EOT;
        if ($priorityLevel == 'High') {
            $op = 'checked';
        }
        $formHtml1 .= <<<EOT
       <label class="font-weight-bold radio radio-success">
                   <input type="radio" name="priorityLevel" value="High" $op>
                    <span></span>
                    High
                </label>
EOT;
        $op = '';
        if ($priorityLevel == 'Medium') {
            $op = 'checked';
        }
        $formHtml1 .= <<<EOT
        <label class="font-weight-bold radio radio-success">
           <input type="radio" name="priorityLevel" value="Medium" $op>
            <span></span>Medium
        </label>
EOT;
        $op = '';
        if ($priorityLevel == 'Low') {
            $op = 'checked';
        }
        $formHtml1 .= <<<EOT
        <label class="font-weight-bold radio radio-success">
           <input type="radio" name="priorityLevel" value="Low" $op>
            <span></span>Low
        </label>
</div>
</div>
</div>

 <div class="form-group row ">
       <label class="font-weight-bold  col-lg-3" >Comments:</label>
	<div class="col-lg-7"><textarea class="form-control tinyMceClass"  name="taskComments" id="taskComments" tabindex="9">$comments</textarea></div>
</div>
 <div class="form-group row ">
       <label class="font-weight-bold  col-lg-3" >Send link to user:</label>
	    <div class="col-lg-9">
                <div class="checkbox-list">
                    <label for="isSendLinkToUser" class="checkbox checkbox-square">
                        <input type="checkbox" name="isSendLinkToUser" id="isSendLinkToUser" value="1" $isSendLinkToUserChk/>
                        <span></span>Send borrower file link
                    </label>
                </div>
			  <span class="form-text text-muted">Checking this box will let the system know that any email notification sent out, because of this task, will contain a URL link to the borrower's file.</span>
	    </div>
</div>

 <div class="form-group row ">
    <label class="font-weight-bold  col-lg-3" >Remind also via: <i class="fa fa-info-circle tooltipClass text-primary" 
    title="SMS feature is available for Borrower,Branch,Employee List,Brokers,and Loan Officers. Make sure the user has a verified cell phone number"></i> </label>
	<div class="col-lg-9">
	   <div class="checkbox-list">
                <label for="sendSMS" class="checkbox checkbox-square">
                    <input type="checkbox" name="sendSMS" id="sendSMS" value="1" $sendSMSChk  />
                    <span></span> SMS
                </label>
       </div>
	</div>
</div>
EOT;
        if (trim($opt) == 'file') {
            $formHtml1 .= <<<EOT
 <div class="form-group row ">
       <label class="font-weight-bold  col-lg-3" >Send Notes History:</label>
	<div class="col-lg-9">
        <div class="checkbox-list">
            <label for="sendNH" class="checkbox">
                <input type="checkbox" name="sendNH" id="sendNH" value="1" $sendNHChk  />
                <span></span>
               Notes History
            </label>
        </div>
	</div>
</div> 
EOT;
        }
        $formHtml1 .= <<<EOT
 <div class="form-group row ">
    <label class="font-weight-bold  col-lg-3" >Send Notification For Task Creation:</label>
	<div class="col-lg-9">
	   <div class="checkbox-list">
                <label for="sendNotificationForTaskCreation" class="checkbox checkbox-square">
                    <input type="checkbox" name="sendNotificationForTaskCreation" id="sendNotificationForTaskCreation" value="1" $sendNotificationForTaskCreationChk  />
                    <span></span> Task Creation Notification
                </label>
       </div>
	</div>
</div>
EOT;


        $progressBarImg = IMG_PROGRESS_BAR;

        $formHtml1 .= <<<EOT
<div class="row d-none">
    <div class="col-xl-5 col-lg-4"></div> 
    <div class="col-xl-6 col-lg-6">
EOT;
        if ($optPipe == 'pipeline' || $optPipe == 'file' || $optPipe == 'menu' || $optPipe == 'list' || $optPipe == 'QAForm') {
            $formHtml1 .= <<<EOT
<!--
		  <input class="btn btn-primary mr-2" type="submit" name="submit" id="myButton" value="Save" alt="Start Search" > <button type="reset" class="btn btn-secondary">Reset</button>-->

EOT;
        } else {
            $formHtml1 .= <<<EOT
	<!--	  <input class="btn btn-primary mr-2" type="reset"  name="submit" value="Save" alt="Start Search"> <button type="reset" class="btn btn-secondary">Reset</button>-->
EOT;
        }
        $formHtml1 .= <<<EOT
            <input class="btn btn-primary mr-2"  type="submit" name="submit" id="submit" value="Save" alt="Start Search">
            <button type="reset" class="btn btn-secondary">Reset</button>  
		  </div>
	</div>

		  <div class="left" id="taskLoaderDiv" style="display:none;"><img src="$progressBarImg" alt=""></div>
</div>
</form>
EOT;
        return $formHtml1;
    }
}
