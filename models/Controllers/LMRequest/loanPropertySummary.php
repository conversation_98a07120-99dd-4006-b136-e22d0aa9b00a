<?php

namespace models\Controllers\LMRequest;

use models\composite\oFile\getFileInfo\fileVelocityInfo;
use models\composite\oFileUpdate\saveFileServices;
use models\composite\oFileUpdate\updateFileLastUpdatedDate;
use models\composite\oPrimaryStatus\getPrimatStatusIdsByNames;
use models\composite\proposalFormula;
use models\constants\gl\glPCID;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Database2;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblFileHUDTransaction;
use models\lendingwise\tblFilePropertyInfo;
use models\lendingwise\tblLMRHUDItemsPayableLoan;
use models\lendingwise\tblLoanPropertySummary;
use models\lendingwise\tblProperties;
use models\lendingwise\tblQAInfo;
use models\lendingwise\tblShortSale;
use models\Request;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;

class loanPropertySummary extends PropertyAnalysis
{

    public static ?int $LMRId = 0;
    public static ?float $totalLoanAmount = null;
    public static ?int $daysUntilClose = null;
    public static ?float $holdbackVal = null;


    public static ?tblProperties $propertyInfo = null;

    public static ?float $propertyMonthlyInsurance = null;
    public static ?float $propertyMonthlyTax = null;
    public static ?float $propertyMonthlyHOAFees = null;
    public static ?float $propertyMonthlyExpenses = null;
    public static ?float $propertyMonthlyPayment = null;
    public static ?float $propertyPITIA = null;
    public static ?float $propertyLTV = null;
    public static ?float $propertyAsIsValue = null;


    public static ?float $totalPropertiesPurchasePrice = 0;
    public static ?float $totalPropertiesEstimatedAsIs = 0;
    public static ?float $totalPropertiesAppraisedAsIs = 0;
    public static ?float $totalPropertiesAllocatedLoanAmount = 0;
    public static ?float $totalPropertiesPayOff = 0;
    public static ?float $totalPropertiesLTP = 0;
    public static ?float $totalPropertiesLTV = 0;
    public static ?float $totalPropertiesAsIsValue = 0;
    public static ?float $totalQualifyingLoanAmount = 0;
    public static ?float $totalAggregateQualifyingLTV = 0;
    // public static ?float $totalPropertiesLoanAmount = 0;


    //public static ?float $totalAsIs = 0;
    public static ?float $totalPropertiesMonthlyPayment = 0;


    public static ?float $totalPropertiesGrossOperatingIncome = 0;
    public static ?float $totalPropertiesMonthlyInsurance = 0;
    public static ?float $totalPropertiesMonthlyTaxes = 0;
    public static ?float $totalPropertiesMonthlyHOAFees = 0;
    public static ?float $totalPropertiesMonthlyTotalExpenses = 0;
    public static ?float $totalPropertiesPITIA = 0;
    public static ?float $totalPropertiesNetOperatingIncome = 0;
    public static ?float $totalPropertiesDSCR = 0;

    public static ?string $financedInterestReserveMonthsField = '';
    public static ?string $financedInterestReserveToolTip = '';
    public static ?int $financedInterestReserveMonths = null;
    public static ?int $financedInterestReserveMonthsMin = null;
    public static ?int $financedInterestReserveMonthsMax = null;
    /**
     * @var array|mixed|string[]
     */
    public static ?array $transactionType;

    public static bool $useOfProceedsShow = false;
    public static int $defaultInterestReserveType;

    public static ?string $totalLoanInterestPaymentTooltip = '';
    public static ?string $totalLoanInterestPaymentTooltipWithValues = '';

    public static function getBridgeLoanToPurchase(): ?float
    {
        return (LMRequest::myFileInfo()->getFileCalculatedValues()->AcquisitionLTV);

    }

    public static function getBridgeLoanToValue(): ?float
    {
        return self::$totalPropertiesAsIsValue ? (LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount / self::$totalPropertiesAsIsValue * 100) : 0;
    }

    public static function getBridgeCombinedLoanToValue(): ?float
    {
        return self::$totalPropertiesAsIsValue ?
            (LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount
                + LMRequest::myFileInfo()->getLoanPropertySummary()->subordinateFinancingAmount)
            / self::$totalPropertiesAsIsValue * 100 : 0;
    }

    public static function updateSumOfAllocatedRehabCost(tblProperties $Property): ?float
    {
        return $Property->getTblPropertiesDetails_by_propertyId()->propertyAllocatedLoanAmount
            + LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostFinanced;
    }

    public static function calculatePropertyAnalysis(?tblProperties $propertyInfo): void
    {
        self::$propertyInfo = $propertyInfo;
        self::$propertyAsIsValue = parent::getPropertyAIsValue($propertyInfo);
        // self::setPropertyQualifyingRent();
        self::$propertyMonthlyInsurance = self::setPropertyMonthlyInsurance($propertyInfo);
        self::$propertyMonthlyTax = self::setPropertyMonthlyTax($propertyInfo);
        self::$propertyMonthlyHOAFees = self::setPropertyMonthlyHOAFee($propertyInfo);
        self::$propertyMonthlyExpenses = self::setPropertyMonthlyExpenses($propertyInfo);
        self::$propertyMonthlyPayment = self::setPropertyMonthlyPayment($propertyInfo);
        self::$propertyPITIA = self::setPropertyPITIA($propertyInfo);
        self::$propertyLTV = parent::getPropertyLTV($propertyInfo);
        // self::setSumOfProperties();
    }

    public static function setPropertyMonthlyInsurance(tblProperties $property): ?float
    {
        return $property->getTblPropertiesAnalysis_by_propertyId()->propertyTotalHazardFloodInsurance ?
            $property->getTblPropertiesAnalysis_by_propertyId()->propertyTotalHazardFloodInsurance / 12 : 0;
    }

    public static function setPropertyMonthlyTax(tblProperties $property): ?float
    {
        return $property->getTblPropertiesDetails_by_propertyId()->annualPropertyTaxes ?
            $property->getTblPropertiesDetails_by_propertyId()->annualPropertyTaxes / 12 : 0;
    }

    public static function setPropertyMonthlyHOAFee(tblProperties $property): ?float
    {
        return $property->getTblPropertiesAnalysis_by_propertyId()->propertyAnnualHOAFees ?
            $property->getTblPropertiesAnalysis_by_propertyId()->propertyAnnualHOAFees / 12 : 0;
    }

    public static function setPropertyMonthlyExpenses(tblProperties $property): ?float
    {
        return (self::$propertyMonthlyInsurance ?? self::setPropertyMonthlyInsurance($property))
            + (self::$propertyMonthlyTax ?? self::setPropertyMonthlyTax($property))
            + (self::$propertyMonthlyHOAFees ?? self::setPropertyMonthlyHOAFee($property));
    }

    public static function setPropertyMonthlyPayment(tblProperties $property): ?float
    {
        /*        $amount = self::$propertyInfo->getTblPropertiesDetails_by_propertyId()->propertyAllocatedLoanAmount;
                $rate =  Property::$propertyInterestRate / 1200;
                $term = 360;
                return $amount * $rate * (pow(1 + $rate, $term) / (pow(1 + $rate, $term) - 1));*/
        return proposalFormula::calculateMonthlyPayment(
            $property->getTblPropertiesDetails_by_propertyId()->propertyAllocatedLoanAmount,
            LMRequest::File()->lien1Rate,
            360
        ) ?? null;
    }

    public static function setPropertyPITIA(tblProperties $property): ?float
    {
        return (self::$propertyMonthlyPayment ?? self::setPropertyMonthlyPayment($property))
            + (self::$propertyMonthlyExpenses ?? self::setPropertyMonthlyExpenses($property));
    }

    public static function updateLoanInfoV2Values(?int $LMRId): void
    {
        if (!$LMRId) {
            return;
        }
        if (!LMRequest::$LMRId) {
            LMRequest::setLMRId($LMRId);
        }

        parent::init($LMRId);
        self::getPropertiesAggregateValues(parent::$propertiesInfo);

        $tblLoanPropertySummary = tblLoanPropertySummary::Get(['LMRId' => $LMRId]) ?? new tblLoanPropertySummary();
        $tblLoanPropertySummary->LMRId = $LMRId;

        $tblLoanPropertySummary->constructionType = (Request::isset('constructionType') && Request::GET('constructionType') ? Database2::strongTypeValue(Request::GetClean('constructionType'), Database2::DATATYPE_NUMBER) : $tblLoanPropertySummary->constructionType);
        $tblLoanPropertySummary->constructionHardCost = (Request::isset('constructionHardCost') ? Database2::strongTypeValue(Request::GetClean('constructionHardCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->constructionHardCost);
        $tblLoanPropertySummary->constructionSoftCost = (Request::isset('constructionSoftCost') ? Database2::strongTypeValue(Request::GetClean('constructionSoftCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->constructionSoftCost);
        $tblLoanPropertySummary->contingencyTypeOption = (Request::isset('contingencyTypeOption') ? Database2::strongTypeValue(Request::GetClean('contingencyTypeOption'), Database2::DATATYPE_STRING) : $tblLoanPropertySummary->contingencyTypeOption);
        $tblLoanPropertySummary->contingencyPercentage = (Request::isset('contingencyPercentage') ? Database2::strongTypeValue(Request::GetClean('contingencyPercentage'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->contingencyPercentage);
        $tblLoanPropertySummary->contingencyAmount = (Request::isset('contingencyAmount') ? Database2::strongTypeValue(Request::GetClean('contingencyAmount'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->contingencyAmount);

        $tblLoanPropertySummary->totalPropertiesPurchasePrice = isset(self::$totalPropertiesPurchasePrice) ? Database2::strongTypeValue(self::$totalPropertiesPurchasePrice, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesPurchasePrice;

        $tblLoanPropertySummary->totalPropertiesEstimatedAsIs = isset(self::$totalPropertiesEstimatedAsIs) ? Database2::strongTypeValue(self::$totalPropertiesEstimatedAsIs, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesEstimatedAsIs;

        $tblLoanPropertySummary->totalPropertiesAppraisedAsIs = isset(self::$totalPropertiesAppraisedAsIs) ? Database2::strongTypeValue(self::$totalPropertiesAppraisedAsIs, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesAppraisedAsIs;

        $tblLoanPropertySummary->totalPropertiesAllocatedLoanAmount = isset(self::$totalPropertiesAllocatedLoanAmount) ? Database2::strongTypeValue(self::$totalPropertiesAllocatedLoanAmount, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesAllocatedLoanAmount;

        $tblLoanPropertySummary->totalPropertiesPayOff = isset(self::$totalPropertiesPayOff) ? Database2::strongTypeValue(self::$totalPropertiesPayOff, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesPayOff;

        $tblLoanPropertySummary->totalPropertiesLTP = isset(self::$totalPropertiesLTP) ? Database2::strongTypeValue(self::$totalPropertiesLTP, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesLTP;
        $tblLoanPropertySummary->totalPropertiesLTV = isset(self::$totalPropertiesLTV) ? Database2::strongTypeValue(self::$totalPropertiesLTV, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesLTV;

        $tblLoanPropertySummary->totalPropertiesAsIsValue = isset(self::$totalPropertiesAsIsValue) ? Database2::strongTypeValue(self::$totalPropertiesAsIsValue, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesAsIsValue;

        $tblLoanPropertySummary->totalPropertiesLoanAmount = self::getTotalPropertiesLoanAmount($tblLoanPropertySummary);

        //  $tblLoanPropertySummary->interestOnlyMonthlyPayment = self::getInterestOnlyMonthlyPayment(
        //    $tblLoanPropertySummary->totalPropertiesLoanAmount, LMRequest::File()->lien1Rate );
        //$tblLoanPropertySummary->interestPaymentHoldBack = $tblLoanPropertySummary->interestOnlyMonthlyPayment * $tblLoanPropertySummary->interestPaymentHoldBackMonths;

        $tblLoanPropertySummary->bridgeLoanToValue = self::getPropertiesBridgeLTV(
            $tblLoanPropertySummary->totalPropertiesLoanAmount,
            self::$totalQualifyingLoanAmount
        );

        $tblLoanPropertySummary->totalQualifyingLoanAmount = self::$totalQualifyingLoanAmount;
        $tblLoanPropertySummary->totalAggregateQualifyingLTV = self::$totalAggregateQualifyingLTV;

        $tblLoanPropertySummary->bridgeCombinedLoanToValue = self::getPropertiesCombinedBridgeLTV(
            $tblLoanPropertySummary->totalPropertiesLoanAmount,
            $tblLoanPropertySummary->totalPropertiesAsIsValue,
            $tblLoanPropertySummary->subordinateFinancingAmount,
        );

        $tblLoanPropertySummary->monthly30YrPIPayment = Database2::strongTypeValue(proposalFormula::calculateMonthlyPayment(
            $tblLoanPropertySummary->totalPropertiesLoanAmount,
            LMRequest::File()->lien1Rate,
            360
        ), Database2::DATATYPE_MONEY);

        $tblLoanPropertySummary->totalPropertiesITIA = self::getTotalPropertiesITIA(
            $tblLoanPropertySummary->totalPropertiesLoanAmount,
            self::$totalPropertiesMonthlyTotalExpenses
        );
        $tblLoanPropertySummary->totalPropertiesGrossOperatingIncome = isset(self::$totalPropertiesGrossOperatingIncome) ? Database2::strongTypeValue(self::$totalPropertiesGrossOperatingIncome, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesGrossOperatingIncome;
        $tblLoanPropertySummary->totalPropertiesMonthlyInsurance = isset(self::$totalPropertiesMonthlyInsurance) ? Database2::strongTypeValue(self::$totalPropertiesMonthlyInsurance, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesMonthlyInsurance;
        $tblLoanPropertySummary->totalPropertiesMonthlyTaxes = isset(self::$totalPropertiesMonthlyTaxes) ? Database2::strongTypeValue(self::$totalPropertiesMonthlyTaxes, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesMonthlyTaxes;
        $tblLoanPropertySummary->totalPropertiesMonthlyHOAFees = isset(self::$totalPropertiesMonthlyHOAFees) ? Database2::strongTypeValue(self::$totalPropertiesMonthlyHOAFees, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesMonthlyHOAFees;

        $tblLoanPropertySummary->totalPropertiesMonthlyTotalExpenses = isset(self::$totalPropertiesMonthlyTotalExpenses) ? Database2::strongTypeValue(self::$totalPropertiesMonthlyTotalExpenses, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesMonthlyTotalExpenses;

        $tblLoanPropertySummary->totalPropertiesPITIA = isset(self::$totalPropertiesPITIA) ? Database2::strongTypeValue(self::$totalPropertiesPITIA, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesPITIA;

        $tblLoanPropertySummary->totalPropertiesNetOperatingIncome = isset(self::$totalPropertiesNetOperatingIncome) ? Database2::strongTypeValue(self::$totalPropertiesNetOperatingIncome, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesNetOperatingIncome;

        $tblLoanPropertySummary->totalPropertiesDSCR = isset(self::$totalPropertiesDSCR) ? Database2::strongTypeValue(self::$totalPropertiesDSCR, Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesDSCR;

        if (LMRequest::myFileInfo()->getLMRClientType() == 'Bri996') {
            $tblLoanPropertySummary->financedInterestReserve = self::getFinancedInterestReserve(parent::$primaryPropertyInfo, $tblLoanPropertySummary);
        } else if (LMRequest::myFileInfo()->getLMRClientType() == 'BRLGUC') {
            $result = self::calculateGUC($tblLoanPropertySummary, parent::$primaryPropertyInfo);
            if (isset($result['GUCTotalLoanAmount'])) {
                $tblLoanPropertySummary->GUCTotalLoanAmount = $result['GUCTotalLoanAmount'];
            }
            if (isset($result['financedInterestReserve'])) {
                $tblLoanPropertySummary->financedInterestReserve = $result['financedInterestReserve'];
            }
        }

        $tblLoanPropertySummary->monthlyPaymentReserve = self::calculateMonthlyPaymentReserve(parent::$propertiesInfo,
            $tblLoanPropertySummary);

        $verifiedAssets = LMRequest::myFileInfo()->assetInfo()->totalVerifiedAssets;
        $tblLoanPropertySummary->totalVerifiedAssets = self::getTotalVerifiedAssets(
            LMRequest::$LMRId,
            $verifiedAssets,
            $tblLoanPropertySummary
        );

        $tblLoanPropertySummary->fundsToVerify = self::getFundsToVerify(LMRequest::$LMRId, $tblLoanPropertySummary->monthlyPaymentReserve);

        $tblLoanPropertySummary->totalExcessReserves = self::getTotalExcessReserves($tblLoanPropertySummary->totalVerifiedAssets,
            $tblLoanPropertySummary->fundsToVerify);


        $tblLoanPropertySummary->constructionTotalProjectCost = self::getGroundUpConstructionTotalProjectCost($tblLoanPropertySummary, parent::$primaryPropertyInfo);
        $tblLoanPropertySummary->grossProfitMargin = self::getGrossProfitMargin($tblLoanPropertySummary, parent::$primaryPropertyInfo);

        $tblLoanPropertySummary->financedAtClosing = self::getFinancedAtClosing($tblLoanPropertySummary);
        $tblLoanPropertySummary->totalLoanInterestPayment = self::getTotalLoanInterestPayment($tblLoanPropertySummary);
        $tblLoanPropertySummary->monthlyPostClosingPaymentReserves = intval(self::getMonthlyPostClosingPaymentReserves($tblLoanPropertySummary->totalExcessReserves,
            $tblLoanPropertySummary->totalLoanInterestPayment));
        $tblLoanPropertySummary->initialLoanToCost = self::getInitialLoanToCost($tblLoanPropertySummary, parent::$primaryPropertyInfo);
        $tblLoanPropertySummary->GUCLoanToCost = self::getGUCLoanToCost($tblLoanPropertySummary, parent::$primaryPropertyInfo);
        $tblLoanPropertySummary->GUCARV = self::GUCARV($tblLoanPropertySummary, parent::$primaryPropertyInfo);
        $tblLoanPropertySummary->GUCLTP = self::GUCLTP($tblLoanPropertySummary, parent::$primaryPropertyInfo);


        if ($tblLoanPropertySummary->id) {
            $tblLoanPropertySummary->updatedDate = Dates::Timestamp();
            $tblLoanPropertySummary->updatedGroup = Strings::GetSess('userGroup') ?? null;
            $tblLoanPropertySummary->updatedBy = Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null;
        } else {
            $tblLoanPropertySummary->createdDate = Dates::Timestamp();
            $tblLoanPropertySummary->createdGroup = Strings::GetSess('userGroup') ?? null;
            $tblLoanPropertySummary->createdBy = Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null;
        }
        $tblLoanPropertySummary->Save();

        self::updateBrideRenovationValues($LMRId, parent::$primaryPropertyInfo);
        if (LMRequest::File()->FPCID == glPCID::PCID_PROD_CV3) {
            HUDCalculation::process(LMRequest::$LMRId);
            self::updatePoints($tblLoanPropertySummary, $LMRId);
        }

        if (LMRequest::myFileInfo()->getLMRClientType() == 'BRLGUC') {
            parent::init(LMRequest::$LMRId);
            PropertyAnalysis::updatePrimaryPropertyInterestOnlyPayment($tblLoanPropertySummary, parent::$primaryPropertyInfo);
        }
        updateFileLastUpdatedDate::getReport([
            'fileID' => self::$LMRId,
        ]);
    }

    public static function init(?int $LMRId = null): void
    {
        if (!LMRequest::$LMRId) {
            LMRequest::setLMRId($LMRId);
        }
        if(Dates::IsEmpty(LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate ? LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate : LMRequest::myFileInfo()->tblFile()->getTblQAInfo_by_LMRId()[0]->desiredClosingDate)){
          $futureDate = '';
        }
        else{
            $futureDate = LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate ? LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate : LMRequest::myFileInfo()->tblFile()->getTblQAInfo_by_LMRId()[0]->desiredClosingDate;
        }
        self::$daysUntilClose = Integers::calculateNoOfDaysBehind([
            'futureDate'      => $futureDate,
            'lastPaymentMade' => Dates::Datestamp(),
        ]);
        $HUDInfo = tblLMRHUDItemsPayableLoan::get(['LMRId' => $LMRId, 'fieldID' => 805]);
        self::$holdbackVal = $HUDInfo->borrowerSettlementValue ?? null;


        self::$defaultInterestReserveType = 3;

        self::$totalLoanInterestPaymentTooltip = 'Total Loan InterestPayment = (Total Allocated Loan Amount) * (interest Rate / 1200);';
        self::$totalLoanInterestPaymentTooltipWithValues = "Total Loan InterestPayment = ".LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesAllocatedLoanAmount ." * (".LMRequest::File()->lien1Rate ."/ 1200) = ".Currency::formatDollarAmountWithDecimalZeros(LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment ?? 0) ;

        if (LMRequest::$loanProgram == 'BRLGUC') {
            //self::$financedInterestReserveMonthsField = ' readonly ';
            //self::$financedInterestReserveMonths = $loanTermMonths ? $loanTermMonths - 1 : null;
            self::$financedInterestReserveToolTip = '(Total Loan Amount - Construction Hard Cost - Construction Soft Cost - Contingency $ - Interest Reserve + (0.65 * (Construction Hard Cost + Construction Soft Cost + Contingency $)) * (Interest Rate ÷ 12) * (M)';
            self::$defaultInterestReserveType = 1;

            self::$totalLoanInterestPaymentTooltip = 'Total Loan InterestPayment = Financed At Closing * (interest Rate / 1200);';
            self::$totalLoanInterestPaymentTooltipWithValues = "Total Loan InterestPayment = ".LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment." * (".LMRequest::File()->lien1Rate."/ 1200) = ".Currency::formatDollarAmountWithDecimalZeros(LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment);

        } else if (LMRequest::$loanProgram == 'Bri996') {
            //self::$financedInterestReserveMonthsMin = 3;
            //self::$financedInterestReserveMonthsMax = $loanTermMonths ? $loanTermMonths - 1 : null;
            self::$financedInterestReserveToolTip = '(Base Loan Amount + (.5 * (Financed Construction/Renovation Holdback)) * (Interest Rate ÷ 12) * (M)';
            self::$defaultInterestReserveType = 2;
        }
        self::$transactionType = array_combine(gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting, gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting);

        if (LMRequest::File()->FPCID == glPCID::PCID_PROD_CV3) {

            $cv3TransactionType = [];
            foreach (self::$transactionType as $key => $displayVal) {
                if (!in_array($key, gltypeOfHMLOLoanRequesting::$cv3ExcludeTypeOfHMLOLoanRequesting)) {
                    $cv3TransactionType[$key] = $displayVal;
                }
            }
            self::$transactionType = $cv3TransactionType;
            self::$useOfProceedsShow = in_array(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting, ['Cash-Out / Refinance', 'Delayed Purchase']);

        }

    }

    /**
     * @param int|null $PCID
     * @param string|null $loanProgram
     * @return bool
     */
    public static function showHidePropertyRehabCv3(?int $PCID, ?string $loanProgram = ''): bool
    {
        if (glPCID::PCID_PROD_CV3 == $PCID && $loanProgram == 'BRLGUC') {
            return true;
        }
        return false;
    }

    public static function getMonthsFromLoanTerm(?string $loanTerm): ?string
    {
        $loanTermArray = $loanTerm ? explode(' ', $loanTerm) : [];
        if (sizeof($loanTermArray) > 1) {
            return trim($loanTermArray[0]);
        }
        return null;
    }

    public static function getPropertiesAggregateValues(array $properties): void
    {
        self::clearPropertiesSummary();
        foreach ($properties as $eachProperty) {
            self::$totalPropertiesPurchasePrice += floatval($eachProperty->getTblPropertiesDetails_by_propertyId()->propertyPurchasePrice);
            self::$totalPropertiesEstimatedAsIs += floatval($eachProperty->getTblPropertiesDetails_by_propertyId()->propertyEstimatedValue);
            self::$totalPropertiesAppraisedAsIs += floatval($eachProperty->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalAsIsValue);
            self::$totalPropertiesAllocatedLoanAmount += floatval($eachProperty->getTblPropertiesDetails_by_propertyId()->propertyAllocatedLoanAmount);
            self::$totalPropertiesPayOff += floatval($eachProperty->getTblPropertiesAnalysis_by_propertyId()->propertyTotalExistingLienPayoffs);

            self::$totalPropertiesAsIsValue += parent::getPropertyAIsValue($eachProperty);

            self::$totalQualifyingLoanAmount += parent::calculateQualifyingLoanAmount($eachProperty);

            //self::$totalPropertiesMonthlyInsurance += self::setPropertyMonthlyInsurance($eachProperty);
            //self::$totalPropertiesMonthlyTaxes += self::setPropertyMonthlyTax($eachProperty);
            //self::$totalPropertiesMonthlyHOAFees += self::setPropertyMonthlyHOAFee($eachProperty);
            self::$totalPropertiesMonthlyTotalExpenses += self::setPropertyMonthlyExpenses($eachProperty);
            self::$totalPropertiesMonthlyPayment += self::setPropertyMonthlyPayment($eachProperty);

            self::$totalPropertiesGrossOperatingIncome += floatval($eachProperty->getTblPropertiesAnalysis_by_propertyId()->propertyGrossOperatingIncome);
            self::$totalPropertiesMonthlyInsurance += self::setPropertyMonthlyInsurance($eachProperty);
            self::$totalPropertiesMonthlyTaxes += self::setPropertyMonthlyTax($eachProperty);
            self::$totalPropertiesMonthlyHOAFees += self::setPropertyMonthlyHOAFee($eachProperty);
            // self::$totalPropertiesTotalExpenses += self::setPropertyMonthlyExpenses($eachProperty);
            self::$totalPropertiesPITIA += floatval($eachProperty->getTblPropertiesAnalysis_by_propertyId()->propertyPITIA);
            self::$totalPropertiesNetOperatingIncome += floatval($eachProperty->getTblPropertiesAnalysis_by_propertyId()->propertyNetOperatingIncome);

            //self::$totalPropertiesLTP += floatval($eachProperty->getTblPropertiesDetails_by_propertyId()->propertyLoanToPurchasePrice);
        }

        self::$totalPropertiesLTP = self::$totalPropertiesPurchasePrice ? self::$totalPropertiesAllocatedLoanAmount / self::$totalPropertiesPurchasePrice * 100 : 0;
        self::$totalPropertiesLTV = self::$totalPropertiesAsIsValue ? self::$totalPropertiesAllocatedLoanAmount / self::$totalPropertiesAsIsValue * 100 : 0;
        self::$totalPropertiesDSCR = self::$totalPropertiesPITIA ? round(self::$totalPropertiesGrossOperatingIncome / self::$totalPropertiesPITIA, 3) : 0;
        self::$totalAggregateQualifyingLTV = self::$totalQualifyingLoanAmount ? self::$totalPropertiesAllocatedLoanAmount / self::$totalQualifyingLoanAmount * 100 : 0;
    }

    public static function clearPropertiesSummary(): void
    {
        self::$totalPropertiesPurchasePrice = 0;
        self::$totalPropertiesEstimatedAsIs = 0;
        self::$totalPropertiesAppraisedAsIs = 0;
        self::$totalPropertiesAllocatedLoanAmount = 0;
        self::$totalPropertiesPayOff = 0;
        self::$totalPropertiesAsIsValue = 0;
        self::$totalQualifyingLoanAmount = 0;
        self::$totalPropertiesMonthlyTotalExpenses = 0;
        self::$totalPropertiesMonthlyPayment = 0;
        self::$totalPropertiesGrossOperatingIncome = 0;
        self::$totalPropertiesMonthlyInsurance = 0;
        self::$totalPropertiesMonthlyTaxes = 0;
        self::$totalPropertiesMonthlyHOAFees = 0;
        self::$totalPropertiesPITIA = 0;
        self::$totalPropertiesNetOperatingIncome = 0;
        self::$totalPropertiesLTP = 0;
        self::$totalPropertiesLTV = 0;
        self::$totalPropertiesDSCR = 0;
        self::$totalAggregateQualifyingLTV = 0;
    }

    public static function getTotalLoanInterestPayment(tblLoanPropertySummary $tblLoanPropertySummary): ?float
    {
        $monthlyInterestRate = Strings::replaceCommaValues(LMRequest::File()->lien1Rate) / 1200;
        $financedAmount = (LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType == 'BRLGUC') ? $tblLoanPropertySummary->financedAtClosing : ($tblLoanPropertySummary->totalPropertiesAllocatedLoanAmount);
        return ($monthlyInterestRate > 0) ? ($financedAmount * $monthlyInterestRate) : null;
    }

    public static function getTotalPropertiesLoanAmount(tblLoanPropertySummary $tblLoanPropertySummary): ?float
    {

        if (LMRequest::myFileInfo()->getLMRClientType() == 'BRLGUC') {
            $result = self::calculateGUC($tblLoanPropertySummary, parent::$primaryPropertyInfo);
            return $result['GUCTotalLoanAmount'];
        }

        $totalPropertiesLoanAmount = 0;
        //$HUDInfo = tblLMRHUDItemsPayableLoan::get(['LMRId' => $LMRId,'fieldID' => 805]);
        //   $totalPropertiesLoanAmount += $HUDInfo->borrowerSettlementValue ?? 0;
        if (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->propertyNeedRehab == 'Yes' && !in_array(LMRequest::myFileInfo()->getLMRClientType(), [
                'BRL',
                'Bri879861',
                'Ren496',
                'Ren949',
            ])
        ) {
            $totalPropertiesLoanAmount += LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostFinanced;
        }
        // $totalPropertiesLoanAmount += LMRequest::myFileInfo()->getLoanPropertySummary()->interestPaymentHoldBack ?? 0;
        $totalPropertiesLoanAmount += ($tblLoanPropertySummary->totalPropertiesAllocatedLoanAmount ?? 0);
        return $totalPropertiesLoanAmount;
    }

    /**
     * @param tblLoanPropertySummary $tblLoanPropertySummary
     * @param tblProperties $Property
     * @return array
     */
    public static function calculateGUC(tblLoanPropertySummary $tblLoanPropertySummary,
                                        tblProperties          $Property): array
    {

        $targetLTC = $tblLoanPropertySummary->targetLTC;
        $targetLTC /= 100;
        $contingencyPercentage = $tblLoanPropertySummary->contingencyPercentage;
        $costRemaining = self::sumOfConstructionCost($tblLoanPropertySummary);
        $C = ($contingencyPercentage / 100) * $costRemaining;
        $R = LMRequest::File()->lien1Rate;
        $R /= 100;
        $Terms = $tblLoanPropertySummary->financedInterestReserveMonths;//self::getMonthsFromLoanTerm(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm);
        $costSpent = LMRequest::myFileInfo()->listingRealtorInfo()->costSpent;
        $purchasePrice = PropertyAnalysis::calculateQualifyingLoanAmount($Property);
        $initialIlaGuess = 0;

        $result = self::findIlaIr($targetLTC, $costRemaining, $C, $R, $Terms, $costSpent, $purchasePrice, $initialIlaGuess);

        $ila = floor($result['ila']);
        $ir = ($result['ir']);

        $GUCTotalLoanAmount = $ila + $ir + $costRemaining + $C;

        return [
            'GUCTotalLoanAmount'      => $GUCTotalLoanAmount,
            'financedInterestReserve' => $ir,
        ];
    }

    /**
     * @param tblLoanPropertySummary $tblLoanPropertySummary
     * @return float|null
     */
    public static function sumOfConstructionCost(tblLoanPropertySummary $tblLoanPropertySummary): ?float
    {
        return $tblLoanPropertySummary->constructionSoftCost + $tblLoanPropertySummary->constructionHardCost;
    }

    /**
     * @param $targetLtc
     * @param $costRemaining
     * @param $C
     * @param $R
     * @param $Terms
     * @param $costSpent
     * @param $purchasePrice
     * @param $initialIlaGuess
     * @param int $maxIterations
     * @param float $tolerance
     * @return array
     */
    public static function findIlaIr($targetLtc,
        $costRemaining,
        $C,
        $R,
        $Terms,
        $costSpent,
        $purchasePrice,
        $initialIlaGuess,
                                     int $maxIterations = 10000,
                                     float $tolerance = 1e-12): array
    {

        $ILA = $initialIlaGuess;
        $IR = 0;

        for ($i = 0; $i < $maxIterations; $i++) {
            $IR = floor((0.65 * ($costRemaining + $C) + $ILA) * ($R / 12) * ($Terms));

            $LTC = 0;
            if (($costSpent + $costRemaining + $C + $purchasePrice + $IR) > 0) {
                $LTC = ($ILA + $costRemaining + $C + $IR) / ($costSpent + $costRemaining + $C + $purchasePrice + $IR);
            }

            $error = $LTC - $targetLtc;

            if (abs($error) < $tolerance) {
                return ['ila' => $ILA, 'ir' => $IR];
            }

            $ILA -= ($error * 10000);
        }

        return ['ila' => $ILA, 'ir' => $IR];
    }

    public static function getPropertiesBridgeLTV(?float $totalPropertiesLoanAmount = 0,
                                                  ?float $totalQualifyingLoanAmount = 0
    ): float
    {
        return $totalQualifyingLoanAmount ? ($totalPropertiesLoanAmount / $totalQualifyingLoanAmount * 100) : 0;
    }

    public static function getPropertiesCombinedBridgeLTV(?float $totalPropertiesLoanAmount = 0,
                                                          ?float $totalPropertiesAsIsValue = 0,
                                                          ?float $subordinateFinancingAmount = 0
    ): float
    {
        return $totalPropertiesAsIsValue ? (($totalPropertiesLoanAmount + $subordinateFinancingAmount) / $totalPropertiesAsIsValue * 100) : 0;
    }

    public static function getTotalPropertiesITIA(
        ?float $totalPropertiesLoanAmount = 0,
        ?float $totalPropertiesExpenses = 0
    ): ?float
    {
        return ($totalPropertiesLoanAmount * Strings::replaceCommaValues(LMRequest::File()->lien1Rate) / 1200) + $totalPropertiesExpenses;
    }

    public static function getFinancedInterestReserve(tblProperties $Property, tblLoanPropertySummary $tblLoanPropertySummary): float
    {
        return ($Property->getTblPropertiesDetails_by_propertyId()->propertyAllocatedLoanAmount
                + (0.5 * LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostFinanced)) * (Strings::replaceCommaValues(LMRequest::File()->lien1Rate) / 1200) * $tblLoanPropertySummary->financedInterestReserveMonths;
    }

    public static function calculateMonthlyPaymentReserve(array $properties, tblLoanPropertySummary $tblLoanPropertySummary): ?float
    {
        $monthlyPaymentReserveVal = 0;
        $propertiesPresentOccupancy = [];
        foreach ($properties as $eachProperty) {
            $propertiesPresentOccupancy[] = $eachProperty->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy;
        }
        $isPresentOccupancyIsVacant = in_array('Vacant', $propertiesPresentOccupancy);
        $isPresentOccupancyIsSellerOccupied = in_array('Seller Occupied', $propertiesPresentOccupancy);
        $isPresentOccupancyIsTenantOccupied = self::checkPresentOccupancyIsTenantOccupied($propertiesPresentOccupancy);
        $monthly30YrPIPayment = $tblLoanPropertySummary->monthly30YrPIPayment;
        $financedInterestReserveMonths = $tblLoanPropertySummary->financedInterestReserveMonths;

        if (in_array(LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType, ['Ren496', 'Ren949'])) {
            //Ren496 = Rental Loan
            //Ren949 = Rental Loan - Portfolio
            $loanTerm = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm;
            $totalPITIA = $tblLoanPropertySummary->totalPropertiesPITIA;
            $totalITIA = $tblLoanPropertySummary->totalPropertiesITIA;
            $validLoanTerms = [
                '360 Months- 30 Year Fixed - Fully Amortizing',
                '360 Months- 5/1 ARM - Interest Only',
                '360 Months- 5/6 ARM- 10 Year Interest Only'
            ];
            //  if (in_array($loanTerm, $validLoanTerms)) {
            if ($loanTerm == '360 Months- 30 Year Fixed - Fully Amortizing') {
                if(LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType == 'Ren496'
                    && $isPresentOccupancyIsTenantOccupied
                    && $tblLoanPropertySummary->totalPropertiesDSCR <= 1 ) {
                    $monthlyPaymentReserveVal = $totalPITIA * 6;
                } elseif(LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType == 'Ren496'
                    && ($isPresentOccupancyIsVacant || $isPresentOccupancyIsSellerOccupied)
                    && $tblLoanPropertySummary->totalPropertiesDSCR <= 1 ) {
                    $monthlyPaymentReserveVal = $totalPITIA * 8;
                } elseif (($isPresentOccupancyIsVacant || $isPresentOccupancyIsSellerOccupied)
                    && (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                        || (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore > 679)
                    )) {
                    $monthlyPaymentReserveVal = $totalPITIA * 6;
                } elseif ($isPresentOccupancyIsTenantOccupied
                    && (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                        || (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore > 679))
                ) {
                    $monthlyPaymentReserveVal = $totalPITIA * 4;
                } elseif (($isPresentOccupancyIsVacant || $isPresentOccupancyIsSellerOccupied)
                    && LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                    && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore < 680) {

                    $monthlyPaymentReserveVal = $totalPITIA * 10;
                } elseif ($isPresentOccupancyIsTenantOccupied
                    && LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                    && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore < 680) {
                    $monthlyPaymentReserveVal = $totalPITIA * 8;
                }
            } elseif ($loanTerm == '360 Months- 5/1 ARM - Interest Only') {
                if ($isPresentOccupancyIsTenantOccupied) {
                    $monthlyPaymentReserveVal = $totalPITIA * 4;
                } elseif ($isPresentOccupancyIsVacant || $isPresentOccupancyIsSellerOccupied) {
                    $monthlyPaymentReserveVal = $totalPITIA * 6;
                }
            } elseif ($loanTerm == '360 Months- 5/6 ARM- 10 Year Interest Only') {
                if ($isPresentOccupancyIsTenantOccupied
                    && (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                        || (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore > 679))
                ) {
                    $monthlyPaymentReserveVal = $totalITIA * 4;
                } elseif (($isPresentOccupancyIsVacant || $isPresentOccupancyIsSellerOccupied)
                    && (
                        LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                        || (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore > 679)
                    )) {
                    $monthlyPaymentReserveVal = $totalITIA * 6;
                } elseif ($isPresentOccupancyIsTenantOccupied
                    && LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                    && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore < 680) {
                    $monthlyPaymentReserveVal = $totalITIA * 8;
                } elseif (($isPresentOccupancyIsVacant || $isPresentOccupancyIsSellerOccupied)
                    && LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                    && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore < 680) {
                    $monthlyPaymentReserveVal = $totalITIA * 10;
                }
            }
        } else if (in_array(LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType, ['BRL', 'Bri879861'])) {
            //BRL =  Bridge Loan
            //Bri879861 =  Bridge Loan Portfolio
            if (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                || (LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore > 679 && LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE)) {
                $monthlyPaymentReserveVal = $tblLoanPropertySummary->totalPropertiesITIA * 4;
            } else if (LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore < 680 && LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE) {
                $monthlyPaymentReserveVal = $tblLoanPropertySummary->totalPropertiesITIA * 8;
            }
            //  $monthlyPaymentReserveVal = $tblLoanPropertySummary->totalLoanInterestPayment * 4;

        } else if (LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType == 'Bri996') {
            //Bridge Loan - Renovation

            $tblLMRHUDItemsPayableLoan_808 = tblLMRHUDItemsPayableLoan::Get([
                'LMRID'   => LMRequest::myFileInfo()->LMRId,
                'fieldID' => '808'
            ]);

            $rehabCost = LMRequest::myFileInfo()->fileHMLOInfo()->rehabCost;
            $rehabCostFinanced = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostFinanced;

            if ($tblLoanPropertySummary->financedInterestReserve || $tblLMRHUDItemsPayableLoan_808->borrowerSettlementValue) {
                $monthlyPaymentReserveVal = max(
                    0.25 * $rehabCost,
                    $rehabCost - $rehabCostFinanced ,
                    4 * $tblLoanPropertySummary->totalPropertiesITIA
                );
            } else if (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE) {
                $monthlyPaymentReserveVal = max(
                    4 * $tblLoanPropertySummary->totalPropertiesITIA,
                    0.25 * $rehabCost,
                    $rehabCost - $rehabCostFinanced //********
                );

            } else if (LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore > 679
                && LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE) {
                $monthlyPaymentReserveVal = max(
                    4 * $tblLoanPropertySummary->totalPropertiesITIA,
                    0.25 * $rehabCost,
                    $rehabCost - $rehabCostFinanced //********
                );
            } else if (LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore <= 679
                && LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE) {
                $monthlyPaymentReserveVal = max(
                    8 * $tblLoanPropertySummary->totalPropertiesITIA,
                    0.25 * $rehabCost,
                    $rehabCost - $rehabCostFinanced //********
                );
            }

            /*            if ($tblLoanPropertySummary->financedInterestReserve == 0) {
                            $monthlyPaymentReserveVal = max(
                                4 * $tblLoanPropertySummary->totalPropertiesITIA,
                                0.25 * $rehabCostFinanced,
                                $rehabCost - $rehabCostFinanced //********
                            );
                        } else {
                            $monthlyPaymentReserveVal = 0.25 * $rehabCostFinanced;
                        }*/

        } else if (LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType == 'BRLGUC') {
            //Ground Up Construction

            $totalConstructionCost = $tblLoanPropertySummary->constructionHardCost + $tblLoanPropertySummary->constructionSoftCost + $tblLoanPropertySummary->contingencyAmount;

            $param = $tblLoanPropertySummary->totalLoanInterestPayment + $tblLoanPropertySummary->totalPropertiesMonthlyTotalExpenses;

            if ($totalConstructionCost > 1000000 && $financedInterestReserveMonths >= 6) {
                $monthlyPaymentReserveVal = 0.15 * $totalConstructionCost;
            } else if ($totalConstructionCost > 1000000 && $financedInterestReserveMonths < 6) {
                $monthlyPaymentReserveVal = 0.15 * $totalConstructionCost + ((6 - $financedInterestReserveMonths) * $param);
            } else if ($totalConstructionCost > 500000 && $totalConstructionCost <= 1000000 && $financedInterestReserveMonths >= 6) {
                $monthlyPaymentReserveVal = 0.20 * $totalConstructionCost;
            } else if ($totalConstructionCost > 500000 && $totalConstructionCost <= 1000000  && $financedInterestReserveMonths < 6) {
                $monthlyPaymentReserveVal = 0.20 * $totalConstructionCost + ((6 - $financedInterestReserveMonths) * $param);
            } else if ($totalConstructionCost <= 500000 && $financedInterestReserveMonths >= 6) {
                $monthlyPaymentReserveVal = 0.25 * $totalConstructionCost;
            } else if ($totalConstructionCost <= 500000 && $financedInterestReserveMonths < 6) {
                $monthlyPaymentReserveVal = 0.25 * $totalConstructionCost + ((6 - $financedInterestReserveMonths) * $param);
            }
            //   $monthlyPaymentReserveVal = 0.25 * ($tblLoanPropertySummary->constructionHardCost + $tblLoanPropertySummary->constructionSoftCost + $tblLoanPropertySummary->contingencyAmount);
        }

        return $monthlyPaymentReserveVal;

    }

    public static function checkPresentOccupancyIsTenantOccupied($propertiesPresentOccupancy = [], $substring = 'Tenant Occupied'): bool
    {
        $returnVal = true;
        foreach ($propertiesPresentOccupancy as $eachOccupancy) {
            if (strpos($eachOccupancy, $substring) === false && $returnVal) {
                $returnVal = false;
            }
        }
        return $returnVal;
    }

    public static function getTotalVerifiedAssets($LMRId, $verifiedAssets,
                                                  tblLoanPropertySummary $tblLoanPropertySummary): ?float
    {
        $tblFileHUDTransaction = tblFileHUDTransaction::Get([
            'LMRId'   => $LMRId,
            'fieldID' => 303,
        ]);
        $loanProceeds = Strings::toNumber($tblFileHUDTransaction->fieldValue);

        if ($loanProceeds < 0
            && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore >= 680
            && $tblLoanPropertySummary->totalAggregateQualifyingLTV <= 75
        ) {
            return $verifiedAssets + ($loanProceeds * -1);
        } else if (
            ($loanProceeds < 0
                && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore >= 680
                && $tblLoanPropertySummary->totalAggregateQualifyingLTV > 75)
            || ($loanProceeds < 0
                && LMRequest::File()->getTblFileHMLO_by_fileID()[0]->midFicoScore < 680)
            || ($loanProceeds >= 0)
        ) {
            return $verifiedAssets;
        }
        return null;
    }

    public static function getFundsToVerify($LMRId, $monthlyPaymentReserve): ?float
    {
        $tblFileHUDTransaction = tblFileHUDTransaction::Get([
            'LMRId'   => $LMRId,
            'fieldID' => 303,
        ]);
        $loanProceeds = Strings::toNumber($tblFileHUDTransaction->fieldValue);

        if ($loanProceeds < 0) {
            return $monthlyPaymentReserve;
        } else return $monthlyPaymentReserve + $loanProceeds;
    }

    public static function getTotalExcessReserves($totalVerifiedAssets, $fundsToVerify): ?float
    {
        return $totalVerifiedAssets - $fundsToVerify;
    }

    public static function getGroundUpConstructionTotalProjectCost(tblLoanPropertySummary $tblLoanPropertySummary,
                                                                   tblProperties          $Property): ?float
    {
        return LMRequest::myFileInfo()->listingRealtorInfo()->costSpent
            + $tblLoanPropertySummary->constructionHardCost
            + $tblLoanPropertySummary->constructionSoftCost
            + $tblLoanPropertySummary->contingencyAmount
            + PropertyAnalysis::calculateQualifyingLoanAmount($Property);
    }

    public static function getGrossProfitMargin(tblLoanPropertySummary $tblLoanPropertySummary,
                                                tblProperties          $Property): ?float
    {
        $grossProfitMargin = 0.0;
        $constructionTotalProjectCost = self::getGroundUpConstructionTotalProjectCost($tblLoanPropertySummary, $Property);

        if (!$Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue) {
            if ($Property->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue) {
                if ($Property->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue > 0) {
                    $grossProfitMargin = ($Property->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue - $constructionTotalProjectCost) / $Property->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue * 100;
                }
            }
        } else if ($Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue) {
            if ($Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue > 0) {
                $grossProfitMargin = ($Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue - $constructionTotalProjectCost) /
                    $Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue * 100;
            }
        }
        return round($grossProfitMargin, 2);
    }

    public static function getFinancedAtClosing(tblLoanPropertySummary $tblLoanPropertySummary): ?float
    {

        return $tblLoanPropertySummary->totalPropertiesLoanAmount
            - self::getGUCConstructionFinanced($tblLoanPropertySummary)
            - $tblLoanPropertySummary->financedInterestReserve;
    }

    public static function getGUCConstructionFinanced(tblLoanPropertySummary $tblLoanPropertySummary): ?float
    {
        return $tblLoanPropertySummary->constructionHardCost +
            $tblLoanPropertySummary->constructionSoftCost +
            $tblLoanPropertySummary->contingencyAmount;
    }


    public static function getMonthlyPostClosingPaymentReserves(?float $totalExcessReserves,
                                                                ?float $totalLoanInterestPayment): ?int
    {
        return $totalLoanInterestPayment ? intval($totalExcessReserves / $totalLoanInterestPayment) : 0;
    }

    public static function getInitialLoanToCost(tblLoanPropertySummary $tblLoanPropertySummary,
                                                tblProperties          $Property): ?float
    {
        $initialLoanCost = 0.0;
        if ((LMRequest::myFileInfo()->listingRealtorInfo()->costSpent
                + PropertyAnalysis::calculateQualifyingLoanAmount($Property)) > 0) {
            $initialLoanCost = $tblLoanPropertySummary->financedAtClosing / (LMRequest::myFileInfo()->listingRealtorInfo()->costSpent
                    + PropertyAnalysis::calculateQualifyingLoanAmount($Property)) * 100;
        }
        return $initialLoanCost;

    }

    public static function getGUCLoanToCost(tblLoanPropertySummary $tblLoanPropertySummary,
                                            tblProperties          $Property): ?float
    {

        $qualifyingLoanAmount = parent::calculateQualifyingLoanAmount($Property);
        $total = LMRequest::myFileInfo()->listingRealtorInfo()->costSpent +
            $tblLoanPropertySummary->constructionSoftCost +
            $tblLoanPropertySummary->constructionHardCost +
            $tblLoanPropertySummary->contingencyAmount +
            $qualifyingLoanAmount +
            $tblLoanPropertySummary->financedInterestReserve;
        if (in_array($Property->getTblPropertiesAnalysis_by_propertyId()->propertyMetrics, [1, 2, 3]) && $total) {
            $GUCLoanToCost = LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount / $total;
        } else {
            $GUCLoanToCost = 0;
        }
        return $GUCLoanToCost * 100;
    }

    public static function GUCARV(tblLoanPropertySummary $tblLoanPropertySummary,
                                  tblProperties          $Property): ?float
    {

        $GUCARV = 0.0;
        if (!$Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue) {
            if ($Property->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue > 0) {
                $GUCARV = LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount / $Property->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue * 100;
            }
        } else if ($Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue) {
            if ($Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue > 0) {
                $GUCARV = LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount / $Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue * 100;
            }
        }
        return round($GUCARV, 2);
    }

    /**
     * @param tblLoanPropertySummary $tblLoanPropertySummary
     * @param tblProperties $Property
     * @return float|null
     */
    public static function GUCLTP(tblLoanPropertySummary $tblLoanPropertySummary,
                                  tblProperties          $Property): ?float
    {
        $GUCLTP = 0.0;
        if (parent::calculateQualifyingLoanAmount($Property) > 0) {
            $GUCLTP = $tblLoanPropertySummary->financedAtClosing / parent::calculateQualifyingLoanAmount($Property) * 100;
        }
        return round($GUCLTP, 2);
    }

    public static function save(): ?int
    {
        if (!self::$LMRId) {
            return 0;
        }
        if (!LMRequest::$LMRId) {
            LMRequest::setLMRId(self::$LMRId);
        }
        return self::saveLoanPurpose();
    }

    public static function saveLoanPurpose(): ?int
    {
        $loanProgram = Request::GetClean('LMRClientType') ?? null;

        if ($loanProgram) {
            saveFileServices::save(self::$LMRId, $loanProgram);
        }

        $tblFileHMLOPropInfo = tblFileHMLOPropInfo::Get(['fileID' => self::$LMRId]) ?? new tblFileHMLOPropInfo();
        $tblFileHMLOPropInfo->propertyNeedRehab = ($loanProgram == 'Bri996' || $loanProgram == 'BRLGUC' ? Request::GetClean('propertyNeedRehab') ?? $tblFileHMLOPropInfo->propertyNeedRehab : '');
        $tblFileHMLOPropInfo->typeOfHMLOLoanRequesting = Request::GetClean('typeOfHMLOLoanRequesting') ?? $tblFileHMLOPropInfo->typeOfHMLOLoanRequesting;
        $tblFileHMLOPropInfo->loanTerm = Request::GetClean('loanTerm') ?? $tblFileHMLOPropInfo->loanTerm;
        $tblFileHMLOPropInfo->lienPosition = Request::isset('lienPosition') ? Database2::strongTypeValue(Request::GetClean('lienPosition'), Database2::DATATYPE_PROPERTY_TYPE_NUMBER) : $tblFileHMLOPropInfo->lienPosition;
        $tblFileHMLOPropInfo->isTherePrePaymentPenalty = Request::GetClean('isTherePrePaymentPenalty') ?? $tblFileHMLOPropInfo->isTherePrePaymentPenalty;
        $tblFileHMLOPropInfo->prePaymentPenalty = Request::GetClean('prePaymentPenalty') ?? $tblFileHMLOPropInfo->prePaymentPenalty;
        $tblFileHMLOPropInfo->exitStrategy = Request::GetClean('exitStrategy') ?? $tblFileHMLOPropInfo->exitStrategy;
        $tblFileHMLOPropInfo->useOfProceeds = Request::GetClean('useOfProceeds') ?? $tblFileHMLOPropInfo->useOfProceeds;
        $tblFileHMLOPropInfo->accountExecutiveLoanExitNotes = Request::GetClean('accountExecutiveLoanExitNotes') ?? $tblFileHMLOPropInfo->accountExecutiveLoanExitNotes;
        $tblFileHMLOPropInfo->Save();

        $tblFileHMLO = tblFileHMLO::Get(['fileID' => self::$LMRId]) ?? new tblFileHMLO();
        $tblFileHMLO->rehabCost = $loanProgram == 'Bri996' || $loanProgram == 'BRLGUC' ? (Request::GetClean('rehabCost') ? Database2::strongTypeValue(Request::GetClean('rehabCost'), Database2::DATATYPE_MONEY) : $tblFileHMLO->rehabCost) : null;
        $tblFileHMLO->save();

        $tblFileHMLONewLoanInfo = tblFileHMLONewLoanInfo::Get(['fileID' => self::$LMRId]) ?? new tblFileHMLONewLoanInfo();
        $tblFileHMLONewLoanInfo->rehabCostPercentageFinanced = $loanProgram == 'Bri996' || $loanProgram == 'BRLGUC' ? (Request::isset('rehabCostPercentageFinanced') ? Database2::strongTypeValue(Request::GetClean('rehabCostPercentageFinanced'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->rehabCostPercentageFinanced) : null;
        $tblFileHMLONewLoanInfo->rehabCostFinanced = $loanProgram == 'Bri996' || $loanProgram == 'BRLGUC' ? (Request::isset('rehabCostFinanced') ? Database2::strongTypeValue(Request::GetClean('rehabCostFinanced'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->rehabCostFinanced) : null;

        $tblFileHMLONewLoanInfo->prePaymentSelectVal = Request::isset('prePaymentSelectVal') ? implode(',', Request::GetClean('prePaymentSelectVal')) : $tblFileHMLONewLoanInfo->prePaymentSelectVal;
        if (Request::GetClean('isTherePrePaymentPenalty') == 'No' || Request::GetClean('isTherePrePaymentPenalty') == '') {
            $tblFileHMLONewLoanInfo->prePaymentSelectVal = '';
        }
        $tblFileHMLONewLoanInfo->originationPointsRate = Request::isset('originationPointsRate') ?
            Database2::strongTypeValue(Request::GetClean('originationPointsRate'), Database2::DATATYPE_MONEY)
            : $tblFileHMLONewLoanInfo->originationPointsRate;
        $tblFileHMLONewLoanInfo->originationPointsValue = Request::isset('originationPointsValue') ? Database2::strongTypeValue(Request::GetClean('originationPointsValue'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->originationPointsValue;

        $tblFileHMLONewLoanInfo->brokerPointsRate = Request::isset('brokerPointsRate') ? Database2::strongTypeValue(Request::GetClean('brokerPointsRate'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->brokerPointsRate;
        $tblFileHMLONewLoanInfo->brokerPointsValue = Request::isset('brokerPointsValue') ? Database2::strongTypeValue(Request::GetClean('brokerPointsValue'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->brokerPointsValue;

        $tblFileHMLONewLoanInfo->cv3OriginationPoint = Request::isset('cv3OriginationPoint') ? Database2::strongTypeValue(Request::GetClean('cv3OriginationPoint'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->cv3OriginationPoint;
        $tblFileHMLONewLoanInfo->cv3OriginationAmount = Request::isset('cv3OriginationAmount') ? Database2::strongTypeValue(Request::GetClean('cv3OriginationAmount'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->cv3OriginationAmount;

        $tblFileHMLONewLoanInfo->brokerProcessingFee = Request::isset('brokerProcessingFee') ? Database2::strongTypeValue(Request::GetClean('brokerProcessingFee'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->brokerProcessingFee;
        $tblFileHMLONewLoanInfo->cv3ReferralPoint = Request::isset('cv3ReferralPoint') ? Database2::strongTypeValue(Request::GetClean('cv3ReferralPoint'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->cv3ReferralPoint;
        $tblFileHMLONewLoanInfo->cv3ReferralAmount = Request::isset('cv3ReferralAmount') ? Database2::strongTypeValue(Request::GetClean('cv3ReferralAmount'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->cv3ReferralAmount;
        $tblFileHMLONewLoanInfo->appraisalFee = Request::isset('appraisalFee') ? Database2::strongTypeValue(Request::GetClean('appraisalFee'), Database2::DATATYPE_MONEY) : $tblFileHMLONewLoanInfo->appraisalFee;
        $tblFileHMLONewLoanInfo->rateLockDate = Request::isset('rateLockDate') ? Database2::strongTypeValue(Request::GetClean('rateLockDate'), Database2::DATATYPE_DATE) : $tblFileHMLONewLoanInfo->rateLockDate;
        $tblFileHMLONewLoanInfo->rateLockExpirationDate = Request::isset('rateLockExpirationDate') ? Database2::strongTypeValue(Request::GetClean('rateLockExpirationDate'), Database2::DATATYPE_DATE) : $tblFileHMLONewLoanInfo->rateLockExpirationDate;
        $tblFileHMLONewLoanInfo->rateLockPeriod = Request::isset('rateLockPeriod') ? Database2::strongTypeValue(Request::GetClean('rateLockPeriod'), Database2::DATATYPE_NUMBER) : $tblFileHMLONewLoanInfo->rateLockPeriod;
        $tblFileHMLONewLoanInfo->Save();


        $tblFilePropertyInfo = tblFilePropertyInfo::Get(['LMRId' => self::$LMRId]) ?? new tblFilePropertyInfo();
        $tblFilePropertyInfo->isHouseProperty = Request::GetClean('isHouseProperty') ?? $tblFilePropertyInfo->isHouseProperty;
        $tblFilePropertyInfo->save();


        $tblQAInfo = tblQAInfo::Get(['LMRId' => self::$LMRId]) ?? new tblQAInfo();
        $tblQAInfo->desiredClosingDate = Request::isset('desiredClosingDate') ? Database2::strongTypeValue(Request::GetClean('desiredClosingDate'), Database2::DATATYPE_DATE) : $tblQAInfo->desiredClosingDate;
        $tblQAInfo->save();

        $tblFileHMLO = tblFileHMLO::Get(['fileID' => self::$LMRId]) ?? new tblFileHMLO();
        $tblFileHMLO->targetClosingDate = Request::isset('targetClosingDate') ? Database2::strongTypeValue(Request::GetClean('targetClosingDate'), Database2::DATATYPE_DATE) : $tblFileHMLO->targetClosingDate;

        $tblFileHMLO->midFicoScore = Request::isset('midFicoScore') ? Database2::strongTypeValue(Request::GetClean('midFicoScore'), Database2::DATATYPE_NUMBER) : $tblFileHMLO->midFicoScore;
        $tblFileHMLO->borEquifaxScore = Request::isset('borEquifaxScore') ? Database2::strongTypeValue(Request::GetClean('borEquifaxScore'), Database2::DATATYPE_NUMBER) : $tblFileHMLO->borEquifaxScore;
        $tblFileHMLO->borTransunionScore = Request::isset('borTransunionScore') ? Database2::strongTypeValue(Request::GetClean('borTransunionScore'), Database2::DATATYPE_NUMBER) : $tblFileHMLO->borTransunionScore;
        $tblFileHMLO->borExperianScore = Request::isset('borExperianScore') ? Database2::strongTypeValue(Request::GetClean('borExperianScore'), Database2::DATATYPE_NUMBER) : $tblFileHMLO->borExperianScore;
        $tblFileHMLO->coBorEquifaxScore = Request::isset('coBorEquifaxScore') ? Database2::strongTypeValue(Request::GetClean('coBorEquifaxScore'), Database2::DATATYPE_NUMBER) : $tblFileHMLO->coBorEquifaxScore;
        $tblFileHMLO->coBorTransunionScore = Request::isset('coBorTransunionScore') ? Database2::strongTypeValue(Request::GetClean('coBorTransunionScore'), Database2::DATATYPE_NUMBER) : $tblFileHMLO->coBorTransunionScore;
        $tblFileHMLO->coBorExperianScore = Request::isset('coBorExperianScore') ? Database2::strongTypeValue(Request::GetClean('coBorExperianScore'), Database2::DATATYPE_NUMBER) : $tblFileHMLO->coBorExperianScore;
        $tblFileHMLO->save();

        $tblShortSale = tblShortSale::Get(['LMRId' => self::$LMRId]) ?? new tblShortSale();
        $tblShortSale->LMRId = self::$LMRId;
        $tblShortSale->costSpent = Request::isset('costSpent') ? Database2::strongTypeValue(Request::GetClean('costSpent'), Database2::DATATYPE_MONEY) : $tblShortSale->costSpent;
        $tblShortSale->save();

        $tblFile = tblFile::Get(['LMRId' => self::$LMRId]) ?? new tblFile();
        if ($tblFile->LMRId
            && Request::isset('lien1Rate')) {
            $tblFile->lien1Rate = Request::isset('lien1Rate') ? Database2::strongTypeValue(Request::GetClean('lien1Rate'), Database2::DATATYPE_MONEY) : $tblFile->lien1Rate;
            $tblFile->save();
        }


        $tblLoanPropertySummary = tblLoanPropertySummary::Get(['LMRId' => self::$LMRId]) ?? new tblLoanPropertySummary();
        $tblLoanPropertySummary->LMRId = self::$LMRId;
        //$tblLoanPropertySummary->interestOnlyMonthlyPayment = Request::GetClean('interestOnlyMonthlyPayment']) ? Database2::strongTypeValue(Request::GetClean('interestOnlyMonthlyPayment'], Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->interestOnlyMonthlyPayment;
        //$tblLoanPropertySummary->interestPaymentHoldBackMonths = Request::GetClean('interestPaymentHoldBackMonths']) ? Database2::strongTypeValue(Request::GetClean('interestPaymentHoldBackMonths'], Database2::DATATYPE_NUMBER) : $tblLoanPropertySummary->interestPaymentHoldBackMonths;
        //$tblLoanPropertySummary->interestPaymentHoldBack = Request::GetClean('interestPaymentHoldBack']) ? Database2::strongTypeValue(Request::GetClean('interestPaymentHoldBack'], Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->interestPaymentHoldBack;

        /*        $tblLoanPropertySummary->totalAllocatedLoanAmount = Request::GetClean('totalAllocatedLoanAmount']) ? Database2::strongTypeValue(Request::GetClean('totalAllocatedLoanAmount'], Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalAllocatedLoanAmount;*/

        $tblLoanPropertySummary->totalPropertiesLoanAmount = Request::isset('totalPropertiesLoanAmount') ? Database2::strongTypeValue(Request::GetClean('totalPropertiesLoanAmount'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesLoanAmount;
        $tblLoanPropertySummary->isPropertyHaveSubordinateFinancing = Request::GetClean('isPropertyHaveSubordinateFinancing') ?? null;
        $tblLoanPropertySummary->sub1DSCR = Request::isset('sub1DSCR') ? intval(Request::Get('sub1DSCR')) : $tblLoanPropertySummary->sub1DSCR;

        $tblLoanPropertySummary->subordinateFinancingAmount = $tblLoanPropertySummary->isPropertyHaveSubordinateFinancing ? (Request::isset('subordinateFinancingAmount') ? Database2::strongTypeValue(Request::GetClean('subordinateFinancingAmount'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->subordinateFinancingAmount) : null;

        $tblLoanPropertySummary->bridgeLoanToValue = (Request::isset('bridgeLoanToValue') ? Database2::strongTypeValue(Request::GetClean('bridgeLoanToValue'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->bridgeLoanToValue);

        $tblLoanPropertySummary->bridgeCombinedLoanToValue = (Request::isset('bridgeCombinedLoanToValue') ? Database2::strongTypeValue(Request::GetClean('bridgeCombinedLoanToValue'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->bridgeCombinedLoanToValue);
        $tblLoanPropertySummary->renovationSumOfAllocatedRehabCost = (Request::isset('renovationSumOfAllocatedRehabCost') ? Database2::strongTypeValue(Request::GetClean('renovationSumOfAllocatedRehabCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->renovationSumOfAllocatedRehabCost);

        $tblLoanPropertySummary->propertyTotalProjectCost = (Request::isset('propertyTotalProjectCost') ? Database2::strongTypeValue(Request::GetClean('propertyTotalProjectCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->propertyTotalProjectCost);
        $tblLoanPropertySummary->LTCTotalLoanAmount = (Request::isset('LTCTotalLoanAmount') ? Database2::strongTypeValue(Request::GetClean('LTCTotalLoanAmount'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->LTCTotalLoanAmount);
        $tblLoanPropertySummary->constructionRatio = (Request::isset('constructionRatio') ? Database2::strongTypeValue(Request::GetClean('constructionRatio'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->constructionRatio);


        $tblLoanPropertySummary->monthly30YrPIPayment = (Request::isset('monthly30YrPIPayment') ? Database2::strongTypeValue(Request::GetClean('monthly30YrPIPayment'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->monthly30YrPIPayment);
        $tblLoanPropertySummary->totalPropertiesITIA = (Request::isset('totalPropertiesITIA') ? Database2::strongTypeValue(Request::GetClean('totalPropertiesITIA'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalPropertiesITIA);

        $tblLoanPropertySummary->monthlyPaymentReserve = (Request::isset('monthlyPaymentReserve') ? Database2::strongTypeValue(Request::GetClean('monthlyPaymentReserve'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->monthlyPaymentReserve);

        $verifiedAssets = LMRequest::myFileInfo()->assetInfo()->totalVerifiedAssets;
        $tblLoanPropertySummary->totalVerifiedAssets = self::getTotalVerifiedAssets(LMRequest::$LMRId,
            $verifiedAssets,
            $tblLoanPropertySummary
        );

        $tblLoanPropertySummary->fundsToVerify = (Request::isset('fundsToVerify') ? Database2::strongTypeValue(Request::GetClean('fundsToVerify'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->fundsToVerify);

        $tblLoanPropertySummary->totalExcessReserves = (Request::isset('totalExcessReserves') ? Database2::strongTypeValue(Request::GetClean('totalExcessReserves'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalExcessReserves);


        $tblLoanPropertySummary->monthlyPostClosingPaymentReserves = intval(Request::isset('monthlyPostClosingPaymentReserves') ? Database2::strongTypeValue(Request::GetClean('monthlyPostClosingPaymentReserves'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->monthlyPostClosingPaymentReserves);

        $tblLoanPropertySummary->constructionType = (Request::isset('constructionType') && Request::GET('constructionType') ? Database2::strongTypeValue(Request::GetClean('constructionType'), Database2::DATATYPE_NUMBER) : $tblLoanPropertySummary->constructionType);
        $tblLoanPropertySummary->constructionHardCost = (Request::isset('constructionHardCost') ? Database2::strongTypeValue(Request::GetClean('constructionHardCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->constructionHardCost);
        $tblLoanPropertySummary->constructionSoftCost = (Request::isset('constructionSoftCost') ? Database2::strongTypeValue(Request::GetClean('constructionSoftCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->constructionSoftCost);
        $tblLoanPropertySummary->contingencyTypeOption = (Request::isset('contingencyTypeOption') ? Database2::strongTypeValue(Request::GetClean('contingencyTypeOption'), Database2::DATATYPE_STRING) : $tblLoanPropertySummary->contingencyTypeOption);
        $tblLoanPropertySummary->contingencyPercentage = (Request::isset('contingencyPercentage') ? Database2::strongTypeValue(Request::GetClean('contingencyPercentage'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->contingencyPercentage);
        $tblLoanPropertySummary->contingencyAmount = (Request::isset('contingencyAmount') ? Database2::strongTypeValue(Request::GetClean('contingencyAmount'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->contingencyAmount);
        $tblLoanPropertySummary->financedInterestReserveMonths = Request::isset('financedInterestReserveMonths') ? Database2::strongTypeValue(Request::GetClean('financedInterestReserveMonths'), Database2::DATATYPE_NUMBER) : $tblLoanPropertySummary->financedInterestReserveMonths;
        $tblLoanPropertySummary->financedInterestReserve = (Request::isset('financedInterestReserve') ? Database2::strongTypeValue(Request::GetClean('financedInterestReserve'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->financedInterestReserve);
        $tblLoanPropertySummary->interestReserveType = (Request::isset('interestReserveType') ? Database2::strongTypeValue(Request::GetClean('interestReserveType'), Database2::DATATYPE_NUMBER) : $tblLoanPropertySummary->interestReserveType);
        $tblLoanPropertySummary->constructionTotalProjectCost = (Request::isset('constructionTotalProjectCost') ? Database2::strongTypeValue(Request::GetClean('constructionTotalProjectCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->constructionTotalProjectCost);
        $tblLoanPropertySummary->GUCConstructionFinanced = (Request::isset('GUCConstructionFinanced') ? Database2::strongTypeValue(Request::GetClean('GUCConstructionFinanced'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->GUCConstructionFinanced);
        $tblLoanPropertySummary->targetLTC = (Request::isset('targetLTC') ? Database2::strongTypeValue(Request::GetClean('targetLTC'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->targetLTC);
        $tblLoanPropertySummary->GUCTotalLoanAmount = (Request::isset('GUCTotalLoanAmount') ? Database2::strongTypeValue(Request::GetClean('GUCTotalLoanAmount'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->GUCTotalLoanAmount);

        $tblLoanPropertySummary->grossProfitMargin = (Request::isset('grossProfitMargin') ? Database2::strongTypeValue(Request::GetClean('grossProfitMargin'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->grossProfitMargin);
        $tblLoanPropertySummary->initialLoanToCost = (Request::isset('initialLoanToCost') ? Database2::strongTypeValue(Request::GetClean('initialLoanToCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->initialLoanToCost);

        $tblLoanPropertySummary->financedAtClosing = (Request::isset('financedAtClosing') ? Database2::strongTypeValue(Request::GetClean('financedAtClosing'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->financedAtClosing);

        $tblLoanPropertySummary->softCostPercentageOfBudget = (Request::isset('softCostPercentageOfBudget') ? Database2::strongTypeValue(Request::GetClean('softCostPercentageOfBudget'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->softCostPercentageOfBudget);

        $tblLoanPropertySummary->GUCLoanToCost = (Request::isset('GUCLoanToCost') ? Database2::strongTypeValue(Request::GetClean('GUCLoanToCost'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->GUCLoanToCost);

        $tblLoanPropertySummary->GUCARV = (Request::isset('GUCARV') ? Database2::strongTypeValue(Request::GetClean('GUCARV'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->GUCARV);
        $tblLoanPropertySummary->GUCLTP = (Request::isset('GUCLTP') ? Database2::strongTypeValue(Request::GetClean('GUCLTP'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->GUCLTP);


        $tblLoanPropertySummary->editableField = (Request::isset('editableField') ? Database2::strongTypeValue(Request::GetClean('editableField'), Database2::DATATYPE_NUMBER) : $tblLoanPropertySummary->editableField);

        $tblLoanPropertySummary->totalLoanInterestPayment = (Request::isset('totalLoanInterestPayment') ? Database2::strongTypeValue(Request::GetClean('totalLoanInterestPayment'), Database2::DATATYPE_MONEY) : $tblLoanPropertySummary->totalLoanInterestPayment);

        if ($tblLoanPropertySummary->id) {
            $tblLoanPropertySummary->updatedDate = Dates::Timestamp();
            $tblLoanPropertySummary->updatedGroup = Strings::GetSess('userGroup') ?? null;
            $tblLoanPropertySummary->updatedBy = Strings::GetSess('userNumber') ?? null;
        } else {
            $tblLoanPropertySummary->createdDate = Dates::Timestamp();
            $tblLoanPropertySummary->createdGroup = Strings::GetSess('userGroup') ?? null;
            $tblLoanPropertySummary->createdBy = Strings::GetSess('userNumber') ?? null;
        }
        $tblLoanPropertySummary->save();

        if (LMRequest::myFileInfo()->getLMRClientType() == 'BRLGUC') {
            parent::init(LMRequest::$LMRId);
            PropertyAnalysis::updatePrimaryPropertyInterestOnlyPayment($tblLoanPropertySummary, parent::$primaryPropertyInfo);
        }

        updateFileLastUpdatedDate::getReport([
            'fileID' => self::$LMRId,
        ]);
        return $tblLoanPropertySummary->id ?? 0;
    }

    public static function updateBrideRenovationValues($LMRId, tblProperties $Property): void
    {
        $tblLoanPropertySummary = tblLoanPropertySummary::Get(['LMRId' => $LMRId]);
        $tblLoanPropertySummary->propertyTotalProjectCost = self::getPropertyTotalProjectCost($Property);
        $QualifyingLoanAmountForRenovation = self::getQualifyingLoanAmountForRenovation($Property);
        $tblLoanPropertySummary->LTCTotalLoanAmount = $QualifyingLoanAmountForRenovation ? $tblLoanPropertySummary->totalPropertiesLoanAmount / $QualifyingLoanAmountForRenovation * 100 : 0;
        $tblLoanPropertySummary->constructionRatio = self::getConstructionRatio($Property);
        $tblLoanPropertySummary->renovationSumOfAllocatedRehabCost = self::getRenovationSumOfAllocatedRehabCost($Property) ?? null;
        $tblLoanPropertySummary->bridgeAfterRepairValue = self::getPropertyAfterRepairValue($Property, $tblLoanPropertySummary) ?? null;

        if ($tblLoanPropertySummary->id) {
            $tblLoanPropertySummary->updatedDate = Dates::Timestamp();
            $tblLoanPropertySummary->updatedGroup = Strings::GetSess('userGroup') ?? null;
            $tblLoanPropertySummary->updatedBy = Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null;
        } else {
            $tblLoanPropertySummary->createdDate = Dates::Timestamp();
            $tblLoanPropertySummary->createdGroup = Strings::GetSess('userGroup') ?? null;
            $tblLoanPropertySummary->createdBy = Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null;
        }
        $tblLoanPropertySummary->save();
    }

    public static function getPropertyTotalProjectCost(tblProperties $Property): ?float
    {
        return $Property->getTblPropertiesDetails_by_propertyId()->propertyPurchasePrice
            + LMRequest::myFileInfo()->listingRealtorInfo()->costSpent
            + LMRequest::myFileInfo()->fileHMLOInfo()->rehabCost;
    }

    public static function getQualifyingLoanAmountForRenovation(tblProperties $Property): ?float
    {
        $qualifyingLoanAmount = parent::calculateQualifyingLoanAmount($Property);
        $qualifyingLoanAmount += LMRequest::myFileInfo()->fileHMLOInfo()->rehabCost;
        $qualifyingLoanAmount += ($Property->getTblPropertiesAnalysis_by_propertyId()->propertyMetrics == 1 ? LMRequest::myFileInfo()->listingRealtorInfo()->costSpent : 0);
        return $qualifyingLoanAmount;
    }

    public static function getConstructionRatio(tblProperties $Property): ?float
    {
        $constructionRatio = 0.0;
        $qualifyAmount = parent::calculateQualifyingLoanAmount($Property);
        if ($qualifyAmount > 0) {
            $constructionRatio = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostFinanced / $qualifyAmount * 100;
        }
        return $constructionRatio;
    }

    public static function getRenovationSumOfAllocatedRehabCost(tblProperties $Property): ?float
    {
        return $Property->getTblPropertiesDetails_by_propertyId()->propertyAllocatedLoanAmount +
            LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostFinanced;
    }

    public static function getPropertyAfterRepairValue(tblProperties $Property, tblLoanPropertySummary $tblLoanPropertySummary): ?float
    {

        if (!$Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue) {
            $propertyAfterRepairValue = $Property->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue ? ($tblLoanPropertySummary->totalPropertiesLoanAmount / $Property->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue * 100) : 0;
        } else {
            $propertyAfterRepairValue = $Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue ? ($tblLoanPropertySummary->totalPropertiesLoanAmount / $Property->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue * 100) : 0;
        }
        return $propertyAfterRepairValue;
    }

    public static function getInterestOnlyMonthlyPayment(?float $totalPropertiesLoanAmount = 0,
                                                         ?float $interestRate = 0
    ): ?float
    {
        return $interestRate ? $totalPropertiesLoanAmount * $interestRate / 1200 : 0;
    }

    public static function getCv3CustomTotalLoanAmountLogic(): int
    {
        $filePrimaryStatus = fileVelocityInfo::getReportObj('', LMRequest::File()->LMRId);
        $customPrimaryStatus = [
            'Initial UW',
            'Collecting Conditions',
            'Final UW',
            'Closing Docs',
            'Funding',
            'Post-Close',
            'Decisioning',
            'Adverse',
            'Adverse Complete',
            'Prospect Cancelled',
            'Completed',
        ];
        $primaryStatuses = getPrimatStatusIdsByNames::getReport($customPrimaryStatus, LMRequest::File()->FPCID);

        return intval(Dates::Datestamp($filePrimaryStatus->recordDate) <= Dates::Datestamp('2024-07-15')
            && in_array(LMRequest::File()->getTblFileResponse_by_LMRId()->primeStatusId, explode(',', $primaryStatuses->PSIDs)));
    }

    public static function updatePoints(tblLoanPropertySummary $tblLoanPropertySummary, $LMRId): void
    {
        $loanAmount = 0;
        if (self::getCv3CustomTotalLoanAmountLogic() || !$tblLoanPropertySummary->totalPropertiesLoanAmount
        ) {
            HMLOLoanTermsCalculation::InitForLMRId($LMRId);
            $loanAmount = HMLOLoanTermsCalculation::$totalLoanAmount;
        } else if ($tblLoanPropertySummary->totalPropertiesLoanAmount > 0) {
            $loanAmount = $tblLoanPropertySummary->totalPropertiesLoanAmount;
        }
        $tblFileHMLONewLoanInfo = tblFileHMLONewLoanInfo::Get(['fileID' => $LMRId]) ?? new tblFileHMLONewLoanInfo();
        $tblFileHMLONewLoanInfo->fileID = $LMRId;
        $tblFileHMLONewLoanInfo->originationPointsValue =
            HMLOLoanTermsCalculation::setOriginationBrokerValue(
                $tblFileHMLONewLoanInfo->originationPointsRate,
                $loanAmount,
                $tblFileHMLONewLoanInfo->closingCostFinanced,
                LMRequest::File()->FPCID == glPCID::PCID_PROD_CV3 ? false : $tblFileHMLONewLoanInfo->origination_based_on_total_loan_amt
            );
        $tblFileHMLONewLoanInfo->brokerPointsValue = HMLOLoanTermsCalculation::setOriginationBrokerValue(
            $tblFileHMLONewLoanInfo->brokerPointsRate,
            $loanAmount,
            $tblFileHMLONewLoanInfo->closingCostFinanced,
            LMRequest::File()->FPCID == glPCID::PCID_PROD_CV3 ? false : $tblFileHMLONewLoanInfo->broker_based_on_total_loan_amt
        );
        //$tblFileHMLONewLoanInfo->cv3OriginationAmount = $tblFileHMLONewLoanInfo->cv3OriginationPoint * $loanAmount / 100;
        $tblFileHMLONewLoanInfo->cv3OriginationAmount = LMRequest::calOriginationPoints() * $loanAmount / 100;
        $tblFileHMLONewLoanInfo->cv3ReferralAmount = $tblFileHMLONewLoanInfo->cv3ReferralPoint * $loanAmount / 100;
        $tblFileHMLONewLoanInfo->save();
    }

}
