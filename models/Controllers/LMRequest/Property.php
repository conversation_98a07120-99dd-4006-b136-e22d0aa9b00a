<?php

namespace models\Controllers\LMRequest;

use models\composite\oFileUpdate\saveFileRentRoll;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\backoffice\LMRequest;
use models\Database2;
use models\dataImporter\fieldMap;
use models\FileStorage;
use models\lendingwise\tblACHInfo;
use models\lendingwise\db\tblProperties_db;
use models\lendingwise\tblContacts;
use models\lendingwise\tblFileContacts;
use models\lendingwise\tblProperties;
use models\lendingwise\tblPropertiesAccess;
use models\lendingwise\tblPropertiesAnalysis;
use models\lendingwise\tblPropertiesAppraiserDetails;
use models\lendingwise\tblPropertiesCharacteristics;
use models\lendingwise\tblPropertiesDetails;
use models\lendingwise\tblPropertiesFloodCertificates;
use models\lendingwise\tblPropertiesHOA;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;
use models\UploadServer;

class Property extends strongType
{
    public static ?int $allowToEdit = null;
    public static ?int $hideThisField = null;
    public static ?string $activeTab = null;
    public static ?array $secArr = null;

    public static ?string $typeOfHMLOLoanRequesting = null;
    public static ?string $propertyAddress = null;
    public static ?int $tabIndex = null;
    public static ?int $publicUser = null;
    public static ?array $propertyCountyInfo = null;
    public static ?array $fieldsInfo = null;
    public static ?string $borrowerActiveSectionDisp = null;
    public static ?array $BORFsecArr = null;
    public static ?array $propertyLocations = null;
    public static ?tblPropertiesCharacteristics $propertyCharacteristics = null;
    public static ?tblPropertiesDetails $propertyDetails = null;
    public static ?tblPropertiesAccess $propertyAccess = null;
    public static ?array $propertyRentRoll = null;
    public static ?array $eachProperty = null;

    public static ?int $LMRId = null;
    public static ?string $userGroup = null;
    public static ?int $userNumber = null;
    public static ?int $propertyId = null;

    public static ?array $fieldValidation = null;
    public static ?array $fieldMap = null;
    public static ?array $fieldLabels = null;
    public static ?array $postValues = null;
    public static ?array $fieldMigrations = null;

    public static ?tblProperties $propertyInfo = null;

    public static ?string $primaryPropertyFullAddress = null;
    public static ?string $primaryPropertyFullAddressWithUnit = null;
    public static ?tblProperties $primaryPropertyInfo = null;

    /* @var tblProperties[] $propertiesInfo */
    public static ?array $propertiesInfo = null;

    /* @var tblProperties[] $blanketLoanPropertiesInfo */
    public static ?array $blanketLoanPropertiesInfo = null;

    public static ?bool $isWebform = null;

    public static ?int $propertyIndex = 1;
    public static array $propertiesInserted = [];
    public static array $propertiesRentrollInserted = [];
    public static array $propertiesFloodCertificatesInserted = [];
    public static array $propertiesAppraisersInserted = [];

    public static array $propertiesHOAInserted = [];
    public static ?array $states = null;

    const propertyTypeOfViewList = [
        1 => 'Mountain',
        2 => 'Ocean',
        3 => 'River',
        4 => 'Lake',
        5 => 'City',
        6 => 'Golf',
        7 => 'Other',
    ];

    public static ?array $propertyTypeOfViewList = self::propertyTypeOfViewList;

    const propertyTypeOfFrontageList = [
        1 => 'None',
        2 => 'Ocean',
        3 => 'River',
        4 => 'Lake',
        5 => 'Golf',
        6 => 'Intercoastal',
        7 => 'Other',
    ];

    public static ?array $propertyTypeOfFrontageList = self::propertyTypeOfFrontageList;

    const propertyLeaseValidationList = [
        1 => 'Appraisal Rents',
        2 => 'Actual Leases',
        3 => 'Market Rents',
        4 => 'Other',
    ];

    public static ?array $propertyLeaseValidationList = self::propertyLeaseValidationList;

    const propertyAccessRelationshipList = [
        1 => 'Property Manager',
        2 => 'Tenant',
        3 => 'Real Estate Agent',
        4 => 'Seller',
        5 => 'Borrower',
        6 => 'Other',
    ];

    public static ?array $propertyAccessRelationshipList = self::propertyAccessRelationshipList;

    const propertyFloodZoneList = [
        1  => 'X',
        2  => 'D',
        3  => 'C',
        4  => 'B',
        5  => 'A',
        6  => 'AE',
        7  => 'A1-30',
        8  => 'AH',
        9  => 'AO',
        10 => 'AR',
        11 => 'A99',
        12 => 'V',
        13 => 'VE',
        14 => 'V1-30',
    ];

    public static ?array $propertyFloodZoneList = self::propertyFloodZoneList;

    const propertyRentRollLeasePaymentFrequency = [
        1 => 'Annual',
        2 => 'Month-To-Month',
        4 => 'Short-Term Rental',
        3 => 'Other',
    ];

    public static ?array $propertyRentRollLeasePaymentFrequency = self::propertyRentRollLeasePaymentFrequency;

    const propertyRentRollPreliminaryRentSource = [
        1 => 'Actual Rents',
        2 => 'Borrower Estimated Rents',
        3 => 'Market Rents',
    ];

    public static ?array $propertyRentRollPreliminaryRentSource = self::propertyRentRollPreliminaryRentSource;

    const propertyRentRollMarketRentSource = [
        1 => 'RentRange',
        2 => 'HouseCanary',
        3 => 'RentCast',
        4 => '1007',
    ];

    public static ?array $propertyRentRollMarketRentSource = self::propertyRentRollMarketRentSource;

    const propertyRentRollLeaseValidation = [
        1 => 'Actual Rents',
        2 => 'Market Rents',
        3 => '120% Market Rents',
    ];

    public static ?array $propertyRentRollLeaseValidation = self::propertyRentRollLeaseValidation;

    const propertyTaxSourceList = [
        1 => 'Property Tax Estimator',
        2 => 'Tax Cert/Roll',
        3 => 'Title Report',
        4 => 'County Tax Assessor',
    ];

    public static ?array $propertyTaxSourceList = self::propertyTaxSourceList;

    const propertyMetrics = [
        1 => 'Purchase Price',
        2 => 'AIV',
        3 => 'Land AIV (60% GUC Only)',
    ];

    public static ?array $propertyMetrics = self::propertyMetrics;

    const propertyCurrentlyLeasedList = [
        1 => 'Long-Term Rental',
        2 => 'Short-Term Rental',
        3 => 'Month-to-Month',
        4 => 'TBD',
    ];

    public static ?array $propertyCurrentlyLeasedList = self::propertyCurrentlyLeasedList;

    const propertyFuturePropertyType = [
        1 => 'Single Family Home',
        2 => '2-4 Units',
        3 => 'Detached Townhouse',
    ];

    public static ?array $propertyFuturePropertyType = GpropertyTypeNumbArray::GpropertyTypeNumbArray;


    const propertyRequiredValuationMethods = [
        1 => 'Appraisal/Interior',
        2 => 'Appraisal/Exterior',
        3 => 'BPO/Exterior',
        4 => 'BPO/Interior',
        5 => 'AVM',
        6 => 'CMA',
        7 => 'Inhouse Value Estimate',
        8 => 'Other',
    ];

    public static ?array $propertyRequiredValuationMethods = self::propertyRequiredValuationMethods;


    /**
     * @param int|null $LMRId
     * @return void
     */
    public static function init(?int $LMRId = null): void
    {
        self::$LMRId = $LMRId;
        self::$propertiesInfo = self::getPropertiesInfo(self::$LMRId);
        self::$primaryPropertyInfo = self::getPrimaryProperty(self::$propertiesInfo);
        self::$primaryPropertyFullAddress = self::getPropertyFullAddress(self::$primaryPropertyInfo);
        self::$primaryPropertyFullAddressWithUnit = self::getPropertyFullAddressWithUnit(self::$primaryPropertyInfo);
        self::$blanketLoanPropertiesInfo = self::getBlanketLoanProperties(self::$propertiesInfo);
        self::$states = Arrays::fetchStatesWithKeys();
    }

    /**
     * @param int|null $LMRId
     * @return array|null
     */
    public static function getPropertiesInfo(?int $LMRId): ?array
    {
        $result = tblProperties::GetAll([
            'LMRId' => $LMRId,
        ],
            [
                'isPrimary'  => 'DESC',
                'propertyId' => 'ASC',
            ],
        );
        $list = [];
        foreach ($result as $property) {
            $list[$property->propertyId] = $property;
        }

        return $list;
    }

    /**
     * @param int|null $LMRId
     * @return int|null
     */
    public static function getPropertyCount(?int $LMRId): ?int
    {
        if (!$LMRId) {
            return 0;
        }

        return tblProperties::GetCount([
            tblProperties_db::COLUMN_LMRID => $LMRId,
        ]);
    }

    /**
     * @param array|null $propertiesInfo
     * @return tblProperties|null
     */
    public static function getPrimaryProperty(?array $propertiesInfo): ?tblProperties
    {
        $primaryProperty = null;
        foreach ($propertiesInfo as $propertyInfo) {
            if ($propertyInfo->isPrimary) {
                $primaryProperty = $propertyInfo;
                break;
            }
        }
        return $primaryProperty ?: new tblProperties();
    }


    /**
     * @param tblProperties|null $propertyInfo
     * @return string|null
     */
    public static function getPropertyFullAddress(?tblProperties $propertyInfo): ?string
    {
        return $propertyInfo->LMRId ? (ucfirst(Strings::arrayToString([
            $propertyInfo->propertyAddress,
            $propertyInfo->propertyUnit,
            $propertyInfo->propertyCity,
            Strings::convertState($propertyInfo->propertyState),
            $propertyInfo->propertyZipCode,
        ]))) : null;
    }

    /**
     * @param tblProperties[]|null $propertiesInfo
     * @return tblProperties[]|null
     */
    public static function getBlanketLoanProperties(?array $propertiesInfo): ?array
    {
        $blanketLoanPropertiesInfo = [];
        foreach ($propertiesInfo as $propertyInfo) {
            if (!$propertyInfo->isPrimary) {
                $blanketLoanPropertiesInfo[] = $propertyInfo;
            }
        }
        return $blanketLoanPropertiesInfo;
    }


    /**
     * @param int|null $LMRId
     * @return tblProperties|null
     */
    public static function getPrimaryPropertyInfo(?int $LMRId): ?tblProperties
    {
        return self::getPropertyInfo($LMRId, 1);
    }


    /**
     * @param tblProperties|null $propertyInfo
     * @return string|null
     */
    public static function getPropertyTypeText(?tblProperties $propertyInfo): ?string
    {
        return ($propertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyType) ?
            GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyType] : '';
    }


    /**
     * @param int|null $propertyId
     * @return tblProperties|null
     */
    public static function getPropertyInfo_by_propertyId(?int $propertyId): ?tblProperties
    {
        return self::getPropertyInfo(null, null, $propertyId);
    }


    /**
     * @param int|null $LMRId
     * @param int|null $isPrimary
     * @param int|null $propertyId
     * @return tblProperties|null
     */
    public static function getPropertyInfo(?int $LMRId = null,
                                           ?int $isPrimary = null,
                                           ?int $propertyId = null): ?tblProperties
    {
        $params = null;
        if ($LMRId) {
            $params['LMRId'] = $LMRId;
        }
        if ($isPrimary === 0 || $isPrimary === 1) {
            $params['isPrimary'] = $isPrimary;
        }
        if ($propertyId) {
            $params['propertyId'] = $propertyId;
        }
        return (tblProperties::Get($params)) ?: new tblProperties();
    }

    /**
     * @return void
     */
    public static function initFieldValidation()
    {
        self::$fieldMap = [
            'propertyAddress'          => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(16) default NULL'),
            'propertyUnit'             => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(16) default NULL'),
            'propertyCity'             => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(16) default NULL'),
            'propertyState'            => fieldMap::create(Database2::DATATYPE_STATE, 'varchar(8) default NULL'),
            'propertyCounty'           => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(16) default NULL'),
            'propertyZipCode'          => fieldMap::create(Database2::DATATYPE_ZIP, 'varchar(16) default NULL'),
            'propertyCountry'          => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(4) default NULL'),
            'propertyLocation'         => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(16) default NULL'),
            'propertyMunicipality'     => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(64) default NULL'),
            'propertyParcelNumber'     => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),
            'propertyLegalDescription' => fieldMap::create(Database2::DATATYPE_STRING, 'longtext'),
            'propertyDistrict'         => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),
            'propertySection'          => fieldMap::create(Database2::DATATYPE_STRING, 'longtext'),
            'propertyBlock'            => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),
            'propertyLot'              => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),
            'propertyMSA'              => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),

            'propertyType'             => fieldMap::create(Database2::DATATYPE_NUMBER, 'int default NULL'),
            'propertyCondoEligibility' => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),
            'propertySqFt'             => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(16) default NULL'),
            'propertyAcres'            => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(16) default NULL'),


            'propertyValue' => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),

            'propertyEstimatedValue'          => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'annualPropertyTaxes'             => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyAcquisitionDate'         => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'propertyZillowValue'             => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyPricePerDoor'            => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyTaxDueDate'              => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'pastDuePropertyTaxes'            => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyPayOffAmount'            => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyAllocatedLoanAmount'     => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyAnnualInsurancePremiums' => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),

            'propertyPastDuePropertyTaxes' => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),

            'propertyAfterRepairValue'       => fieldMap::create(Database2::DATATYPE_MONEY, 'DATE default NULL'),
            'propertyListPrice'              => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyListDate'               => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'propertyCostSpent'              => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyPayOffLienRelease'      => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyPurchasePrice'          => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(15,2) default NULL'),
            'propertyLTV'                    => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(5,2) default NULL'),
            'propertyLoanToPurchasePrice'    => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(5,2) default NULL'),
            'propertyLoanToAfterRepairValue' => fieldMap::create(Database2::DATATYPE_MONEY, 'decimal(5,2) default NULL'),
            'propertyYearBuilt'              => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyRenovatedYear'          => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyTaxYear'                => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyMLSNumber'              => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyRehabToComplete'        => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyAccessRelationship'     => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyWaterFront'             => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyYearPurchased'          => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfRooms'          => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfBedRooms'       => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfBathRooms'      => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfHalfBathRooms'  => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyIsHomeHaveBasement'     => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyIsBasementFinished'     => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyIsHomeHaveGarage'       => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfParkings'       => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfBuildings'      => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfUnits'          => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfParcels'        => fieldMap::create(Database2::DATATYPE_NUMBER),
            'futurePropertyType'             => fieldMap::create(Database2::DATATYPE_NUMBER),
            'futureNoOfUnits'                => fieldMap::create(Database2::DATATYPE_NUMBER),

            'propertyOwnerOccupancy' => fieldMap::create(Database2::DATATYPE_MONEY),

            'propertyCommercialTenantOccupancyRate'  => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyCRE'                            => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyRESI'                           => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyNumberOfStories'                => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyTypeOfView'                     => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyTypeOfFrontage'                 => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyFutureNumberOfBedRooms'         => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyFutureNumberOfBathRooms'        => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyFutureNumberOfHalfBathRooms'    => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyConstructionOfPercentage'       => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyLeaseValidation'                => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyCurrentlyLeased'                => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyMarketRents'                    => fieldMap::create(Database2::DATATYPE_MONEY),


            /* Property Analysis Fields */
            'propertyAnnualHazardInsurance'          => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyAnnualFloodInsurance'           => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyTotalHazardFloodInsurance'      => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyTaxSource'                      => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyAnnualHOAFees'                  => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyTotalExpenses'                  => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyTotalExistingLienPayoffs'       => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyGrossOperatingIncome'           => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyNetOperatingIncome'             => fieldMap::create(Database2::DATATYPE_MONEY),
            'property30YearAmortizationPayment'      => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyInterrestOnlyPayment'           => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyPITIA'                          => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyITIA'                           => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyMetrics'                        => fieldMap::create(Database2::DATATYPE_NUMBER),
            'propertyRequiredValuationMethods'       => fieldMap::create(Database2::DATATYPE_MULTI_SELECT),
            'propertyAllocatedLTA'                   => fieldMap::create(Database2::DATATYPE_MONEY),
            'property30YearAMDSCR'                   => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyQualifyingAllocatedLTV'         => fieldMap::create(Database2::DATATYPE_MONEY),


            /* Rent Roll Fields */
            'tenantName'                             => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(55) default NULL'),
            'unitNum'                                => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(16) default NULL'),
            'sqFt'                                   => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(55) default NULL'),
            'rentRollBedroom'                        => fieldMap::create(Database2::DATATYPE_NUMBER, 'varchar(55) default NULL'),
            'rentRollBath'                           => fieldMap::create(Database2::DATATYPE_NUMBER, 'varchar(55) default NULL'),
            'leaseStartDate'                         => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'leaseEndDate'                           => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'rentRollLastRentIncreaseDate'           => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'rentRollMoveInDate'                     => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'rentRollMonthlyRent'                    => fieldMap::create(Database2::DATATYPE_MONEY, 'DATE default NULL'),
            'rentRollLeasePaymentFrequency'          => fieldMap::create(Database2::DATATYPE_NUMBER, 'DATE default NULL'),
            'rentRollNumberOfHalfBaths'              => fieldMap::create(Database2::DATATYPE_NUMBER, 'DATE default NULL'),
            'rentRollLeaseExtensionEndDate'          => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'rentRollPreliminaryEstimatedRents'      => fieldMap::create(Database2::DATATYPE_MONEY),
            'rentRollPreliminaryRentSource'          => fieldMap::create(Database2::DATATYPE_NUMBER),
            'rentRollMarketRents'                    => fieldMap::create(Database2::DATATYPE_MONEY),
            'rentRoll120MarketRents'                 => fieldMap::create(Database2::DATATYPE_MONEY),
            'rentRollMarketRentSource'               => fieldMap::create(Database2::DATATYPE_NUMBER),
            'rentRollActualRents'                    => fieldMap::create(Database2::DATATYPE_MONEY),
            'rentRollLeaseValidation'                => fieldMap::create(Database2::DATATYPE_NUMBER),


            /* Flood Certificate Fields */
            'propertyIsInSpecialFloodHazardZone'     => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(8) default NULL'),
            'propertyFloodZone'                      => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(8) default NULL'),
            'propertyFloodCertificateCompany'        => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),
            'propertyDateOfDetermination'            => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'propertyNFIPNumberCommunityPanelNumber' => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),
            'propertyNFIPMapPanelDate'               => fieldMap::create(Database2::DATATYPE_DATE, 'DATE default NULL'),
            'propertyFloodCertificateNumber'         => fieldMap::create(Database2::DATATYPE_STRING, 'varchar(32) default NULL'),
            'propertyFloodNotes'                     => fieldMap::create(Database2::DATATYPE_STRING, 'text default NULL'),


            /* Appraiser Fields */

            'propertyAppraiserCompany'   => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraiserFirstName' => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraiserLastName'  => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraiserEmail'     => fieldMap::create(Database2::DATATYPE_EMAIL),
            // 'propertyAppraiserPhone' => fieldMap::create(Database2::DATATYPE_NUMBER),

            'propertyAppraiserUpload'                                => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraisalAsIsValue'                             => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyAppraisalRehabbedValue'                         => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyAppraisalMonthlyRent'                           => fieldMap::create(Database2::DATATYPE_MONEY),
            'propertyAppraisalJobTypes'                              => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraisalDateObtained'                          => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalRequestedReturnDate'                   => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalOrderDate'                             => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalComments'                              => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraisalEffectiveDate'                         => fieldMap::create(Database2::DATATYPE_DATE),
            'primaryAppraisalEcoaDeliveryDate'                       => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalInspectionDate'                        => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalExpectedDeliveryDate'                  => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalExpectedDeliveryDelay'                 => fieldMap::create(Database2::DATATYPE_NUMBER),

            /* Supplemental Product */
            'propertyAppraisalSupplementalProductFormType1'          => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraisalSupplementalProductEffectiveDate1'     => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalSupplementalProductEcoaADeliveryDate1' => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalSupplementalProductFormType2'          => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraisalSupplementalProductEffectiveDate2'     => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalSupplementalProductEcoaADeliveryDate2' => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalSupplementalProductFormType3'          => fieldMap::create(Database2::DATATYPE_STRING),
            'propertyAppraisalSupplementalProductEffectiveDate3'     => fieldMap::create(Database2::DATATYPE_DATE),
            'propertyAppraisalSupplementalProductEcoaADeliveryDate3' => fieldMap::create(Database2::DATATYPE_DATE),

            /* HOA Fields */
            'monthlyHOAFees'                                         => fieldMap::create(Database2::DATATYPE_MONEY),
            'delinquencyTotal'                                       => fieldMap::create(Database2::DATATYPE_MONEY),
            'primaryHOAContactName'                                  => fieldMap::create(Database2::DATATYPE_STRING),
            'primaryHOACompanyName'                                  => fieldMap::create(Database2::DATATYPE_STRING),
            'primaryHOAEmail'                                        => fieldMap::create(Database2::DATATYPE_EMAIL),
            'primaryHOAAddress'                                      => fieldMap::create(Database2::DATATYPE_STRING),
            'primaryHOACity'                                         => fieldMap::create(Database2::DATATYPE_STRING),
            'primaryHOAState'                                        => fieldMap::create(Database2::DATATYPE_STATE),
            'primaryHOAZip'                                          => fieldMap::create(Database2::DATATYPE_ZIP),
            'primaryHOAContactPhone'                                 => fieldMap::create(Database2::DATATYPE_NUMBER),
            'primaryHOAFax'                                          => fieldMap::create(Database2::DATATYPE_NUMBER),
            'primaryHOANotes'                                        => fieldMap::create(Database2::DATATYPE_STRING),

            'secondaryHOAAmount'      => fieldMap::create(Database2::DATATYPE_MONEY),
            'secondaryHOAContactName' => fieldMap::create(Database2::DATATYPE_STRING),
            'secondaryHOACompanyName' => fieldMap::create(Database2::DATATYPE_STRING),
            'secondaryHOAEmail'       => fieldMap::create(Database2::DATATYPE_EMAIL),
            'secondaryHOAAddress'     => fieldMap::create(Database2::DATATYPE_STRING),
            'secondaryHOACity'        => fieldMap::create(Database2::DATATYPE_STRING),
            'secondaryHOAState'       => fieldMap::create(Database2::DATATYPE_STATE),
            'secondaryHOAZipCode'     => fieldMap::create(Database2::DATATYPE_ZIP),
            'secondaryHOANotes'       => fieldMap::create(Database2::DATATYPE_STRING),
        ];

        self::$fieldLabels = [
            'propertyAddress' => 'Address',
            'propertyUnit'    => 'Unit#',
            'propertyCity'    => 'City',
            'propertyState'   => 'State',
            'propertyCounty'  => 'County',
            'propertyZipCode' => 'Zip Code',
            'propertyType'    => 'Property Type',
        ];
        self::$fieldMigrations = [
            'propertyAddress'  => 'propertyAddress',
            'propertyUnit'     => 'propertyUnit',
            'propertyCity'     => 'propertyCity',
            'propertyState'    => 'propertyState',
            'propertyZip'      => 'propertyZipCode',
            'propertyCountry'  => 'propertyCountry',
            'propertyCounty'   => 'propertyCounty',
            'propertyLocation' => 'propertyLocation',
            'municipality'     => 'propertyMunicipality',
            'parcelNo'         => 'propertyParcelNumber',
            'legalDescription' => 'propertyLegalDescription',
            'district'         => 'propertyDistrict',
            'section'          => 'propertySection',
            'block'            => 'propertyBlock',
            'lot'              => 'propertyLot',
            'msa'              => 'propertyMSA',
            'realestateapi_id' => 'realestateapi_id',

            'propertyType'                         => 'propertyType',
            'condoEligibility'                     => 'propertyCondoEligibility',
            'propertySqFt'                         => 'propertySqFt',
            'acres'                                => 'propertyAcres',
            'yearBuilt'                            => 'propertyYearBuilt',
            'propertyFeatures'                     => 'propertyFeatures',
            'adjustedSqFt'                         => 'propertyAdjustedSqFt',
            'waterFront'                           => 'propertyWaterFront',
            'propConstructionType'                 => 'propertyConstructionType',
            'propConstructionMethod'               => 'propertyConstructionMethod',
            'yearPurchased'                        => 'propertyYearPurchased',
            'howManyRoom'                          => 'propertyNumberOfRooms',
            'howManyBedRoom'                       => 'propertyNumberOfBedRooms',
            'howManyBathRoom'                      => 'propertyNumberOfBathRooms',
            'howManyHalfBathRoom'                  => 'propertyNumberOfHalfBathRooms',
            'basementHome'                         => 'propertyIsHomeHaveBasement',
            'basementFinish'                       => 'propertyIsBasementFinished',
            'garageHome'                           => 'propertyIsHomeHaveGarage',
            'addRentableSqFt'                      => 'propertyRentableSqFt',
            'noOfParking'                          => 'propertyNumberOfParkings',
            'noOfBuildings'                        => 'propertyNumberOfBuildings',
            'stabilizedRate'                       => 'propertyStabilizedRate',
            'noUnitsOccupied'                      => 'propertyNumberOfUnits',
            'propertyValue'                        => 'propertyValue',
            'noOfParcels'                          => 'propertyNumberOfParcels',
            'ownerOccupancyPercentage'             => 'propertyOwnerOccupancy',
            'currentCommercialTenantOccupancyRate' => 'propertyCommercialTenantOccupancyRate',
            'anchorTenant'                         => 'propertyIsAnchorTenant',
            'anchorTenantName'                     => 'propertyAnchorTenantName',
            'cre'                                  => 'propertyCRE',
            'resi'                                 => 'propertyRESI',
            'propertyClass'                        => 'propertyClass',
            'addNoOfStories'                       => 'propertyNumberOfStories',
            'leaseType'                            => 'propertyLeaseType',

            'estimatedPropertyValue'      => 'propertyEstimatedValue',
            'annualPropertyTaxes'         => 'annualPropertyTaxes',
            'zillowValue'                 => 'propertyZillowValue',
            'pricePerDoor'                => 'propertyPricePerDoor',
            'propertyURLLink1'            => 'propertyURLLink1',
            'propertyURLLink2'            => 'propertyURLLink2',
            'presentOccupancy'            => 'propertyPresentOccupancy',
            'occupancyNotes'              => 'propertyOccupancyNotes',
            'propertyCondition'           => 'propertyCondition',
            'conditionNotes'              => 'propertyConditionNotes',
            'yearRenovated'               => 'propertyRenovatedYear',
            // 'occupancy' => 'propertypresentOccupancy',
            'taxYear'                     => 'propertyTaxYear',
            //   'taxes1' => 'taxes1',
            //   'annualPropTaxes1' => 'annualPropTaxes1',
            'propertyTaxDueDate'          => 'propertyTaxDueDate',
            'pastDuePropertyTaxes'        => 'propertyPastDuePropertyTaxes',
            'propertyPayoffAmt'           => 'propertyPayOffAmount',
            'allocatedLoanAmount'         => 'propertyAllocatedLoanAmount',
            'HMLOmlsnumber'               => 'propertyMLSNumber',
            'propAnnualInsurancePremiums' => 'propertyAnnualInsurancePremiums',
            'rehabToComplete'             => 'propertyRehabToComplete',

            'LBContactName'        => 'propertyAccessName',
            'LBContactEmail'       => 'propertyAccessEmail',
            'LBInfo'               => 'propertyAccessLockBoxInfo',
            'LBContactPhone'       => 'propertyAccessPhone',
            'obtainInteriorAccess' => 'propertyAccessObtainInteriorAccess',
            'propertyRequiredValuationMethods' => 'propertyRequiredValuationMethods',

        ];

        self::$fieldValidation = [
            'propertyAddress' => [
                'required' => false,
                'type'     => 'string',
                'min'      => 1,
                'max'      => 16,
            ],
            'propertyCity'    => [
                'required' => false,
                'type'     => 'string',
            ],
            'propertyState'   => [
                'required' => false,
                'type'     => 'string',
            ],
            'propertyType'    => [
                'required' => true,
                /* 'type' => 'multiple',
                 'min' => 1,
                 'max' => 3,*/
                // 'msg' => 'Minimum 1 and Maximum 3 ' . self::$fieldLabels['propertyType'] . ' is Mandatory',
            ],
        ];
    }

// (!(self::$fieldValidation[$fldName]['msg'] ?: '')) ?? ' At-least One Parameter is Mandatory ')

    /**
     * @param $value
     * @param $fldName
     * @param $error
     * @return string|null
     */
    public static function checkMandatoryFieldValidation($value, $fldName, $error = null): ?string
    {
        if (self::$fieldValidation[$fldName]['required']) {

            if (self::$fieldValidation[$fldName]['type'] == 'multiple') {
                if (!count($value)) {
                    $error = (self::$fieldValidation[$fldName]['msg'] ?: ' At-least One Parameter is Mandatory ');
                }
            } else {
                if (!$value) {
                    $error = ' Mandatory Field ';
                }
            }
        }
        return $error;
    }


    /**
     * @param string $fieldKey
     * @param $fieldValue
     * @param array $msg
     * @param bool $validate
     * @return false|float|int|mixed|string|null
     */
    public static function sanitizePropertyFields(string $fieldKey, $fieldValue, array &$msg, bool $validate = true)
    {
        $error = '';
        if (!is_array($fieldValue)) {
            $fieldValue = stripslashes($fieldValue);
            if (strcasecmp($fieldValue, 'NULL') == 0 || $fieldValue == NULL) {
                $fieldValue = '';
            }
            $fieldValue = trim($fieldValue);
        }
        $error = ($validate) ? self::checkMandatoryFieldValidation($fieldValue, $fieldKey, $error) : '';

        $dataType = isset(self::$fieldMap[$fieldKey]) ? self::$fieldMap[$fieldKey]->dataType : null;
        $fieldValue = Database2::strongTypeValue($fieldValue, $dataType, null, $error);
        $fieldLabel = self::$fieldLabels[$fieldKey] ?? null;
        if ($error) {
            $msg[] = $fieldLabel . ' has error - ' . $error;
        }
        return $fieldValue;
    }

    public static function getExistingProperty(bool $isPrimary, int $LMRId, ?int $propertyId = 0): ?tblProperties
    {

        if (!$propertyId) {
            $item = new tblProperties();
        } else {
            $item = tblProperties::Get(['LMRId' => $LMRId, 'propertyId' => $propertyId]);
            if (!$item) {
                $item = new tblProperties();
            }
        }
        $item->isPrimary = $isPrimary;
        $item->LMRId = $LMRId;

        return $item;
    }


    /**
     * @return int|null
     */
    public static function save(): ?int
    {
        if (!self::$LMRId) {
            return null;
        }
        LMRequest::setLMRId(self::$LMRId);

        $item = self::getExistingProperty(
            (bool)(self::$postValues['isPrimary'] ?? 0),
            self::$LMRId,
            self::$postValues['propertyId'] ?: null
        );
        $item->propertyAddress = (self::$postValues['propertyAddress'] ?? $item->propertyAddress);
        $item->propertyUnit = (self::$postValues['propertyUnit'] ?? $item->propertyUnit);
        $item->propertyCity = (self::$postValues['propertyCity'] ?? $item->propertyCity);
        $item->propertyState = (self::$postValues['propertyState'] ?? $item->propertyState);
        $item->propertyCounty = (self::$postValues['propertyCounty'] ?? $item->propertyCounty);
        $item->propertyZipCode = (self::$postValues['propertyZipCode'] ?? $item->propertyZipCode);
        $item->propertyCountry = (self::$postValues['propertyCountry'] ?? $item->propertyCountry);
        $item->propertyLocation = (self::$postValues['propertyLocation'] ?? $item->propertyLocation);
        $item->propertyMunicipality = (self::$postValues['propertyMunicipality'] ?? $item->propertyMunicipality);
        $item->propertyParcelNumber = (self::$postValues['propertyParcelNumber'] ?? $item->propertyParcelNumber);
        $item->propertyLegalDescription = (self::$postValues['propertyLegalDescription'] ?? $item->propertyLegalDescription);
        $item->propertyDistrict = (self::$postValues['propertyDistrict'] ?? $item->propertyDistrict);
        $item->propertySection = (self::$postValues['propertySection'] ?? $item->propertySection);
        $item->propertyBlock = (self::$postValues['propertyBlock'] ?? $item->propertyBlock);
        $item->propertyLot = (self::$postValues['propertyLot'] ?? $item->propertyLot);
        $item->propertyMSA = (self::$postValues['propertyMSA'] ?? $item->propertyMSA);
        $item->realestateapi_id = (int)(self::$postValues['realestateapi_id'] ?? $item->realestateapi_id);

        if (self::$postValues['isPrimary']) {
            self::removePrimaryStatusToProperties(self::$LMRId);
        }

        if ($item->propertyId) {
            $item->updatedDate = Dates::Timestamp();
            $item->updatedGroup = $item->createdGroup;
            $item->updatedBy = $item->createdBy;
        } else {
            $item->createdDate = Dates::Timestamp();
            $item->createdGroup = self::$userGroup;
            $item->createdBy = self::$userNumber;
        }

//        Debug($item, self::$postValues, $_REQUEST);

        $item->Save();

        if ($item->propertyId) {
            self::$propertyId = $item->propertyId;
            self::saveCharacteristics();
            self::saveAnalysis();
            self::saveDetails();
            self::saveAccess();

            self::$propertyRentRoll = self::$postValues['rentRoll'];
            self::saveRentRoll();

            if (self::$postValues['floodCertificates']) {
                self::$propertiesFloodCertificatesInserted = self::saveFloodCertificates(self::$propertyId, self::$postValues['floodCertificates']);
            }
            if (self::$postValues['appraiser']) {
                self::$propertiesAppraisersInserted = self::savePropertyAppraisers(
                    LMRequest::File()->FPCID,
                    self::$propertyId,
                    self::$LMRId,
                    self::$postValues['appraiser'],
                    LMRequest::File()->recordDate,
                    self::$propertyIndex,
                );
            }
            self::$propertiesHOAInserted = self::saveHOA(Property::$propertyIndex);
        }

        /*if ($item->isPrimary) {
            $LoanServicer = PaymentInfo::setLoanServicer();
            $tblACHInfo = tblACHInfo::Get(['LMRID' => self::$LMRId]);
            if (!$tblACHInfo) {
                $tblACHInfo = new tblACHInfo();
                $tblACHInfo->LMRID = self::$LMRId;
                $tblACHInfo->recordDate = Dates::Timestamp();
            }
            $tblACHInfo->ACTLoanServicer = $LoanServicer;
            $tblACHInfo->Save();
        }*/
        return $item->propertyId;
    }


    /**
     * @return void
     */
    public static function saveCharacteristics()
    {
        if (!self::$propertyId) {
            return;
        }
        $item = tblPropertiesCharacteristics::Get([
            'propertyId' => self::$propertyId,
        ]);
        if (!$item) {
            $item = new tblPropertiesCharacteristics();
        }
        $item->propertyId = self::$propertyId;
        $item->propertyType = intval(self::$postValues['propertyType'] ?? $item->propertyType);
        $item->propertyCondoEligibility = (self::$postValues['propertyCondoEligibility'] ?? $item->propertyCondoEligibility);
        $item->propertySqFt = (self::$postValues['propertySqFt'] ?? $item->propertySqFt);
        $item->propertyAcres = (self::$postValues['propertyAcres'] ?? $item->propertyAcres);
        $item->propertyYearBuilt = (self::$postValues['propertyYearBuilt'] ?? $item->propertyYearBuilt);
        $item->propertyFeatures = (self::$postValues['propertyFeatures'] ?? $item->propertyFeatures);
        $item->propertyAdjustedSqFt = (self::$postValues['propertyAdjustedSqFt'] ?? $item->propertyAdjustedSqFt);
        $item->propertyWaterFront = (self::$postValues['propertyWaterFront'] ?? $item->propertyWaterFront);
        $item->propertyConstructionType = (self::$postValues['propertyConstructionType'] ?? $item->propertyConstructionType);
        $item->propertyConstructionMethod = (self::$postValues['propertyConstructionMethod'] ?? $item->propertyConstructionMethod);
        $item->propertyYearPurchased = (self::$postValues['propertyYearPurchased'] ?? $item->propertyYearPurchased);
        $item->propertyNumberOfRooms = (self::$postValues['propertyNumberOfRooms'] ?? $item->propertyNumberOfRooms);
        $item->propertyNumberOfBedRooms = (self::$postValues['propertyNumberOfBedRooms'] ?? $item->propertyNumberOfBedRooms);
        $item->propertyNumberOfBathRooms = (self::$postValues['propertyNumberOfBathRooms'] ?? $item->propertyNumberOfBathRooms);
        $item->propertyNumberOfHalfBathRooms = (self::$postValues['propertyNumberOfHalfBathRooms'] ?? $item->propertyNumberOfHalfBathRooms);
        $item->propertyIsHomeHaveBasement = (self::$postValues['propertyIsHomeHaveBasement'] ?? $item->propertyIsHomeHaveBasement);
        $item->propertyIsBasementFinished = (self::$postValues['propertyIsBasementFinished'] ?? $item->propertyIsBasementFinished);
        $item->propertyIsHomeHaveGarage = (self::$postValues['propertyIsHomeHaveGarage'] ?? $item->propertyIsHomeHaveGarage);
        $item->propertyRentableSqFt = (self::$postValues['propertyRentableSqFt'] ?? $item->propertyRentableSqFt);
        $item->propertyNumberOfParkings = (self::$postValues['propertyNumberOfParkings'] ?? $item->propertyNumberOfParkings);
        $item->propertyNumberOfBuildings = (self::$postValues['propertyNumberOfBuildings'] ?? $item->propertyNumberOfBuildings);
        $item->propertyStabilizedRate = Strings::replaceCommaValues(self::$postValues['propertyStabilizedRate'] ?? $item->propertyStabilizedRate);
        $item->propertyNumberOfUnits = (self::$postValues['propertyNumberOfUnits'] ?? $item->propertyNumberOfUnits);
        $item->futureNoOfUnits = (self::$postValues['futureNoOfUnits'] ?? $item->futureNoOfUnits);
        $item->propertyValue = (self::$postValues['propertyValue'] ?? $item->propertyValue);
        $item->propertyNumberOfParcels = (self::$postValues['propertyNumberOfParcels'] ?? $item->propertyNumberOfParcels);
        $item->propertyOwnerOccupancy = (self::$postValues['propertyOwnerOccupancy'] ?? $item->propertyOwnerOccupancy);
        $item->propertyCommercialTenantOccupancyRate = (self::$postValues['propertyCommercialTenantOccupancyRate'] ?? $item->propertyCommercialTenantOccupancyRate);
        $item->propertyIsAnchorTenant = intval(self::$postValues['propertyIsAnchorTenant'] ?? $item->propertyIsAnchorTenant);
        $item->propertyAnchorTenantName = (self::$postValues['propertyAnchorTenantName'] ?? $item->propertyAnchorTenantName);
        $item->propertyCRE = (self::$postValues['propertyCRE'] ?? $item->propertyCRE);
        $item->propertyRESI = (self::$postValues['propertyRESI'] ?? $item->propertyRESI);
        $item->propertyClass = (self::$postValues['propertyClass'] ?? $item->propertyClass);
        $item->propertyNumberOfStories = (self::$postValues['propertyNumberOfStories'] ?? $item->propertyNumberOfStories);
        $item->propertyLeaseType = (self::$postValues['propertyLeaseType'] ?? $item->propertyLeaseType);
        $item->propertyIsValueAddedView = (self::$postValues['propertyIsValueAddedView'] ?? $item->propertyIsValueAddedView);
        $item->propertyTypeOfView = (self::$postValues['propertyTypeOfView'] ?? $item->propertyTypeOfView);
        $item->propertyTypeOfFrontage = (self::$postValues['propertyTypeOfFrontage'] ?? $item->propertyTypeOfFrontage);
        $item->propertyTypeOfFrontageOther = (self::$postValues['propertyTypeOfFrontageOther'] ?? $item->propertyTypeOfFrontageOther);
        $item->propertyIsPool = (self::$postValues['propertyIsPool'] ?? $item->propertyIsPool);
        $item->futurePropertyType = (self::$postValues['futurePropertyType'] ?? $item->futurePropertyType);
        $item->propertyFutureNumberOfBedRooms = (self::$postValues['propertyFutureNumberOfBedRooms'] ?? $item->propertyFutureNumberOfBedRooms);
        $item->propertyFutureNumberOfBathRooms = (self::$postValues['propertyFutureNumberOfBathRooms'] ?? $item->propertyFutureNumberOfBathRooms);
        $item->propertyFutureNumberOfHalfBathRooms = (self::$postValues['propertyFutureNumberOfHalfBathRooms'] ?? $item->propertyFutureNumberOfHalfBathRooms);
        $item->propertyIsMidConstruction = intval(self::$postValues['propertyIsMidConstruction'] ?? $item->propertyIsMidConstruction);
        $item->propertyConstructionOfPercentage = (self::$postValues['propertyConstructionOfPercentage'] ?? $item->propertyConstructionOfPercentage);
        $item->propertyLeaseValidation = (self::$postValues['propertyLeaseValidation'] ?? $item->propertyLeaseValidation);
        $item->propertyCurrentlyLeased = (self::$postValues['propertyCurrentlyLeased'] ?? $item->propertyCurrentlyLeased);
        $item->propertyLeaseValidationOther = (self::$postValues['propertyLeaseValidationOther'] ?? $item->propertyLeaseValidationOther);
        $item->propertyIsPlannedUnitDevelopment = intval(self::$postValues['propertyIsPlannedUnitDevelopment'] ?? $item->propertyIsPlannedUnitDevelopment);
        $item->propertyPUDName = (self::$postValues['propertyPUDName'] ?? $item->propertyPUDName);
        $item->propertyMarketRents = (self::$postValues['propertyMarketRents'] ?? $item->propertyMarketRents);
        $item->currentADU = intval(self::$postValues['currentADU'] ?? $item->currentADU);
        $item->futureADU = intval(self::$postValues['futureADU'] ?? $item->futureADU);

        if ($item->id) {
            $item->updatedDate = Dates::Timestamp();
            $item->updatedGroup = self::$userGroup;
            $item->updatedBy = self::$userNumber;
        } else {
            $item->createdDate = Dates::Timestamp();
            $item->createdGroup = self::$userGroup;
            $item->createdBy = self::$userNumber;
        }
        $item->save();

    }


    public static function saveAnalysis(): void
    {
        if (!self::$propertyId) {
            return;
        }
        $item = tblPropertiesAnalysis::Get([
            'propertyId' => self::$propertyId,
        ]);
        if (!$item) {
            $item = new tblPropertiesAnalysis();
        }
        $item->propertyId = self::$propertyId;

        $item->propertyAnnualHazardInsurance = (self::$postValues['propertyAnnualHazardInsurance'] ?? $item->propertyAnnualHazardInsurance);
        $item->propertyAnnualFloodInsurance = (self::$postValues['propertyAnnualFloodInsurance'] ?? $item->propertyAnnualFloodInsurance);
        $item->propertyTotalHazardFloodInsurance = (self::$postValues['propertyTotalHazardFloodInsurance'] ?? $item->propertyTotalHazardFloodInsurance);
        $item->propertyTaxSource = (self::$postValues['propertyTaxSource'] ?? $item->propertyTaxSource);
        $item->propertyAnnualHOAFees = (self::$postValues['propertyAnnualHOAFees'] ?? $item->propertyAnnualHOAFees);
        $item->propertyTotalExpenses = (self::$postValues['propertyTotalExpenses'] ?? $item->propertyTotalExpenses);
        $item->propertyTotalExistingLienPayoffs = (self::$postValues['propertyTotalExistingLienPayoffs'] ?? $item->propertyTotalExistingLienPayoffs);
        $item->propertyGrossOperatingIncome = (self::$postValues['propertyGrossOperatingIncome'] ?? $item->propertyGrossOperatingIncome);
        $item->propertyNetOperatingIncome = (self::$postValues['propertyNetOperatingIncome'] ?? $item->propertyNetOperatingIncome);
        $item->property30YearAmortizationPayment = (self::$postValues['property30YearAmortizationPayment'] ?? $item->property30YearAmortizationPayment);
        $item->propertyInterrestOnlyPayment = (self::$postValues['propertyInterrestOnlyPayment'] ?? $item->propertyInterrestOnlyPayment);
        $item->propertyPITIA = (self::$postValues['propertyPITIA'] ?? $item->propertyPITIA);
        $item->propertyITIA = (self::$postValues['propertyITIA'] ?? $item->propertyITIA);
        $item->propertyMetrics = (self::$postValues['propertyMetrics'] ?? $item->propertyMetrics);
        $item->propertyRequiredValuationMethods = (self::$postValues['propertyRequiredValuationMethods'] ?? $item->propertyRequiredValuationMethods);
        $item->propertyAllocatedLTA = (self::$postValues['propertyAllocatedLTA'] ?? $item->propertyAllocatedLTA);
        $item->property30YearAMDSCR = (self::$postValues['property30YearAMDSCR'] ?? $item->property30YearAMDSCR);
        $item->propertyQualifyingAllocatedLTV = (self::$postValues['propertyQualifyingAllocatedLTV'] ?? $item->propertyQualifyingAllocatedLTV);

        if ($item->id) {
            $item->updatedDate = Dates::Timestamp();
            $item->updatedGroup = self::$userGroup;
            $item->updatedBy = self::$userNumber;
        } else {
            $item->createdDate = Dates::Timestamp();
            $item->createdGroup = self::$userGroup;
            $item->createdBy = self::$userNumber;
        }
        $item->save();

    }


    /**
     * @return void
     */
    public static function saveDetails()
    {

        if (!self::$propertyId) {
            return;
        }
        $item = tblPropertiesDetails::Get([
            'propertyId' => self::$propertyId,
        ]);
        if (!$item) {
            $item = new tblPropertiesDetails();
        }
        $item->propertyId = self::$propertyId;

        $item->propertyEstimatedValue = (self::$postValues['propertyEstimatedValue'] ?? $item->propertyEstimatedValue);
        $item->annualPropertyTaxes = (self::$postValues['annualPropertyTaxes'] ?? $item->annualPropertyTaxes);
        $item->propertyZillowValue = (self::$postValues['propertyZillowValue'] ?? $item->propertyZillowValue);
        $item->propertyPricePerDoor = (self::$postValues['propertyPricePerDoor'] ?? $item->propertyPricePerDoor);
        $item->propertyURLLink1 = (self::$postValues['propertyURLLink1'] ?? $item->propertyURLLink1);
        $item->propertyURLLink2 = (self::$postValues['propertyURLLink2'] ?? $item->propertyURLLink2);
        $item->propertyPresentOccupancy = (self::$postValues['propertyPresentOccupancy'] ?? $item->propertyPresentOccupancy);
        $item->propertyOccupancyNotes = (self::$postValues['propertyOccupancyNotes'] ?? $item->propertyOccupancyNotes);
        $item->propertyCondition = (self::$postValues['propertyCondition'] ?? $item->propertyCondition);
        $item->propertyConditionNotes = (self::$postValues['propertyConditionNotes'] ?? $item->propertyConditionNotes);
        $item->propertyRenovatedYear = (self::$postValues['propertyRenovatedYear'] ?? $item->propertyRenovatedYear);
        $item->propertyTaxYear = (self::$postValues['propertyTaxYear'] ?? $item->propertyTaxYear);
        $item->propertyTaxDueDate = (self::$postValues['propertyTaxDueDate'] ?? $item->propertyTaxDueDate);
        $item->propertyPastDuePropertyTaxes = (self::$postValues['propertyPastDuePropertyTaxes'] ?? $item->propertyPastDuePropertyTaxes);
        $item->propertyPayOffAmount = (self::$postValues['propertyPayOffAmount'] ?? $item->propertyPayOffAmount);
        $item->propertyAllocatedLoanAmount = (self::$postValues['propertyAllocatedLoanAmount'] ?? $item->propertyAllocatedLoanAmount);
        $item->propertyMLSNumber = (self::$postValues['propertyMLSNumber'] ?? $item->propertyMLSNumber);
        $item->propertyAnnualInsurancePremiums = (self::$postValues['propertyAnnualInsurancePremiums'] ?? $item->propertyAnnualInsurancePremiums);
        $item->propertyRehabToComplete = (self::$postValues['propertyRehabToComplete'] ?? $item->propertyRehabToComplete);
        $item->propertyAcquisitionDate = (self::$postValues['propertyAcquisitionDate'] ?? $item->propertyAcquisitionDate);
        $item->propertyAfterRepairValue = (self::$postValues['propertyAfterRepairValue'] ?? $item->propertyAfterRepairValue);
        $item->propertyIsListedForSale = intval(self::$postValues['propertyIsListedForSale'] ?? $item->propertyIsListedForSale);
        $item->propertyListPrice = (self::$postValues['propertyListPrice'] ?? $item->propertyListPrice);
        $item->propertyListDate = (self::$postValues['propertyListDate'] ?? $item->propertyListDate);
        $item->propertyIsConstructionOrRehabComplete = intval(self::$postValues['propertyIsConstructionOrRehabComplete'] ?? $item->propertyIsConstructionOrRehabComplete);
        $item->propertyCostSpent = (self::$postValues['propertyCostSpent'] ?? $item->propertyCostSpent);
        $item->propertyPayOffLienRelease = (self::$postValues['propertyPayOffLienRelease'] ?? $item->propertyPayOffLienRelease);
        $item->propertyPurchasePrice = (self::$postValues['propertyPurchasePrice'] ?? $item->propertyPurchasePrice);
        $item->propertyLTV = (self::$postValues['propertyLTV'] ?? $item->propertyLTV);
        $item->propertyLoanToPurchasePrice = (self::$postValues['propertyLoanToPurchasePrice'] ?? $item->propertyLoanToPurchasePrice);
        $item->propertyLoanToAfterRepairValue = (self::$postValues['propertyLoanToAfterRepairValue'] ?? $item->propertyLoanToAfterRepairValue);

        if ($item->id) {
            $item->updatedDate = Dates::Timestamp();
            $item->updatedGroup = self::$userGroup;
            $item->updatedBy = self::$userNumber;
        } else {
            $item->createdDate = Dates::Timestamp();
            $item->createdGroup = self::$userGroup;
            $item->createdBy = self::$userNumber;
        }
        $item->save();

    }

    /**
     * @return void
     */
    public static function saveAccess()
    {
        if (!self::$propertyId) {
            return;
        }
        $item = tblPropertiesAccess::Get([
            'propertyId' => self::$propertyId,
        ]);
        if (!$item) {
            $item = new tblPropertiesAccess();
        }
        $item->propertyId = self::$propertyId;

        $item->propertyAccessName = (self::$postValues['propertyAccessName'] ?? $item->propertyAccessName);
        $item->propertyAccessEmail = (self::$postValues['propertyAccessEmail'] ?? $item->propertyAccessEmail);
        $item->propertyAccessPhone = (self::$postValues['propertyAccessPhone'] ?? $item->propertyAccessPhone);
        $item->propertyAccessLockBoxInfo = (self::$postValues['propertyAccessLockBoxInfo'] ?? $item->propertyAccessLockBoxInfo);
        $item->propertyAccessObtainInteriorAccess = intval(self::$postValues['propertyAccessObtainInteriorAccess'] ?? $item->propertyAccessObtainInteriorAccess);
        $item->propertyAccessRelationship = (self::$postValues['propertyAccessRelationship'] ?? $item->propertyAccessRelationship);
        $item->propertyAccessRelationshipOther = (self::$postValues['propertyAccessRelationshipOther'] ?? $item->propertyAccessRelationshipOther);

        if ($item->id) {
            $item->updatedDate = Dates::Timestamp();
            $item->updatedGroup = self::$userGroup;
            $item->updatedBy = self::$userNumber;
        } else {
            $item->createdDate = Dates::Timestamp();
            $item->createdGroup = self::$userGroup;
            $item->createdBy = self::$userNumber;
        }

        $item->save();

    }

    /**
     * @param $propertyKey
     * @return array|void
     */
    public static function saveHOA($propertyKey): array
    {
        if (!self::$propertyId) {
            return [];
        }
        $item = tblPropertiesHOA::Get([
            'propertyId' => self::$propertyId,
        ]);
        if (!$item) {
            $item = new tblPropertiesHOA();
        }
        $item->propertyId = self::$propertyId;

        $contactItem = tblContacts::Get(['CID' => self::$postValues['primaryHOAId']]);
        if (!$contactItem) {
            $contactItem = new tblContacts();
        }
        if (self::$postValues['primaryHOAEmail'] || $contactItem->email) {
            $contactItem->contactName = (self::$postValues['primaryHOAContactName'] ?? $contactItem->contactName);
            $contactItem->companyName = (self::$postValues['primaryHOACompanyName'] ?? $contactItem->companyName);
            $contactItem->email = (self::$postValues['primaryHOAEmail'] ?? $contactItem->email);
            $contactItem->address = (self::$postValues['primaryHOAAddress'] ?? $contactItem->address);
            $contactItem->city = (self::$postValues['primaryHOACity'] ?? $contactItem->city);
            $contactItem->state = (self::$postValues['primaryHOAState'] ?? $contactItem->state);
            $contactItem->zip = (self::$postValues['primaryHOAZip'] ?? $contactItem->zip);
            $contactItem->phone = (self::$postValues['primaryHOAContactPhone'] ?? $contactItem->phone);
            $contactItem->fax = (self::$postValues['primaryHOAFax'] ?? $contactItem->fax);
            $contactItem->CTypeID = 8;
            $contactItem->PCID = LMRequest::File()->FPCID;
            $contactItem->dummyID = 0;
            $contactItem->activeStatus = 1;
            $contactItem->save();
            $item->primaryHOAId = $contactItem->CID;
        } else {
            $item->primaryHOAId = null;
        }
        $item->isHOAAvailable = intval(self::$postValues['isHOAAvailable'] ?? $item->isHOAAvailable);
        $item->isCurrentHOAFeePaid = (self::$postValues['isCurrentHOAFeePaid'] ?? $item->isCurrentHOAFeePaid);
        $item->monthlyHOAFees = (self::$postValues['monthlyHOAFees'] ?? $item->monthlyHOAFees);
        $item->delinquencyTotal = (self::$postValues['delinquencyTotal'] ?? $item->delinquencyTotal);
        $item->isHOAAllowRentals = (self::$postValues['isHOAAllowRentals'] ?? $item->isHOAAllowRentals);
        $item->isHOAAllowAirbnb = (self::$postValues['isHOAAllowAirbnb'] ?? $item->isHOAAllowAirbnb);
        $item->isHOAAppliesToAllProperties = (self::$postValues['isHOAAppliesToAllProperties'] ?? $item->isHOAAppliesToAllProperties);
        $item->isHOACommunity55 = (self::$postValues['isHOACommunity55'] ?? $item->isHOACommunity55);
        $item->primaryHOANotes = (self::$postValues['primaryHOANotes'] ?? $item->primaryHOANotes);
        $item->isSecondaryHOAAvailable = (self::$postValues['isSecondaryHOAAvailable'] ?? $item->isSecondaryHOAAvailable);
        $item->secondaryHOAAmount = (self::$postValues['secondaryHOAAmount'] ?? $item->secondaryHOAAmount);

        $secondaryContactItem = tblContacts::Get(['CID' => self::$postValues['secondaryHOAId']]);
        if (!$secondaryContactItem) {
            $secondaryContactItem = new tblContacts();
        }
        if (self::$postValues['secondaryHOAEmail'] || $secondaryContactItem->email) {
            $secondaryContactItem->contactName = (self::$postValues['secondaryHOAContactName'] ?? $secondaryContactItem->contactName);
            $secondaryContactItem->companyName = (self::$postValues['secondaryHOACompanyName'] ?? $secondaryContactItem->companyName);
            $secondaryContactItem->email = (self::$postValues['secondaryHOAEmail'] ?? $secondaryContactItem->email);
            $secondaryContactItem->address = (self::$postValues['secondaryHOAAddress'] ?? $secondaryContactItem->address);
            $secondaryContactItem->city = (self::$postValues['secondaryHOACity'] ?? $secondaryContactItem->city);
            $secondaryContactItem->state = (self::$postValues['secondaryHOAState'] ?? $secondaryContactItem->state);
            $secondaryContactItem->zip = (self::$postValues['secondaryHOAZipCode'] ?? $secondaryContactItem->zip);

            $secondaryContactItem->CTypeID = 8;
            $secondaryContactItem->PCID = LMRequest::File()->FPCID;
            $secondaryContactItem->dummyID = 0;
            $secondaryContactItem->activeStatus = 1;
            $secondaryContactItem->Save();
            $item->secondaryHOAId = $secondaryContactItem->CID;
        } else {
            $item->secondaryHOAId = null;
        }
        $item->secondaryHOANotes = (self::$postValues['secondaryHOANotes'] ?? $item->secondaryHOANotes);

        if ($item->id) {
            $item->updatedDate = Dates::Timestamp();
            $item->updatedGroup = self::$userGroup;
            $item->updatedBy = self::$userNumber;
        } else {
            $item->createdDate = Dates::Timestamp();
            $item->createdGroup = self::$userGroup;
            $item->createdBy = self::$userNumber;
        }
        $item->Save();

        $result['properties']['HOA'][$propertyKey]['primaryHOAId'] = $item->primaryHOAId;
        $result['properties']['HOA'][$propertyKey]['secondaryHOAId'] = $item->secondaryHOAId;

        return $result;
    }


    /**
     * @return void
     */
    public static function saveRentRoll()
    {
        if (!sizeof(self::$propertyRentRoll ?? [])) {
            return;
        }
        self::$propertiesRentrollInserted = saveFileRentRoll::save(
            self::$propertyId,
            self::$LMRId,
            self::$propertyRentRoll,
            LMRequest::File()->recordDate,
            LMRequest::File()->FPCID,
            self::$propertyIndex,
        );
    }

    /**
     * @param string|null $tab
     * @param string|null $encodePropertyId
     * @return string|null
     */
    public static function getPropertyUrl(?string $tab, ?string $encodePropertyId): ?string
    {
        $parts = parse_url($_SERVER['REQUEST_URI']);
        parse_str($parts['query'], $query);

        if (stristr($parts['url'], 'LMRequest.php') !== false) {
            $query['tabOpt'] = $tab;
        }
        $query['propertyId'] = $encodePropertyId;

        return $parts['url'] . '?' . http_build_query($query);
    }


    /**
     * @param int $LMRId
     * @return void
     */
    public static function removePrimaryStatusToProperties(int $LMRId)
    {

        $properties = tblProperties::GetAll(['LMRId' => $LMRId, 'isPrimary' => 1]);
        foreach ($properties as $item) {
            $item->isPrimary = 0;
            $item->save();
        }

    }

    /**
     * @return array
     */
    public static function initSave(): array
    {

        self::initFieldValidation();
        foreach ($_REQUEST['properties'] ?? [] as $propertyIndex => $propertyArray) {
            $msg = [];
            $item = [];
            foreach ($propertyArray as $propertyKey => $propertyValue) {

                if (!is_array($propertyValue)) {
                    $propertyValue = stripslashes($propertyValue);
                }

                switch ($propertyKey) {
                    case 'floodCertificates':
                        if (is_array($propertyValue)) {
                            foreach ($propertyValue as $eachFloodCertificateIndex => $floodCertificateDetails) {
                                foreach ($floodCertificateDetails as $eachFloodCertificatekey => $floodCertificateVal) {
                                    $item['floodCertificates'][$eachFloodCertificateIndex][$eachFloodCertificatekey] = self::sanitizePropertyFields($eachFloodCertificatekey, $floodCertificateVal, $msg, false);
                                }
                            }
                        }
                        break;
                    case 'rentRoll':
                        if (is_array($propertyValue)) {
                            foreach ($propertyValue as $eachRentRollIndex => $rentRollDetails) {
                                foreach ($rentRollDetails as $eachRentRollKey => $rentRollVal) {
                                    $item['rentRoll'][$eachRentRollIndex][$eachRentRollKey] = self::sanitizePropertyFields($eachRentRollKey, $rentRollVal, $msg, false);
                                }
                            }
                        }
                        break;
                    case 'appraiser':
                        if (is_array($propertyValue)) {
                            foreach ($propertyValue as $eachAppraiserIndex => $appraiserDetails) {
                                foreach ($appraiserDetails as $eachAppraiserKey => $appraiserVal) {
                                    $appraiserVal = (is_array($appraiserVal) ? implode(',', $appraiserVal) : $appraiserVal);
                                    $item['appraiser'][$eachAppraiserIndex][$eachAppraiserKey] = self::sanitizePropertyFields($eachAppraiserKey, $appraiserVal, $msg, false);
                                }
                            }
                        }
                        break;
                    default:
                        if (self::$fieldMigrations[$propertyKey]) {
                            $propertyKey = self::$fieldMigrations[$propertyKey];
                        }
                        $item[$propertyKey] = self::sanitizePropertyFields($propertyKey, $propertyValue, $msg, false);
                }
            }
            ksort($item);
            self::$postValues = $item;
            self::$propertyIndex = $propertyIndex;
            $lastId = self::save();
            if ($lastId) {
                self::$propertiesInserted[$propertyIndex] = $lastId;
            }
        }

        loanPropertySummary::updateLoanInfoV2Values(self::$LMRId);
        return self::$propertiesInserted;
    }

    public static function saveFloodCertificates(int    $propertyId,
                                                 ?array $floodCertificates
    ): ?array
    {
        $result = [];

        foreach ($floodCertificates as $key => $eachCertificate) {
            if (!array_filter($eachCertificate)) {
                continue;
            }
            $item = tblPropertiesFloodCertificates::Get(['id' => $eachCertificate['id']]);
            if (!$item) {
                $item = new tblPropertiesFloodCertificates();
            }
            $item->propertyId = $propertyId;
            $item->propertyIsInSpecialFloodHazardZone = ($eachCertificate['propertyIsInSpecialFloodHazardZone'] ?? $item->propertyIsInSpecialFloodHazardZone);
            $item->propertyFloodZone = ($eachCertificate['propertyFloodZone'] ?? $item->propertyFloodZone);
            $item->propertyFloodCertificateCompany = ($eachCertificate['propertyFloodCertificateCompany'] ?? $item->propertyFloodCertificateCompany);
            if (array_key_exists('propertyDateOfDetermination', $eachCertificate)){
                $item->propertyDateOfDetermination = $eachCertificate['propertyDateOfDetermination'];
            }
            $item->propertyNFIPNumberCommunityPanelNumber = ($eachCertificate['propertyNFIPNumberCommunityPanelNumber'] ?? $item->propertyNFIPNumberCommunityPanelNumber);
            if (array_key_exists('propertyNFIPMapPanelDate', $eachCertificate)){
                $item->propertyNFIPMapPanelDate = $eachCertificate['propertyNFIPMapPanelDate'];
            }
            $item->propertyFloodCertificateNumber = ($eachCertificate['propertyFloodCertificateNumber'] ?? $item->propertyFloodCertificateNumber);
            $item->propertyFloodNotes = ($eachCertificate['propertyFloodNotes'] ?? $item->propertyFloodNotes);
            $item->propertyIsNFIPNonParticipating = intval($eachCertificate['propertyIsNFIPNonParticipating']) ?? $item->propertyIsNFIPNonParticipating;

            if (!$item->id) {
                $item->createdBy = self::$userNumber;
                $item->createdGroup = self::$userGroup;
                $item->createdDate = Dates::Timestamp();
            } else {
                $item->updatedBy = self::$userNumber;
                $item->updatedGroup = self::$userGroup;
                $item->updatedDate = Dates::Timestamp();
            }
            $item->save();
            $result['properties'][self::$propertyIndex]['floodCertificates'][$key]['id'] = $item->id;
            $result['properties'][self::$propertyIndex]['floodCertificates'][$key]['propertyId'] = $propertyId;
        }
        return $result;

    }

    public static function savePropertyAppraisers(int     $PCID,
                                                  int     $propertyId,
                                                  int     $LMRId,
                                                  ?array  $appraisers,
                                                  ?string $recordDate,
                                                  int     $propertyIndex
    ): ?array
    {
        $result = [];


        foreach ($appraisers as $key => $eachAppraiser) {
            if (!array_filter($eachAppraiser)) {
                continue;
            }
            $item = tblPropertiesAppraiserDetails::Get(['id' => $eachAppraiser['id']]);
            if (!$item) {
                $item = new tblPropertiesAppraiserDetails();
            }
            $item->propertyId = $propertyId;

            $contactItem = tblContacts::Get(['CID' => $eachAppraiser['appraiserId']]);
            if (!$contactItem) {
                $contactItem = new tblContacts();
            }
            if ($eachAppraiser['propertyAppraiserEmail'] || $contactItem->email
            || ($eachAppraiser['propertyAppraiserFirstName'] || $contactItem->contactName)
            || ($eachAppraiser['propertyAppraiserLastName'] || $contactItem->contactLName)
            || ($eachAppraiser['propertyAppraiserCompany'] || $contactItem->companyName)
            ) {
                $contactItem->companyName = ($eachAppraiser['propertyAppraiserCompany'] ?? $contactItem->companyName);
                $contactItem->email = ($eachAppraiser['propertyAppraiserEmail'] ?? $contactItem->email);
                $contactItem->contactName = ($eachAppraiser['propertyAppraiserFirstName'] ?? $contactItem->contactName);
                $contactItem->contactLName = ($eachAppraiser['propertyAppraiserLastName'] ?? $contactItem->contactLName);
                $contactItem->phone = ($eachAppraiser['propertyAppraiserPhone'] ?? $contactItem->phone);
                $contactItem->CTypeID = 4;
                $contactItem->PCID = $PCID;
                $contactItem->dummyID = 0;
                $contactItem->activeStatus = 1;
                $contactItem->save();
                $item->appraiserId = $contactItem->CID;

                $fileContact = tblFileContacts::Get(['CID' => $item->appraiserId, 'fileID' => $LMRId]);
                if (!$fileContact) {
                    $fileContact = new tblFileContacts();
                }
                $fileContact->fileID = $LMRId;
                $fileContact->CID = $item->appraiserId;
                $fileContact->cRole = 'Appraiser';
                $fileContact->Save();
            } else {
                $item->appraiserId = null;
            }
            //$item->propertyAppraiserUpload = ($eachAppraiser['propertyAppraiserUpload'] ?? $item->propertyAppraiserUpload);
            $item->propertyAppraisalAsIsValue = ($eachAppraiser['propertyAppraisalAsIsValue'] ?? $item->propertyAppraisalAsIsValue);
            $item->propertyAppraisalRehabbedValue = ($eachAppraiser['propertyAppraisalRehabbedValue'] ?? $item->propertyAppraisalRehabbedValue);
            $item->propertyAppraisalMonthlyRent = ($eachAppraiser['propertyAppraisalMonthlyRent'] ?? $item->propertyAppraisalMonthlyRent);
            $item->propertyAppraisalJobTypes = ($eachAppraiser['propertyAppraisalJobTypes'] ?? $item->propertyAppraisalJobTypes);
            $item->propertyAppraisalDateObtained = ($eachAppraiser['propertyAppraisalDateObtained'] ?? $item->propertyAppraisalDateObtained);
            $item->propertyAppraisalStatus = intval(($eachAppraiser['propertyAppraisalStatus'] ?? $item->propertyAppraisalStatus)) ?? null;
            $item->propertyAppraisalRequestedReturnDate = ($eachAppraiser['propertyAppraisalRequestedReturnDate'] ?? $item->propertyAppraisalRequestedReturnDate);
            $item->propertyAppraisalIsRushOrder = ($eachAppraiser['propertyAppraisalIsRushOrder'] ?? $item->propertyAppraisalIsRushOrder);
            $item->propertyAppraisalOrderDate = ($eachAppraiser['propertyAppraisalOrderDate'] ?? $item->propertyAppraisalOrderDate);
            $item->propertyAppraisalComments = ($eachAppraiser['propertyAppraisalComments'] ?? $item->propertyAppraisalComments);
            $item->propertyAppraisalEffectiveDate = ($eachAppraiser['propertyAppraisalEffectiveDate'] ?? $item->propertyAppraisalEffectiveDate);
            $item->propertyAppraisalInspectionDate = ($eachAppraiser['propertyAppraisalInspectionDate'] ?? $item->propertyAppraisalInspectionDate);
            $item->propertyAppraisalExpectedDeliveryDate = ($eachAppraiser['propertyAppraisalExpectedDeliveryDate'] ?? $item->propertyAppraisalExpectedDeliveryDate);
            $item->primaryAppraisalEcoaDeliveryDate = ($eachAppraiser['primaryAppraisalEcoaDeliveryDate'] ?? $item->primaryAppraisalEcoaDeliveryDate);
            $item->propertyAppraisalExpectedDeliveryDelay = ($eachAppraiser['propertyAppraisalExpectedDeliveryDelay'] ?? $item->propertyAppraisalExpectedDeliveryDelay);
            //Supplemental Product Fields
            $item->propertyAppraisalSupplementalProductFormType1 = ($eachAppraiser['propertyAppraisalSupplementalProductFormType1'] ?? $item->propertyAppraisalSupplementalProductFormType1);
            $item->propertyAppraisalSupplementalProductEffectiveDate1 = ($eachAppraiser['propertyAppraisalSupplementalProductEffectiveDate1'] ?? $item->propertyAppraisalSupplementalProductEffectiveDate1);
            $item->propertyAppraisalSupplementalProductEcoaADeliveryDate1 = ($eachAppraiser['propertyAppraisalSupplementalProductEcoaADeliveryDate1'] ?? $item->propertyAppraisalSupplementalProductEcoaADeliveryDate1);
            $item->propertyAppraisalSupplementalProductFormType2 = ($eachAppraiser['propertyAppraisalSupplementalProductFormType2'] ?? $item->propertyAppraisalSupplementalProductFormType2);
            $item->propertyAppraisalSupplementalProductEffectiveDate2 = ($eachAppraiser['propertyAppraisalSupplementalProductEffectiveDate2'] ?? $item->propertyAppraisalSupplementalProductEffectiveDate2);
            $item->propertyAppraisalSupplementalProductEcoaADeliveryDate2 = ($eachAppraiser['propertyAppraisalSupplementalProductEcoaADeliveryDate2'] ?? $item->propertyAppraisalSupplementalProductEcoaADeliveryDate2);
            $item->propertyAppraisalSupplementalProductFormType3 = ($eachAppraiser['propertyAppraisalSupplementalProductFormType3'] ?? $item->propertyAppraisalSupplementalProductFormType3);
            $item->propertyAppraisalSupplementalProductEffectiveDate3 = ($eachAppraiser['propertyAppraisalSupplementalProductEffectiveDate3'] ?? $item->propertyAppraisalSupplementalProductEffectiveDate3);
            $item->propertyAppraisalSupplementalProductEcoaADeliveryDate3 = ($eachAppraiser['propertyAppraisalSupplementalProductEcoaADeliveryDate3'] ?? $item->propertyAppraisalSupplementalProductEcoaADeliveryDate3);
            //Supplemental Product Fields
            $item->propertyAppraiserUpload = (self::getFileUpload($propertyId, $LMRId, $propertyIndex, $key, $recordDate, $PCID)) ?? $eachAppraiser['propertyAppraiserUploaded'];

            if (!$item->id) {
                $item->createdBy = Property::$userNumber;
                $item->createdGroup = Property::$userGroup;
                $item->createdDate = Dates::Timestamp();
            } else {
                $item->updatedBy = Property::$userNumber;
                $item->updatedGroup = Property::$userGroup;
                $item->updatedDate = Dates::Timestamp();
            }
            $item->Save();
            $result['properties'][Property::$propertyIndex]['appraiser'][$key]['id'] = $item->id;
            $result['properties'][Property::$propertyIndex]['appraiser'][$key]['appraiserId'] = $item->appraiserId;
            $result['properties'][Property::$propertyIndex]['appraiser'][$key]['propertyId'] = $propertyId;
        }
        return $result;
    }


    public static function getFileUpload(int    $propertyId,
                                         int    $LMRId,
                                         int    $propertyIndex,
                                         int    $key,
                                         string $recordDate,
                                         string $PCID): ?string
    {
        $fileName = null;
        if ($_FILES['properties']['size'][$propertyIndex]['appraiser'][$key]['propertyAppraiserUpload'] > 0) {

            $fileName = $propertyId . '_' . Strings::removeDisAllowedChars(Strings::stripQuote(
                    $_FILES['properties']['name'][$propertyIndex]['appraiser'][$key]['propertyAppraiserUpload']
                ));
            UploadServer::upload([
                'recordDate'     => Dates::Datestamp($recordDate),
                'LMRID'          => $LMRId,
                'oldFPCID'       => $PCID,
                'tmpFileContent' => base64_encode(FileStorage::getFile($_FILES['properties']['tmp_name'][$propertyIndex]['appraiser'][$key]['propertyAppraiserUpload'])),
                'propertyDocs'   => 1,
                'fileDocName'    => $fileName,
            ]);
        }
        return $fileName;
    }

    /**
     * @param int|null $LMRId
     * @return void
     */
    public static function updatePrimaryPropertyInfo(?int $LMRId): void
    {
        if(!$LMRId) {
            return;
        }
        $propertyArray = [
            'propertyAddress',
            'propertyCity',
            'propertyState',
            'propertyZip'
        ];
        $item = self::getPrimaryPropertyInfo($LMRId);
        if (!$item) {
            $item = new tblProperties();
        }
        foreach ($propertyArray as $eachPropertyKey) {
            if (isset($_POST[$eachPropertyKey])) {
                $item->$eachPropertyKey = Request::GetClean($eachPropertyKey);
            }
        }
        $item->LMRId = $LMRId;
        $item->realestateapi_id = intval(Request::GetClean('realestateapi_id'));
        $item->save();
    }

    public static function getPropertyFullAddressWithUnit(?tblProperties $propertyInfo): ?string
    {
        return $propertyInfo->LMRId ? (ucfirst(Strings::arrayToString([
            $propertyInfo->propertyAddress,
            $propertyInfo->propertyUnit ? 'Unit ' . $propertyInfo->propertyUnit : null,
            $propertyInfo->propertyCity,
            $propertyInfo->propertyState . ' ' . $propertyInfo->propertyZipCode,
        ]))) : null;
    }

    public static function saveImportProperties(?array $propertyData, ?int $LMRId)
    {
        foreach ($propertyData as $index => $value) {
            foreach ($value as $propertyKey => $propertyValue) {
                switch ($propertyKey) {
                    case 'Location':
                        $tblProperties = new tblProperties();
                        foreach ($propertyValue as $propertyLocationKey => $propertyLocationValue) {
                            $tblProperties->$propertyLocationKey = $propertyLocationValue;
                        }
                        $tblProperties->LMRId = $LMRId;
                        $tblProperties->createdBy = PageVariables::$userNumber;
                        $tblProperties->createdGroup = PageVariables::$userGroup;
                        $tblProperties->createdDate = Dates::Timestamp();
                        $tblProperties->Save();
                        break;
                    case 'Characteristics':
                        $tblPropertiesCharacteristics = new tblPropertiesCharacteristics();
                        foreach ($propertyValue as $propertyCharKey => $propertyCharValue) {
                            $tblPropertiesCharacteristics->$propertyCharKey = $propertyCharValue;
                        }
                        $tblPropertiesCharacteristics->propertyId = $tblProperties->propertyId;
                        $tblPropertiesCharacteristics->createdBy = PageVariables::$userNumber;
                        $tblPropertiesCharacteristics->createdGroup = PageVariables::$userGroup;
                        $tblPropertiesCharacteristics->createdDate = Dates::Timestamp();
                        $tblPropertiesCharacteristics->Save();
                        break;
                    case 'Details':
                        $tblPropertiesDetails = new tblPropertiesDetails();
                        foreach ($propertyValue as $propertyDetailsKey => $propertyDetailsValue) {
                            $tblPropertiesDetails->$propertyDetailsKey = $propertyDetailsValue;
                        }
                        $tblPropertiesDetails->propertyId = $tblProperties->propertyId;
                        $tblPropertiesDetails->createdBy = PageVariables::$userNumber;
                        $tblPropertiesDetails->createdGroup = PageVariables::$userGroup;
                        $tblPropertiesDetails->createdDate = Dates::Timestamp();
                        $tblPropertiesDetails->Save();
                        break;
                    case 'Analysis':
                        $tblPropertiesAnalysis = new tblPropertiesAnalysis();
                        foreach ($propertyValue as $propertyAnalysisKey => $propertyAnalysisValue) {
                            $tblPropertiesAnalysis->$propertyAnalysisKey = $propertyAnalysisValue;
                        }
                        $tblPropertiesAnalysis->propertyId = $tblProperties->propertyId;
                        $tblPropertiesAnalysis->createdBy = PageVariables::$userNumber;
                        $tblPropertiesAnalysis->createdGroup = PageVariables::$userGroup;
                        $tblPropertiesAnalysis->createdDate = Dates::Timestamp();
                        $tblPropertiesAnalysis->Save();
                        break;
                }
            }
        }
    }
}
