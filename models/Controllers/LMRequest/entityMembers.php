<?php

namespace models\Controllers\LMRequest;

use models\composite\oFile\getFileEntityMembers;
use models\constants\borrowerBusinessEntity;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\tblEntityMembers;
use models\lendingwise\tblMemberOfficerInfo;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class entityMembers extends strongType
{
    public static ?string $html = null;
    public static ?string $closeHtml = '</div></div></div></div>';
    public static function removeEntityMembers(?int $memberId, ?int $entityId): bool
    {
        if (!$memberId && !$entityId) {
            return false;
        }
        $params = [
            'memberId' => $memberId
        ];
        if ($entityId) {
            $params['id'] = $entityId;
        }
        $tblEntityMembers = tblEntityMembers::GetAll($params);
        foreach ($tblEntityMembers as $member) {
            $member->Delete();
        }
        return true;
    }

    /**
     * @param int $LMRId
     * @param int|null $parent_id
     * @param string|null $header
     * @return string|null
     */
    public static function getEntityMembers(int $LMRId, ?int $parent_id = null, ?string $header = null): ?string
    {
        $parentEntityMembers = getFileEntityMembers::getRootMembers($LMRId, $parent_id);
        $i = 1;
        loanForm::pushSectionID('BEN');
        foreach ($parentEntityMembers as $member) { //loop -1
            $accordionBorderClass = (!$parent_id || $i <= 2) ? 'border border-secondary' : '';
            $title = self::getHeaderTitle($member, $i, $header);
            self::$html .= '
        <!--begin::Accordion-->
            <div class="accordion ' . $accordionBorderClass . '" id="accordionExample_' . cypher::myEncryption($member->memberId) . '">
                <div class="card">
                    <div class="card-header d-flex">
                        <div class="col-md-10 card-title collapsed" data-toggle="collapse" data-target="#collapseOne' . $member->memberId . '">
                        ' . $title . '
                        </div>
                        <div class="col-md-2 text-right">
                            <span class="btn btn-sm btn-danger btn-icon m-2 tooltipClass"
                                title="" data-toggle="tooltip" data-placement="top"
                                data-original-title="Click to remove Individual/Entity info"
                                data-rem-tableid="' . cypher::myEncryption($member->memberId) . '"
                                data-rem-lmrid="' . cypher::myEncryption($member->LMRID) . '"
                                data-rem-accordion="accordionExample_' . cypher::myEncryption($member->memberId) . '"
                                onclick="BusinessEntitySection.removeEntityMemberFieldsCV3(this);"
                                >
                                    <i class="icon-1x fas fa-minus-circle"></i>
                            </span>
                            <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon m-2 toggleClass"
                                  data-card-tool="toggle"
                                  data-target="#collapseOne' . $member->memberId . '"
                                  data-toggle="collapse"
                                  title=""
                                  data-original-title="Toggle Card">
                                <i class="icon-1x ki ki-arrow-down icon-nm"></i>
                            </span>
                        </div>
                    </div>
                    <div id="collapseOne' . $member->memberId . '" class="collapse" data-parent="#accordionExample_' . cypher::myEncryption($member->memberId) . '">
                        <div class="card-body">';
                            $childCountData = getFileEntityMembers::getRootMembers($LMRId, $member->memberId);
                            $childCount = count($childCountData);
                            self::getFormData($member, $childCount);
                            self::getEntityMembers($LMRId, $member->memberId, $member->memberName);
                            self::$html .= self::$closeHtml;
        $i++;
        }
        loanForm::popSectionID();
        return self::$html;
    }

    /**
     * @param tblMemberOfficerInfo $item
     * @param int $childCount
     */

    public static function getFormData(tblMemberOfficerInfo $item, int $childCount)
    {
        $parent_id = $item->parent_id ?: 0;
        if (!$item->memberId) $item->memberId = 0;
        if (!$item->memberType) {
            $item->memberType = 'Individual'; //Default
        }
        $individualChecked = $item->memberType == 'Individual' ? 'checked=checked' : '';
        $entityChecked = $item->memberType == 'Entity' ? 'checked=checked' : '';

        $memberPersonalGuaranteeYes = $item->memberPersonalGuarantee == 'Yes' ? 'checked=checked' : '';
        $memberPersonalGuaranteeNo = $item->memberPersonalGuarantee == 'No' ? 'checked=checked' : '';
        $memberAuthorizedSignerYes = $item->memberAuthorizedSigner == 'Yes' ? 'checked=checked' : '';
        $memberAuthorizedSignerNo = $item->memberAuthorizedSigner == 'No' ? 'checked=checked' : '';
        $memberCitizenshipUSCitizen = $item->memberCitizenship == 'U.S. Citizen' ? 'checked=checked' : '';
        $memberCitizenshipPermResident = $item->memberCitizenship == 'Perm Resident Alien' ? 'checked=checked' : '';
        $memberCitizenshipNonPermResident = $item->memberCitizenship == 'Non-Perm Resident Alien' ? 'checked=checked' : '';
        $memberCitizenshipForeignNational = $item->memberCitizenship == 'Foreign National' ? 'checked=checked' : '';


        //Hide or Show fields based on member type and form field configuration
        $searchString = 'secHide';
        $memberTinHideShowClass = $childCount ? 'secHide' : 'hide';
        $memberTitleHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberTitle'), $searchString)) ? 'secHide' : '';
        $memberAnnualSalaryHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberAnnualSalary'), $searchString)) ? 'secHide' : '';
        $memberSSNHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberSSN'), $searchString)) ? 'secHide' : '';
        $memberDOBHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberDOB'), $searchString)) ? 'secHide' : '';
        $memberCreditScoreHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCreditScore'), $searchString)) ? 'secHide' : '';
        $memberEmailHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberEmail'), $searchString)) ? 'secHide' : '';
        $memberDriversLicenseHideShow  = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberDriversLicense'), $searchString)) ? 'secHide' : '';
        $memberDriversLicenseStateHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberDriversLicenseState'), $searchString)) ? 'secHide' : '';
        $memberTinHideShow = ($item->memberType != 'Entity' || strpos(loanForm::showField('memberTin'), $searchString)) ? $memberTinHideShowClass : '';
        $memberPersonalGuaranteeHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberPersonalGuarantee'), $searchString)) ? 'secHide' : '';
        $memberAuthorizedSignerHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberAuthorizedSigner'), $searchString)) ? 'secHide' : '';
        $memberCitizenshipHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCitizenship'), $searchString)) ? 'secHide' : '';
        $memberMaritalStatusHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberMaritalStatus'), $searchString)) ? 'secHide' : '';
        $memberMarriageDateHideShow = (
            $item->memberType == 'Entity'
            || $item->memberMaritalStatus == 'Unmarried'
            || strpos(loanForm::showField('memberMarriageDate'), $searchString)
        ) ? 'secHide' : '';
        $memberDivorceDateHideShow = (
            $item->memberType == 'Entity'
            || $item->memberMaritalStatus == 'Unmarried'
            || $item->memberMaritalStatus == 'Married'
            || strpos(loanForm::showField('memberDivorceDate'), $searchString)
        ) ? 'secHide' : '';
        $memberMaidenNameHideShow = (
            $item->memberType == 'Entity'
            || $item->memberMaritalStatus == 'Unmarried'
            || strpos(loanForm::showField('memberMaidenName'), $searchString)
        ) ? 'secHide' : '';
        $memberSpouseNameHideShow = (
            $item->memberType == 'Entity'
            || $item->memberMaritalStatus == 'Unmarried'
            || strpos(loanForm::showField('memberSpouseName'), $searchString)
        ) ? 'secHide' : '';

        $memberCreditScoreDateHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCreditScoreDate'), $searchString)) ? 'secHide' : '';
        $memberRentOrOwnHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberRentOrOwn'), $searchString)) ? 'secHide' : '';
        $memberMonthlyRentOrMortgageHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberMonthlyRentOrMortgage'), $searchString)) ? 'secHide' : '';
        $memberDateMovedAddressHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberDateMovedAddress'), $searchString)) ? 'secHide' : '';
        $memberRealEstateValueHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberRealEstateValue'), $searchString)) ? 'secHide' : '';
        $memberRetirementAccountBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberRetirementAccountBalance'), $searchString)) ? 'secHide' : '';
        $memberCashSavingsStocksBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCashSavingsStocksBalance'), $searchString)) ? 'secHide' : '';
        $memberCreditCardBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberCreditCardBalance'), $searchString)) ? 'secHide' : '';
        $memberMortgageBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberMortgageBalance'), $searchString)) ? 'secHide' : '';
        $memberAutoLoanBalanceHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberAutoLoanBalance'), $searchString)) ? 'secHide' : '';
        $memberTotalNetWorthHideShow = ($item->memberType == 'Entity' || strpos(loanForm::showField('memberTotalNetWorth'), $searchString)) ? 'secHide' : '';

        $howManyMemberOfficerHideShow = $item->memberType != 'Entity' ? 'hide' : ''; //Entity only

        //Rent or Own Selected
        $memberRentSelected = $item->memberRentOrOwn == 'Rent' ? 'selected=selected' : '';
        $memberOwnSelected = $item->memberRentOrOwn == 'Own' ? 'selected=selected' : '';

        //
        $memberTypeDisabled = '';
        $memberTypeToolTip = '';
        if ($childCount) {
            $memberTypeDisabled = 'disabled=disabled';
            $memberTypeToolTip = '<i class="fa fa-info-circle text-primary tooltipClass" title="" data-original-title="Member Type cannot be updated. It has nested entities/member(s). 
            If you still need to update delete all the nested entities/member(s)."></i>';
        }

        self::$html .= '<div class="row">
        <input type="hidden" name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberId]" value="' . $item->memberId . '">
        <input type="hidden" name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][parent_id]" value="' . $parent_id . '">
        <div class="col-md-3">
            <div class="radio-inline">
                <label class="radio">
                    <input
                    type="radio"
                    name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberType]"
                    value="Individual"
                    id=""             
                    ' . $individualChecked . '
                    ' . $memberTypeDisabled . '
                    onchange="BusinessEntitySection.memberTypeShowHideFieldsCV3(' . "'Individual_$item->memberId" . "_" . "$parent_id'" . ',this);"
                    >
                    <span></span>Individual
                </label>
                <label class="radio">
                    <input
                    type="radio"
                    name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberType]"
                    value="Entity"
                    id=""                     
                    ' . $entityChecked . '
                    ' . $memberTypeDisabled . '
                    onchange="BusinessEntitySection.memberTypeShowHideFieldsCV3(' . "'Entity_$item->memberId" . "_" . "$parent_id'" . ',this);"
                    >
                    <span></span>Entity &nbsp;&nbsp;' . $memberTypeToolTip . '
                </label>
            </div>
        </div>
        
        <div class="col-md-3 ' . loanForm::showField('memberName') . ' " id="">
            <div class="form-group">
                ' . loanForm::label('memberName') . '
                <div class="">
                    <input name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberName]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberName') . ' "
                            type="text"
                            value="' . $item->memberName . '"
                            ' . loanForm::isEnabled('memberName') . '
                            id="memberName' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberTitleHideShow . ' " id="memberTitle_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberTitle') . '
                <div class="">
                    <input name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberTitle]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberTitle') . ' "
                            type="text"
                            value="' . $item->memberTitle . '"
                            ' . loanForm::isEnabled('memberTitle') . '
                            id="memberTitle' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . loanForm::showField('memberCategory') . ' ">
            <div class="form-group">
                ' . loanForm::label('memberCategory') . '
                <div class="">
                    <select class="form-control input-sm ' . loanForm::isMandatory('memberCategory') . ' "
                    ' . loanForm::isEnabled('memberCategory') . '
                    name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCategory]"
                    id="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCategory]">
                        <option value="">-Select-</option>';
                        foreach (borrowerBusinessEntity::getEntityTypes() as $category) {
                            $memberCategory = $item->memberCategory == $category ? 'selected=selected' : '';
                            self::$html .= '<option value="' . $category . '" ' . $memberCategory . ' >' . $category . '</option>';
                        }
    self::$html .= '</select>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . loanForm::showField('memberOwnership') . ' " id="">
            <div class="form-group">
                ' . loanForm::label('memberOwnership') . '
                <div class="">
                    <div class="input-group">
                        <input name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberOwnership]"
                        class="form-control input-sm memberOwnership_' . $parent_id . ' ' . loanForm::isMandatory('memberOwnership') . ' "
                        type="text"
                        value="' . htmlentities($item->memberOwnership) . '"
                        id="memberOwnership' . $item->memberId . '_' . $parent_id . '"
                        ' . loanForm::isEnabled('memberOwnership') . '
                        onblur="BusinessEntitySection.calculateOwnerShip(this)" >
                        <div class="input-group-append">
                            <span class="input-group-text">
                                %
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberAnnualSalaryHideShow . ' " id="memberAnnualSalary_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberAnnualSalary') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberAnnualSalary]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberAnnualSalary') . ' "
                            type="text" onblur="currencyConverter(this, this.value)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberAnnualSalary)) . '"
                            ' . loanForm::isEnabled('memberAnnualSalary') . '
                            id="memberAnnualSalary' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . loanForm::showField('memberAddress') . ' " id="">
            <div class="form-group">
                ' . loanForm::label('memberAddress') . '
                <script>
                $(document).ready(function() {
                    $("#memberAddress' . $item->memberId . '_' . $parent_id . '").on("input", function() {
                        address_lookup.InitLegacy($(this));
                    });
                });
                </script>
                <div class="">
                    <input name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberAddress]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberAddress') . ' "
                            type="text"
                            value="' . htmlentities($item->memberAddress) . '"
                            ' . loanForm::isEnabled('memberAddress') . '
                            id="memberAddress' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . loanForm::showField('memberPhone') . ' " id="">
            <div class="form-group">
                ' . loanForm::label('memberPhone') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberPhone]"
                            class="form-control input-sm mask_phone ' . loanForm::isMandatory('memberPhone') . ' "
                            type="text"
                            placeholder="(___) ___ - ____ Ext ____"
                            value="' . Strings::formatPhoneNumber($item->memberPhone) . '"
                            ' . loanForm::isEnabled('memberPhone') . '
                            id="memberPhone' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . loanForm::showField('memberCell') . ' " id="">
            <div class="form-group">
                ' . loanForm::label('memberCell') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCell]"
                            class="form-control input-sm mask_cellnew ' . loanForm::isMandatory('memberCell') . ' "
                            type="text"
                            placeholder="(___) ___ - ____"
                            value="' . Strings::formatCellNumber($item->memberCell) . '"
                            ' . loanForm::isEnabled('memberCell') . '
                            id="memberCell' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberSSNHideShow . ' " id="memberSSN_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberSSN') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberSSN]"
                            class="form-control input-sm mask_ssn ' . loanForm::isMandatory('memberSSN') . ' "
                            type="text"
                            placeholder="___-__-____"
                            value="' . Strings::formatSSNNumber($item->memberSSN) . '"
                            ' . loanForm::isEnabled('memberSSN') . '
                            id="memberSSN' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberDOBHideShow . ' " id="memberDOB_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDOB') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberDOB]"
                            class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDOB') . ' "
                            type="text"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberDOB, 'YMD', 'm/d/Y') . '"
                            ' . loanForm::isEnabled('memberDOB') . '
                            id="memberDOB' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberEmailHideShow . ' " id="memberEmail_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberEmail') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberEmail]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberEmail') . ' "
                            type="text"
                            value="' . $item->memberEmail . '"
                            ' . loanForm::isEnabled('memberEmail') . '
                            id="memberEmail' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberCreditScoreHideShow . ' " id="memberCreditScore_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCreditScore') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCreditScore]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberCreditScore') . ' "
                            type="text"
                            value="' . $item->memberCreditScore . '"
                            ' . loanForm::isEnabled('memberCreditScore') . '
                            id="memberCreditScore' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberCreditScoreDateHideShow . ' " id="memberCreditScoreDate_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCreditScoreDate') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCreditScoreDate]"
                            class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberCreditScoreDate') . ' "
                            type="text"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberCreditScoreDate, 'YMD', 'm/d/Y') . '"
                            ' . loanForm::isEnabled('memberCreditScoreDate') . '
                            id="memberCreditScoreDate' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberRentOrOwnHideShow . ' " id="memberRentOrOwn_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberRentOrOwn') . '
                <div class="">
                    <select name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberRentOrOwn]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberRentOrOwn') . '"
                            ' . loanForm::isEnabled('memberRentOrOwn') . '
                            id="memberRentOrOwn' . $item->memberId . '_' . $parent_id . '">
                        <option value="">-Select-</option>
                        <option value="Rent" ' . $memberRentSelected . '>Rent</option>
                        <option value="Own" ' . $memberOwnSelected . '>Own</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberMonthlyRentOrMortgageHideShow . ' " id="memberMonthlyRentOrMortgage_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMonthlyRentOrMortgage') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberMonthlyRentOrMortgage]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberMonthlyRentOrMortgage') . ' "
                            type="text" onblur="currencyConverter(this, this.value)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberMonthlyRentOrMortgage)) . '"
                            ' . loanForm::isEnabled('memberMonthlyRentOrMortgage') . '
                            id="memberMonthlyRentOrMortgage' . $item->memberId . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberDateMovedAddressHideShow . ' " id="memberDateMovedAddress_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDateMovedAddress') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberDateMovedAddress]"
                            class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDateMovedAddress') . ' "
                            type="text"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberDateMovedAddress, 'YMD', 'm/d/Y') . '"
                            ' . loanForm::isEnabled('memberDateMovedAddress') . '
                            id="memberDateMovedAddress' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberRealEstateValueHideShow . ' " id="memberRealEstateValue_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberRealEstateValue') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberRealEstateValue]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberRealEstateValue') . ' "
                            type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberRealEstateValue)) . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberRealEstateValue') . '
                            id="memberRealEstateValue' . $item->memberId . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberRetirementAccountBalanceHideShow . ' " id="memberRetirementAccountBalance_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberRetirementAccountBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberRetirementAccountBalance]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberRetirementAccountBalance') . ' "
                            type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberRetirementAccountBalance)) . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberRetirementAccountBalance') . '
                            id="memberRetirementAccountBalance' . $item->memberId . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberCashSavingsStocksBalanceHideShow . ' " id="memberCashSavingsStocksBalance_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCashSavingsStocksBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCashSavingsStocksBalance]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberCashSavingsStocksBalance') . ' "
                            type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberCashSavingsStocksBalance)) . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberCashSavingsStocksBalance') . '
                            id="memberCashSavingsStocksBalance' . $item->memberId . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberCreditCardBalanceHideShow . ' " id="memberCreditCardBalance_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCreditCardBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCreditCardBalance]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberCreditCardBalance') . ' "
                            type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberCreditCardBalance)) . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberCreditCardBalance') . '
                            id="memberCreditCardBalance' . $item->memberId . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberMortgageBalanceHideShow . ' " id="memberMortgageBalance_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMortgageBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberMortgageBalance]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberMortgageBalance') . ' "
                            type="text" onblur="currencyConverter(this, this.value); BusinessEntitySection.calculateTotalNetWorth(this)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberMortgageBalance)) . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberMortgageBalance') . '
                            id="memberMortgageBalance' . $item->memberId . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberAutoLoanBalanceHideShow . ' " id="memberAutoLoanBalance_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberAutoLoanBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberAutoLoanBalance]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberAutoLoanBalance') . ' "
                            type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberAutoLoanBalance)) . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberAutoLoanBalance') . '
                            id="memberAutoLoanBalance' . $item->memberId . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberTotalNetWorthHideShow . ' " id="memberTotalNetWorth_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberTotalNetWorth') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input class="form-control input-sm" readonly 
                            type="text" onblur="currencyConverter(this, this.value)"
                            value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberTotalNetWorth)) . '"
                            ' . loanForm::isEnabled('memberTotalNetWorth') . '
                            id="memberTotalNetWorth' . $item->memberId . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberMaritalStatusHideShow . ' " id="memberMaritalStatus_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMaritalStatus') . '
                <div class="">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusUnmarried_' . $item->memberId . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberMaritalStatus') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberMaritalStatus]"
                            id="memberMaritalStatusUnmarried_' . $item->memberId . '_' . $parent_id . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberMaritalStatus') . '
                            value="Unmarried" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                            ' . ($item->memberMaritalStatus == 'Unmarried' ? 'checked=checked' : '') . '
                            ><span></span>Unmarried
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusMarried_' . $item->memberId . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberMaritalStatus') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberMaritalStatus]"
                            id="memberMaritalStatusMarried_' . $item->memberId . '_' . $parent_id . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberMaritalStatus') . '
                            value="Married" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                            ' . ($item->memberMaritalStatus == 'Married' ? 'checked=checked' : '') . '
                            ><span></span>Married
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusSeparated_' . $item->memberId . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberMaritalStatus') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberMaritalStatus]"
                            id="memberMaritalStatusSeparated_' . $item->memberId . '_' . $parent_id . '"
                            data-index="' . $item->memberId . '_' . $parent_id . '"
                            ' . loanForm::isEnabled('memberMaritalStatus') . '
                            value="Separated" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                            ' . ($item->memberMaritalStatus == 'Separated' ? 'checked=checked' : '') . '
                            ><span></span>Separated
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberMarriageDateHideShow . ' " id="memberMarriageDate_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMarriageDate') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberMarriageDate]"
                            class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberMarriageDate') . ' "
                            type="text"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberMarriageDate, 'YMD', 'm/d/Y') . '"
                            ' . loanForm::isEnabled('memberMarriageDate') . '
                            id="memberMarriageDate' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberDivorceDateHideShow . ' " id="memberDivorceDate_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDivorceDate') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberDivorceDate]"
                            class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDivorceDate') . ' "
                            type="text"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberDivorceDate, 'YMD', 'm/d/Y') . '"
                            ' . loanForm::isEnabled('memberDivorceDate') . '
                            id="memberDivorceDate' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberMaidenNameHideShow . ' " id="memberMaidenName_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMaidenName') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberMaidenName]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberMaidenName') . ' "
                            type="text"
                            value="' . $item->memberMaidenName . '"
                            ' . loanForm::isEnabled('memberMaidenName') . '
                            id="memberEmail' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberSpouseNameHideShow . ' " id="memberSpouseName_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberSpouseName') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberSpouseName]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberSpouseName') . ' "
                            type="text"
                            value="' . $item->memberSpouseName . '"
                            ' . loanForm::isEnabled('memberSpouseName') . '
                            id="memberEmail' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberDriversLicenseStateHideShow . ' " id="memberDriversLicenseState_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDriversLicenseState') . '
                <div class="">
                    <select name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberDriversLicenseState]"
                    class="form-control input-sm ' . loanForm::isMandatory('memberDriversLicenseState') . '"
                    ' . loanForm::isEnabled('memberDriversLicenseState') . '
                    id="memberDriversLicenseState' . $item->memberId . '_' . $parent_id . '">
                        <option value="">-Select-</option>';
                        foreach(Arrays::fetchStates() as $dlState) {
                            $licenseState = $item->memberDriversLicenseState == $dlState['stateCode'] ? 'selected=selected' : '';
                            self::$html .= '<option value="' . $dlState['stateCode'] . '" ' . $licenseState . ' >' . $dlState['stateName'] . '</option>';
                        }
self::$html .= '</select>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberDriversLicenseHideShow . ' " id="memberDriversLicense_' . $item->memberId  . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDriversLicense') . '
                <div class="">
                    <input  name="members[' . $item->memberId  . '][entityMember][' . $parent_id . '][memberDriversLicense]"
                            class="form-control input-sm ' . loanForm::isMandatory('memberDriversLicense') . ' "
                            type="text"
                            value="' . htmlentities($item->memberDriversLicense) . '"
                            ' . loanForm::isEnabled('memberDriversLicense') . '
                            id="memberDriversLicense' . $item->memberId  . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberTinHideShow . ' " id="memberTin_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberTin') . '
                <div class="">
                    <input  name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberTin]"
                            class="form-control input-sm mask_ein ' . loanForm::isMandatory('memberTin') . ' "
                            type="text"
                            placeholder="__-_______"
                            value="' . $item->memberTin . '"
                            ' . loanForm::isEnabled('memberTin') . '
                            id="memberTin' . $item->memberId . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        
        
        <div class="col-md-3 ' . $memberPersonalGuaranteeHideShow . ' " id="memberPersonalGuarantee_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberPersonalGuarantee') . '
                <div class="">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold" for="memberPersonalGuaranteeYes_' . $item->memberId . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberPersonalGuarantee') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberPersonalGuarantee]"
                            id="memberPersonalGuaranteeYes_' . $item->memberId . '_' . $parent_id . '"
                            ' . $memberPersonalGuaranteeYes . '
                            ' . loanForm::isEnabled('memberPersonalGuarantee') . '
                            value="Yes"><span></span>Yes
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="memberPersonalGuaranteeNo_' . $item->memberId . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberPersonalGuarantee') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberPersonalGuarantee]"
                            id="memberPersonalGuaranteeNo_' . $item->memberId . '_' . $parent_id . '"
                            ' . $memberPersonalGuaranteeNo . '
                            ' . loanForm::isEnabled('memberPersonalGuarantee') . '
                            value="No"><span></span>No
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberAuthorizedSignerHideShow . ' " id="memberAuthorizedSigner_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberAuthorizedSigner') . '
                <div class="">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold" for="memberAuthorizedSignerYes_' . $item->memberId . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberAuthorizedSigner') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberAuthorizedSigner]"
                            id="memberAuthorizedSignerYes_' . $item->memberId . '_' . $parent_id . '"
                            ' . $memberAuthorizedSignerYes . '
                            ' . loanForm::isEnabled('memberAuthorizedSigner') . '
                            value="Yes"><span></span>Yes
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="memberAuthorizedSignerNo_' . $item->memberId . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberAuthorizedSigner') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberAuthorizedSigner]"
                            id="memberAuthorizedSignerNo_' . $item->memberId . '_' . $parent_id . '"
                            ' . $memberAuthorizedSignerNo . '
                            ' . loanForm::isEnabled('memberAuthorizedSigner') . '
                            value="No"><span></span>No
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 ' . $memberCitizenshipHideShow . '  " id="memberCitizenship_' . $item->memberId  . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCitizenship') . '
                <div class="">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold" for="memberCitizenship_us_' . $item->memberId  . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberCitizenship') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCitizenship]"
                            id="memberCitizenship_us_' . $item->memberId  . '_' . $parent_id . '"
                            ' . $memberCitizenshipUSCitizen . '
                            ' . loanForm::isEnabled('memberCitizenship') . '
                            value="U.S. Citizen"><span></span>U.S.
                            Citizen
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="memberCitizenship_pra_' . $item->memberId  . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberCitizenship') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCitizenship]"
                            id="memberCitizenship_pra_' . $item->memberId  . '_' . $parent_id . '"
                            ' . $memberCitizenshipPermResident . '
                            ' . loanForm::isEnabled('memberCitizenship') . '
                            value="Perm Resident Alien"><span></span>Perm
                            Resident
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="memberCitizenship_npra_' . $item->memberId  . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberCitizenship') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCitizenship]"
                            id="memberCitizenship_npra_' . $item->memberId  . '_' . $parent_id . '"
                            ' . $memberCitizenshipNonPermResident . '
                            ' . loanForm::isEnabled('memberCitizenship') . '
                            value="Non-Perm Resident Alien"><span></span>Non-Perm
                            Resident
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="memberCitizenship_fn_' . $item->memberId  . '_' . $parent_id . '">
                            <input type="radio"
                            class=" ' . loanForm::isMandatory('memberCitizenship') . ' "
                            name="members[' . $item->memberId . '][entityMember][' . $parent_id . '][memberCitizenship]"
                            id="memberCitizenship_fn_' . $item->memberId  . '_' . $parent_id . '"
                            ' . $memberCitizenshipForeignNational . '
                            ' . loanForm::isEnabled('memberCitizenship') . '
                            value="Foreign National"><span></span>Foreign
                            National
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
        <div class="col-md-6 ' . $howManyMemberOfficerHideShow . ' " id="howManyMemberOfficer_' . $item->memberId . '_' . $parent_id . '">
            <div class="form-group row">
                <label class="col-md-10 font-weight-bold">
                    How many members/officers are there with 20%+ ownership?
                </label>
                <div class="col-md-2">
                    <input
                            class="form-control input-sm"
                            type="text"
                            name=""
                            value="' . $childCount . '"
                            id="' . $item->memberId . '_' . $parent_id . '"
                            maxlength="2"
                            data-lmrid="' . cypher::myEncryption($item->LMRID) . '"
                            data-memberId="' . cypher::myEncryption($item->memberId) . '"
                            data-activetab="' . cypher::myEncryption(LMRequest::$activeTab) . '"
                            onchange="BusinessEntitySection.parentEntityMemberFields(this)"
                            >
                </div>
            </div>
        </div>
        </div>
        <div class="col-md-12" id="formContainer_' . $item->memberId . '_' . $parent_id . '"></div>
</div>';
    }

    /**
     * @param $item
     * @param $i
     * @param $header
     * @return string
     */
    public static function getHeaderTitle($item, $i, $header): string
    {
        $individualEntityMemberCount = getFileEntityMembers::getIndividualEntityMemberCount($item->LMRID, $item->memberId);
        $title = 'Member/Officer #' . $i . ' of ' . $header . ' > ' . $item->memberName;
        $title .= $individualEntityMemberCount->individualCount ? ' |(' . $individualEntityMemberCount->individualCount . ')Individual ' : '';
        $title .= $individualEntityMemberCount->entityCount ? ' |(' . $individualEntityMemberCount->entityCount . ')Entity ' : '';
        return $title;
    }
}
