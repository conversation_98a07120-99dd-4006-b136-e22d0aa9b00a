<?php

namespace models\Controllers\LMRequest;

use models\constants\gl\glPCID;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\cypher;
use models\Database2;
use models\lendingwise\tblAllonges;
use models\lendingwise\tblAssignmentOfMortgage;
use models\lendingwise\tblDeedOfTrusts;
use models\lendingwise\tblMortgageAssignments;
use models\lendingwise\tblPostCloseShipping;
use models\lendingwise\tblPostSaleExceptions;
use models\lendingwise\tblPreBoarding;
use models\lendingwise\tblPreSaleExceptions;
use models\lendingwise\tblTrailingDocuments;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\types\strongType;

class PostClosing extends strongType
{

    public static ?int $LMRId = 0;

    public static array $DOTTypeForCV3 = [
        '1' => 'CV3',
        '2' => 'Investor',
    ];
    public static array $DOTTypeForOthers = [
        '1' => 'Lender',
        '2' => 'Investor',
    ];
    public static array $DOTSourceOfDelivery = [
        '1' => 'Email',
        '2' => 'Portal',
        '3' => 'Mail-in',
    ];
    public static array $PostClosingYesNo = [
        '1' => 'Yes',
        '2' => 'No',
    ];
    public static array $assignmentTypes = [
        '1' => 'AOM 1',
        '2' => 'AOM 3',
        '3' => 'Corrective',
        '4' => 'Repurchase',
    ];
    public static array $saleExceptionTypes = [
        '1' => 'DOT',
        '2' => 'FTP',
        '3' => 'Allonge 1',
        '4' => 'Allonge 2',
        '5' => 'AOM 1',
        '6' => 'AOM 2',
    ];

    public static function saveTrailingDocuments(): void
    {

        $tblTrailingDocuments = tblTrailingDocuments::Get(['LMRId' => self::$LMRId]) ?? new tblTrailingDocuments();
        $tblTrailingDocuments->LMRId = self::$LMRId;
        $tblTrailingDocuments->DOTRequestedDate = Request::isset('DOTRequestedDate') ? Database2::strongTypeValue(Request::GetClean('DOTRequestedDate'), Database2::DATATYPE_DATE) : $tblTrailingDocuments->DOTRequestedDate;

        $tblTrailingDocuments->FTPRequestedDate = Request::isset('FTPRequestedDate') ? Database2::strongTypeValue(Request::GetClean('FTPRequestedDate'), Database2::DATATYPE_DATE) : $tblTrailingDocuments->FTPRequestedDate;

        $tblTrailingDocuments->FTPReceivedDate = Request::isset('FTPReceivedDate') ? Database2::strongTypeValue(Request::GetClean('FTPReceivedDate'), Database2::DATATYPE_DATE) : $tblTrailingDocuments->FTPReceivedDate;
        $tblTrailingDocuments->FTPDateSentToCustodian = Request::isset('FTPDateSentToCustodian') ? Database2::strongTypeValue(Request::GetClean('FTPDateSentToCustodian'), Database2::DATATYPE_DATE) : $tblTrailingDocuments->FTPDateSentToCustodian;
        $tblTrailingDocuments->FTPSourceOfDelivery = Request::isset('FTPSourceOfDelivery') ? Request::GetClean('FTPSourceOfDelivery') : $tblTrailingDocuments->FTPSourceOfDelivery;
        $tblTrailingDocuments->FTPCopySentToCustodian = Request::isset('FTPCopySentToCustodian') ? intval(Request::GetClean('FTPCopySentToCustodian')) : $tblTrailingDocuments->FTPCopySentToCustodian;
        $tblTrailingDocuments->FTPCorrectiveRequested = Request::isset('FTPCorrectiveRequested') ? intval(Request::GetClean('FTPCorrectiveRequested')) : $tblTrailingDocuments->FTPCorrectiveRequested;
        $tblTrailingDocuments->trailingDocumentComments = Request::isset('trailingDocumentComments') ? Request::GetClean('trailingDocumentComments') : $tblTrailingDocuments->trailingDocumentComments;
        $tblTrailingDocuments->trailingDocsCompletedBy = Request::isset('trailingDocsCompletedBy') ? intval(Request::GetClean('trailingDocsCompletedBy')) : $tblTrailingDocuments->trailingDocsCompletedBy;
        $tblTrailingDocuments->trailingDocsCompleteDate = Request::isset('trailingDocsCompleteDate') ? Database2::strongTypeValue(Request::GetClean('trailingDocsCompleteDate'), Database2::DATATYPE_DATE) : $tblTrailingDocuments->trailingDocsCompleteDate;
        $tblTrailingDocuments->save();


        self::DOTMultiSave();
        self::AOMMultiSave();
        self::AOMSave();
        self::allongeSave();
        self::preSaleExceptionSave();
        self::postSaleExceptionSave();

    }


    /**
     * @return void
     */
    public static function save(): void
    {
        if (!self::$LMRId) {
            return;
        }
        if (!LMRequest::$LMRId) {
            LMRequest::setLMRId(self::$LMRId);
        }
        self::savePreBoarding();
        self::saveShipping();
        self::saveTrailingDocuments();
    }

    public static function savePreBoarding(): void
    {

        $tblPreBoarding = tblPreBoarding::Get(['LMRId' => self::$LMRId]) ?? new tblPreBoarding();
        $tblPreBoarding->LMRId = self::$LMRId;
        $tblPreBoarding->isNoteProvided = Request::isset('isNoteProvided') ? Database2::strongTypeValue(Request::GetClean('isNoteProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isNoteProvided;
        $tblPreBoarding->isFinalHUDProvided = Request::isset('isFinalHUDProvided') ? Database2::strongTypeValue(Request::GetClean('isFinalHUDProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isFinalHUDProvided;

        $tblPreBoarding->isPropertyTaxDocumentProvided = Request::isset('isPropertyTaxDocumentProvided') ? Database2::strongTypeValue(Request::GetClean('isPropertyTaxDocumentProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isPropertyTaxDocumentProvided;

        $tblPreBoarding->isLoanAgreementProvided = Request::isset('isLoanAgreementProvided') ? Database2::strongTypeValue(Request::GetClean('isLoanAgreementProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isLoanAgreementProvided;
        $tblPreBoarding->isFloodCertificationProvided = Request::isset('isFloodCertificationProvided') ? Database2::strongTypeValue(Request::GetClean('isFloodCertificationProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isFloodCertificationProvided;
        $tblPreBoarding->isFirstPaymentLetterProvided = Request::isset('isFirstPaymentLetterProvided') ? Database2::strongTypeValue(Request::GetClean('isFirstPaymentLetterProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isFirstPaymentLetterProvided;
        $tblPreBoarding->isDeedOfTrustProvided = Request::isset('isDeedOfTrustProvided') ? Database2::strongTypeValue(Request::GetClean('isDeedOfTrustProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isDeedOfTrustProvided;
        $tblPreBoarding->isFloodInsuranceProvided = Request::isset('isFloodInsuranceProvided') ? Database2::strongTypeValue(Request::GetClean('isFloodInsuranceProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isFloodInsuranceProvided;
        $tblPreBoarding->isAssignmentOfMortgage1Provided = Request::isset('isAssignmentOfMortgage1Provided') ? Database2::strongTypeValue(Request::GetClean('isAssignmentOfMortgage1Provided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isAssignmentOfMortgage1Provided;
        $tblPreBoarding->isACHProvided = Request::isset('isACHProvided') ? Database2::strongTypeValue(Request::GetClean('isACHProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isACHProvided;
        $tblPreBoarding->isHazardInsuranceProvided = Request::isset('isHazardInsuranceProvided') ? Database2::strongTypeValue(Request::GetClean('isHazardInsuranceProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isHazardInsuranceProvided;
        $tblPreBoarding->isOtherProvided = Request::isset('isOtherProvided') ? Database2::strongTypeValue(Request::GetClean('isOtherProvided'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->isOtherProvided;
        $tblPreBoarding->preBoardingComments = Request::isset('preBoardingComments') ? Request::GetClean('preBoardingComments') : $tblPreBoarding->preBoardingComments;
        $tblPreBoarding->boardingCompletedBy = Request::isset('boardingCompletedBy') ? Database2::strongTypeValue(Request::GetClean('boardingCompletedBy'), Database2::DATATYPE_NUMBER) : $tblPreBoarding->boardingCompletedBy;

        $tblPreBoarding->boardingCompleteDate = Request::isset('boardingCompleteDate') ? Database2::strongTypeValue(Request::GetClean('boardingCompleteDate'), Database2::DATATYPE_DATE) : $tblPreBoarding->boardingCompleteDate;

        $tblPreBoarding->save();

    }

    public static function saveShipping(): void
    {

        $tblPostCloseShipping = tblPostCloseShipping::Get(['LMRId' => self::$LMRId]) ?? new tblPostCloseShipping();
        $tblPostCloseShipping->LMRId = self::$LMRId;
        $tblPostCloseShipping->closingPackageShippedDate = Request::isset('closingPackageShippedDate') ? Database2::strongTypeValue(Request::GetClean('closingPackageShippedDate'), Database2::DATATYPE_DATE) : $tblPostCloseShipping->closingPackageShippedDate;
        $tblPostCloseShipping->closingPackageShippedTracking = Request::isset('closingPackageShippedTracking') ? Request::GetClean('closingPackageShippedTracking') : $tblPostCloseShipping->closingPackageShippedTracking;

        $tblPostCloseShipping->closingPackageReceivedDate = Request::isset('closingPackageReceivedDate') ? Database2::strongTypeValue(Request::GetClean('closingPackageReceivedDate'), Database2::DATATYPE_DATE) : $tblPostCloseShipping->closingPackageReceivedDate;
        $tblPostCloseShipping->custodianShippedDate = Request::isset('custodianShippedDate') ? Database2::strongTypeValue(Request::GetClean('custodianShippedDate'), Database2::DATATYPE_DATE) : $tblPostCloseShipping->custodianShippedDate;
        $tblPostCloseShipping->custodianTracking = Request::isset('custodianTracking') ? Request::GetClean('custodianTracking') : $tblPostCloseShipping->custodianTracking;
        $tblPostCloseShipping->custodianReceivedDate = Request::isset('custodianReceivedDate') ? Database2::strongTypeValue(Request::GetClean('custodianReceivedDate'), Database2::DATATYPE_DATE) : $tblPostCloseShipping->custodianReceivedDate;
        $tblPostCloseShipping->custodianClearedDate = Request::isset('custodianClearedDate') ? Database2::strongTypeValue(Request::GetClean('custodianClearedDate'), Database2::DATATYPE_DATE) : $tblPostCloseShipping->custodianClearedDate;
        $tblPostCloseShipping->shippingComments = Request::isset('shippingComments') ? Request::GetClean('shippingComments') : $tblPostCloseShipping->shippingComments;

        $tblPostCloseShipping->shippingCompletedBy = Request::isset('shippingCompletedBy') ? Database2::strongTypeValue(Request::GetClean('shippingCompletedBy'), Database2::DATATYPE_NUMBER) : $tblPostCloseShipping->shippingCompletedBy;

        $tblPostCloseShipping->shippingCompletedDate = Request::isset('shippingCompletedDate') ? Database2::strongTypeValue(Request::GetClean('shippingCompletedDate'), Database2::DATATYPE_DATE) : $tblPostCloseShipping->shippingCompletedDate;

        $tblPostCloseShipping->save();
    }

    public static function DOTMultiSave(): void
    {

        if (Request::isset('DOTFields') && is_array(Request::GetClean('DOTFields'))) {

            foreach (Request::GetClean('DOTFields') as $DOTKey => $DOTField) {
                if (!array_filter($DOTField)) {
                    continue;
                }
                $tblDeedOfTrusts = tblDeedOfTrusts::Get([
                    'LMRId' => self::$LMRId,
                    'id'    => cypher::myDecryption($DOTField['id']),
                ]) ?? new tblDeedOfTrusts();
                $tblDeedOfTrusts->DOTRecordedDateReceived = isset($DOTField['DOTRecordedDateReceived']) ? Database2::strongTypeValue($DOTField['DOTRecordedDateReceived'], Database2::DATATYPE_DATE) : $tblDeedOfTrusts->DOTRecordedDateReceived;

                $tblDeedOfTrusts->DOTRecordingInformation = isset($DOTField['DOTRecordingInformation']) ? $DOTField['DOTRecordingInformation'] : $tblDeedOfTrusts->DOTRecordingInformation;
                $tblDeedOfTrusts->DOTRecordedCopyDateReceived = isset($DOTField['DOTRecordedCopyDateReceived']) ? Database2::strongTypeValue($DOTField['DOTRecordedCopyDateReceived'], Database2::DATATYPE_DATE) : $tblDeedOfTrusts->DOTRecordedCopyDateReceived;

                $tblDeedOfTrusts->DOTRecordedCopySentToCustodian = isset($DOTField['DOTRecordedCopySentToCustodian']) ? intval($DOTField['DOTRecordedCopySentToCustodian']) : $tblDeedOfTrusts->DOTRecordedCopySentToCustodian;

                $tblDeedOfTrusts->DOTRecordedCopySentToCustodianDate = isset($DOTField['DOTRecordedCopySentToCustodianDate']) ? Database2::strongTypeValue($DOTField['DOTRecordedCopySentToCustodianDate'], Database2::DATATYPE_DATE) : $tblDeedOfTrusts->DOTRecordedCopySentToCustodianDate;
                $tblDeedOfTrusts->DOTSourceOfDeliveryToCustodian = isset($DOTField['DOTSourceOfDeliveryToCustodian']) ? intval($DOTField['DOTSourceOfDeliveryToCustodian']) : $tblDeedOfTrusts->DOTSourceOfDeliveryToCustodian;
                $tblDeedOfTrusts->DOTTrackingNumber = $DOTField['DOTTrackingNumber'] ?? $tblDeedOfTrusts->DOTTrackingNumber;
                $tblDeedOfTrusts->DOTCorrectRequested = isset($DOTField['DOTCorrectRequested']) ? intval($DOTField['DOTCorrectRequested']) : $tblDeedOfTrusts->DOTCorrectRequested;
                $tblDeedOfTrusts->LMRId = self::$LMRId;

                $tblDeedOfTrusts->save();
            }
        }
    }

    public static function AOMMultiSave(): void
    {

        if (Request::isset('AOMFields') && is_array(Request::GetClean('AOMFields'))) {
            foreach (Request::GetClean('AOMFields') as $AOMKey => $AOMField) {
                if (!array_filter($AOMField)) {
                    continue;
                }
                $tblMortgageAssignments = tblMortgageAssignments::Get([
                    'LMRId' => self::$LMRId,
                    'id'    => cypher::myDecryption($AOMField['id']),
                ]) ?? new tblMortgageAssignments();
                $tblMortgageAssignments->aomAssignmentType = isset($AOMField['aomAssignmentType']) ?
                    Database2::strongTypeValue($AOMField['aomAssignmentType'], Database2::DATATYPE_NUMBER) : $tblMortgageAssignments->aomAssignmentType;
                $tblMortgageAssignments->aomExecuted = isset($AOMField['aomExecuted']) ?
                    Database2::strongTypeValue($AOMField['aomExecuted'], Database2::DATATYPE_DATE) : $tblMortgageAssignments->aomExecuted;
                $tblMortgageAssignments->aomExecutedSentForRecording = isset($AOMField['aomExecutedSentForRecording']) ?
                    Database2::strongTypeValue($AOMField['aomExecutedSentForRecording'], Database2::DATATYPE_DATE) : $tblMortgageAssignments->aomExecutedSentForRecording;
                $tblMortgageAssignments->aomDateRecorded = isset($AOMField['aomDateRecorded']) ?
                    Database2::strongTypeValue($AOMField['aomDateRecorded'], Database2::DATATYPE_DATE) : $tblMortgageAssignments->aomDateRecorded;
                $tblMortgageAssignments->aomDateSentToCustodian = isset($AOMField['aomDateSentToCustodian']) ?
                    Database2::strongTypeValue($AOMField['aomDateSentToCustodian'], Database2::DATATYPE_DATE) : $tblMortgageAssignments->aomDateSentToCustodian;
                $tblMortgageAssignments->aomSourceOfDelivery = isset($AOMField['aomSourceOfDelivery']) ? Database2::strongTypeValue($AOMField['aomSourceOfDelivery'], Database2::DATATYPE_NUMBER) : $tblMortgageAssignments->aomSourceOfDelivery;
                $tblMortgageAssignments->aomSourceOfDeliveryTracking = $AOMField['aomSourceOfDeliveryTracking'] ?? $tblMortgageAssignments->aomSourceOfDeliveryTracking;

                $tblMortgageAssignments->aomRecordedCopySentToCustodian = isset($AOMField['aomRecordedCopySentToCustodian']) ? Database2::strongTypeValue($AOMField['aomRecordedCopySentToCustodian'], Database2::DATATYPE_NUMBER) : $tblMortgageAssignments->aomRecordedCopySentToCustodian;

                $tblMortgageAssignments->aomCompletedBy = isset($AOMField['aomCompletedBy']) ? Database2::strongTypeValue($AOMField['aomCompletedBy'], Database2::DATATYPE_NUMBER) : $tblMortgageAssignments->aomCompletedBy;

                $tblMortgageAssignments->aomCompletedDate = isset($AOMField['aomCompletedDate']) ?
                    Database2::strongTypeValue($AOMField['aomCompletedDate'], Database2::DATATYPE_DATE) : $tblMortgageAssignments->aomCompletedDate;
                $tblMortgageAssignments->LMRId = self::$LMRId;
                $tblMortgageAssignments->save();
            }
        }
    }

    /**
     * @return void
     */
    public static function AOMSave(): void
    {
        $tblAssignmentOfMortgage = tblAssignmentOfMortgage::Get(['LMRId' => self::$LMRId]) ?? new tblAssignmentOfMortgage();
        $tblAssignmentOfMortgage->LMRId = self::$LMRId;
        $tblAssignmentOfMortgage->executedAOM2 = Request::isset('executedAOM2') ?
            Database2::strongTypeValue(Request::GetClean('executedAOM2'), Database2::DATATYPE_DATE) : $tblAssignmentOfMortgage->executedAOM2;
        $tblAssignmentOfMortgage->AOMComments = Request::isset('AOMComments') ? Request::GetClean('AOMComments') : $tblAssignmentOfMortgage->AOMComments;
        $tblAssignmentOfMortgage->save();
    }

    public static function allongeSave(): void
    {
        $tblAllonges = tblAllonges::Get(['LMRId' => self::$LMRId]) ?? new tblAllonges();
        $tblAllonges->LMRId = self::$LMRId;
        $tblAllonges->executedAllonge1 = Request::isset('executedAllonge1') ?
            Database2::strongTypeValue(Request::GetClean('executedAllonge1'), Database2::DATATYPE_DATE) : $tblAllonges->executedAllonge1;
        $tblAllonges->executedAllonge2 = Request::isset('executedAllonge2') ?
            Database2::strongTypeValue(Request::GetClean('executedAllonge2'), Database2::DATATYPE_DATE) : $tblAllonges->executedAllonge2;
        $tblAllonges->allongeComments = Request::isset('allongeComments') ? Request::GetClean('allongeComments') : $tblAllonges->allongeComments;
        $tblAllonges->save();
    }

    public static function preSaleExceptionSave(): void
    {

        if (Request::isset('PreSaleExceptionFields') && is_array(Request::GetClean('PreSaleExceptionFields'))) {
            foreach (Request::GetClean('PreSaleExceptionFields') as
                     $preSaleExceptionSerial => $preSaleExceptionField) {
                if (!array_filter($preSaleExceptionField)) {
                    continue;
                }
                $tblPreSaleExceptions = tblPreSaleExceptions::Get([
                    'LMRId' => self::$LMRId,
                    'id'    => cypher::myDecryption($preSaleExceptionField['id']),
                ]) ?? new tblPreSaleExceptions();
                $tblPreSaleExceptions->preSaleExceptionType = isset($preSaleExceptionField['preSaleExceptionType']) ?
                    Database2::strongTypeValue($preSaleExceptionField['preSaleExceptionType'], Database2::DATATYPE_NUMBER) : $tblPreSaleExceptions->preSaleExceptionType;
                $tblPreSaleExceptions->dateExceptionIssued = isset($preSaleExceptionField['dateExceptionIssued']) ?
                    Database2::strongTypeValue($preSaleExceptionField['dateExceptionIssued'], Database2::DATATYPE_DATE) : $tblPreSaleExceptions->dateExceptionIssued;
                $tblPreSaleExceptions->dateExceptionSentToInvestor = isset($preSaleExceptionField['dateExceptionSentToInvestor']) ?
                    Database2::strongTypeValue($preSaleExceptionField['dateExceptionSentToInvestor'], Database2::DATATYPE_DATE) : $tblPreSaleExceptions->dateExceptionSentToInvestor;
                $tblPreSaleExceptions->exceptionSourceOfDeliveryToCustodian = isset($preSaleExceptionField['exceptionSourceOfDeliveryToCustodian']) ? Database2::strongTypeValue($preSaleExceptionField['exceptionSourceOfDeliveryToCustodian'], Database2::DATATYPE_NUMBER) : $tblPreSaleExceptions->exceptionSourceOfDeliveryToCustodian;
                $tblPreSaleExceptions->dateExceptionSentToInvestor = isset($preSaleExceptionField['dateExceptionSentToInvestor']) ?
                    Database2::strongTypeValue($preSaleExceptionField['dateExceptionSentToInvestor'], Database2::DATATYPE_DATE) : $tblPreSaleExceptions->dateExceptionSentToInvestor;
                $tblPreSaleExceptions->exceptionTracking = $preSaleExceptionField['exceptionTracking'] ?? $tblPreSaleExceptions->exceptionTracking;
                $tblPreSaleExceptions->preSaleExceptionCompletedBy = isset($preSaleExceptionField['preSaleExceptionCompletedBy']) ? Database2::strongTypeValue($preSaleExceptionField['preSaleExceptionCompletedBy'], Database2::DATATYPE_NUMBER) : $tblPreSaleExceptions->preSaleExceptionCompletedBy;
                $tblPreSaleExceptions->preSaleExceptionComments = $preSaleExceptionField['preSaleExceptionComments'] ?? $tblPreSaleExceptions->preSaleExceptionComments;

                $tblPreSaleExceptions->LMRId = self::$LMRId;
                $tblPreSaleExceptions->save();
            }
        }
    }

    public static function postSaleExceptionSave(): void
    {

        if (Request::isset('PostSaleExceptionFields') && is_array(Request::GetClean('PostSaleExceptionFields'))) {
            foreach (Request::GetClean('PostSaleExceptionFields') as
                     $postSaleExceptionSerial => $postSaleExceptionField) {
                if (!array_filter($postSaleExceptionField)) {
                    continue;
                }
                $tblPostSaleExceptions = tblPostSaleExceptions::Get([
                    'LMRId' => self::$LMRId,
                    'id'    => cypher::myDecryption($postSaleExceptionField['id']),
                ]) ?? new tblPostSaleExceptions();

                $tblPostSaleExceptions->postSaleExceptionType = isset($postSaleExceptionField['postSaleExceptionType']) ?
                    Database2::strongTypeValue($postSaleExceptionField['postSaleExceptionType'], Database2::DATATYPE_NUMBER) : $tblPostSaleExceptions->postSaleExceptionType;

                $tblPostSaleExceptions->postDateExceptionIssued = isset($postSaleExceptionField['postDateExceptionIssued']) ?
                    Database2::strongTypeValue($postSaleExceptionField['postDateExceptionIssued'], Database2::DATATYPE_DATE) : $tblPostSaleExceptions->postDateExceptionIssued;

                $tblPostSaleExceptions->postDateExceptionSentToInvestor = isset($postSaleExceptionField['postDateExceptionSentToInvestor']) ?
                    Database2::strongTypeValue($postSaleExceptionField['postDateExceptionSentToInvestor'], Database2::DATATYPE_DATE) : $tblPostSaleExceptions->postDateExceptionSentToInvestor;

                $tblPostSaleExceptions->postExceptionSourceOfDeliveryToCustodian = isset($postSaleExceptionField['postExceptionSourceOfDeliveryToCustodian']) ? Database2::strongTypeValue($postSaleExceptionField['postExceptionSourceOfDeliveryToCustodian'], Database2::DATATYPE_NUMBER) : $tblPostSaleExceptions->postExceptionSourceOfDeliveryToCustodian;

                $tblPostSaleExceptions->postExceptionTracking = $postSaleExceptionField['postExceptionTracking'] ?? $tblPostSaleExceptions->postExceptionTracking;


                $tblPostSaleExceptions->postSaleExceptionCompletedBy = isset($postSaleExceptionField['postSaleExceptionCompletedBy']) ? Database2::strongTypeValue($postSaleExceptionField['postSaleExceptionCompletedBy'], Database2::DATATYPE_NUMBER) : $tblPostSaleExceptions->postSaleExceptionCompletedBy;
                $tblPostSaleExceptions->postSaleExceptionComments = $postSaleExceptionField['postSaleExceptionComments'] ?? $tblPostSaleExceptions->postSaleExceptionComments;

                $tblPostSaleExceptions->LMRId = self::$LMRId;
                $tblPostSaleExceptions->save();
            }
        }
    }

    public static function getCalender(string  $fieldName,
                                       ?string $value,
                                       string  $fieldId = '',
                                       ?string $allowEdit = null
    ): string
    {
        if (!$allowEdit) {
            return Dates::formatDateWithRE($value, 'YMD', 'm/d/Y');
        }
        return '
    <div class="input-group">
        <div class="input-group-prepend">
            <div class="input-group-text">
                <i class="fa fa-calendar text-primary"></i>
            </div>
        </div>
        <input type="text"
               class="form-control dateNewClass"
               name="' . $fieldName . '"
               id="' . (!$fieldId ? $fieldName : $fieldId) . '"
               value="' . ($value ? Dates::formatDateWithRE($value, 'YMD', 'm/d/Y') : '') . '"
               autocomplete="off"
               placeholder="MM/DD/YYYY"
        />
    </div>
    ';

    }

    public static function getTextbox(string  $fieldName,
                                      ?string $value,
                                      ?string $fieldId = '',
                                      ?string $allowEdit = null

    ): ?string
    {
        if (!$allowEdit) {
            return $value;
        }
        return '
        <input type="text"
               class="form-control "
               name="' . $fieldName . '"
               id="' . (!$fieldId ? $fieldName : $fieldId) . '"
               value="' . ($value) . '"
               autocomplete="off"
        />
    ';
    }

    public static function getSelect(
        string  $fieldName,
        ?string $fieldId = null,
        ?string $value = null,
        ?array  $options = [],
        ?string $allowEdit = null
    ): ?string
    {

        if (!$allowEdit) {
            return $options[$value];
        }

        $html = '
        <select class="chzn-select form-control"
            name="' . $fieldName . '"
            id="' . ($fieldId ?? $fieldName) . '"
            data-placeholder="Please Select">
        <option value=""></option>';
        foreach ($options as $optionKey => $optionValue) {
            $html .= '<option value="' . $optionKey . '" '
                . (Arrays::isSelected($optionKey, $value)) . '>'
                . htmlspecialchars($optionValue) . '</option>';
        }
        $html .= '</select>';
        return $html;
    }

    public static function getTextarea(
        string  $fieldName,
        ?string $fieldId = null,
        ?string $value = null,
        ?string $allowEdit = null
    ): ?string
    {
        if (!$allowEdit) {
            return $value;
        }
        return loanForm::simpleTextArea(
            ($fieldId ?? $fieldName),
            $fieldName,
            $value
        );
    }

    public static function fetchUserInfoByAID($dataArray, $AID): ?string
    {
        foreach ($dataArray as $eachEmployee) {
            if ($eachEmployee->AID == $AID) {
                return $eachEmployee->processorName . ' ' . $eachEmployee->processorLName . '(' . $eachEmployee->role . ')'; // Return the first match
            }
        }
        return null; // Return null if no match is found
    }

    /**
     * @param int|null $PCID
     * @return array|string[]
     */
    public static function getDOTType(?int $PCID): array
    {
        return glPCID::PCID_PROD_CV3 == $PCID ? self::$DOTTypeForCV3 : self::$DOTTypeForOthers;
    }
}
