<?php

namespace models\Controllers\LMRequest;

use models\constants\gl\glPCID;
use models\Controllers\backoffice\LMRequest;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblFileHUDBasicInfo;
use models\lendingwise\tblFileHUDSettlementCharges;
use models\lendingwise\tblFileHUDTransaction;
use models\lendingwise\tblLMRHUDAdditionalCharges;
use models\lendingwise\tblLMRHUDItemsPayableLoan;
use models\lendingwise\tblLMRHUDLenderToPay;
use models\lendingwise\tblLMRHUDReservesDeposit;
use models\Log;
use models\PageVariables;
use models\standard\Currency;
use models\standard\Strings;
use models\types\strongType;

class HUDCalculation extends strongType
{
    public static function process($LMRId)
    {
        if (!$LMRId) {
            return null;
        }
        LMRequest::$LMRId = null;
        LMRequest::setLMRId($LMRId);
        //Log::Insert('LMRId: ' . $LMRId);
        //copy origination and broker points data
        self::updateOriginationAndBrokerPoints($LMRId); //801, 802
        //copy Interest Payment Hold Back data
        self::updateInterestPaymentHoldBack($LMRId); //808 //already calculated in HUDSave.php
        //update 301 Gross Amount due from borrower (line 120)
        self::update301($LMRId); //301
        //update 302 Less amount paid by/for borrower (line 220)
        self::update302($LMRId); //302
        //update 303 Cash at settlement from/to borrower (line 303)
        self::update303($LMRId); //303
    }


    public static function updateOriginationAndBrokerPoints(int $LMRId)
    {
        if (!$LMRId) {
            return;
        }
        $tblFileHMLONewLoanInfo = tblFileHMLONewLoanInfo::Get(['fileID' => $LMRId]);
        if ($tblFileHMLONewLoanInfo) {
            $originationPointsValue = Strings::replaceCommaValues($tblFileHMLONewLoanInfo->originationPointsValue ?? 0);
            $brokerPointsValue = Strings::replaceCommaValues($tblFileHMLONewLoanInfo->brokerPointsValue ?? 0);

            //update tblLMRHUDItemsPayableLoan
            //origination points
            if ($originationPointsValue) {
                $HUD_801 = tblLMRHUDItemsPayableLoan::Get([
                    'LMRID' => $LMRId,
                    'fieldID' => '801'
                ]) ?? new tblLMRHUDItemsPayableLoan();

                $HUD_801->LMRID = $LMRId;
                $HUD_801->fieldID = '801';
                $HUD_801->borrowerSettlementValue = $originationPointsValue;
                $HUD_801->Save();
            }

            //Log::Insert('HUD_801: ' . $HUD_801->borrowerSettlementValue ?? 0);
            //broker points
            if ($brokerPointsValue) {
                $HUD_802 = tblLMRHUDItemsPayableLoan::Get([
                    'LMRID' => $LMRId,
                    'fieldID' => '802'
                ]) ?? new tblLMRHUDItemsPayableLoan();

                $HUD_802->LMRID = $LMRId;
                $HUD_802->fieldID = '802';
                $HUD_802->borrowerSettlementValue = $brokerPointsValue;
                $HUD_802->Save();
            }
            //Log::Insert('HUD_802: ' . $HUD_802->borrowerSettlementValue ?? 0);
        }

    }

    //808 Interest Payment Hold back
    public static function updateInterestPaymentHoldBack($LMRId): void
    {
        $interestPaymentHoldBackMonth = 0;
        $tblFileHUDBasicInfo = tblFileHUDBasicInfo::Get(['LMRID' => $LMRId]) ?? new tblFileHUDBasicInfo();
        $tblFileHUDBasicInfo->LMRID = $LMRId;
        if ($tblFileHUDBasicInfo) {
            $interestPaymentHoldBackMonth = Strings::replaceCommaValues($tblFileHUDBasicInfo->interestPaymentHoldBackMonth ?? 0);
        }
        //Log::Insert('interestPaymentHoldBackMonth: ' . $interestPaymentHoldBackMonth);
        /*if (!$interestPaymentHoldBackMonth) {
            return;
        }*/
        if(LMRequest::File()->FPCID == glPCID::PCID_PROD_CV3
            && LMRequest::myFileInfo()->getPrimaryProperty()->getTblPropertiesAnalysis_by_propertyId()->propertyInterrestOnlyPayment) {
            $interestPaymentHoldBackAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment;
        } else {
            HMLOLoanTermsCalculation::InitForLMRId($LMRId);
            $interestPaymentHoldBackAmount = HMLOLoanTermsCalculation::$totalMonthlyPayment;
        }
        if ($tblFileHUDBasicInfo) {
            $tblFileHUDBasicInfo->interestPaymentHoldBackAmount = $interestPaymentHoldBackAmount;
            $tblFileHUDBasicInfo->Save();
        }
        //Log::Insert('interestPaymentHoldBackAmount: ' . $interestPaymentHoldBackAmount);
        //interest payment hold back - 808
        $HUD_808 = HUD::calculateHUDInterestPaymentTotal($interestPaymentHoldBackAmount, $interestPaymentHoldBackMonth);
        //Log::Insert('HUD_808: ' . $HUD_808);
        //update HUD_808
        if ($HUD_808) {
            $tblLMRHUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::Get([
                'LMRID' => $LMRId,
                'fieldID' => '808'
            ]) ?? new tblLMRHUDItemsPayableLoan();

            $tblLMRHUDItemsPayableLoan->LMRID = $LMRId;
            $tblLMRHUDItemsPayableLoan->fieldID = '808';
            $tblLMRHUDItemsPayableLoan->borrowerSettlementValue = Strings::replaceCommaValues($HUD_808);
            $tblLMRHUDItemsPayableLoan->Save();
        }
        //Log::Insert('HUD_808: ' . $HUD_808);
    }

    public static function update301(int $LMRId)
    {
        if (!$LMRId) {
            return;
        }
        $HUD_V_301 = self::calculate_301($LMRId);
        //Log::Insert('HUD_V_301: ' . $HUD_V_301);
        if ($HUD_V_301) {
            $HUD_301 = tblFileHUDTransaction::Get([
                'LMRID' => $LMRId,
                'fieldID' => 301
            ]) ?? new tblFileHUDTransaction();

            $HUD_301->LMRID = $LMRId;
            $HUD_301->fieldID = 301;
            $HUD_301->fieldValue = $HUD_V_301;
            $HUD_301->Save();
        }
    }

    public static function update302(int $LMRId)
    {
        if (!$LMRId) {
            return;
        }
        $HUD_V_302 = self::calculate_302($LMRId);
        //Log::Insert('HUD_V_302: ' . $HUD_V_302);
        if ($HUD_V_302) {
            $HUD_302 = tblFileHUDTransaction::Get([
                'LMRID' => $LMRId,
                'fieldID' => 302
            ]) ?? new tblFileHUDTransaction();

            $HUD_302->LMRID = $LMRId;
            $HUD_302->fieldID = 302;
            $HUD_302->fieldValue = $HUD_V_302;
            $HUD_302->Save();
        }
    }

    public static function update303(int $LMRId)
    {
        if (!$LMRId) {
            return;
        }
        $HUD_V_303 = self::calculate_303($LMRId);
        //Log::Insert('HUD_V_303: ' . $HUD_V_303);
        if ($HUD_V_303) {
            $HUD_303 = tblFileHUDTransaction::Get([
                'LMRID' => $LMRId,
                'fieldID' => 303
            ]) ?? new tblFileHUDTransaction();

            $HUD_303->LMRID = $LMRId;
            $HUD_303->fieldID = 303;
            $HUD_303->fieldValue = $HUD_V_303;
            $HUD_303->Save();
        }
    }

    //1101 Title Service and Lender's Title Insurance Total (1102-1116)
    public static function calculate_1101($LMRId)
    {
        //get the data from tblLMRHUDAdditionalCharges
        $tblLMRHUDAdditionalCharges = tblLMRHUDAdditionalCharges::GetAll(['LMRID' => $LMRId]);
        $HUD_V_1101 = 0;
        foreach ($tblLMRHUDAdditionalCharges as $item) {
            if ($item->fieldID >= 1102 && $item->fieldID <= 1116) {
                $HUD_V_1101 += Strings::replaceCommaValues($item->borrowerSettlementValue);
            }
        }
        //Log::Insert('HUD_V_1101: ' . $HUD_V_1101);
        return $HUD_V_1101;
    }

    //1201 Government Recording Charges Total (1202-1210)
    public static function calculate_1201($LMRId)
    {
        //get the data from tblLMRHUDAdditionalCharges
        $tblLMRHUDAdditionalCharges = tblLMRHUDAdditionalCharges::GetAll(['LMRID' => $LMRId]);
        $HUD_V_1201 = 0;
        foreach ($tblLMRHUDAdditionalCharges as $item) {
            if ($item->fieldID >= 1202 && $item->fieldID <= 1210) {
                $HUD_V_1201 += Strings::replaceCommaValues($item->borrowerSettlementValue);
            }
        }
        //Log::Insert('HUD_V_1201: ' . $HUD_V_1201);
        return $HUD_V_1201;
    }

    //1300 Additional Settlement Charges (1301-1320)
    public static function calculate_1300($LMRId)
    {
        //get the data from tblLMRHUDAdditionalCharges
        $tblLMRHUDAdditionalCharges = tblLMRHUDAdditionalCharges::GetAll(['LMRID' => $LMRId]);
        $HUD_V_1300 = 0;
        foreach ($tblLMRHUDAdditionalCharges as $item) {
            if ($item->fieldID >= 1301 && $item->fieldID <= 1320) {
                $HUD_V_1300 += Strings::replaceCommaValues($item->borrowerSettlementValue);
            }
        }
        //Log::Insert('HUD_V_1300: ' . $HUD_V_1300);
        return $HUD_V_1300;
    }

    //1000 Reserves Deposited With Lender(1002-1008)
    public static function calculate_1000($LMRId)
    {
        //get the data from tblLMRHUDReservesDeposit
        $tblLMRHUDReservesDeposit = tblLMRHUDReservesDeposit::GetAll(['LMRID' => $LMRId]);
        $HUD_V_1000 = 0;
        foreach ($tblLMRHUDReservesDeposit as $item) {
            if ($item->fieldID >= 1002 && $item->fieldID <= 1008) {
                $HUD_V_1000 += Strings::replaceCommaValues($item->borrowerSettlementValue);
            }
        }
        //Log::Insert('HUD_V_1000: ' . $HUD_V_1000);
        return $HUD_V_1000;
    }

    //Calculate 901
    public static function calculate_901($LMRId)
    {
        $interestChargedFromDate = tblFileHMLONewLoanInfo::Get([
            'fileID' => $LMRId
        ])->interestChargedFromDate;

        $interestChargedEndDate = tblFileHMLONewLoanInfo::Get([
            'fileID' => $LMRId
        ])->interestChargedEndDate;

        $ActualDays = HUD::calculateNoOfDays($interestChargedFromDate, $interestChargedEndDate, !PageVariables::$nonInclusivePerDiem);

        $interestRate = tblFile::Get([
            'LMRID' => $LMRId
        ])->lien1Rate;
        $HUD_V_202 = tblFileHUDTransaction::Get([
            'LMRID' => $LMRId,
            'fieldID' => 202
        ])->fieldValue;
        $HUD_V_202 = Strings::replaceCommaValues($HUD_V_202);
        $HUD_BS_806 = tblLMRHUDItemsPayableLoan::Get([
            'LMRID' => $LMRId,
            'fieldID' => 806
        ])->borrowerSettlementValue;

        $PerDiemInterest = HUD::calculatePerDiemInterestHUD($interestRate, $HUD_V_202, $HUD_BS_806);
        $DailyInterestChanges = HUD::calculateDailyInterestChanges(Strings::replaceCommaValues($PerDiemInterest), $ActualDays);
        //update 901
        $HUD_901 = tblLMRHUDLenderToPay::Get([
            'LMRID' => $LMRId,
            'fieldID' => 901
        ]) ?? new tblLMRHUDLenderToPay();

        $HUD_901->LMRID = $LMRId;
        $HUD_901->fieldID = 901;
        $HUD_901->borrowerSettlementValue = Strings::replaceCommaValues($DailyInterestChanges);
        $HUD_901->Save();
    }

    //900 Items Required By Lender To Be Paid In Advance (901-905)
    public static function calculate_900($LMRId)
    {
        //calculate 901
        self::calculate_901($LMRId);

        //get the data from tblLMRHUDLenderToPay
        $tblLMRHUDLenderToPay = tblLMRHUDLenderToPay::GetAll(['LMRID' => $LMRId]);
        $HUD_V_900 = 0;
        foreach ($tblLMRHUDLenderToPay as $item) {
            if ($item->fieldID >= 901 && $item->fieldID <= 905) {
                $HUD_V_900 += Strings::replaceCommaValues($item->borrowerSettlementValue);
            }
        }
        //Log::Insert('HUD_V_900: ' . $HUD_V_900);
        return $HUD_V_900;
    }

    //800 Items Payable In Connection With Loan (801-813)
    public static function calculate_800($LMRId)
    {
        //get the data from tblLMRHUDItemsPayableLoan
        $tblLMRHUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::GetAll(['LMRID' => $LMRId]);
        $HUD_V_800 = 0;
        foreach ($tblLMRHUDItemsPayableLoan as $item) {
            if ($item->fieldID >= 801 && $item->fieldID <= 813) {
                $HUD_V_800 += Strings::replaceCommaValues($item->borrowerSettlementValue);
            }
        }
        //Log::Insert('HUD_V_800: ' . $HUD_V_800);
        return $HUD_V_800;
    }

    //700 Total Sales/ Broker's Commission (703-706)
    public static function calculate_700($LMRId)
    {
        //get the data from tblFileHUDSettlementCharges
        $tblFileHUDSettlementCharges = tblFileHUDSettlementCharges::GetAll(['LMRID' => $LMRId]);
        $HUD_V_700 = 0;
        foreach ($tblFileHUDSettlementCharges as $item) {
            if ($item->fieldID >= 703 && $item->fieldID <= 706) {
                $HUD_V_700 += Strings::replaceCommaValues($item->borrowerSettlementValue);
            }
        }
        //Log::Insert('HUD_V_700: ' . $HUD_V_700);
        return $HUD_V_700;
    }

    public static function calculate_1400($LMRId): float
    {
        $HUD_BS_1101 = self::calculate_1101($LMRId);
        $HUD_BS_1201 = self::calculate_1201($LMRId);
        $HUD_BS_1301_1320 = self::calculate_1300($LMRId);
        $HUD_BS_1002_1008 = self::calculate_1000($LMRId);
        $HUD_BS_901_905 = self::calculate_900($LMRId);
        $HUD_BS_801_813 = self::calculate_800($LMRId);
        $HUD_BS_703_706 = self::calculate_700($LMRId);

        return HUD::calculateTotalSettlementCharges(
            $HUD_BS_1101,
            $HUD_BS_1201,
            $HUD_BS_1301_1320,
            $HUD_BS_1002_1008,
            $HUD_BS_901_905,
            $HUD_BS_801_813,
            $HUD_BS_703_706
        );
    }

    public static function calculate_301($LMRId): float //301/120/ Sum(101-112)
    {
        //update 103 Settlement Charges to Borrower (line1400)
        $HUD_103 = tblFileHUDTransaction::Get([
            'LMRID' => $LMRId,
            'fieldID' => 103
        ]) ?? new tblFileHUDTransaction();

        $HUD_103->LMRID = $LMRId;
        $HUD_103->fieldID = 103;
        $HUD_103->fieldValue = self::calculate_1400($LMRId);
        //Log::Insert('HUD_103: ' . $HUD_103->fieldValue);
        //Log::Insert('HUD_1400: ' . $HUD_103->fieldValue);
        $HUD_103->Save();
        //get the data from tblFileHUDTransaction
        $tblFileHUDTransaction = tblFileHUDTransaction::GetAll(['LMRID' => $LMRId]);
        $HUD_V_301 = 0;
        foreach ($tblFileHUDTransaction as $item) {
            if ($item->fieldID >= 101 && $item->fieldID <= 112) {
                $HUD_V_301 += Strings::replaceCommaValues($item->fieldValue);
            }
        }
        //Save 120 Gross Amount due from borrower (line 301) // Sum(101-112)
        $HUD_120 = tblFileHUDTransaction::Get([
            'LMRID' => $LMRId,
            'fieldID' => 120
        ]) ?? new tblFileHUDTransaction();
        $HUD_120->LMRID = $LMRId;
        $HUD_120->fieldID = 120;
        $HUD_120->fieldValue = $HUD_V_301;
        $HUD_120->Save();

        return $HUD_V_301;
    }

    //200 Amounts Paid By On In Behalf Of Borrower (201-219)
    public static function calculate_302($LMRId) //302/220
    {
        //get the data from tblFileHUDTransaction
        $tblFileHUDTransaction = tblFileHUDTransaction::GetAll(['LMRID' => $LMRId]);
        $HUD_V_200 = 0;
        foreach ($tblFileHUDTransaction as $item) {
            if ($item->fieldID >= 201 && $item->fieldID <= 219) {
                $HUD_V_200 += Strings::replaceCommaValues($item->fieldValue);
            }
        }
        //Log::Insert('HUD_V_200: ' . $HUD_V_200);
        return $HUD_V_200;
    }

    public static function calculate_303($LMRId): float //303/Sum(301-302)
    {
        $HUD_301 = tblFileHUDTransaction::Get([
            'LMRID' => $LMRId,
            'fieldID' => 301
        ]);

        $HUD_302 = tblFileHUDTransaction::Get([
            'LMRID' => $LMRId,
            'fieldID' => 302
        ]);
        //Log::Insert('HUD_301: ' . $HUD_301->fieldValue);
        //Log::Insert('HUD_302: ' . $HUD_302->fieldValue);
        //Log::Insert('HUD_303: ' . ($HUD_301->fieldValue + $HUD_302->fieldValue));
        return Strings::replaceCommaValues($HUD_301->fieldValue ?? 0)
            + Strings::replaceCommaValues($HUD_302->fieldValue ?? 0);
    }

    public static function total_HUD_801_804_807_809($HUD_SS_801,$HUD_SS_804,$HUD_SS_807,$HUD_SS_809)
    {
        return $HUD_SS_801 + $HUD_SS_804 + $HUD_SS_807 + $HUD_SS_809;
    }

    /**
     * @param $LMRId
     * @return float
     */
    public static function fundingWorksheetTotalDebits($LMRId): float
    {
        $fundingWorksheetTotalDebits = 0;
        $totalDebitFields = [202, 207, 208, 209];
        $totalDebits = tblFileHUDTransaction::GetAll(['LMRID' => $LMRId]);
        if ($totalDebits) {
            foreach ($totalDebits as $debit) {
                if (in_array($debit->fieldID, $totalDebitFields)) {
                    $fundingWorksheetTotalDebits += Strings::replaceCommaValues($debit->fieldValue);
                }
            }
        }
//        Log::Insert('fundingWorksheetTotalDebits: ' . $fundingWorksheetTotalDebits);
        return $fundingWorksheetTotalDebits;
    }

    /**
     * @param $LMRId
     * @return float|int
     */

    public static function fundingWorksheetTotalCredits($LMRId)
    {
        $fundingWorksheetTotalCredits = 0;
        $totalCreditFields = [801, 804, 809, 810, 811, 812, 806, 807, 808, 805];
        $tblLMRHUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::GetAll(['LMRID' => $LMRId]);
        if ($tblLMRHUDItemsPayableLoan) {
            foreach ($tblLMRHUDItemsPayableLoan as $credit) {
                if (in_array($credit->fieldID, $totalCreditFields)) {
                    $fundingWorksheetTotalCredits += Strings::replaceCommaValues($credit->borrowerSettlementValue);
                }
            }
        }
//        Log::Insert('fundingWorksheetTotalCredits: ' . $fundingWorksheetTotalCredits);
        $totalCreditFields_2 = [1002, 1003, 1004];
        $tblLMRHUDReservesDeposit = tblLMRHUDReservesDeposit::GetAll(['LMRID' => $LMRId]);
        if ($tblLMRHUDReservesDeposit) {
            foreach ($tblLMRHUDReservesDeposit as $credit) {
                if (in_array($credit->fieldID, $totalCreditFields_2)) {
                    $fundingWorksheetTotalCredits += Strings::replaceCommaValues($credit->borrowerSettlementValue);
                }
            }
        }
//        Log::Insert('fundingWorksheetTotalCredits: ' . $fundingWorksheetTotalCredits);
        //901
        $tblLMRHUDLenderToPay = tblLMRHUDLenderToPay::Get([
            'LMRID'   => $LMRId,
            'fieldID' => 901
        ]);
        if ($tblLMRHUDLenderToPay) {
            $fundingWorksheetTotalCredits += Strings::replaceCommaValues($tblLMRHUDLenderToPay->borrowerSettlementValue);
        }
//        Log::Insert('fundingWorksheetTotalCredits: ' . $fundingWorksheetTotalCredits);
        //financedInterestReserve

        $tblLoanPropertySummary = LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserve;
        if ($tblLoanPropertySummary) {
            $fundingWorksheetTotalCredits += Strings::replaceCommaValues($tblLoanPropertySummary);
        }
//        Log::Insert('fundingWorksheetTotalCredits: ' . $fundingWorksheetTotalCredits);
        return $fundingWorksheetTotalCredits;
    }


    /**
     * @param $LMRId
     * @return float|int
     */
    public static function fundingWorksheetWireTransferAmount($LMRId)
    {
        $fundingWorksheetTotalCredits = self::fundingWorksheetTotalCredits($LMRId);
        $fundingWorksheetTotalDebits = self::fundingWorksheetTotalDebits($LMRId) * (-1);


        $fundingWorksheetWireTransferAmount = $fundingWorksheetTotalDebits - ($fundingWorksheetTotalCredits);
//        Log::Insert('fundingWorksheetWireTransferAmount: ' . $fundingWorksheetWireTransferAmount);
        return $fundingWorksheetWireTransferAmount;
    }

    public static function getPrincipalAmountOfNewLoans($LMRId): float
    {
        $HUD_V_202 = tblFileHUDTransaction::Get([
            'LMRID' => $LMRId,
            'fieldID' => 202
        ])->fieldValue ?? 0;
        return Strings::replaceCommaValues($HUD_V_202);
    }

    /**
     * @param $LMRId
     * @return array
     */
    public static function getAppraisalFeeDue($LMRId): array
    {
        $HUD_BS_810 = 0;
        $HUD_BS_811 = 0;
        $HUD_BS_812 = 0;

        $tblLMRHUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::GetAll(['LMRID' => $LMRId]);
        if ($tblLMRHUDItemsPayableLoan) {
            foreach ($tblLMRHUDItemsPayableLoan as $appraisal) {
                if($appraisal->fieldID == 810) {
                    $HUD_BS_810 = Strings::replaceCommaValues($appraisal->borrowerSettlementValue);
                }
                if($appraisal->fieldID == 811) {
                    $HUD_BS_811 = Strings::replaceCommaValues($appraisal->borrowerSettlementValue);
                }
                if($appraisal->fieldID == 812) {
                    $HUD_BS_812 = Strings::replaceCommaValues($appraisal->borrowerSettlementValue);
                }
            }
        }
//        Log::Insert('HUD_BS_810: ' . $HUD_BS_810);
//        Log::Insert('HUD_BS_811: ' . $HUD_BS_811);
//        Log::Insert('HUD_BS_812: ' . $HUD_BS_812);
        return [
            'HUD_BS_810' => $HUD_BS_810,
            'HUD_BS_811' => $HUD_BS_811,
            'HUD_BS_812' => $HUD_BS_812
        ];
    }




}
