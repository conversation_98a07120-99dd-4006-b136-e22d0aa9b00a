<?php

namespace models\Controllers\LMRequest;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\loanProgram;
use models\constants\packageId;
use models\Controllers\backoffice\LMRequest;
use models\cypher;
use models\Database2;
use models\lendingwise\tblFileCalculatedValues;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblLMRHUDItemsPayableLoan;
use models\standard\Dates;
use models\standard\Maths;
use models\standard\Strings;
use models\types\strongType;

class HUD extends strongType
{

    public static string $date_before = '2024-11-07';
    public static string $date_after = '2024-11-07';

    public static string $date_new = '2025-04-16';


    /**
     * @param $param301
     * @param $param302
     * @return mixed
     */
    public static function calculateCashFromToBorrower($param301, $param302)
    {
        return $param301 + $param302; //Sum(301-302)
    }

    /**
     * @param $param810
     * @param $param811
     * @param $param812
     * @return mixed
     */
    public static function calculateAppraisalFeeTotal($param810, $param811, $param812)
    {
        return $param810 + $param811 + $param812;
    }

    /**
     * @param $param810
     * @param $param811
     * @param $param812
     * @return mixed
     */
    public static function calculateAppraisalFeePOC($param810, $param811, $param812)
    {
        return $param810 + $param811 + $param812;
    }

    /**
     * @param $param1
     * @param $param2
     * @return mixed
     */
    public static function calculateAppraisalFeeTotalFinal($param1, $param2)
    {
        return $param1 - $param2;
    }

    /**
     * @param $totalLoanAmount
     * @param $rehab
     * @param $interestRate
     * @return string
     */
    public static function calculateHUDInterestPayment($totalLoanAmount, $rehab, $interestRate): string
    {
         $interestPayment = (Strings::replaceCommaValues($totalLoanAmount) - Strings::replaceCommaValues($rehab)) * round((Strings::replaceCommaValues($interestRate) / 1200), 8);
         return Strings::replaceCommaValues($interestPayment);
    }

    public static function calculateHUDInterestPaymentTotal($interestPayment, $noOfMonths)
    {
        return round($interestPayment, 2) * $noOfMonths;
    }

    /**
     * @param $HUD_V_101
     * @param $HUD_V_102
     * @param $HUD_V_103
     * @param $HUD_V_104
     * @param $HUD_V_105
     * @param $HUD_V_106
     * @param $HUD_V_107
     * @param $HUD_V_108
     * @param $HUD_V_109
     * @param $HUD_V_110
     * @param $HUD_V_111
     * @param $HUD_V_112
     * @return float
     */
    public static function calculateGrossAmountDueFromBorrower(
        $HUD_V_101,
        $HUD_V_102,
        $HUD_V_103,
        $HUD_V_104,
        $HUD_V_105,
        $HUD_V_106,
        $HUD_V_107,
        $HUD_V_108,
        $HUD_V_109,
        $HUD_V_110,
        $HUD_V_111,
        $HUD_V_112
    ): float
    {
        return Strings::replaceCommaValues($HUD_V_101)
            + Strings::replaceCommaValues($HUD_V_102)
            + Strings::replaceCommaValues($HUD_V_103)
            + Strings::replaceCommaValues($HUD_V_104)
            + Strings::replaceCommaValues($HUD_V_105)
            + Strings::replaceCommaValues($HUD_V_106)
            + Strings::replaceCommaValues($HUD_V_107)
            + Strings::replaceCommaValues($HUD_V_108)
            + Strings::replaceCommaValues($HUD_V_109)
            + Strings::replaceCommaValues($HUD_V_110)
            + Strings::replaceCommaValues($HUD_V_111)
            + Strings::replaceCommaValues($HUD_V_112);
    }

    public static function calculateTotalPaidByForBorrower(
        $HUD_V_201,
        $HUD_V_202,
        $HUD_V_203,
        $HUD_V_204,
        $HUD_V_205,
        $HUD_V_206,
        $HUD_V_207,
        $HUD_V_208,
        $HUD_V_209,
        $HUD_V_210,
        $HUD_V_211,
        $HUD_V_212,
        $HUD_V_213,
        $HUD_V_214,
        $HUD_V_215,
        $HUD_V_216,
        $HUD_V_217,
        $HUD_V_218,
        $HUD_V_219
    ): float
    {
        return Strings::replaceCommaValues($HUD_V_201)
            + Strings::replaceCommaValues($HUD_V_202)
            + Strings::replaceCommaValues($HUD_V_203)
            + Strings::replaceCommaValues($HUD_V_204)
            + Strings::replaceCommaValues($HUD_V_205)
            + Strings::replaceCommaValues($HUD_V_206)
            + Strings::replaceCommaValues($HUD_V_207)
            + Strings::replaceCommaValues($HUD_V_208)
            + Strings::replaceCommaValues($HUD_V_209)
            + Strings::replaceCommaValues($HUD_V_210)
            + Strings::replaceCommaValues($HUD_V_211)
            + Strings::replaceCommaValues($HUD_V_212)
            + Strings::replaceCommaValues($HUD_V_213)
            + Strings::replaceCommaValues($HUD_V_214)
            + Strings::replaceCommaValues($HUD_V_215)
            + Strings::replaceCommaValues($HUD_V_216)
            + Strings::replaceCommaValues($HUD_V_217)
            + Strings::replaceCommaValues($HUD_V_218)
            + Strings::replaceCommaValues($HUD_V_219);
    }

    public static function calculateInitialDepositEscrowAccount(
        $HUD_BS_1002,
        $HUD_BS_1003,
        $HUD_BS_1004
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_1002)
            + Strings::replaceCommaValues($HUD_BS_1003)
            + Strings::replaceCommaValues($HUD_BS_1004);
    }

    public static function calculateReservesDepositWithLender($month, $amount): float
    {
        return Strings::replaceCommaValues($month) * Strings::replaceCommaValues($amount);
    }

    /**
     * @return float|null
     */
    public static function getTotalLoanAmountHUD(): ?float
    {
        $LMRId = LMRequest::File()->LMRId;
        $tblFileCalculatedValues = tblFileCalculatedValues::Get(['LMRId' => $LMRId]);
        if ($tblFileCalculatedValues) {
            $loanProgram = LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType;
            if (in_array($loanProgram, [
                loanProgram::CV3_BRIDGE_LOAN,
                loanProgram::CV3_RENTAL_LOAN,
                loanProgram::CV3_BRIDGE_LOAN_PORTFOLIO,
                loanProgram::CV3_RENTAL_LOAN_PORTFOLIO,
            ])) {
                return $tblFileCalculatedValues->TotalLoanAmount;
            }
            if ($loanProgram == loanProgram::CV3_BRIDGE_LOAN_RENOVATION) {
                return $tblFileCalculatedValues->InitialLoanAmount;
            }
        }
        return null;
    }

    /**
     * @param $interest
     * @param $HUD_V_202
     * @param $HUD_BS_806
     * @return string
     */
    public static function calculatePerDiemInterestHUD($interest, $HUD_V_202, $HUD_BS_806): string
    {
        $interest = Strings::replaceCommaValues($interest);
        $interest /= 100; //Interest Rate
        $interest /= 360;

        $perDiemInterest = $interest * ((Strings::replaceCommaValues($HUD_V_202) + Strings::replaceCommaValues($HUD_BS_806)) * -1);//([Interest Rate]/360) * ((Line 202 + Line 806) * -1)
        if (glCustomJobForProcessingCompany::isPC_CV3(LMRequest::File()->FPCID)) {
            //$perDiemInterest = $perDiemInterest * 100 / 100;
            $loanCreatedDate = LMRequest::File()->recordDate;
            $loanCreatedDateInt = Dates::DateToInt($loanCreatedDate);
            $dateCreatedAfterInt = Dates::DateToInt(self::$date_after);
            $dateCreatedBeforeInt = Dates::DateToInt(self::$date_before);
            if ($loanCreatedDateInt >= $dateCreatedAfterInt) {
                return $perDiemInterest;
            } elseif ($loanCreatedDateInt < $dateCreatedBeforeInt) {
                return Maths::mRound($perDiemInterest, 0.01);
            }
            return Maths::mRound($perDiemInterest, 0.01);
        } else {
            return floor($perDiemInterest * 100) / 100;
        }
    }

    public static function calculateNoOfDays($startDate, $endDate, bool $inclusive = true): int
    {
        if (Dates::IsEmpty($startDate) || Dates::IsEmpty($endDate)) {
            return 0;
        }

        return Dates::DaysDiff($startDate, $endDate, $inclusive);
    }

    /**
     * @param $PerDiemInterest
     * @param $noOfDays
     * @return float
     */
    public static function calculateDailyInterestChanges($PerDiemInterest, $noOfDays): float
    {
        $loanCreatedDate = LMRequest::File()->recordDate;
        $loanCreatedDateInt = Dates::DateToInt($loanCreatedDate);
        $dateCreatedAfterInt = Dates::DateToInt(self::$date_after);
        if ($loanCreatedDateInt >= $dateCreatedAfterInt) {
            $DailyInterestChanges = Maths::mRound(($PerDiemInterest * $noOfDays), 0.01);
        } else {
            $DailyInterestChanges = round($PerDiemInterest, 2) * $noOfDays;
        }
        return $DailyInterestChanges;
    }


    public static function calculateTotalSalesBrokerCommission(
        $HUD_BS_703,
        $HUD_BS_704,
        $HUD_BS_705,
        $HUD_BS_706
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_703)
            + Strings::replaceCommaValues($HUD_BS_704)
            + Strings::replaceCommaValues($HUD_BS_705)
            + Strings::replaceCommaValues($HUD_BS_706);
    }

    public static function calculateItemsPayableInConnectionWithLoan( //801-813
        $HUD_BS_801,
        $HUD_BS_802,
        $HUD_BS_803,
        $HUD_BS_804,
        $HUD_BS_805,
        $HUD_BS_806,
        $HUD_BS_807,
        $HUD_BS_808,
        $HUD_BS_809,
        $HUD_BS_810,
        $HUD_BS_811,
        $HUD_BS_812,
        $HUD_BS_813
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_801)
            + Strings::replaceCommaValues($HUD_BS_802)
            + Strings::replaceCommaValues($HUD_BS_803)
            + Strings::replaceCommaValues($HUD_BS_804)
            + Strings::replaceCommaValues($HUD_BS_805)
            + Strings::replaceCommaValues($HUD_BS_806)
            + Strings::replaceCommaValues($HUD_BS_807)
            + Strings::replaceCommaValues($HUD_BS_808)
            + Strings::replaceCommaValues($HUD_BS_809)
            + Strings::replaceCommaValues($HUD_BS_810)
            + Strings::replaceCommaValues($HUD_BS_811)
            + Strings::replaceCommaValues($HUD_BS_812)
            + Strings::replaceCommaValues($HUD_BS_813);
    }

    public static function calculateItemsRequiredByLenderToBePaidInAdvance( //901-905
        $HUD_BS_901,
        $HUD_BS_902,
        $HUD_BS_903,
        $HUD_BS_904,
        $HUD_BS_905
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_901)
            + Strings::replaceCommaValues($HUD_BS_902)
            + Strings::replaceCommaValues($HUD_BS_903)
            + Strings::replaceCommaValues($HUD_BS_904)
            + Strings::replaceCommaValues($HUD_BS_905);
    }

    public static function calculateTitleCharges( //1100
        $HUD_BS_1102,
        $HUD_BS_1103,
        $HUD_BS_1104,
        $HUD_BS_1105,
        $HUD_BS_1106,
        $HUD_BS_1107,
        $HUD_BS_1108,
        $HUD_BS_1109,
        $HUD_BS_1110,
        $HUD_BS_1111,
        $HUD_BS_1112,
        $HUD_BS_1113,
        $HUD_BS_1114,
        $HUD_BS_1115,
        $HUD_BS_1116
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_1102)
            + Strings::replaceCommaValues($HUD_BS_1103)
            + Strings::replaceCommaValues($HUD_BS_1104)
            + Strings::replaceCommaValues($HUD_BS_1105)
            + Strings::replaceCommaValues($HUD_BS_1106)
            + Strings::replaceCommaValues($HUD_BS_1107)
            + Strings::replaceCommaValues($HUD_BS_1108)
            + Strings::replaceCommaValues($HUD_BS_1109)
            + Strings::replaceCommaValues($HUD_BS_1110)
            + Strings::replaceCommaValues($HUD_BS_1111)
            + Strings::replaceCommaValues($HUD_BS_1112)
            + Strings::replaceCommaValues($HUD_BS_1113)
            + Strings::replaceCommaValues($HUD_BS_1114)
            + Strings::replaceCommaValues($HUD_BS_1115)
            + Strings::replaceCommaValues($HUD_BS_1116);
    }

    public static function calculateGovernmentRecordingCharges( //1201
        $HUD_BS_1202,
        $HUD_BS_1203,
        $HUD_BS_1204,
        $HUD_BS_1205,
        $HUD_BS_1206,
        $HUD_BS_1207,
        $HUD_BS_1208,
        $HUD_BS_1209,
        $HUD_BS_1210
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_1202)
            + Strings::replaceCommaValues($HUD_BS_1203)
            + Strings::replaceCommaValues($HUD_BS_1204)
            + Strings::replaceCommaValues($HUD_BS_1205)
            + Strings::replaceCommaValues($HUD_BS_1206)
            + Strings::replaceCommaValues($HUD_BS_1207)
            + Strings::replaceCommaValues($HUD_BS_1208)
            + Strings::replaceCommaValues($HUD_BS_1209)
            + Strings::replaceCommaValues($HUD_BS_1210);

    }

    public static function calculateAdditionalSettlementCharges(
        $HUD_BS_1301,
        $HUD_BS_1302,
        $HUD_BS_1303,
        $HUD_BS_1304,
        $HUD_BS_1305,
        $HUD_BS_1306,
        $HUD_BS_1307,
        $HUD_BS_1308,
        $HUD_BS_1309,
        $HUD_BS_1310,
        $HUD_BS_1311,
        $HUD_BS_1312,
        $HUD_BS_1313,
        $HUD_BS_1314,
        $HUD_BS_1315,
        $HUD_BS_1316,
        $HUD_BS_1317,
        $HUD_BS_1318,
        $HUD_BS_1319,
        $HUD_BS_1320
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_1301)
            + Strings::replaceCommaValues($HUD_BS_1302)
            + Strings::replaceCommaValues($HUD_BS_1303)
            + Strings::replaceCommaValues($HUD_BS_1304)
            + Strings::replaceCommaValues($HUD_BS_1305)
            + Strings::replaceCommaValues($HUD_BS_1306)
            + Strings::replaceCommaValues($HUD_BS_1307)
            + Strings::replaceCommaValues($HUD_BS_1308)
            + Strings::replaceCommaValues($HUD_BS_1309)
            + Strings::replaceCommaValues($HUD_BS_1310)
            + Strings::replaceCommaValues($HUD_BS_1311)
            + Strings::replaceCommaValues($HUD_BS_1312)
            + Strings::replaceCommaValues($HUD_BS_1313)
            + Strings::replaceCommaValues($HUD_BS_1314)
            + Strings::replaceCommaValues($HUD_BS_1315)
            + Strings::replaceCommaValues($HUD_BS_1316)
            + Strings::replaceCommaValues($HUD_BS_1317)
            + Strings::replaceCommaValues($HUD_BS_1318)
            + Strings::replaceCommaValues($HUD_BS_1319)
            + Strings::replaceCommaValues($HUD_BS_1320);
    }


    public static function calculateTotalSettlementCharges(
        $HUD_BS_1101,
        $HUD_BS_1201,
        $HUD_BS_1301_1320,
        $HUD_BS_1001,
        $HUD_BS_901_905,
        $HUD_BS_801_813,
        $HUD_BS_700_706
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_1101)
            + Strings::replaceCommaValues($HUD_BS_1201)
            + Strings::replaceCommaValues($HUD_BS_1301_1320)
            + Strings::replaceCommaValues($HUD_BS_1001)
            + Strings::replaceCommaValues($HUD_BS_901_905)
            + Strings::replaceCommaValues($HUD_BS_801_813)
            + Strings::replaceCommaValues($HUD_BS_700_706);
    }

    public static function calculateReservesDepositedWithLender(
        $HUD_BS_1002,
        $HUD_BS_1003,
        $HUD_BS_1004,
        $HUD_BS_1005,
        $HUD_BS_1006,
        $HUD_BS_1007,
        $HUD_BS_1008
    ): float
    {
        return Strings::replaceCommaValues($HUD_BS_1002)
            + Strings::replaceCommaValues($HUD_BS_1003)
            + Strings::replaceCommaValues($HUD_BS_1004)
            + Strings::replaceCommaValues($HUD_BS_1005)
            + Strings::replaceCommaValues($HUD_BS_1006)
            + Strings::replaceCommaValues($HUD_BS_1007)
            + Strings::replaceCommaValues($HUD_BS_1008);
    }

    /**
     * @param $PCID
     * @return mixed|string
     */
    public static function getUnderwritingFeeHUD($PCID)
    {
        $return = '';
        if (self::isPCAndFileStatus()) {
            $loanProgram = LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType; //Loan Program
            if ($loanProgram) {
                $sql = 'select
            t100.underwritingFees
            FROM tblPCHMLOBasicLoanInfo t100
            LEFT JOIN tblPCHMLOBasicLoanPgmInfo t200 ON t100.BLID = t200.BLID
            WHERE t100.PCID = :PCID  and t200.loanPgm = :loanPgm';
                $params = [
                    'PCID' => $PCID,
                    'loanPgm' => $loanProgram
                ];
                $result = Database2::getInstance()->queryData($sql, $params, null, true);
                if ($result) {
                    //save in the hud table
                    $return = $result['underwritingFees'];
                    self::setUnderwritingFeeHUD($return);
                }
            }
        }
        return $return;
    }

    /**
     * @return void
     */

    public static function getSetUnderwritingFeeHUD()
    {
        $return = 0;
        $fileCreatedDate = LMRequest::File()->recordDate;
        if ($fileCreatedDate >= self::$date_new) {
            //get total loan amount from Loan Info V2
            $totalLoanAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount;
            if ($totalLoanAmount < 2000000.00) {
                $return = 1495;
            } elseif ($totalLoanAmount >= 2000000.00) {
                $return = 1995;
            }
            self::setUnderwritingFeeHUD($return);
        }
    }

    /**
     * @param $underwritingFees
     * @return void
     */
    public static function setUnderwritingFeeHUD($underwritingFees)
    {
        //save in the hud table
        $LMRId = LMRequest::File()->LMRId;
        $tblLMRHUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::Get([
            'LMRID' => $LMRId
            , 'fieldID' => 804
        ]);
        if (!$tblLMRHUDItemsPayableLoan) {
            $tblLMRHUDItemsPayableLoan = new tblLMRHUDItemsPayableLoan();
            $tblLMRHUDItemsPayableLoan->LMRID = $LMRId;
            $tblLMRHUDItemsPayableLoan->fieldID = 804;
        }
        $tblLMRHUDItemsPayableLoan->borrowerSettlementValue = Strings::replaceCommaValues($underwritingFees);
        $tblLMRHUDItemsPayableLoan->Save();
    }


    /**
     * @param $PCID
     * @return mixed|string
     */
    public static function getProcessingFeeHUD($PCID)
    {
        $return = '';
        if (self::isPCAndFileStatus()) {
            $loanProgram = LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType; //Loan Program
            if ($loanProgram) {
                $sql = 'select
            t100.processingFee
            FROM tblPCHMLOBasicLoanInfo t100
            LEFT JOIN tblPCHMLOBasicLoanPgmInfo t200 ON t100.BLID = t200.BLID
            WHERE t100.PCID = :PCID  and t200.loanPgm = :loanPgm';
                $params = [
                    'PCID' => $PCID,
                    'loanPgm' => $loanProgram
                ];
                $result = Database2::getInstance()->queryData($sql, $params, null, true);
                if ($result) {
                    //save in the hud table
                    $return = $result['processingFee'];
                    self::setProcessingFeeHUD($return);
                }
            }
        }
        return $return;
    }

    /**
     * @return void
     */
    public static function getSetProcessingFeeHUD()
    {
        $return = 0;
        $fileCreatedDate = LMRequest::File()->recordDate;
        if ($fileCreatedDate >= self::$date_new) {
            //get total loan amount from Loan Info V2
            $totalLoanAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount;
            if ($totalLoanAmount < 2000000.00) {
                $return = 745;
            } elseif ($totalLoanAmount >= 2000000.00) {
                $return = 995;
            }
            self::setProcessingFeeHUD($return);
        }
    }

    /**
     * @param $processingFee
     * @return void
     */
    public static function setProcessingFeeHUD($processingFee)
    {
        $LMRId = LMRequest::File()->LMRId;
        $tblLMRHUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::Get([
            'LMRID' => $LMRId
            , 'fieldID' => 809
        ]);
        if (!$tblLMRHUDItemsPayableLoan) {
            $tblLMRHUDItemsPayableLoan = new tblLMRHUDItemsPayableLoan();
            $tblLMRHUDItemsPayableLoan->LMRID = $LMRId;
            $tblLMRHUDItemsPayableLoan->fieldID = 809;
        }
        $tblLMRHUDItemsPayableLoan->borrowerSettlementValue = Strings::replaceCommaValues($processingFee);
        $tblLMRHUDItemsPayableLoan->Save();
    }

    public static function getFeasibilityFeeHUD($PCID)
    {
        if (self::isPCAndFileStatus()) {
            $loanProgram = LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType; //Loan Program
            if ($loanProgram) {
                $sql = 'select
            t100.projectFeasibility
            FROM tblPCHMLOBasicLoanInfo t100
            LEFT JOIN tblPCHMLOBasicLoanPgmInfo t200 ON t100.BLID = t200.BLID
            WHERE t100.PCID = :PCID  and t200.loanPgm = :loanPgm';
                $params = [
                    'PCID'    => $PCID,
                    'loanPgm' => $loanProgram
                ];
                $result = Database2::getInstance()->queryData($sql, $params, null, true);
                if ($result) {
                    //save in the hud table
                    $return = $result['projectFeasibility'];
                    self::setFeasibilityFeeHUD($return);
                }
            }
        }
    }

    public static function setFeasibilityFeeHUD($feasibilityFee)
    {
        $LMRId = LMRequest::File()->LMRId;
        $tblLMRHUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::Get([
            'LMRID' => $LMRId
            , 'fieldID' => 807
        ]);
        if (!$tblLMRHUDItemsPayableLoan) {
            $tblLMRHUDItemsPayableLoan = new tblLMRHUDItemsPayableLoan();
            $tblLMRHUDItemsPayableLoan->LMRID = $LMRId;
            $tblLMRHUDItemsPayableLoan->fieldID = 807;
        }
        $tblLMRHUDItemsPayableLoan->borrowerSettlementValue = Strings::replaceCommaValues($feasibilityFee);
        $tblLMRHUDItemsPayableLoan->Save();
    }

    /**
     * @param $processingFee
     * @return void
     */
    public static function setProcessingFeeLoanInfoFeesAndCost($processingFee)
    {
        $LMRId = LMRequest::File()->LMRId;
        $tblFileHMLONewLoanInfo = tblFileHMLONewLoanInfo::Get(['fileID' => $LMRId]);
        if(!$tblFileHMLONewLoanInfo) {
            $tblFileHMLONewLoanInfo = new tblFileHMLONewLoanInfo();
            $tblFileHMLONewLoanInfo->fileID = $LMRId;
        }
        $tblFileHMLONewLoanInfo->isFeeUpdated = 1;
        $tblFileHMLONewLoanInfo->processingFee = Strings::replaceCommaValues($processingFee);
        $tblFileHMLONewLoanInfo->Save();
    }

    /**
     * @return bool
     */
    public static function isPCAndFileStatus(): bool
    {
        $return = true; //Default - other PCs
        $PCID = LMRequest::File()->FPCID;
        if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) { //CV3
            $primaryStatusId = LMRequest::File()->getTblFileResponse_by_LMRId()->primeStatusId;
            if (!in_array($primaryStatusId, [106963, 106977, 114234])) { //Primary Status is Lead(106963), Application Started(106977) and Application Submitted(114234)
                $return = false;
            }
        }
        return $return;
    }

    public static function getHUDPackageUrl(): string
    {
        return CONST_SITE_URL . 'package/pkgController.php?'.http_build_query([
            'rId'=> cypher::myEncryption(LMRequest::File()->getTblFileResponse_by_LMRId()->LMRResponseId),
            'bn'=> cypher::myEncryption(LMRequest::File()->brokerNumber),
            'lId'=> cypher::myEncryption(LMRequest::File()->LMRId),
            'pkgID'=> cypher::myEncryption(packageId::HUD),
            'opt'=> 'sample',
        ]);
    }

    public static function getHUDPackageButton(): ?string
    {
        return !glCustomJobForProcessingCompany::hideHUDPackageButton(LMRequest::File()->FPCID) ?
            '<a href="'.self::getHUDPackageUrl().'" class="btn btn-primary" target="_blank">View HUD</a>' : null;
    }

}
