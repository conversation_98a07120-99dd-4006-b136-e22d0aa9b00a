<?php

namespace models\Controllers\backoffice;

use Exception;
use models\composite\Ledger;
use models\composite\LoanAmortization;
use models\composite\LoanPayment;
use models\composite\LoanPaymentDue;
use models\composite\LoanServicing;
use models\Database2;
use models\lendingwise\tblCharges;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileInterestRateHistory;
use models\lendingwise\tblInvestorInfo;
use models\lendingwise\tblLedger;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\types\colorType;
use models\types\strongType;

/**
 *
 */
class servicingForm extends strongType
{
    public static ?int $LMRId = null;

    public static ?LoanServicing $loanServicing = null;
    public static ?colorType $sourceColors = null;
    /**
     * @var tblLedger[]
     */
    private static ?array $report = null;
    private static ?array $summary = null;

    public static ?string $source_name = null;
    /**
     * @var Ledger[]|null
     */
    private static ?array $source_ledger = null;

    /* @var LoanPayment[] $amortization */
    private static ?array $amortization = null;

    private static ?LoanPaymentDue $paymentDue = null;

    public static ?string $PaymentDate = null;
    public static ?string $PaymentDueDate = null;

    public static ?bool $isValid = null;
    public static ?array $invalidReason = [];

    // GET params for embedded view
    public static ?string $_param_eId = null;
    public static ?string $_param_rId = null;
    public static ?string $_param_lId = null;
    public static ?string $_param_tabOpt = null;
    public static ?string $_param_op = null;

    public static ?tblFile $loan = null;


    /**
     * @param int $LMRId
     * @return void
     */
    public static function Init(int $LMRId)
    {
        self::$isValid = false;
        self::$invalidReason = [];
        self::$LMRId = $LMRId;

        if (!$LMRId) {
            return;
        }


        // eId=f0e12fb56703c889&lId=1f62d45ed1691b55&rId=0bced2f8975ae361&tabOpt=SER2&op=a72f9e967052513d
        self::$_param_eId = $_REQUEST['eId'] ?? '';
        self::$_param_rId = $_REQUEST['rId'] ?? '';
        self::$_param_lId = $_REQUEST['lId'] ?? '';
        self::$_param_tabOpt = $_REQUEST['tabOpt'] ?? '';
        self::$_param_op = $_REQUEST['op'] ?? '';

// SELECT * FROM tblAdminUsers WHERE role = 'Administrator' AND activeStatus = 1;
// SELECT * FROM stage_new.tblFile WHERE propertyAddress LIKE '%#34 Dfdsf%';
// http://dev.theloanpost.com/backoffice/LMRequest.php?eId=ba5971351d9f244d&lId=adcab64f0d4615bf&rId=f61ec4d302bd49f3&tabOpt=CI&op=a72f9e967052513d

// http://dev.theloanpost.com/backoffice/servicingForm.php

        self::$loan = tblFile::Get(['LMRId' => self::$LMRId]);
        self::$loanServicing = self::$loan ? self::$loan->getLoanServicing() : new LoanServicing(self::$LMRId);

        if (!self::$loanServicing) {
            Debug(['error' => 'Loan Servicing Not Loaded', self::$loanServicing, self::$LMRId]);
        }

        $sql = '
        SELECT COUNT(*) AS cnt 
        FROM tblLedger 
        WHERE LMRId = :LMRId
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId' => $LMRId,
        ]);
        if (!($res[0]['cnt'] ?? null)) {
            self::convert();
        }

        self::$sourceColors = new colorType();

        self::$invalidReason = self::$loanServicing->checkValid();

        if (sizeof(self::$invalidReason) == 0) {
            self::$isValid = true;
        }
    }

    /**
     * @param string $source_name
     * @param int|null $source_id
     * @return string
     */
    public static function NiceSummarySourceName(string $source_name, ?int $source_id): string
    {
        switch ($source_name) {
            case 'per_diem':
                return 'Per Diem';
            case 'closing_costs':
                return 'Closing Costs';
            case 'charges':
                if ($source_id) {
                    if (!isset(self::$_sourceCache['charges::' . $source_id])) {
                        self::$_sourceCache['charges::' . $source_id] = tblCharges::Get([
                            'tblChargesId' => $source_id,
                            'LMRId' => self::$LMRId,
                        ]);
                    }
                    $charge = self::$_sourceCache['charges::' . $source_id];
                    return 'Charge: ' . $charge->description . ' ' . Dates::StandardDate($charge->dueDate);
                }
                return 'Charge';
            case 'borrower':
                return 'Borrower: ' . self::$loanServicing->getFile()->borrowerName;
            case 'escrow':
                return 'Escrow';
            case 'escrow_insurance':
                return 'Escrow: Insurance';
            case 'escrow_prepaid_interest':
                return 'Escrow: Prepaid Interest';
            case 'escrow_prepaid_escrow':
                return 'Escrow: Prepaid Escrow';
            case 'escrow_rehab':
                return 'Escrow: Rehab';
            case 'escrow_taxes':
                return 'Escrow: Taxes';
            case 'escrow_default_fees':
                return 'Escrow: Default Fees';
            case 'investor':
                return 'Investor Funding';
            case 'investor_escrow':
                return 'Investor Funds Escrowed';
            case 'investor_vested':
                return 'Investor Funds Vested';
            case 'lender':
                return 'Lender';
            case 'principal':
                return 'Principal';
            case 'interest':
                return 'Interest';
            case 'lender_interest':
                return 'Lender Interest';
            case 'regular_payment':
                return 'Regular Payment';
            case 'investor_interest':
                return 'Investor Interest';
        }
        return $source_name;
    }


    /* @var tblInvestorInfo[] $_sourceCache */
    private static array $_sourceCache = [];

    /**
     * @param string $source_name
     * @param int|null $source_id
     * @return string|null
     */
    public static function NiceSourceName(string $source_name, ?int $source_id): ?string
    {
        if (in_array($source_name, ['investor', 'investor_escrow', 'investor_vested', 'investor_interest', 'investor_principal'])) {
            $InID = $source_id;
            if (!isset(self::$_sourceCache['investor::' . $InID])) {
                self::$_sourceCache['investor::' . $InID] = tblInvestorInfo::Get(['InID' => $InID]);
            }

            if (!self::$_sourceCache['investor::' . $InID]) {
                return $source_name;
            }

            $contact = self::$_sourceCache['investor::' . $InID]->getTblContact_by_CID();

            switch ($source_name) {
                case 'investor':
                    return !is_null($contact) ? 'Investor: ' . $contact->companyName . ' - Deposit' : $source_name;
                case 'investor_escrow':
                    return !is_null($contact) ? 'Investor: ' . $contact->companyName . ' - Escrow' : $source_name;
                case 'investor_vested':
                    return !is_null($contact) ? 'Investor: ' . $contact->companyName . ' - Vested' : $source_name;
                case 'investor_interest':
                    return !is_null($contact) ? 'Investor: ' . $contact->companyName . ' - Interest Earned' : $source_name;
                case 'investor_principal':
                    return !is_null($contact) ? 'Investor: ' . $contact->companyName . ' - Principal Earned' : $source_name;
            }
        }
        return self::NiceSummarySourceName($source_name, $source_id);
    }

    /**
     * @param string $transaction_type
     * @return string
     */
    public static function NiceTransactionTypeName(string $transaction_type): string
    {
        switch ($transaction_type) {
            case 'per_diem':
                return 'Per Diem';
            case 'closing_costs':
                return 'Closing Costs';
            case 'charges':
                return 'Charges';
            case 'pay_down':
                return 'Pay Down';
            case 'draw':
                return 'Draw';
            case 'escrow':
                return 'Escrow';
            case 'funding':
                return 'Funding';
            case 'investor_funding':
                return 'Investor Funding';
            case 'investor_history':
                return 'Investor History';
            case 'payment':
                return 'Regular Payment';

        }
        return $transaction_type;
    }

    public static function getPaymentDue(): ?LoanPaymentDue
    {
        if(is_null(self::$paymentDue)) {
            self::$paymentDue = LoanPaymentDue::GetPaymentDue(
                self::$loanServicing,
                self::$PaymentDueDate,
                self::$PaymentDate
            );
        }

        return self::$paymentDue;
    }

    /**
     * @return tblLedger[]|null
     */
    public static function getReport(): ?array
    {
        if(is_null(self::$report)) {
            self::$report = LoanAmortization::GetReport(self::$loanServicing);
        }
        return self::$report;

    }

    /**
     * @return array|null
     */
    public static function getSummary(): ?array
    {
        if(is_null(self::$summary)) {
            self::$summary = LoanAmortization::GetSummary(self::$loanServicing);
        }
        return self::$summary;
    }

    /**
     * @return LoanPayment[]|null
     */
    public static function getAmortization(): ?array
    {
        if (is_null(self::$amortization)) {

            self::$amortization = LoanAmortization::GetAmortizationTable(self::$loanServicing);
        }

        return self::$amortization;
    }

    /**
     * @return Ledger[]|null
     */
    public static function getSourceLedger(): ?array
    {
        if(is_null(self::$source_ledger)) {
            if (self::$source_name) {
                self::$source_ledger = LoanAmortization::GetSourceReport(
                    self::$loanServicing,
                    self::$source_name
                );
            }
        }
        return self::$source_ledger;
    }

    /**
     *
     */
    public static function Get()
    {
        if (self::$loanServicing->isInvalid) {
            return;
        }

        try {
            self::$PaymentDueDate = Dates::Datestamp($_REQUEST['payment_due'] ??
                LoanPaymentDue::GetPaymentDueDate(self::$loanServicing), '');
            self::$PaymentDate = Dates::Datestamp($_REQUEST['payment_date'] ?? null);

            self::$source_name = $_REQUEST['source_name'] ?? null;


        } catch (Exception $e) {
            Debug($e);
        }
    }

    /**
     * @param tblLedger $parentItem
     * @param int $parent_id
     * @param float $loan_balance
     * @param int $level
     * @param int $max_level
     * @return string
     */
    public static function BuildLedger(
        tblLedger $parentItem,
        int       $parent_id,
        float     &$loan_balance,
        int       $level,
        int       $max_level
    ): string
    {
        if ($level > $max_level) {
            $level = $max_level;
        }
        $records = LoanAmortization::GetReport(self::$loanServicing, $parent_id);
        if (!sizeof($records)) {
            return '';
        }

        $html = '
        <tr>
        <td colspan="9">
    <table class="ledger level_' . $level . '">
        <thead>
        <tr>
            <th style="width: 5%"></th>
            <th style="width: 10%"></th>
            ' . (str_repeat('<th style="width: 17.5%"></th>', $level)) . '
            <th style="width: 17.5%">Debit</th>
            <th style="width: 17.5%">Credit</th>
            ' . (str_repeat('<th style="width: 17.5%"></th>', $max_level - $level)) . '
            <th style="text-align: right; width: 5%;">Amount</th>
            <th style="text-align: right; width: 5%;">Yield</th>
            <th style="text-align: right; width: 5%;">Balance</th>
        </tr>
        </thead>
        <tbody>        
        ';
        foreach ($records as $item) {
            $loan_balance += $item->alter_loan_balance;
            $html .= '
            <tr>
            <td></td>
            <td></td>
            ' . (str_repeat('<td></td>', $level)) . '
                <td
                    style="color:' . self::$sourceColors->getColor($item->debit_source_name) . '"
                >' . self::NiceSourceName($item->debit_source_name, $item->debit_source_id) . '</td>
                <td
                    style="color:' . self::$sourceColors->getColor($item->credit_source_name) . '"
                >' . self::NiceSourceName($item->credit_source_name, $item->credit_source_id) . '</td>
            ' . (str_repeat('<td></th>', 2 - $level)) . '
                <td style="text-align: right;">' . number_format($item->amount, 2) . '</td>
                <td style="text-align: right;">' . ($item->yield ? number_format($item->yield, 3) . '%' : '') . '</td>
                <td style="text-align: right;">' . number_format($loan_balance, 2) . '</td>
            </tr>            
            ';
            $html .= servicingForm::BuildLedger($parentItem, $item->ledgerId, $loan_balance, $level + 1, $max_level);
        }
        $html .= '
        </tbody>
        </table>
        </td>
        </tr>
        ';

        return $html;
    }



    public static function deleteInterestRateHistory(): void
    {
        $id = $_REQUEST['interest_rate_history_id'] ?? null;
        if (!$id) {
            return;
        }
        $check = tblFileInterestRateHistory::Get(['LMRId' => self::$LMRId, 'id' => $id]);
        if (!$check) {
            return;
        }
        $check->Delete();
        HTTP::ExitJSON($check->toArray());
    }

    /**
     *
     */






    public static function loadInterestRateHistory()
    {
        $id = $_REQUEST['id'] ?? null;
        if (!$id) {
            return;
        }

        $item = tblFileInterestRateHistory::Get(['LMRId' => self::$LMRId, 'id' => $id]);
        if (!$item) {
            return;
        }

        HTTP::ExitJSON($item->toArray());
    }

    /**
     *
     */
    public static function saveInterestHistory()
    {
        $id = $_REQUEST['id'] ?? null;
        $apply_date = $_REQUEST['apply_date'] ?? null;
        $interest_rate = $_REQUEST['interest_rate'] ?? null;

        if ($id) {
            $check = tblFileInterestRateHistory::Get(['id' => $id]);
            if ($check->LMRId != self::$LMRId) {
                return;
            }
        } else {
            $check = new tblFileInterestRateHistory();
            $check->LMRId = self::$LMRId;
        }
        $check->apply_date = Dates::Datestamp($apply_date);
        $check->interest_rate = Strings::replaceCommaValues($interest_rate);
        $check->Save();
    }

    /**
     * @param bool $reload
     */
    public static function convert(bool $reload = true): void
    {
        try {
            LoanAmortization::ConvertToServicing(self::$loanServicing);
        } catch (Exception $e) {
            Debug($e);
        }
        if ($reload) {
            HTTP::ReloadPage();
        }
    }
}
