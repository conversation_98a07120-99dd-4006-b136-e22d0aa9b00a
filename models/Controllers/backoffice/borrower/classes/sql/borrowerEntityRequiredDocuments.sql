select t100.id,
       t100.requiredDocId,
       t100.fileStorageId,
       t100.displayName as displayName,
       t100.expiryDate,
       t100.status,
       t200.docName     as requiredDocName,
       t300.givenPath,
       t300.storagePath,
       t300.fileName,
       t300.fileSize,
       t300.fileType,
       t150.entityName,
       CASE
           WHEN t100.createdGroup = 'Employee' OR t100.createdGroup = 'Super'
               THEN CONCAT(u100.processorName, ' (', t100.createdGroup, ')')
           WHEN t100.createdGroup = 'Branch'
               THEN CONCAT(u110.LMRExecutive, ' (', t100.createdGroup, ')')
           WHEN t100.createdGroup = 'Agent'
               THEN CONCAT(u120.firstName, ' ', u120.lastName, ' (', t100.createdGroup, ')')
           END          AS createdBy
from tblBorrowerEntityDocs as t100

         join tblPCClientEntityInfo as t150 on
    t100.CBEID = t150.CBE<PERSON>

         left join tblBorrowerProfileRequiredDocs as t200 on
    t100.requiredDocId = t200.id and t200.docType = 'entity'

         left join tblFileStorage as t300 on
    t100.fileStorageId = t300.id

         left join tblAdminUsers u100 ON
    t100.createdGroup IN ('Employee', 'Super') AND t100.createdBy = u100.AID

         left join tblBranch u110 on
    (t100.createdGroup = 'Branch') and t100.createdBy = u110.executiveId

         left join tblAgent u120 on
    (t100.createdGroup = 'Agent') and t100.createdBy = u120.userNumber

where t150.CID = @borrowerId
and t100.CBEID = @CBEID
