<?php

namespace models\Controllers\backoffice\borrower\classes;

use models\APIHelper;
use models\Database2;
use models\types\strongType;

class borrowerEntityRequiredDocuments extends strongType
{

    public static int $id;
    public static int $requiredDocId;
    public static int $fileStorageId;
    public static int $status;
    public static ?string $requiredDocName = null;
    public static ?string $givenPath = null;
    public static ?string $storagePath = null;
    public static ?string $fileName = null;
    public static ?int $fileSize = null;
    public static ?string $fileType = null;
    public ?string $displayName = null;
    public ?string $expiryDate = null;
    public ?string $createdBy = null;
    public ?string $entityName = null;

    public static function get(int $borrowerId,
                               ?int $CBEID): array
    {

        $sql = APIHelper::getSQL(__DIR__ . '/sql/borrowerEntityRequiredDocuments.sql');
        $params['borrowerId'] = $borrowerId;
        $params['CBEID'] = $CBEID;

        /* @var self[] $res */
        return Database2::getInstance()->queryData($sql, $params, function ($row) {
            return new self($row);
        });
    }

}
