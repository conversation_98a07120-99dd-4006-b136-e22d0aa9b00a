<?php

namespace models\Controllers;

use models\constants\gl\glPCID;
use models\Database2;
use models\composite\oPC\getacqualifyPCDetails;
use models\composite\oPC\getCountData;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getQueryResponded;
use models\composite\oPC\oLogo;
use models\constants\gl\glUserGroup;
use models\constants\gl\glUserNumber;
use models\constants\gl\glUserRole;
use models\constants\MMSException;
use models\cypher;
use models\PageVariables;
use models\standard\Strings;
use models\types\strongType;
use models\constants\gl\glCustomJobForProcessingCompany;

class menu extends strongType
{
    public static ?string $dashURL = null;
    public static ?string $userLogo = null;
    public static ?string $PCID = null;
    public static ?string $userSiteURL = null;
    public static ?string $getCurrentPageFromURL = null;
    public static ?string $contactsListURL = null;
    public static ?string $createSchoolURL = null;
    public static ?string $marketPlaceNichesListURL = null;
    public static ?string $allowDashboard = null;
    public static ?string $userRole = null;
    public static ?string $getCurrentURL = null;
    public static ?string $quickAppWebURL = null;
    public static ?string $fullAppWebURL = null;
    public static ?string $ssURL = null;
    public static ?string $newClientURL;
    public static ?array $PCModulesArray = [];
    public static ?array $MMSException = [];
    public static ?string $allowToCreateFiles = null;
    public static ?string $url = null;
    public static ?string $userNumber = null;
    public static ?string $newPipe2URL = null;
    public static ?string $getCurrentQUERY_STRING = null;
    public static ?string $userGroup = null;
    public static ?string $pipelineLiteUrl = null;
    public static ?string $subscribeToHOME = null;
    public static ?string $subscribePCToHOME = null;
    public static ?string $HOMEPipeURL = null;
    public static ?string $allowToCFPBSubmitForPC = null;
    public static ?string $allowToViewCFPBPipeline = null;
    public static ?string $allowCFPBAuditing = null;
    public static ?string $LoanAuditPipeURL = null;
    public static ?string $creditScreeningAcQualifyPC = null;
    public static ?string $pcAcqualifyId = null;
    public static ?string $allowToViewCreditScreening = null;
    public static ?string $pcAcqualifyStatus = null;
    public static ?string $pcPriceEngineStatus = null;
    public static ?string $userPriceEngineStatus = null;
    public static ?string $branchListURL = null;
    public static ?string $procListURL = null;
    public static ?string $procURL = null;
    public static ?string $allowEmpToCreateBranch = null;
    public static ?string $branchURL = null;
    public static ?string $agentListURL = null;
    public static ?string $loanOfficerListURL = null;
    public static ?string $createAgentURL = null;
    public static ?string $externalBroker = null;
    public static ?string $allowBranchToAddAgent = null;
    public static ?string $allowEmpToSeeAgent = null;
    public static ?string $lenderListURL = null;
    public static ?string $createLenderURL = null;
    public static ?string $createBusinessLenderURL = null;
    public static ?string $schoolListURL = null;
    public static ?string $marketPlaceLoanProgramListURL = null;
    public static ?string $PLOListURL = null;
    public static ?string $allowToViewContactsList = null;
    public static ?string $createContactsURL = null;
    public static ?string $allowToCreateTasks = null;
    public static ?string $groupingServicesURL = null;
    public static ?string $userSeeBilling = null;
    public static ?string $allowToAccessRAM = null;
    public static ?string $allowAutomation = null;
    public static ?string $templateURL = null;
    public static ?string $announcementURL = null;
    public static ?string $generalFAQURL = null;
    public static ?string $reductionEstimatorURL = null;
    public static ?string $checklistCount = null;
    public static ?string $completedCount = null;
    public static ?string $queryFlagRes = null;
    public static ?string $superQueryFlagRes = null;
    public static ?string $createURL1 = null;
    public static ?string $createURL2ShortSaleQuickAPP = null;
    public static ?string $createURL3SSQuickAPP = null;
    public static ?string $createURL3SSFullAPP = null;
    public static ?string $createURL4LMQuickApp = null;
    public static ?string $createURL4LMFullApp = null;
    public static ?string $clientURLPipeline = null;

    public static ?array $menu = null;
    public static ?string $logoutURL = null;

    const MENU_ITEM_TYPE_SUB_SUB_ITEM = 'menuSubSubItemHTML';
    const MENU_ITEM_TYPE_MODAL = 'menuModal';
    const MENU_ITEM_TYPE_POPUP = 'menuPopUp';
    const MENU_ITEM_TYPE_SUB_ITEM = 'menuSubItemHTML';

    public static ?bool $isAllowDashboardUserRoleSuper = null;
    public static ?bool $notUserRoleClientRest = null;
    public static ?bool $isUserRoleAuditorCFPBAuditorAuditorManager = null;
    public static ?bool $isUserRoleCFBPAuditorAuditorManager = null;
    public static ?bool $isUserRoleClient = null;
    public static ?bool $isUserRoleSuper = null;
    public static ?array $resultBorrower = null;
    public static ?string $REQUEST_URI = null;
    public static ?string $getCurrentPath = null;
    public static ?int $docWizard = null;
    public static ?int $showDrawManagementTab = null;

    public static function Init()
    {
        global $userRole, $userGroup, $PCID, $fileModuleCodes;
        global $pcAcqualifyDetails, $allowEmpToCreateBranch;
        global $allowToCreateFiles, $subscribeToHOME, $subscribePCToHOME, $allowToCFPBSubmitForPC;
        global $allowToViewCFPBPipeline, $allowCFPBAuditing, $allowDashboard, $userNumber;
        global $allowAutomation, $allowToViewCreditScreening, $allowToCreateTasks, $pcAcqualifyStatus;
        global $allowBranchToAddAgent, $userSeeBilling, $allowToAccessRAM;
        global $userPriceEngineStatus, $externalBroker, $allowEmpToSeeAgent,
               $pcPriceEngineStatus, $allowToViewContactsList;

        $MMSException = MMSException::$MMSException;

        $marketPlaceLoanProgramListURL = null;
        $marketPlaceNichesListURL = null;
        $groupingServicesURL = null;
        $createSchoolURL = null;
        $schoolListURL = null;
        $createContactsURL = null;
        $contactsListURL = null;
        $branchURL = null;
        $pcAcqualifyId = null;
        $loanOfficerListURL = null;
        $LoanAuditPipeURL = null;
        $creditScreeningAcQualifyPC = null;
        $HOMEPipeURL = null;
        $url = null;
        $fullAppWebURL = null;
        $quickAppWebURL = null;
        $userSiteURL = null;
        $userLogo = '';
        $queryFlagRes = null;
        $superQueryFlagRes = null;
        $agentListURL = '';
        $newClientURL = '';
        $ssURL = '';
        $newPipe2URL = '';
        $dashURL = '';
        $procURL = '';
        $procListURL = '';
        $branchListURL = '';
        $createAgentURL = '';
        $createLenderURL = '';
        $lenderListURL = '';
        $PLOListURL = '';
        $templateURL = '';
        $announcementURL = '';
        $reductionEstimatorURL = '';
        $createBusinessLenderURL = '';
        $modulesInfoArray = [];
        $PCModulesArray = [];
        $generalFAQURL = '';
        $checklistCount = 0;
        $completedCount = 0;
        $pipelineLiteUrl = '';

        $getCurrentURL = Strings::getCurrentURLFull();
        $getCurrentPath = explode('?', Strings::REQUEST_URI())[0];
        if (substr($getCurrentPath, -1) == '/') { // remove trailing slash
            $getCurrentPath = substr($getCurrentPath, 0, -1);
        }

        $getCurrentPageFromURL = Strings::getCurrentPageFromURL();
        $getCurrentQUERY_STRING = Strings::getCurrentQUERY_STRING();

        $logoutURL = '/logout';


        if ($userRole) {

            if ($PCID > 0) {
                $ip['PCID'] = $PCID;
                $ip['keyNeeded'] = 'n';

                $modulesInfoArray = getPCModules::getReport($ip);
                $queryFlagRes = getQueryResponded::getReport(['PCID' => $PCID, 'type' => 'BO']);
                $checklistCountDataArr = getCountData::getReport(['PCID' => $PCID]);
                if (count($checklistCountDataArr) > 0) {
                    $checklistCountDataArr = $checklistCountDataArr[0];
                    $checklistCount = $checklistCountDataArr['checklistCount'];
                    $completedCount = $checklistCountDataArr['completedCount'];
                }
            }

            if ($userGroup == glUserGroup::SUPER) {
                $superQueryFlagRes = getQueryResponded::getReport(['PCID' => $PCID]);
            }


            $pcAcqualifyDetails = [];
            $pcAcqualifyId = 0;

            if ($PCID > 0) {
                $userLogoArr = oLogo::getReport(['PCID' => $PCID, 'userGroup' => $userGroup, 'userNumber' => $userNumber]);
                $userLogo = $userLogoArr['logo'];
                $pcAcqualifyDetails = getacqualifyPCDetails::getReport(['pcid' => $PCID]);
                $pcAcqualifyId = $pcAcqualifyDetails[0]['accountId'] ?? 0;
            }

            for ($i = 0; $i < count($modulesInfoArray); $i++) {
                $PCModulesArray[] = $modulesInfoArray[$i]['moduleCode'];
            }
            if ($userGroup == glUserGroup::CLIENT && count($PCModulesArray) == 0 && $fileModuleCodes) {
                $PCModulesArray = $fileModuleCodes;
            }

            $pcAssignedModuleCode = null;
            if (sizeof($PCModulesArray ?? []) == 1) {
                $pcAssignedModuleCode = $PCModulesArray[0];
            }


            switch ($userRole) {
                case glUserRole::BRANCH:
                    $userSiteURL = CONST_URL_BR;

                    $newClientURL = $userSiteURL . 'LMRequest.php?eOpt=0';
                    $ssURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=SS&moduleCode=SS';
                    $newPipe2URL = $userSiteURL . 'myPipeline.php';
                    $HOMEPipeURL = $userSiteURL . 'myPipeline.php?fileType=b92575b6c7c7374f';
                    $LoanAuditPipeURL = $userSiteURL . 'myPipeline.php?fileType=' . cypher::myEncryption('LA');

                    $dashURL = $userSiteURL . 'dashboard'; // 'adminWelcome.php';
                    $agentListURL = $userSiteURL . 'brokers';
                    $loanOfficerListURL = $userSiteURL . 'loan_officers';
                    $lenderListURL = $userSiteURL . 'lenders';
                    $PLOListURL = $userSiteURL . 'clients';
                    $createAgentURL = $userSiteURL . 'createAgent.php';

                    $quickAppWebURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=QAPP' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');
                    $fullAppWebURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=LI' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');


                    $creditScreeningAcQualifyPC = CONST_URL_BRSSL . 'creditScreening.php';
                    break;
                case glUserRole::CLIENT:
                    $userSiteURL = CONST_URL_CL;

                    if (preg_match('/client_new\//i', $_SERVER['HTTP_REFERER'])) {
                        $url = CONST_URL_CL_NEW;
                    } else {
                        $url = CONST_URL_CL;
                    }

                    $quickAppWebURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=QAPP' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');
                    $fullAppWebURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=LI' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');

                    $newClientURL = CONST_URL_BOSSL . 'LMRequest.php?eOpt=0';
                    $dashURL = $userSiteURL . 'clientPipeline.php';
                    break;

                case glUserRole::AGENT:
                    $userSiteURL = CONST_URL_AG;

                    $newClientURL = $userSiteURL . 'LMRequest.php?eOpt=0';
                    $dashURL = $userSiteURL . 'dashboard'; // 'adminWelcome.php';
                    $templateURL = $userSiteURL . 'manageLMREmailTemplate.php';
                    $lenderListURL = $userSiteURL . 'lenders';
                    $newPipe2URL = $userSiteURL . 'myPipeline.php';
                    $HOMEPipeURL = $userSiteURL . 'myPipeline.php';
                    $LoanAuditPipeURL = $userSiteURL . 'myPipeline.php?fileType=' . cypher::myEncryption('LA');
                    $ssURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=SS&moduleCode=SS';
                    $agentListURL = $userSiteURL . 'agentList.php';

                    $quickAppWebURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=QAPP' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');
                    $fullAppWebURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=LI' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');

                    $PLOListURL = $userSiteURL . 'clients';

                    $creditScreeningAcQualifyPC = $userSiteURL . 'creditScreening.php';

                    $contactsListURL = CONST_BO_URL . '/contacts';
                    $createContactsURL = CONST_BO_URL . '/contacts?create=1';
                    break;
                default:
                    $userSiteURL = CONST_BO_URL;

                    if ($_SESSION['spoof'] ?? 0) {
                        $logoutURL = $userSiteURL . 'logoutSpoof.php';
                    }  //this url will occur for normal user too because in the session, but it's on purpose, so they don't log me out.

                    $newClientURL = $userSiteURL . 'LMRequest.php?eOpt=0';
                    $ssURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=SS&moduleCode=SS';
                    $newPipe2URL = $userSiteURL . 'myPipeline.php';
                    $HOMEPipeURL = $userSiteURL . 'myPipeline.php';
                    $LoanAuditPipeURL = $userSiteURL . 'myPipeline.php?fileType=' . cypher::myEncryption('LA');
                    $dashURL = $userSiteURL . 'dashboard'; // 'adminWelcome.php';
                    $procURL = $userSiteURL . 'createProcessor.php';
                    $procListURL = $userSiteURL . 'processorList.php';
                    $branchURL = $userSiteURL . 'createBranch.php';
                    $branchListURL = $userSiteURL . 'branchList.php';
                    $agentListURL = $userSiteURL . 'brokers';
                    $loanOfficerListURL = $userSiteURL . 'loan_officers';
                    $createAgentURL = $userSiteURL . 'createAgent.php';
                    $createLenderURL = $userSiteURL . 'lenders/create';
                    $lenderListURL = $userSiteURL . 'lenders';
                    $createSchoolURL = $userSiteURL . 'schoolCreate.php';
                    $schoolListURL = $userSiteURL . 'schoolList.php';

                    $contactsListURL = $userSiteURL . 'contacts';
                    $createContactsURL = $userSiteURL . 'contacts?create=1';

                    $PLOListURL = $userSiteURL . 'clients';
                    $templateURL = $userSiteURL . 'manageLMREmailTemplate.php';
                    $announcementURL = $userSiteURL . 'settings/announcement';
                    $generalFAQURL = $userSiteURL . 'generalFAQ.php';
                    $marketPlaceNichesListURL = $userSiteURL . 'nichesList.php';
                    $groupingServicesURL = $userSiteURL . 'groupingServices.php';
                    $marketPlaceLoanProgramListURL = $userSiteURL . 'marketPlaceLPList.php';

                    $reductionEstimatorURL = CONST_URL_POPS . 'paymentReductionEstimator.php';
                    $quickAppWebURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=QAPP' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');
                    $fullAppWebURL = $userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=LI' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');

                    $creditScreeningAcQualifyPC = $userSiteURL . 'creditScreening.php';
            }


// No longer needed, we don't allow this report to run unless a PCID is selected
//            if ($userRole == glUserRole::SUPER) {
//                $newPipe2URL .= '?multipleModuleCode=HMLO&searchField=tl.borrowerLName&searchTerm=changeme!';
//            }

            if ($userRole == glUserRole::SUPER) {
                $pipelineLiteUrl = CONST_URL_BOSSL . 'mypipelinelite.php';
            }

            $chkHttps = '';
            if (isset($_SERVER['HTTPS'])) $chkHttps = trim($_SERVER['HTTPS']);
            if ($chkHttps == 'on' && (CONST_ENVIRONMENT == 'production' || CONST_ENVIRONMENT == 'development')) {
                //        $userLogo = preg_replace("/http:\/\//i", "https://", $userLogo);
                if (trim($userLogo)) {
                    $userLogo = preg_replace_callback(
                        '#http://#i',
                        function () {
                            return '';
                        },
                        $userLogo
                    );
                    $userLogo = preg_replace_callback(
                        '#https://#i',
                        function () {
                            return '';
                        },
                        $userLogo
                    );
                    $userLogo = 'https://' . $userLogo;
                }
            }
        }

        self::$dashURL = $dashURL;
        self::$userLogo = $userLogo;
        self::$PCID = $PCID;
        self::$userSiteURL = $userSiteURL;
        self::$getCurrentPageFromURL = $getCurrentPageFromURL;
        self::$contactsListURL = $contactsListURL;
        self::$createSchoolURL = $createSchoolURL;
        self::$marketPlaceNichesListURL = $marketPlaceNichesListURL;
        self::$allowDashboard = $allowDashboard;
        self::$userRole = $userRole;
        self::$getCurrentURL = $getCurrentURL;
        self::$getCurrentPath = $getCurrentPath;
        self::$quickAppWebURL = $quickAppWebURL;
        self::$fullAppWebURL = $fullAppWebURL;
        self::$ssURL = $ssURL;
        self::$newClientURL = $newClientURL;
        self::$PCModulesArray = $PCModulesArray;
        self::$MMSException = $MMSException;
        self::$allowToCreateFiles = $allowToCreateFiles;
        self::$url = $url;
        self::$userNumber = $userNumber;
        self::$newPipe2URL = $newPipe2URL;
        self::$getCurrentQUERY_STRING = $getCurrentQUERY_STRING;
        self::$userGroup = $userGroup;
        self::$pipelineLiteUrl = $pipelineLiteUrl;
        self::$subscribeToHOME = $subscribeToHOME;
        self::$subscribePCToHOME = $subscribePCToHOME;
        self::$HOMEPipeURL = $HOMEPipeURL;
        self::$allowToCFPBSubmitForPC = $allowToCFPBSubmitForPC;
        self::$allowToViewCFPBPipeline = $allowToViewCFPBPipeline;
        self::$allowCFPBAuditing = $allowCFPBAuditing;
        self::$LoanAuditPipeURL = $LoanAuditPipeURL;
        self::$creditScreeningAcQualifyPC = $creditScreeningAcQualifyPC;
        self::$pcAcqualifyId = $pcAcqualifyId;
        self::$allowToViewCreditScreening = $allowToViewCreditScreening;
        self::$docWizard = PageVariables::$docWizard;
        self::$pcAcqualifyStatus = $pcAcqualifyStatus;
        self::$pcPriceEngineStatus = $pcPriceEngineStatus;
        self::$userPriceEngineStatus = $userPriceEngineStatus;
        self::$branchListURL = $branchListURL;
        self::$procListURL = $procListURL;
        self::$procURL = $procURL;
        self::$allowEmpToCreateBranch = $allowEmpToCreateBranch;
        self::$branchURL = $branchURL;
        self::$agentListURL = $agentListURL;
        self::$loanOfficerListURL = $loanOfficerListURL;
        self::$createAgentURL = $createAgentURL;
        self::$externalBroker = $externalBroker;
        self::$allowBranchToAddAgent = $allowBranchToAddAgent;
        self::$allowEmpToSeeAgent = $allowEmpToSeeAgent;
        self::$lenderListURL = $lenderListURL;
        self::$createLenderURL = $createLenderURL;
        self::$createBusinessLenderURL = $createBusinessLenderURL;
        self::$schoolListURL = $schoolListURL;
        self::$marketPlaceLoanProgramListURL = $marketPlaceLoanProgramListURL;
        self::$PLOListURL = $PLOListURL;
        self::$allowToViewContactsList = $allowToViewContactsList;
        self::$createContactsURL = $createContactsURL;
        self::$allowToCreateTasks = $allowToCreateTasks;
        self::$groupingServicesURL = $groupingServicesURL;
        self::$userSeeBilling = $userSeeBilling;
        self::$allowToAccessRAM = $allowToAccessRAM;
        self::$allowAutomation = $allowAutomation;
        self::$templateURL = $templateURL;
        self::$announcementURL = $announcementURL;
        self::$generalFAQURL = $generalFAQURL;
        self::$reductionEstimatorURL = $reductionEstimatorURL;
        self::$checklistCount = $checklistCount;
        self::$completedCount = $completedCount;
        self::$queryFlagRes = $queryFlagRes;
        self::$superQueryFlagRes = $superQueryFlagRes;
        self::$showDrawManagementTab = PageVariables::PC()->drawManagement && PageVariables::PC()->enableDrawManagementV2;

        self::$createURL1 = menu::$userSiteURL . 'LMRequest.php?eOpt=0' . ($pcAssignedModuleCode ? '&moduleCode=' . $pcAssignedModuleCode : '');
        self::$createURL2ShortSaleQuickAPP = menu::$userSiteURL . 'SSQuickApp.php';
        self::$createURL3SSQuickAPP = menu::$userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&moduleCode=SS&tabOpt=QAPP';
        self::$createURL3SSFullAPP = menu::$userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&moduleCode=SS&tabOpt=LI';
        self::$createURL4LMQuickApp = menu::$userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&moduleCode=LM&tabOpt=QAPP';
        self::$createURL4LMFullApp = menu::$userSiteURL . 'LMRequest.php?eOpt=0&cliType=PC&moduleCode=LM&tabOpt=LI';
        self::$clientURLPipeline = menu::$url . 'clientPipeline.php?cId=' . cypher::myEncryption(menu::$userNumber);
        self::$logoutURL = $logoutURL;
    }

    private static function doRenderMenu(
        string $name,
        array  $menu,
        ?bool  $open,
        int    $level = 0
    ): string
    {

        if (isset($menu['visible']) && !$menu['visible']) {
            return '';
        }

        $icon = $menu['icon'] ?? null;
        $links = $menu['links'];
        $open = $menu['open'] ?? ($open ?: in_array(menu::$getCurrentURL, array_keys($links)));

        $html = '
            <li
                class="menu-item menu-item-submenu ' . ($level == 0 ? 'font-weight-bold' : '') . ' ' . ($open ? 'menu-item-open menu-item-active' : '') . '"
                data-level="' . $level . '"
                aria-haspopup="true"
                data-menu-toggle="hover"
            >
                <span
                    class="menu-link menu-toggle "
                    style="cursor: pointer;"
                >
                ' . ($level == 0 ? '
                    <span class="menu-icon">
                        <i class="' . $icon . '"></i>
                    </span>
                ' : '
                <i class="menu-bullet menu-bullet-line">
                    <span></span>
                </i>
                ') . '
                <span class="menu-text" id=menu_' . Strings::removeSpaceWithSpecialChars($name) . '>' . $name . '</span>
                <i class="menu-arrow"></i>
            </span>
            <div class="menu-submenu">
                <i class="menu-arrow"></i>
                    <ul class="menu-subnav">
        ';

        foreach ($links as $url => $link) {
            if (isset($link['links'])) {
                $html .= self::doRenderMenu(
                    $url,
                    $link,
                    $link['open'] ?? null,
                    1
                );
                continue;
            }

            switch ($link['method']) {
                case self::MENU_ITEM_TYPE_SUB_SUB_ITEM:
                    $html .= self::menuSubSubItemHTML(
                        (bool)$link['visible'] ?? true ,
                        $link['active'],
                        $url,
                        $link['name'],
                        $link['automationEnable'] ?? true,
                    );
                    break;

                case self::MENU_ITEM_TYPE_MODAL:
                    $html .= self::menuModal(
                        $link['visible'],
                        $link['active'],
                        $link['href'],
                        $link['target'],
                        $link['name'],
                        $link['wsize'],
                        $link['id']
                    );
                    break;
                case self::MENU_ITEM_TYPE_POPUP:
                    $html .= self::menuPopUp(
                        $url,
                        $link['name'],
                        $link['width'],
                        $link['height'],
                        $link['scrollOpt']
                    );
                    break;
                case self::MENU_ITEM_TYPE_SUB_ITEM:
                    $html .= self::menuSubItemHTML(
                        $link['visible'],
                        $link['active'],
                        $url,
                        $link['name'],
                    );
                    break;
            }
        }

        return $html . '
                        </ul>
                    </div>
                </li>
        ';
    }

    public static function renderMenu(
        string $name,
        bool   $open = false
    ): string
    {
        if (!isset(self::$menu[$name])) {
            return '';
        }

        return self::doRenderMenu(
            $name,
            self::$menu[$name],
            $open
        );
    }

    const MENU_SUPER_TOOLS = '(Super) Tools';
    const MENU_SUPER_ADMIN = '(Super) Admin';
    const MENU_SETTINGS = 'Settings';
    const MENU_CALCULATORS = 'Calculators';
    const MENU_REPORTS = 'Reports';
    const MENU_CALENDAR = 'Calendar/Tasks';
    const MENU_USER_CONTACTS = 'User / Contacts';
    const MENU_PIPELINE = 'Pipeline';
    const MENU_CREATE_LOAN = 'Create Loan';
    const MENU_CREATE_LOAN_BORROWER = 'Apply for a Loan';

    public static function initMenu()
    {
        $isAllowDashboardUserRoleSuper = menu::$allowDashboard == 1 || menu::$userRole == glUserRole::SUPER;
        $notUserRoleClientRest = !(menu::$userRole == glUserRole::CLIENT || menu::$userRole == glUserRole::REST);
        $isUserRoleAuditorCFPBAuditorAuditorManager = menu::$userRole == glUserRole::AUDITOR || menu::$userRole == glUSerRole::CFPB_AUDITOR || menu::$userRole == glUserRole::AUDITOR_MANAGER;
        $isUserRoleCFBPAuditorAuditorManager = menu::$userRole == glUSerRole::CFPB_AUDITOR || menu::$userRole == glUserRole::AUDITOR_MANAGER;
        $isUserRoleClient = menu::$userNumber == glUserRole::CLIENT;
        $isUserRoleSuper = menu::$userRole == glUserRole::SUPER;
        $resultBorrower = menu::getResultBorrower();

        self::$isAllowDashboardUserRoleSuper = $isAllowDashboardUserRoleSuper;
        self::$notUserRoleClientRest = $notUserRoleClientRest;
        self::$isUserRoleAuditorCFPBAuditorAuditorManager = $isUserRoleAuditorCFPBAuditorAuditorManager;
        self::$isUserRoleCFBPAuditorAuditorManager = $isUserRoleCFBPAuditorAuditorManager;
        self::$isUserRoleClient = $isUserRoleClient;
        self::$isUserRoleSuper = $isUserRoleSuper;
        self::$resultBorrower = $resultBorrower;

        $temp = explode('/', $_SERVER['REQUEST_URI']);
        $pageRequested = $temp[sizeof($temp) - 1];


        $notClientBranchAgent = menu::$userGroup != glUserGroup::CLIENT && menu::$userGroup != glUserGroup::BRANCH && menu::$userGroup != glUserGroup::AGENT;

        $MENU_CREATE_LOAN = (menu::$userGroup == 'Client') ? menu::MENU_CREATE_LOAN_BORROWER : menu::MENU_CREATE_LOAN;

        self::$REQUEST_URI = $_SERVER['REQUEST_URI'];

//        Debug(
//            menu::$getCurrentPath,
//            menu::$agentListURL,
//            in_array(menu::$getCurrentPageFromURL, [
//                'automatedActions.php',
//                'automatedActivityLog.php',
//                'automatedCancelledActivityLog.php',
//            ])
//            ||  menu::$getCurrentPath == '/backoffice/settings/automated_rules_v2'
//        );

//        Debug( in_array(menu::$getCurrentPageFromURL, [
//                'processorList.php',
//                'createProcessor.php',
//                'branchList.php',
//                'createBranch.php',
//                'processingCompanyList.php',
//                'createAgent.php',
//                'loanofficerList.php',
//                'schoolList.php',
//                'schoolCreate.php',
//                'marketPlaceLPList.php',
//                'clientCreate.php',
//            ]) || menu::$getCurrentPath == '/backoffice/clients'
//            || menu::$getCurrentPath == '/backoffice/contacts'
//            || menu::$getCurrentPath == '/backoffice/lenders'
//            || menu::$getCurrentPath == '/backoffice/brokers'
//            || menu::$getCurrentPath == '/agent/brokers'
//            || menu::$getCurrentPath == '/backoffice/loan_officers'
//            || menu::$getCurrentPath == '/backoffice/lenders/create'
//            || menu::$getCurrentPath == '/branch/clients'
//            || menu::$getCurrentPath == '/agent/clients');

        self::$menu = [
            $MENU_CREATE_LOAN => [
                'icon'  => 'flaticon-doc icon-md',
                'visible' => PageVariables::$allowToCreateFiles ,

                'links' => [
                    menu::$quickAppWebURL => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => in_array('HMLO', menu::$PCModulesArray)
                            || in_array('loc', menu::$PCModulesArray)
                            || in_array('EF', menu::$PCModulesArray)
                            || in_array('LM', menu::$PCModulesArray)
                            || in_array('SS', menu::$PCModulesArray)
                            || in_array('MF', menu::$PCModulesArray)
                            || htmlspecialchars($_GET['moduleCode']) == 'HMLO',
                        'active'  => (strpos($pageRequested, 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=QAPP') !== false) ?? true,
                        'name'    => 'Quick App',
                    ],
                    menu::$fullAppWebURL  => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => in_array('HMLO', menu::$PCModulesArray)
                            || in_array('loc', menu::$PCModulesArray)
                            || in_array('EF', menu::$PCModulesArray)
                            || in_array('LM', menu::$PCModulesArray)
                            || in_array('SS', menu::$PCModulesArray)
                            || in_array('MF', menu::$PCModulesArray)
                            || htmlspecialchars($_GET['moduleCode']) == 'HMLO',
                        'active'  => (strpos($pageRequested, 'LMRequest.php?eOpt=0&cliType=PC&tabOpt=LI') !== false) ?? true,
                        'name'    => 'Full App',//
                    ],
                ],
            ],

            menu::MENU_PIPELINE => [
                'icon'    => 'flaticon2-list-2 icon-md',
                'visible' => !(menu::$userRole == glUserRole::AUDITOR || menu::$userRole == glUserRole::CFPB_AUDITOR || menu::$userRole == glUserRole::AUDITOR_MANAGER),
                'links'   => [
                    menu::$pipelineLiteUrl                                                                                                                                                                                                                               => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userGroup == glUserGroup::SUPER,
                        'active'  => menu::$getCurrentPageFromURL == 'mypipelinelite.php',
                        'name'    => 'Pipeline - Lite'
                    ],
                    menu::$newPipe2URL                                                                                                                                                                                                                                   => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true, // menu::$userRole != glUserRole::SUPER,  // PCID is required for Pipeline report, do not reuse it for Super
                        'active'  => menu::$getCurrentPageFromURL == 'myPipeline.php' && !menu::$getCurrentQUERY_STRING, //
                        'name'    => 'Pipeline - Main',
                    ],
                    menu::$HOMEPipeURL . '?fileType=b92575b6c7c7374f'                                                                                                                                                                                                    => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' =>
                            menu::$userGroup != glUserGroup::SUPER // PCID is required for Pipeline report, do not reuse it for Super
                            && (
                                (menu::$subscribeToHOME == 1 && menu::$subscribePCToHOME == 1)
                                || menu::$userGroup == glUserGroup::SALES
                            ),
                        'active'  => menu::$getCurrentPageFromURL == 'myPipeline.php' && menu::$getCurrentQUERY_STRING == 'fileType=b92575b6c7c7374f',
                        'name'    => 'HOME Report Pipeline'
                    ],
                    menu::$HOMEPipeURL . '?fileType=' . cypher::myEncryption('LA')                                                                                                                                                                                       => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userGroup != glUserGroup::SUPER // PCID is required for Pipeline report, do not reuse it for Super
                            && ((menu::$allowToCFPBSubmitForPC == 1 && menu::$allowToViewCFPBPipeline == 1) || (menu::$allowCFPBAuditing == 1)),
                        'active'  => menu::$getCurrentPageFromURL == 'myPipeline.php' && menu::$getCurrentQUERY_STRING == 'fileType=' . cypher::myEncryption('LA'),
                        'name'    => 'Loan Audit Pipeline'
                    ],
                    CONST_BO_URL . 'myPipeline.php?empId=' . cypher::myEncryption(menu::$userNumber)                                                                                                                                                                     => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::MANAGER || menu::$userGroup == glUserGroup::EMPLOYEE,
                        'active'  => menu::$getCurrentPageFromURL == 'myPipeline.php' && menu::$getCurrentQUERY_STRING == 'empId=' . cypher::myEncryption(menu::$userNumber),
                        'name'    => 'My Assigned Files'
                    ],
                    menu::$creditScreeningAcQualifyPC                                                                                                                                                                                                                    => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' =>
                            ((menu::$userRole == glUserRole::MANAGER
                                    || (menu::$userRole != glUserRole::MANAGER && (menu::$pcAcqualifyId > 0 && menu::$allowToViewCreditScreening > 0
                                            && menu::$pcAcqualifyStatus > 0))) && menu::$PCID != glPCID::PCID_PROD_CV3)
                            || (menu::$PCID == glPCID::PCID_PROD_CV3 && menu::$pcAcqualifyStatus > 0 &&
                                (menu::$userRole == glUserRole::MANAGER
                                    || (menu::$userRole != glUserRole::MANAGER && menu::$pcAcqualifyId > 0 && menu::$allowToViewCreditScreening > 0))),
                        'active'  => menu::$getCurrentPageFromURL == 'creditScreening.php',
                        'name'    => 'Soft Credit Pulls'
                    ],
                    menu::$userGroup == glUserGroup::EMPLOYEE ? CONST_BO_URL . 'pricingEngine.php' : (menu::$userGroup == glUserGroup::BRANCH ? CONST_URL_BR . 'pricingEngine.php' : (menu::$userGroup == glUserGroup::AGENT ? CONST_URL_AG . 'pricingEngine.php' : '')) => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userPriceEngineStatus > 0 && menu::$pcPriceEngineStatus > 0,
                        'active'  => menu::$getCurrentPageFromURL == 'pricingEngine.php',
                        'name'    => 'Pricing Engine'
                    ]
                ],
            ],

            menu::MENU_USER_CONTACTS => [
                'icon'    => 'flaticon-users-1 icon-md',
                'visible' => menu::$userRole != glUserRole::CLIENT,
                'open'    => in_array(menu::$getCurrentPageFromURL, [
                        'processorList.php',
                        'createProcessor.php',
                        'branchList.php',
                        'createBranch.php',
                        'processingCompanyList.php',
                        'createAgent.php',
                        'agentList.php',
                        'loanofficerList.php',
                        'schoolList.php',
                        'schoolCreate.php',
                        'marketPlaceLPList.php',
                        'clientCreate.php',
                    ]) || menu::$getCurrentPath == '/backoffice/clients'
                    || menu::$getCurrentPath == '/backoffice/contacts'
                    || menu::$getCurrentPath == '/agent/contacts'
                    || menu::$getCurrentPath == '/backoffice/lenders'
                    || menu::$getCurrentPath == '/backoffice/brokers'
                    || menu::$getCurrentPath == '/branch/brokers'
                    || menu::$getCurrentPath == '/agent/brokers'
                    || menu::$getCurrentPath == '/backoffice/loan_officers'
                    || menu::$getCurrentPath == '/backoffice/lenders/create'
                    || menu::$getCurrentPath == '/branch/clients'
                    || menu::$getCurrentPath == '/agent/clients'
                ,
                'links'   => [
                    CONST_BO_URL . 'processingCompanyList.php'   => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH) && menu::$userRole != glUserRole::BRANCH && $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'processingCompanyList.php',
                        'name'    => 'List PC\'s'
                    ],
                    CONST_BO_URL . 'createProcessingCompany.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH) && menu::$userRole != glUserRole::BRANCH && $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php',
                        'name'    => 'Create PC'
                    ],
                    'Back Office'                                => [
                        'visible' => menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER ||
                                $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH)
                            && menu::$userRole != glUserRole::BRANCH,
                        'links'   => [
                            menu::$procListURL => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER ||
                                        $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH)
                                    && menu::$userRole != glUserRole::BRANCH,
                                'active'  => menu::$getCurrentPageFromURL == 'processorList.php',
                                'name'    => 'Back Office List',
                            ],
                            menu::$procURL     => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper
                                        || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH)
                                    && menu::$userRole != glUserRole::BRANCH,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessor.php',
                                'name'    => 'Create Back Office User'
                            ],
                        ],
                    ],
                    'Branch'                                     => [
                        'visible' => (((menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH) && menu::$userRole == glUserRole::REST)
                                || (menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH)
                                    && menu::$userRole != glUserRole::BRANCH && (menu::$allowEmpToCreateBranch == 1 || $isUserRoleSuper || menu::$userRole == glUserRole::MANAGER))
                                || (!(menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH) && menu::$userRole != glUserRole::AGENT)) ||
                            (menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper
                                    || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH) &&
                                menu::$userRole != glUserRole::BRANCH && (menu::$allowEmpToCreateBranch == 1
                                    || $isUserRoleSuper || menu::$userRole == glUserRole::MANAGER)),
                        'links'   => [
                            menu::$branchListURL => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                                'visible' => ((menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH) && menu::$userRole == glUserRole::REST)
                                    || (menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH)
                                        && menu::$userRole != glUserRole::BRANCH && (menu::$allowEmpToCreateBranch == 1 || $isUserRoleSuper || menu::$userRole == glUserRole::MANAGER))
                                    || (!(menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH) && menu::$userRole != glUserRole::AGENT)
                                ,
                                'active'  => menu::$getCurrentPageFromURL == 'branchList.php',
                                'name'    => 'Branch List'
                            ],
                            menu::$branchURL     => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                                'visible' => menu::$userRole != glUserRole::REST && (menu::$userRole == glUserRole::MANAGER || $isUserRoleSuper
                                        || menu::$userRole == glUserRole::REST || menu::$userRole == glUserRole::BRANCH) &&
                                    menu::$userRole != glUserRole::BRANCH && (menu::$allowEmpToCreateBranch == 1
                                        || $isUserRoleSuper || menu::$userRole == glUserRole::MANAGER),
                                'active'  => menu::$getCurrentPageFromURL == 'createBranch.php',
                                'name'    => 'Create Branch'
                            ],
                        ],
                    ],
                    'Broker'                                     => [
                        'visible' => (menu::$externalBroker == 1 && menu::$userRole == glUserRole::AGENT)
                            || (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 1)
                            || (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 0)
                            || (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 1)
                            || (menu::$userGroup == glUserGroup::EMPLOYEE && (menu::$allowEmpToSeeAgent == 1 || menu::$userRole == glUserRole::MANAGER))
                            || $isUserRoleSuper,
                        'open'    => menu::$getCurrentPath == '/backoffice/brokers'
                            || menu::$getCurrentPath == '/agent/brokers'
                            || menu::$getCurrentPath == '/branch/brokers'
                            || menu::$getCurrentPageFromURL == 'agentList.php'
                            || menu::$getCurrentPageFromURL == 'createAgent.php' && (strpos(menu::$getCurrentQUERY_STRING, 'agentType=0') !== false)
                        ,
                        'links'   => [
                            menu::$agentListURL                    => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 1)
                                    || (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 0)
                                    || (menu::$userGroup == glUserGroup::EMPLOYEE && (menu::$allowEmpToSeeAgent == 1 || menu::$userRole == glUserRole::MANAGER))
                                    || $isUserRoleSuper
                                    || (menu::$externalBroker == 1 && menu::$userRole == glUserRole::AGENT)
                                ,
                                'active'  =>
                                    menu::$getCurrentPath == '/backoffice/brokers'
                                    || menu::$getCurrentPageFromURL == 'agentList.php'
                                    || menu::$getCurrentPath == '/agent/brokers'
                                    || menu::$getCurrentPath == '/branch/brokers'
                                ,
                                'name'    => 'Broker List'
                            ],
                            menu::$createAgentURL . '?agentType=0' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 1)
                                    || menu::$userGroup == glUserGroup::EMPLOYEE && (menu::$allowEmpToSeeAgent == 1 || menu::$userRole == glUserRole::MANAGER)
                                    || $isUserRoleSuper
                                ,
                                'active'  => menu::$getCurrentPageFromURL == 'createAgent.php' && (strpos(menu::$getCurrentQUERY_STRING, 'agentType=0') !== false),
                                'name'    => 'Create Agent/Broker'
                            ],
                        ],
                    ],
                    'Loan Officer'                               => [
                        'visible' => ((menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 1)
                            || (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 0)
                            || (menu::$userGroup == glUserGroup::EMPLOYEE && (menu::$allowEmpToSeeAgent == 1 || menu::$userRole == glUserRole::MANAGER))
                            || $isUserRoleSuper),
                        'open'    => menu::$getCurrentPath == '/backoffice/loan_officers'
                            || menu::$getCurrentPath == '/agent/loan_officers'
                            || menu::$getCurrentPath == '/branch/loan_officers'
                            || menu::$getCurrentPageFromURL == 'createAgent.php' && (strpos(menu::$getCurrentQUERY_STRING, 'agentType=1') !== false)
                        ,
                        'links'   => [
                            menu::$loanOfficerListURL              => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 1)
                                    || (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 0)
                                    || (menu::$userGroup == glUserGroup::EMPLOYEE && (menu::$allowEmpToSeeAgent == 1 || menu::$userRole == glUserRole::MANAGER))
                                    || $isUserRoleSuper
                                ,
                                'active'  => menu::$getCurrentPath == '/backoffice/loan_officers',
                                'name'    => 'Loan Officer List'
                            ],
                            menu::$createAgentURL . '?agentType=1' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => (menu::$userRole == glUserRole::BRANCH && menu::$allowBranchToAddAgent == 1)
                                    || menu::$userGroup == glUserGroup::EMPLOYEE && (menu::$allowEmpToSeeAgent == 1 || menu::$userRole == glUserRole::MANAGER)
                                    || $isUserRoleSuper
                                ,
                                'active'  => menu::$getCurrentPageFromURL == 'createAgent.php' && (strpos(menu::$getCurrentQUERY_STRING, 'agentType=1') !== false),
                                'name'    => 'Create Loan Officer'
                            ],
                        ],
                    ],

                    menu::$lenderListURL => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper
                            || ((in_array('LM', menu::$PCModulesArray)
                                    || in_array('SS', menu::$PCModulesArray))
                                && menu::$userGroup == glUserGroup::EMPLOYEE
                                && menu::$userRole != glUserRole::SUPER)
                        ,
                        'active'  => menu::$getCurrentPath == '/backoffice/lenders',
                        'name'    => $isUserRoleSuper ? 'Lenders/Business Lenders' : 'Lenders / Servicers',
                    ],

                    menu::$createLenderURL => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPath == '/backoffice/lenders/create',
                        'name'    => 'Create Lenders',
                    ],

                    menu::$createBusinessLenderURL => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => in_array(menu::$getCurrentPageFromURL, ['lenderList.php', 'lenderCreate.php']),
                        'name'    => 'Business Lenders',
                    ],

                    menu::$schoolListURL => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => in_array(menu::$getCurrentPageFromURL, ['schoolList.php', 'schoolCreate.php']),
                        'name'    => 'Schools'
                    ],

                    menu::$createSchoolURL => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == menu::$createSchoolURL,
                        'name'    => 'Create School'
                    ],
                    'Contacts' => [
                        'open'    => (menu::$getCurrentPath == '/backoffice/contacts' || menu::$getCurrentPath == '/agent/contacts'),
                        'visible' => ($isUserRoleSuper
                                || (menu::$userGroup == glUserGroup::EMPLOYEE && menu::$allowToViewContactsList == 1)
                                || (menu::$userRole == glUserRole::AGENT && menu::$allowToViewContactsList == 1)
                        ),
                        'links'   => [
                            menu::$contactsListURL => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => ($isUserRoleSuper
                                    || (menu::$userGroup == glUserGroup::EMPLOYEE && menu::$allowToViewContactsList == 1)
                                    || (menu::$userRole == glUserRole::AGENT && menu::$allowToViewContactsList == 1)
                                ),
                                'active'  => menu::$getCurrentPath == '/backoffice/contacts' && (($_REQUEST['create'] ?? 0) == 0),
                                'name'    => 'Contact List'
                            ],

                            menu::$createContactsURL => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => ($isUserRoleSuper
                                    || (menu::$userGroup == glUserGroup::EMPLOYEE && menu::$allowToViewContactsList == 1)
                                    || (menu::$userRole == glUserRole::AGENT && menu::$allowToViewContactsList == 1)
                                ),
                                'active'  => menu::$getCurrentPath == '/backoffice/contacts' && ($_REQUEST['create'] ?? 0),
                                'name'    => 'Create Contact'
                            ],
                        ],
                    ],


                    menu::$marketPlaceNichesListURL      => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == menu::$marketPlaceNichesListURL,
                        'name'    => 'Marketplace - Niches List'

                    ],
                    menu::$marketPlaceLoanProgramListURL => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'marketPlaceLPList.php',
                        'name'    => 'Marketplace - Loan Program List'

                    ],
                    'Borrowers'                          => [
                        'open'    => in_array(menu::$getCurrentPageFromURL, ['clientCreate.php'])
                            || menu::$getCurrentPath == '/backoffice/clients'
                            || menu::$getCurrentPath == '/branch/clients'
                            || menu::$getCurrentPath == '/agent/clients',
                        'visible' => ($resultBorrower[0]['allowToViewAllFiles'] == 1 || menu::$userRole == glUserRole::BRANCH ||
                                menu::$userRole == glUserRole::AGENT) ||
                            (menu::$userRole == glUserRole::AGENT
                                || menu::$userRole == glUserRole::BRANCH || menu::$userRole == glUserRole::MANAGER),
                        'links'   => [
                            menu::$PLOListURL                                                                                                                                                                                           => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => $resultBorrower[0]['allowToViewAllFiles'] == 1 || menu::$userRole == glUserRole::BRANCH ||
                                    menu::$userRole == glUserRole::AGENT,
                                'active'  => menu::$getCurrentPath == '/backoffice/clients'
                                    || menu::$getCurrentPath == '/branch/clients'
                                    || menu::$getCurrentPath == '/agent/clients',
                                'name'    => 'Borrower List'
                            ],
                            menu::$userSiteURL . 'clientCreate.php?' . (menu::$userRole != glUserRole::SUPER ? 'isClient=1' : '') . (menu::$userRole == glUserRole::BRANCH ? '&encEId=' . cypher::myEncryption(menu::$userNumber) : '') => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::AGENT
                                    || menu::$userRole == glUserRole::BRANCH || menu::$userRole == glUserRole::MANAGER,
                                'active'  => menu::$getCurrentPageFromURL == 'clientCreate.php',
                                'name'    => 'Create Borrower'
                            ],
                        ],
                    ],
                ],
            ],

            menu::MENU_CALENDAR => [
                'icon'    => 'ki ki-calendar icon-md',
                'visible' => menu::$userRole == glUserRole::MANAGER
                    || menu::$userRole == glUserRole::AGENT
                    || menu::$userRole == glUserRole::BRANCH
                    || menu::$userGroup == glUserGroup::EMPLOYEE,
                'links'   => [
                    CONST_URL_BR . 'myCalendar.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::BRANCH,
                        'active'  => menu::$getCurrentPageFromURL == 'myCalendar.php',
                        'name'    => 'Calendar',
                    ],

                    CONST_URL_BR . 'taskList.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::BRANCH,
                        'active'  => menu::$getCurrentPageFromURL == 'taskList.php',
                        'name'    => 'Task List'
                    ],

                    CONST_URL_AG . 'myCalendar.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::AGENT,
                        'active'  => menu::$getCurrentPageFromURL == 'myCalendar.php',
                        'name'    => 'Calendar'

                    ],

                    CONST_URL_AG . 'taskList.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::AGENT,
                        'active'  => menu::$getCurrentPageFromURL == 'taskList.php',
                        'name'    => 'Task List'

                    ],

                    CONST_BO_URL . 'myCalendar.php' => [
                        'method'  => 'menuSubItemHTML',
                        'visible' => glUserGroup::isBackOfficeUser(self::$userGroup),
                        'active'  => menu::$getCurrentPageFromURL == 'myCalendar.php',
                        'name'    => 'Calendar'

                    ],

                    CONST_BO_URL . 'taskList.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => glUserGroup::isBackOfficeUser(self::$userGroup),
                        'active'  => menu::$getCurrentPageFromURL == 'taskList.php',
                        'name'    => 'Task List'

                    ],

                    'Create Client Task' => [
                        'method'  => self::MENU_ITEM_TYPE_MODAL,
                        'visible' => menu::$allowToCreateTasks == 1,
                        'active'  => menu::$getCurrentPageFromURL == 'addTask.php',
                        'href'    => CONST_URL_POPS . 'addTask.php',
                        'target'  => 'exampleModal1',
                        'name'    => 'Create Client Task',
                        'wsize'   => 'modal-xl',
                        'id'      => 'PCID=' . cypher::myEncryption(menu::$PCID) . '&taskOpt=' . cypher::myEncryption(glUserRole::CLIENT) . '&opt=menu'
                    ],

                    'Create Client Reminder' => [
                        'method'  => self::MENU_ITEM_TYPE_MODAL,
                        'visible' => menu::$allowToCreateTasks == 1,
                        'active'  => menu::$getCurrentPageFromURL == 'addTask.php',
                        'href'    => CONST_URL_POPS . 'addTask.php',
                        'target'  => 'exampleModal1',
                        'name'    => 'Create Client Reminder',
                        'wsize'   => 'modal-xl',
                        'id'      => 'PCID=' . cypher::myEncryption(menu::$PCID) . '&taskOpt=' . cypher::myEncryption(glUserRole::CLIENT) . '&opt=menu'
                    ],
                ],
            ],


            menu::MENU_REPORTS => [
                'icon'  => 'flaticon2-graph-1 icon-md',
                'open'  => menu::$getCurrentPath == '/backoffice/reports/mail_queue_files/calendar'
                    || menu::$getCurrentPath == '/backoffice/reports/mail_queue_files'
                    || menu::$getCurrentPath == '/backoffice/reports/mail_queue_marketing/calendar'
                    || menu::$getCurrentPath == '/backoffice/reports/mail_queue_marketing'
                    || menu::$getCurrentPath == '/backoffice/reports/bounced_emails/calendar'
                    || menu::$getCurrentPath == '/backoffice/reports/bounced_emails'
                    || menu::$getCurrentPageFromURL == 'unSubscribeList.php'
                    || stristr(self::$REQUEST_URI, '/backoffice/servicing') !== false
                    || stristr(self::$REQUEST_URI, '/backoffice/reports/thirdparty') !== false
                    || $pageRequested === 'email_queue'
                    || menu::$getCurrentPageFromURL == 'billingReport.php'
                    || menu::$getCurrentPageFromURL == '2FAVerificationLog.php'
                    || menu::$getCurrentPageFromURL == 'errorLog.php'
                    || menu::$getCurrentPageFromURL == 'groupingServices.php'
                    || menu::$getCurrentPageFromURL == 'analytics.php'
                    || menu::$getCurrentPageFromURL == 'rawUserAccess.php'
                    || menu::$getCurrentPageFromURL == 'pipelineAccess.php'
                    || menu::$getCurrentPageFromURL == 'packageAccess.php'
                    || menu::$getCurrentPageFromURL == 'eSignUsage.php'
                    || menu::$getCurrentPageFromURL == 'getActiveUser.php'
                    || menu::$getCurrentPageFromURL == 'marketingMailQueueList.php'
                    || menu::$getCurrentPageFromURL == 'employeeNotesReport.php'
                    || menu::$getCurrentPageFromURL == 'faxedFiles.php'
                    || menu::$getCurrentPageFromURL == 'RAMPaymentReport.php'
                ,
                'links' => [
                    CONST_BO_URL . 'servicing'          => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::MANAGER,
                        'active'  => stristr(self::$REQUEST_URI, '/backoffice/servicing') !== false,
                        'name'    => 'Servicing',
                    ],
                    CONST_BO_URL . 'reports/thirdparty' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::MANAGER,
                        'active'  => stristr(self::$REQUEST_URI, '/backoffice/reports/thirdparty') !== false,
                        'name'    => '3rd Party Services',
                    ],

                    CONST_BO_URL . 'reports/email_queue' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => $pageRequested === 'email_queue',
                        'name'    => 'Email Queue Check',
                    ],

                    CONST_BO_URL . 'billingReport.php?newPg=1' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::BRANCH
                            || (menu::$userRole == glUserRole::AGENT && menu::$userSeeBilling == 1)
                            || (menu::$userGroup == glUserGroup::EMPLOYEE && menu::$userSeeBilling == 1)
                        ,
                        'active'  => menu::$getCurrentPageFromURL == 'billingReport.php',
                        'name'    => 'Billing / Payables',
                    ],

                    CONST_BO_URL . 'reports/bounced_emails/calendar' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $notClientBranchAgent,
                        'active'  => stristr(self::$REQUEST_URI, '/backoffice/reports/bounced_emails') !== false,
                        'name'    => 'Bounced Emails'
                    ],

                    CONST_BO_URL . '2FAVerificationLog.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == '2FAVerificationLog.php',
                        'name'    => '2FA Log',
                    ],

                    CONST_BO_URL . 'errorLog.php'  => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'errorLog.php',
                        'name'    => 'DB Error Log'
                    ],
                    menu::$groupingServicesURL     => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'groupingServices.php',
                        'name'    => 'Grouping Loan Programs (Report)',
                    ],
                    CONST_BO_URL . 'analytics.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'analytics.php',
                        'name'    => 'Analytics',
                    ],

                    CONST_BO_URL . 'rawUserAccess.php'                     => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'rawUserAccess.php',
                        'name'    => 'User Access',
                    ],
                    CONST_BO_URL . 'pipelineAccess.php'                    => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'pipelineAccess.php',
                        'name'    => 'Pipeline Access',
                    ],
                    CONST_BO_URL . 'packageAccess.php'                     => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'packageAccess.php',
                        'name'    => 'Package Access',
                    ],
                    CONST_BO_URL . 'eSignUsage.php'                        => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'eSignUsage.php',
                        'name'    => 'Esign Usage'
                    ],
                    CONST_BO_URL . 'getActiveUser.php'                     => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'getActiveUser.php',
                        'name'    => 'Active Users'
                    ],
                    CONST_BO_URL . 'reports/mail_queue_files/calendar'     => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper || menu::$userGroup == glUserGroup::EMPLOYEE,
                        'active'  => menu::$getCurrentPath == '/backoffice/reports/mail_queue_files/calendar'
                            || menu::$getCurrentPath == '/backoffice/reports/mail_queue_files'
                        ,
                        'name'    => 'Mail Queue - File Related',
                    ],
                    CONST_BO_URL . 'reports/mail_queue_marketing/calendar' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper || menu::$userGroup == glUserGroup::EMPLOYEE,
                        'active'  => menu::$getCurrentPath == '/backoffice/reports/mail_queue_marketing/calendar'
                            || menu::$getCurrentPath == '/backoffice/mail_queue_marketing/mail_queue_files',
                        'name'    => 'Mail Queue - Marketing Related',
                    ],

                    CONST_BO_URL . 'employeeNotesReport.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userGroup == glUserGroup::EMPLOYEE && (menu::$userRole == glUserRole::MANAGER || menu::$userNumber == glUserNumber::USER_6955),
                        'active'  => menu::$getCurrentPageFromURL == 'employeeNotesReport.php',
                        'name'    => 'Employee - Reports',
                    ],

                    CONST_BO_URL . 'faxedFiles.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userGroup == glUserGroup::EMPLOYEE || $isUserRoleSuper,
                        'active'  => menu::$getCurrentPageFromURL == 'faxedFiles.php',
                        'name'    => 'Faxed - Files',
                    ],

                    CONST_URL_AG . 'faxedFiles.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::AGENT
                            || menu::$userRole == glUserRole::BRANCH,
                        'active'  => menu::$getCurrentPageFromURL == 'faxedFiles.php',
                        'name'    => 'Faxed Files'
                    ],
                    CONST_URL_BR . 'faxedFiles.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::MANAGER && menu::$allowToAccessRAM == 1,
                        'active'  => menu::$getCurrentPageFromURL == 'RAMPaymentReport.php',
                        'name'    => 'RAM Payment Report',
                    ],

                    CONST_BO_URL . 'unSubscribeList.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => $isUserRoleSuper || menu::$userGroup == glUserGroup::EMPLOYEE,
                        'active'  => menu::$getCurrentPageFromURL == 'unSubscribeList.php',
                        'name'    => 'Unsubscribed',
                    ],
                ],
            ],

            menu::MENU_CALCULATORS => [
                'icon'    => 'fas fa-calculator',
                'visible' => !(menu::$PCID == 4257 && in_array(menu::$userGroup, [glUserGroup::AGENT, glUserGroup::CLIENT])),
                'open'    => menu::$getCurrentPageFromURL == 'biWeeklyMortgageCalc.php',
                'links'   => [
                    CONST_BO_URL . 'biWeeklyMortgageCalc.php?dispOpt=ca'   => [
                        'method'    => self::MENU_ITEM_TYPE_POPUP,
                        'name'      => 'Bi-Weekly Mortgage',
                        'width'     => 615,
                        'height'    => 590,
                        'scrollOpt' => 0,
                    ],
                    CONST_BO_URL . 'blendedRatePaymentCalc.php?dispOpt=ca' => [
                        'method'    => self::MENU_ITEM_TYPE_POPUP,
                        'name'      => 'Blended Rate Payment',
                        'width'     => 615,
                        'height'    => 590,
                        'scrollOpt' => 0
                    ],
                    CONST_BO_URL . 'debtConsolidationCalc.php?dispOpt=ca'  => [
                        'method'    => self::MENU_ITEM_TYPE_POPUP,
                        'name'      => 'Debt Consolidation',
                        'width'     => 760,
                        'height'    => 750,
                        'scrollOpt' => 1
                    ],
                    CONST_BO_URL . 'interestCalc.php?dispOpt=ca'           => [
                        'method'    => self::MENU_ITEM_TYPE_POPUP,
                        'name'      => 'Interest Calculator',
                        'width'     => 615,
                        'height'    => 590,
                        'scrollOpt' => 0
                    ],
                    CONST_BO_URL . 'loanAmortizationCalc.php?dispOpt=ca'   => [
                        'method'    => self::MENU_ITEM_TYPE_POPUP,
                        'name'      => 'Loan Amortization',
                        'width'     => 815,
                        'height'    => 700,
                        'scrollOpt' => 0
                    ],
                    CONST_BO_URL . 'loanCalc.php?dispOpt=ca'               => [
                        'method'    => self::MENU_ITEM_TYPE_POPUP,
                        'name'      => 'Loan Calculator',
                        'width'     => 615,
                        'height'    => 590,
                        'scrollOpt' => 0
                    ],
                    CONST_BO_URL . 'mortgageCalc.php?dispOpt=ca'           => [
                        'method'    => self::MENU_ITEM_TYPE_POPUP,
                        'name'      => 'Mortgage PITI',
                        'width'     => 615,
                        'height'    => 590,
                        'scrollOpt' => 0
                    ],
                ],
            ],

            menu::MENU_SETTINGS => [
                'icon'    => 'ki ki-wrench icon-md',
                'visible' => menu::$userRole == glUserRole::MANAGER,
                'open'    => in_array(menu::$getCurrentPageFromURL, [
                        'leadPostingAPI.php',
                        'biWeeklyMortgageCalc.php',
                        'importLeadsV2.php',
                        'importLeads.php',
                        'customDocLib.php',
                        'manageLMREmailTemplate.php',
                        'createProcessingCompany.php',
                        'automatedActions.php',
                        'automatedActivityLog.php',
                        'automatedCancelledActivityLog.php',
                    ])
                    || menu::$getCurrentPath == '/backoffice/settings/custom_fields'
                    || menu::$getCurrentPath == '/backoffice/settings/automated_rules_v2'
                    || menu::$getCurrentPath == '/backoffice/settings/automated_rules_v2/edit'
                ,
                'links'   => [
                    'Platform Settings' => [
                        'open'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' || menu::$getCurrentPath == '/backoffice/settings/custom_fields',
                        'links' => [
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID)                 => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userGroup == glUserGroup::EMPLOYEE,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && !($_REQUEST['tabNumb'] ?? 0),
                                'name'    => 'Company Info',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=2'  => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userGroup == glUserGroup::EMPLOYEE,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 2,
                                'name'    => 'Doc Library'
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=20'  => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userGroup == glUserGroup::EMPLOYEE,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 20,
                                'name'    => '3rd Party Integrations'
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=4'  => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::MANAGER,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 4,
                                'name'    => 'Required Docs',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=19' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => true,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 19,
                                'name'    => 'Doc Statuses',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=21' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => true,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 21,
                                'name'    => 'Borrower Profile Required Docs',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=5'  => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::MANAGER,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 5,
                                'name'    => 'Workflow',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=6'  => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::MANAGER,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 6,
                                'name'    => 'File Status',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=7'  => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::MANAGER,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 7,
                                'name'    => 'File Sub-status',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=7'  => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userGroup == glUserGroup::EMPLOYEE,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 7,
                                'name'    => 'File Sub-status',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=22' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::MANAGER && menu::$showDrawManagementTab,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 22,
                                'name'    => 'Draw Management',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=11' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userGroup == glUserGroup::EMPLOYEE,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 11,
                                'name'    => 'Web Forms / Logins',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=13' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::MANAGER,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 13,
                                'name'    => 'Custom Loan Guidelines',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=14' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userGroup == glUserGroup::EMPLOYEE,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 14,
                                'name'    => 'File Tab Display Order',
                            ],
                            CONST_BO_URL . 'createProcessingCompany.php?pcId=' . cypher::myEncryption(menu::$PCID) . '&tabNumb=16' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::MANAGER,
                                'active'  => menu::$getCurrentPageFromURL == 'createProcessingCompany.php' && $_REQUEST['tabNumb'] == 16,
                                'name'    => 'Form Fields',
                            ],
                            '/backoffice/settings/custom_fields'                                                                   => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                                'visible' => menu::$userRole == glUserRole::MANAGER,
                                'active'  => menu::$getCurrentPath == '/backoffice/settings/custom_fields',
                                'name'    => 'Custom Form Fields',
                            ],
                        ],
                    ],
                    'Automation'        => [
                        'open'  => in_array(menu::$getCurrentPageFromURL, [
                                'automatedActions.php',
                                'automatedActivityLog.php',
                                'automatedCancelledActivityLog.php',
                            ])
                            || menu::$getCurrentPath == '/backoffice/settings/automated_rules_v2'
                            || menu::$getCurrentPath == '/backoffice/settings/automated_rules_v2/edit'

                        ,
                        'links' => [
                            CONST_BO_URL . 'automatedActions.php'           => [
                                'method'           => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible'          => true,
                                'active'           => menu::$getCurrentPageFromURL == 'automatedActions.php' && $_REQUEST['tabNumb'] != 4,
                                'name'             => 'Actions',
                                'automationEnable' => menu::$allowAutomation,
                            ],
                            CONST_BO_URL . 'settings/automated_rules_v2'    => [
                                'method'           => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible'          => PageVariables::PC()->allowAutomatedRulesV2,
                                'active'           => menu::$getCurrentPath == '/backoffice/settings/automated_rules_v2'
                                    || menu::$getCurrentPath == '/backoffice/settings/automated_rules_v2/edit'

                                ,
                                'name'             => 'Rules V2',
                                'automationEnable' => menu::$allowAutomation,
                            ],
                            CONST_BO_URL . 'automatedActions.php?tabNumb=4' => [
                                'method'           => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible'          => true,
                                'active'           => menu::$getCurrentPageFromURL == 'automatedActions.php' && $_REQUEST['tabNumb'] == 4,
                                'name'             => 'Rules',
                                'automationEnable' => menu::$allowAutomation,
                            ],
                            CONST_BO_URL . 'automatedActivityLog.php'       => [
                                'method'           => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible'          => true,
                                'active'           => menu::$getCurrentPageFromURL == 'automatedActivityLog.php',
                                'name'             => 'Activity Log',
                                'automationEnable' => menu::$allowAutomation,
                            ],
                            //                            CONST_BO_URL . 'automatedCancelledActivityLog.php' => [
                            //                                'method' => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                            //                                'visible' => true,
                            //                                'active' => menu::$getCurrentPageFromURL == 'automatedCancelledActivityLog.php',
                            //                                'name' => 'Cancelled Activity Log',
                            //                                'automationEnable' => menu::$allowAutomation,
                            //                            ],
                        ],
                    ],

                    CONST_BO_URL . 'leadPostingAPI.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::MANAGER || menu::$userRole == glUserRole::BRANCH,
                        'active'  => menu::$getCurrentPageFromURL == 'leadPostingAPI.php',
                        'name'    => 'API - Lead / Data Posting'
                    ],

                    CONST_BO_URL . 'importLeadsV2.php' => [
                        'method'  => 'menuSubItemHTML',
                        'visible' => menu::$userRole == 'Manager',
                        'active'  => menu::$getCurrentPageFromURL == 'importLeadsV2.php',
                        'name'    => 'Import Data'
                    ],

                    CONST_BO_URL . 'customDocLib.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::MANAGER && MENU::$docWizard,
                        'active'  => menu::$getCurrentPageFromURL == 'customDocLib.php',
                        'name'    => 'Doc Wizard Tool'
                    ],

                    menu::$templateURL => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => menu::$userRole == glUserRole::AGENT || menu::$userRole == glUserRole::MANAGER,
                        'active'  => menu::$getCurrentPageFromURL == 'manageLMREmailTemplate.php',
                        'name'    => 'E-mail Wizard Tool'
                    ],


                    //                    CONST_BO_URL . 'helpPage.php' => [
                    //                        'method' => 'menuSubItemHTML',
                    //                        'visible' => menu::$userRole == 'Manager' || menu::$userRole == 'Branch' || menu::$userRole == 'Agent' || menu::$userGroup == 'Employee',
                    //                        'active' => false,
                    //                        'name' => 'E-Help Page Tool'
                    //                    ],
                    //
                    //                    CONST_BO_URL . 'helpPage.php?learn=yes' => [
                    //                        'method' => 'menuSubItemHTML',
                    //                        'visible' => menu::$userGroup == 'Employee',
                    //                        'active' => menu::$getCurrentPageFromURL === 'helpPage.php' && ($_REQUEST['learn'] ?? '') === 'yes',
                    //                        'name' => 'LendingWise University'
                    //                    ],
                ],
            ],

            menu::MENU_SUPER_ADMIN => [
                'icon'    => 'fas fa-user-secret icon-md',
                'visible' => menu::$userRole == glUserRole::SUPER,
                'open'    => in_array(menu::$getCurrentPageFromURL, [
                        'libPackage.php',
                        menu::$generalFAQURL,
                        menu::$reductionEstimatorURL,
                    ]) || in_array(menu::$getCurrentPath, [
                        '/backoffice/settings/announcement',
                        '/backoffice/reports/pcstorage',
                    ]),
                'links'   => [
                    CONST_BO_URL . 'libPackage.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => false,
                        'name'    => 'Package',
                    ],
                    menu::$announcementURL          => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPath == '/backoffice/settings/announcement',
                        'name'    => 'Announcement'
                    ],
                    menu::$generalFAQURL            => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => false,
                        'name'    => 'General FAQs'
                    ],
                    menu::$reductionEstimatorURL    => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => false,
                        'name'    => 'HAMP Reduction Est.'

                    ],
                    '/backoffice/reports/pcstorage' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPath == '/backoffice/reports/pcstorage',
                        'name'    => 'PC\'s Storage'
                    ],
                ],
            ],

            menu::MENU_SUPER_TOOLS => [
                'icon'    => 'fas fa-user-secret icon-md',
                'visible' => menu::$userRole == glUserRole::SUPER,
                'open'    => menu::$getCurrentPageFromURL == 'bcLookup.php' ||
                    menu::$getCurrentPageFromURL == 'importFiles.php' ||
                    menu::$getCurrentPageFromURL == 'createServiceType.php' ||
                    menu::$getCurrentPageFromURL == 'moduleCreate.php' ||
                    menu::$getCurrentPageFromURL == 'masterFormFieldsType.php' ||
                    menu::$getCurrentPageFromURL == 'masterPortFormFields.php' ||
                    menu::$getCurrentPageFromURL == 'copyMigrations.php',
                'links'   => [
                    CONST_BO_URL . 'importFiles.php'          => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPageFromURL == 'importFiles.php',
                        'name'    => 'Import Files',
                    ],
                    CONST_BO_URL . 'bcLookup.php'             => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPageFromURL == 'bcLookup.php',
                        'name'    => 'Lookup a BRC Code',
                    ],
                    CONST_BO_URL . 'createServiceType.php'    => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPageFromURL == 'createServiceType.php',
                        'name'    => 'Service Type',
                    ],
                    CONST_BO_URL . 'createUserRole.php'       => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPageFromURL == 'createUserRole.php',
                        'name'    => 'User Role',
                    ],
                    CONST_BO_URL . 'moduleCreate.php'         => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPageFromURL == 'moduleCreate.php',
                        'name'    => 'Modules',
                    ],
                    CONST_BO_URL . 'masterFormFieldsType.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPageFromURL == 'masterFormFieldsType.php',
                        'name'    => 'Form Fields',
                    ],
                    CONST_BO_URL . 'masterPortFormFields.php' => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPageFromURL == 'masterPortFormFields.php',
                        'name'    => 'Port Form Fields',
                    ],
                    'Account Default Settings'                => [
                        'visible' => menu::$userGroup == glUserRole::SUPER,
                        'links'   => [
                            CONST_BO_URL . 'defaultPCSettings.php?tabNumb=4' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => true,
                                'active'  => false,
                                'name'    => 'Required Docs',
                            ],
                            CONST_BO_URL . 'defaultPCSettings.php?tabNumb=5' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => true,
                                'active'  => false,
                                'name'    => 'Work Flow',
                            ],
                            CONST_BO_URL . 'defaultPCSettings.php?tabNumb=6' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => true,
                                'active'  => false,
                                'name'    => 'File Status',
                            ],
                            CONST_BO_URL . 'defaultPCSettings.php?tabNumb=7' => [
                                'method'  => self::MENU_ITEM_TYPE_SUB_SUB_ITEM,
                                'visible' => true,
                                'active'  => false,
                                'name'    => 'File Sub-Status',
                            ],
                        ],
                    ],
                    CONST_BO_URL . 'copyMigrations.php'       => [
                        'method'  => self::MENU_ITEM_TYPE_SUB_ITEM,
                        'visible' => true,
                        'active'  => menu::$getCurrentPageFromURL == 'copyMigrations.php',
                        'name'    => 'System Copy',
                    ],
                ],
            ],
        ];
    }

    public static function getResultBorrower(): ?array
    {
        $borrowerSql = ' select  allowToViewAllFiles from tblAdminUsers where activeStatus = 1 ';
        if (menu::$userNumber > 0) {
            $borrowerSql .= ' and AID in (' . menu::$userNumber . ')';
        }
        if (menu::$PCID > 0) {
            $borrowerSql .= ' and processingCompanyId in(' . menu::$PCID . ')';
        }
        return Database2::getInstance()->fetchRecords(['qry' => $borrowerSql]);
    }

    public static function menuModal(
        bool   $visible,
        bool   $active,
        string $href,
        string $target,
        string $name,
        string $wsize,
        string $id
    ): string
    {

        if (!$visible) {
            return '';
        }

        return '
        <li
            class="menu-item menu-item-submenu ' . ($active ? 'menu-item-active' : '') . '"
            aria-haspopup="true"
            data-menu-toggle="hover">
            <span
                style="cursor: pointer"
                data-href="' . $href . '"
                class="menu-link menu-toggle"
                data-toggle="modal"
                data-target="#' . $target . '"
                data-name="' . $name . '"
                data-wsize="' . $wsize . '"
                data-id="' . $id . '&opt=menu">
                <i class="menu-bullet menu-bullet-line">
                    <span></span>
                </i>
                <span class="menu-text">' . $name . '</span>
            </span>
        </li>
        ';
    }

    public static function menuPopUp(
        string $popup,
        string $name,
        int    $width,
        int    $height,
        int    $scrollOpt
    ): string
    {
        return '
            <li class="menu-item" aria-haspopup="true">
                <span
                    style="cursor:pointer;"
                    onclick="scriptPopUp(\'' . $popup . '\', ' . $width . ', ' . $height . ', ' . $scrollOpt . ');"
                    class="menu-link">
                    <i class="menu-bullet menu-bullet-line">
                        <span></span>
                    </i>
                        <span class="menu-text">' . $name . '</span>
                </span>
            </li>
        ';
    }

    public static function menuSubSubItemHTML(
        ?bool   $visible,
        bool   $active,
        string $url,
        string $name,
        string $automationEnable = null
    ): string
    {
        if (!$visible) {
            return '';
        }
        return '
            <li class="menu-item ' . ($active ? 'menu-item-active' : '') . '" aria-haspopup="true">
                <a ' . ($automationEnable ? 'href="' . $url . '"' : '') . '
                   class="menu-link ' . ($automationEnable ? '' : 'automationPopup') . '">
                    <i class="menu-bullet menu-bullet-dot">
                        <span></span>
                    </i>
                    <span class="menu-text" id=menu_' . Strings::removeSpaceWithSpecialChars($name) . '>' . $name . '</span>
                </a>
            </li>
        ';
    }

    public static function menuSubItemHTML(bool $visible, bool $active, string $url, string $name): string
    {
        if (!$url || !$visible) {
            return '';
        }

        return '
            <li class="menu-item menu-item-submenu ' . ($active ? ' menu-item-active ' : '') . ' font-weight-bold " aria-haspopup="true" data-menu-toggle="hover">
                <a href="' . $url . '" class="menu-link menu-toggle">
                    <i class="menu-bullet menu-bullet-line">
                        <span></span>
                    </i>
                    <span class="menu-text" id=menu_' . Strings::removeSpaceWithSpecialChars($name) . '>' . $name . '</span>
                </a>
            </li>
        ';
    }

    public static function menuItemHTML(
        bool   $visible,
        bool   $active,
        string $url,
        string $name,
        string $icon = '',
        string $target = ''
    ): string
    {
        if (!$visible) {
            return '';
        }

        return '
            <li class="menu-item ' . ($active ? ' menu-item-active ' : '') . '" aria-haspopup="true">
                <a href="' . $url . '"
                ' . ($target ? 'target=' . $target : '') . '
                   class="menu-link">
                        <span class="menu-icon">' . ($icon ? '<i class="' . $icon . '"></i>' : '<i class="menu-bullet menu-bullet-dot"></i>') . '</span>

                    <span class="menu-text" id=menu_' . Strings::removeSpaceWithSpecialChars($name) . '>' . $name . '</span>
                </a>
            </li>
        ';
    }

    public static function menuLogo(): string
    {
        return '
        <a href="' . menu::$dashURL . '" class="brand-logo">
            <img alt="" src="' . (menu::$userLogo ?: '/assetsNew/media/logos/owleymeansbusiness.png') . '" />
        </a>
        <button class="brand-toggle btn btn-sm px-0" id="kt_aside_toggle">
            <span class="svg-icon svg-icon svg-icon-xl">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                     width="24px" height="24px" viewBox="0 0 24 24">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"/>
                        <path d="M5.29288961,6.70710318 C4.90236532,6.31657888 4.90236532,5.68341391 5.29288961,5.29288961 C5.68341391,4.90236532 6.31657888,4.90236532 6.70710318,5.29288961 L12.7071032,11.2928896 C13.0856821,11.6714686 13.0989277,12.281055 12.7371505,12.675721 L7.23715054,18.675721 C6.86395813,19.08284 6.23139076,19.1103429 5.82427177,18.7371505 C5.41715278,18.3639581 5.38964985,17.7313908 5.76284226,17.3242718 L10.6158586,12.0300721 L5.29288961,6.70710318 Z"
                              fill="#000000" fill-rule="nonzero"
                              transform="translate(8.999997, 11.999999) scale(-1, 1) translate(-8.999997, -11.999999)"/>
                        <path d="M10.7071009,15.7071068 C10.3165766,16.0976311 9.68341162,16.0976311 9.29288733,15.7071068 C8.90236304,15.3165825 8.90236304,14.6834175 9.29288733,14.2928932 L15.2928873,8.29289322 C15.6714663,7.91431428 16.2810527,7.90106866 16.6757187,8.26284586 L22.6757187,13.7628459 C23.0828377,14.1360383 23.1103407,14.7686056 22.7371482,15.1757246 C22.3639558,15.5828436 21.7313885,15.6103465 21.3242695,15.2371541 L16.0300699,10.3841378 L10.7071009,15.7071068 Z"
                              fill="#000000" fill-rule="nonzero" opacity="0.3"
                              transform="translate(15.999997, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-15.999997, -11.999999)"/>
                    </g>
                </svg>
            </span>
        </button>
        ';
    }
}
