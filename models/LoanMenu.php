<?php

namespace models;

use models\composite\oFile\getFileInfo\fileSubstatusInfo;
use models\composite\oFile\getFileInfo\PCSubStatusInfo;
use models\composite\oPC\getFileTabsForPCModules;
use models\constants\gl\glUserRole;
use models\lendingwise\tblPCHMLOBasicLoanInfo;
use models\myFileInfo\branchModuleInfo;
use models\standard\HTTP;
use models\types\strongType;
use models\composite\oEmail\alertLoanProgramNull;
use models\composite\oFile\getFileInfo;
use models\composite\oPC\getAppFormFields;
use models\composite\oUser\getUserInfo;
use models\constants\accessIntakeFileTabPC;
use models\constants\accessNewIntakeFileTabPC;
use models\constants\fileTabIcons;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFUModulesNotesTypeArray;
use models\constants\gl\glNotesTypeArray;
use models\constants\gl\glNotShowCFPBTabForClientLogin;
use models\constants\gl\glShowALTabForClientLogin;
use models\constants\showAssignedEmployeeToPC;
use models\constants\ShowSaleDate;
use models\constants\showTotalOwedAndOutstandingBalance;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Strings;

use models\composite\oBranch\getBranches;
use models\composite\oBranch\getBranchesForAgent;
use models\composite\oBranch\getBranchModules;
use models\composite\oBranch\getBranchServices;
use models\composite\oClient\getClientFileBranchAndAgent;
use models\composite\oEmployee\getEmployeeAssignedBranches;
use models\composite\oFile\checkFileAccess;
use models\composite\oFile\getFileModules;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getPCServiceType;
use models\composite\oPrimaryStatus\getPCPrimaryStatus;
use models\constants\gl\glAllowEmpToUpdateBranchsForFiles;
use models\standard\Arrays;
use models\standard\UserAccess;

use models\composite\oCFPBAudit\getPermissionsToEditCFPBFile;
use models\composite\oCFPBAudit\isCFPBAllowedToLock;
use models\composite\oLockFile\isFileLockedLastVal;
use models\composite\oPC\getPermissionsToEdit;

use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glHMLOLoanTerms;

class LoanMenu extends strongType
{
    public const TabToMVC = [
        'LI'    => 'full_app',
        'QAPP'  => 'quick_app',
        'CI'    => 'borrower_info',
        'HMLI'  => 'loan_info',
        'DOC'   => 'docs',
        'SO'    => 'offers',
        'MEMO'  => 'credit_memo',
        'PEN'   => 'pricing_engine',
        'NOI'   => 'adverse_action',
        'CD'    => 'credit_decision',
        'SER2'  => 'servicing',
        '1003'  => '_1003',
        'PI'    => 'property_info',
        'BD'    => 'budget_draws',
        'MP'    => 'market_place',
        'IE'    => 'personal_inc_exp',
        'AL'    => 'assets_liabilities',
        'TA'    => 'task',
        'CW'    => 'workflow',
        'BC'    => 'billing_com',
        'CON'   => 'contacts',
        'ADMIN' => 'admin',
        'TPS'   => 'third_party',
        'GOVT'  => 'hmda',
        'HUD'   => 'hud',
        'DA'    => 'deal_analysis',
        'SSS'   => 'summary',
        'MI'    => 'mort_info',
        'QA'    => 'q_a',
        'HA'    => 'hardship',
        'LP'    => 'lm_proposal',
        'LE'    => 'legal',
        'LIV2'  => 'loan_info_v2',
        'DS'    => 'deal_sizer',
        'LT'    => 'loan_terms',
        'DASH'  => 'ss_dashboard',
        'SS'    => 'short_sale',
        'SP'    => 'ss_proposal',
    ];

    public static ?int $LMRId = null;
    public static ?array $fieldsInfo = null;
    public static ?int $isSLM = null;
    public static ?int $PCID = null;
    public static ?string $branchReferralCode = null;
    public static ?int $publicUser = null;
    public static ?int $isHOALien = null;
    public static ?string $propertyAddress = null;
    public static ?string $agentReferralCode = null;
    public static ?string $entityName = null;
    public static ?string $propertyCity = null;
    public static ?array $processorCommentsArray = null;
    public static ?string $userGroup = null;
    private static ?int $isHMLO = null;
    public static ?string $borrowerName = null;
    public static ?string $propertyState = null;
    public static ?string $propertyZip = null;
    public static ?string $BorInfoMO = null;
    public static ?int $isMF = null;
    public static ?array $glFUModulesNotesTypeArray = null;
    public static ?string $userTimeZone = null;
    public static ?int $showSysGenNote = null;
    public static ?int $viewPrivateNotes = null;
    public static ?string $userRole = null;
    public static ?int $allowUserToUpdateCFPBFile = null;
    public static ?int $viewPublicNotes = null;
    private static ?int $allowCFPBAuditing = null;
    public static ?array $fileTabs = null;
    public static ?int $LMRResponseId = null;
    public static ?int $executiveId = null;
    public static ?string $fileModuleCodeForWebForm = null;
    public static ?int $primaryStatusId = null;
    public static ?array $permissionsToEdit = null;
    public static ?array $showAssignedEmployeeToPC = null;
    public static ?string $activeTab = null;
    public static ?string $op = null;
    public static ?string $mortgageNotes = null;
    public static ?array $ShowSaleDate = null;
    public static ?string $salesDate = null;
    public static ?array $showTotalOwedAndOutstandingBalance = null;
    public static ?int $totalBalanceOwed = null;
    private static ?int $allowToEdit = null;
    public static ?string $cemail = null;
    public static ?string $typeOfHMLOLoanRequesting = null;
    private static ?int $userPriceEngineStatus = null;
    private static ?int $viewSubmitOfferTab = null;
    private static ?int $allowToSeeAlowareTab = null;
    public static ?int $allowToUpdateFileAdminSection = null;
    public static ?string $clsColor = null;
    public static ?int $addedToFav = null;
    private static ?array $glShowALTabForClientLogin = null;
    private static ?int $isSLMOnly = null;
    private static ?int $allowPCUserToSubmitCFPB = null;
    private static ?array $glNotShowCFPBTabForClientLogin = null;
    private static ?array $accessIntakeFileTabPC = null;
    private static ?array $accessNewIntakeFileTabPC = null;

    /* @var branchModuleInfo[] $fileModuleInfo */
    private static ?array $fileModuleInfo = null;
    private static ?array $servicesRequested = null;
    private static ?int $showSSTab = null;
    private static ?int $allowAgentWorkflowEdit = null;
    private static ?int $pcPriceEngineStatus = null;
    private static ?int $subscribePCToHOME = null;
    private static ?int $subscribeToHOME = null;
    private static ?int $allowToViewMarketPlace = null;
    private static ?int $isLM = null;
    private static ?int $isLO = null;
    private static ?int $isLSR = null;
    private static ?array $PCClientWFServiceTypeArray = null;
    private static ?int $isEF = null;
    private static ?array $CFPBInfo = null;
    private static ?string $ftModuleCode = null;
    private static ?int $allowToAccessRAM = null;
    private static ?array $moduleRequested = null;
    private static ?array $LMRClientTypeInfo = null;
    public static ?int $allowToSendBorrower = null;
    public static ?int $allowToSendAgent = null;
    private static ?string $fileCT = null;
    private static ?string $fileCLP = null;
    private static ?array $PCStatusInfo = null;

    /* @var PCSubStatusInfo[] $PCSubStatusInfo */
    private static ?array $PCSubStatusInfo = null;
    public static ?array $fileTabIcons = null;
    private static ?array $fileLP = null;
    private static ?array $fileMC = null;
    private static ?string $submitterPCName = null;
    private static ?string $submitterEmail = null;
    private static ?string $submitterPCPhone = null;
    private static ?string $primaryStatus = null;
    private static ?string $submitterPhone = null;
    private static ?string $submitterCell = null;
    private static ?string $submittedBy = null;
    private static ?string $phoneNumber = null;
    private static ?string $borOrigEmail = null;
    private static ?string $cellNumber = null;
    private static ?string $tempSSNNumber = null;

    /* @var fileSubstatusInfo[] $fileSubstatusInfo */
    private static ?array $fileSubstatusInfo = null;
    private static ?string $ssnNumber = null;
    private static ?string $borrowerDOB = null;
    private static ?string $closingDate = null;
    private static ?string $occupancy = null;
    private static ?string $loanNumber = null;
    private static ?string $loanNumber2 = null;
    private static ?string $servicer1 = null;
    private static ?string $servicer2 = null;
    private static ?string $eOpt = null;
    private static ?string $moduleCode = null;
    private static array $glAllowEmpToUpdateBranchsForFiles;
    private static ?string $cliType = null;
    private static ?int $allowPeerstreet = null;
    private static ?int $thirdPartyServices = null;
    private static ?int $allowPCUsersToMarketPlace = null;
    public static ?int $userNumber = null;
    private static ?int $glThirdPartyServices = null;
    private static ?int $allowServicing = null;
    private static ?int $isPCActiveAloware = null;
    private static ?int $allowToEditMyFile = null;
    private static ?int $fOpt = null;
    private static ?string $propertyNeedRehab = null;
    private static ?string $userEmail = null;
    private static ?int $allowToEditCommission = null;
    private static ?int $allowClientToCreateHMLOFile = null;
    private static ?int $externalBroker = null;
    private static ?int $allowToSeeAllBrokers = null;
    private static ?array $lockInfo = null;
    private static ?string $pcClient_id = null;
    public static ?int $fileTab = null;
    private static ?array $branchList = null;
    private static ?string $assignedBranchesID = null;
    private static ?int $allowUserToUpdatePCStatus = null;
    private static ?int $isLOC = null;
    private static ?array $AssignedBranchesInfo = null;
    private static ?int $allowPCToMarketPlace = null;
    private static ?int $allowPCToSeeAlowareTab = null;
    public static ?int $thirdPartyServicesLegalDocs = null;
    /**
     * @var getFileInfo\BillingPaymentInfo[][]|null
     */
    private static ?array $billingPaymentInfoArray = null;
    /**
     * @var lendingwise\tblLMRClientType[]
     */
    private static ?array $tempLMRClientTypeInfo = null;
    /**
     * @var myFileInfo\branchClientTypeInfo[]|null
     */
    private static ?array $tempServicesRequested = null;
    /**
     * @var getFileInfo\BillingFeeInfo[][]|null
     */
    private static ?array $billingFeeInfoArray = null;
    /**
     * @var lendingwise\tblFileMFLoanTerms[]
     */
    private static ?array $MFLoanTermsInfo = null;
    /**
     * @var lendingwise\tblPCHMLOBasicLoanPropertyType[]|null
     */
    private static ?array $HMLOPCPropertyType = null;
    /**
     * @var lendingwise\tblPCHMLOBasicLoanPropertyType[]|null $HMLOPCLoanTerm
     */
    private static ?array $HMLOPCLoanTerm = null;
    /**
     * @var lendingwise\tblPCHMLOBasicLoanOccupancy[]|null
     */
    private static ?array $HMLOPCOccupancy = null;

    private static ?tblPCHMLOBasicLoanInfo $HMLOPCBasicLoanInfo = null;
    /**
     * @var State[]|null
     */
    private static ?array $HMLOPCState = null;
    private static ?int $brokerId = null;
    public static ?array $glNotesTypeArray = null;
    private static ?int $userSeeBilling = null;
    public static ?array $forms = null;
    private static ?string $validationName = null;
    private static ?string $PCPermissonToREST = null;
    private static ?string $branchApprovedByREST = null;
    private static ?int $permissionToREST = null;
    private static ?int $subscribeToREST = null;
    private static ?string $RESTSF = null;
    private static ?string $RESTFN = null;
    private static ?string $HRSF = null;
    private static ?string $HRFN = null;
    private static ?string $validationPropertyInfoTab = null;
    private static ?string $validationLoanInfoTab = null;
    private static ?string $validAdminForm = null;
    private static ?string $validationDealAnalysisTab = null;
    private static ?string $validationHMDATab = null;
    private static ?string $validationIncExpTab = null;
    private static ?string $validationAssetLiabilityTab = null;
    private static ?string $validationBillCommTab = null;
    private static ?string $validationAdverseActionTab = null;
    private static ?string $validationCreditDecisionTab = null;
    private static ?int $allowAutomation = null;

    /**
     * @var getFileModules[]
     */
    public static ?array $moduleFileTabInfo = null;

    public static function loadGlobals(): void
    {
        global $fileTab,
               $branchReferralCode,
               $agentReferralCode,
               $fOpt,
               $executiveId,
               $LMRResponseId,
               $borrowerName,
               $isMF,
               $isSLM,
               $isHOALien,
               $processorCommentsArray,
               $userTimeZone,
               $showSysGenNote,
               $permissionsToEdit,
               $allowToUpdateFileAdminSection,
               $addedToFav,
               $isSLMOnly,
               $allowPCUserToSubmitCFPB,
               $isEF,
               $moduleRequested,
               $servicesRequested,
               $isLM,
               $showSSTab,
               $isLO,
               $isLSR,
               $allowToEdit,
               $pcClient_id,
               $allowPeerstreet,
               $thirdPartyServices,
               $glThirdPartyServices,
               $allowPCUsersToMarketPlace,
               $allowToViewMarketPlace,
               $allowServicing,
               $isPCActiveAloware,
               $viewPublicNotes,
               $viewPrivateNotes,
               $userEmail,
               $allowToEditCommission,
               $allowClientToCreateHMLOFile,
               $allowToSeeAllBrokers,
               $allowToEditMyFile,
               $userGroup,
               $PCID,
               $externalBroker,
               $op,
               $isHMLO,
               $publicUser,
               $eOpt,
               $userNumber;

        global $activeTab,
               $userRole,
               $allowCFPBAuditing,
               $allowAutomation,
               $allowUserToUpdateCFPBFile,
               $allowToAccessRAM,
               $subscribePCToHOME,
               $subscribeToHOME,
               $allowToSeeAlowareTab,
               $viewSubmitOfferTab,
               $pcPriceEngineStatus,
               $userPriceEngineStatus,
               $permissionToREST,
               $userSeeBilling;

        self::$userSeeBilling = $userSeeBilling;


        self::$activeTab = $activeTab;
        self::$userRole = $userRole;
        self::$allowCFPBAuditing = $allowCFPBAuditing;
        self::$allowAutomation = $allowAutomation;
        self::$allowUserToUpdateCFPBFile = $allowUserToUpdateCFPBFile;
        self::$allowToAccessRAM = $allowToAccessRAM;
        self::$subscribePCToHOME = $subscribePCToHOME;
        self::$subscribeToHOME = $subscribeToHOME;
        self::$allowToSeeAlowareTab = $allowToSeeAlowareTab;
        self::$viewSubmitOfferTab = $viewSubmitOfferTab;
        self::$pcPriceEngineStatus = $pcPriceEngineStatus;
        self::$userPriceEngineStatus = $userPriceEngineStatus;
        self::$permissionToREST = $permissionToREST;

        self::$glAllowEmpToUpdateBranchsForFiles = glAllowEmpToUpdateBranchsForFiles::$glAllowEmpToUpdateBranchsForFiles;
        self::$allowAgentWorkflowEdit = PageVariables::$allowAgentWorkflowEdit;
        self::$ShowSaleDate = ShowSaleDate::$ShowSaleDate;
        self::$accessNewIntakeFileTabPC = accessNewIntakeFileTabPC::$accessNewIntakeFileTabPC;
        self::$glShowALTabForClientLogin = glShowALTabForClientLogin::$glShowALTabForClientLogin;
        self::$showTotalOwedAndOutstandingBalance = showTotalOwedAndOutstandingBalance::$showTotalOwedAndOutstandingBalance;
        self::$showAssignedEmployeeToPC = showAssignedEmployeeToPC::$showAssignedEmployeeToPC;
        self::$accessIntakeFileTabPC = accessIntakeFileTabPC::$accessIntakeFileTabPC;
        self::$glFUModulesNotesTypeArray = glFUModulesNotesTypeArray::$glFUModulesNotesTypeArray;
        self::$fileTabIcons = fileTabIcons::$fileTabIcons;
        self::$glNotShowCFPBTabForClientLogin = glNotShowCFPBTabForClientLogin::$glNotShowCFPBTabForClientLogin;
        self::$glNotesTypeArray = glNotesTypeArray::$glNotesTypeArray;


        self::$fileTab = $fileTab;
        self::$branchReferralCode = $branchReferralCode;
        self::$agentReferralCode = $agentReferralCode;
        self::$fOpt = $fOpt;
        self::$activeTab = $activeTab;
        self::$executiveId = $executiveId;
        self::$LMRResponseId = $LMRResponseId;
        self::$PCID = $PCID;
        self::$allowUserToUpdateCFPBFile = $allowUserToUpdateCFPBFile;
        self::$borrowerName = $borrowerName;
        self::$isHMLO = $isHMLO;
        self::$isMF = $isMF;
        self::$isSLM = $isSLM;
        self::$isHOALien = $isHOALien;
        self::$processorCommentsArray = $processorCommentsArray;
        self::$userTimeZone = $userTimeZone;
        self::$showSysGenNote = $showSysGenNote;
        self::$permissionsToEdit = $permissionsToEdit;
        self::$allowToUpdateFileAdminSection = $allowToUpdateFileAdminSection;
        self::$op = $op;
        self::$addedToFav = $addedToFav;
        self::$isSLMOnly = $isSLMOnly;
        self::$allowPCUserToSubmitCFPB = $allowPCUserToSubmitCFPB;
        self::$isEF = $isEF;
        self::$allowToAccessRAM = $allowToAccessRAM;
        self::$moduleRequested = $moduleRequested;
        self::$servicesRequested = $servicesRequested;
        self::$isLM = $isLM;
        self::$showSSTab = $showSSTab;
        self::$isLO = $isLO;
        self::$isLSR = $isLSR;
        self::$allowToEdit = $allowToEdit;
        self::$publicUser = $publicUser;
        self::$userGroup = $userGroup;
        self::$pcClient_id = $pcClient_id;
        self::$allowPeerstreet = $allowPeerstreet;
        self::$allowToSeeAlowareTab = $allowToSeeAlowareTab;
        self::$thirdPartyServices = $thirdPartyServices;
        self::$glThirdPartyServices = $glThirdPartyServices;
        self::$allowPCUsersToMarketPlace = $allowPCUsersToMarketPlace;
        self::$allowToViewMarketPlace = $allowToViewMarketPlace;
        self::$subscribeToHOME = $subscribeToHOME;
        self::$subscribePCToHOME = $subscribePCToHOME;
        self::$allowServicing = $allowServicing;
        self::$isPCActiveAloware = $isPCActiveAloware;
        self::$viewSubmitOfferTab = $viewSubmitOfferTab;
        self::$pcPriceEngineStatus = $pcPriceEngineStatus;
        self::$userRole = $userRole;
        self::$userNumber = $userNumber;
        self::$viewPublicNotes = $viewPublicNotes;
        self::$viewPrivateNotes = $viewPrivateNotes;
        self::$allowCFPBAuditing = $allowCFPBAuditing;
        self::$userEmail = $userEmail;
        self::$userPriceEngineStatus = $userPriceEngineStatus;
        self::$allowToEditCommission = $allowToEditCommission;
        self::$externalBroker = $externalBroker;
        self::$allowClientToCreateHMLOFile = $allowClientToCreateHMLOFile;
        self::$allowToSeeAllBrokers = $allowToSeeAllBrokers;
        self::$allowToEditMyFile = $allowToEditMyFile;
        self::$userGroup = $userGroup;
        self::$PCID = $PCID;
        self::$externalBroker = $externalBroker;
        self::$op = $op;
        self::$activeTab = $activeTab;
        self::$publicUser = $publicUser;
        self::$userRole = $userRole;
        self::$eOpt = $eOpt;
        self::$userNumber = $userNumber;
    }

    public static function loadMyFileInfo(): void
    {
        if (!self::$LMRId) {
            return;
        }

        self::$moduleRequested = LMRequest::myFileInfo()->branchModuleInfo();

        self::$allowPCToMarketPlace = LMRequest::myFileInfo()->PCInfo()->allowPCToMarketPlace ?? 0;
        self::$allowServicing = LMRequest::myFileInfo()->PCInfo()->allowServicing ?? 0;
        self::$allowPCToSeeAlowareTab = LMRequest::myFileInfo()->PCInfo()->allowToCreateAloware ?? 0;
        self::$allowPeerstreet = LMRequest::myFileInfo()->PCInfo()->allowPeerstreet ?? 0;
        self::$borOrigEmail = LMRequest::myFileInfo()->LMRInfo()->borrowerEmail;
        self::$phoneNumber = LMRequest::myFileInfo()->LMRInfo()->phoneNumber;
        self::$cellNumber = LMRequest::myFileInfo()->LMRInfo()->cellNumber;
        self::$entityName = LMRequest::myFileInfo()->fileHMLOEntityInfo()->entityName;
        self::$billingPaymentInfoArray = LMRequest::myFileInfo()->BillingPaymentInfo();
        self::$billingFeeInfoArray = LMRequest::myFileInfo()->BillingFeeInfo();
        self::$tempLMRClientTypeInfo = LMRequest::myFileInfo()->LMRClientTypeInfo();
        self::$tempServicesRequested = LMRequest::myFileInfo()->branchClientTypeInfo();
        self::$MFLoanTermsInfo = LMRequest::myFileInfo()->MFLoanTermsInfo();
        self::$CFPBInfo = LMRequest::myFileInfo()->CFPBInfo();
        self::$fileModuleInfo = LMRequest::myFileInfo()->fileModuleInfo();
        self::$LMRClientTypeInfo = LMRequest::myFileInfo()->LMRClientTypeInfo();

        self::$addedToFav = LMRequest::myFileInfo()->addedToFav();
        self::$executiveId = LMRequest::myFileInfo()->LMRInfo()->FBRID;
        self::$LMRResponseId = LMRequest::myFileInfo()->ResponseInfo()->LMRResponseId;

        if (sizeof(LMRequest::myFileInfo()->LMRClientTypeInfo())) {
            LMRequest::$loanProgram = LMRequest::myFileInfo()->LMRClientTypeInfo()[0]->ClientType;
        }

        self::$servicesRequested = LMRequest::myFileInfo()->branchClientTypeInfo();
        self::$processorCommentsArray = LMRequest::myFileInfo()->processorComments(self::$viewPrivateNotes, self::$viewPublicNotes);

        self::$borrowerName = LMRequest::myFileInfo()->LMRInfo()->borrowerName . ' ' . LMRequest::myFileInfo()->LMRInfo()->borrowerLName;
        if (self::$userGroup == 'Super'
            || self::$userGroup == 'Auditor'
            || self::$userGroup == 'Agent'
            || self::$userGroup == 'Client'
        ) {
            self::$PCID = LMRequest::myFileInfo()->LMRInfo()->FPCID;
        }
        self::$showSysGenNote = LMRequest::myFileInfo()->PCInfo()->showSysGenNote;

        self::$PCStatusInfo = LMRequest::myFileInfo()->PCStatusInfo(self::$moduleRequested);
        self::$PCSubStatusInfo = LMRequest::myFileInfo()->PCSubStatusInfo(self::$moduleRequested);
        self::$PCClientWFServiceTypeArray = LMRequest::myFileInfo()->PCClientWFServiceType(self::$moduleRequested);
        self::$fileSubstatusInfo = LMRequest::myFileInfo()->fileSubstatusInfo(self::$moduleRequested);

        self::$fileMC = self::$fileLP = [];
        /* Get File Modules Keys */
        foreach (self::$fileModuleInfo as $item) {
            self::$fileMC[] = trim($item->moduleCode);
        }

        foreach (self::$LMRClientTypeInfo as $item) {
            self::$fileLP[] = trim($item->ClientType);
        }

        self::$HMLOPCPropertyType = LMRequest::myFileInfo()->HMLOPCPropertyType(self::$fileLP);
        self::$HMLOPCLoanTerm = LMRequest::myFileInfo()->HMLOPCLoanTerm(self::$fileLP);
        self::$HMLOPCOccupancy = LMRequest::myFileInfo()->HMLOPCOccupancy(self::$fileLP);
        self::$HMLOPCState = LMRequest::myFileInfo()->HMLOPCState(self::$fileLP);
        self::$HMLOPCBasicLoanInfo = LMRequest::myFileInfo()->HMLOPCBasicLoanInfo(self::$fileLP);

        self::$brokerId = 0;
        if (trim(LMRequest::myFileInfo()->BrokerInfo()->email) != trim(self::$PCID) . '@dummyAgentemail.com') {
            self::$brokerId = LMRequest::myFileInfo()->LMRInfo()->brokerNumber;
        }

        self::$typeOfHMLOLoanRequesting = null;
        if (LMRequest::myFileInfo()->fileHMLOPropertyInfo()) {
            self::$typeOfHMLOLoanRequesting = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting;
        }

        self::$ssnNumber = LMRequest::myFileInfo()->LMRInfo()->ssnNumber;
        self::$borrowerDOB = LMRequest::myFileInfo()->LMRInfo()->borrowerDOB;
        self::$occupancy = LMRequest::myFileInfo()->LMRInfo()->occupancy;
        self::$loanNumber = LMRequest::myFileInfo()->LMRInfo()->loanNumber;
        self::$loanNumber2 = LMRequest::myFileInfo()->LMRInfo()->loanNumber2;
        self::$primaryStatusId = LMRequest::myFileInfo()->ResponseInfo()->primeStatusId;
        self::$servicer1 = LMRequest::myFileInfo()->LMRInfo()->servicer1;
        self::$servicer2 = LMRequest::myFileInfo()->LMRInfo()->servicer2;
        self::$closingDate = LMRequest::myFileInfo()->QAInfo()->closingDate;

        self::$salesDate = LMRequest::myFileInfo()->LMRInfo()->salesDate;
        self::$mortgageNotes = LMRequest::myFileInfo()->LMRInfo()->mortgageNotes;
        self::$submitterPCName = LMRequest::myFileInfo()->PCInfo()->processingCompanyName;
        self::$submitterPCPhone = LMRequest::myFileInfo()->PCInfo()->attorneyTelephone;

        self::$propertyAddress = LMRequest::myFileInfo()->getPrimaryProperty()->propertyAddress;
        self::$propertyCity = LMRequest::myFileInfo()->getPrimaryProperty()->propertyCity;
        self::$propertyState = LMRequest::myFileInfo()->getPrimaryProperty()->propertyState;
        self::$propertyZip = LMRequest::myFileInfo()->getPrimaryProperty()->propertyZipCode;

    }

    public static function initFileTabs(): void
    {
        self::$fileTabs = getFileTabsForPCModules::getReport([
            'PCID'       => self::$PCID,
            'userGroup'  => self::$userGroup,
            'moduleCode' => self::$ftModuleCode,
            'isPublic'   => self::$publicUser,
        ]);

        if (!sizeof(self::$fileTabs)) {
            Debug([
                'PCID'       => self::$PCID,
                'userGroup'  => self::$userGroup,
                'moduleCode' => self::$ftModuleCode,
                'isPublic'   => self::$publicUser,
            ]);
        }

        unset(self::$fileTabs['SER']); // deprecated

        if (self::$userGroup == 'Client' && self::$PCID == 3866) {  //3866 is loanguys (ticket is sc-24934) **exception**
            self::$fileTabs = [
                'DOC' => 'Docs',
                'LI'  => 'Full App',
                'CON' => 'Contacts'
            ];
        }
        if (count(self::$fileTabs) > 0) {
            if (self::$propertyNeedRehab != 'Yes' && isset($fileTabs['BD'])) {
                unset($fileTabs['BD']);
            } // https://www.pivotaltracker.com/story/show/161015580
            if (self::$pcClient_id == '' || self::$allowPeerstreet != 1) {
                unset(self::$fileTabs['PE']);
            }
            if (!self::$allowToSeeAlowareTab > 0) {
                unset(self::$fileTabs['AW']);
            }
            if (!((self::$thirdPartyServices > 0 && self::$glThirdPartyServices > 0) || PageVariables::$glThirdPartyServicesLegalDocs > 0)) {
                unset(self::$fileTabs['TPS']);
            }
            if (self::$userGroup == 'Client' && glCustomJobForProcessingCompany::isHideBorrowerPortal(self::$PCID)) {
                unset(self::$fileTabs['HMLI']);
            }
            if (!in_array(self::$PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                unset(self::$fileTabs['DS']);
            }
            if (!glCustomJobForProcessingCompany::showLoanInfoV2(self::$PCID)) {
                unset(self::$fileTabs['LIV2']);
            }
            if (glCustomJobForProcessingCompany::hideTabsForBorrowerPortal(self::$PCID) && self::$userGroup == 'Client') {
                unset(self::$fileTabs['CW']);
                unset(self::$fileTabs['BD']);
                unset(self::$fileTabs['HMLI']);
                unset(self::$fileTabs['CI']);
                unset(self::$fileTabs['PI']);
                unset(self::$fileTabs['CON']);
                unset(self::$fileTabs['DA']);
                unset(self::$fileTabs['EM']);
            }

            if (self::$allowPCToMarketPlace == 0 || self::$allowToViewMarketPlace == 0) {
                unset(self::$fileTabs['MP']);
            }

            if (self::$subscribePCToHOME == 0 || self::$subscribeToHOME == 0) {
                unset(self::$fileTabs['HR']);
            }

            if (self::$allowToSeeAlowareTab == 0 || self::$allowPCToSeeAlowareTab == 0) {
                unset(self::$fileTabs['AW']);
            }

            if (self::$allowPeerstreet == 0) {
                unset(self::$fileTabs['PE']);
            }

            if (self::$allowServicing == 0 || (self::$userGroup != 'Employee' && self::$userGroup != 'Super')) {
                unset(self::$fileTabs['SER2']);
            }

            //permission to view, Submit Offer Tab value assigned in getPageVariables.php
            if (self::$viewSubmitOfferTab != 1) {
                unset(self::$fileTabs['SO']);
            }

            if (self::$pcPriceEngineStatus == 0 || self::$userPriceEngineStatus == 0) {
                unset(self::$fileTabs['PEN']);
            }

            if (self::$userGroup == 'Agent' && self::$allowAgentWorkflowEdit == 0) {
                unset(self::$fileTabs['CW']);
            }

            /* File tab check show or not to show - customization other than DB - START */
            if (self::$publicUser == 1 || self::$userGroup == 'Client') {
                if (in_array(self::$PCID, self::$glShowALTabForClientLogin)) { //Law offices Client portal - Show mortgage info tab and assets tab
                    doNothing();
                } else {
                    if (array_key_exists('AL', self::$fileTabs)) {
                        unset(self::$fileTabs['AL']);
                    }

                    if (self::$isSLMOnly != 1 && (self::$allowPCUserToSubmitCFPB == 1 || self::$allowCFPBAuditing == 1)) {
                        doNothing();
                    } else {
                        if (array_key_exists('CFPB', self::$fileTabs)) {
                            unset(self::$fileTabs['CFPB']);
                        }
                    }
                }
                if (self::$userGroup == 'Client') {

                    // Customization for PC = Vantis Law Firm (Aug 27, 2016)
                    // PC = 886 Vantis Law Firm hide the Admin & CFPB tabs

                    if (in_array(self::$PCID, self::$glNotShowCFPBTabForClientLogin)) {
                        $notShowArray = ['CFPB', 'ADMIN'];
                        for ($n = 0;
                             $n < count($notShowArray);
                             $n++) {
                            if (array_key_exists($notShowArray[$n], self::$fileTabs)) {
                                unset(self::$fileTabs[$notShowArray[$n]]);
                            }
                        }
                    }
                }

                if (self::$publicUser == 1 && self::$PCID == '1399') { // PC = Debt Free, LLC hide the Doc tabs
                    if (array_key_exists('DOC', self::$fileTabs)) {
                        unset(self::$fileTabs['DOC']);
                    }
                }

                /*Add toggle switch for workflows--> Display in borrower portal? Y/N . - PT #: 157958923 - Date June 21, 2018 */
                if (count(self::$PCClientWFServiceTypeArray) == 0) {
                    if (array_key_exists('CW', self::$fileTabs)) {
                        unset(self::$fileTabs['CW']);
                    }
                }
            }

            if (self::$isHMLO == 1 || self::$isEF == 1) { // Added the Tab Name changed for Hard/Private Money LO Module on Date Jan 03, 2017
                if (array_key_exists('CI', self::$fileTabs)) {
                    self::$fileTabs['CI'] = 'Borrower Info';
                }

                if (array_key_exists('IE', self::$fileTabs)) {
                    self::$fileTabs['IE'] = 'Personal Inc & Exp';
                }
            }

            // Allow to view new tab intake
            // AWATA, Dave PC, Enrollment Advisory (Client Document)

            if ((!in_array(self::$PCID, self::$accessIntakeFileTabPC) || self::$isSLMOnly == 1) && (!in_array(self::$PCID, self::$accessNewIntakeFileTabPC) || self::$isSLMOnly == 1)) {
                if (array_key_exists('INT', self::$fileTabs)) {
                    unset(self::$fileTabs['INT']);
                }
            }
            if (self::$isSLMOnly != 1
                && (self::$allowPCUserToSubmitCFPB == 1 || self::$allowCFPBAuditing == 1)
                && self::$CFPBInfo) {
                doNothing();
            } else {
                if (array_key_exists('CFPB', self::$fileTabs)) {
                    unset(self::$fileTabs['CFPB']);
                }
            }

            if (self::$PCID == '656') {  //Pac1lending.com
                if (array_key_exists('HR', self::$fileTabs)) {
                    unset(self::$fileTabs['HR']);
                }
            }

            if (self::$userRole == 'Client' && self::$isHMLO == 1) {
                self::$userSeeBilling = 1;
            } // PT : 152699388

            if (self::$userSeeBilling == 1 && self::$userRole != 'Auditor' && self::$userRole != 'CFPB Auditor' && self::$userRole != 'Auditor Manager') {
                doNothing();
            } else {
                if (array_key_exists('BC', self::$fileTabs)) {
                    unset(self::$fileTabs['BC']);
                }
            }

            if (self::$allowToAccessRAM != 1) {
                if (array_key_exists('RAM', self::$fileTabs)) {
                    unset(self::$fileTabs['RAM']);
                }
            }

            $tabCheckArray = ['MP', 'HR', 'SER2', 'AW', 'PE', 'SO', 'PEN'];
            foreach (self::$fileTabs as $fileTabsKey => $fileTab) {
                if (self::$activeTab == '') {
                    if (in_array($fileTabsKey, $tabCheckArray)) {
                        switch ($fileTabsKey) {
                            case 'MP':
                                if (self::$allowPCUsersToMarketPlace == 1 && self::$allowToViewMarketPlace == 1) {
                                    self::$activeTab = $fileTabsKey;
                                }
                                break;
                            case 'HR':
                                if (self::$subscribePCToHOME == 1 && self::$subscribeToHOME == 1) {
                                    self::$activeTab = $fileTabsKey;
                                }
                                break;
                            case 'SER2':
                                if (self::$allowServicing == 1) {
                                    self::$activeTab = $fileTabsKey;
                                }
                                break;
                            case 'AW':
                                if (self::$allowToSeeAlowareTab == 1 && self::$isPCActiveAloware == 1) {
                                    self::$activeTab = $fileTabsKey;
                                }
                                break;
                            case 'PE':
                                if (self::$allowPeerstreet == 1) {
                                    self::$activeTab = $fileTabsKey;
                                }
                                break;
                            case 'SO':
                                if (self::$viewSubmitOfferTab == 1) {
                                    self::$activeTab = $fileTabsKey;
                                }
                                break;
                            case 'PEN':
                                if (self::$pcPriceEngineStatus == 1 && self::$userPriceEngineStatus == 1) {
                                    self::$activeTab = $fileTabsKey;
                                }
                                break;
                        }
                    } else {
                        self::$activeTab = $fileTabsKey;
                        break;
                    }
                } else {
                    break;
                }
            }
            // https://www.pivotaltracker.com/story/show/160146292
        }
        if ((!isset($_REQUEST['tabOpt'])) && self::$publicUser != 1) {
            $_REQUEST['tabOpt'] = self::$activeTab;
        }
        if (trim(self::$userGroup) == 'Auditor') {
            self::$activeTab = 'LA';
        }                /* Auditor User active tab */

        if (self::$userGroup == 'Client' && (self::$isLM == 1 || self::$showSSTab == 1 || self::$isMF == 1 || self::$isLO == 1 || self::$isLSR == 1 || self::$isSLMOnly == 1)) {
            if (array_key_exists('CON', self::$fileTabs)) {
                unset(self::$fileTabs['CON']);
            }
            if (array_key_exists('HUD', self::$fileTabs)) {
                unset(self::$fileTabs['HUD']);
            }
            if (array_key_exists('ADMIN', self::$fileTabs)) {
                unset(self::$fileTabs['ADMIN']);
            }
        }

        if (!glCustomJobForProcessingCompany::showLoanTermsToBusinessFunding(self::$PCID) && self::$ftModuleCode == 'loc') { //2057 customization (ticket is sc-30749) **exception**
            unset(self::$fileTabs['LT']);
        }

        if (!in_array(self::$PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
            unset(self::$fileTabs['DS']);
        }
        if (!count(BaseHTML::sectionAccess2(['sId' => 'EF', 'opt' => 'BO']))) {
            if (array_key_exists('EF', self::$fileTabs)) {
                unset(self::$fileTabs['EF']);
            }
        }

        $tabsForNewFile = [];
        if (!self::$LMRId) {

            $tabsForNewFile[self::$activeTab] = self::$fileTabs[self::$activeTab];
            self::$fileTabs = array_intersect($tabsForNewFile, self::$fileTabs);
        }

        if (!sizeof(self::$fileTabs)) {
            Debug(self::$fileTabs, self::$fileTabs, self::$PCID);
        }
    }

    public static function loadRequestVars(): void
    {
        if (isset($_REQUEST['eOpt'])) {
            self::$eOpt = trim($_REQUEST['eOpt']);
        }

        if (isset($_REQUEST['tabOpt'])) {
            self::$activeTab = trim($_REQUEST['tabOpt']);
        }

        if (isset($_REQUEST['lId'])) {
            self::$LMRId = intval(cypher::myDecryption(trim($_REQUEST['lId'])));
        }

        if (isset($_REQUEST['cliType'])) {
            self::$cliType = trim($_REQUEST['cliType']);
        }

        if (isset($_REQUEST['moduleCode'])) {
            self::$moduleCode = trim($_REQUEST['moduleCode']);
        }

        if (isset($_REQUEST['op'])) {
            self::$op = trim(cypher::myDecryption(trim($_REQUEST['op'])));
        }

        if (isset($_POST['publicUser'])) {
            self::$publicUser = Request::GetClean('publicUser');
        }
        if (isset($_REQUEST['cemail'])) {
            self::$cemail = cypher::myDecryption(Request::GetClean('cemail'));
        }


        if (self::$publicUser != 1) {
            self::$executiveId = 0;
            if (isset($_REQUEST['eId'])) {
                self::$executiveId = intval(cypher::myDecryption(trim($_REQUEST['eId'])));
            }
        }
    }

    public static function preInit(): void
    {
        self::loadGlobals();
        self::loadRequestVars();

        LMRequest::$moduleCode = self::$moduleCode;
        LMRequest::setLMRId(self::$LMRId);

        if(!self::$LMRId) {
            return;
        }

        self::loadMyFileInfo();


        if (self::$LMRId > 0) {
//            insertLastView::getReport(self::$LMRId);  // Latest opened -- handled by fileCommon, for MVC put in init

            $PCID = LMRequest::File()->FPCID;

            if (self::$userGroup == 'Super') {
                $exInArray['TABLENAME'] = 'tblProcessingCompany';
                $exInArray['FIELDNAMEARRAY'] = ['client_id'];
                $exInArray['CONDITION'] = ' where activeStatus = 1 and dstatus = 0 and PCID = ' . $PCID;

                $exResultArray = Database2::getInstance()->getSelectedFieldsForFile($exInArray);
                self::$pcClient_id = trim($exResultArray['client_id']);
            }

            self::$propertyNeedRehab = LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->propertyNeedRehab;

            self::$moduleFileTabInfo = getFileModules::getObject(self::$LMRId);

            $apStr = [];
            foreach (self::$moduleFileTabInfo as $moduleInfo) {
                $apStr[] = $moduleInfo->moduleCode;
            }
            self::$ftModuleCode = implode(',', $apStr);

        } else {
            self::$ftModuleCode = self::$moduleCode;
            /* https://www.pivotaltracker.com/story/show/********* */
            if (trim(self::$cliType) == 'SLM') {
                self::$activeTab = 'SLM'; // Student Loan Mod File
            } elseif (!($_REQUEST['tabOpt'] ?? null)) {
                self::$activeTab = 'CI';
            }
        }


        /** Object file we need to include end **/

        if (self::$userRole == 'Branch') {
            self::$executiveId = trim(self::$userNumber);
        }

        if (self::$eOpt == 0) {
            self::$op = 'create';
        }

        $inArray['LMRId'] = self::$LMRId;
        $inArray['userNo'] = self::$userNumber;
        $inArray['userRole'] = self::$userGroup;

        if (self::$LMRId == 0) {
            $inArray['PN'] = 'createfiles';
            if (self::$publicUser != 1) {
                UserAccess::checkPageAccess($inArray);
            }
        }

        if (self::$publicUser != 1) {

            $ipArray['LMRId'] = self::$LMRId;
            $ipArray['userNo'] = self::$userNumber;
            $ipArray['userRole'] = self::$userRole;
            $ipArray['userGroup'] = self::$userGroup;
            $ipArray['allowCFPBAuditing'] = self::$allowCFPBAuditing;
            if (self::$userGroup == 'Client') {
                $CPwd = '';
                if (isset($_SESSION['userPwd'])) {
                    $CPwd = trim($_SESSION['userPwd']);
                }
                $ipArray['clientEmail'] = self::$userEmail;
                $ipArray['clientPwd'] = $CPwd;
            }

            if (!checkFileAccess::getReport($ipArray)) {
                HTTP::Redirect('/invalidFileAccess.php');
                exit;
            }
        }
        if (trim(self::$cliType) && self::$LMRId == 0) {
            self::$LMRClientTypeInfo[0]['ClientType'] = self::$cliType;
        }
        if (trim(self::$moduleCode) && self::$LMRId == 0) {
            self::$fileModuleInfo[0]['moduleCode'] = self::$moduleCode;
        } /* Default Module Select */

        $inArray['LMRId'] = self::$LMRId;

        if (self::$activeTab == 'BC' && self::$allowToEditCommission == 0) {
            if (self::$userGroup == 'Employee') {
                $ip['commissionUserId'] = self::$userNumber;
                $ip['commissionUserType'] = 'Attorney/Employee';
            } else if (self::$userGroup == 'Branch') {
                $ip['commissionUserId'] = self::$userNumber;
                $ip['commissionUserType'] = 'Branch/AE';
            } else if (self::$userGroup == 'Agent') {
                $ip['commissionUserId'] = self::$userNumber;
                $ip['commissionUserType'] = 'Agent';
            }
        }

        if (self::$activeTab == 'CI'
            || self::$activeTab == 'LI'
            || self::$activeTab == 'SSS'
            || self::$activeTab == 'BC'
            || self::$activeTab == 'SLM'
            || self::$activeTab == 'INT'
            || self::$activeTab == 'QAPP'
        ) {


            $ip = ['PCID' => self::$PCID];
            if (self::$userRole == 'Client' && self::$allowClientToCreateHMLOFile == 1) { /* Allow client to create HMLO file - Mar 30, 2017 */
                $tempBRID = $tempAGID = 0;

                $clientFileListArray = getClientFileBranchAndAgent::getReport(['PCID' => self::$PCID, 'CID' => self::$userNumber]);

                if (count($clientFileListArray) > 0) {
                    $tempAGID = $clientFileListArray['AGID'];
                    $tempBRID = $clientFileListArray['BRID'];
                }

                $ip['agentId'] = $tempAGID;
                $ip['execID'] = $tempBRID;
            }


            if (self::$userRole == 'Agent') {
                $ip['brokerNumber'] = self::$userNumber;
                self::$branchList = getBranchesForAgent::getReport($ip);
                /** Fetch Branches assigned to Agents **/
            } else {
                if (self::$userGroup == 'Employee') {
                    $AssignedBranches = getEmployeeAssignedBranches::getReport(['EID' => self::$userNumber]);
                    if (array_key_exists(self::$userNumber, $AssignedBranches)) {
                        self::$AssignedBranchesInfo = $AssignedBranches[self::$userNumber];
                    }
                    foreach (self::$AssignedBranchesInfo as $i => $info) {
                        if ($i > 0) {
                            self::$assignedBranchesID .= ', ';
                        }
                        self::$assignedBranchesID .= trim($info['LMRAEID']);
                    }
                    if (self::$assignedBranchesID) {
                        if (in_array(self::$userNumber, self::$glAllowEmpToUpdateBranchsForFiles) && self::$userGroup == 'Employee') {
                            doNothing();
                        } else {
                            $ip['execID'] = self::$assignedBranchesID;
                        }
                    }
                }
                $ip['moduleCode'] = trim(self::$moduleCode);
                $tempBranchList = getBranches::getReport($ip);
                /** Fetch Branches assigned to PC **/
                if (count($tempBranchList) > 0) {
                    if (array_key_exists('branchList', $tempBranchList)) {
                        self::$branchList = $tempBranchList['branchList'];
                    }
                }
            }
            if (self::$userRole == 'Branch') {
                $ip['executiveId'] = self::$userNumber;
                $ip['opt'] = 'list';
            } else if (self::$userRole == 'Agent' && self::$externalBroker == 1) {
                $ip['opt'] = 'list';
                if (self::$PCID > 0) {
                    $branchListForLoanOfficer = Arrays::buildKeyByValue(self::$branchList, 'executiveId');
                    if (self::$LMRId > 0) {
                        $ip['branchIds'] = self::$executiveId;
                    } else {
                        $ip['branchIds'] = implode(',', array_keys($branchListForLoanOfficer));
                    }
                    $ip['externalBroker'] = 0;
                    $ip['loanOfficerId'] = self::$userNumber;

                    if (self::$LMRId && self::$brokerId) {
                        $ip['fileBrokerId'] = self::$brokerId;
                    }
                    $ip['allowToSeeAllBrokers'] = self::$allowToSeeAllBrokers;
                    if (self::$userGroup == 'Agent') {
                        /** For Agents assigned to PC's Branches **/
                        $ip['userGroup'] = self::$userGroup;
                    }
                    $ip['PCID'] = self::$PCID;
                }
            } else {
                $ip['opt'] = 'list';
                if (self::$PCID > 0) {
                    if (self::$userGroup == 'Employee') {
                        $ip['executiveId'] = self::$executiveId;
                        /** For Agents assigned to PC's Branches **/
                        $ip['userGroup'] = self::$userGroup;
                    }
                }
            }
        }

        if (self::$LMRId == 0 && (self::$activeTab == 'CI' || self::$activeTab == 'DOC' || self::$activeTab == 'SLM' || self::$activeTab == 'LI' || self::$activeTab == 'QAPP')) {

            $ip = [
                'PCID'      => self::$PCID,
                'opt1'      => 'list',
                'keyNeeded' => 'n',
                'userGroup' => PageVariables::$userGroup
            ];
            self::$PCStatusInfo = getPCPrimaryStatus::getReport($ip);
            /** Fetch PC primary status **/
            if (array_key_exists('primaryStatusInfo', self::$PCStatusInfo)) {
                self::$PCStatusInfo = self::$PCStatusInfo['primaryStatusInfo'];
            }

            if (self::$userGroup == 'Branch' && self::$userNumber > 0) {
                $ip = ['branchID' => self::$userNumber, 'moduleCode' => self::$moduleCode];
                $ip['keyNeeded'] = 'n';
                $servicesRequested = getBranchServices::getReport($ip);
                /** Fetch Branch services requested **/
                if (array_key_exists(self::$userNumber, $servicesRequested)) {
                    self::$servicesRequested = $servicesRequested[self::$userNumber];
                }

                $moduleRequested = getBranchModules::getReport($ip);
                /** Fetch Branch Modules requested **/
                if (array_key_exists(self::$executiveId, $moduleRequested)) {
                    self::$moduleRequested = $moduleRequested[self::$executiveId];
                }
            } else if (self::$publicUser == 1) {
                $ip = ['branchID' => self::$executiveId, 'moduleCode' => self::$moduleCode];
                $ip['keyNeeded'] = 'n';
                $servicesRequested = getBranchServices::getReport($ip);
                /** Fetch Branch services requested for iframe users **/
                if (array_key_exists(self::$executiveId, $servicesRequested)) {
                    self::$servicesRequested = $servicesRequested[self::$executiveId];
                }
                $moduleRequested = getBranchModules::getReport($ip);
                /** Fetch Branch Modules requested **/
                if (array_key_exists(self::$executiveId, $moduleRequested)) {
                    self::$moduleRequested = $moduleRequested[self::$executiveId];
                }
            } else {
                $ip['keyNeeded'] = 'n';
                $ip['moduleCode'] = self::$moduleCode;
                if (self::$PCID > 0) {
                    self::$servicesRequested = getPCServiceType::getReport($ip);
                    /** Fetch PC services requested **/

                    $ip['keyNeeded'] = 'n';
                    self::$moduleRequested = getPCModules::getReport($ip);
                    /** Fetch PC Modules requested **/
                }

            }
        }


        /** Student loan mod file - Change the tab from CI to SLM (open) if user clicks from the pipeline **/

        foreach (self::$fileModuleInfo as $item) {
            if ($item->moduleCode == 'SS') {
                self::$showSSTab = 1;
            }
            if ($item->moduleCode == 'SLM') {
                self::$isSLM = 1;
            }
            if ($item->moduleCode == 'LM') {
                self::$isLM = 1;
            }
        }

        if (self::$isSLM == 1 && (count(self::$fileModuleInfo) == 1)) {
            /** Show ONLY Student loan mod tabs and its respective tabs **/
            self::$isSLMOnly = 1;
        }

        foreach (self::$fileModuleInfo as $item) {
            if (trim($item->moduleCode) == 'HOA') {
                /** HOA Lien Tab **/
                self::$isHOALien = 1;
                break;
            }

            if (trim($item->moduleCode) == 'LO') {
                /** LO Tab **/
                self::$isLO = 1;
                break;
            }

            if (trim($item->moduleCode) == 'LSR') {
                /** Loan Servicing Section For Doyen Asset Management PCs **/
                self::$isLSR = 1;
            }

            if (trim($item->moduleCode) == 'MF') {
                /** Merchant Funding Section **/
                self::$isMF = 1;
            }

            if (trim($item->moduleCode) == 'HMLO') {
                /** Hard / Private Money LOS Section on                                                                    Nov 17, 2016 **/
                self::$isHMLO = 1;
            }

            if (trim($item->moduleCode) == 'EF') {   /* Equipment Financing - EF Module - Selected in the file (PT-#155682423) - April 18, 2018 */
                self::$isEF = 1;
            }

            if (trim($item->moduleCode) == 'loc') {   /* Line Of Credit. - LOC Module - Selected in the file (PT-#159946019) - August 27, 2018 */
                self::$isLOC = 1;
            }

        }

        if (self::$isLOC == 1) self::$isHMLO = 1;

        /**  Fetch all File tabs for PC modules or File selected Modules **/

        /* Check Permission to edit files >> PC, self profile */


        self::$permissionsToEdit = [];
        self::$allowUserToUpdatePCStatus = 0;
        if (self::$allowToEditMyFile == 2 || self::$userGroup == 'Client') {
            $permissionsToEdit = getPermissionsToEdit::getReport([
                'PCID'           => self::$PCID,
                'userGroup'      => self::$userGroup,
                'externalBroker' => self::$externalBroker,
                'op'             => self::$op
            ]);
            /*** Allowed PC status ***/
            if (array_key_exists(LMRequest::myFileInfo()->ResponseInfo()->primeStatusId, $permissionsToEdit)) {
                self::$allowUserToUpdatePCStatus = 1;
            }


            /*
             * Quality Fair Consulting Inc Client Portal -
             * Allow all the clients to upload docs under status irrespective of their status for the PC = Quality Fair Consulting Inc - 13-11-2015
             */
            if ((self::$PCID == 2 || self::$PCID == 1513) && self::$userGroup == 'Client' && self::$activeTab == 'DOC') {
                self::$allowUserToUpdatePCStatus = 1;
            }
        }

        /**
         * Allow Loan version Email Send to Client. pivotal #154715300
         * Allow Loan version Email Send to Agent. pivotal #154715300
         */
        $permissionsToEditClient = [];
        if (self::$LMRId > 0 && self::$isHMLO == 1) {
            $permissionsToEditClient = getPermissionsToEdit::getReport(['PCID' => self::$PCID, 'userGroup' => 'Client', 'op' => self::$op]);
        }
        /**
         *
         * Description    : Not allow to edit the file in client login
         * Date        : March 27, 2017
         * Author        : Venkatesh
         * Allow Client to edit Property info tab on Dec 6, 2017
         * Tiket ID: 153422124
         **/

        $HMLOClientViewTab = [
            'AL',
            // 'SER', // deprecated
            'LI',
            'QAPP',
            'HUD',
            'ADMIN',
            'CON',
            'DA'
        ];
        if (LMRequest::myFileInfo() && array_key_exists(LMRequest::myFileInfo()->ResponseInfo()->primeStatusId, $permissionsToEditClient)) {
            unset($HMLOClientViewTab[2]);
            unset($HMLOClientViewTab[3]);
        }
        if (self::$op == 'view') {
            Strings::SetSess('msg', 'You are in view-only mode.');
        }
        if (self::$publicUser == 1) {
            self::$allowToEdit = true;
        } elseif (self::$userRole == 'Client' && self::$activeTab == 'CON') {
            self::$allowToEdit = false;
        } elseif (self::$userRole == 'Client' && (in_array(self::$activeTab, $HMLOClientViewTab)) && self::$LMRId > 0) { // Not editable for those tabs for Client Portal login on March 27, 2017 By Venkatesh
            self::$allowToEdit = false;
        } elseif (((self::$eOpt == 0 || self::$allowToEditMyFile == 1 || ((self::$allowToEditMyFile == 2 || self::$userGroup == 'Client') && self::$allowUserToUpdatePCStatus == 1)) && self::$op != 'view')) {
            self::$allowToEdit = true;
        } elseif (self::$userRole == 'Client' && self::$activeTab == 'QAPP' && self::$LMRId == 0) {
            self::$allowToEdit = false;
        } else {
            self::$allowToEdit = false;
        }


        if (self::$publicUser != 1 && self::$LMRId > 0) { // While edit or view files //

            if (self::$allowToEdit) {
                $LIP = ['LMRId' => self::$LMRId];

                self::$lockInfo = isFileLockedLastVal::getReport($LIP);

                if (count(self::$lockInfo) > 0) {
                    if (self::$lockInfo['locked']) {
                        if (!(trim(self::$lockInfo['lockedTime'])) || (trim(self::$lockInfo['lockedTime']) == '0000-00-00')) {
                            self::$lockInfo['lockedTime'] = '';
                        } else {
                            self::$lockInfo['lockedTime'] = Dates::formatDateWithRE(self::$lockInfo['lockedTime'], 'YMD', 'm/d/Y');
                        }
                    }
                }
            }

            if (!self::$allowToEdit) {
                self::$allowToUpdateFileAdminSection = 0;
            }        // If Allow to edit file is OFF. Then, OFF Admin section //
        }

        if (self::$userRole == 'Client' && self::$activeTab == 'CFPB') { /* As per Daniel advice, Please allow the CFPB tab to be editable EVEN IF primary file status rules
                                                          * say the client cannot edit. All other tabs of the client file can follow primary file status rules.
                                                          * Sep 1, 2015
                                                          */
            self::$allowToEdit = true;
        }


        self::$allowUserToUpdateCFPBFile = getPermissionsToEditCFPBFile::getReport(['LMRId' => self::$LMRId]);
        /*** Allowed PC status ***/

        if (self::$publicUser != 1 && self::$LMRId > 0 && self::$activeTab == 'CFPB') {
            if (self::$allowToEdit && self::$op == 'edit') {
                $LIP = [
                    'allowToEditFile' => self::$allowToEdit,
                    'LMRId'           => self::$LMRId,
                    'UID'             => self::$userNumber,
                    'URole'           => self::$userRole,
                ];

                $CFPBLockInfo = isCFPBAllowedToLock::getReport($LIP);

                if (count($CFPBLockInfo) > 0) {
                    if ($CFPBLockInfo['allow']) {
                        self::$allowToEdit = true;
                    } else {
                        self::$allowToEdit = false;
                    }
                }
            }

        }


        /* PC loan terms rules - (Pivotal # : 153960785) */
        if (self::$isHMLO || self::$isLOC) {

            glHMLOCreditScoreRange::$glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange(self::$PCID);


            $PCSelectedLoanTerm = [];
            foreach (self::$HMLOPCLoanTerm as $item) {
                $PCSelectedLoanTerm[] = $item->loanTerm;
            }

            if (count($PCSelectedLoanTerm) > 0) {
                glHMLOLoanTerms::$glHMLOLoanTerms = $PCSelectedLoanTerm;
            }

            $PCSelectedOccupancy = [];
            foreach (self::$HMLOPCOccupancy as $item) {
                $PCSelectedOccupancy[] = $item->occupancy;
            }

            if (count($PCSelectedOccupancy) > 0) {
                glHMLOHouseType::$glHMLOHouseType = $PCSelectedOccupancy;
            }


            $PCBorrCreditScoreRange = self::$HMLOPCBasicLoanInfo->PCBorrCreditScoreRange;


            if ($PCBorrCreditScoreRange) {
                glHMLOCreditScoreRange::$glHMLOCreditScoreRange = explode(',', $PCBorrCreditScoreRange);
            }


            /**
             * Get Custom Loan Guidelines From PC..
             */
            $propertyTypeKeyArray = [];
            foreach (self::$HMLOPCPropertyType as $item) {
                $propertyTypeKeyArray[] = $item->propertyType;
            }

            if (sizeof($propertyTypeKeyArray)) {
                LMRequest::$propertyTypeKeys = $propertyTypeKeyArray;
            }

            $stateKeyArray = [];
            foreach (self::$HMLOPCState as $item) {
                $stateKeyArray[] = $item->stateCode;
            }

            if (sizeof($stateKeyArray)) {
                LMRequest::$customLoanGuidelinesStateKeys = $stateKeyArray;
            }
        }

        self::$clsColor = '';


        LMRequest::$activeTab = self::$activeTab;
        self::$fileCT = self::$fileCLP = '';

        if (self::$publicUser == 1) {
            $myUrl = CONST_SITE_URL . 'loanModificationPrequalRemote.php?rsc=' . self::$branchReferralCode .
                '&aRc=' . cypher::myEncryption(self::$agentReferralCode) .
                '&lId=' . cypher::myEncryption(self::$LMRId) .
                '&fOpt=' . cypher::myEncryption(self::$fOpt) .
                '&tabOpt=' . self::$activeTab;
        } else {
            $myUrl = 'LMRequest.php?eId=' . cypher::myEncryption(self::$executiveId) . '&amp;lId=' . cypher::myEncryption(self::$LMRId) . '&amp;rId=' . cypher::myEncryption(self::$LMRResponseId) . '&amp;tabOpt=' . self::$activeTab;
        }
        Strings::SetSess('gotoUrl', $myUrl);

        if (self::$activeTab == 'QAPP') {
            $fileTab = 'QA';
        } else if (self::$activeTab == 'LI') {
            $fileTab = 'FA';
        } else {
            $fileTab = 'BO';
        }
        if (self::$activeTab == 'QAPP' || self::$activeTab == 'LI' || self::$activeTab == 'CI') {
            $fileTypeArray = self::$moduleRequested;

        } else {
            $fileTypeArray = self::$fileModuleInfo;
        }
        /* Get Branch Modules Keys */
        $aCm = '';
        foreach ($fileTypeArray as $item) {
            self::$fileCT .= $aCm . trim($item->moduleCode);
            $aCm = ',';
        }


        $lpmsg = '';

        $sql = '
                        SELECT 
                            * 
                        FROM 
                            tblLMRClientType_h 
                        WHERE recordDate = (
                                SELECT  MAX(recordDate) 
                                FROM tblLMRClientType_h 
                                WHERE lmrid =  :LMRId
                        )';
        $lphresultArray = Database2::getInstance()->queryData($sql, [
            'LMRId' => self::$LMRId,
        ]);

        if (self::$LMRId > 0 && (!self::$fileLP || sizeof(self::$fileLP) == 0)) {
            if (count($lphresultArray) <= 0) {
                self::$fileLP[0] = 'TBD';
                $lpmsg = 'Loan Program of file has been deleted so by default is assigned to "TBD" 1';
            } else {
                self::$LMRClientTypeInfo = $lphresultArray;
                foreach (self::$LMRClientTypeInfo as $item) {
                    self::$fileLP[] = trim($item['ClientType']);
                }
                $lpmsg = 'Loan Program of file has been deleted so assigned to last known Loan Program ';
            }
            alertLoanProgramNull::getReport([
                'msg'         => $lpmsg,
                'LMRID'       => self::$LMRId,
                'loanProgram' => self::$fileLP[0]
            ]);
        } elseif ((self::$fileLP[0] ?? '') == '') {
            if (count($lphresultArray) <= 0) {
                self::$fileLP[0] = 'TBD';
                $lpmsg = 'Loan Program of file has been deleted so by default is assigned to "TBD" 2';
            } else {
                $LMRClientTypeInfo = $lphresultArray;
                foreach ($LMRClientTypeInfo as $item) {
                    self::$fileLP[] = trim($item['ClientType']);
                    $lpmsg = 'Loan Program of file has been deleted so assigned to last known Loan Program ';
                }
            }
            alertLoanProgramNull::getReport([
                'msg'         => $lpmsg,
                'LMRID'       => self::$LMRId,
                'loanProgram' => self::$fileLP[0]
            ]);
        }

        /*get branch level loan program*/
        $aCm = '';
        foreach (self::$servicesRequested as $item) {
            self::$fileCLP .= $aCm . trim($item->LMRClientType);
            $aCm = ',';
        }


        self::$fieldsInfo = getAppFormFields::getReport([
            'assignedPCID' => self::$PCID,
            'fTArray'      => $fileTypeArray,
            'myOpt'        => $fileTab,
            'activeTab'    => self::$activeTab,
        ]);

        loanForm::init(
            self::$PCID,
            $fileTab,
            cypher::myEncryption(self::$LMRId),
            (int)self::$LMRId,
            self::$fileMC,
            getFileInfo::$fileLoanPrograms,
            getFileInfo::$fileInternalLoanPrograms,
            getFileInfo::$fileAdditionalLoanPrograms
        );


        self::BorrowerInfo();
        self::initFileTabs();
    }

    public static function Init(): void
    {
        self::preInit();
    }

    public static function BorrowerInfo(): void
    {
        if (!LMRequest::myFileInfo()) {
            return;
        }

        self::$PCStatusInfo = [];
        $totals = [];

        self::$fileModuleCodeForWebForm = self::$fileModuleInfo[0]->moduleCode;


        if (Dates::IsEmpty(self::$salesDate)) {
            self::$salesDate = '';
        } else {
            self::$salesDate = Dates::formatDateWithRE(self::$salesDate, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty(self::$closingDate)) {
            self::$closingDate = '';
        } else {
            self::$closingDate = Dates::formatDateWithRE(self::$closingDate, 'YMD', 'm/d/Y');
        }
        if (trim(self::$ssnNumber)) self::$tempSSNNumber = Strings::formatSSNNumber(self::$ssnNumber);

        if (Dates::IsEmpty(self::$borrowerDOB)) {
            self::$borrowerDOB = '';
        } else {
            self::$borrowerDOB = Dates::formatDateWithRE(self::$borrowerDOB, 'YMD', 'm/d/Y');
        }

        for ($j = 0; $j < count(self::$PCStatusInfo); $j++) {
            if (trim(self::$PCStatusInfo[$j]['PSID']) == self::$primaryStatusId) {
                self::$primaryStatus = Strings::escapeQuoteNew(trim(self::$PCStatusInfo[$j]['primaryStatus']));
            }
        }


        $appStr = '';

        if (self::$userRole == 'CFPB Auditor' || self::$userRole == 'Auditor Manager' || (self::$allowCFPBAuditing == 1 && self::$allowUserToUpdateCFPBFile == 1)) {
            $CFPBSubmitterInfo = $tempCFPBSubmitterInfo = [];
            $CFPBSubmitterUID = 0;

            if (count(self::$CFPBInfo) > 0) {
                $tempCFPBSubmitterInfo = getUserInfo::getReport([
                    'UID'   => LMRequest::myFileInfo()->CFPBInfo()->UID,
                    'UType' => LMRequest::myFileInfo()->CFPBInfo()->UType
                ]);
            }
            if (count($tempCFPBSubmitterInfo) > 0) {
                if (array_key_exists($CFPBSubmitterUID, $tempCFPBSubmitterInfo)) $CFPBSubmitterInfo = $tempCFPBSubmitterInfo[$CFPBSubmitterUID];
            }
            if (count($CFPBSubmitterInfo) > 0) {
                self::$submitterEmail = trim($CFPBSubmitterInfo['email']);
                self::$submitterPhone = Strings::formatPhoneNumber(trim($CFPBSubmitterInfo['tollFree']));
                self::$submitterCell = Strings::formatPhoneNumber(trim($CFPBSubmitterInfo['cellNumber']));
                self::$submittedBy = trim($CFPBSubmitterInfo['employeeNames']);
            }
        }
        /* Customization for PC = The J. Freeman Firm March 2 , 2016
        1492 = The J. Freeman Firm  , 820 = Dave PC , 2 = AWATA  PC
        */

        $billingPaymentKeyArray = [];
        $billingFeeKeyArray = [];
        if (self::$billingPaymentInfoArray) {
            $billingPaymentKeyArray = array_keys(self::$billingPaymentInfoArray);
        }
        $totalPaidAmount = 0;
        $totalOwedAmt = 0;

        /* no of Phases shown in Billing section START */

        $noOfCols = CONST_BILLING_PHASE;
        /* no of Phases shown in Billing section END */

        if (self::$billingFeeInfoArray) {
            $billingFeeKeyArray = array_keys(self::$billingFeeInfoArray);
        }
        foreach (self::$billingFeeInfoArray as $tempArray) {
            foreach ($tempArray as $item) {
                $noOfCols = $item->maxPhase;
                break 2;
            }
        }
        if ((int)($noOfCols) == 0 || (int)($noOfCols) <= CONST_BILLING_PHASE) {
            $noOfCols = CONST_BILLING_PHASE;
        }
        for ($i = 1; $i <= $noOfCols; $i++) {
            $totals['totalOwedAmt' . $i] = 0;
            $totals['totalPaid' . $i] = 0;
            $totals['billingDateOwed' . $i] = 0;
            $totals['totalAmt' . $i] = 0;
        }

        for ($bf = 0; $bf < count($billingFeeKeyArray); $bf++) {
            $billingKey = trim($billingFeeKeyArray[$bf]);
            if (array_key_exists($billingKey, self::$billingFeeInfoArray)) {
                $tempArray = self::$billingFeeInfoArray[$billingKey];
                foreach ($tempArray as $temp) {
                    $feeCode = trim($temp->feeCode);
                    $phase = trim($temp->phase);
                    $feeAmount = trim($temp->feeAmount);

                    $totals['totalAmt' . $phase] = floatval($totals['totalAmt' . $phase]);

                    $totals[$feeCode . '_phase_' . $phase] = Strings::replaceCommaValues($feeAmount);
                    $totals[$feeCode . '_phase_' . $phase] = Strings::Currency($totals[$feeCode . '_phase_' . $phase]);
                    $totals['totalAmt' . $phase] += Strings::replaceCommaValues($totals[$feeCode . '_phase_' . $phase]);
                }
            }
        }
        for ($bp = 0; $bp < count($billingPaymentKeyArray); $bp++) {
            $phaseKey = $billingPaymentKeyArray[$bp];

            $tempBillingArray = self::$billingPaymentInfoArray[$phaseKey];
            $totals['billingDateOwed' . $phaseKey] = trim($tempBillingArray->dateOwed);
            $totals['billingDateOwed' . $phaseKey] = Dates::formatDateWithRE($totals['billingDateOwed' . $phaseKey], 'YMD', 'm/d/Y');
            $totals['totalPaid' . $phaseKey] = trim($tempBillingArray->totalPaid);

            $totalPaidAmount += Strings::replaceCommaValues($totals['totalPaid' . $phaseKey]);

        }

        $tempColorY = 0;
        $curDate = date('Y/m/d');
        $curDate = Dates::formatDateWithRE($curDate, 'YMD', 'm/d/Y');

        for ($i = 1; $i <= $noOfCols; $i++) {
            $totals['totalOwedAmt' . $i] = Strings::replaceCommaValues($totals['totalAmt' . $i]);

            $billingDateOwed = $totals['billingDateOwed' . $i];

            $totalBalAmtOwed = Strings::replaceCommaValues($totals['totalOwedAmt' . $i]) - Strings::replaceCommaValues($totals['totalPaid' . $i]);
            if ($billingDateOwed && strtotime($billingDateOwed) < strtotime($curDate) && $totalBalAmtOwed > 0) {
                $tempColorY = 1;
            }
            $totalOwedAmt += $totals['totalOwedAmt' . $i];
        }

        self::$totalBalanceOwed = $totalOwedAmt - $totalPaidAmount;

        if (in_array(self::$PCID, self::$showTotalOwedAndOutstandingBalance)) {
            if ($tempColorY == 1) {
                self::$clsColor = 'red-text';
            }
        }

        /** Mouse over help icon >> Show Borrower details **/
        $BorInfoMO = '<table style="width:100%;">';

        if (self::$userRole == glUserRole::CFPB_AUDITOR
            || self::$userRole == glUserRole::AUDITOR_MANAGER
            || (self::$allowCFPBAuditing == 1 && self::$allowUserToUpdateCFPBFile == 1)
        ) {
            $BorInfoMO .= '<tr><td style="vertical-align:top"><b>Company Name:</b></td><td>' . self::$submitterPCName . '</td></tr>';

            if (trim(self::$submitterPCPhone)) {
                $BorInfoMO .= '<tr><td><b>Company Phone:</b></td><td>' . Strings::formatPhoneNumber(self::$submitterPCPhone) . '</td></tr>';
            }

            $BorInfoMO .= '<tr><td style="vertical-align:top"><b>Submitted By:</b></td><td>' . self::$submittedBy . '</td></tr>';

            if (trim(self::$submitterPhone)) {
                $BorInfoMO .= '<tr><td><b>Submitter Phone:</b></td><td>' . Strings::formatPhoneNumber(self::$submitterPhone) . '</td></tr>';
            }

            if (trim(self::$submitterCell)) {
                $BorInfoMO .= '<tr><td><b>Submitter Cell:</b></td><td>' . Strings::formatPhoneNumber(self::$submitterCell) . '</td></tr>';
            }

            if (trim(self::$submitterEmail)) {
                $BorInfoMO .= '<tr><td><b>Submitter Email:</b></td><td>' . self::$submitterEmail . '</td></tr>';
            }
        }


        if (trim(self::$entityName)) {
            $BorInfoMO .= '<tr><td><b>Entity Name:</b></td><td>' . self::$entityName . '</td></tr>';
        }

        $BorInfoMO .= '<tr><td><b>Borrower&apos;s Name:</b></td><td>' . self::$borrowerName . '</td></tr>';

        if (trim(self::$phoneNumber)) {
            $BorInfoMO .= '<tr><td><b>Phone:</b></td><td>' . Strings::formatPhoneNumber(self::$phoneNumber) . '</td></tr>';
        }

        if (trim(self::$cellNumber)) {
            $BorInfoMO .= '<tr><td><b>Cell:</b></td><td>' . Strings::formatPhoneNumber(self::$cellNumber) . '</td></tr>';
        }

        if (trim(self::$borOrigEmail)) {
            $BorInfoMO .= '<tr><td><b>Email:</b></td><td>' . self::$borOrigEmail . '</td></tr>';
        }


        $propAddressInfo = '';
        if (trim(self::$propertyAddress)) {
            $propAddressInfo = ucwords(self::$propertyAddress);
        }

        $propAddrInfo = '';
        if (trim(self::$propertyCity)) {
            $propAddrInfo .= $appStr . ucfirst(self::$propertyCity);
            $appStr = ', ';
        }
        if (trim(self::$propertyState)) {
            $propAddrInfo .= $appStr . self::$propertyState;
            $appStr = ' ';
        }
        if (trim(self::$propertyZip)) {
            $propAddrInfo .= $appStr . self::$propertyZip;
        }


        if ($propAddrInfo || $propAddressInfo) {
            $BorInfoMO .= '<tr><td><b>Property Address:</b></td><td>' . ($propAddressInfo ? $propAddressInfo . '<br/>' : '') . $propAddrInfo . '</td></tr>';
        }

        if (trim(self::$tempSSNNumber)) {
            $BorInfoMO .= '<tr><td><b>Social Security Number:</b></td><td>' . self::$tempSSNNumber . '</td></tr>';
        }

        if (trim(self::$borrowerDOB)) {
            $BorInfoMO .= '<tr><td><b>Date Of Birth:</b></td><td>' . self::$borrowerDOB . '</td></tr>';
        }

        if (trim(self::$occupancy)) {
            $BorInfoMO .= '<tr><td><b>Occupancy:</b></td><td>' . self::$occupancy . '</td></tr>';
        }

        if (trim(self::$loanNumber)) {
            $BorInfoMO .= '<tr><td><b>' . (self::$isHMLO == 1 ? 'Loan #:' : '1st Lien Loan Number:') . ':</b></td><td>' . self::$loanNumber . '</td></tr>';
        }

        if (trim(self::$servicer1)) {
            $BorInfoMO .= '<tr><td><b>1st Lien Current Lender:</b></td><td>' . self::$servicer1 . '</td></tr>';
        }

        if (trim(self::$loanNumber2)) {
            $BorInfoMO .= '<tr><td><b>2nd Lien Loan Number:</b></td><td>' . self::$loanNumber2 . '</td></tr>';
        }

        if (trim(self::$servicer2)) {
            $BorInfoMO .= '<tr><td><b>2nd Lien Current Lender:</b></td><td>' . self::$servicer2 . '</td></tr>';
        }

        if (self::$userRole == glUserRole::CFPB_AUDITOR
            || self::$userRole == glUserRole::AUDITOR_MANAGER
            || (self::$allowCFPBAuditing == 1 && self::$allowUserToUpdateCFPBFile == 1)
        ) {
            doNothing();
        } else {
            if (trim(self::$primaryStatus)) {
                $BorInfoMO .= '<tr><td><b>' . (self::$isHMLO == 1 ? 'File Primary Status:' : 'Primary Client File Status:') . '</b></td><td>' . self::$primaryStatus . '</td></tr>';
            }

            $fileSubStatus = [];
            foreach (self::$PCSubStatusInfo as $status) {
                foreach (self::$fileSubstatusInfo as $substatus) {
                    if (trim($status->PFSID) == trim($substatus->substatusId)) {
                        $fileSubStatus [] = trim($status->substatus);
                    }
                }
            }

            if (sizeof($fileSubStatus)) {
                $BorInfoMO .= '<tr><td><b>File Sub Status:</b></td><td>' . implode(', ', $fileSubStatus) . '</td></tr>';
            }
        }
        if (self::$isHMLO == 1) {

            $tempLMRClientServiceType = [];
            foreach (self::$tempServicesRequested as $item) {
                $tempLMRClientTypeCode = trim($item->LMRClientType);
                foreach (self::$tempLMRClientTypeInfo as $type) {
                    if ($tempLMRClientTypeCode == trim($type->ClientType)) {
                        $tempLMRClientServiceType [] = trim($item->serviceType);
                    }
                }
            }

            if (sizeof($tempLMRClientServiceType)) {
                $BorInfoMO .= '<tr><td><b>Loan Programs:</b></td><td>' . implode(', ', $tempLMRClientServiceType) . '</td></tr>';
            }

            if (trim(self::$closingDate)) {
                $BorInfoMO .= '<tr><td><b>Closing Date:</b></td><td>' . self::$closingDate . '</td></tr>';
            }
        }

        if (self::$isMF == 1) {
            $MFLoanTermsInfoCnt = count(self::$MFLoanTermsInfo);
            if ($MFLoanTermsInfoCnt) {
                $BorInfoMO .= '<tr><td><b>Applications Submitted:</b></td><td>' . $MFLoanTermsInfoCnt . '</td></tr>';
            }

            $noOfApprovedStatusCnt = 0;
            $totalApprovedLoanAmt = 0;
            foreach (self::$MFLoanTermsInfo as $item) {
                if ($item->applnStatus == 'Approved') {
                    $totalApprovedLoanAmt += $item->approvedLoanAmt;
                    $noOfApprovedStatusCnt++;
                }
            }

            if ($noOfApprovedStatusCnt) {
                $BorInfoMO .= '<tr><td><b>Applications Approved:</b></td><td>' . $noOfApprovedStatusCnt . '</td></tr>';
            }

            if ($totalApprovedLoanAmt) {
                $BorInfoMO .= '<tr><td><b>Total Amount Approved:</b></td><td>' . Strings::Currency($totalApprovedLoanAmt) . '</td></tr>';
            }
        }

        $BorInfoMO .= '</table>';

        self::$BorInfoMO = $BorInfoMO;
    }

    public static function initForms(): void
    {
        if (self::$activeTab == 'REST') {
            if (LMRequest::myFileInfo()) {
                self::$PCPermissonToREST = Strings::showField('subscribeToREST', 'PCInfo');
                self::$branchApprovedByREST = Strings::showField('RESTApproval', 'BranchInfo');
            }
            if ((self::$PCPermissonToREST == 1 && self::$branchApprovedByREST == 1 && self::$permissionToREST == 1) || (self::$userRole == 'Super') || (self::$userRole == 'REST')) {
                self::$subscribeToREST = 1;
            } else {
                self::$subscribeToREST = 0;
            }
        }
        if (self::$subscribeToREST == 1 && self::$activeTab == 'REST') {
            self::$RESTSF = '/backoffice/NPVSave.php'; // change log
            self::$RESTFN = 'NPVForm.php';

        } else {
            self::$RESTSF = 'javascript:void(0);';
            self::$RESTFN = 'NPVFormDemo.php';
        }

        if ((self::$subscribeToHOME == 1 && self::$subscribePCToHOME == 1) || self::$userGroup == 'Super') {
            self::$HRFN = 'homeReportForm.php';
            self::$HRSF = '/backoffice/homeReportSave.php'; // change log
        } else {
            self::$HRSF = 'javascript:void(0);';
            self::$HRFN = 'homeReportDemoPage.php';
        }

        /** The forms array contain the following info.
         ** FN = Form / File Name (to be included), SF = Save File (file to which data is posted), JS = JS validation function
         **/

        self::$validationName = '';
        self::$validationPropertyInfoTab = '';

        if (self::$publicUser == 1) {
            self::$forms = [
                'CI'  => ['FN' => 'clientInfoForm.php', 'SF' => '/backoffice/clientInfoSave.php', 'JS' => ' ( validateLoanForm() && validateQuickAndLongAppFormsHMLI() )'], // change log // trigger
                'IE'  => ['FN' => 'incExpForm.php', 'SF' => '/backoffice/IncExpSave.php', 'JS' => ''], // change log // trigger
                'AL'  => ['FN' => 'assetLiabilityForm.php', 'SF' => '/backoffice/assetLiabilitySave.php', 'JS' => ''], // change log // trigger
                'HA'  => ['FN' => 'hardshipForm.php', 'SF' => '/backoffice/hardshipSave.php', 'JS' => ''], // change log // trigger
                'QA'  => ['FN' => 'QAForm.php', 'SF' => '/backoffice/QASave.php', 'JS' => 'validateQAInfo()'], // change log // trigger
                'DOC' => ['FN' => 'DocsForm.php', 'SF' => '/backoffice/DocsSave.php', 'JS' => ''], // change log // trigger
            ];
        } else {
            if (self::$userGroup == 'Client') {
                self::$validationName = '( validateQuickAndLongAppFormsHMLI() && validateBorrowerFormClientPortal() && validateMinMaxLoanGuidelines() )';
            } else {
                if (self::$allowAutomation) {
                    self::$validAdminForm = '( validateFileAdminForm() && allowAutomationRuleRepeat() )';
                    self::$validationName = '( validateFileTypeAndLoanProgram() && validateClientInfoForm() && validateMinMaxLoanGuidelines() && allowAutomationRuleRepeat() )';
                    self::$validationLoanInfoTab = ' ( validateQuickAndLongAppFormsHMLI() && loanCalculation.checkLoanTermViolation() && allowAutomationRuleRepeat() )';
                    self::$validationPropertyInfoTab = ' ( validatePropertyInfoForm() && allowAutomationRuleRepeat() )';
                    self::$validationDealAnalysisTab = 'allowAutomationRuleRepeat()';
                    self::$validationHMDATab = 'allowAutomationRuleRepeat()';
                    self::$validationIncExpTab = '( validateIncomeExpForm() && allowAutomationRuleRepeat() )';
                    self::$validationAssetLiabilityTab = 'allowAutomationRuleRepeat()';
                    self::$validationBillCommTab = '( validateBillingForm() && allowAutomationRuleRepeat() )';
                    self::$validationAdverseActionTab = 'allowAutomationRuleRepeat()';
                    self::$validationCreditDecisionTab = 'allowAutomationRuleRepeat()';
                } else {
                    self::$validAdminForm = 'validateFileAdminForm()';
                    self::$validationName = '( validateFileTypeAndLoanProgram() && validateClientInfoForm() && validateMinMaxLoanGuidelines()  )';
                    self::$validationLoanInfoTab = ' ( validateQuickAndLongAppFormsHMLI() && loanCalculation.checkLoanTermViolation() )';
                    self::$validationPropertyInfoTab = ' validatePropertyInfoForm() ';
                    self::$validationDealAnalysisTab = '';
                    self::$validationHMDATab = '';
                    self::$validationIncExpTab = 'validateIncomeExpForm()';
                    self::$validationAssetLiabilityTab = '';
                    self::$validationBillCommTab = 'validateBillingForm()';
                    self::$validationAdverseActionTab = '';
                    self::$validationCreditDecisionTab = '';
                }
            }
            self::$forms = [
                'CI'    => ['FN' => 'clientInfoForm.php', 'SF' => '/backoffice/clientInfoSave.php', 'JS' => self::$validationName], // change log // trigger
                'PI'    => ['FN' => 'propertyInfoForm.php', 'SF' => '/backoffice/propertyInfoSave.php', 'JS' => self::$validationPropertyInfoTab], // change log
                'IE'    => ['FN' => 'incExpForm.php', 'SF' => '/backoffice/IncExpSave.php', 'JS' => self::$validationIncExpTab], // change log // trigger
                'HA'    => ['FN' => 'hardshipForm.php', 'SF' => '/backoffice/hardshipSave.php', 'JS' => ''], // change log // trigger
                'QA'    => ['FN' => 'QAForm.php', 'SF' => '/backoffice/QASave.php', 'JS' => 'validateQAInfo()'], // change log // trigger
                'REST'  => ['FN' => self::$RESTFN, 'SF' => self::$RESTSF, 'JS' => ''], // change log
                'LP'    => ['FN' => 'LMProposalForm.php', 'SF' => '/backoffice/LMProposalSave.php', 'JS' => ''], // change log // trigger
                'CON'   => ['FN' => 'contactInfoForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''],
                'SS'    => ['FN' => 'SSForm.php', 'SF' => '/backoffice/SSSave.php', 'JS' => 'validateShorSaleForm()'], // change log // trigger
                'SP'    => ['FN' => 'SSProposalForm.php', 'SF' => '/backoffice/SSProposalSave.php', 'JS' => ''], // change log // trigger
                'HUD'   => ['FN' => 'HUDForm.php', 'SF' => '/backoffice/HUDSave.php', 'JS' => ''], // change log // trigger
                'AL'    => ['FN' => 'assetLiabilityForm.php', 'SF' => '/backoffice/assetLiabilitySave.php', 'JS' => self::$validationAssetLiabilityTab], // change log // trigger
                'DOC'   => ['FN' => 'DocsForm.php', 'SF' => '/backoffice/DocsSave.php', 'JS' => ''], // change log // trigger
                'TA'    => ['FN' => 'taskForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''],
                'ADMIN' => ['FN' => 'fileAdminForm.php', 'SF' => '/backoffice/fileAdminSave.php', 'JS' => self::$validAdminForm], // change log // trigger
                'LE'    => ['FN' => 'legalForm.php', 'SF' => '/backoffice/legalSave.php', 'JS' => ''], // change log // trigger
                'MI'    => ['FN' => 'mortgageInfoForm.php', 'SF' => '/backoffice/mortgageInfoSave.php', 'JS' => ''], // change log // trigger
                'NOI'   => ['FN' => 'adverseAction.php', 'SF' => '/backoffice/adverseActionSave.php', 'JS' => self::$validationAdverseActionTab], // change log // trigger
                'CD'    => ['FN' => 'creditDecisionForm.php', 'SF' => '/backoffice/creditDecisionSave.php', 'JS' => self::$validationCreditDecisionTab],
            ];
            if (self::$userGroup != 'Agent' || (self::$userGroup == 'Agent' && self::$allowAgentWorkflowEdit == 1)) {
                self::$forms['CW'] = ['FN' => 'CWForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''];
            }
            /*
             * Allow to view new tab intake
             * AWATA, Dave PC, Enrollment Advisory (Client Document)
             */
            if (in_array(self::$PCID, self::$accessIntakeFileTabPC ?? [])) {
                self::$forms['INT'] = ['FN' => 'clientDocumentIntakeForm.php', 'SF' => '/backoffice/clientDocumentIntakeFormSave.php', 'JS' => 'validateClientDocumentIntakeForm()']; // change log // trigger
            }

            /* PC = Loscalzo & Associates */
            if (in_array(self::$PCID, self::$accessNewIntakeFileTabPC ?? [])) {
                self::$forms['INT'] = ['FN' => 'loscalzoIntakeForm.php', 'SF' => '/backoffice/loscalzoIntakeFormSave.php', 'JS' => 'validateLoscalzoIntakeForm()']; // change log // trigger
            }

            /*
             * CFPB Audit tab
             */
            if (self::$isSLMOnly != 1
                && (self::$allowPCUserToSubmitCFPB == 1 || self::$allowCFPBAuditing == 1)
                && count(self::$CFPBInfo ?? []) > 0
            ) {
                self::$forms['CFPB'] = ['FN' => 'CFPBAuditForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''];
            }

            if (self::$isSLMOnly == 1) {
                unset(self::$forms['LE']);
            }

            if (self::$userSeeBilling == 1 && self::$userRole != 'Auditor') {
                self::$forms['BC'] = ['FN' => 'BCForm.php', 'SF' => '/backoffice/BCSave.php', 'JS' => self::$validationBillCommTab]; // change log // trigger
            }

            if (self::$allowToAccessRAM == 1) {
                self::$forms['RAM'] = ['FN' => 'RAMFileForm.php', 'SF' => '/backoffice/RAMFileInfoSave.php', 'JS' => 'validateRAMFileForm()']; // change log // trigger
            }

            if (self::$userRole == 'Auditor') {
                unset(self::$forms['CW']);
                unset(self::$forms['REST']);
            }
            if (self::$PCID == '656') {
                /** Pac1lending.com **/
                unset(self::$forms['HR']);
            }
            if (self::$isSLM == 1 || self::$isSLMOnly == 1) {
                self::$forms['SLM'] = ['FN' => 'SLMForm.php', 'SF' => '/backoffice/SLMSave.php', 'JS' => 'validateSLMInfoForm()']; // change log // trigger
            }
        }

        if (self::$isHOALien == 1) {
            self::$forms['ER'] = ['FN' => 'estimatedRepairForm.php', 'SF' => '/backoffice/estimatedRepairSave.php', 'JS' => '']; // change log // trigger
        }

        /* Loan Origination File - Show Explanation tab */

        if (self::$isLO == 1) {/*  remove expln tab on Feb 3, 2017 */
            self::$forms['EXP'] = ['FN' => 'explanationForm.php', 'SF' => '/backoffice/explanationSave.php', 'JS' => '']; // change log // trigger
        }

        /* Merchant Funding File - Show Merchant Offers tab (March 26,2016) */

        if (self::$isMF == 1 || (glCustomJobForProcessingCompany::showLoanTermsToBusinessFunding(self::$PCID) && self::$ftModuleCode == 'loc')) { //2057 customization (ticket is sc-30749) **exception**
            self::$forms['MFO'] = ['FN' => 'merchantFundingOfferForm.php', 'SF' => '/backoffice/merchantFundingOfferSave.php', 'JS' => '']; // does not exist
            self::$forms['LT'] = ['FN' => 'MFLoanTerms.php', 'SF' => '/backoffice/MFLoanTermsSave.php', 'JS' => '']; // empty file
        }

        /* Short sale File - Show Dash tab */
        if (self::$showSSTab == 1) {
            self::$forms['DASH'] = ['FN' => 'SSDashForm.php', 'SF' => '/backoffice/SSDashSave.php', 'JS' => '']; // change log // trigger
        }
        self::$forms['SSS'] = ['FN' => 'emeraldQuickForm.php', 'SF' => '/backoffice/saveEmeraldQuickForm.php', 'JS' => '']; // change log // trigger

        /* Loan servicing File - Show summary tab (Sep 23, 2016.) */
        if (self::$isLSR == 1) {
            self::$forms['LSS'] = ['FN' => 'loanServicingSummaryForm.php', 'SF' => '/backoffice/loanServicingSummarySave.php', 'JS' => '']; // change log // trigger
        }
        if (self::$allowPCToMarketPlace == '1') {
            if (self::$allowToViewMarketPlace == '1') {
                self::$forms['MP'] = ['FN' => 'marketPlaceLoanForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''];
            }
        }

        if (self::$subscribePCToHOME == '1') {
            if (self::$subscribeToHOME == '1') {
                self::$forms['HR'] = ['FN' => self::$HRFN, 'SF' => self::$HRSF, 'JS' => ''];
            }
        }
        if (self::$allowPeerstreet == '1') {
            self::$forms['PE'] = ['FN' => 'peerStreet.php', 'SF' => null, 'JS' => 'validateHMLOWebForm()']; // change log // trigger

        }

        if (self::$pcPriceEngineStatus > 0 && self::$userPriceEngineStatus > 0) {
            self::$forms['PEN'] = ['FN' => 'filePricingEngine.php', 'SF' => '', 'JS' => ''];
        }

        if (self::$allowServicing == '1') {
            self::$forms['SER2'] = ['FN' => 'servicingForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''];
        }

        if (self::$allowPCToSeeAlowareTab == '1') {
            if (self::$allowToSeeAlowareTab == '1') {
                self::$forms['AW'] = ['FN' => 'alowarePage.php', 'SF' => '', 'JS' => ''];
            }
        }

        /** New Hard / Private Money LOS module on (Nov 21, 2016) **/
        //if ($isHMLO == 1) {
        self::$forms['LI'] = ['FN' => 'HMLOLoanInfoForm.php', 'SF' => '/backoffice/HMLOLoanInfoSave.php', 'JS' => '']; // change  // trigger
        self::$forms['1003'] = ['FN' => 'HMLO1003InfoForm.php', 'SF' => '/backoffice/HMLO1003InfoSave.php', 'JS' => '']; // change log // trigger
        // self::$forms['SER'] = ['FN' => 'HMLOServicingForm.php', 'SF' => '/backoffice/HMLOServicingSave.php', 'JS' => self::$validationServicingTab]; // change log // trigger // deprecated
        self::$forms['HMLI'] = ['FN' => 'HMLONewLoanInfoForm.php', 'SF' => '/backoffice/HMLONewLoanInfoSave.php', 'JS' => self::$validationLoanInfoTab]; // change log // trigger
        self::$forms['DA'] = ['FN' => 'dealAnalysisInfoForm.php', 'SF' => '/backoffice/dealAnalysisSave.php', 'JS' => self::$validationDealAnalysisTab]; // change log // trigger
        self::$forms['GOVT'] = ['FN' => 'govtMntrInfoForm.php', 'SF' => '/backoffice/govtMntrInfoSave.php', 'JS' => self::$validationHMDATab]; // change log // trigger
        self::$forms['TPS'] = ['FN' => 'thirdPartyServiceInfoForm.php', 'SF' => '/backoffice/saveThirdPartyServiceInfo.php', 'JS' => 'validateHMLOWebForm()']; // change log // trigger
        self::$forms['QAPP'] = ['FN' => 'HMLOLoanInfoForm.php', 'SF' => '/backoffice/HMLOLoanInfoSave.php', 'JS' => '']; // change log // trigger
        self::$forms['BD'] = ['FN' => 'budgetAndDrawsInfo.php', 'SF' => '/backoffice/saveBudgetAndDraws.php', 'JS' => '']; // change log // trigger
        //}

        /* Equipment Financing - EF Module - Selected in the file (PT-#155682423) - April 18, 2018 */
        //if ($isEF == 1) {
        self::$forms['EF'] = ['FN' => 'equipmentFinancingForm.php', 'SF' => '/backoffice/equipmentFinancingSave.php', 'JS' => '']; // change log // trigger
        //}

        /**
         * Description    : Added the New tab for "Entity Info" in Business Funding Modules
         * Date        : Dec 07, 2017
         **/
        //if ($isMF == 1) {
        self::$forms['FUEI'] = ['FN' => 'fundingEntityInfoForm.php', 'SF' => '/backoffice/fundingEntityInfoSave.php', 'JS' => '']; // change log // trigger
        self::$forms['FUCE'] = ['FN' => 'fundingCreditEnhancementForm.php', 'SF' => '/backoffice/fundingCreditEnhancementSave.php', 'JS' => '']; // change log // trigger
        //}


        if (in_array(self::$PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
            self::$forms['DS'] = [
                'FN' => 'dealSizerForm.php',
                'SF' => '/backoffice/dealSizerSave.php', // change log // trigger
                'JS' => '',
            ];
        }
        /*
         * Allow to view new tab intake
         * AWATA, Dave PC, Enrollment Advisory (Client Document)
         */

        if (self::$viewSubmitOfferTab == '1') {
            self::$forms['SO'] = ['FN' => 'LMRequest/sections/submitOffer.php', 'SF' => '/backoffice/submitOfferSave.php', 'JS' => '']; // change log // trigger
        }

        self::$forms['MEMO'] = ['FN' => 'creditMemoSection.php', 'SF' => '/backoffice/creditMemoSave.php', 'JS' => '']; // change log // trigger

        self::$forms['RC'] = [
            'FN' => '/LMRequest/sections/rule_check.php',
        ];
        if (LMRequest::$hasChangeLog) {
            self::$forms['CL'] = [
                'FN' => 'changeLog.php',
            ];
        }
    }

    public static function getFileTitle(): string
    {
        $appStr = '';
        $fileTitle = '';
        $propAddrInfo = '';
        if (LoanMenu::$entityName) {
            $fileTitle = LoanMenu::$entityName;
            $appStr = ', ';
        }
        if (LoanMenu::$borrowerName) {
            if ($fileTitle) {
                $fileTitle .= $appStr;
            }
            $fileTitle .= LoanMenu::$borrowerName;
            $appStr = ', ';
        }
        if (trim(LoanMenu::$propertyAddress)) {
            $propAddrInfo .= ucwords(LoanMenu::$propertyAddress);
            $appStr = ', ';
        }
        if (trim(LoanMenu::$propertyCity)) {
            $propAddrInfo .= $appStr . ucfirst(LoanMenu::$propertyCity);
            $appStr = ', ';
        }
        if (trim(LoanMenu::$propertyState)) {
            $propAddrInfo .= $appStr . LoanMenu::$propertyState;
            $appStr = ' ';
        }
        if (trim(LoanMenu::$propertyZip)) {
            $propAddrInfo .= $appStr . LoanMenu::$propertyZip;
        }

        return 'File: ' . stripslashes($fileTitle) . ($propAddrInfo ? ', ' . stripslashes($propAddrInfo) : '') . (LMRequest::File()->activeStatus == 0 ? ' (Inactive)' : '');
    }

    public static function getLink(string $fileTabsKey): string
    {
        return '/backoffice/LMRequest.php?lId=' . ($_REQUEST['lId'] ?? '') . '&eId=' . ($_REQUEST['eId'] ?? '') . '&rId=' . ($_REQUEST['rId'] ?? '') . '&tabOpt=' . $fileTabsKey . '&op=';
    }

    public static function getMVCLink(string $fileTabsKey): string
    {
        if (!isset(self::TabToMVC[$fileTabsKey])) {
            Debug($fileTabsKey . ' unknown', self::$LMRId);
        }

        return '/backoffice/loan/' . self::TabToMVC[$fileTabsKey] . '?lId=' . ($_REQUEST['lId'] ?? '') . '&eId=' . ($_REQUEST['eId'] ?? '') . '&rId=' . ($_REQUEST['rId'] ?? '') . '&tabOpt=' . $fileTabsKey . '&op=';
    }
}
