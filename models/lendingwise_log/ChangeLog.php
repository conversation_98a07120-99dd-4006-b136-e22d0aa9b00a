<?php

namespace models\lendingwise_log;

use models\Database2;
use models\dbExaminer;
use models\lendingwise\tblFile;
use models\lendingwise\tblProperties;
use models\PageVariables;
use models\portals\LoginPage;
use models\standard\Dates;
use models\standard\Strings;
use models\types\databaseType;
use models\types\strongType;

class ChangeLog extends strongType
{
    public ?int $id = null;
    public ?string $object_type = null;
    public ?string $primary_key = null;
    public ?string $source = null;
    public ?string $changes = null;
    public ?int $changed_by_id = null;
    public ?string $changed_by_ur = null;
    public ?string $changed_at = null;
    public ?string $request = null;

    public function changes()
    {
        return json_decode(str_replace("\\'", "'", $this->changes), true);
    }

    // this is for the form to see if a field has the change log
    public static function GetColumnsFor(
        string  $object_type,
        ?string $primary_key
    ): array
    {
        if (!$primary_key) {
            return [];
        }

        $logs = self::GetFor($object_type, $primary_key);
        $columns = [];
        foreach ($logs as $log) {
            $changes = $log->changes();
            if (!is_array($changes)) {
                continue;
            }
            $cols = array_keys($changes);
            foreach ($cols as $col) {
                $columns[$col] = $col;
            }
        }
        ksort($columns);
        return array_values($columns);
    }

    // this is for the API endpoint which renders the requested change log values

    /**
     * @param string $object_type
     * @param string|null $primary_key
     * @return self[]
     */
    public static function GetFor(
        string  $object_type,
        ?string $primary_key
    ): array
    {
        if (!$primary_key) {
            return [];
        }

        $sql = '
            SELECT
                changes
                , changed_by_id
                , changed_by_ur
                , changed_at
            FROM
                lendingwise_log.change_log
            WHERE 
                object_type = :object_type
                AND primary_key = :primary_key
            ORDER BY 
                changed_at
        ';
        return Database2::getInstance()->queryData($sql, [
            'object_type' => str_replace('\\', '\\\\', $object_type),
            'primary_key' => $primary_key,
        ], function ($row) {
            return new self($row);
        });
    }

    public static function hasChangeLog(
        string  $object_type,
        ?string $primary_key
    ): int
    {
        if (!$primary_key) {
            return 0;
        }

        $sql = '
            SELECT
                COUNT(*) AS logs
            FROM
                lendingwise_log.change_log
            WHERE 
                object_type = :object_type
                AND primary_key = :primary_key
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'object_type' => str_replace('\\', '\\\\', $object_type),
            'primary_key' => $primary_key,
        ]);
        return intval($res[0]['logs'] ?? 0);
    }

    public static function getLMRIdColumn(
        databaseType $obj
    ): ?string
    {
        $class = get_class($obj);
        return self::getPrimaryColumnForTable(
            tblFile::class,
            $class::TABLE,
            true
        );
    }

    public static function getPrimaryColumnForTable(
        string $objectType,
        string $table,
        ?bool  $ignoreMissing = false
    ): ?string
    {
        switch ($objectType) {
            case tblFile::class:
                switch ($table) {
                    case 'tblFileHMLONewLoanInfo':
                    case 'tblFileHMLOPropInfo':
                    case 'tblFileLOExplanation':
                    case 'tblFileLoanOrigination':
                    case 'tblFileLoanServicing':
                    case 'tblFileHMLO':
                    case 'tblFileHMLOBackGround':
                    case 'tblFileHMLOBusinessEntity':
                    case 'tblFileHMLOExperience':
                    case 'tblFileLOAssetsInfo':
                    case 'tblFileLOPropertyInfo':
                    case 'tblFileSBAQuestions':
                    case 'tblHomeReport':
                    case 'tblFileHMLOListOfRepairs':
                    case 'tblFilePayee':
                    case 'tblFileDealAnalysis':
                    case 'tblFileCourtInfo':
                    case 'tblFileLegalBankruptcy':
                    case 'tblFileClients':
                    case 'tblLMRProcessorComments':
                    case 'tblFileLOScheduleRealInfo':
                    case 'tblFileLOChekingSavingInfo':
                    case 'tblFileLOLiabilitiesInfo':
                    case 'tblFileHMLOBusinessEntityRef':
                    case 'tblInsuranceDetails':
                    case 'tblFileUpdatedDate':
                    case 'tblFileContacts':
                    case 'tblFileMFLoanTerms':
                        return 'fileID';
                    case 'tblIncomeInfo':
                    case 'tblShortSale':
                    case 'tblFile':
                    case 'tblQAInfo':
                    case 'tblDealSizerCommercial':
                    case 'tblLoanSetting':
                    case 'fixpayment':
                    case 'tblFilePropertyInfo':
                    case 'tblFileResponse':
                    case 'tblPropertyManagement':
                    case 'tblPropSellerInfo':
                    case 'tblRemoteUrl':
                    case 'tblRestInfo':
                    case 'tblAdverseAction':
                    case 'tblShortSale2':
                    case 'tblProposalInfo':
                    case 'tblOffers':
                    case 'tblFilePropertyInfo2':
                    case 'tblListingPageData':
                    case 'tblThirdPartyService':
                    case 'tblPeerStreet':
                    case 'tblFileAdminInfo':
                    case 'tblProperties':
                    case 'tblRecentSalesInfo':
                    case 'tblCreditDecisionException':
                    case 'tblFundingClosing':
                    case 'tblFileCalculatedValues':
                    case 'tblFilePaymentDue':
                    case 'tblCreditDecisionForm':
                    case 'tblSSRegnInfo':
                    case 'tblGiftsOrGrants':
                    case 'tblLMRCreditorInfo':
                    case 'tblPartnerShips':
                    case 'tblFileAdditionalGuarantors':
                    case 'tblFileRentRoll':
                    case 'tblEquipmentInfo':
                    case 'tblMarketPlaceFilterResults':
                    case 'tblRefinanceMortgage':
                    case 'tblCreditMemo':
                    case 'tblLoanSettingTerms':
                    case 'tblLMRCommission':
                    case 'tblLoanPropertySummary':
                    case 'tblFileServicing':
                    case 'tblDeedOfTrusts':
                    case 'tblMortgageAssignments':
                        return 'LMRId';
                    case 'collateral':
                    case 'estimatedProjectCost':
                    case 'contingentLiabilities':
                        return 'lmrid';
                    case 'tblLoanOriginatorInfo':
                    case 'tblTempFinancialInfoUpdated':
                    case 'tblACHInfo':
                    case 'tblAssetsInfo':
                    case 'tblEquipmentInformation':
                    case 'tblFile2':
                    case 'tblFileHUDBasicInfo':
                    case 'tblRecordFileStatus':
                    case 'tblCollateralValues':
                    case 'tblLMProposalSummary':
                    case 'tblHardshipDescription':
                    case 'tblFeeSchedule':
                    case 'tblSalesMethod':
                    case 'tblHAFATermsOfSale':
                    case 'tblSSProposalInfo':
                    case 'tblBorrowerAlternateNames':
                    case 'tblFileAdditionalLoanPrograms':
                    case 'tblRecordFileSubstatus':
                    case 'tblOtherNewMortgageLoansOnProperty':
                    case 'tblLMRClientType':
                        return 'LMRID';
                    case 'tblDomainComments':
                    case 'tblClient':
                    case 'tblContacts':
                    case 'tblMarketPlaceLeadStatus':
                    case 'tblPCHMLOBasicLoanTransactionType':
                    case 'tblClientReferral':
                    case 'tblBrokerLoanofficer':
                    case 'tblalowareuser':
                    case 'tblInvestorFunding':
                    case 'tblPropertiesDetails':
                    case 'tblBranch':
                    case 'tblInvestorDistribution':
                    case 'tblLMRCreditorStatus':
                    case 'tblPropertiesCharacteristics':
                    case 'tblPropertiesAnalysis':
                    case 'tblPropertiesFloodCertificates':
                    case 'tblPropertiesAppraiserDetails':
                    case 'tblPropertiesHOA':
                    case 'tblReservesMaster':
                    case 'tblProcessingCompany':
                    case 'tblPropertiesAccess':
                    case 'tblBuildingAnalysisOutstanding':
                    case 'tblBuildingAnalysisNeed':
                    case 'tblBorrowerAuthorizationStatus':
                    case 'tblVOMPayoffStatus':
                    case 'tblBorrowerExperienceTrackRecord':
                    case 'tblWelcomeCallStatus':
                    case 'tblLenders':
                    case 'tblThirdPartyServicesUserDetails':
                    case 'tblThirdPartyServicesPCDetails':
                    case 'tblFileStorage':
                        return null;
                    default:
                        if ($ignoreMissing) {
                            return null;
                        }
                        Debug([
                            'objectType' => $objectType,
                            'table'      => $table,
                            'columns'    => dbExaminer::getColumns($table),
                        ]);
                        return null;
                }
        }
        if ($ignoreMissing) {
            return null;
        }
        Debug([
            'objectType' => $objectType,
            'table'      => $table,
        ]);
        return null;
    }

    public static function LogChanges(
        string $object_type,
        string $primary_key,
        string $source,
        array  $submitted_values,
        ?int   $userNumber
    )
    {
        if (!$primary_key) {
            return;
        }

        $params = [];
        $in = [];
        $j = 0;
        ksort($submitted_values);
        foreach ($submitted_values as $k => $v) {
            if (is_array($v)) {
                continue;
            }
            $j++;
            $in[] = ':field' . $j;
            $params['field' . $j] = ($k);
        }
        $sql = '
            SELECT
                tableName
                 , fieldName
            FROM 
                lendingwise_log.table_fields
            WHERE 
                fieldName IN (' . implode(', ', $in) . ')
        ';
        $res = Database2::getInstance()->queryData($sql, $params);
        $getValues = [];
        foreach ($res as $row) {
            $getValues[$row['tableName']][] = $row['fieldName'];
        }

        $currentValues = [];
        $columnTypes = [];
        foreach ($getValues as $table => $fields) {
            if (in_array($table, [
                'acqualifyAccount',
                'tblAdminUsers',
                'tblImportCSV',
                'tblPCHMLOBasicLoanInfo',
                'tblProcessingCompany',
            ])) {
                continue;
            }
            $cols = dbExaminer::getColumns($table);
            foreach ($cols as $col) {
                if (isset($submitted_values[$col->Field])) {
                    $columnTypes[$col->Field] = dbExaminer::getDataType($col->Type);
                }
            }
            $primaryColumn = self::getPrimaryColumnForTable($object_type, $table);
            if (!$primaryColumn) {
                continue;
            }
            $sql = '
                   SELECT
                   `' . implode('`,`', $fields) . '`
                   FROM `' . $table . '`
                   WHERE `' . $primaryColumn . '` = :primaryKey
            ';
            $res = Database2::getInstance()->queryData($sql, [
                'primaryKey' => $primary_key,
            ]);
            foreach ($res as $row) {
                foreach ($row as $k => $v) {
                    $currentValues[$k] = $v;
                }
            }
        }

        ksort($columnTypes);

        $changeLog = [];
        foreach ($currentValues as $k => $v) {
            if (!isset($columnTypes[$k])) {
                continue;
            }
            if ($v == '0000-00-00' && $submitted_values[$k] == '') {
                continue;
            }
            if ($v == '0.00' && $submitted_values[$k] == '') {
                continue;
            }
            if ($v == '0.00' && $submitted_values[$k] == '0') {
                continue;
            }
            if (strcasecmp($v, $submitted_values[$k]) !== 0) {
                switch ($columnTypes[$k]) {
                    case 'datetime':
                        $submitted_values[$k] = Dates::Timestamp($submitted_values[$k]);
                        break;
                    case 'string':
                        break;
                    case 'float':
                    case 'int':
                        $submitted_values[$k] = Strings::replaceCommaValues($submitted_values[$k]);
                        break;
                    default:
                        Debug($k, $columnTypes[$k], $columnTypes);
                }
                $changeLog[$k] = ['old' => $v, 'new' => $submitted_values[$k]];
            }
        }
        if (!sizeof($changeLog)) {
            return;
        }

        $sql = '
            INSERT INTO lendingwise_log.change_log
            (object_type, primary_key, source, changes, changed_by_id, changed_by_ur, changed_at, request)
VALUES (
    :object_type
    , :primary_key
    , :source
    , :changes
    , :changed_by_id
    , :changed_by_ur
    , :changed_at
    , :request        
)
        ';

        $ur = $_SESSION['UR'] ?? '';
        $userNumber = PageVariables::$isGhosted ? (PageVariables::$GhostDetails['created_by_id'] ?? $userNumber) : $userNumber;
        $ur = PageVariables::$isGhosted ? LoginPage::UR_BACKOFFICE : $ur;

        $submitted_values['_SERVER_'] = $_SERVER;

        Database2::getInstance()->executeQuery($sql, [
            'object_type'   => str_replace('\\', '\\\\', $object_type),
            'primary_key'   => $primary_key,
            'source'        => $source,
            'changes'       => json_encode($changeLog),
            'changed_by_id' => $userNumber,
            'changed_by_ur' => $ur,
            'changed_at'    => Dates::Timestamp(),
            'request'       => base64_encode(gzcompress(json_encode($submitted_values))),
        ]);

    }

    public static function Insert(
        string $object_type,
        string $primary_key,
        string $source,
        array  $changeLog,
        int    $userNumber
    )
    {
        $sql = '
            INSERT INTO lendingwise_log.change_log
            (object_type, primary_key, source, changes, changed_by_id, changed_by_ur, changed_at, request)
VALUES (
    :object_type
    , :primary_key
    , :source
    , :changes
    , :changed_by_id
    , :changed_by_ur
    , :changed_at
    , :request        
)
        ';

        $ur = $_SESSION['UR'] ?? '';
        $userNumber = PageVariables::$isGhosted ? (PageVariables::$GhostDetails['created_by_id'] ?? $userNumber) : $userNumber;
        $ur = PageVariables::$isGhosted ? LoginPage::UR_BACKOFFICE : $ur;


        Database2::getInstance()->executeQuery($sql, [
            'object_type'   => str_replace('\\', '\\\\', $object_type),
            'primary_key'   => $primary_key,
            'source'        => $source,
            'changes'       => json_encode($changeLog),
            'changed_by_id' => $userNumber,
            'changed_by_ur' => $ur,
            'changed_at'    => Dates::Timestamp(),
            'request'       => base64_encode(gzcompress(json_encode($_REQUEST))),
        ]);
    }
}
