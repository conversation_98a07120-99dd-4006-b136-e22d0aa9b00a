<?php

namespace models\composite\oEmail;

use models\constants\gl\glPCID;
use models\constants\MailType;
use models\Controllers\LMRequest\Property;
use models\Controllers\LMRequest\WebForm;
use models\lendingwise\emailNotification;
use models\lendingwise\tblMailQueue;
use models\myFileInfo;
use models\standard\Integers;
use models\types\strongType;

/**
 *
 */
class MailNotification extends strongType
{

    public static ?myFileInfo $myFileInfoObject = null;

    /**
     * @param array $event
     * @return array
     */
    public static function sanitizePropertyFields(array $event): array
    {
        $output = [];
        foreach ($event as $fieldKey => $fieldValue) {
            $fieldKey = str_replace('-', '_', $fieldKey);

            if (is_array($fieldValue)) {
                $fieldValue = implode(',', $fieldValue);
            }
            $output[$fieldKey] = $fieldValue;
        }
        if ($event['event'] == 'bounce' && $event['processingCompanyId'] == glPCID::PCID_PROD_CV3) {
            self::alertBouncedEmailInfo($event);
        }
        return $output;
    }


    /**
     * @param $ip
     * @return int|string|null
     */
    public static function save($ip): void
    {
        $event = $ip['event'];
        $data = self::sanitizePropertyFields($event);
        $emailNotification = new emailNotification();
        $emailNotification->fromData($data);
        $emailNotification->Save();
    }

    /**
     * @param array $data
     * @return void
     */
    public static function alertBouncedEmailInfo(array $data): void
    {
        $LMRId = $data['LMRID'];
        $mailNumber = $data['mailNumber'];
        $bouncedEmail = $data['email'];
        $PCID = $data['processingCompanyId'];

        $mailQueue = null;
        if (!$mailNumber
            || !$LMRId
            || $PCID <> glPCID::PCID_PROD_CV3
            || Integers::checkEmailIsDummy($bouncedEmail)
        ) {
            return;
        }
        self::$myFileInfoObject = new myFileInfo();
        self::$myFileInfoObject->LMRId = $LMRId;
        $propertyAddress = Property::getPropertyFullAddress(Property::getPrimaryPropertyInfo($LMRId)) ?? null;

        $toName = $toEmail = [];

        foreach ((self::$myFileInfoObject->AssignedBOStaffInfo() ?? []) as $eachEmployeeInfo) {
            if ($eachEmployeeInfo->role == 'Account Manager') {
                $toName[] = $eachEmployeeInfo->processorName;
                $toEmail[] = $eachEmployeeInfo->email;
            }
        }
        if (self::$myFileInfoObject->SecondaryBrokerInfo()->email) {
            $toName[] = self::$myFileInfoObject->SecondaryBrokerInfo()->firstName . ' ' . self::$myFileInfoObject->SecondaryBrokerInfo()->lastName;
            $toEmail[] = self::$myFileInfoObject->SecondaryBrokerInfo()->email;
        }
        $tblMailQueue = tblMailQueue::Get(['mail_Number' => $mailNumber]);

        $subject[] = 'Unsuccessful Email Attempt : ' . $LMRId;
        if ($propertyAddress) {
            $subject[] = $propertyAddress;
        }
        $subject[] = self::$myFileInfoObject->LMRData()->borrowerName . ' ' . self::$myFileInfoObject->LMRData()->borrowerLName;
        $subject = implode(' | ', $subject);

        WebForm::$myFileInfoObject = self::$myFileInfoObject;
        $loanFilePathBO = WebForm::generateFilePath($LMRId, 'Employee', 'QAPP');
        $loanFilePathLO = WebForm::generateFilePath($LMRId, 'Agent', 'QAPP');

        $mailBody = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                       <html xmlns="http://www.w3.org/1999/xhtml" lang="">
                        <head>
                            <title>Email Bounced</title>
                            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                            <meta http-equiv="Content-Language" content="en-us">
                        </head>
                        <body style="background-color: rgb(255, 255, 255);">';
        $mailBody .= '<div style="font-family: Arial, Helvetica, sans-serif; color: #000000;">';
        $mailBody .= '<p>Hello  ';
        if (sizeof($toName) > 1) {
            $mailBody .= '<b>' . implode('</b> and <b>', $toName) . '</b>';
        } else {
            $mailBody .= $toName[0];
        }
        $mailBody .= ', </p>';
        $mailBody .= '<p>The below email was unsuccessful to <b>' . $bouncedEmail . '</b></p>';
        if ($tblMailQueue->subj) {
            $mailBody .= '<p>Subject : ' . $tblMailQueue->subj . '</p>';
        }
        $mailBody .= '<p>Here is a link to that loan (BackOffice): <a href="' . $loanFilePathBO . '" target="_blank">' . $LMRId . '</a></p>';
        $mailBody .= '<p>Here is a link to that loan (LoanOfficer): <a href="' . $loanFilePathLO . '" target="_blank">' . $LMRId . '</a></p>';
        $mailBody .= '</div>';
        $mailBody .= '</body></html>';

        $mailQueue[] = [
            'fromName' => $tblMailQueue->sender,
            'fromEmailActual' => CONST_EMAIL_FROM,
            'fromEmail' => CONST_EMAIL_FROM,
            'sendAsUser' => $tblMailQueue->sendAsUser,
            'sendServerUserId' => $tblMailQueue->sendServerUserId,
            'sendServerUserType' => '',
            'toName' => implode(';', $toName),
            'toEmail' => implode(';', $toEmail),
            'mailType' => MailType::FEEDBACK,
            'subject' => $subject,
            'msg' => $mailBody,
            'PCID' => self::$myFileInfoObject->LMRData()->FPCID,
            'LMRId' => $LMRId,
            'UID' => $tblMailQueue->UID,
            'URole' => $tblMailQueue->UType,
        ];
        if ($mailQueue) {
            saveMailsToQueue::getReport(['info' => $mailQueue]);
        }
    }

}
