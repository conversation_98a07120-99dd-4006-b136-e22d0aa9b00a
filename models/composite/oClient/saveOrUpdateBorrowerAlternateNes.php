<?php

namespace models\composite\oClient;

use models\Database2;
use models\types\strongType;

/**
 *
 */
class saveOrUpdateBorrowerAlternateNes extends strongType
{
    /**
     * @param $ip
     * @return array|null
     */
    public static function getReport($ip): ?array
    {
        $alternateNamesArrayValues = [];
        $alternateFNameArr = array_key_exists('alternateFName', $ip) ? $ip['alternateFName'] : [];
        $alternateMNameArr = array_key_exists('alternateMName', $ip) ? $ip['alternateMName'] : [];
        $alternateLNameArr = array_key_exists('alternateLName', $ip) ? $ip['alternateLName'] : [];
        $LMRID = array_key_exists('LMRID', $ip) ? $ip['LMRID'] : 0;
        $alternateNameIDArr = array_key_exists('alternateNameID', $ip) ? $ip['alternateNameID'] : [];

        $qryRes = Database2::getInstance()->selectRowsFromTable(
            'tblBorrowerAlternateNames',
            ['nameID'],
            ' LMRID = :LMRID', [
                'LMRID' => $LMRID,
            ]
        );
        $deletedNameIDArr = array_values(array_diff(array_column($qryRes, 'nameID'), $alternateNameIDArr ?? []));

        if (count($deletedNameIDArr) > 0) {
            $sqlParams = [];
            $sql = ' DELETE FROM tblBorrowerAlternateNames WHERE nameID IN ( ' . Database2::GetPlaceholders(sizeof($deletedNameIDArr), ':nameID', true) . ' )';
            foreach ($deletedNameIDArr as $i => $paVal) {
                $sqlParams['nameID' . $i] = trim($paVal);
            }
            return Database2::getInstance()->queryData($sql, $sqlParams);
        }
        $insSql = 'INSERT INTO tblBorrowerAlternateNames (
                        LMRID,
                        alternateFName,
                        alternateMName,
                        alternateLName                    ) VALUES (
                        :LMRID,
                        :alternateFName,
                        :alternateMName,
                        :alternateLName                    );';
        foreach ($alternateFNameArr as $an => $item) {
            if ($item != '' || $alternateMNameArr[$an] != '' || $alternateLNameArr[$an] != '') {
                if ($alternateNameIDArr[$an] > 0) {
                    //update
                    $qryUpdate = ' UPDATE tblBorrowerAlternateNames set alternateFName = :alternateFName , alternateMName = :alternateMName ,
                                      alternateLName = :alternateLName  where nameID = :alternateNameID ;';
                    $sqlParams = [
                        'alternateFName' => $item,
                        'alternateMName' => $alternateMNameArr[$an],
                        'alternateLName' => $alternateLNameArr[$an],
                        'alternateNameID' => $alternateNameIDArr[$an]];
                    Database2::getInstance()->update($qryUpdate, $sqlParams);
                } else {
                    //insert
                    $alternateNamesArrayValues[] = [
                        'LMRID' => $LMRID,
                        'alternateFName' => $item,
                        'alternateMName' => $alternateMNameArr[$an],
                        'alternateLName' => $alternateLNameArr[$an]];
                }
            }
        }
        if (count($alternateNamesArrayValues) > 0) {
            Database2::getInstance()->insertMulti($insSql, $alternateNamesArrayValues);
        }
        return null;
    }
}