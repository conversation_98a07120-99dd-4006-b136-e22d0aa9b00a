<?php

namespace models\composite\oClient;

use models\Database2;
use models\cypher;
use models\FileStorage;
use models\lendingwise\tblBorrowerEntityDocs;
use models\lendingwise\tblPCClientEntityInfo;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class savePCClientEntityInfo extends strongType
{

    public static ?int $CBEID = null;
    /**
     * Create or update client entity info.
     */
    public static function getReport($ip): int
    {
        $cnt = 0;
        $recordDate = Dates::Timestamp();
        $CID = cypher::myDecryption(Arrays::getArrayValue('encCID', $ip));
        $PCID = cypher::myDecryption(Arrays::getArrayValue('encPCID', $ip));
        $CBEID = cypher::myDecryption(Arrays::getArrayValue('encCBEID', $ip));
        if ($CID == 0) return $cnt;

        $tblPCClientEntityInfo = tblPCClientEntityInfo::Get(['CBEID' => $CBEID]) ?? new tblPCClientEntityInfo();

        $PCClientEntityFieldsArray = [
            'borrowerType'
            , 'entityName'
            , 'entityType'
            , 'ENINo'
            , 'entityAddress'
            , 'entityCity'
            , 'entityState'
            , 'entityZip'
            , 'entityStateOfFormation'
            , 'entityNotes'
            , 'borrowerUnderEntity'
            , 'entityWebsite'
            , 'businessTypeEF'
            , 'organizationalRef'
            , 'tradeName'
            , 'dateOfFormation'
            , 'dateOfOperatingAgreement'
            , 'businessCategory'
            , 'productTypeOrServiceSold'
            , 'terminalOrMakeModel'
            , 'businessPhone'
            , 'startDateAtLocation'
            , 'entityPropertyOwnerShip'
            , 'landlordMortagageContactName'
            , 'landlordMortagagePhone'
            , 'rentMortagagePayment'
            , 'avgMonthlyCreditcardSale'
            , 'avgTotalMonthlySale'
            , 'annualGrossSales'
            , 'annualGrossProfit'
            , 'ordinaryBusinessIncome'
            , 'businessDescription'
            , 'merchantProcessingBankName'
            , 'noOfEmployees'
            , 'crossCorporateGuarantor'
            , 'grossAnnualRevenues'
            , 'grossIncomeLastYear'
            , 'netIncomeLastYear'
            , 'grossIncome2YearsAgo'
            , 'netIncome2YearsAgo'
            , 'valueOfProperty'
            , 'totalDebtOnProperty'
            , 'nameOfLenders'
            , 'entityLocation'
            , 'benBusinessHomeBased'
            , 'benCreditCardPayments'
            , 'benCardProcessorBank'
            , 'benChargeSalesTax'
            , 'benEmployeesPaid'
            , 'benBusinessLocation'
            , 'benHowManyLocation'
            , 'benOtherLocation'
            , 'benBusinessFranchise'
            , 'benNameOfFranchise'
            , 'benPointOfContact'
            , 'benPointOfContactPhone'
            , 'benPointOfContactEmail'
            , 'benWebsiteForFranchise'
            , 'averageBankBalance'
            , 'isBusinessSeasonal'
            , 'isBusinessSeasonalPeakMonth'
            , 'noOfEmployeesAfterLoan'
            , 'naicsCode'
            , 'borLicenseNumber'
            , 'minTimeInBusiness'
            , 'borLicenseExpiration'
            , 'borLicenseIssuance'
            , 'statesLicIssued'
            , 'dbaNames'
            , 'priorBusName'
            , 'entityAddress2'
            , 'businessType'
            , 'hasBusinessBankruptcy'
            , 'recentNSFs'
            , 'businessBankruptcy'
            , 'businessOweTaxesPrior'
            , 'estimatedAmountOwed'
            , 'entityB2B'
            , 'entityB2C'
            , 'entityProduct'
            , 'entityService'
        ];

        if (isset($_REQUEST['dateOfFormation']) && Arrays::getArrayValue('from', $ip) != 'file') {
            $_REQUEST['dateOfFormation'] = Dates::formatDateWithRE($_REQUEST['dateOfFormation'], 'MDY', 'Y-m-d');
        }

        if (isset($_REQUEST['dateOfOperatingAgreement']) && Arrays::getArrayValue('from', $ip) != 'file') {
            $_REQUEST['dateOfOperatingAgreement'] = Dates::formatDateWithRE($_REQUEST['dateOfOperatingAgreement'], 'MDY', 'Y-m-d');
        }

        if (isset($_REQUEST['startDateAtLocation']) && Arrays::getArrayValue('from', $ip) != 'file') {
            $_REQUEST['startDateAtLocation'] = Dates::formatDateWithRE($_REQUEST['startDateAtLocation'], 'MDY', 'Y-m-d');
        }
        if (isset($_REQUEST['borLicenseIssuance']) && Arrays::getArrayValue('from', $ip) != 'file') {
            $_REQUEST['borLicenseIssuance'] = Dates::formatDateWithRE($_REQUEST['borLicenseIssuance'], 'MDY', 'Y-m-d');
        }
        if (isset($_REQUEST['borLicenseExpiration']) && Arrays::getArrayValue('from', $ip) != 'file') {
            $_REQUEST['borLicenseExpiration'] = Dates::formatDateWithRE($_REQUEST['borLicenseExpiration'], 'MDY', 'Y-m-d');
        }

        foreach ($PCClientEntityFieldsArray as $PCClientEntityField) {
            if($PCClientEntityField == 'businessBankruptcy'){
                $postVal = trim($_REQUEST['businessBankruptcyBEN']);
                if ($postVal != '') {
                    $tblPCClientEntityInfo->safeSet($PCClientEntityField, $postVal);
                }
            } elseif (isset ($_REQUEST["$PCClientEntityField"])) {
                $postVal = trim($_REQUEST["$PCClientEntityField"]);
                if ($postVal != '') {
                    $tblPCClientEntityInfo->safeSet($PCClientEntityField, $postVal);
                }
            }
        }

        if (isset($_REQUEST['statesRegisterdIn']) && $_REQUEST['statesRegisterdIn'] != '') {
            $statesRegisterdIn = implode(',', $_REQUEST['statesRegisterdIn']);
            $tblPCClientEntityInfo->statesRegisterdIn = trim($statesRegisterdIn);
        } elseif (isset($_REQUEST['statesRegisterdInHidden'])) {
            $statesRegisterdIn = '';
            $tblPCClientEntityInfo->statesRegisterdIn = trim($statesRegisterdIn);
        }

        if (isset($_REQUEST['operatingStates']) && $_REQUEST['operatingStates'] != '') {
            $operatingStates = implode(',', $_REQUEST['operatingStates']);
            $tblPCClientEntityInfo->operatingStates = trim($operatingStates);
        }
        if (!$CBEID) {
            $tblPCClientEntityInfo->recordDate = $recordDate;
            $tblPCClientEntityInfo->CID = $CID;
            $tblPCClientEntityInfo->PCID = $PCID;
        }

        $tblPCClientEntityInfo->Save();
        $CBEID = $tblPCClientEntityInfo->CBEID;

        $borrowerType = Request::isset('borrowerType') ? Request::GetClean('borrowerType') : '';
        if ($borrowerType != 'Entity') {
            saveMembersOfficersInfo::deleteEntityMembers($CID, $CBEID);
            return $CBEID;
        }

        /* Save members officers info. */
        $ip['CID'] = $CID;
        $ip['CBEID'] = $CBEID;
        self::$CBEID = $CBEID;
        self::uploadEntityDocs();
        $allowNestedEntityMembers = Request::isset('allowNestedEntityMembers') ? Request::GetClean('allowNestedEntityMembers') : 0;
        if ($allowNestedEntityMembers) { //Nested Entity Members - Enabled
            if ($borrowerType == 'Entity') {
                saveMembersOfficersInfo::saveNestedEntityMembers($ip);
            }
        } else { //Nested Entity Members - Disabled | Old Code
            saveMembersOfficersInfo::getReport($ip);
        }

        return $CBEID;
    }

    public static function uploadEntityDocs(): void
    {
        $docs = Request::isset('docs') ? Request::GetClean('docs') : null;
        foreach ($docs as $docType => $eachRequiredDocInfo) {
            foreach ($eachRequiredDocInfo as $requiredDocID => $docInfo) {
                foreach ($docInfo as $docIndex => $eachDoc) {
                    $item = tblBorrowerEntityDocs::Get(['id' => $eachDoc['id']]) ?? new tblBorrowerEntityDocs();
                    $item->CBEID = self::$CBEID ?: null;
                    $item->requiredDocId = $requiredDocID ?: null;
                    $item->expiryDate = $eachDoc['expiryDate'] ? Dates::Datestamp($eachDoc['expiryDate']) : null;
                    $item->displayName = $eachDoc['displayName'] ?: null;

                    if ($_FILES['docs']['name'][$docType][$requiredDocID][$docIndex]['file']) {
                        $fileName = $_FILES['docs']['name'][$docType][$requiredDocID][$docIndex]['file'];
                        $fileType = $_FILES['docs']['type'][$docType][$requiredDocID][$docIndex]['file'];
                        $fileTmpName = $_FILES['docs']['tmp_name'][$docType][$requiredDocID][$docIndex]['file'];
                        $fileError = $_FILES['docs']['error'][$docType][$requiredDocID][$docIndex]['file'];
                        $fileSize = $_FILES['docs']['size'][$docType][$requiredDocID][$docIndex]['file'];
                        if(!$item->displayName) {
                            $item->displayName = $fileName;
                        }
                        $fileName = trim('/' . $item->CBEID . '/' . $docType . '/' . $item->requiredDocId . '/' . $fileName);
                        if ($fileTmpName
                            && $fileName
                            && $fileSize) {
                            $tblFileStorage = FileStorage::moveFile($fileTmpName, $fileName);
                            $item->fileStorageId = $tblFileStorage->id;
                        }
                    }
                    if ($item->fileStorageId) {
                        $item->save();
                    }
                }
            }
        }
    }
}
