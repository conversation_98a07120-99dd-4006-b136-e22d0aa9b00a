<?php

namespace models\composite;

use Exception;
use models\constants\accrualTypes;
use models\composite\proposalFormula\interestLoop;
use models\composite\proposalFormula\principalLoop;
use models\composite\proposalFormula\termsLoop;
use models\constants\typeOfHMLOLoanRequesting;
use models\HMLOLoanTermsCalculation;
use models\servicing\LoanTerms;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class proposalFormula extends strongType
{

    /**
     * @param $lienAmount
     * @param $lienBalanceDue
     * @param $lienEscrowShortage
     * @param $lienProposalPrincipalReductionAmt
     * @return float
     */
    public static function calculateLienProposalBalance($lienAmount, $lienBalanceDue, $lienEscrowShortage, $lienProposalPrincipalReductionAmt): float
    {
        /*** Calculate 1st and 2nd Lien Proposal Balance ***/

        // $lienProposalBalance = 0;
        return (Strings::replaceCommaValues($lienAmount) + Strings::replaceCommaValues($lienBalanceDue) + Strings::replaceCommaValues($lienEscrowShortage)) - Strings::replaceCommaValues($lienProposalPrincipalReductionAmt);
    }


    /**
     * @param $lienPayment
     * @param $lienProposalRate
     * @param $lienProposalTerms
     * @param $taxes1
     * @param $insurance1
     * @param $HOAFees1
     * @param $mortgageInsurance1
     * @return float
     */
    public static function calculatePaymentPITIA($lienPayment, $lienProposalRate, $lienProposalTerms, $taxes1, $insurance1, $HOAFees1, $mortgageInsurance1): float
    {
        /*** Calculate 1st and 2nd Lien and Proposal Monthly Payment PITIA ***/

        // $lienPaymentPITIA = 0;
        $lienPaymentPITIA = self::calculatePaymentValue1($lienPayment, $lienProposalRate, $lienProposalTerms);
        $lienPaymentPITIA = Strings::replaceCommaValues($lienPaymentPITIA) + Strings::replaceCommaValues($taxes1);
        $lienPaymentPITIA += Strings::replaceCommaValues($insurance1) + Strings::replaceCommaValues($HOAFees1);
        $lienPaymentPITIA += Strings::replaceCommaValues($mortgageInsurance1);
        return round($lienPaymentPITIA, 2);
    }

    /**
     * @param $lien1PaymentPITIA
     * @param $lien2PaymentPITIA
     * @param $grossMonthlyHouseHoldIncome
     * @return float
     */
    public static function calculateDTI($lien1PaymentPITIA, $lien2PaymentPITIA, $grossMonthlyHouseHoldIncome): float
    {
        /*** Calculate Current and Proposal Housing DTI ***/

        $lien1DTI = 0;
        // $tempValue = 0;
        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues($grossMonthlyHouseHoldIncome);

        if (($grossMonthlyHouseHoldIncome == '') || ($grossMonthlyHouseHoldIncome == NULL)) {
            $grossMonthlyHouseHoldIncome = 0;
        }
        if ($grossMonthlyHouseHoldIncome > 0) {
            $lien1DTI = Strings::replaceCommaValues($lien1PaymentPITIA) + Strings::replaceCommaValues($lien2PaymentPITIA);
            $lien1DTI = $lien1DTI / $grossMonthlyHouseHoldIncome;
            $lien1DTI = $lien1DTI * 100;
        }
        return round($lien1DTI, 2);
    }

    /*** Calculate Proposal New Monthly Payment ***/


    public static function calculateProposalNewMonthlyPayment($lien1ProposalPaymentPITIA, $lien2ProposalPaymentPITIA, $taxes1, $insurance1, $HOAFees1, $mortgageInsurance1): float
    {
//    $proposalMonthlyPayment = 0;
//    $tempValue = 0;
        $tempValue = Strings::replaceCommaValues($taxes1) + Strings::replaceCommaValues($insurance1);
        $tempValue += Strings::replaceCommaValues($HOAFees1) + Strings::replaceCommaValues($mortgageInsurance1);
        $proposalMonthlyPayment = Strings::replaceCommaValues($lien1ProposalPaymentPITIA) + Strings::replaceCommaValues($lien2ProposalPaymentPITIA);
        return $proposalMonthlyPayment - $tempValue;
    }


    /**
     * @param $totalHouseHoldExpenses
     * @param $primaryMortgage
     * @param $taxes
     * @param $insurance
     * @param $HOAFees
     * @param $lien2Payment
     * @param $mortgageInsurance1
     * @return float
     */
    public static function calculateNonMortgageTotalMonthlyExpenses($totalHouseHoldExpenses, $primaryMortgage, $taxes, $insurance, $HOAFees, $lien2Payment, $mortgageInsurance1): float
    {
        /*** Calculate Non Mortgage Total Monthly Expenses ***/

//    $nonMtgTotalHouseHoldExpenses = 0;
//    $tempValue = 0;
        $tempValue = Strings::replaceCommaValues($primaryMortgage) + Strings::replaceCommaValues($taxes);
        $tempValue += Strings::replaceCommaValues($insurance) + Strings::replaceCommaValues($HOAFees) + Strings::replaceCommaValues($mortgageInsurance1);
        $tempValue += Strings::replaceCommaValues($lien2Payment);
        return Strings::replaceCommaValues($totalHouseHoldExpenses) - $tempValue;
    }


    /**
     * @param $taxes1
     * @param $insurance1
     * @param $HOAFees1
     * @param $mortgageInsurance1
     * @param int $floodInsurance1
     * @return float
     */
    public static function calculateHousingTaxesInsurance($taxes1, $insurance1, $HOAFees1, $mortgageInsurance1, int $floodInsurance1 = 0): float
    {
        /*** Calculate Current and Proposed Housing Taxes and Insurance ***/

        //$housingTaxesInsurance = 0;
        $housingTaxesInsurance = Strings::replaceCommaValues($taxes1) + Strings::replaceCommaValues($insurance1);
        $housingTaxesInsurance += Strings::replaceCommaValues($HOAFees1) + Strings::replaceCommaValues($mortgageInsurance1);
        $housingTaxesInsurance += Strings::replaceCommaValues($floodInsurance1);
        return $housingTaxesInsurance;
    }


    /**
     * @param $lien1ProposalPaymentPITIA
     * @param $lien2ProposalPaymentPITIA
     * @param $nonMtgProposalTotalHouseHoldExpenses
     * @return float
     */
    public static function calculateTotalMonthlyMortgageExpenses($lien1ProposalPaymentPITIA, $lien2ProposalPaymentPITIA, $nonMtgProposalTotalHouseHoldExpenses): float
    {
        /*** Calculate Total Monthly (Including Mortgage) Expenses ***/

        //$proposalTotalMonthlyMortgageExpenses = 0;
        $proposalTotalMonthlyMortgageExpenses = Strings::replaceCommaValues($lien1ProposalPaymentPITIA);
        $proposalTotalMonthlyMortgageExpenses += Strings::replaceCommaValues($lien2ProposalPaymentPITIA);
        $proposalTotalMonthlyMortgageExpenses += Strings::replaceCommaValues($nonMtgProposalTotalHouseHoldExpenses);
        return $proposalTotalMonthlyMortgageExpenses;
    }


    /**
     * @param $netMonthlyIncome1
     * @param $proposalTotalMonthlyMortgageExpenses
     * @return float
     */
    public static function calculateMonthlyNetCashFlow($netMonthlyIncome1, $proposalTotalMonthlyMortgageExpenses): float
    {
        /*** Calculate Current and Proposed Monthly Net Cash Flow ***/

        //$monthlyNetCashFlow = 0;
        $monthlyNetCashFlow = Strings::replaceCommaValues($netMonthlyIncome1);
        return $monthlyNetCashFlow - Strings::replaceCommaValues($proposalTotalMonthlyMortgageExpenses);
    }

    /**
     * @param $monthlyNetCashFlow
     * @param $netMonthlyIncome1
     * @return float
     */
    public static function calculateSurplusDeficitIncome($monthlyNetCashFlow, $netMonthlyIncome1): float
    {
        /*** Calculate Current and Proposed Surplus/deficit % of Income ***/

        $surplusIncome = 0;
        $netMonthlyIncome1 = Strings::replaceCommaValues($netMonthlyIncome1);
        if (($netMonthlyIncome1 == '') || ($netMonthlyIncome1 == NULL)) {
            $netMonthlyIncome1 = 0;
        }
        if ($netMonthlyIncome1 > 0) {
            $surplusIncome = Strings::replaceCommaValues($monthlyNetCashFlow) / Strings::replaceCommaValues($netMonthlyIncome1);
            $surplusIncome = $surplusIncome * 100;
        }
        return round($surplusIncome, 2);
    }

    /**
     * @param $lien1Amount
     * @param $lien2Amount
     * @return float
     */
    public static function calculateTotalMortgageDebt($lien1Amount, $lien2Amount): float
    {
        /*** Calculate Total Mortgage Debt ***/

        //$totalMortgageDebt = 0;
        return Strings::replaceCommaValues($lien1Amount) + Strings::replaceCommaValues($lien2Amount);
    }

    /**
     * @param $homeValue
     * @param $totalMortgageDebt
     * @return float
     */
    public static function calculateProposalEquitySurplus($homeValue, $totalMortgageDebt): float
    {
        /*** Calculate Equity Surplus/Deficit ***/

        //$proposalEquitySurplus = 0;
        return Strings::replaceCommaValues($homeValue) - Strings::replaceCommaValues($totalMortgageDebt);
    }


    /*** Calculate Current LTV ***/

    public static function calculateProposalCurrentLTV($totalMortgageDebt, $homeValue): float
    {
        $proposedCurrentLTV = 0;
        $homeValue = Strings::replaceCommaValues($homeValue);
        if (($homeValue == '') || ($homeValue == NULL)) {
            $homeValue = 0;
        }
        if ($homeValue > 0) {
            $proposedCurrentLTV = Strings::replaceCommaValues($totalMortgageDebt) / Strings::replaceCommaValues($homeValue);
            $proposedCurrentLTV = $proposedCurrentLTV * 100;
        }
        return round($proposedCurrentLTV, 2);
    }

    /*** Calculate Est. months in arrears at time of REO sale ***/

    public static function calculateMortgagePmtSaleArrears($lien1PaymentPITIA, $REOArrearsMonths)
    {
        //$mortgagePmtSaleArrears = 0;
        return Strings::replaceCommaValues($lien1PaymentPITIA) * Strings::replaceCommaValues($REOArrearsMonths);

    }

    /*** Calculate Total Estimated Foreclosure Costs ***/

    public static function calculateTotalEstimateCost($mortgagePmtSaleArrears, $attorneyFees, $securePropertyCost, $maintenanceCost): float
    {
        //$proposalTotalEstimatedCost = 0;
        $proposalTotalEstimatedCost = Strings::replaceCommaValues($mortgagePmtSaleArrears) + Strings::replaceCommaValues($attorneyFees);
        $proposalTotalEstimatedCost += Strings::replaceCommaValues($securePropertyCost) + Strings::replaceCommaValues($maintenanceCost);
        return $proposalTotalEstimatedCost;
    }


    /*** Calculate Foreclosure Estimated Sales Price (75% of CMV) ***/

    public static function calculateForeclosureEstimate($homeValue): float
    {
        //$proposalForeclosureEstimate = 0;
        $proposalForeclosureEstimate = Strings::replaceCommaValues($homeValue) * 0.75;
        return round($proposalForeclosureEstimate, 2);
    }

    /*** Calculate Less: Real Estate Commissions @ 5% ***/

    public static function calculateEstimateCommission($proposalForeclosureEstimate, $realEstateCommissions)
    {
        //$proposalRealEstateCommission = 0;
        $realEstateCommissions = Strings::replaceCommaValues($realEstateCommissions);
        $realEstateCommissions = $realEstateCommissions / 100;
        return Strings::replaceCommaValues($proposalForeclosureEstimate) * $realEstateCommissions;
    }

    /*** Calculate Total Sale Proceeds to Lender ***/

    public static function calculateTotalSaleProceedsToLender($proposalForeclosureEstimate, $proposalEstimatedForeclosureCost, $proposalRealEstateCommission): float
    {
        //$proposalTotalSaleProceedsToLender = 0;
        $proposalTotalSaleProceedsToLender = Strings::replaceCommaValues($proposalForeclosureEstimate);
        $proposalTotalSaleProceedsToLender = $proposalTotalSaleProceedsToLender - Strings::replaceCommaValues($proposalEstimatedForeclosureCost);
        return $proposalTotalSaleProceedsToLender - Strings::replaceCommaValues($proposalRealEstateCommission);
    }

    /*** Calculate Estimated Lender Loss from Foreclosure ***/

    public static function calculateEstimateLenderLoss($proposalTotalSaleProceeds, $lien1Amount, $lienAmount2): float
    {
        //$proposalEstimateLenderLoss = 0;
        //$tempValue = 0;
        $tempValue = Strings::replaceCommaValues($lien1Amount) + Strings::replaceCommaValues($lienAmount2);
        return Strings::replaceCommaValues($proposalTotalSaleProceeds) - $tempValue;
    }

    /*** Calculate Lien1 Total PITIA ***/

    public static function calculateLien1TotalPITIA($lien1ProposalPaymentPITIA, $lien2Payment): float
    {
        //$lien1TotalPITIA = 0;
        return Strings::replaceCommaValues($lien1ProposalPaymentPITIA) + Strings::replaceCommaValues($lien2Payment);
    }

    /*** Calculate Total PITIA ***/

    public static function calculateTotalPITIA($lien1TotalPITIA, $lien2TotalPITIA): float
    {
        //$totalPITIA = 0;
        return Strings::replaceCommaValues($lien1TotalPITIA) + Strings::replaceCommaValues($lien2TotalPITIA);
    }

    /*** Calculate Total DTI ***/

    public static function calculateTotalDTI($grossMonthlyHouseHoldIncome, $lien1ProposalPaymentPITIA, $lien2ProposalPaymentPITIA)
    {
        $totalPITIA = 0;
        //$tempValue = 0;
        $tempValue = Strings::replaceCommaValues($lien1ProposalPaymentPITIA) + Strings::replaceCommaValues($lien2ProposalPaymentPITIA);
        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues($grossMonthlyHouseHoldIncome);
        if ($tempValue > 0) {
            $totalPITIA = $grossMonthlyHouseHoldIncome / $tempValue * 100;
        }
        return $totalPITIA;
    }

    /*** Calculate Estimated Lender Loss from Foreclosure ***/

    public static function calculateGrossMonthlyHouseHoldIncome($grossIncome1, $netMonthlyIncome1, $totalHouseHoldIncome, $commissionOrBonus1, $overtime1, $tipsMiscIncome1): float
    {
        //$grossMonthlyHouseHoldIncome = 0;
        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues($grossIncome1) + Strings::replaceCommaValues($totalHouseHoldIncome);
        $grossMonthlyHouseHoldIncome += Strings::replaceCommaValues($commissionOrBonus1) + Strings::replaceCommaValues($overtime1);
        $grossMonthlyHouseHoldIncome += Strings::replaceCommaValues($tipsMiscIncome1);
        return $grossMonthlyHouseHoldIncome - Strings::replaceCommaValues($netMonthlyIncome1);
    }

    /*** Calculate 1st and 2nd Lien and Proposal Monthly Payment PITIA ***/

    public static function calculatePaymentPI($lienPayment, $taxes1, $insurance1, $HOAFees1, $mortgageInsurance1, $floodInsurance = 0): float
    {
        //$lienPaymentPITIA = 0;
        $lienPaymentPITIA = Strings::replaceCommaValues($lienPayment) + Strings::replaceCommaValues($taxes1);
        $lienPaymentPITIA += Strings::replaceCommaValues($insurance1) + Strings::replaceCommaValues($HOAFees1);
        $lienPaymentPITIA += Strings::replaceCommaValues($mortgageInsurance1) + Strings::replaceCommaValues($floodInsurance);
        //        $lienPaymentPITIA = '20';
        return round($lienPaymentPITIA, 2);
    }

    /*** Absolute value of the Given Number ***/

    public static function convertToAbsoluteValueWithNewFormat($inputValue): float
    {
        $inputValue = trim($inputValue);
        $inputValue = Strings::replaceCommaValues($inputValue);
        $inputValue = abs($inputValue);
        return round($inputValue, 3);
    }

    /*** Absolute value of the Given Number ***/

    public static function convertToAbsoluteValueWithNewFormatForPercent($inputValue): string
    {
        $tempValue = trim($inputValue);
        $tempValue = Strings::replaceCommaValues($tempValue);
        $inputValue = abs($tempValue);
        $inputValue = number_format($inputValue, 3);
        if ($tempValue < 0) {
            $inputValue = "<span style=\"color:#ff0000\">(" . $inputValue . '%)</span>';
        } else {
            $inputValue = $inputValue . '%';
        }
        return $inputValue;
    }


    /*** Absolute value of the Given Number ***/

    public static function convertToAbsoluteValue($inputValue): float
    {
        $inputValue = trim($inputValue);
        $inputValue = Strings::replaceCommaValues($inputValue);
        $inputValue = abs($inputValue);
        return round($inputValue, 2);
    }

    /*** Absolute value of the Given Number For PDF ***/

    public static function convertToAbsoluteValueForPDF($inputValue): string
    {
        $tempValue = trim($inputValue);
        $tempValue = Strings::replaceCommaValues($tempValue);
        $inputValue = abs($tempValue);
        $inputValue = number_format($inputValue, 2);
        if ($tempValue < 0) {
            $inputValue = "<span style=\"color:#ff0000\">($" . $inputValue . ')</span>';
        } else {
            $inputValue = '$' . $inputValue;
        }
        return $inputValue;
    }

    /*** Absolute value of the Given Number For PDF ***/

    public static function convertToAbsoluteValueForDollar($inputValue): string
    {
        $tempValue = trim($inputValue);
        $tempValue = Strings::replaceCommaValues($tempValue);
        $inputValue = abs($tempValue);
        $inputValue = number_format($inputValue, 2);
        if ($tempValue < 0) {
            $inputValue = "<span style=\"color:#ff0000\">($" . $inputValue . ')</span>';
        } else {
            $inputValue = '$ ' . $inputValue;
        }
        return $inputValue;
    }

    /*** Absolute value of the Given Number For PDF ***/

    public static function convertToAbsoluteValueForPercent($inputValue): string
    {
        $tempValue = trim($inputValue);
        $tempValue = Strings::replaceCommaValues($tempValue);
        $inputValue = abs($tempValue);
        $inputValue = number_format($inputValue, 2);
        if ($tempValue < 0) {
            $inputValue = "<span style=\"color:#ff0000\">(" . $inputValue . '%)</span>';
        } else {
            $inputValue = $inputValue . '%';
        }
        return $inputValue;
    }

    /*** Calculate Current and Proposal Back End DTI ***/

    public static function calculateBackEndDTI($totalMonthlyMortgageExpenses, $grossMonthlyHouseHoldIncome): float
    {
        $lien1BackEndDTI = 0;
        //$tempValue = 0;
        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues($grossMonthlyHouseHoldIncome);

        if (($grossMonthlyHouseHoldIncome == '') || ($grossMonthlyHouseHoldIncome == NULL)) {
            $grossMonthlyHouseHoldIncome = 0;
        }

        if ($grossMonthlyHouseHoldIncome > 0) {
            $lien1BackEndDTI = $totalMonthlyMortgageExpenses / $grossMonthlyHouseHoldIncome;
            $lien1BackEndDTI = $lien1BackEndDTI * 100;
        }
        return round($lien1BackEndDTI, 2);
    }

    /*** Calculate Current and Proposal Back End DTI ***/

    public static function calculateNewProposedLTV($lien1ProposalBalance, $lien2ProposalBalance, $homeValue): float
    {
        $lien1LTV = 0;
        $homeValue = Strings::replaceCommaValues($homeValue);

        if (($homeValue == '') || ($homeValue == NULL)) {
            $homeValue = 0;
        }

        if ($homeValue > 0) {
            $lien1LTV = Strings::replaceCommaValues($lien1ProposalBalance) + Strings::replaceCommaValues($lien2ProposalBalance);
            $lien1LTV = $lien1LTV / $homeValue;
            $lien1LTV = $lien1LTV * 100;
        }
        return round($lien1LTV, 2);
    }

    /**
     * @param $loanAmt
     * @param $rate
     * @param $term
     * @return float
     */
    public static function calculatePaymentValue1($loanAmt, $rate, $term): float
    {
        $amt = 0;
        $terms = 0;
        $loanAmt = Strings::replaceCommaValues($loanAmt);
        $rate = Strings::replaceCommaValues($rate);

        if ($term == 'Not Sure') {
            return 0;
        }

        if ($term == LoanTerms::INTEREST_ONLY) {
            return round(($loanAmt * $rate / 1200), 2);
        }

        if ($term) {
            ///$pos = '';
            $pos = strpos($term, ' ');
            $amort = intval(substr($term, 0, $pos));
            if ($amort <= 0) {
                $amort = 1;
            }
            if ($amort > 0) {
                $terms = $amort * 12;
            }
        }

        if ($loanAmt > 0 && $rate > 0 && $terms > 0) {
            $intr = $rate / 1200;
            $amt = round($loanAmt * $intr / (1 - (pow(1 / (1 + $intr), $terms))), 2);
//                Log::Insert(["round($loanAmt * $intr / (1 - (pow(1 / (1 + $intr), $terms))), 2)", $intr / (1 - (pow(1 / (1 + $intr), $terms)))]);
        }

        return $amt;
    }

    /**
     * @param $loanAmt
     * @param $rate
     * @param $term
     * @return float
     */
    public static function calculateHMLOPaymentValue_old($loanAmt, $rate, $term)
    {
        $amt = $terms = 0;
        $loanAmt = Strings::replaceCommaValues($loanAmt);
        $rate = Strings::replaceCommaValues($rate);
        if ($term == 'Not Sure') {
            $amt = '';
        } elseif ($term == LoanTerms::INTEREST_ONLY) {
//	    	$amt = calculateInterestValue1($loanAmt, $rate, 12);
            $amt = round((($loanAmt * $rate) / 1200), 2);
        } else {
            if ($term != '') {
                // $pos = '';
                $pos = strpos($term, ' ');
                $amort = substr($term, 0, $pos);
                if ($amort <= 0) {
                    $amort = 1;
                }
                if ($amort > 0) {
                    $terms = $amort * 12;
                }
            }
            if ($loanAmt > 0 && $rate > 0 && $terms > 0) {
                $intr = $rate / 1200;
                $amt = round($loanAmt * $intr / (1 - (pow(1 / (1 + $intr), $terms))), 2);
            }
        }
        return $amt;
    }

    public static function calculateHMLOPaymentValue($totalLoanAmount, $interestRate, $term, $purchaseCloseDate, $accrualType): float
    {
        $amt = $terms = 0;
        $loanAmt = Strings::replaceCommaValues($totalLoanAmount);
        $rate = Strings::replaceCommaValues($interestRate);
        if ($term == 'Not Sure') {
            doNothing();
        } elseif ($term == LoanTerms::INTEREST_ONLY) {
            $outputArray = self::getAccrualTypeBaseValues($purchaseCloseDate, $accrualType);

            $_monthlyPaymentPart1 = ($rate / 100 / $outputArray->yearDays) * $outputArray->monthDays;
            $amt = round($_monthlyPaymentPart1 * $loanAmt, 2);
        } else {
            if ($term) {
                $pos = strpos($term, ' ');
                $amort = (int)substr($term, 0, $pos);
                if ($amort <= 0) {
                    $amort = 1;
                }
                if ($amort > 0) {
                    $terms = $amort * 12;
                }
            }
            $amt = self::calculateMonthlyPayment($loanAmt, $rate, $terms);
            $amt = round($amt, 2);
        }
        return $amt;
    }


    public static function calculateMonthlyPayment($loanAmt, $rate, $termInMonths): ?string
    {
        $amt = 0;
        $interest = 0;
        if ($loanAmt > 0 &&
            $rate > 0 &&
            $termInMonths > 0) {
            $interest = $rate / 1200;
            $amt = $loanAmt * $interest / (1 - (pow(1 / (1 + $interest), $termInMonths)));
        }
        $amt = round($amt, 2);
        HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltipWithValues = " $loanAmt * ($rate / 1200) / (1 - (pow(1 / (1 + ($rate / 1200)), $termInMonths)))  = $ ".Currency::formatDollarAmountWithDecimal($amt);
        return $amt;
    }


    /*** Calculate Total PITIA ***/

    public static function calculateTotalPaymentPITIA($lien1PaymentPITIA, $lien2PaymentPI, $totalHousingTaxesInsurance): float
    {
        // $totalPaymentPITIA = 0;
        $totalPaymentPITIA = Strings::replaceCommaValues($lien1PaymentPITIA) + Strings::replaceCommaValues($lien2PaymentPI);
        return $totalPaymentPITIA - Strings::replaceCommaValues($totalHousingTaxesInsurance);
    }


    /*** Calculate Short Sale Offer Analysis ***/

    public static function calculateLiquidationValue($homeValue, $SS_HomePriceForecastedDepreciation, $SS_REOStigmaDiscount, $SS_CureRate)
    {
//    $liquidationValue = 0;
//    $tempValue1 = 0;
//    $tempValue2 = 0;

        $SS_HomePriceForecastedDepreciation = Strings::replaceCommaValues($SS_HomePriceForecastedDepreciation);
        $SS_REOStigmaDiscount = Strings::replaceCommaValues($SS_REOStigmaDiscount);
        $SS_CureRate = Strings::replaceCommaValues($SS_CureRate);

        if (($SS_HomePriceForecastedDepreciation == '') || ($SS_HomePriceForecastedDepreciation == NULL)) {
            $SS_HomePriceForecastedDepreciation = 0;
        }
        if (($SS_REOStigmaDiscount == '') || ($SS_REOStigmaDiscount == NULL)) {
            $SS_REOStigmaDiscount = 0;
        }
        if (($SS_CureRate == '') || ($SS_CureRate == NULL)) {
            $SS_CureRate = 0;
        }

        if ($SS_HomePriceForecastedDepreciation > 0) {
            $SS_HomePriceForecastedDepreciation = $SS_HomePriceForecastedDepreciation / 100;
        }
        if ($SS_REOStigmaDiscount > 0) {
            $SS_REOStigmaDiscount = $SS_REOStigmaDiscount / 100;
        }
        if ($SS_CureRate > 0) {
            $SS_CureRate = $SS_CureRate / 100;
        }

        $tempValue1 = 1 - Strings::replaceCommaValues($SS_HomePriceForecastedDepreciation)
            - Strings::replaceCommaValues($SS_REOStigmaDiscount);
        $tempValue2 = Strings::replaceCommaValues($SS_CureRate) * Strings::replaceCommaValues($homeValue);
        return (Strings::replaceCommaValues($homeValue) * $tempValue1) - $tempValue2;
    }


    /**
     * @param $SS_LiquidationValue
     * @param $SS_PIAEF
     * @return float
     */
    public static function calculateExpectedREODispositionValue($SS_LiquidationValue, $SS_PIAEF): float
    {
        // $SS_REODispositionValue = 0;
        return Strings::replaceCommaValues($SS_LiquidationValue)
            - Strings::replaceCommaValues($SS_PIAEF);
    }

    /**
     * @param $SS_CureRate
     * @param $lien1OriginalBalance
     * @param $SS_REODispositionValue
     * @return float|int
     */
    public static function calculateREOValue($SS_CureRate, $lien1OriginalBalance, $SS_REODispositionValue)
    {
//    $SS_REOValue = 0;
//    $tempValue = 0;

        $SS_CureRate = Strings::replaceCommaValues($SS_CureRate);

        if (($SS_CureRate == '') || ($SS_CureRate == NULL)) {
            $SS_CureRate = 0;
        }
        if ($SS_CureRate > 0) {
            $SS_CureRate = $SS_CureRate / 100;
        }

        $tempValue = (1 - Strings::replaceCommaValues($SS_CureRate))
            * Strings::replaceCommaValues($SS_REODispositionValue);
        return Strings::replaceCommaValues($SS_CureRate)
            * Strings::replaceCommaValues($lien1OriginalBalance)
            + Strings::replaceCommaValues($tempValue);
    }

    /**
     * @param $SS_REOValue
     * @param $SS_Offer
     * @return float
     */
    public static function calculateInvestorBenefit($SS_REOValue, $SS_Offer): float
    {
        //$SS_InvestorBenefit = 0;
        return Strings::replaceCommaValues($SS_Offer)
            - Strings::replaceCommaValues($SS_REOValue);
    }

    /*** Calculate 1st and 2nd Lien Proposal Balance ***/

    public static function calculateMonthlyPaymentReduction($currentMonthlyPayment, $proposalMonthlyPayment): float
    {
        //$monthlyPaymentReduction = 0;
        $tempValue = 0;

        $currentMonthlyPayment = Strings::replaceCommaValues($currentMonthlyPayment);

        if ($currentMonthlyPayment > 0) {
            $tempValue = Strings::replaceCommaValues($proposalMonthlyPayment) / $currentMonthlyPayment;
        }

        $monthlyPaymentReduction = 1 - ($tempValue);
        $monthlyPaymentReduction = $monthlyPaymentReduction * 100;

        return round($monthlyPaymentReduction, 2);
    }

    /*** Calculate Proposal Housing DTI ***/

    public static function calculateProposedHousingDTI($inArray): float
    {
//    $lien1PaymentPITIA = 0;
//    $lien2PaymentPITIA = 0;
//    $grossMonthlyHouseHoldIncome = 0;
//    $unemployment = 0;
//    $foodStampWelfare = 0;
        $lien1DTI = 0;
        // $tempValue = 0;

        $lien1PaymentPITIA = trim($inArray['lien1PaymentPITIA']);
        $lien2PaymentPITIA = trim($inArray['lien2PaymentPITIA']);
        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues(trim($inArray['grossMonthlyHouseHoldIncome']));
        $unemployment = Strings::replaceCommaValues(trim($inArray['unemployment']));
        // $foodStampWelfare = Strings::replaceCommaValues(trim($inArray['foodStampWelfare']));

        if (($grossMonthlyHouseHoldIncome == '') || ($grossMonthlyHouseHoldIncome == NULL)) {
            $grossMonthlyHouseHoldIncome = 0;
        }

        if (($unemployment == '') || ($unemployment == NULL)) {
            $unemployment = 0;
        }

//    if (($foodStampWelfare == "") || ($foodStampWelfare == NULL)) {
//        $foodStampWelfare = 0;
//    }
        if ($grossMonthlyHouseHoldIncome > 0) {
//            $grossMonthlyHouseHoldIncome = $grossMonthlyHouseHoldIncome - ($unemployment + $foodStampWelfare);
            $grossMonthlyHouseHoldIncome = $grossMonthlyHouseHoldIncome - ($unemployment); /* Include Food Stamp Welfare income in proposed DTI Calculations from 2014-11-13 */
        }

        if (($grossMonthlyHouseHoldIncome == '') || ($grossMonthlyHouseHoldIncome == NULL)) {
            $grossMonthlyHouseHoldIncome = 0;
        }

        if ($grossMonthlyHouseHoldIncome > 0) {
            $lien1DTI = Strings::replaceCommaValues($lien1PaymentPITIA) + Strings::replaceCommaValues($lien2PaymentPITIA);
            $lien1DTI = $lien1DTI / $grossMonthlyHouseHoldIncome;
            $lien1DTI = $lien1DTI * 100;
        }
        return round($lien1DTI, 2);
    }

    /*** Calculate New Gross Monthly HouseHold Income ***/

    public static function calculateNewGrossMonthlyHouseHoldIncome($inArray): float
    {
//    $grossMonthlyHouseHoldIncome = 0;
//    $totalGrossIncome = 0;
//    $otherHouseHoldMonthlyGrossIncome = 0;

        $totalGrossIncome = $inArray['totalGrossIncome'];
        $otherHouseHoldMonthlyGrossIncome = $inArray['otherMonthlyIncome'];

        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues($totalGrossIncome);
        $grossMonthlyHouseHoldIncome += Strings::replaceCommaValues($otherHouseHoldMonthlyGrossIncome);

        return $grossMonthlyHouseHoldIncome;
    }

    /*** Calculate Non Mortgage Total Monthly Expenses ***/

    public static function calculateNewNonMortgageTotalMonthlyExpenses($inArray): float
    {

        $totalHouseHoldExpenses = 0;
        $primaryMortgage = 0;
        $taxes = 0;
        $insurance = 0;
        $HOAFees = 0;
        $lien2Payment = 0;
        $mortgageInsurance1 = 0;
        $floodInsurance1 = 0;

        if (array_key_exists('totalHouseHoldExpenses', $inArray)) {
            $totalHouseHoldExpenses = $inArray['totalHouseHoldExpenses'];
        }
        if (array_key_exists('primaryMortgage1', $inArray)) {
            $primaryMortgage = $inArray['primaryMortgage1'];
        }
        if (array_key_exists('taxes1', $inArray)) {
            $taxes = $inArray['taxes1'];
        }
        if (array_key_exists('insurance1', $inArray)) {
            $insurance = $inArray['insurance1'];
        }
        if (array_key_exists('HOAFees1', $inArray)) {
            $HOAFees = $inArray['HOAFees1'];
        }
        if (array_key_exists('lien2Payment', $inArray)) {
            $lien2Payment = $inArray['lien2Payment'];
        }
        if (array_key_exists('mortgageInsurance1', $inArray)) {
            $mortgageInsurance1 = $inArray['mortgageInsurance1'];
        }
        if (array_key_exists('floodInsurance1', $inArray)) {
            $floodInsurance1 = $inArray['floodInsurance1'];
        }

        //$nonMtgTotalHouseHoldExpenses = 0;
        //$tempValue = 0;
        $tempValue = Strings::replaceCommaValues($primaryMortgage) + Strings::replaceCommaValues($taxes);
        $tempValue += Strings::replaceCommaValues($insurance) + Strings::replaceCommaValues($HOAFees) + Strings::replaceCommaValues($mortgageInsurance1);
        $tempValue += Strings::replaceCommaValues($lien2Payment);
        $tempValue += Strings::replaceCommaValues($floodInsurance1);

        return Strings::replaceCommaValues($totalHouseHoldExpenses) - $tempValue;
    }


    /*** Calculate 1st and 2nd Lien and Proposal Monthly Payment PITIA ***/

    public static function calculateHAMPT1PITIA($lienPayment, $lienProposalRate, $lienProposalTerms, $taxes1, $insurance1, $HOAFees1, $mortgageInsurance1, $floodInsurance1): float
    {
        return round(self::calculatePaymentValue1($lienPayment, $lienProposalRate, $lienProposalTerms)
            + Strings::replaceCommaValues($taxes1)
            + Strings::replaceCommaValues($insurance1)
            + Strings::replaceCommaValues($HOAFees1)
            + Strings::replaceCommaValues($mortgageInsurance1)
            + Strings::replaceCommaValues($floodInsurance1)
            , 2);
    }

    public static function calculateHAMPT1PITIA_Expenses($taxes1, $insurance1, $HOAFees1, $mortgageInsurance1, $floodInsurance1): float
    {
        return round(
            Strings::replaceCommaValues($taxes1)
            + Strings::replaceCommaValues($insurance1)
            + Strings::replaceCommaValues($HOAFees1)
            + Strings::replaceCommaValues($mortgageInsurance1)
            + Strings::replaceCommaValues($floodInsurance1)
            , 2);
    }

    /*** Calculate Current and Proposal Housing DTI ***/

    public static function calculateHAMPT1DTI($lien1PaymentPITIA, $grossMonthlyHouseHoldIncome, $unemployment): float
    {
        $lien1DTI = 0;

        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues($grossMonthlyHouseHoldIncome);

        if (!$grossMonthlyHouseHoldIncome) {
            $grossMonthlyHouseHoldIncome = 0;
        }

        if ($grossMonthlyHouseHoldIncome > 0) {
            /* Include Food Stamp Welfare income in proposed DTI Calculations from 2014-11-13 */
            $grossMonthlyHouseHoldIncome = $grossMonthlyHouseHoldIncome - Strings::replaceCommaValues($unemployment);
            if ($grossMonthlyHouseHoldIncome > 0) {
                $lien1DTI = Strings::replaceCommaValues($lien1PaymentPITIA) / $grossMonthlyHouseHoldIncome;
            }
            $lien1DTI *= 100;
        }
        return round($lien1DTI, 2);
    }

    /*
    * Calculate DTI for HAMP Tier1 PRA Approach.
    */
    /**
     * @param $ip
     * @return array
     */
    public static function HAMPT1PRA($ip): array
    {
        $DTIReduceTo = 31;
        $minInterestRate = 2.00;
        $interestRateReducedBy = 0.125;
        $amountReducedBy = 100;
//    $terms = 360;
//
//    $principalLoop = true;
//    $termsLoop = true;
//    $interestLoop = true;
        $i = 0;
//    $forbearanceLoop = true;


        $result = [];
//    $newHAMPT1Balance = 0;
//    $newHAMPT1PrincipalReductionAmt = 0;
//    $newHAMPT1Rate = 0;
//    $newHAMPT1Rate = 0;
//    $newHAMPT1Terms = 0;
//    $newHAMPT1Payment = 0;
//    $newHAMPT1DTI = 0;
//    $principalBalance = 0;
//    $amountDue = 0;
//    $rate = 0;
//    $grossMonthlyHouseHoldIncome = 0;
//    $DTI = 0;
        $principalReductionAmt = 0;
        $lien1PaymentPITIA = 0;
//    $taxes = 0;
//    $insurance = 0;
//    $HOAFees = 0;
//    $mortgageInsurance = 0;
//    $floodInsurance = 0;
//    $unemployment = 0;
//    $foodStampWelfare = 0;
//    $homeValue = 0;
//    $capitalizedLoanAmt = 0;
        $MTMLTV = 0;
//    $interestBearingUPB = 0;
//    $targetPayment = 0;
//    $newTargetPayment = 0;
        $principalForbearance = 0;
//    $orgInterestBearingUPB = 0;
//    $escrowAdvances = 0;
//    $model = 'PRA';
//    $tempRate = 0;
//    $tempBalance = 0;
        $lien1PaymentPI = 0;
        $lien1DTI = 0;
//    $tempBalance1 = 0;
        $terms = 1;
//    $term = '';
//    $tempTerms = 1;

        $principalBalance = trim($ip['principalBalance']);
        $amountDue = trim($ip['amountDue']);
        $rate = trim($ip['rate']);
        $grossMonthlyHouseHoldIncome = trim($ip['grossMonthlyHouseHoldIncome']);
        $DTI = trim($ip['DTI']);
        $taxes = trim($ip['taxes']);
        $insurance = trim($ip['insurance']);
        $HOAFees = trim($ip['HOAFees']);
        $mortgageInsurance = trim($ip['mortgageInsurance']);
        $floodInsurance = trim($ip['floodInsurance']);
        $unemployment = trim($ip['unemployment']);
        //$foodStampWelfare = trim($ip['foodStampWelfare']);
        $homeValue = trim($ip['homeValue']);
        //$targetPayment = trim($ip['targetPayment']);
        $escrowAdvances = trim($ip['escrowAdvances']);
        $model = trim($ip['model']);
        $term = trim($ip['amort']);

        if ($term == LoanTerms::INTEREST_ONLY) {
            $terms = 12;
        } elseif ($term == 'Remaining Months') {
            $terms = trim($ip['term']);
        } elseif ($term != '') {
            // $pos = '';
            $pos = strpos($term, '/');
            if (($pos == 0) || ($pos == -1)) {
                $pos = strpos($term, ' ');
            }
            $amort = substr($term, 0, $pos);
            if ($amort <= 0) {
                $amort = 1;
            }
            if ($amort > 0) {
                $terms = $amort * 12;
            }
        }


        $principalBalance = Strings::replaceCommaValues($principalBalance);
        $amountDue = Strings::replaceCommaValues($amountDue);
        $rate = Strings::replaceCommaValues($rate);
        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues($grossMonthlyHouseHoldIncome);
        $DTI = Strings::replaceCommaValues($DTI);
        $taxes = Strings::replaceCommaValues($taxes);
        $insurance = Strings::replaceCommaValues($insurance);
        $HOAFees = Strings::replaceCommaValues($HOAFees);
        $mortgageInsurance = Strings::replaceCommaValues($mortgageInsurance);
        $floodInsurance = Strings::replaceCommaValues($floodInsurance);
        $homeValue = Strings::replaceCommaValues($homeValue);

        /** Calculate Proposal Balance - capitalized loan amount = sum of principal balance and amount due **/

        $capitalizedLoanAmt = self::calculateLienProposalBalance($principalBalance, $amountDue, $escrowAdvances, 0);
        $balance = $capitalizedLoanAmt;
        if ($model == 'PRA') {
            $MTMLTV = (Strings::replaceCommaValues($homeValue) * 1.15);
            /** 115% of property value **/
            if ($MTMLTV < $balance) {
                /** HAMP Tier 1  Alternative Modification Waterfall **/
                $balance = $MTMLTV;
            }
        }

        if ($DTI > $DTIReduceTo) {
//        $originalTerms = $terms;
//        $originalRate = $rate;
            $balance = Strings::replaceCommaValues($balance);
            $capitalizedLoanAmt = Strings::replaceCommaValues($capitalizedLoanAmt);
            $MTMLTV = Strings::replaceCommaValues($MTMLTV);

            //$tempBalance = $balance;
            if ($capitalizedLoanAmt * 0.3 < $homeValue) {
                $balanceLowerLimit = $capitalizedLoanAmt * 0.3;
            } else {
                $balanceLowerLimit = $homeValue;
            }

            $tempLien1DTI = 0;

            if($terms && !is_numeric($terms)) {
                Debug($terms);
            }

            $terms = Strings::replaceCommaValues($terms);

            interestLoop::getReport(
                $balance,
                $lien1PaymentPITIA,
                $lien1DTI,
                $tempLien1DTI,
                $rate,
                $terms,
                $taxes,
                $insurance,
                $HOAFees,
                $mortgageInsurance,
                $floodInsurance,
                $grossMonthlyHouseHoldIncome,
                $unemployment,
                $DTIReduceTo,
                $interestRateReducedBy,
                $minInterestRate
            );

            /*** interest rate do loop end ***/
            if ($tempLien1DTI > $DTIReduceTo) {
                termsLoop::getReport(
                    $balance,
                    $lien1PaymentPITIA,
                    $lien1DTI,
                    $rate,
                    $terms,
                    $taxes,
                    $insurance,
                    $HOAFees,
                    $mortgageInsurance,
                    $floodInsurance,
                    $grossMonthlyHouseHoldIncome,
                    $unemployment,
                    $DTIReduceTo
                );
                /*** Terms do loop end ***/
            }

            if ($tempLien1DTI > $DTIReduceTo) {
                principalLoop::getReport(
                    $balance,
                    $lien1PaymentPITIA,
                    $lien1DTI,
                    $rate,
                    $terms,
                    $taxes,
                    $insurance,
                    $HOAFees,
                    $mortgageInsurance,
                    $floodInsurance,
                    $grossMonthlyHouseHoldIncome,
                    $unemployment,
                    $DTIReduceTo,
                    $amountReducedBy,
                    $balanceLowerLimit
                );
                /*** Principal do loop end ***/
            }

            /* adjust DTI to be exactly 31% */
            /*
                if(Strings::replaceCommaValues($lien1DTI) <= $DTIReduceTo && $lien1DTI>0) {
                    $lien1DTI = 31;
                    $grossMonthlyHouseHoldIncome = $grossMonthlyHouseHoldIncome - (Strings::replaceCommaValues($unemployment) + Strings::replaceCommaValues($foodStampWelfare));
                    $lien1PaymentPITIA = $grossMonthlyHouseHoldIncome * $lien1DTI/100;
                    $tempPaymentPITIA = 0;
                    $tempPaymentPITIA = $lien1PaymentPITIA -(Strings::replaceCommaValues($taxes) + Strings::replaceCommaValues($insurance) + Strings::replaceCommaValues($HOAFees) + Strings::replaceCommaValues($mortgageInsurance) + Strings::replaceCommaValues($floodInsurance));
                    $intr = $rate/1200;

                    $balance = $tempPaymentPITIA / ($intr / (1 - (pow(1/(1 + $intr), $terms))));
                }
    */
            /* adjust DTI to be exactly 31% */

            /* Calculate Principal forbearance */
//        if (Strings::replaceCommaValues($targetPayment) == 0) {
//            $forbearanceLoop = false;
//        }
            /** Calculate PI **/

            if (Strings::replaceCommaValues($lien1PaymentPITIA) > 0) {
                $lien1PaymentPI = Strings::replaceCommaValues($lien1PaymentPITIA) - (Strings::replaceCommaValues($taxes) + Strings::replaceCommaValues($insurance) + Strings::replaceCommaValues($HOAFees) + Strings::replaceCommaValues($mortgageInsurance) + Strings::replaceCommaValues($floodInsurance));
            }

            /** Calculate Principal Reduction Amt **/
            $principalReductionAmt = Strings::replaceCommaValues($capitalizedLoanAmt) - Strings::replaceCommaValues($MTMLTV);
            /*
                if ($rate == 2 && ($terms == 480)) {
                    $principalForbearance = Strings::replaceCommaValues($capitalizedLoanAmt) - Strings::replaceCommaValues($balance);
                }
        */
            /** Calculate Principal Forbearance **/
            $principalForbearance = Strings::replaceCommaValues($capitalizedLoanAmt) - Strings::replaceCommaValues($balance);

            if ($model == 'PRA') {
                $principalForbearance = Strings::replaceCommaValues($principalForbearance) - Strings::replaceCommaValues($principalReductionAmt);
            }
        }
        $newHAMPT1PrincipalReductionAmt = $principalReductionAmt;
        $newHAMPT1Rate = $rate;
        $newHAMPT1Terms = $terms;
        if (($DTI > 0) && (($DTI == $DTIReduceTo) || ($DTI < $DTIReduceTo))) {
            //$newHAMPT1Balance = $principalBalance;
            $newHAMPT1DTI = $DTI;
        } else {
            //$newHAMPT1Balance = $balance;

            $newHAMPT1DTI = $lien1DTI;
        }
        $result['capitalizedLoanAmt'] = $capitalizedLoanAmt;
        $result['principalReductionAmt'] = $newHAMPT1PrincipalReductionAmt;
        $result['rate'] = $newHAMPT1Rate;
        $result['terms'] = $newHAMPT1Terms;
        $result['interestBearingUPB'] = $balance;
        $result['principalForbearance'] = $principalForbearance;
        $result['DTI'] = $newHAMPT1DTI;
        $result['newTargetPayment'] = $lien1PaymentPITIA;
        $result['newPaymentInterest'] = $lien1PaymentPI;
        return $result;
    }


    /*
    * Calculate DTI for HAMP Tier2 PRA Approach.
    */

    /**
     * @param $ip
     * @return array
     */
    public static function HAMPT2PRA($ip): array
    {
        $DTIReduceTo = 31;
        //$amountReducedBy = 1000;
        $terms = 480;
//    $principalLoop = true;
//    $termsLoop = true;
//    $interestLoop = true;
//    $i = 0;
//    $forbearanceLoop = true;

//        $rate = $HAMPTier2PMMSRate+.50;
        $v = CONST_HAMP_TIER_2_MMS_RATE + .50;
        $rate = $v;
        $step = 0.125;
//            echo $v . "<br>";
        if (fmod($v, $step) > 0.0625) $rate = (round($v / $step)) * 0.125;
        elseif ((fmod($v, $step) > 0.0)) $rate = (round($v / $step) + 1) * 0.125;


        $result = [];
//    $newHAMPT1Balance = 0;
//    $newHAMPT1PrincipalReductionAmt = 0;
//    $newHAMPT1Rate = 0;
//    $newHAMPT1Rate = 0;
//    $newHAMPT1Terms = 0;
//    $newHAMPT1Payment = 0;
//    $newHAMPT1DTI = 0;
//    $principalBalance = 0;
//    $amountDue = 0;
//    $grossMonthlyHouseHoldIncome = 0;
//    $DTI = 0;
//    $principalReductionAmt = 0;
//    $lien1PaymentPITIA = 0;
//    $taxes = 0;
//    $insurance = 0;
//    $HOAFees = 0;
//    $mortgageInsurance = 0;
//    $floodInsurance = 0;
//    $unemployment = 0;
//    $foodStampWelfare = 0;
//    $homeValue = 0;
//    $capitalizedLoanAmt = 0;
        $MTMLTV = 0;
//    $interestBearingUPB = 0;
//    $targetPayment = 0;
//    $newTargetPayment = 0;
//    $principalForbearance = 0;
//    $orgInterestBearingUPB = 0;
//    $escrowAdvances = 0;
//    $model = 'PRA';
//    $tempRate = 0;
//    $tempBalance = 0;
        $lien1PaymentPI = 0;
//    $tempDTI = 0;
        // $lien1DTI = 0;
//    $tempBalance1 = 0;
//    $balanceLowerLimit = 0;
//    $occupancy = '';
//    $HAMPTier1Payment = 0;
//    $lien1Payment = 0;
//    $v1 = 0;
//    $v2 = 0;

        $principalBalance = trim($ip['principalBalance']);
        $amountDue = trim($ip['amountDue']);
        $grossMonthlyHouseHoldIncome = trim($ip['grossMonthlyHouseHoldIncome']);
        $DTI = trim($ip['DTI']);
        $taxes = trim($ip['taxes']);
        $insurance = trim($ip['insurance']);
        $HOAFees = trim($ip['HOAFees']);
        $mortgageInsurance = trim($ip['mortgageInsurance']);
        $floodInsurance = trim($ip['floodInsurance']);
        $unemployment = trim($ip['unemployment']);
        //$foodStampWelfare = trim($ip['foodStampWelfare']);
        $homeValue = trim($ip['homeValue']);
        // $targetPayment = trim($ip['targetPayment']);
        $escrowAdvances = trim($ip['escrowAdvances']);
        $model = trim($ip['model']);
//    $occupancy = trim($ip['occupancy']);
//    $HAMPTier1Payment = trim($ip['HAMPTier1Payment']);
//    $lien1Payment = trim($ip['lien1Payment']);

        $principalBalance = Strings::replaceCommaValues($principalBalance);
        $amountDue = Strings::replaceCommaValues($amountDue);
        $rate = Strings::replaceCommaValues($rate);
        $grossMonthlyHouseHoldIncome = Strings::replaceCommaValues($grossMonthlyHouseHoldIncome);
        $DTI = Strings::replaceCommaValues($DTI);

        /** Calculate Proposal Balance - capitalized loan amount = sum of principal balance and amount due **/

        $capitalizedLoanAmt = self::calculateLienProposalBalance($principalBalance, $amountDue, $escrowAdvances, 0);

        //$balance = $capitalizedLoanAmt;
        if ($model == 'PRA') {
            $MTMLTV = (Strings::replaceCommaValues($homeValue) * 1.15);
            /** 115% of property value **/
//        if ($MTMLTV < $balance) {
//            /** HAMP Tier 2  Alternative Modification Waterfall **/
//            //$balance = $MTMLTV;
//        }
        }
        /*
            if (Strings::replaceCommaValues($capitalizedLoanAmt)*0.7 < Strings::replaceCommaValues($homeValue)*1.15) {
                $balance = Strings::replaceCommaValues($capitalizedLoanAmt)*0.7;
            } else {
                $balance = Strings::replaceCommaValues($homeValue)*1.15;
            }
    */
        $LTV = '';
        if ($homeValue > 0) $LTV = Strings::replaceCommaValues($capitalizedLoanAmt) / Strings::replaceCommaValues($homeValue);
        /** Calculate the LTV value **/
        if ($LTV >= 1.15) {
            $v1 = Strings::replaceCommaValues($capitalizedLoanAmt) * 0.3;
            /** 30% of UPB **/
            $v2 = Strings::replaceCommaValues($capitalizedLoanAmt) - (Strings::replaceCommaValues($homeValue) * 1.15);
            /** 115% of property value **/

            /** Calculate principal forbearance **/
            if ($v1 < $v2) {
                /** Pick up the lesser amount = 30% of UPB, 115% of home value **/
                $principalForbearance = $v1;
            } else {
                $principalForbearance = $v2;
            }
            $balance = Strings::replaceCommaValues($capitalizedLoanAmt) - Strings::replaceCommaValues($principalForbearance);
        } else {
            $balance = Strings::replaceCommaValues($capitalizedLoanAmt);
            $principalForbearance = Strings::replaceCommaValues($capitalizedLoanAmt) - Strings::replaceCommaValues($balance);
        }

        $originalTerms = $terms;
        $originalRate = $rate;
        $balance = Strings::replaceCommaValues($balance);
        $capitalizedLoanAmt = Strings::replaceCommaValues($capitalizedLoanAmt);
        $MTMLTV = Strings::replaceCommaValues($MTMLTV);

        //$tempBalance = $balance;

        //$termsLoop = false;
        $terms = $originalTerms;

        //$interestLoop = true;
        $rate = $originalRate;
        //$tempRate = $originalRate;

        /** Calculate PITIA **/

        $lien1PaymentPITIA = self::calculateHAMPT1PITIA($balance, $rate, $terms, $taxes, $insurance, $HOAFees, $mortgageInsurance, $floodInsurance);

        if (Strings::replaceCommaValues($lien1PaymentPITIA) > 0) {
            $lien1PaymentPI = Strings::replaceCommaValues($lien1PaymentPITIA) - (Strings::replaceCommaValues($taxes) + Strings::replaceCommaValues($insurance) + Strings::replaceCommaValues($HOAFees) + Strings::replaceCommaValues($mortgageInsurance) + Strings::replaceCommaValues($floodInsurance));
        }
        /** Calculate DTI **/
        //$tempDTI = $lien1DTI;

        $lien1DTI = self::calculateHAMPT1DTI($lien1PaymentPITIA, $grossMonthlyHouseHoldIncome, $unemployment);

        /* Calculate Principal forbearance */
//    if (Strings::replaceCommaValues($targetPayment) == 0) {
//        $forbearanceLoop = false;
//    }
        /** Calculate PI **/

        if (Strings::replaceCommaValues($lien1PaymentPITIA) > 0) {
            $lien1PaymentPI = Strings::replaceCommaValues($lien1PaymentPITIA) - (Strings::replaceCommaValues($taxes) + Strings::replaceCommaValues($insurance) + Strings::replaceCommaValues($HOAFees) + Strings::replaceCommaValues($mortgageInsurance) + Strings::replaceCommaValues($floodInsurance));
        }

        /** Calculate Principal Forbearance **/
//        $principalForbearance = Strings::replaceCommaValues($capitalizedLoanAmt) - Strings::replaceCommaValues($balance);

        $interestBearingUPB = Strings::replaceCommaValues($capitalizedLoanAmt) - $principalForbearance;

        /** Calculate Principal Reduction Amt **/
        $principalReductionAmt = Strings::replaceCommaValues($capitalizedLoanAmt) - Strings::replaceCommaValues($MTMLTV);

        if (($DTI > 0) && (($DTI == $DTIReduceTo) || ($DTI < $DTIReduceTo))) {
            $newHAMPT1Balance = $principalBalance;
        } else {
            $newHAMPT1Balance = $balance;
        }
        $newHAMPT1PrincipalReductionAmt = $principalReductionAmt;
        $newHAMPT1Rate = $rate;
        $newHAMPT1Terms = $terms;
        $newHAMPT1DTI = $lien1DTI;
        $result['capitalizedLoanAmt'] = $capitalizedLoanAmt;
        $result['balance'] = $newHAMPT1Balance;
        $result['principalReductionAmt'] = $newHAMPT1PrincipalReductionAmt;
        $result['rate'] = $newHAMPT1Rate;
        $result['terms'] = $newHAMPT1Terms;
        $result['interestBearingUPB'] = $interestBearingUPB;
        $result['principalForbearance'] = $principalForbearance;
        $result['DTI'] = $newHAMPT1DTI;
        $result['newTargetPayment'] = $lien1PaymentPITIA;
        $result['newPaymentInterest'] = $lien1PaymentPI;
        return $result;
    }

    /*** Calculate approvedLoanAmt and lien1Rate MonthlyPayment  On Feb 02, 2017 ***/

    public static function calculateHMLOLoanInfoTotalMonthlyPayment($approvedLoanAmt, $lien1Rate, $lien1Terms, $purchaseCloseDate, $accrualType)
    {

        //$totalMonthlyPayment = 0;

        return self::calculateHMLOPaymentValue($approvedLoanAmt, $lien1Rate, $lien1Terms, $purchaseCloseDate, $accrualType);
    }

    /*** Calculate approvedLoanAmt and lien1Rate MonthlyPayment ***/

    public static function calculateTotalLoanToValue($approvedLoanAmt, $assessedValue)
    {
        $totalLoanToValue = 0;

        $approvedLoanAmt = Strings::replaceCommaValues($approvedLoanAmt);
        $assessedValue = Strings::replaceCommaValues($assessedValue);

        if (($approvedLoanAmt == '') || ($approvedLoanAmt == NULL)) {
            $approvedLoanAmt = 0;
        }
        if (($assessedValue == '') || ($assessedValue == NULL)) {
            $assessedValue = 0;
        }

        if ($assessedValue > 0) {
            $totalLoanToValue = ($approvedLoanAmt / $assessedValue);
            $totalLoanToValue = $totalLoanToValue * 100;
        }
        return $totalLoanToValue;
    }

    /*** Calculate flood and annualPremium for Insurance on Jan 25, 2016 ***/

    public static function calculateTotalInsurance($floodInsurance1, $annualPremium)
    {
        //$totalInsurance = 0;

        $floodInsurance1 = Strings::replaceCommaValues($floodInsurance1);
        $annualPremium = Strings::replaceCommaValues($annualPremium);

        if ($floodInsurance1 == '') {
            $floodInsurance1 = 0;
        }
        if ($annualPremium == '') {
            $annualPremium = 0;
        }
        return ($floodInsurance1 + $annualPremium) / 12;
    }

    /*** Calculate HMLO Real Estate Taxes on Feb 2, 2017 ***/

    public static function calculateHMLORealEstateTaxes($taxes1, $isTaxesInsEscrowed = ''): float
    {
        // $HMLORealEstateTaxes = 0;

        $taxes1 = Strings::replaceCommaValues($taxes1);
        $isTaxesInsEscrowed = trim($isTaxesInsEscrowed);

        if ($taxes1 == '') {
            $taxes1 = 0;
        }
        if ($isTaxesInsEscrowed == '') $isTaxesInsEscrowed = '';

        if ($isTaxesInsEscrowed == 'Yes') {
            $HMLORealEstateTaxes = $taxes1;
        } else {
            $HMLORealEstateTaxes = $taxes1 / 12;
        }
        return round(Strings::replaceCommaValues($HMLORealEstateTaxes), 2);
    }

    /*** Calculate 1st and 2nd Lien and Proposal Monthly Payment PITIA ***/

    public static function calculatePaymentPITIAWithRemainingMonths($lienPayment, $lienProposalRate, $lienProposalTerms, $noOfTerms, $taxes1, $insurance1, $HOAFees1, $mortgageInsurance1): float
    {
        //$lienPaymentPITIA = 0;
        $lienPaymentPITIA = self::calculatePaymentValueWithRemainingMonths($lienPayment, $lienProposalRate, $lienProposalTerms, $noOfTerms);
        $lienPaymentPITIA = Strings::replaceCommaValues($lienPaymentPITIA) + Strings::replaceCommaValues($taxes1);
        $lienPaymentPITIA += Strings::replaceCommaValues($insurance1) + Strings::replaceCommaValues($HOAFees1);
        $lienPaymentPITIA += Strings::replaceCommaValues($mortgageInsurance1);
        return round($lienPaymentPITIA, 2);
    }

    /**
     * @param $loanAmt
     * @param $rate
     * @param $term
     * @param $terms
     * @return float
     */
    public static function calculatePaymentValueWithRemainingMonths($loanAmt, $rate, $term, $terms): float
    {
        $amt = 0;
        $loanAmt = Strings::replaceCommaValues($loanAmt);
        $rate = Strings::replaceCommaValues($rate);
        if ($term == 'Not Sure') {
            $amt = '';
        } elseif ($term == LoanTerms::INTEREST_ONLY) {
//          $amt = calculateInterestValue1($loanAmt, $rate, 12);
            $amt = round(($loanAmt * $rate / 1200), 2);
        } else {
            if ($term != 'Remaining Months') {
                //$pos = '';
                $pos = strpos($term, ' ');
                $amort = substr($term, 0, $pos);
                if ($amort <= 0) {
                    $amort = 1;
                }
                if ($amort > 0) {
                    $terms = $amort * 12;
                }
            }
            if ($loanAmt > 0 && $rate > 0 && $terms > 0) {
                $intr = $rate / 1200;
                $amt = round($loanAmt * $intr / (1 - (pow(1 / (1 + $intr), $terms))), 2);
            }
        }
        return $amt;
    }

    /*** Calculate Current DTI for HMLO ***/

    public static function calculateDTIForHMLO($totalHouseHoldIncome, $totalHouseHoldExpenses): float
    {
        $lien1DTI = 0;
        //$tempValue = 0;
        $totalHouseHoldIncome = Strings::replaceCommaValues($totalHouseHoldIncome);
        $totalHouseHoldExpenses = Strings::replaceCommaValues($totalHouseHoldExpenses);

        if (($totalHouseHoldIncome == '') || ($totalHouseHoldIncome == NULL)) {
            $totalHouseHoldIncome = 0;
        }
        if (($totalHouseHoldExpenses == '') || ($totalHouseHoldExpenses == NULL)) {
            $totalHouseHoldExpenses = 0;
        }
        if ($totalHouseHoldIncome > 0) {
            $lien1DTI = $totalHouseHoldExpenses / $totalHouseHoldIncome;
            $lien1DTI = $lien1DTI * 100;
        }
        return round($lien1DTI, 2);
    }


    /*** HMLO New Loan info tab Calculation - May 10, 2017 ***/
    /* Card 872-Make Total loan amount editable for the files whose transaction type is purchase
    * Description:- https://trello.com/c/bzIJlNef
    * On making the total lon amount as an entry field need to re-calculate the Down Payment % ,Acquisition Down Payment,Current Loan Balance
    * */

    public static function calculateInitialLoanAmount($inputArr): array
    {
        $costBasis = Strings::replaceCommaValues($inputArr['costBasis'] ?? ''); // isEmptyFieldsCheck(Arrays::getArrayValue('costBasis', $inputArr));
        $maxAmtToPutDown = Strings::replaceCommaValues($inputArr['maxAmtToPutDown'] ?? ''); // isEmptyFieldsCheck(Arrays::getArrayValue('maxAmtToPutDown', $inputArr));
        $downPaymentPercentage = $costBasis <> 0 ? ($maxAmtToPutDown * 100) / $costBasis : 0;
        $acquisitionPriceFinanced = $costBasis - $maxAmtToPutDown;

        return [
            'acquisitionPriceFinanced' => round($acquisitionPriceFinanced, 2),
            'maxAmtToPutDown'          => round($maxAmtToPutDown, 2),
            'downPaymentPercentage'    => round($downPaymentPercentage, 5),
        ];
    }

    /**
     * @param $costBasis
     * @param $acquisitionPriceFinanced
     * @return float
     */
    public static function calculateAcquisitionLTV($costBasis, $acquisitionPriceFinanced): float
    {
        $acquisitionLTV = 0;

        $costBasis = Strings::replaceCommaValues($costBasis);
        $acquisitionPriceFinanced = Strings::replaceCommaValues($acquisitionPriceFinanced);

        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }
        if (($acquisitionPriceFinanced == '') || ($acquisitionPriceFinanced == NULL)) {
            $acquisitionPriceFinanced = 0;
        }

        if ($acquisitionPriceFinanced > 0) {
            $acquisitionLTV = ($acquisitionPriceFinanced / $costBasis);
            $acquisitionLTV = $acquisitionLTV * 100;
        }
        return round($acquisitionLTV, 2);
    }

    /**
     * @param $costBasis
     * @param $acquisitionPriceFinanced
     * @param $closingCostFinanced
     * @param $prepaidInterestReserve
     * @return float
     */
    public static function calculateAcquisitionLTV_New($costBasis, $acquisitionPriceFinanced, $closingCostFinanced, $prepaidInterestReserve): float
    {
        $acquisitionLTV = 0;

        $costBasis = Strings::replaceCommaValues($costBasis);
        $acquisitionPriceFinanced = Strings::replaceCommaValues($acquisitionPriceFinanced);
        $closingCostFinanced = Strings::replaceCommaValues($closingCostFinanced);
        $prepaidInterestReserve = Strings::replaceCommaValues($prepaidInterestReserve);

        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }
        if (($acquisitionPriceFinanced == '') || ($acquisitionPriceFinanced == NULL)) {
            $acquisitionPriceFinanced = 0;
        }
        if (($closingCostFinanced == '') || ($closingCostFinanced == NULL)) {
            $closingCostFinanced = 0;
        }
        if ($acquisitionPriceFinanced > 0) {
            $acquisitionLTV = $costBasis != 0 ? (($acquisitionPriceFinanced + $closingCostFinanced ) / $costBasis) : 0;
            $acquisitionLTV = $acquisitionLTV * 100;
        }
        $acquisitionLTV = round($acquisitionLTV, 2);
        HMLOLoanTermsCalculation::$acquisitionLTVTooltipWithValues = "(($acquisitionPriceFinanced + $closingCostFinanced ) / $costBasis) * 100 = $acquisitionLTV % ";
        return $acquisitionLTV;

    }


    /**
     * @param $rehabCost
     * @param $rehabCostFinanced
     * @return float
     */
    public static function calculatePercentageRehabCostFinanced($rehabCost, $rehabCostFinanced): float
    {
        $perRehabCostFinanced = 0;

        $rehabCost = Strings::replaceCommaValues($rehabCost);
        $rehabCostFinanced = Strings::replaceCommaValues($rehabCostFinanced);

        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }
        if (($rehabCostFinanced == '') || ($rehabCostFinanced == NULL)) {
            $rehabCostFinanced = 0;
        }

        if ($rehabCost > 0) {
            $perRehabCostFinanced = ($rehabCostFinanced / $rehabCost);
            $perRehabCostFinanced = $perRehabCostFinanced * 100;
        }
        $perRehabCostFinanced = round($perRehabCostFinanced, 2);
        HMLOLoanTermsCalculation::$rehabCostFinancedTooltipWithValues = "$rehabCostFinanced / $rehabCost * 100 = $perRehabCostFinanced % ";
        return $perRehabCostFinanced;
    }

    /**
     * @param $costBasis
     * @param $rehabCost
     * @return float
     */
    public static function calculateTotalProjectCost($costBasis, $rehabCost): float
    {
        //$totalProjectCost = 0;

        $costBasis = Strings::replaceCommaValues($costBasis);
        $rehabCost = Strings::replaceCommaValues($rehabCost);

        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }
        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }

        $totalProjectCost = ($costBasis + $rehabCost);
        return round($totalProjectCost, 2);
    }

    /**
     * @param $rehabCost
     * @param $assessedValue
     * @param $typeOfHMLOLoanRequesting
     * @param $costBasis
     * @param $closingCostFinanced
     * @param $prepaidInterestReserve
     * @param $interestOnInterestReserveFeeAmt
     * @param $costSpent
     * @return float
     */
    public static function calculateTotalProjectCost_new(
        $rehabCost,
        $assessedValue,
        $typeOfHMLOLoanRequesting,
        $costBasis,
        $closingCostFinanced,
        $prepaidInterestReserve,
        $interestOnInterestReserveFeeAmt,
        $costSpent,
        $addToTotalProjectValue
    ): float
    {
        //$totalProjectCost = 0;

        $rehabCost = Strings::replaceCommaValues($rehabCost);
        $assessedValue = Strings::replaceCommaValues($assessedValue);
        $costBasis = Strings::replaceCommaValues($costBasis);
        $typeOfHMLOLoanRequesting = trim($typeOfHMLOLoanRequesting);
        $closingCostFinanced = Strings::replaceCommaValues($closingCostFinanced);
        //$prepaidInterestReserve = Strings::replaceCommaValues($prepaidInterestReserve);
        $interestOnInterestReserveFeeAmt = Strings::replaceCommaValues($interestOnInterestReserveFeeAmt);
        $costSpent = Strings::replaceCommaValues($costSpent);

        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }
        if (($assessedValue == '') || ($assessedValue == NULL)) {
            $assessedValue = 0;
        }
        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }
        if (($closingCostFinanced == '') || ($closingCostFinanced == NULL)) {
            $closingCostFinanced = 0;
        }
//    if (($prepaidInterestReserve == "") || ($prepaidInterestReserve == NULL)) {
//        $prepaidInterestReserve = 0;
//    }
        if (($interestOnInterestReserveFeeAmt == '') || ($interestOnInterestReserveFeeAmt == NULL)) {
            $interestOnInterestReserveFeeAmt = 0;
        }
        if (($costSpent == '') || ($costSpent == NULL)) {
            $costSpent = 0;
        }

        if ($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance'
            || $typeOfHMLOLoanRequesting == 'New Construction - Existing Land'
            || $typeOfHMLOLoanRequesting == 'Refinance'
            || $typeOfHMLOLoanRequesting == 'Rate & Term Refinance'
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
        ) {
            HMLOLoanTermsCalculation::$TPCToolTipWithValues = " $assessedValue + $rehabCost  ";
            $totalProjectCost = ($assessedValue + $rehabCost);
        } elseif ($typeOfHMLOLoanRequesting == 'Line of Credit') {
            HMLOLoanTermsCalculation::$TPCToolTipWithValues = " $rehabCost + $closingCostFinanced ";
            $totalProjectCost = ($rehabCost) + ($closingCostFinanced);
        } else {
            HMLOLoanTermsCalculation::$TPCToolTipWithValues = " $costBasis + $rehabCost  ";
            $totalProjectCost = ($costBasis + $rehabCost);
        }

        $totalProjectCost += $costSpent;
        HMLOLoanTermsCalculation::$TPCToolTipWithValues .= " + $costSpent ";
        if($addToTotalProjectValue == 'Yes') {
            $totalProjectCost += $prepaidInterestReserve;
            HMLOLoanTermsCalculation::$TPCToolTipWithValues .= " + $prepaidInterestReserve ";
        }
        if($interestOnInterestReserveFeeAmt) {
            $totalProjectCost += $interestOnInterestReserveFeeAmt;
            HMLOLoanTermsCalculation::$TPCToolTipWithValues .= " + $interestOnInterestReserveFeeAmt ";
        }
        $totalProjectCost = round($totalProjectCost, 2);
        HMLOLoanTermsCalculation::$TPCToolTipWithValues .= " = $ ".Currency::formatDollarAmountWithDecimal($totalProjectCost);
        return $totalProjectCost;
    }

    /**
     * @param $totalLoanAmount
     * @param $totalProjectCost
     * @return float
     */
    public static function calculateLoanToCost($totalLoanAmount, $totalProjectCost): float
    {
        $LTC = 0;

        $totalLoanAmount = Strings::replaceCommaValues($totalLoanAmount);
        $totalProjectCost = Strings::replaceCommaValues($totalProjectCost);

        if (($totalLoanAmount == '') || ($totalLoanAmount == NULL)) {
            $totalLoanAmount = 0;
        }
        if (($totalProjectCost == '') || ($totalProjectCost == NULL)) {
            $totalProjectCost = 0;
        }

        if ($totalProjectCost > 0) {
            $LTC = ($totalLoanAmount / $totalProjectCost);
            $LTC = $LTC * 100;
        }
        $LTC = round($LTC, 2);
        HMLOLoanTermsCalculation::$LTCToolTipWithValues = "$totalLoanAmount / $totalProjectCost * 100 = $LTC % ";
        return $LTC;
    }

    /**
     * @param $val
     * @return float
     */
    public static function isEmptyFieldsCheck($val): float
    {
        if (($val == '') || ($val == NULL)) {
            $val = 0;
        }
        return Strings::replaceCommaValues($val);
    }

    /**
     * @param $acquisitionPriceFinanced
     * @param $rehabCostFinanced
     * @param $closingCostFinanced
     * @param $payOffMortgage1
     * @param $payOffMortgage2
     * @param $payOffOutstandingTaxes
     * @param $payOffOtherOutstandingAmounts
     * @param $cashOutAmt
     * @param $typeOfHMLOLoanRequesting
     * @param $prepaidInterestReserve
     * @return float
     */
    public static function calculateHMLOFeeCostTotalLoanAmount(
        $acquisitionPriceFinanced,
        $rehabCostFinanced,
        $closingCostFinanced,
        $payOffMortgage1,
        $payOffMortgage2,
        $payOffOutstandingTaxes,
        $payOffOtherOutstandingAmounts,
        $cashOutAmt,
        $typeOfHMLOLoanRequesting,
        $prepaidInterestReserve
    ): float
    {
        //$totalLoanAmount = 0;

        $acquisitionPriceFinanced = self::isEmptyFieldsCheck($acquisitionPriceFinanced);
        $rehabCostFinanced = self::isEmptyFieldsCheck($rehabCostFinanced);
        $closingCostFinanced = self::isEmptyFieldsCheck($closingCostFinanced);
        $payOffMortgage1 = self::isEmptyFieldsCheck($payOffMortgage1);
        $payOffMortgage2 = self::isEmptyFieldsCheck($payOffMortgage2);
        $payOffOutstandingTaxes = self::isEmptyFieldsCheck($payOffOutstandingTaxes);
        //$cashOutAmt = isEmptyFieldsCheck($cashOutAmt);
        $typeOfHMLOLoanRequesting = trim($typeOfHMLOLoanRequesting);
        if ($typeOfHMLOLoanRequesting == 'Rate & Term Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance') {

            $totalLoanAmount = ($payOffMortgage1 + $payOffMortgage2 + $payOffOutstandingTaxes + $closingCostFinanced + $rehabCostFinanced);

        } else if ($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance'
            || $typeOfHMLOLoanRequesting == 'Refinance'
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {

            $totalLoanAmount = ($rehabCostFinanced);

        }  else {
            $totalLoanAmount = ($acquisitionPriceFinanced + $rehabCostFinanced + $closingCostFinanced);
        }
        $totalLoanAmount = round($totalLoanAmount, 2);
        $prepaidInterestReserve = self::isEmptyFieldsCheck($prepaidInterestReserve);
        $totalLoanAmount = self::isEmptyFieldsCheck($totalLoanAmount);
        $totalLoanAmount += $prepaidInterestReserve;
        return round($totalLoanAmount, 2);
    }

    /**
     * @param $ipArray
     * @return float
     */
    public static function calculatePointsToRate($ipArray): float
    {
        //$rate = 0;
        $points = self::isEmptyFieldsCheck($ipArray['points']);
        $rehabCostFinanced = self::isEmptyFieldsCheck($ipArray['rehabCostFinanced']);
        $prepaidInterestReserve = self::isEmptyFieldsCheck($ipArray['prepaidInterestReserve']);
        $initialLoanAmount = self::isEmptyFieldsCheck($ipArray['initialLoanAmount']);
        $rate = 0;
        if (($initialLoanAmount + $rehabCostFinanced + $prepaidInterestReserve) > 0) {
            $rate = ($points / ($initialLoanAmount + $rehabCostFinanced + $prepaidInterestReserve)) * 100; // Formula Implementation..
        }

        return round($rate, 5);
    }

    /**
     * @param $ipArray
     * @return float
     */
    public static function calculateRateToPoints($ipArray): float
    {

        $rate = self::isEmptyFieldsCheck($ipArray['rate']);
        $rehabCostFinanced = self::isEmptyFieldsCheck($ipArray['rehabCostFinanced']);
        $prepaidInterestReserve = self::isEmptyFieldsCheck($ipArray['prepaidInterestReserve']);
        $initialLoanAmount = self::isEmptyFieldsCheck($ipArray['initialLoanAmount']);

        $points = ($rate * ($initialLoanAmount + $rehabCostFinanced + $prepaidInterestReserve)) / 100; // Formula Implementation..

        return round($points, 2);//round($points);
    }

    /**
     * @param array $inputArray
     * @param array $secondInArray
     * @return float
     */
    public static function calculateTotalFeesAndCostNew(array $inputArray, array $secondInArray): float
    {
        $totalFeesAndCost = 0;
        /* Removed on 11-4-2018 by task #**********/
        /* constructionHoldbackFee, loanOriginationFee, valuationAVEFee, creditCheckFee, valuationCMAFee, employmentVerificationFee, taxReturnOrderFee, miscellaneousFee,"escrowFees", "recordingFee"*/

        $feesArray = [
            'originationPointsValue',
            'brokerPointsValue',
            'appraisalFee',
            'applicationFee',
            'drawsSetUpFee',
            'estdTitleClosingFee',
            'processingFee',
            'valuationBPOFee',
            'valuationAVMFee',
            'creditReportFee',
            'backgroundCheckFee',
            'floodCertificateFee',
            'otherFee',
            'thirdPartyFees',
            'taxImpoundsFee',
            'insImpoundsFee',
            'documentPreparationFee',
            'servicingSetUpFee',
            'floodServiceFee',
            'drawsFee', 'wireFee',
            'taxServiceFee',
            'inspectionFees',
            'projectFeasibility',
            'dueDiligence',
            'UccLienSearch',
            'closingCostFinancingFee',
            'attorneyFee',
            'escrowFees',
            'recordingFee',
            'underwritingFees',
            'propertyTax',
            'bufferAndMessengerFee',
            'travelNotaryFee',
            'prePaidInterest',
            'realEstateTaxes',
            'insurancePremium',
            'payOffLiensCreditors',
            'wireTransferFeeToTitle',
            'wireTransferFeeToEscrow',
            'pastDuePropertyTaxes',
            'totalEstPerDiem',
            'survey',
            'wholeSaleAdminFee',
            'cityCountyTaxStamps',
            'valuationCMAFee',
            'valuationAVEFee',
            'creditCheckFee',
            'employmentVerificationFee',
            'taxReturnOrderFee',
            'constructionHoldbackFee',
            'brokerProcessingFee',
        ];

        for ($fee = 0; $fee < count($feesArray); $fee++) {
//        $tempFeeKey = 0;
//        $tempFeeValue = 0;
            $tempFee = null;
            $tempFeeKey = $feesArray[$fee];

            if (array_key_exists($tempFeeKey, $secondInArray)) {
                $tempFeeValue = Strings::replaceCommaValues($secondInArray[$tempFeeKey]);
                if ($tempFeeValue == '' || $tempFeeValue == NULL) {
                    $tempFee = 0;
                } else {
                    $tempFee = $tempFeeValue;
                }
            } elseif (array_key_exists($tempFeeKey, $inputArray)) {
                $tempFeeValue = Strings::replaceCommaValues($inputArray[$tempFeeKey]);
                if ($tempFeeValue == '' || $tempFeeValue == NULL) {
                    $tempFee = 0;
                } else {
                    $tempFee = $tempFeeValue;
                }
            }
            $totalFeesAndCost += $tempFee;
        }

        return round($totalFeesAndCost, 2);
    }

    /**
     * @param $closingCostFinanced
     * @param $totalFeesAndCost
     * @return float
     */
    public static function calculatePercentageClosingCostFinanced($closingCostFinanced, $totalFeesAndCost): float
    {
        $perClosingCostFinanced = 0;

        $closingCostFinanced = Strings::replaceCommaValues($closingCostFinanced);
        $totalFeesAndCost = Strings::replaceCommaValues($totalFeesAndCost);

        if (($closingCostFinanced == '') || ($closingCostFinanced == NULL)) {
            $closingCostFinanced = 0;
        }
        if (($totalFeesAndCost == '') || ($totalFeesAndCost == NULL)) {
            $totalFeesAndCost = 0;
        }

        if ($totalFeesAndCost > 0) {
            $perClosingCostFinanced = (($closingCostFinanced / $totalFeesAndCost) * 100);
        }
        return round($perClosingCostFinanced, 2);
    }

    /**
     * @param $totalLoanAmount
     * @param $homeValue
     * @return float
     */
    public static function calculateARVPercentage($totalLoanAmount, $homeValue): float
    {
        $ARV = 0;

        $totalLoanAmount = Strings::replaceCommaValues($totalLoanAmount);
        $homeValue = Strings::replaceCommaValues($homeValue);

        if (($totalLoanAmount == '') || ($totalLoanAmount == NULL)) {
            $totalLoanAmount = 0;
        }
        if (($homeValue == '') || ($homeValue == NULL)) {
            $homeValue = 0;
        }

        if ($homeValue > 0) {
            $ARV = (($totalLoanAmount / $homeValue) * 100);
        }
        $ARV = round($ARV, 2);
        HMLOLoanTermsCalculation::$fullARVTooltipWithValues = " $totalLoanAmount / $homeValue * 100 = $ARV % ";
        return $ARV;
    }

    /**
     * @param $totalFeesAndCost
     * @param $closingCostFinanced
     * @return float
     */
    public static function calculateClosingCostNotFinanced($totalFeesAndCost, $closingCostFinanced): float
    {


        $totalFeesAndCost = Strings::replaceCommaValues($totalFeesAndCost);
        $closingCostFinanced = Strings::replaceCommaValues($closingCostFinanced);

        if (empty($totalFeesAndCost)) {
            $totalFeesAndCost = 0;
        }
        if (empty($closingCostFinanced)) {
            $closingCostFinanced = 0;
        }
        HMLOLoanTermsCalculation::$closingCostNotFinancedTooltip = 'Total Fees & Costs - Closing Costs Financed';

        $closingCostNotFinanced = ($totalFeesAndCost - $closingCostFinanced);
        $closingCostNotFinanced = round($closingCostNotFinanced, 2);
        HMLOLoanTermsCalculation::$closingCostNotFinancedTooltipWithValues = " $totalFeesAndCost - $closingCostFinanced <hr> $closingCostNotFinanced";
        return $closingCostNotFinanced;
    }

    /**
     * @param $closingCostNotFinanced
     * @param $maxAmtToPutDown
     * @param $otherDownPayment
     * @param $earnestDeposit
     * @return float
     */
    public static function calculateTotalCashToClose($closingCostNotFinanced, $maxAmtToPutDown, $otherDownPayment, $earnestDeposit): float
    {
        //$totalCashToClose = 0;

        $closingCostNotFinanced = Strings::replaceCommaValues($closingCostNotFinanced);
        $maxAmtToPutDown = Strings::replaceCommaValues($maxAmtToPutDown);
        $otherDownPayment = Strings::replaceCommaValues($otherDownPayment);
        $earnestDeposit = Strings::replaceCommaValues($earnestDeposit);

        if (($closingCostNotFinanced == '') || ($closingCostNotFinanced == NULL)) {
            $closingCostNotFinanced = 0;
        }
        if (($maxAmtToPutDown == '') || ($maxAmtToPutDown == NULL)) {
            $maxAmtToPutDown = 0;
        }
        if (($otherDownPayment == '') || ($otherDownPayment == NULL)) {
            $otherDownPayment = 0;
        }
        if (($earnestDeposit == '') || ($earnestDeposit == NULL)) {
            $earnestDeposit = 0;
        }

        $totalCashToClose = ($closingCostNotFinanced + $maxAmtToPutDown - $earnestDeposit - $otherDownPayment);
        $totalCashToClose = round($totalCashToClose, 2);

        HMLOLoanTermsCalculation::$totalCashToCloseTooltip = " Closing Cost Not Financed + Down Payment - Earnest Deposit -  Paid Outside Escrow ";
        HMLOLoanTermsCalculation::$totalCashToCloseTooltipWithValues = " $closingCostNotFinanced + $maxAmtToPutDown - $earnestDeposit - $otherDownPayment <hr> $totalCashToClose";

        return $totalCashToClose;
    }


    public static function calculateTotalCashToCloseForSPREO(bool $isTransactionTypePurchaseCategory,
                                                             $acquisitionPriceFinanced,
                                                             $closingCostFinanced
    ): float
    {

        $totalCashToCloseForSPREO = 0;

        if($isTransactionTypePurchaseCategory) {
            $totalCashToCloseForSPREO = $acquisitionPriceFinanced - $closingCostFinanced;
            $totalCashToCloseForSPREO = round($totalCashToCloseForSPREO,2);
            HMLOLoanTermsCalculation::$totalCashToCloseTooltip = " Initial Loan Amount - Closing Costs Financed ";
            HMLOLoanTermsCalculation::$totalCashToCloseTooltipWithValues = " $acquisitionPriceFinanced - $closingCostFinanced <hr> $totalCashToCloseForSPREO ";
        }

        return $totalCashToCloseForSPREO;
    }

        public static function calculateTotalCashOutForSPREO(bool $isTransactionTypeRefinanceCategory,
                                                             $initialLoanAmount,
                                                             $closingCostFinanced,
                                                             $payOffMortgage1,
                                                             $payOffMortgage2,
                                                             $payOffOutstandingTaxes
    ): float
        {

        $totalCashOutForSPREO = 0;
        if($isTransactionTypeRefinanceCategory) {
            $totalCashOutForSPREO = $initialLoanAmount
                                        - $payOffMortgage1
                                        - $payOffMortgage2
                                        - $payOffOutstandingTaxes
                                        - $closingCostFinanced;
            $totalCashOutForSPREO = round($totalCashOutForSPREO,2);
            HMLOLoanTermsCalculation::$totalCashOutToolTip = " Initial Loan Amount - Payoffs - Closing Costs Financed ";
            HMLOLoanTermsCalculation::$totalCashOutToolTipWithValues = " $initialLoanAmount
                                        - $payOffMortgage1
                                        - $payOffMortgage2
                                        - $payOffOutstandingTaxes
                                        - $closingCostFinanced <hr> $totalCashOutForSPREO ";
        }
        return $totalCashOutForSPREO;
    }


    /**
     ** Description    : HMLO Modules Deal Analysis Tab Calculation Methods
     ** Developer    : Venkatesh, Suresh
     ** Date            : May 30, 2017
     **/


    public static function calculateDealAnalysisTotalPrimaryExpenses($costBasis, $rehabCost): float
    {
        //$totalPrimaryExpenses = 0;

        $costBasis = Strings::replaceCommaValues($costBasis);
        $rehabCost = Strings::replaceCommaValues($rehabCost);

        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }
        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }

        $totalPrimaryExpenses = ($costBasis + $rehabCost);
        return round($totalPrimaryExpenses, 2);
    }

    /**
     * @param $costBasis
     * @return float
     */
    public static function calculateDealAnalysisTitleInsurances($costBasis): float
    {
        //$totaTitleInsurances = 0;

        $costBasis = Strings::replaceCommaValues($costBasis);

        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }

        $totalTitleInsurances = $costBasis * (1 / 100);

        return round($totalTitleInsurances, 2);
    }


    /**
     * @param $approvedLoanAmt
     * @return float
     */
    public static function calculateDealAnalysisTotalLenderPointsFee($approvedLoanAmt): float
    {
        //$totaLenderPointsFee = 0;

        $approvedLoanAmt = Strings::replaceCommaValues($approvedLoanAmt);

        if (($approvedLoanAmt == '') || ($approvedLoanAmt == NULL)) {
            $approvedLoanAmt = 0;
        }

        $totalLenderPointsFee = $approvedLoanAmt * (1 / 100);
        return round($totalLenderPointsFee, 2);
    }

    /**
     * @param $hazardInsurance
     * @param $taxes1
     * @param $processingFee
     * @param $appraisalFee
     * @param $estdTitleClosingFee
     * @param $lenderPointsFee
     * @return float
     */
    public static function calculateDealAnalysisTotalSecondaryExpenses(
        $hazardInsurance,
        $taxes1,
        $processingFee,
        $appraisalFee,
        $estdTitleClosingFee,
        $lenderPointsFee
    ): float
    {
        //$totalSecondaryExpenses = 0;

        $hazardInsurance = Strings::replaceCommaValues($hazardInsurance);
        $taxes1 = Strings::replaceCommaValues($taxes1);
        $processingFee = Strings::replaceCommaValues($processingFee);
        $appraisalFee = Strings::replaceCommaValues($appraisalFee);
        $estdTitleClosingFee = Strings::replaceCommaValues($estdTitleClosingFee);
        $lenderPointsFee = Strings::replaceCommaValues($lenderPointsFee);

        if (($hazardInsurance == '') || ($hazardInsurance == NULL)) {
            $hazardInsurance = 0;
        }
        if (($taxes1 == '') || ($taxes1 == NULL)) {
            $taxes1 = 0;
        }
        if (($processingFee == '') || ($processingFee == NULL)) {
            $processingFee = 0;
        }
        if (($appraisalFee == '') || ($appraisalFee == NULL)) {
            $appraisalFee = 0;
        }
        if (($estdTitleClosingFee == '') || ($estdTitleClosingFee == NULL)) {
            $estdTitleClosingFee = 0;
        }
        if (($lenderPointsFee == '') || ($lenderPointsFee == NULL)) {
            $lenderPointsFee = 0;
        }

        $totalSecondaryExpenses = $hazardInsurance + $taxes1 + $processingFee + $appraisalFee + $estdTitleClosingFee + $lenderPointsFee;
        return round($totalSecondaryExpenses, 2);
    }

    /**
     * @param $totalPrimaryExpenses
     * @param $costBasis
     * @param $LTV
     * @param $rehabCost
     * @param $percentOfRehab
     * @return float
     */
    public static function calculateDealAnalysisEstimatedLoanAmount(
        $totalPrimaryExpenses,
        $costBasis,
        $LTV,
        $rehabCost,
        $percentOfRehab
    ): float
    {

        //$totalEstimatedLoanAmount = 0;

        $totalPrimaryExpenses = Strings::replaceCommaValues($totalPrimaryExpenses);
        $costBasis = Strings::replaceCommaValues($costBasis);
        $LTV = Strings::replaceCommaValues($LTV);
        $rehabCost = Strings::replaceCommaValues($rehabCost);
        $percentOfRehab = Strings::replaceCommaValues($percentOfRehab);

        if (($totalPrimaryExpenses == '') || ($totalPrimaryExpenses == NULL)) {
            $totalPrimaryExpenses = 0;
        }
        $totalEstimatedLoanAmount = ($costBasis * $LTV / 100) + ($rehabCost * $percentOfRehab / 100);
        //  $totalEstimatedLoanAmount = $totalPrimaryExpenses * (80 / 100);
        return round($totalEstimatedLoanAmount, 2);
    }


    /**
     * @param $estimatedLoanAmount
     * @param $AVGInterestRate
     * @return float
     */
    public static function calculateDealAnalysisEstimatedMonthlyPayment($estimatedLoanAmount, $AVGInterestRate): float
    {
        //$totalEstimatedMonthlyPayment = 0;

        $estimatedLoanAmount = Strings::replaceCommaValues($estimatedLoanAmount);

        if (($estimatedLoanAmount == '') || ($estimatedLoanAmount == NULL)) {
            $estimatedLoanAmount = 0;
        }

        $totalEstimatedMonthlyPayment = ($estimatedLoanAmount * $AVGInterestRate) / 1200;
        return round($totalEstimatedMonthlyPayment, 2);
    }

    /**
     * @param $estimatedMonthlyPayment
     * @param $loanCarryingCost
     * @return float
     */
    public static function calculateDealAnalysisTotalLoanCarryingCost($estimatedMonthlyPayment, $loanCarryingCost): float
    {
        //$totalLoanCarryingCost = 0;

        $estimatedMonthlyPayment = Strings::replaceCommaValues($estimatedMonthlyPayment);
        $loanCarryingCost = Strings::replaceCommaValues($loanCarryingCost);

        if (($estimatedMonthlyPayment == '') || ($estimatedMonthlyPayment == NULL)) {
            $estimatedMonthlyPayment = 0;
        }
        if (($loanCarryingCost == '') || ($loanCarryingCost == NULL)) {
            $loanCarryingCost = 0;
        }

        $totalLoanCarryingCost = ($estimatedMonthlyPayment * $loanCarryingCost);
        return round($totalLoanCarryingCost, 2);
    }

    /**
     * @param $homeValue
     * @param $realtorCommissions
     * @return float
     */
    public static function calculateDealAnalysisTotalRealtorCommissions($homeValue, $realtorCommissions): float
    {
        $totalRealtorCommissions = 0;

        $homeValue = Strings::replaceCommaValues($homeValue);
        $realtorCommissions = Strings::replaceCommaValues($realtorCommissions);

        if (($homeValue == '') || ($homeValue == NULL)) {
            $homeValue = 0;
        }
        if (($realtorCommissions == '') || ($realtorCommissions == NULL)) {
            $realtorCommissions = 0;
        }
        if ($realtorCommissions > 0) {
            $totalRealtorCommissions = $homeValue * ($realtorCommissions / 100);
        }

        return round($totalRealtorCommissions, 2);
    }

    /**
     * @param $rehabCost
     * @return float
     */
    public static function calculateDealAnalysisRehabContingencyFee($rehabCost): float
    {
        //$totaRehabContingencyFee = 0;

        $rehabCost = Strings::replaceCommaValues($rehabCost);

        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }

        $totalRehabContingencyFee = $rehabCost * (10 / 100);
        return round($totalRehabContingencyFee, 2);
    }

    /**
     * @param $estimatedMonthlyPayment
     * @param $monthsPaymentReserves
     * @return float
     */
    public static function calculateDealAnalysisTotalMonthsPaymentReserves($estimatedMonthlyPayment, $monthsPaymentReserves): float
    {
        //$totalMonthsPaymentReserves = 0;

        $estimatedMonthlyPayment = Strings::replaceCommaValues($estimatedMonthlyPayment);
        $monthsPaymentReserves = Strings::replaceCommaValues($monthsPaymentReserves);

        if (($estimatedMonthlyPayment == '') || ($estimatedMonthlyPayment == NULL)) {
            $estimatedMonthlyPayment = 0;
        }
        if (($monthsPaymentReserves == '') || ($monthsPaymentReserves == NULL)) {
            $monthsPaymentReserves = 0;
        }

        $totalMonthsPaymentReserves = $estimatedMonthlyPayment * $monthsPaymentReserves;
        return round($totalMonthsPaymentReserves, 2);
    }

    /**
     * @param $totalMonthsPaymentReserves
     * @param $rehabCost
     * @return float
     */
    public static function calculateDealAnalysisTotalCashReserves($totalMonthsPaymentReserves, $rehabCost): float
    {
        //$totalCashReserves = 0;

        $totalMonthsPaymentReserves = Strings::replaceCommaValues($totalMonthsPaymentReserves);
        $rehabCost = Strings::replaceCommaValues($rehabCost);

        if (($totalMonthsPaymentReserves == '') || ($totalMonthsPaymentReserves == NULL)) {
            $totalMonthsPaymentReserves = 0;
        }
        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }

        $totalCashReserves = $totalMonthsPaymentReserves + ($rehabCost * (10 / 100));
        return round($totalCashReserves, 2);
    }

    /**
     * @param $estimatedLoanAmount
     * @param $totalPrimaryExpenses
     * @param $totalSecondaryExpenses
     * @return float
     */
    public static function calculateDealAnalysisCashNeededDownPayment($estimatedLoanAmount, $totalPrimaryExpenses, $totalSecondaryExpenses): float
    {
        //$cashNeededDownPayment = 0;

        //$LTV                        = Strings::replaceCommaValues($LTV);
        $estimatedLoanAmount = Strings::replaceCommaValues($estimatedLoanAmount);
        $totalPrimaryExpenses = Strings::replaceCommaValues($totalPrimaryExpenses);
        $totalSecondaryExpenses = Strings::replaceCommaValues($totalSecondaryExpenses);

        if (($estimatedLoanAmount == '') || ($estimatedLoanAmount == NULL)) {
            $estimatedLoanAmount = 0;
        }
        if (($totalPrimaryExpenses == '') || ($totalPrimaryExpenses == NULL)) {
            $totalPrimaryExpenses = 0;
        }
        if (($totalSecondaryExpenses == '') || ($totalSecondaryExpenses == NULL)) {
            $totalSecondaryExpenses = 0;
        }

        //$cashNeededDownPayment = ($LTV/100) * ($totalPrimaryExpenses + $totalSecondaryExpenses);
        $cashNeededDownPayment = ($totalPrimaryExpenses + $totalSecondaryExpenses) - $estimatedLoanAmount;
        return round($cashNeededDownPayment, 2);
    }

    /**
     * @param $cashNeededDownPayment
     * @param $rehabContingencyFee
     * @param $totalMonthsPaymentReserves
     * @return float
     */
    public static function calculateDealAnalysisTotalCashNeededToCloseYourLoan($cashNeededDownPayment, $rehabContingencyFee, $totalMonthsPaymentReserves): float
    {
        //$totalCashNeededToCloseYourLoan = 0;

        $cashNeededDownPayment = Strings::replaceCommaValues($cashNeededDownPayment);
        $rehabContingencyFee = Strings::replaceCommaValues($rehabContingencyFee);
        $totalMonthsPaymentReserves = Strings::replaceCommaValues($totalMonthsPaymentReserves);

        if (($cashNeededDownPayment == '') || ($cashNeededDownPayment == NULL)) {
            $cashNeededDownPayment = 0;
        }
        if (($rehabContingencyFee == '') || ($rehabContingencyFee == NULL)) {
            $rehabContingencyFee = 0;
        }
        if (($totalMonthsPaymentReserves == '') || ($totalMonthsPaymentReserves == NULL)) {
            $totalMonthsPaymentReserves = 0;
        }

        $totalCashNeededToCloseYourLoan = $cashNeededDownPayment + $rehabContingencyFee + $totalMonthsPaymentReserves;
        return round($totalCashNeededToCloseYourLoan, 2);
    }

    /**
     * @param $homeValue
     * @param $totalPrimaryExpenses
     * @param $totalSecondaryExpenses
     * @param $totalRealtorCommissions
     * @param $totalLoanCarryingCost
     * @param $additionalExpenses
     * @return float
     */
    public static function calculateDealAnalysisTotalProjectedProfit($homeValue, $totalPrimaryExpenses, $totalSecondaryExpenses, $totalRealtorCommissions, $totalLoanCarryingCost, $additionalExpenses): float
    {
        //$totalProjectedProfit = 0;

        $homeValue = Strings::replaceCommaValues($homeValue);
        $additionalExpenses = Strings::replaceCommaValues($additionalExpenses);
        $totalPrimaryExpenses = Strings::replaceCommaValues($totalPrimaryExpenses);
        $totalSecondaryExpenses = Strings::replaceCommaValues($totalSecondaryExpenses);
        $totalRealtorCommissions = Strings::replaceCommaValues($totalRealtorCommissions);
        $totalLoanCarryingCost = Strings::replaceCommaValues($totalLoanCarryingCost);

        if (($totalPrimaryExpenses == '') || ($totalPrimaryExpenses == NULL)) {
            $totalPrimaryExpenses = 0;
        }
        if (($homeValue == '') || ($homeValue == NULL)) {
            $homeValue = 0;
        }
        if (($additionalExpenses == '') || ($additionalExpenses == NULL)) {
            $additionalExpenses = 0;
        }
        if (($totalSecondaryExpenses == '') || ($totalSecondaryExpenses == NULL)) {
            $totalSecondaryExpenses = 0;
        }
        if (($totalRealtorCommissions == '') || ($totalRealtorCommissions == NULL)) {
            $totalRealtorCommissions = 0;
        }
        if (($totalLoanCarryingCost == '') || ($totalLoanCarryingCost == NULL)) {
            $totalLoanCarryingCost = 0;
        }

        //$totalProjectedProfit = $homeValue - ($totalPrimaryExpenses + $totalSecondaryExpenses + $totalRealtorCommissions + (($AVGInterestRate / 100) * $loanCarryingCost) + $additionalExpenses);
        $totalProjectedProfit = $homeValue - ($totalPrimaryExpenses + $totalSecondaryExpenses + $additionalExpenses + $totalLoanCarryingCost + $totalRealtorCommissions);
        return round($totalProjectedProfit, 2);
    }

    /**
     * @param $costBasis
     * @param $totalProjectedProfit
     * @param $minProfitDesired
     * @return float
     */
    public static function calculateDealAnalysisTotalMaxOfferAmount($costBasis, $totalProjectedProfit, $minProfitDesired): float
    {
        //$totalMaximumOfferAmount = 0;

        $costBasis = Strings::replaceCommaValues($costBasis);
        $totalProjectedProfit = Strings::replaceCommaValues($totalProjectedProfit);
        $minProfitDesired = Strings::replaceCommaValues($minProfitDesired);

        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }
        if (($totalProjectedProfit == '') || ($totalProjectedProfit == NULL)) {
            $totalProjectedProfit = 0;
        }
        if (($minProfitDesired == '') || ($minProfitDesired == NULL)) {
            $minProfitDesired = 0;
        }

        $totalMaximumOfferAmount = ($costBasis + $totalProjectedProfit) - $minProfitDesired;
        return round($totalMaximumOfferAmount, 2);
    }

    /**
     * @param $acquisitionPriceFinanced
     * @param $assessedValue
     * @param $typeOfHMLOLoanRequesting
     * @param $totalLoanAmount
     * @param $closingCostFinanced
     * @param $prepaidInterestReserve
     * @return float
     */
    public static function calculateMarketLTV_New($acquisitionPriceFinanced, $assessedValue, $typeOfHMLOLoanRequesting, $totalLoanAmount, $closingCostFinanced, $prepaidInterestReserve): float
    {
        $assessedValue = Strings::replaceCommaValues($assessedValue);
        $acquisitionPriceFinanced = Strings::replaceCommaValues($acquisitionPriceFinanced);
        $totalLoanAmount = Strings::replaceCommaValues($totalLoanAmount);
        $typeOfHMLOLoanRequesting = trim($typeOfHMLOLoanRequesting);
        $closingCostFinanced = Strings::replaceCommaValues($closingCostFinanced);
        $prepaidInterestReserve = Strings::replaceCommaValues($prepaidInterestReserve);

        if (!$assessedValue || $assessedValue < 0) {
            return 0;
        }

        if (
            $typeOfHMLOLoanRequesting == 'Cash-Out / Refinance'
            || $typeOfHMLOLoanRequesting == 'Rate & Term Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance'
            || $typeOfHMLOLoanRequesting == 'Line of Credit'
            || $typeOfHMLOLoanRequesting == 'New Construction - Existing Land'
            || $typeOfHMLOLoanRequesting == 'Refinance'
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
        ) {
            $marketLTV = ($totalLoanAmount / $assessedValue) * 100.0;
            HMLOLoanTermsCalculation::$marketLTVToolTipWithValues = "($totalLoanAmount / $assessedValue) * 100.0";
        } else {
            $marketLTV = (($acquisitionPriceFinanced + $closingCostFinanced) / $assessedValue) * 100;
            HMLOLoanTermsCalculation::$marketLTVToolTipWithValues = "( $acquisitionPriceFinanced + $closingCostFinanced) / $assessedValue * 100";
        }
        $marketLTV = round($marketLTV, 2);
        HMLOLoanTermsCalculation::$marketLTVToolTipWithValues .= " = $marketLTV %";
        return $marketLTV;
    }

    /**
     * @param $acquisitionPriceFinanced
     * @param $assessedValue
     * @param $typeOfHMLOLoanRequesting
     * @param $totalLoanAmount
     * @return float
     */
    public static function calculateMarketLTV($acquisitionPriceFinanced, $assessedValue, $typeOfHMLOLoanRequesting, $totalLoanAmount): float
    {
        $marketLTV = 0;

        $assessedValue = Strings::replaceCommaValues($assessedValue);
        $acquisitionPriceFinanced = Strings::replaceCommaValues($acquisitionPriceFinanced);
        $totalLoanAmount = Strings::replaceCommaValues($totalLoanAmount);
        $typeOfHMLOLoanRequesting = trim($typeOfHMLOLoanRequesting);

        if (($assessedValue == '') || ($assessedValue == NULL)) {
            $assessedValue = 0;
        }
        if (($acquisitionPriceFinanced == '') || ($acquisitionPriceFinanced == NULL)) {
            $acquisitionPriceFinanced = 0;
        }
        if (($totalLoanAmount == '') || ($totalLoanAmount == NULL)) {
            $totalLoanAmount = 0;
        }

        if ($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance'
            || $typeOfHMLOLoanRequesting == 'Rate & Term Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance'
            || $typeOfHMLOLoanRequesting == 'Line of Credit'
            || $typeOfHMLOLoanRequesting == 'New Construction - Existing Land'
            || $typeOfHMLOLoanRequesting == 'Refinance'
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {

            if ($assessedValue > 0) {
                $marketLTV = ($totalLoanAmount / $assessedValue) * 100;
            }
        } elseif ($assessedValue > 0) {
            $marketLTV = ($acquisitionPriceFinanced / $assessedValue) * 100;
        }


        return round($marketLTV, 2);
    }

    /**
     * @param $marketLTVInArray
     * @return float
     */
    public static function calculateMarketLTVNew($marketLTVInArray): float
    {
        $marketLTV = 0;
        $assessedValue = 0;
        $totalLoanAmount = 0;
        $typeOfHMLOLoanRequesting = '';
        $originalPurchasePrice = 0;

        if (array_key_exists('totalLoanAmount', $marketLTVInArray)) {
            $totalLoanAmount = self::isEmptyFieldsCheck($marketLTVInArray['totalLoanAmount']);
        }
        if (array_key_exists('assessedValue', $marketLTVInArray)) {
            $assessedValue = self::isEmptyFieldsCheck($marketLTVInArray['assessedValue']);
        }
        if (array_key_exists('originalPurchasePrice', $marketLTVInArray)) {
            $originalPurchasePrice = self::isEmptyFieldsCheck($marketLTVInArray['originalPurchasePrice']);
        }
        if (array_key_exists('typeOfHMLOLoanRequesting', $marketLTVInArray)) {
            $typeOfHMLOLoanRequesting = $marketLTVInArray['typeOfHMLOLoanRequesting'];
        }

        if ($typeOfHMLOLoanRequesting == 'Rate & Term Refinance'
            || $typeOfHMLOLoanRequesting == 'Cash-Out / Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance'
            || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance'
            || $typeOfHMLOLoanRequesting == 'Line of Credit'
            || $typeOfHMLOLoanRequesting == 'Refinance'
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {

            if ($assessedValue > 0) {
                $marketLTV = ($totalLoanAmount / $assessedValue) * 100;
            }
        } elseif ($originalPurchasePrice > 0) {
            $marketLTV = ($totalLoanAmount / $originalPurchasePrice) * 100;
        }


        return round($marketLTV, 2);
    }

    public static function calculateNetOperatingIncome($actualRentsInPlace,
                                                       $actualRentsInPlaceCommercial,
                                                       $tenantContribution,
                                                       $otherIncome,
                                                       $vacancy,
                                                       $vacancyCommercial,
                                                       $vacancyOtherIncome,
                                                       $vacancyTenantContribution,
                                                       $lessActualExpenses,
                                                       $waterSewer,
                                                       $electricity,
                                                       $gas,
                                                       $repairsMaintenance,
                                                       $legal,
                                                       $payroll,
                                                       $misc,
                                                       $commonAreaUtilities,
                                                       $elevatorMaintenance,
                                                       $replacementReserves,
                                                       $other,
                                                       $tenantReimursements,
                                                       $managementExpense,
                                                       $spcf_taxes1_cal,
                                                       $spcf_annualPremium_cal,
                                                       $spcf_hoafees_cal
    ): float
    {

        $netOperatingIncome = Strings::replaceCommaValues($actualRentsInPlace)
            + Strings::replaceCommaValues($actualRentsInPlaceCommercial)
            + Strings::replaceCommaValues($tenantContribution)
            + Strings::replaceCommaValues($otherIncome)
            - Strings::replaceCommaValues($vacancy)
            - Strings::replaceCommaValues($vacancyCommercial)
            - Strings::replaceCommaValues($vacancyOtherIncome)
            - Strings::replaceCommaValues($vacancyTenantContribution)
            - Strings::replaceCommaValues($lessActualExpenses)
            - Strings::replaceCommaValues($waterSewer)
            - Strings::replaceCommaValues($electricity)
            - Strings::replaceCommaValues($gas)
            - Strings::replaceCommaValues($repairsMaintenance)
            - Strings::replaceCommaValues($legal)
            - Strings::replaceCommaValues($payroll)
            - Strings::replaceCommaValues($misc)
            - Strings::replaceCommaValues($commonAreaUtilities)
            - Strings::replaceCommaValues($elevatorMaintenance)
            - Strings::replaceCommaValues($replacementReserves)
            - Strings::replaceCommaValues($other)
            - Strings::replaceCommaValues($tenantReimursements)
            - Strings::replaceCommaValues($managementExpense)
            - Strings::replaceCommaValues($spcf_taxes1_cal)
            - Strings::replaceCommaValues($spcf_annualPremium_cal)
            - Strings::replaceCommaValues($spcf_hoafees_cal);

        return round($netOperatingIncome, 2);
    }

    /**
     * @param $inputArray
     * @return float
     */
    public static function calculateNetLenderFundsToBorrower($inputArray): float
    {
        $totalLoanAmount = 0;
        // $closingCostFinanced = 0;
        $totalFeesAndCost = 0;
        $netLenderFundsToBorrower = 0;
        $rehabCostFinanced = 0;
        $prepaidInterestReserve = 0;
        $initialDrawAmount = 0;
        $payOffMortgage1 = 0;
        $payOffMortgage2 = 0;
        $payOffOutstandingTaxes = 0;

        if (array_key_exists('totalLoanAmount', $inputArray)) {
            $totalLoanAmount = self::isEmptyFieldsCheck($inputArray['totalLoanAmount']);
        }
        if (array_key_exists('totalFeesAndCost', $inputArray)) {
            $totalFeesAndCost = self::isEmptyFieldsCheck($inputArray['totalFeesAndCost']);
        }
//    if (array_key_exists('closingCostFinanced', $inputArray)) {
//        $closingCostFinanced = isEmptyFieldsCheck($inputArray['closingCostFinanced']);
//    }
        if (array_key_exists('rehabCostFinanced', $inputArray)) {
            $rehabCostFinanced = self::isEmptyFieldsCheck($inputArray['rehabCostFinanced']);
        }
        if (array_key_exists('prepaidInterestReserve', $inputArray)) {
            $prepaidInterestReserve = self::isEmptyFieldsCheck($inputArray['prepaidInterestReserve']);
        }
        if (array_key_exists('initialDrawAmount', $inputArray)) {
            $initialDrawAmount = self::isEmptyFieldsCheck($inputArray['initialDrawAmount']);
        }
        if (array_key_exists('payOffMortgage1', $inputArray)) {
            $payOffMortgage1 = self::isEmptyFieldsCheck($inputArray['payOffMortgage1']);
        }
        if (array_key_exists('payOffMortgage2', $inputArray)) {
            $payOffMortgage2 = self::isEmptyFieldsCheck($inputArray['payOffMortgage2']);
        }
        if (array_key_exists('payOffOutstandingTaxes', $inputArray)) {
            $payOffOutstandingTaxes = self::isEmptyFieldsCheck($inputArray['payOffOutstandingTaxes']);
        }
        if($inputArray['isTransactionTypePurchaseCategory'] == true) {
            $netLenderFundsToBorrower = $totalLoanAmount
                - $totalFeesAndCost
                - $rehabCostFinanced
                - $prepaidInterestReserve
                + $initialDrawAmount
                + ((bool)$inputArray['isAutoCalcTotalLoanAmountBasedOnLTC2'] === true ?
                    ($inputArray['escrowFees']
                    - ($inputArray['LTC2_additionalReserveInterest']?: 0)
                    - ($inputArray['LTC2_additionalOriginationInterest'] ?: 0)
                    )
                    : 0 )
            ;

            HMLOLoanTermsCalculation::$netLenderFundsToBorrowerToolTipWithValues =
                "$totalLoanAmount
                - $totalFeesAndCost
                - $rehabCostFinanced
                - $prepaidInterestReserve
                + $initialDrawAmount" .
                (((bool)$inputArray['isAutoCalcTotalLoanAmountBasedOnLTC2'] === true) ?
                    " + ". $inputArray['escrowFees']
                    ." - ". $inputArray['LTC2_additionalReserveInterest']
                    ." - ". $inputArray['LTC2_additionalOriginationInterest']
                    : "").
                "<hr>". "$netLenderFundsToBorrower"
           ;

        } else if($inputArray['isTransactionTypeRefinanceCategory'] == true) {
            $netLenderFundsToBorrower = $totalLoanAmount
                - $totalFeesAndCost
                - $rehabCostFinanced
                - $prepaidInterestReserve
                + $initialDrawAmount
                + ((bool)$inputArray['isAutoCalcTotalLoanAmountBasedOnLTC2'] === true ?
                    ($inputArray['escrowFees'])
                    : 0 )
            ;

            HMLOLoanTermsCalculation::$netLenderFundsToBorrowerToolTipWithValues =
                "$totalLoanAmount
                - $totalFeesAndCost
                - $rehabCostFinanced
                - $prepaidInterestReserve
                + $initialDrawAmount" .
                (((bool)$inputArray['isAutoCalcTotalLoanAmountBasedOnLTC2'] === true) ?
                    " + " .$inputArray['escrowFees']
                    : "").
                "<hr>". "$netLenderFundsToBorrower"
            ;

        }
        return round($netLenderFundsToBorrower, 2);
    }

    /**
     * @param $inputArray
     * @return float
     */
    public static function calculateTotalDailyInterestCharge($inputArray): float
    {
        $totalLoanAmount = 0.0;
        $lien1Rate = 0.0;
        $totalDailyInterestCharge = 0.0;

        if (array_key_exists('totalLoanAmount', $inputArray)) {
            $totalLoanAmount = self::isEmptyFieldsCheck($inputArray['totalLoanAmount']);
        }
        if (array_key_exists('lien1Rate', $inputArray)) {
            $lien1Rate = self::isEmptyFieldsCheck($inputArray['lien1Rate']);
        }
        if ($lien1Rate > 0) {
            $totalDailyInterestCharge = $totalLoanAmount * ((($lien1Rate / 100) / 12) / 30);
            // $totalDailyInterestCharge = ($totalDailyInterestCharge);
        }
        return $totalDailyInterestCharge;
    }

    /**
     * @param $inputArray
     * @return float
     */
    public static function calculateTotalEstPerDiem($inputArray): float
    {
        $totalDailyInterestCharge = 0;
        $diemDays = 0;
        // $totalEstPerDiem = 0;

        if (array_key_exists('totalDailyInterestCharge', $inputArray)) {
            $totalDailyInterestCharge = self::isEmptyFieldsCheck($inputArray['totalDailyInterestCharge']);
        }
        if (array_key_exists('diemDays', $inputArray)) {
            $diemDays = self::isEmptyFieldsCheck($inputArray['diemDays']);
        }

        $totalEstPerDiem = $diemDays * Strings::replaceCommaValues($totalDailyInterestCharge);
        return round($totalEstPerDiem, 2);
    }

    /*
    * Description   : Tax Impounds Calculation
    * public static function      : calculateTaxImpoundsFee($taxImpoundsInArray)
    * Params        : taxImpoundsMonth, taxImpoundsMonthAmt
    * Formula       : Tax impounds * Impounds Months
    * Author        : Suresh K (SK)
    */
    /**
     * @param $taxImpoundsInArray
     * @return float
     */
    public static function calculateTaxImpoundsFee($taxImpoundsInArray): float
    {
        //$taxImpoundsFee = $taxImpoundsMonth = $taxImpoundsMonthAmt = 0;

        $taxImpoundsMonth = 0;
        $taxImpoundsMonthAmt = 0;

        if (array_key_exists('taxImpoundsMonth', $taxImpoundsInArray)) {
            $taxImpoundsMonth = self::isEmptyFieldsCheck($taxImpoundsInArray['taxImpoundsMonth']);
        }
        if (array_key_exists('taxImpoundsMonthAmt', $taxImpoundsInArray)) {
            $taxImpoundsMonthAmt = self::isEmptyFieldsCheck($taxImpoundsInArray['taxImpoundsMonthAmt']);
        }

        $taxImpoundsFee = $taxImpoundsMonth * $taxImpoundsMonthAmt;
        return round($taxImpoundsFee, 2);
    }

    /**
     ** Description    : Required Reserves Section Calculation Methods
     ** Developer    : Venkatesh
     ** Date            : Oct 12, 2017
     **/

    public static function calculatePaymentReserves($paymentReserves, $totalMonthlyPayment): float
    {
        $paymentReservesAmt = 0;

        $paymentReserves = Strings::replaceCommaValues($paymentReserves);
        $totalMonthlyPayment = Strings::replaceCommaValues($totalMonthlyPayment);

        if (($paymentReserves == '') || ($paymentReserves == NULL)) {
            $paymentReserves = 0;
        }
        if (($totalMonthlyPayment == '') || ($totalMonthlyPayment == NULL)) {
            $totalMonthlyPayment = 0;
        }

        if ($paymentReserves > 0) {
            $paymentReservesAmt = ($totalMonthlyPayment * $paymentReserves);
            $paymentReservesAmt = round($paymentReservesAmt, 2);
        }

        return $paymentReservesAmt;
    }

    /**
     * @param $requiredConstruction
     * @param $rehabCost
     * @param $rehabCostFinanced
     * @return float
     */
    public static function calculateRequiredConstruction($requiredConstruction, $rehabCost, $rehabCostFinanced): float
    {
        $requiredConstructionAmt = 0;

        $requiredConstruction = Strings::replaceCommaValues($requiredConstruction);
        $rehabCost = Strings::replaceCommaValues($rehabCost);
        $rehabCostFinanced = Strings::replaceCommaValues($rehabCostFinanced);

        if (($requiredConstruction == '') || ($requiredConstruction == NULL)) {
            $requiredConstruction = 0;
        }
        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }
        if (($rehabCostFinanced == '') || ($rehabCostFinanced == NULL)) {
            $rehabCostFinanced = 0;
        }

        if ($requiredConstruction > 0) {
            $requiredConstructionAmt = ($requiredConstruction / 100) * ($rehabCost - $rehabCostFinanced);
            $requiredConstructionAmt = round($requiredConstructionAmt, 2);
        }

        return $requiredConstructionAmt;
    }

    public static function calculatePercentageTotalLoanAmount($percentageTotalLoan, $totalLoanAmount): float
    {
        $percentageTotalLoanAmount = 0;
        $percentageTotalLoan = Strings::replaceCommaValues($percentageTotalLoan);
        $totalLoanAmount = Strings::replaceCommaValues($totalLoanAmount);
        $percentageTotalLoanAmount = (($percentageTotalLoan * $totalLoanAmount) / 100);
        return round($percentageTotalLoanAmount, 2);
    }

    /**
     * @param $contingencyReserve
     * @param $rehabCost
     * @return float
     */
    public static function calculateContingencyReserve($contingencyReserve, $rehabCost): float
    {
        $contingencyReserveAmt = 0;

        $contingencyReserve = Strings::replaceCommaValues($contingencyReserve);
        $rehabCost = Strings::replaceCommaValues($rehabCost);

        if (($contingencyReserve == '') || ($contingencyReserve == NULL)) {
            $contingencyReserve = 0;
        }
        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }

        HMLOLoanTermsCalculation::$contingencyReserveTooltip = "(Rehab/Construction Cost * Contingency Reserve / 100)";

        if ($contingencyReserve > 0) {
            $contingencyReserveAmt = ($contingencyReserve / 100);
            $contingencyReserveAmt = ($rehabCost * $contingencyReserveAmt);
            $contingencyReserveAmt = round($contingencyReserveAmt, 2);
        }
        HMLOLoanTermsCalculation::$contingencyReserveTooltipWithValues = "$rehabCost * $contingencyReserve / 100 <hr> $contingencyReserveAmt";

        return $contingencyReserveAmt;
    }

    /**
     * @param $inputValue
     * @return string
     */
    public static function isCheckValue($inputValue): string
    {
        $tempValue = Strings::replaceCommaValues($inputValue);
        if ($tempValue < 0) {
            $inputValue = abs($tempValue);
            $inputValue = number_format(Strings::replaceCommaValues($inputValue), 2);
            $inputValue = "<span style=\"color:#ff0000\">(" . $inputValue . ')</span>';
        } elseif ($tempValue == 0.00 || $tempValue == 0 || $tempValue == '') {
            $inputValue = '';
        }

        return $inputValue;
    }

    /**
     * @param $paymentReservesAmt
     * @param $requiredConstructionAmt
     * @param $contingencyReserveAmt
     * @param $percentageTotalLoanAmount
     * @return float
     */
    public static function calculateTotalRequiredReserves($totalInterestPaymentReserveRequired,
                                                          $requiredConstructionAmt,
                                                          $contingencyReserveAmt,
                                                          $percentageTotalLoanAmount): float
    {
        //$totalRequiredReserves = 0;

        $totalInterestPaymentReserveRequired = Strings::replaceCommaValues($totalInterestPaymentReserveRequired);
        $requiredConstructionAmt = Strings::replaceCommaValues($requiredConstructionAmt);
        $contingencyReserveAmt = Strings::replaceCommaValues($contingencyReserveAmt);
        $percentageTotalLoanAmount = Strings::replaceCommaValues($percentageTotalLoanAmount);

        if (($totalInterestPaymentReserveRequired == '') || ($totalInterestPaymentReserveRequired == NULL)) {
            $totalInterestPaymentReserveRequired = 0;
        }
        if (($requiredConstructionAmt == '') || ($requiredConstructionAmt == NULL)) {
            $requiredConstructionAmt = 0;
        }
        if (($contingencyReserveAmt == '') || ($contingencyReserveAmt == NULL)) {
            $contingencyReserveAmt = 0;
        }

        $totalRequiredReserves = ($totalInterestPaymentReserveRequired
            + $requiredConstructionAmt
            + $contingencyReserveAmt
            + $percentageTotalLoanAmount);
        $totalRequiredReserves = round($totalRequiredReserves, 2);

        HMLOLoanTermsCalculation::$totalRequiredReservesTooltip = "Total Required Reserves = Total Interest Payment Reserve Required 
        + Required Construction /Rehab Budget Not Financed 
        + % Contingency Reserve 
        + % of Total Loan Amount";

        HMLOLoanTermsCalculation::$totalRequiredReservesTooltipWithValues = "($totalInterestPaymentReserveRequired + $requiredConstructionAmt + $contingencyReserveAmt + $percentageTotalLoanAmount) <hr> $totalRequiredReserves";

        return $totalRequiredReserves;
    }

    public static function calculateTotalRequiredReservesForSPREO($rehabCost,
                                                                  $rehabCostFinanced): float
    {
        //$totalRequiredReserves = 0;

        $rehabCost = Strings::replaceCommaValues($rehabCost);
        $rehabCostFinanced = Strings::replaceCommaValues($rehabCostFinanced);


        if (empty($rehabCost)) {
            $rehabCost = 0;
        }
        if (empty($rehabCostFinanced)) {
            $rehabCostFinanced = 0;
        }

        $totalRequiredReserves = ($rehabCost
            - $rehabCostFinanced)
            + (0.1 * $rehabCostFinanced);
        $totalRequiredReserves = round($totalRequiredReserves, 2);

        HMLOLoanTermsCalculation::$totalRequiredReservesTooltip = "(Rehab/Construction Cost - Rehab/Construction Cost Financed) + (.1 * Rehab/Construction Cost Financed)";

        HMLOLoanTermsCalculation::$totalRequiredReservesTooltipWithValues = "($rehabCost
            - $rehabCostFinanced)
            + (0.1 * $rehabCostFinanced) <hr> $totalRequiredReserves";

        return $totalRequiredReserves;
    }
    public static function calculateContingencyReserveForSPREO($rehabCostFinanced): float
    {
        $rehabCostFinanced = Strings::replaceCommaValues($rehabCostFinanced);
        if (empty($rehabCostFinanced)) {
            $rehabCostFinanced = 0;
        }
        $contingencyReserveAmt =  (0.1 * $rehabCostFinanced);
        $contingencyReserveAmt = round($contingencyReserveAmt, 2);

        HMLOLoanTermsCalculation::$contingencyReserveTooltip = "(.1 * Rehab/Construction Cost Financed)";

        HMLOLoanTermsCalculation::$contingencyReserveTooltipWithValues = "(0.1 * $rehabCostFinanced) <hr> $contingencyReserveAmt";

        return $contingencyReserveAmt;
    }

    /**
     * @param $rehabCost
     * @param $rehabCostPercentageFinanced
     * @return float
     */
    public static function calculateRehabCostFinancedByPercentage($rehabCost, $rehabCostPercentageFinanced): float
    {
        $rehabCostFinancedAmt = 0;

        try {
            $rehabCost = self::isEmptyFieldsCheck($rehabCost);
        } catch (Exception $e) {
        }
        try {
            $rehabCostPercentageFinanced = self::isEmptyFieldsCheck($rehabCostPercentageFinanced);
        } catch (Exception $e) {
        }

        if ($rehabCostPercentageFinanced > 0) {
            $rehabCostFinancedAmt = $rehabCost * ($rehabCostPercentageFinanced / 100);
            $rehabCostFinancedAmt = round($rehabCostFinancedAmt, 2);
        }

        return $rehabCostFinancedAmt;
    }


    /**
     * @param $rehabCostFinanced
     * @param $rehabCost
     * @return float
     */
    public static function calculateRehabCostPercentageFinanced($rehabCostFinanced, $rehabCost): float
    {
        $rehabCostFinancedPercentage = 0;

        $rehabCost = Strings::replaceCommaValues($rehabCost);
        $rehabCostFinanced = Strings::replaceCommaValues($rehabCostFinanced);

        if (($rehabCost == '') || ($rehabCost == NULL)) {
            $rehabCost = 0;
        }

        if (($rehabCostFinanced == '') || ($rehabCostFinanced == NULL)) {
            $rehabCostFinanced = 0;
        }

        if ($rehabCost > 0) {
            $rehabCostFinancedPercentage = ($rehabCostFinanced / $rehabCost) * 100;
            $rehabCostFinancedPercentage = round($rehabCostFinancedPercentage, 2);//round($rehabCostFinancedPercentage);
        }

        return $rehabCostFinancedPercentage;
    }

    /**
     * @param $downPaymentPercentage
     * @param $costBasis
     * @return float
     */
    public static function calculateDownPaymentByPercentage($downPaymentPercentage, $costBasis): float
    {
        $maxAmtToPutDown = 0;

        $downPaymentPercentage = Strings::replaceCommaValues($downPaymentPercentage);
        $costBasis = Strings::replaceCommaValues($costBasis);

        if (($downPaymentPercentage == '') || ($downPaymentPercentage == NULL)) {
            $downPaymentPercentage = 0;
        }

        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }

        if ($downPaymentPercentage > 0) {
            $maxAmtToPutDown = ($downPaymentPercentage / 100);
            $maxAmtToPutDown = ($costBasis * $maxAmtToPutDown);
            $maxAmtToPutDown = round($maxAmtToPutDown, 2); //round($maxAmtToPutDown);
        }
        return $maxAmtToPutDown;
    }

    /**
     * @param $maxAmtToPutDown
     * @param $costBasis
     * @return float
     */
    public static function calculateDownPaymentPercentage($maxAmtToPutDown, $costBasis): float
    {
        $downPaymentPercentage = 0;

        $maxAmtToPutDown = Strings::replaceCommaValues($maxAmtToPutDown);
        $costBasis = Strings::replaceCommaValues($costBasis);

        if (($maxAmtToPutDown == '') || ($maxAmtToPutDown == NULL)) {
            $maxAmtToPutDown = 0;
        }

        if (($costBasis == '') || ($costBasis == NULL)) {
            $costBasis = 0;
        }

        if ($costBasis > 0) {
            $downPaymentPercentage = ($maxAmtToPutDown / $costBasis) * 100;
            $downPaymentPercentage = round($downPaymentPercentage, 5);
        }

        return $downPaymentPercentage;
    }

    /**
     * @param $totalLoanAmount
     * @param $payOffMortgage1
     * @param $payOffMortgage2
     * @param $payOffOutstandingTaxes
     * @param $closingCostFinanced
     * @param $rehabCostFinanced
     * @param $typeOfHMLOLoanRequesting
     * @param $prepaidInterestReserveForCal
     * @return float
     */
    public static function calculateTotalCashOut(
        $initialLoanAmount,
        $payOffMortgage1,
        $payOffMortgage2,
        $payOffOutstandingTaxes,
        $closingCostFinanced,
        $rehabCostFinanced,
        $typeOfHMLOLoanRequesting,
        $prepaidInterestReserveForCal
    ): float
    {
        $totalCashOutAmt = 0;

        $initialLoanAmount = Strings::replaceCommaValues($initialLoanAmount);
        $payOffMortgage1 = Strings::replaceCommaValues($payOffMortgage1);
        $payOffMortgage2 = Strings::replaceCommaValues($payOffMortgage2);
        $payOffOutstandingTaxes = Strings::replaceCommaValues($payOffOutstandingTaxes);
        $closingCostFinanced = Strings::replaceCommaValues($closingCostFinanced);
        //$rehabCostFinanced = Strings::replaceCommaValues($rehabCostFinanced);
        $prepaidInterestReserveForCal = Strings::replaceCommaValues($prepaidInterestReserveForCal);

        if (empty($initialLoanAmount)) {
            $initialLoanAmount = 0;
        }

        if (empty($payOffMortgage1)) {
            $payOffMortgage1 = 0;
        }

        if (empty($payOffMortgage2)) {
            $payOffMortgage2 = 0;
        }

        if (empty($payOffOutstandingTaxes)) {
            $payOffOutstandingTaxes = 0;
        }

        if (empty($closingCostFinanced)) {
            $closingCostFinanced = 0;
        }


//    if (($rehabCostFinanced == "") || ($rehabCostFinanced == NULL)) {
//        $rehabCostFinanced = 0;
//    }
        if ($typeOfHMLOLoanRequesting === typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting === typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting === typeOfHMLOLoanRequesting::REFINANCE
            || $typeOfHMLOLoanRequesting === typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting === typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting === typeOfHMLOLoanRequesting::DELAYED_PURCHASE
        ) {
            $totalCashOutAmt = ($initialLoanAmount
                - $payOffMortgage1
                - $payOffMortgage2
                - $payOffOutstandingTaxes
                - $closingCostFinanced
                - $prepaidInterestReserveForCal);
            $totalCashOutAmt = round($totalCashOutAmt,2);

            HMLOLoanTermsCalculation::$totalCashOutToolTip = 'Cash to be Disbursed = (Initial Loan Amount - Pay-Off on Mortgage 1 - Pay-Off on Mortgage 2 - Pay Off Outstanding Taxes - Closing Costs Financed)';
            HMLOLoanTermsCalculation::$totalCashOutToolTipWithValues = '$initialLoanAmount 
                - $payOffMortgage1 
                - $payOffMortgage2 
                - $payOffOutstandingTaxes 
                - $closingCostFinanced 
                - $prepaidInterestReserveForCal <hr> $totalCashOutAmt';
        }

        /* else {
            $totalCashOut = ($totalLoanAmount - $payOffMortgage1 - $payOffMortgage2 - $payOffOutstandingTaxes - $closingCostFinanced - $rehabCostFinanced);
        }*/
        return round($totalCashOutAmt, 2);
    }

    /**
     * @param $CORefiLTVPercentage
     * @param $assessedValue
     * @return float
     */
    public static function calculateCORefiLoanAmtByLTVPercentage($CORefiLTVPercentage, $assessedValue): float
    {
        $CORTotalLoanAmt = 0;

        $CORefiLTVPercentage = Strings::replaceCommaValues($CORefiLTVPercentage);
        $assessedValue = Strings::replaceCommaValues($assessedValue);

        if (($CORefiLTVPercentage == '') || ($CORefiLTVPercentage == NULL)) {
            $CORefiLTVPercentage = 0;
        }

        if (($assessedValue == '') || ($assessedValue == NULL)) {
            $assessedValue = 0;
        }

        if ($CORefiLTVPercentage > 0) {
            $CORTotalLoanAmt = ($CORefiLTVPercentage / 100);
            $CORTotalLoanAmt = ($assessedValue * $CORTotalLoanAmt);
            $CORTotalLoanAmt = round($CORTotalLoanAmt, 2);
        }

        return $CORTotalLoanAmt;
    }

    /**
     * @param $CORTotalLoanAmt
     * @param $assessedValue
     * @return float
     */
    public static function calculateCORefiLTVPercentage($CORTotalLoanAmt, $assessedValue): float
    {
        $CORefiLTVPercentage = 0;

        $CORTotalLoanAmt = Strings::replaceCommaValues($CORTotalLoanAmt);
        $assessedValue = Strings::replaceCommaValues($assessedValue);

        if (($CORTotalLoanAmt == '') || ($CORTotalLoanAmt == NULL)) {
            $CORTotalLoanAmt = 0;
        }

        if (($assessedValue == '') || ($assessedValue == NULL)) {
            $assessedValue = 0;
        }

        if ($assessedValue > 0) {
            $CORefiLTVPercentage = ($CORTotalLoanAmt / $assessedValue) * 100;
            $CORefiLTVPercentage = round($CORefiLTVPercentage, 2); // round($CORefiLTVPercentage)
        }

        return $CORefiLTVPercentage;
    }

    /**
     * @param $Payment
     * @param $taxes1
     * @param $insurance1
     * @param $spcf_hoafees
     * @return float
     */
    public static function calculateHMLONetMonthlyPayment($Payment, $taxes1, $insurance1, $spcf_hoafees): float
    {
        //$netMonthlyPayment = 0;
        $Payment = Strings::replaceCommaValues($Payment);
        $taxes1 = Strings::replaceCommaValues($taxes1);
        $insurance1 = Strings::replaceCommaValues($insurance1);
        $spcf_hoafees = Strings::replaceCommaValues($spcf_hoafees);

        HMLOLoanTermsCalculation::$netMonthlyPaymentTooltip = "$Payment + ( $taxes1 + $insurance1 + $spcf_hoafees ) / 12";

        if (($Payment == '') || ($Payment == NULL)) $Payment = 0;
        if (($taxes1 == '') || ($taxes1 == NULL)) $taxes1 = 0;
        if (($insurance1 == '') || ($insurance1 == NULL)) $insurance1 = 0;
        if ($taxes1 > 0) {
            $taxes1 = $taxes1 / 12;
        }
        if ($insurance1 > 0) {
            $insurance1 = $insurance1 / 12;
        }
        if ($spcf_hoafees > 0) {
            $spcf_hoafees = $spcf_hoafees / 12;
        }
        $netMonthlyPayment = ($Payment + $taxes1 + $insurance1 + $spcf_hoafees);
        $netMonthlyPayment = round($netMonthlyPayment, 2);
        HMLOLoanTermsCalculation::$netMonthlyPaymentTooltip .= " = $ ".Currency::formatDollarAmountWithDecimal($netMonthlyPayment);
        return $netMonthlyPayment;
    }

    /**
     * @param $Payment
     * @param $netIncome
     * @return float
     */
    public static function calculateDebtServiceRatio($Payment, $netIncome): float
    {
        $debtServiceRatio = 0;
        $Payment = Strings::replaceCommaValues($Payment);
        $netIncome = Strings::replaceCommaValues($netIncome);

        if (($Payment == '') || ($Payment == NULL)) $Payment = 0;
        if (($netIncome == '') || ($netIncome == NULL)) $netIncome = 0;

        if ($Payment > 0) $debtServiceRatio = $netIncome / ($Payment * 12);

        return round($debtServiceRatio, 2);
    }

    /**
     * @param $lien1Rate
     * @param $noOfMonthsPrepaid
     * @param $prepaidInterestReserve
     * @return float
     */
    public static function calculateInterestOnInterestReserveFee($lien1Rate, $noOfMonthsPrepaid, $prepaidInterestReserve): float
    {
        $interestOnInterestReserveFeeAmt = 0;

        $lien1Rate = Strings::replaceCommaValues($lien1Rate);
        $noOfMonthsPrepaid = Strings::replaceCommaValues($noOfMonthsPrepaid);
        $prepaidInterestReserve = Strings::replaceCommaValues($prepaidInterestReserve);

        if (($lien1Rate == '') || ($lien1Rate == NULL)) {
            $lien1Rate = 0;
        }

        if (($prepaidInterestReserve == '') || ($prepaidInterestReserve == NULL)) {
            $prepaidInterestReserve = 0;
        }

        if ($prepaidInterestReserve > 0) {
            $interestOnInterest = (($prepaidInterestReserve * ($lien1Rate / 100)) / 12) * $noOfMonthsPrepaid;

            $interestOnInterestReserveFee = $interestOnInterest + ($interestOnInterest * ($lien1Rate / 100)) / 12 * $noOfMonthsPrepaid;

            $interestOnInterestReserveFeeAmt = round($interestOnInterestReserveFee, 2);
        }

        return $interestOnInterestReserveFeeAmt;
    }

    /* Simple ARV Calculation */
    /**
     * @param $ipArray
     * @return float
     */
    public static function calculateSimpleARVPercentage($ipArray): float
    {
        //$totalLoanAmount = $originationPointsValue = $brokerPointsValue = $IOIRFee = $assessedValue = $simpleARV = $simpleARVP = 0;

        $simpleARV = 0;
        $totalLoanAmount = self::isEmptyFieldsCheck($ipArray['totalLoanAmount']);
        $originationPointsValue = self::isEmptyFieldsCheck($ipArray['originationPointsValue']);
        $brokerPointsValue = self::isEmptyFieldsCheck($ipArray['brokerPointsValue']);
        $prepaidInterestReserve = self::isEmptyFieldsCheck($ipArray['prepaidInterestReserve']);
        $assessedValue = self::isEmptyFieldsCheck($ipArray['assessedValue']);

        if ($assessedValue > 0) {
            $simpleARVP = $totalLoanAmount - ($originationPointsValue + $brokerPointsValue + $prepaidInterestReserve);
            $simpleARV = ($simpleARVP / $assessedValue) * 100;
        }
        $simpleARV = round($simpleARV, 2);
        HMLOLoanTermsCalculation::$simpleARVTooltipWithValues = " (  ($totalLoanAmount - ($originationPointsValue + $brokerPointsValue + $prepaidInterestReserve))  / $assessedValue ) * 100 =  $simpleARV % ";

        return $simpleARV;
    }

    /*
    * Description   : Ins Impounds Calculation
    * public static function      : calculateInsImpoundsFee($insImpoundsInArray)
    * Params        : insImpoundsMonth, insImpoundsMonthAmt
    * Formula       : Ins impounds * Impounds Months
    * Author        : Valarmathi
    */
    /**
     * @param $insImpoundsInArray
     * @return float
     */
    public static function calculateInsImpoundsFee($insImpoundsInArray): float
    {
        //$insImpoundsFee = $insImpoundsMonth = $insImpoundsMonthAmt = 0;

        $insImpoundsMonth = 0;
        $insImpoundsMonthAmt = 0;

        if (array_key_exists('insImpoundsMonth', $insImpoundsInArray)) {
            $insImpoundsMonth = self::isEmptyFieldsCheck($insImpoundsInArray['insImpoundsMonth']);
        }
        if (array_key_exists('insImpoundsMonthAmt', $insImpoundsInArray)) {
            $insImpoundsMonthAmt = self::isEmptyFieldsCheck($insImpoundsInArray['insImpoundsMonthAmt']);
        }

        $insImpoundsFee = $insImpoundsMonth * $insImpoundsMonthAmt;
        return round($insImpoundsFee, 2);
    }

    /*Description   : Extension Option Calculation
    * public static function      : calculatePercentageExtensionOption($extensionOptionsAmtInputArray)
    * Formula       : extensionOptionPercentage * totalLoanAmount / 100 * extensionOption
    * Author        : Berin
    */
    /**
     * @param $extensionOptionsAmtInputArray
     * @return float
     */
    public static function calculatePercentageExtensionOption($extensionOptionsAmtInputArray): float
    {
        $totalLoanAmount = $extensionOptionPercentage = $extensionOptionsAmt = 0;
//    $extensionOptionValue = 0;
//    $extensionOption = '';

        if (array_key_exists('totalLoanAmount', $extensionOptionsAmtInputArray)) {
            $totalLoanAmount = self::isEmptyFieldsCheck($extensionOptionsAmtInputArray['totalLoanAmount']);
        }
        if (array_key_exists('extensionOptionPercentage', $extensionOptionsAmtInputArray)) {
            $extensionOptionPercentage = self::isEmptyFieldsCheck($extensionOptionsAmtInputArray['extensionOptionPercentage']);
        }

        $totalLoanAmount = Strings::replaceCommaValues($totalLoanAmount);

        if ($extensionOptionPercentage > 0) {
            $extensionOptionsAmt = (($extensionOptionPercentage * $totalLoanAmount) / 100);
            $extensionOptionsAmt = round($extensionOptionsAmt, 2);
        }
        return $extensionOptionsAmt;
    }

    /*
    * Description   : Interest Rate
    */
    /**
     * @param $intArray
     * @return float
     */
    public static function calculateInterestRate($intArray): float
    {
        $costOfCapital = $yieldSpread = 0;

        if (array_key_exists('costOfCapital', $intArray)) {
            $costOfCapital = self::isEmptyFieldsCheck($intArray['costOfCapital']);
        }
        if (array_key_exists('yieldSpread', $intArray)) {
            $yieldSpread = self::isEmptyFieldsCheck($intArray['yieldSpread']);
        }

        $lien1Rate = $costOfCapital + $yieldSpread;
        return round($lien1Rate, 3);
    }

    /*
    * Description   : Current Loan Balance.
    * Formula       : Current Loan Balance = Initial Loan Amount + Pre-paid Interest Reserves + Closing Costs + Funded Draws
    * Task          : https://www.pivotaltracker.com/story/show/160959499
    */

    /**
     * @param $intArray
     * @param $typeOfHMLOLoanRequesting
     * @return float
     */
    public static function calculateCurrentLoanBalance($intArray, $typeOfHMLOLoanRequesting): float
    {
        $initLAmt = $prePIR = $closingCost = $funDraw = 0;

        $principalPayDown = 0;
        if (isset($intArray['initLAmt'])) $initLAmt = self::isEmptyFieldsCheck($intArray['initLAmt']);
        if (isset($intArray['funDraw'])) $funDraw = self::isEmptyFieldsCheck($intArray['funDraw']);
        if (isset($intArray['principalPayDown'])) $principalPayDown = self::isEmptyFieldsCheck($intArray['principalPayDown']);
        //print_r($principalPayDown);
        //Please Note: the $principalPayDown is used in the calculation for Servicing 2.0 tab
        //The below equation is true for Loan Info Tab (even if is it not same as in the tooltip formulae)

        if (!HMLOLoanTermsCalculation::isTransactionTypeRefinance($typeOfHMLOLoanRequesting)) {
            if (isset($intArray['prePIR'])) $prePIR = self::isEmptyFieldsCheck($intArray['prePIR']);
            if (isset($intArray['closingCost'])) $closingCost = self::isEmptyFieldsCheck($intArray['closingCost']);
           // HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues = "$initLAmt + $funDraw ";
            HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues = "$initLAmt + $prePIR + $closingCost + $funDraw - $principalPayDown ";
        } else {
            HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues = "$initLAmt + $funDraw";
        }

        $currentLoanBalance = $initLAmt + $prePIR + $closingCost + $funDraw - $principalPayDown;
        $currentLoanBalance = round($currentLoanBalance, 2);
        HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues .= " = $ ".Currency::formatDollarAmountWithDecimal($currentLoanBalance);
        return $currentLoanBalance;
    }


    public static function calculateCurrentLoanBalanceForSPREO($initialLoanAmount,
                                                               $prepaidInterestReserve): float
    {

        $currentLoanBalance = $initialLoanAmount + $prepaidInterestReserve;
        $currentLoanBalance = round($currentLoanBalance, 2);

        HMLOLoanTermsCalculation::$currentLoanBalanceTooltip = "Initial Loan Amount + Prepaid Interest Reserve";
        HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues = "$initialLoanAmount + $prepaidInterestReserve";
        HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues .= " <hr>  ".Currency::formatDollarAmountWithDecimal($currentLoanBalance);
        return $currentLoanBalance;
    }
    /*
    * Description   : PayoffAmount.
    * Formula       : PayoffAmount = Current Loan Balance + Amount due+( per diem interest * # of days until pay off date)+(# of months until pay off date* monthly Payment)
    * Task          : card : #268
    */
    /**
     * @param $intArray
     * @return float
     */
    public static function calculatePayOffAmount($intArray): float
    {
        $payOffDate = $paymentduedate = '';
        $currentLoanBalance = 0;
        $totalMonthlyPayment = 0;
        $amountdue = 0;
        $totalDailyInterestCharge = 0;
        $diff = null;

        if (isset($intArray['paymentduedate'])) {
            $paymentduedate = self::isEmptyFieldsCheck($intArray['paymentduedate']);
        }
        if (isset($intArray['payOffDate'])) {
            $payOffDate = self::isEmptyFieldsCheck($intArray['payOffDate']);
        }
        if (isset($intArray['currentLoanBalance'])) {
            $currentLoanBalance = self::isEmptyFieldsCheck($intArray['currentLoanBalance']);
        }
        if (isset($intArray['totalDailyInterestCharge'])) {
            $totalDailyInterestCharge = self::isEmptyFieldsCheck($intArray['totalDailyInterestCharge']);
        }
        if (isset($intArray['totalMonthlyPayment'])) {
            $totalMonthlyPayment = self::isEmptyFieldsCheck($intArray['totalMonthlyPayment']);
        }
        if (isset($intArray['amountdue'])) {
            $amountdue = self::isEmptyFieldsCheck($intArray['amountdue']);
        }
        $totalmonth = 0;
        $weekday = date('d', strtotime($paymentduedate));

        if ($payOffDate && $paymentduedate) {
            $payOffDate = date_create($payOffDate);// pay off date
            $paymentduedate = date_create($paymentduedate);// past payment due Date

            if ($payOffDate < $paymentduedate) {
                $month = (date('m') - 1);
            } else {
                $month = date('m');
            }

            $paymentduedate = date('Y') . '-' . $month . '-' . $weekday;
            $paymentduedate = Dates::formatDateWithRE($paymentduedate, 'YMD', 'm/d/Y');
            $paymentduedate = date_create($paymentduedate);
            if ($paymentduedate && $payOffDate) {
                $diff = date_diff($payOffDate, $paymentduedate);
                $totalmonth = ($diff->m + ($diff->y * 12));
            }
        }
        return Strings::replaceCommaValues($currentLoanBalance)
            + ($totalmonth * $totalMonthlyPayment)
            + $amountdue
            + ($totalDailyInterestCharge * ($diff->d ?? 0));

    }

    /**
     * @param $nper
     * @param $pmt
     * @param $pv
     * @param float $fv
     * @param int $type
     * @param float $guess
     * @return float
     */
    public static function RATE($nper, $pmt, $pv, float $fv = 0.0, int $type = 0, float $guess = 0.1): float
    { //echo "===".$nper."======".$pmt."=====".$pv;
        $nper = floatval($nper);
        $rate = $guess;
        $f = 0;
        if (abs($rate) >= FINANCIAL_PRECISION) {
            $f = exp($nper * log(1 + $rate));
        }
        $y0 = $pv + $pmt * $nper + $fv;
        $y1 = $pv * $f + $pmt * (1 / $rate + $type) * ($f - 1) + $fv;
        // find root by secant method
        $i = $x0 = 0.0;
        $x1 = $rate;
        while ((abs($y0 - $y1) > FINANCIAL_PRECISION) && ($i < FINANCIAL_MAX_ITERATIONS)) {
            $rate = ($y1 * $x0 - $y0 * $x1) / ($y1 - $y0);
            $x0 = $x1;
            $x1 = $rate;
            if (abs($rate) < FINANCIAL_PRECISION) {
                $y = $pv * (1 + $nper * $rate) + $pmt * (1 + $rate * $type) * $nper + $fv;
            } else {
                $f = exp($nper * log(1 + $rate));
                $y = $pv * $f + $pmt * (1 / $rate + $type) * ($f - 1) + $fv;
            }
            $y0 = $y1;
            $y1 = $y;
            ++$i;
        }
        return $rate;
    }


    public static function getAccrualTypeBaseValues($purchaseCloseDate, $accrualType): accrualTypes
    {
        return accrualTypes::getMonthYearDays($purchaseCloseDate, $accrualType);
    }

    public static function getLenderRevenue($inAddArray, $inSubArray): float
    {
        return floatval(array_sum($inAddArray) - array_sum($inSubArray));
    }

    public static function calculateLTCOriginalPurchasePriceValue($initialLoanAmount, $costSpent, $refinanceInitialOriginalPurchasePrice): ?float
    {
        if ($costSpent + $refinanceInitialOriginalPurchasePrice == 0) {
            return 0;
        }
        return $initialLoanAmount / ($costSpent + $refinanceInitialOriginalPurchasePrice) * 100;
    }
}
