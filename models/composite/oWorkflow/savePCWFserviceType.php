<?php

namespace models\composite\oWorkflow;

use models\Database2;
use models\lendingwise\db\tblPCWorkflowStepServiceType_db;
use models\lendingwise\db\tblWorkflowStepServiceTypeMaster_db;
use models\lendingwise\tblPCWFServiceType;
use models\lendingwise\tblPCWorkflowStepServiceType;
use models\lendingwise\tblWFServiceTypeMaster;
use models\lendingwise\tblWorkflowStepServiceTypeMaster;
use models\types\strongType;

/**
 *
 */
class savePCWFserviceType extends strongType
{
    /**
     * @param $inArray
     * @return int|null
     */
    public static function getReport($inArray): ?int
    {
        $WFServiceTypeArray = $inArray['WFServiceTypeArray'] ?? [];
        $PCID = trim($inArray['PCID'] ?? 0);

        if (!sizeof($WFServiceTypeArray)) {
            return 0;
        }

        $WFId = $inArray['WFId'];

        deletePCWFServiceType::getReport($inArray); // refactored

        foreach ($WFServiceTypeArray as $item) {
            $tblPCWFServiceType = new tblPCWFServiceType();
            $tblPCWFServiceType->WFID = $WFId;
            $tblPCWFServiceType->WFServiceType = $item;
            $tblPCWFServiceType->Save();

            if ($PCID == 0) {
                $tblWFServiceTypeMaster = new tblWFServiceTypeMaster();
                $tblWFServiceTypeMaster->WFID = $WFId;
                $tblWFServiceTypeMaster->WFServiceType = $item;
                $tblWFServiceTypeMaster->Save();
            }
        }

        if ($PCID) {
            $lpQry = '
                SELECT wfst.WFStepServiceID AS myOpt 
                FROM tblPCWorkflowSteps wfs 
                INNER JOIN tblPCWorkflowStepServiceType wfst ON wfst.WFSID = wfs.WFSID
                WHERE WFID = :WFID  AND WFStepServiceType NOT IN (
                    SELECT  WFServiceType
                    FROM tblPCWFServiceType 
                    WHERE WFID = :WFID 
            ) ;';

            $lpArr = Database2::getInstance()->queryData($lpQry, [
                'WFID' => $WFId,
            ], null, null, 'myOpt');

            foreach ($lpArr as $item) {
                $tblPCWorkflowStepServiceType = tblPCWorkflowStepServiceType::GetAll([
                    tblPCWorkflowStepServiceType_db::COLUMN_WFSTEPSERVICEID => $item,
                ]);

                foreach ($tblPCWorkflowStepServiceType as $type) {
                    $type->Delete();
                }
            }

        } else {
            $lpQry = '
                SELECT wfst.WFStepServiceID AS myOpt 
                FROM tblWorkflowStepsMaster wfs 
                                INNER JOIN tblWorkflowStepServiceTypeMaster wfst ON wfst.WFSID = wfs.WFSID
                                WHERE WFID = :WFID 
                                  AND WFStepServiceType NOT IN (
                                  SELECT WFServiceType
                                  FROM tblWFServiceTypeMaster 
                                  WHERE WFID = :WFID
                                  ) 
            ;';

            $lpArr = Database2::getInstance()->queryData($lpQry, [
                'WFID' => $WFId,
            ], null, null, 'myOpt');

            foreach ($lpArr as $item) {
                $tblWorkflowStepServiceTypeMaster = tblWorkflowStepServiceTypeMaster::GetAll([
                    tblWorkflowStepServiceTypeMaster_db::COLUMN_WFSTEPSERVICEID => $item,
                ]);

                foreach ($tblWorkflowStepServiceTypeMaster as $type) {
                    $type->Delete();
                }
            }
        }

        return sizeof($WFServiceTypeArray);
    }
}