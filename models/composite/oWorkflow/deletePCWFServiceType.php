<?php

namespace models\composite\oWorkflow;

use models\Database2;
use models\lendingwise\db\tblPCWFServiceType_db;
use models\lendingwise\db\tblWFServiceTypeMaster_db;
use models\lendingwise\tblPCWFServiceType;
use models\lendingwise\tblWFServiceTypeMaster;
use models\types\strongType;

/**
 *
 */
class deletePCWFServiceType extends strongType
{
    /**
     * @param $inputArray
     * @return int
     */
    public static function getReport($inputArray): int
    {
        $WFId = trim($inputArray['WFId'] ?? 0);
        $PCID = trim($inputArray['PCID'] ?? 0);

        if (!$WFId) {
            return 0;
        }

        $list = tblPCWFServiceType::GetAll([
            tblPCWFServiceType_db::COLUMN_WFID => $WFId
        ]);

        foreach ($list as $item) {
            $item->Delete();
        }

        if (!$PCID) {
            $list = tblWFServiceTypeMaster::GetAll([
                tblWFServiceTypeMaster_db::COLUMN_WFID => $WFId
            ]);

            foreach ($list as $item) {
                $item->Delete();
            }
        }

        return 1;
    }
}