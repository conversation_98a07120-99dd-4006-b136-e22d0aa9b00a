<?php

namespace models\composite\oBroker;

use models\constants\gl\glPCID;
use models\Database2;
use models\constants\brokerStatusArray;
use models\lendingwise\tblAgentPC;
use models\standard\Dates;
use models\types\strongType;

/**
 *
 */
class saveAgentPCs extends strongType
{
    /**
     * @param $inArray
     * @return int
     */
    public static function getReport($inArray): int
    {

        $ucnt = 0;
        $agentNo = $inArray['brokerNumber'];
        $PCID = $inArray['PCID'];

        $userName = $inArray['userName'] ?? '';
        $brokerStatus = intval($inArray['brokerStatus']) ?? 0;
        $brokerNotes = trim($inArray['brokerNotes'] ?? '');

        if (!$PCID || !$agentNo) {
            return 0;
        }
        if ($PCID == glPCID::PCID_PROD_CV3) {
            $brokerStatusArray = brokerStatusArray::$brokerStatusArrayCV3;
        } else {
            $brokerStatusArray = brokerStatusArray::$brokerStatusArray;
        }

        $agent = tblAgentPC::Get([
            'AID'          => $agentNo,
            'PCID'         => $PCID,
            'activeStatus' => 1,
        ]);

        if ($agent) {

            if (array_key_exists('brokerStatus', $inArray)) {

                if ($agent->brokerStatus == $brokerStatus && $agent->brokerNotes == trim($brokerNotes)) {
                    doNothing();
                } else {
                    $agent->activeStatus = 0;
                    $agent->Save();

                    $newAgent = new tblAgentPC();

                    if (isset($inArray['brokerStatus'])) {
                        $newAgent->brokerStatus = intval($inArray['brokerStatus']);
                    } else {
                        $newAgent->brokerStatus = $agent->brokerStatus;
                    }

                    $statusNotes = '';
                    $oldStatus = '';
                    $newStatus = '';
                    if ($agent->brokerStatus) {
                        $oldStatus = $brokerStatusArray[$agent->brokerStatus];
                    }

                    if ($brokerStatus) {
                        $newStatus = $brokerStatusArray[$brokerStatus];
                    }

                    if ($agent->brokerStatus == $brokerStatus) {
                    } else {
                        $statusNotes = 'Broker status changed from ' . $oldStatus . ' to ' . $newStatus . '<br>';
                    }

                    if (array_key_exists('brokerNotes', $inArray)) {
                        $newAgent->brokerNotes = $inArray['brokerNotes'] . '<br>' . $statusNotes . ' - ' . $userName;
                    } else {
                        $newAgent->brokerNotes = $statusNotes . ' - ' . $userName;
                    }

                    if (isset($inArray['expirationDate'])) {
                        $expirationDate = trim(Dates::formatDateWithRE($inArray['expirationDate'], 'MDY', 'Y-m-d'));
                        $newAgent->expirationDate = $expirationDate;
                    }

                    if (isset($inArray['statusDate'])) {
                        $statusDate = trim(Dates::formatDateWithRE($inArray['statusDate'], 'MDY', 'Y-m-d'));
                        $newAgent->statusDate = $statusDate;
                    }

                    $newAgent->AID = $agentNo;
                    $newAgent->PCID = $PCID;
                    $newAgent->recordDate = Dates::Timestamp();
                    $newAgent->Save();
                    $ucnt = 1;
                }
            }

        } else {

            $agent = new tblAgentPC();
            $agent->brokerStatus = $inArray['brokerStatus'] ?? 1;

            if (array_key_exists('brokerNotes', $inArray)) {
                $agent->brokerNotes = $inArray['brokerNotes'];
            }

            if (isset($inArray['expirationDate'])) {
                $expirationDate = trim(Dates::formatDateWithRE($inArray['expirationDate'], 'MDY', 'Y-m-d'));
                $agent->expirationDate = $expirationDate;
            }
            if (isset($inArray['statusDate'])) {
                $statusDate = trim(Dates::formatDateWithRE($inArray['statusDate'], 'MDY', 'Y-m-d'));
                $agent->statusDate = $statusDate;
            }

            $agent->AID = $agentNo;
            $agent->PCID = $PCID;
            $agent->recordDate = Dates::Timestamp();
            $agent->Save();
            $ucnt = 1;

        }

        return $ucnt;
    }
}