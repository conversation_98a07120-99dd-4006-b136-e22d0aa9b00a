<?php

namespace models\composite;

use models\Database2;
use Exception;
use models\lendingwise\tblBudgetAndDraws;
use models\lendingwise\tblCharges;
use models\lendingwise\tblEscrow;
use models\lendingwise\tblFileInterestRateHistory;
use models\lendingwise\tblInvestorFunding;
use models\lendingwise\tblInvestorHistory;
use models\lendingwise\tblLedger;
use models\lendingwise\tblPayment;
use models\lendingwise\tblPrincipalPayDown;
use models\servicing\LoanTerms;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;


// document logic behind calculations, reasoning
// servicing tab - late fee - days after due date
// default interest rate - triggered after 90 days
// short term hard money loans - interest only
// after 90 days non-payment - interest rate goes up to default interest rate
// next payment
// interest history
// divide each escrow item by the months left to pay
// show investor yield


/**
 *
 */
class LoanAmortization extends strongType
{


    /**
     * @param LoanServicing $loan
     * @return LoanPayment[]
     */
    public static function GetAmortizationTable(LoanServicing $loan): array
    {
        $originalBalance = $loan->getFile()->getOriginationBalance();
        $interestRate = $loan->getFile()->getInterestRate(null);
        $firstMonth = $loan->getFile()->getFirstPaymentDueDate();
        $payment = $loan->getFile()->getMonthlyPayment();
        $months = null;
        $mode = 'monthly';
        $payment_mode = null;

        $amort = $loan->getFile()->getAmortization();
        $term = 0;
        if (stristr(substr($amort, -5), 'years') !== false) {
            $term = intval(trim(str_ireplace('years', '', $amort)));
            $amort = 'Years';
        }

        switch ($amort) {
            case 'Years':
                $months = $term * 30;
                break;
            case LoanTerms::INTEREST_ONLY:
                switch ($loan->getFile()->getPaymentMethod()) {
                    case LoanTerms::TLA: // Total Loan Balance
                        $payment = $interestRate / $loan->getFile()->getAccrualDays() / 100.0 * $originalBalance;
                        $mode = 'daily';
                        $payment_mode = 'fixed';
                        $firstMonth = $loan->getFile()->getFundingDate();
                        break;
                    case 'ILA': // Current Balance
                        $mode = 'daily';
                        $payment = 0;
                        $payment_mode = 'draws';
                        break;
                    case '':
                        // use given monthly payment
                        break;
                    default:
                        Debug('unknown PaymentMethod: ' . $loan->getFile()->getPaymentMethod());
                }
                switch ($loan->getFile()->getLoanTerm()) {
                    case '9 Months':
                    case '6 Months':
                        $months = intval(trim(str_ireplace('months', '', $loan->getFile()->getLoanTerm())));
                        break;
                    default:
                        return [];
                }
                break;
            default:
                return [];
        }
        $table = [];
        switch ($mode) {
            case 'monthly':
                for ($j = 0; $j < $months; $j++) {
                    $interest = round($originalBalance * $interestRate / 12.0 / 100.0, 2);
                    if ($payment > $originalBalance + $interest) {
                        $payment = $originalBalance + $interest;
                    }
                    $p = new LoanPayment();
                    $p->MonthNumber = $j + 1;
                    $p->BeginBalance = $originalBalance;
                    $p->Amount = round($payment, 2);
                    $p->Interest = $interest;
                    $p->Principal = round(round($payment, 2) - $p->Interest, 2);
                    $p->DateRecorded = Dates::Datestamp(strtotime('+' . $j . ' months', strtotime($firstMonth)));
                    $p->EndBalance = round($originalBalance - $p->Principal, 2);
                    $table[] = $p;

                    $originalBalance = $p->EndBalance;
                }
                break;
            case 'daily':
                $date_recorded = null;
                $balance_history = [];
                for ($j = 0; $j < $months; $j++) {
                    $date = Dates::Datestamp(strtotime('+' . $j . ' months', strtotime($firstMonth)));
                    $end_date = Dates::Datestamp(strtotime('+' . ($j + 1) . ' months', strtotime($firstMonth)));
                    $d = Dates::DaysDiff($date, $end_date);
                    $interest = 0;
                    $balance = $originalBalance;
                    for ($k = 0; $k < $d; $k++) {
                        $date_recorded = Dates::Datestamp(strtotime('+' . $k . ' days', strtotime($date)));
                        if ($payment_mode == 'draws') {
                            $balance = $loan->getDrawBalance($date_recorded);
                            $payment = round($interestRate / $loan->getFile()->getAccrualDays() / 100.0 * $balance, 2);
                        }
                        $interest += $payment;
                        $balance_history[$date_recorded] = [$balance, $payment, $interest];
                    }
                    $p = new LoanPayment();
                    $p->MonthNumber = $j + 1;
                    $p->BeginBalance = $balance;
                    $p->Amount = round($interest, 2);
                    $p->Interest = $interest;
                    $p->Principal = 0;
                    $p->DateRecorded = $date_recorded;
                    $p->EndBalance = $originalBalance;
                    $table[] = $p;
                }
                //Debug($balance_history);
                break;
        }
        return $table;
    }

    /**
     * @param LoanServicing $loan
     * @param string $source_name
     * @return Ledger[]
     */
    public static function GetSourceReport(LoanServicing $loan, string $source_name): array
    {
        $sql = '
        
SELECT source_name
       , source_id
       , payee_name
       , payee_id
       , amount
       , transactionDatetime
       , ledgerId
       , transactionType
       , yield

FROM (
         SELECT credit_source_name AS source_name
              , credit_source_id   AS source_id
              , debit_source_name  AS payee_name
              , debit_source_id    AS payee_id
              , amount             AS amount
              , transactionDatetime
                , ledgerId
            , transactionType
       , yield
         FROM tblLedger
         WHERE LMRId = :LMRId
           AND credit_source_name LIKE :source_name

         UNION

         SELECT debit_source_name  AS source_name
              , debit_source_id    AS source_id
              , credit_source_name AS payee_name
              , credit_source_id   AS payee_id
              , -amount            AS amount
              , transactionDatetime
                , ledgerId
            , transactionType
       , yield
         FROM tblLedger
         WHERE LMRId = :LMRId
           AND debit_source_name LIKE :source_name
     ) t100        
ORDER BY transactionDatetime, ledgerId
        ';

        if ($source_name === 'investor') {
            $source_name = 'investor%';
        }
        return Database2::getInstance()->queryData($sql, [
            'LMRId' => $loan->getLMRId(),
            'source_name' => $source_name,
        ], function ($row) {
            $l = new Ledger();
            $l->fromData($row);
            return $l;
        });
    }

    /**
     * @param LoanServicing $loan
     * @return array
     */
    public static function GetSummary(LoanServicing $loan): array
    {
        $sql = '
SELECT source_name,
       -- source_id,
       SUM(amount) AS amount
FROM (
         SELECT credit_source_name AS source_name
              -- , credit_source_id   AS source_id
              , SUM(amount)        AS amount
         FROM tblLedger
         WHERE LMRId = :LMRId
         GROUP BY credit_source_name
                

         UNION

         SELECT debit_source_name AS source_name
              -- , debit_source_id   AS source_id
              , -SUM(amount)      AS amount
         FROM tblLedger
         WHERE LMRId = :LMRId
         GROUP BY debit_source_name
                
     ) t100
GROUP BY source_name
       -- , source_id        
        ';
        return Database2::getInstance()->queryData($sql, [
            'LMRId' => $loan->getLMRId()
        ]);
    }

    /**
     * @param LoanServicing $loan
     * @param float $loan_balance
     * @return float
     */
    public static function GetLateFee(LoanServicing $loan, float $loan_balance): float
    {
        $fee = $loan_balance * $loan->getFile()->getLateChargePercent() / 100 / 12;
        return $fee >= $loan->getFile()->getLateChargeMinAmount() ? $fee : $loan->getFile()->getLateChargeMinAmount();
    }

    /**
     * @param LoanServicing $loan
     * @return tblLedger[]
     */
    public static function GetCompleteReport(LoanServicing $loan): array
    {
        $sql = '
SELECT
    *
FROM
    tblLedger
WHERE 
    LMRId = :LMRId
ORDER BY 
    transactionDatetime, ledgerId
        ';
        return Database2::getInstance()->queryData($sql, [
            'LMRId' => $loan->getLMRId(),
        ], function ($row) {
            return new tblLedger($row);
        });
    }

    /**
     * @param LoanServicing $loan
     * @param int|null $parent_id
     * @return tblLedger[]
     */
    public static function GetReport(LoanServicing $loan, ?int $parent_id = null): array
    {
        $sql = '
SELECT
    *
FROM
    tblLedger
WHERE 
    LMRId = :LMRId
    AND (IF(:parent_id = 0, parentLedgerId IS NULL, parentLedgerId = :parent_id))
ORDER BY 
    transactionDatetime, ledgerId
        ';
        return Database2::getInstance()->queryData($sql, [
            'LMRId' => $loan->getLMRId(),
            'parent_id' => $parent_id ?? 0,
        ], function ($row) {
            return new tblLedger($row);
        });
    }

    public const LEDGER_BORROWER = 'borrower';
    public const LEDGER_LENDER = 'lender';
    public const LEDGER_PRINCIPAL = 'principal';
    public const LEDGER_INTEREST = 'interest';
    public const LEDGER_INVESTOR = 'investor';
    public const LEDGER_DRAW = 'draw';
    public const LEDGER_PAY_DOWN = 'pay_down';
    public const LEDGER_CHARGES = 'charges';
    public const LEDGER_ESCROW = 'escrow';
    public const LEDGER_REGULAR_PAYMENT = 'regular_payment';
    public const LEDGER_PRINCIPAL_ESCROW = 'escrow_principal';
    public const LEDGER_ESCROW_TAXES = 'escrow_taxes';
    public const LEDGER_ESCROW_INSURANCE = 'escrow_insurance';
    public const LEDGER_ESCROW_REHAB = 'escrow_rehab';
    public const LEDGER_ESCROW_DEFAULT_FEES = 'escrow_default_fees';

    // escrow_prepaid_% excluded from balance
    public const LEDGER_ESCROW_PREPAID_INTEREST = 'escrow_prepaid_interest';
    public const LEDGER_ESCROW_PREPAID_ESCROW = 'escrow_prepaid_escrow';

    public const LEDGER_ESCROW_INVESTOR = 'investor_escrow';
    public const LEDGER_VESTED_INVESTOR = 'investor_vested';
    public const LEDGER_INVESTOR_INTEREST = 'investor_interest';
    public const LEDGER_LENDER_INTEREST = 'lender_interest';
    public const LEDGER_CLOSING_COSTS = 'closing_costs';
    public const LEDGER_PER_DIEM = 'per_diem';

    public const LEDGER_TRANSACTION_TYPE_PAY_DOWN = 'pay_down';
    public const LEDGER_TRANSACTION_TYPE_DRAW = 'draw';
    public const LEDGER_TRANSACTION_TYPE_CHARGES = 'charges';
    public const LEDGER_TRANSACTION_TYPE_REHAB = 'rehab';
    public const LEDGER_TRANSACTION_TYPE_ESCROW = 'escrow';
    public const LEDGER_TRANSACTION_TYPE_PAYMENT = 'payment';
    public const LEDGER_TRANSACTION_TYPE_FUNDING = 'funding';
    public const LEDGER_TRANSACTION_TYPE_CLOSING_COSTS = 'closing_costs';
    public const LEDGER_TRANSACTION_TYPE_INVESTOR_HISTORY = 'investor_history';
    public const LEDGER_TRANSACTION_TYPE_INVESTOR_FUNDING = 'investor_funding';

    /**
     * @param LoanServicing $loan
     */
    public static function LedgerClear(LoanServicing $loan)
    {
        // remove all existing records
        $sql = '
            DELETE FROM tblLedger WHERE LMRId = :LMRId
        ';
        Database2::getInstance()->executeQuery($sql, [
            'LMRId' => $loan->getLMRId()
        ]);
    }

    /**
     * @param LoanServicing $loan
     */
    public static function LedgerInitializeLoan(LoanServicing $loan)
    {
        // Lender Funding
        $l = new tblLedger();
        $l->LMRId = $loan->getLMRId();
        $l->parentLedgerId = null;
        $l->transactionDatetime = $loan->getFile()->getFundingDate();
        $l->debit_source_name = self::LEDGER_LENDER;
        $l->debit_source_id = $loan->getFile()->FPCID;
        $l->credit_source_name = self::LEDGER_PRINCIPAL;
        $l->credit_source_id = $loan->getLMRId();
        $l->amount = $loan->getFile()->getOriginalBalance() - $loan->getFile()->getClosingCostsFinanced();
        $l->alter_loan_balance = $l->amount;
        $l->transactionType = self::LEDGER_TRANSACTION_TYPE_FUNDING;
        $l->Save();
    }

    /**
     * @param LoanServicing $loan
     * @return int|null
     */
    public static function getTotalInvestorFunds(LoanServicing $loan): ?int
    {
        $total = 0;
        foreach ($loan->getFile()->ActiveInvestors() as $item) {
            foreach ($item->getFundingRecords() as $funding) {
                $total += $funding->FundingAmount;
            }
        }
        return $total;
    }

    /**
     * @param LoanServicing $loan
     */
    public static function LedgerInitializeInvestors(LoanServicing $loan)
    {
        foreach ($loan->getFile()->ActiveInvestors() as $item) {
            $investor = $loan->getFile()->getInvestorInfo($item->InID);
            $investor->deleteHistory(true); // don't remove manual entries
        }

        foreach ($loan->getInvestorHistory() as $item) { // credits
            self::LedgerApplyInvestorHistory($loan, $item);
        }

        foreach ($loan->getInvestorFunding() as $item) { // funding
            self::LedgerApplyInvestorFunding($loan, $item);
        }
    }

    /**
     * @param LoanServicing $loan
     * @param tblEscrow $item
     */
    public static function LedgerApplyEscrow(LoanServicing $loan, tblEscrow $item)
    {
        $l = new tblLedger();
        $l->LMRId = $loan->getLMRId();
        $l->parentLedgerId = null;
        $l->transactionDatetime = $item->dueDate;
        switch ($item->escrowType) {
            case 'taxes':
                $l->credit_source_name = self::LEDGER_ESCROW_TAXES;
                break;
            case 'insurance':
                $l->credit_source_name = self::LEDGER_ESCROW_INSURANCE;
                break;
            default:
                $l->credit_source_name = self::LEDGER_ESCROW;
                break;
        }
        $l->credit_source_id = $item->tblEscrowId;
        $l->debit_source_name = self::LEDGER_ESCROW_PREPAID_ESCROW;
        $l->debit_source_id = $loan->getLMRId();
        $l->amount = $item->amount;
        $l->alter_loan_balance = 0;
        $l->transactionType = self::LEDGER_TRANSACTION_TYPE_ESCROW;
        $l->Save();
    }

    /**
     * @param LoanServicing $loan
     * @param tblPayment $item
     * @param int $interestPaymentLedgerId
     * @param float $percentPaid
     * @return float|int|null
     */
    public static function payoutInvestorInterest(
        LoanServicing $loan,
        tblPayment    $item,
        int           $interestPaymentLedgerId,
        float         $percentPaid
    ): float
    {
        $months = Dates::MonthsBetween($item->dueDate, $item->paymentDate);
        $endDate = Dates::Datestamp(strtotime('+ ' . $months . ' months', strtotime(Dates::Datestamp($item->dueDate))));

        $temp = $loan->getInvestorInterest($endDate);
        $investorInterest = $temp['interest'];
        $investorInvested = $temp['invested'];

        $totalInvestorInterest = 0;
        foreach ($investorInterest as $InID => $amount) {
            if (!$amount) {
                continue;
            }
            $investorTotal = $investorInvested[$InID] ?? 0;
            if (!$investorTotal) {
                continue;
            }

            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = $interestPaymentLedgerId;
            $l->transactionDatetime = $item->paymentDate;
            $l->credit_source_name = self::LEDGER_INVESTOR_INTEREST;
            $l->credit_source_id = $InID;
            $l->debit_source_name = self::LEDGER_INTEREST;
            $l->debit_source_id = null;
            $l->amount = round($amount * $percentPaid, 2);
            $l->alter_loan_balance = 0;
            $l->yield = $l->amount * 100.0 * 12.0 / $investorTotal;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
            $l->Save();
            $totalInvestorInterest += $l->amount;
        }
        return $totalInvestorInterest;
    }

    /**
     * @param LoanServicing $loan
     * @param tblPayment $item
     * @param int|null $parentLedgerId
     * @param float $paymentBalance
     * @param string $credit_source_name
     * @return float|null
     */
    public static function payoutEscrow(
        LoanServicing $loan,
        tblPayment    $item,
        ?int          $parentLedgerId,
        float         $paymentBalance,
        string        $credit_source_name
    ): ?float
    {
        $escrows = $loan->getEscrowRecords();

        $prepaidLedgerId = $parentLedgerId;

        // this is not a thing 9/28
//        if ($item->prepaidEscrowAmount) {
//            $paymentBalance += $item->prepaidEscrowAmount;
//
//            $l = new tblLedger();
//            $l->LMRId = $loan->getLMRId();
//            $l->parentLedgerId = null;
//            $l->transactionDatetime = $item->paymentDate;
//            $l->debit_source_name = self::LEDGER_ESCROW_PREPAID_ESCROW;
//            $l->debit_source_id = null;
//            $l->credit_source_name = $credit_source_name; // self::LEDGER_BORROWER;
//            $l->credit_source_id = null;
//            $l->amount = floatval($item->prepaidEscrowAmount);
//            $l->alter_loan_balance = 0;
//            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
//            $res = $l->Save();
//            if ($res['error']) {
//                Debug($res);
//            }
//            $prepaidLedgerId = $res['last_id'];
//        }

        foreach ($escrows as $escrow) {
            $escrow_payment = $escrow->getMonthlyPayment($item->dueDate);

            if ($escrow_payment > $paymentBalance) {
                $escrow_payment = $paymentBalance;
            }
            if (!$escrow_payment) {
                continue;
            }

            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = $prepaidLedgerId;
            $l->transactionDatetime = $item->paymentDate;
            switch ($escrow->escrowType) {
                case 'taxes':
                    $l->debit_source_name = self::LEDGER_ESCROW_TAXES;
                    break;
                case 'insurance':
                    $l->debit_source_name = self::LEDGER_ESCROW_INSURANCE;
                    break;
                default:
                    $l->debit_source_name = self::LEDGER_ESCROW;
                    break;
            }
            $l->debit_source_id = $escrow->tblEscrowId;
            $l->credit_source_name = $credit_source_name; // self::LEDGER_BORROWER;
            $l->credit_source_id = null;
            $l->amount = $escrow_payment;
            $l->alter_loan_balance = 0;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
            $res = $l->Save();
            if ($res['error']) {
                Debug($res);
            }
            $paymentBalance -= $l->amount;


        }
        return $paymentBalance;
    }

    /**
     * @param LoanServicing $loan
     * @param tblPayment $item
     */
    public static function LedgerApplyPayment(LoanServicing $loan, tblPayment $item)
    {
        $fundingInterestOwed = $loan->getFile()->getFundingInterest();

        $months = Dates::MonthsBetween($item->dueDate, $item->paymentDate);
        $endDate = Dates::Datestamp(strtotime('+ ' . $months . ' months', strtotime(Dates::Datestamp($item->dueDate))));

        if(strtotime($item->dueDate) < strtotime($loan->getFile()->getFirstPaymentDueDate())) {
            $regularInterestOwed = 0;
            $regularInterestPaid = 0;
        } else {

            $regularInterestOwed = $loan->getTotalInterestOwed($endDate) + $fundingInterestOwed;
            $regularInterestPaid = $loan->getTotalInterestPayments($endDate);
        }

        $interestOwed = $regularInterestOwed - $regularInterestPaid;

        // Debug($item, $interestOwed, $regularInterestOwed, $regularInterestPaid, $loan->getFile()->getFirstPaymentDueDate());

        $totalInterestOwed = $interestOwed;
        $maxDefaultEscrowAmount = 0;
        $paymentDue = LoanPaymentDue::GetPaymentDue(
            $loan,
            $item->dueDate,
            $item->paymentDate
        );

        // total default amount collected
        $defaultFeeOwed = $paymentDue->DefaultFee;

        // double counted
        $paymentDue->paymentCash -= ($paymentDue->LateFee + $paymentDue->DefaultFee);
        $paymentDue->ChargesAmount -= ($paymentDue->LateFee + $paymentDue->DefaultFee);

        if ($paymentDue->IsDefault) {
            // fee beyond actual interest owed
            $maxDefaultEscrowAmount = round($paymentDue->DefaultFee - $interestOwed, 2);
        }

        $prePaidInterestReserve = $item->prepaidInterestAmount;


        $parentLedgerId = null;

        // log regular payment
        if ($item->amount) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = $item->paymentDate;
            $l->credit_source_name = self::LEDGER_REGULAR_PAYMENT;
            $l->credit_source_id = $item->tblPaymentId;
            $l->debit_source_name = self::LEDGER_BORROWER;
            $l->debit_source_id = null;
            $l->amount = $item->amount;
            $l->alter_loan_balance = 0;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
            $res = $l->Save();
            if ($res['error']) {
                Debug($res);
            }
            $parentLedgerId = $res['last_id'];
            $paymentBalance = $item->amount;

            // pay charges
            $charges = $loan->getChargesOwed($item->dueDate);

            foreach ($charges as $charge) {
                $charge_payment = $charge->_owed;

                if ($charge_payment > $paymentBalance) {
                    $charge_payment = $paymentBalance;
                }
                if (!$charge_payment) {
                    continue;
                }

                if (Dates::DateToInt($charge->dueDate) > Dates::DateToInt($item->dueDate)) {
                    continue;
                }

                // whatever payment is entered first, pays the charge
//                if ($charge->description == 'Late Fee' && !$item->lateFee) {
//                    continue;
//                }

                if ($charge->description == 'Default Interest') {
                    if ($paymentBalance > $item->lateFee && $maxDefaultEscrowAmount > 0) {
                        $l = new tblLedger();
                        $l->LMRId = $loan->getLMRId();
                        $l->parentLedgerId = $parentLedgerId;
                        $l->transactionDatetime = $item->paymentDate;
                        $l->debit_source_name = self::LEDGER_CHARGES;
                        $l->debit_source_id = $charge->tblChargesId;
                        $l->credit_source_name = self::LEDGER_BORROWER;
                        $l->credit_source_id = null;
                        $l->amount = $paymentBalance - $item->lateFee >= $maxDefaultEscrowAmount ? $maxDefaultEscrowAmount : $paymentBalance - $item->lateFee;
                        $l->alter_loan_balance = 0;
                        $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                        $res = $l->Save();
                        if ($res['error']) {
                            Debug($res);
                        }
                        $defaultFeeOwed -= $l->amount;
                    }

                    if ($defaultFeeOwed) {
                        $l = new tblLedger();
                        $l->LMRId = $loan->getLMRId();
                        $l->parentLedgerId = null;
                        $l->transactionDatetime = $item->paymentDate;
                        $l->debit_source_name = self::LEDGER_CHARGES;
                        $l->debit_source_id = $charge->tblChargesId;
                        $l->credit_source_name = self::LEDGER_BORROWER;
                        $l->credit_source_id = null;
                        $l->amount = $defaultFeeOwed;
                        $l->alter_loan_balance = 0;
                        $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                        $res = $l->Save();
                        if ($res['error']) {
                            Debug($res);
                        }
                        $defaultFeeOwed -= $l->amount;
                        $prePaidInterestReserve -= $l->amount;
                    }
                }

                if ($charge->description != 'Default Interest') {
                    $l = new tblLedger();
                    $l->LMRId = $loan->getLMRId();
                    $l->parentLedgerId = $parentLedgerId;
                    $l->transactionDatetime = $item->paymentDate;
                    $l->debit_source_name = self::LEDGER_CHARGES;
                    $l->debit_source_id = $charge->tblChargesId;
                    $l->credit_source_name = self::LEDGER_BORROWER;
                    $l->credit_source_id = null;
                    $l->amount = $charge_payment;
                    $l->alter_loan_balance = 0;
                    $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                    $res = $l->Save();
                    if ($res['error']) {
                        Debug($res);
                    }
                    $paymentBalance -= $charge_payment;
                    $item->amount -= $charge_payment;
                }
            }

            if ($item->escrowAmount > 0) {
                $l = new tblLedger();
                $l->LMRId = $loan->getLMRId();
                $l->parentLedgerId = $parentLedgerId;
                $l->transactionDatetime = $item->paymentDate;
                $l->credit_source_name = self::LEDGER_ESCROW_PREPAID_ESCROW;
                $l->credit_source_id = null;
                $l->debit_source_name = self::LEDGER_REGULAR_PAYMENT;
                $l->debit_source_id = null;
                $l->amount = $item->escrowAmount;
                $l->alter_loan_balance = 0;
                $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                $res = $l->Save();
                if ($res['error']) {
                    Debug($res);
                }
                $item->amount -= $l->amount;
            }

            if ($item->additionalEscrow > 0) {
                $l = new tblLedger();
                $l->LMRId = $loan->getLMRId();
                $l->parentLedgerId = $parentLedgerId;
                $l->transactionDatetime = $item->paymentDate;
                $l->credit_source_name = self::LEDGER_ESCROW_PREPAID_ESCROW;
                $l->credit_source_id = null;
                $l->debit_source_name = self::LEDGER_REGULAR_PAYMENT;
                $l->debit_source_id = null;
                $l->amount = $item->additionalEscrow;
                $l->alter_loan_balance = 0;
                $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                $res = $l->Save();
                if ($res['error']) {
                    Debug($res);
                }
                $item->amount -= $l->amount;
            }

            if ($item->perDiemPayment) {
                $l = new tblLedger();
                $l->LMRId = $loan->getLMRId();
                $l->parentLedgerId = $parentLedgerId;
                $l->transactionDatetime = $item->paymentDate;
                $l->credit_source_name = self::LEDGER_PER_DIEM;
                $l->credit_source_id = null;
                $l->debit_source_name = self::LEDGER_REGULAR_PAYMENT;
                $l->debit_source_id = null;
                $l->amount = $item->perDiemPayment;
                $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                $res = $l->Save();
                if ($res['error']) {
                    Debug($res);
                }
                $item->amount -= $l->amount;
            }

            if ($paymentDue->Principal) {
                $l = new tblLedger();
                $l->LMRId = $loan->getLMRId();
                $l->parentLedgerId = $parentLedgerId;
                $l->transactionDatetime = $item->paymentDate;
                $l->credit_source_name = self::LEDGER_PRINCIPAL;
                $l->credit_source_id = null;
                $l->debit_source_name = self::LEDGER_REGULAR_PAYMENT;
                $l->debit_source_id = null;
                $l->amount = $paymentDue->Principal;
                $l->alter_loan_balance = -$paymentDue->Principal;
                $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                $res = $l->Save();
                if ($res['error']) {
                    Debug($res);
                }
                $item->amount -= $l->amount;
            }
            $item->amount = round($item->amount, 2);

//            if($item->paymentDate === '2023-05-01') {
//                Debug($interestOwed, $paymentDue, $item,$regularInterestOwed, $regularInterestPaid);
//            }


            if ($interestOwed > 0 && $item->amount) {

                $l = new tblLedger();
                $l->LMRId = $loan->getLMRId();
                $l->parentLedgerId = $parentLedgerId;
                $l->transactionDatetime = $item->paymentDate;
                $l->credit_source_name = self::LEDGER_INTEREST;
                $l->credit_source_id = null;
                $l->debit_source_name = self::LEDGER_REGULAR_PAYMENT;
                $l->debit_source_id = null;
                $l->amount = $item->amount - $item->lateFee > $interestOwed ? $interestOwed : $item->amount - $item->lateFee;
                $l->alter_loan_balance = 0;
                $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;

                $res = $l->Save();
                if ($res['error']) {
                    Debug($res);
                }
                $interestPaymentLedgerId = $res['last_id'];

                $item->amount -= $l->amount;
                $item->amount = round($item->amount, 2);

                $totalInterestPaid = $l->amount;
                $totalInterestPaidToInvestors = self::payoutInvestorInterest(
                    $loan,
                    $item,
                    $interestPaymentLedgerId,
                    $l->amount / $totalInterestOwed
                );

                if ($totalInterestPaid - $totalInterestPaidToInvestors > 0) {
                    $l = new tblLedger();
                    $l->LMRId = $loan->getLMRId();
                    $l->parentLedgerId = $interestPaymentLedgerId;
                    $l->transactionDatetime = $item->paymentDate;
                    $l->credit_source_name = self::LEDGER_LENDER_INTEREST;
                    $l->credit_source_id = null;
                    $l->debit_source_name = self::LEDGER_INTEREST;
                    $l->debit_source_id = $item->tblPaymentId;
                    $l->amount = $totalInterestPaid - $totalInterestPaidToInvestors;
                    $l->alter_loan_balance = 0;
                    $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                    $res = $l->Save();
                    if ($res['error']) {
                        Debug($res);
                    }
                }

            }
        }

        // put all escrow payments into prepaid escrow
//        if($item->escrowAmount > 0) {
//            self::payoutEscrow(
//                $loan,
//                $item,
//                $parentLedgerId,
//                $item->escrowAmount,
//                self::LEDGER_BORROWER
//            );
//        }
        if ($maxDefaultEscrowAmount > $item->amount && $prePaidInterestReserve > 0) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = $item->paymentDate;
            $l->debit_source_name = self::LEDGER_ESCROW_PREPAID_INTEREST;
            $l->debit_source_id = $item->tblPaymentId;
            $l->credit_source_name = self::LEDGER_ESCROW_DEFAULT_FEES;
            $l->credit_source_id = null;
            $l->amount = $prePaidInterestReserve >= $maxDefaultEscrowAmount ? $maxDefaultEscrowAmount : $prePaidInterestReserve;
            $l->alter_loan_balance = 0;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
            $res = $l->Save();
            if ($res['error']) {
                Debug($res);
            }
            $maxDefaultEscrowAmount -= $l->amount;
            $prePaidInterestReserve -= $l->amount;
        }


        // log prepaid interest payment
        if ($interestOwed > 0 && $prePaidInterestReserve > 0) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = $item->paymentDate;
            $l->credit_source_name = self::LEDGER_INTEREST;
            $l->credit_source_id = null;
            $l->debit_source_name = self::LEDGER_ESCROW_PREPAID_INTEREST;
            $l->debit_source_id = null;
            $l->amount = $prePaidInterestReserve > $interestOwed ? $interestOwed : $prePaidInterestReserve;
            $l->alter_loan_balance = 0;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
            $res = $l->Save();

            $prePaidInterestReserve -= $l->amount;

            $interestPaymentLedgerId = $res['last_id'];
            if ($res['error']) {
                Debug($res);
            }
            $totalInterestPaid = $l->amount;
            $totalInterestPaidToInvestors = self::payoutInvestorInterest(
                $loan,
                $item,
                $interestPaymentLedgerId,
                $l->amount / $totalInterestOwed
            );


            if ($totalInterestPaid - $totalInterestPaidToInvestors > 0) {
                $l = new tblLedger();
                $l->LMRId = $loan->getLMRId();
                $l->parentLedgerId = $interestPaymentLedgerId;
                $l->transactionDatetime = $item->paymentDate;
                $l->credit_source_name = self::LEDGER_LENDER_INTEREST;
                $l->credit_source_id = null;
                $l->debit_source_name = self::LEDGER_INTEREST;
                $l->debit_source_id = $item->tblPaymentId;
                $l->amount = $totalInterestPaid - $totalInterestPaidToInvestors;
                $l->alter_loan_balance = 0;
                $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
                $res = $l->Save();
                if ($res['error']) {
                    Debug($res);
                }
            }

        }

        // $paymentBalance = $item->amount;

        // if we have prepaid interest left, credit the account
        if ($prePaidInterestReserve > 0) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = $parentLedgerId;
            $l->transactionDatetime = $item->paymentDate;
            $l->debit_source_name = self::LEDGER_REGULAR_PAYMENT;
            $l->debit_source_id = $item->tblPaymentId;
            $l->credit_source_name = self::LEDGER_ESCROW_PREPAID_INTEREST;
            $l->credit_source_id = null;
            $l->amount = $prePaidInterestReserve;
            $l->alter_loan_balance = 0;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
            $l->Save();
        }

        if($item->additionalPrincipal) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = $parentLedgerId;
            $l->transactionDatetime = $item->paymentDate;
            $l->debit_source_name = self::LEDGER_REGULAR_PAYMENT;
            $l->debit_source_id = $item->tblPaymentId;
            $l->credit_source_name = self::LEDGER_PRINCIPAL;
            $l->credit_source_id = null;
            $l->amount = $item->additionalPrincipal;
            $l->alter_loan_balance = -$item->additionalPrincipal;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAYMENT;
            $l->Save();
        }
    }

    public static function LedgerApplyInvestorFunding(LoanServicing $loan, tblInvestorFunding $item)
    {
        $l = new tblLedger();
        $l->LMRId = $loan->getLMRId();
        $l->parentLedgerId = null;
        $l->transactionDatetime = $item->FundingDate;
        if ($item->FundingAmount < 0) {
            $l->credit_source_name = self::LEDGER_ESCROW_INVESTOR;
            $l->credit_source_id = $item->InID;
            $l->debit_source_name = self::LEDGER_VESTED_INVESTOR;
        } else {
            $l->credit_source_name = self::LEDGER_VESTED_INVESTOR;
            $l->credit_source_id = $item->InID;
            $l->debit_source_name = self::LEDGER_ESCROW_INVESTOR;
        }
        $l->debit_source_id = $item->InID;
        $l->amount = abs($item->FundingAmount);
        $l->alter_loan_balance = 0;
        $l->transactionType = self::LEDGER_TRANSACTION_TYPE_INVESTOR_HISTORY;
        $l->Save();
    }

    /**
     * @param LoanServicing $loan
     * @param tblInvestorHistory $item
     */
    public static function LedgerApplyInvestorHistory(LoanServicing $loan, tblInvestorHistory $item)
    {
        $l = new tblLedger();
        $l->LMRId = $loan->getLMRId();
        $l->parentLedgerId = null;
        $l->transactionDatetime = $item->dateApplied;
        if ($item->amount < 0) {
            $l->credit_source_name = self::LEDGER_ESCROW_INVESTOR;
            $l->credit_source_id = $item->InID;
            $l->debit_source_name = self::LEDGER_VESTED_INVESTOR;
        } else {
            $l->credit_source_name = self::LEDGER_VESTED_INVESTOR;
            $l->credit_source_id = $item->InID;
            $l->debit_source_name = self::LEDGER_ESCROW_INVESTOR;
        }
        $l->debit_source_id = $item->InID;
        $l->amount = abs($item->amount);
        $l->alter_loan_balance = 0;
        $l->transactionType = self::LEDGER_TRANSACTION_TYPE_INVESTOR_HISTORY;
        $l->Save();
    }

    /**
     * @param LoanServicing $loan
     * @param float $totalAmount
     * @param int|null $parentLedgerId
     * @param string $debit_source_name
     * @param int|null $debit_source_id
     * @param string|null $date
     * @param string|null $transactionType
     */
    public static function LedgerVestInvestor(
        LoanServicing $loan,
        float         $totalAmount,
        ?int           $parentLedgerId,
        string        $debit_source_name,
        ?int           $debit_source_id,
        ?string        $date,
        ?string        $transactionType
    )
    {
        if(!$date) {
            return;
        }

        $list = [];
        $investorYield = [];
        $loan_balance = $loan->getCurrentLoanBalance($date);

        foreach ($loan->getFile()->ActiveInvestors() as $inv) {
            $investor = $loan->getFile()->getInvestorInfo($inv->InID);
            $invested = 0;
            $interest = 0;
            foreach ($investor->getTblInvestorFunding_by_InID($date) as $hist) {
                $invested += $hist->FundingAmount;
                $interest += $hist->InvestorYield / 100.0 * $hist->FundingAmount;
            }
            if(!$invested) {
                continue;
            }
            $list[$investor->InID] = round($invested * $totalAmount / $loan_balance, 2);
            $investorYield[$investor->InID] = round($interest * 100.0 / $invested, 3);
        }

        foreach ($list as $InID => $amount) {
            if(!$amount) {
                continue;
            }

            $history = new tblLedger();
            $history->LMRId = $loan->getLMRId();
            $history->parentLedgerId = $parentLedgerId;
            $history->transactionDatetime = Dates::Datestamp($date);
            $history->credit_source_id = $InID;
            $history->credit_source_name = self::LEDGER_INVESTOR;
            $history->debit_source_id = $debit_source_id;
            $history->debit_source_name = $debit_source_name;
            $history->amount = $amount;
            $history->yield = $investorYield[$InID];
            $history->transactionType = $transactionType;
            $history->Save();
        }
    }

    /**
     * @param LoanServicing $loan
     * @param tblPrincipalPayDown $item
     */
    public static function LedgerApplyPayDown(LoanServicing $loan, tblPrincipalPayDown $item)
    {
        $rehab_balance = $loan->getFile()->getRehabCostFinanced() - $loan->getRehabLimit($item->principalPayDownDate);

        $rehab_paydown = $item->principalPayDownAmount > $rehab_balance ? $rehab_balance : $item->principalPayDownAmount;
        $principal_paydown = $item->principalPayDownAmount - $rehab_paydown;

        if ($rehab_paydown > 0) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = Dates::Datestamp($item->principalPayDownDate);
            $l->debit_source_name = self::LEDGER_BORROWER;
            $l->debit_source_id = $loan->getLMRId();
            $l->credit_source_name = self::LEDGER_ESCROW_REHAB;
            $l->credit_source_id = $item->ID;
            $l->amount = $rehab_paydown;
            $l->alter_loan_balance = -$rehab_paydown;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAY_DOWN;
            $l->Save();
        }

        if ($principal_paydown > 0) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = Dates::Datestamp($item->principalPayDownDate);
            $l->debit_source_name = self::LEDGER_BORROWER;
            $l->debit_source_id = $loan->getLMRId();
            $l->credit_source_name = self::LEDGER_PRINCIPAL;
            $l->credit_source_id = $item->ID;
            $l->amount = $principal_paydown;
            $l->alter_loan_balance = -$principal_paydown;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_PAY_DOWN;
            $l->Save();

            self::LedgerVestInvestor(
                $loan,
                $item->principalPayDownAmount,
                $l->ledgerId,
                self::LEDGER_PRINCIPAL,
                $item->ID,
                $item->principalPayDownDate,
                self::LEDGER_TRANSACTION_TYPE_PAY_DOWN
            );
        }
    }

    /**
     * @param LoanServicing $loan
     * @param tblBudgetAndDraws $item
     */
    public static function LedgerApplyDraw(LoanServicing $loan, tblBudgetAndDraws $item)
    {
        if (Dates::IsEmpty($item->dateFunded)) {
            return;
        }

        $l = new tblLedger();
        $l->LMRId = $loan->getLMRId();
        $l->parentLedgerId = null;
        $l->transactionDatetime = Dates::Datestamp($item->dateFunded);
        $l->debit_source_name = self::LEDGER_ESCROW_REHAB;
        $l->debit_source_id = $item->BDID;
        $l->credit_source_name = self::LEDGER_BORROWER;
        $l->credit_source_id = $loan->getLMRId();
        $l->amount = $item->drawFunded;
        $l->alter_loan_balance = $l->amount;
        $l->transactionType = self::LEDGER_TRANSACTION_TYPE_DRAW;
        $l->Save();

        self::LedgerVestInvestor(
            $loan,
            $item->drawApproved,
            $l->ledgerId,
            self::LEDGER_ESCROW_REHAB,
            $item->BDID,
            $item->dateFunded,
            self::LEDGER_TRANSACTION_TYPE_DRAW
        );
    }

    /**
     * @param LoanServicing $loan
     * @param tblCharges $item
     */
    public static function LedgerApplyCharge(LoanServicing $loan, tblCharges $item)
    {
        $l = new tblLedger();
        $l->LMRId = $loan->getLMRId();
        $l->parentLedgerId = null;
        $l->transactionDatetime = $item->dueDate;
        $l->credit_source_name = self::LEDGER_CHARGES;
        $l->credit_source_id = $item->tblChargesId;
        $l->debit_source_name = self::LEDGER_BORROWER;
        $l->debit_source_id = $loan->getLMRId();
        $l->amount = $item->amount;
        $l->alter_loan_balance = 0;
        $l->transactionType = self::LEDGER_TRANSACTION_TYPE_CHARGES;
        $l->Save();
    }

    /**
     * @param LoanServicing $loan
     * @throws Exception
     */
    public static function ConvertToServicing(LoanServicing $loan): void
    {
        self::LedgerClear($loan);

        self::LedgerInitializeLoan($loan);
        self::LedgerInitializeInvestors($loan);

        // initialize interest rate to origination rate
        $check = tblFileInterestRateHistory::Get([
            'LMRId' => $loan->getFile()->LMRId,
            'apply_date' => $loan->getFile()->getFundingDate(),
        ]);
        if ($check) {
            $sql = '
            DELETE FROM 
               tblFileInterestRateHistory 
            WHERE 
               LMRId = :LMRId
                AND apply_date = :apply_date
            ';
            Database2::getInstance()->executeQuery($sql, [
                'LMRId' => $loan->getFile()->LMRId,
                'apply_date' => $loan->getFile()->getFundingDate(),
            ]);
        }
        $check = new tblFileInterestRateHistory();
        $check->LMRId = $loan->getFile()->LMRId;
        $check->apply_date = $loan->getFile()->getFundingDate();
        $check->interest_rate = Strings::replaceCommaValues($loan->getFile()->lien1Rate);
        $check->Save();


        if ($loan->getFile()->getRehabCostFinanced()) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = $loan->getFile()->getFundingDate();
            $l->debit_source_name = self::LEDGER_LENDER;
            $l->debit_source_id = null;
            $l->credit_source_name = self::LEDGER_ESCROW_REHAB;
            $l->credit_source_id = null;
            $l->amount = $loan->getFile()->getRehabCostFinanced();
            $l->alter_loan_balance = 0;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_ESCROW;
            $l->Save();

            if ($loan->getFile()->getPaymentMethod() !== LoanTerms::ILA) {
                self::LedgerVestInvestor(
                    $loan,
                    $l->amount,
                    $l->ledgerId,
                    self::LEDGER_ESCROW_REHAB,
                    null,
                    $l->transactionDatetime,
                    self::LEDGER_TRANSACTION_TYPE_REHAB
                );
            }
        }

        if ($loan->getFile()->getPrepaidInterestReserve()) {

            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = $loan->getFile()->getFundingDate();
            $l->debit_source_name = self::LEDGER_BORROWER;
            $l->debit_source_id = null;
            $l->credit_source_name = self::LEDGER_ESCROW_PREPAID_INTEREST;
            $l->credit_source_id = null;
            $l->amount = $loan->getFile()->getPrepaidInterestReserve();
            $l->alter_loan_balance = 0;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_ESCROW;
            $l->Save();
        }

        if ($loan->getFile()->getPrepaidEscrow()) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = $loan->getFile()->getFundingDate();
            $l->debit_source_name = self::LEDGER_BORROWER;
            $l->debit_source_id = null;
            $l->credit_source_name = self::LEDGER_ESCROW_PREPAID_ESCROW;
            $l->credit_source_id = null;
            $l->amount = $loan->getFile()->getPrepaidEscrow();
            $l->alter_loan_balance = 0;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_ESCROW;
            $l->Save();
        }

        if ($loan->getFile()->getClosingCostsFinanced()) {
            $l = new tblLedger();
            $l->LMRId = $loan->getLMRId();
            $l->parentLedgerId = null;
            $l->transactionDatetime = $loan->getFile()->getFundingDate();
            $l->debit_source_name = self::LEDGER_BORROWER;
            $l->debit_source_id = null;
            $l->credit_source_name = self::LEDGER_CLOSING_COSTS;
            $l->credit_source_id = null;
            $l->amount = $loan->getFile()->getClosingCostsFinanced();
            $l->alter_loan_balance = $l->amount;
            $l->transactionType = self::LEDGER_TRANSACTION_TYPE_CLOSING_COSTS;
            $l->Save();

            self::LedgerVestInvestor(
                $loan,
                $loan->getFile()->getClosingCostsFinanced(),
                $l->ledgerId,
                self::LEDGER_ESCROW_PREPAID_ESCROW,
                null,
                $loan->getFile()->getFundingDate(),
                self::LEDGER_TRANSACTION_TYPE_ESCROW
            );
        }

        $paymentHistory = $loan->getPaymentHistory();
        foreach ($paymentHistory as $item) {

            // make sure charges have been applied for default
            if ($item->defaultChargesAmount) {
                $charge = tblCharges::getForPaymentDescription($item->tblPaymentId, 'Default Interest');
                $charge->tblPaymentId = $item->tblPaymentId;
                $charge->sourceKey = 'system';
                $charge->description = 'Default Interest';
                $charge->amount = round($item->defaultChargesAmount, 2);
                $charge->dueDate = $item->dueDate;
                $charge->LMRId = $loan->getFile()->LMRId;
                $charge->Save();
            }

            // make sure charges have been applied for late fees
            if ($item->lateFee) {
                $charge = tblCharges::getForPaymentDescription($item->tblPaymentId, 'Late Fee');
                $charge->tblPaymentId = $item->tblPaymentId;
                $charge->sourceKey = 'system';
                $charge->description = 'Late Fee';
                $charge->amount = $item->lateFee;
                $charge->dueDate = $item->dueDate;
                $charge->LMRId = $loan->getFile()->LMRId;
                $charge->Save();
            }
        }

        foreach ($loan->getFile()->getDraws() as $item) {
            self::LedgerApplyDraw($loan, $item);
        }

        foreach ($loan->getFile()->getTblPrincipalPayDown_by_LMRID() as $item) {
            self::LedgerApplyPayDown($loan, $item);
        }

        foreach ($loan->getChargesRecords() as $item) {
            self::LedgerApplyCharge($loan, $item);
        }

        foreach ($loan->getEscrowRecords() as $item) {
            self::LedgerApplyEscrow($loan, $item);
        }

        foreach ($loan->getPaymentHistory() as $item) {
            self::LedgerApplyPayment($loan, $item);
        }

        $PaymentDueDate = Dates::Datestamp(LoanPaymentDue::GetPaymentDueDate($loan), '');
        $PaymentDate = null;
        $paymentDue = LoanPaymentDue::GetPaymentDue(
            $loan,
            $PaymentDueDate,
            $PaymentDate
        );
        $paymentDue->Save();
    }
}