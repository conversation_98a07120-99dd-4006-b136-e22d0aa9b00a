<?php

namespace models\composite\oBranch;

use models\Database2;
use models\composite\oBroker\saveAgentPCs;
use models\lendingwise\tblAgentBranch;
use models\standard\Dates;
use models\types\strongType;

/**
 *
 */
class savePreferredAgentForBranch extends strongType
{
    /**
     * savePreferredAgentForBranch
     * Insert branch ID to Broker while add new/existing broker in admin.
     *
     * @param mixed $ip
     * @access public
     *
     * <AUTHOR> <<EMAIL>>
     * @example
     */
    public static function getReport($ip): int
    {
        $contactDate = Dates::Datestamp();
        $branchIdArray = explode(', ', $ip['branchId']);
        $agentId = $ip['agentId'];
        $PCID = $ip['PCID'];
        $PCID = $ip['BPCID'] ?? $PCID;

        if (!sizeof($branchIdArray)) {
            return 0;
        }

        $cnt = 0;
        foreach ($branchIdArray as $BID) {
            if(!$BID) {
                continue;
            }

            $item = new tblAgentBranch();
            $item->BID = $BID;
            $item->brokerNumber = $agentId;
            $item->contactDate = $contactDate;
            $item->PCID = $PCID;
            $item->Save();
            $cnt++;
        }

        saveAgentPCs::getReport([
            'PCID' => $PCID,
            'brokerNumber' => $agentId
        ]);
        return $cnt;
    }
}