<?php

namespace models\composite\oSubstatus;

use models\Database2;
use models\types\strongType;

/**
 *
 */
class getPCFileSubstatus extends strongType
{
    /**
     * @param $inputArray
     * @return array
     */
    public static function getReport($inputArray): array
    {
        $PCID = 0;
        $PFSID = 0;
        $opt1 = '';
        $PCFileSubstatusArray = $PCSubstatusInfo = $PCModuleInfo = $res = [];
        $opt2 = '';
        $PCModuleArray = [];
        $PCSubstatusCategory = $PCSubstatusCatInfo = [];
        $searchTerm = '';
        $keyNeeded = '';
        $searchCategory = '';

        if (array_key_exists('PCID', $inputArray)) $PCID = trim($inputArray['PCID']);
        if (array_key_exists('PFSID', $inputArray)) $PFSID = $inputArray['PFSID'];
        if (array_key_exists('opt1', $inputArray)) $opt1 = trim($inputArray['opt1']);
        if (array_key_exists('opt2', $inputArray)) $opt2 = trim($inputArray['opt2']);
        if (array_key_exists('searchTerm', $inputArray)) $searchTerm = trim($inputArray['searchTerm']);
        if (array_key_exists('searchCategory', $inputArray)) $searchCategory = trim($inputArray['searchCategory']);
        if (array_key_exists('keyNeeded', $inputArray)) $keyNeeded = trim($inputArray['keyNeeded']);

        if ($PFSID == 0) {
            $PFSID = '';
        }

        if (is_array($PFSID)) $PFSID = implode(',', $PFSID);

        if ($searchCategory == 0) {
            $searchCategory = '';
        }

        $qry = 'CALL SP_GetPCSubstatusWithModules(
        :PCID
        , :PFSID
        , :opt1
        , :opt2
        , :searchTerm
        , :searchCategory);';
        $params = ['PCID' => $PCID, 'PFSID' => $PFSID, 'opt1' => $opt1, 'opt2' => $opt2, 'searchTerm' => $searchTerm, 'searchCategory' => $searchCategory];

        $result = Database2::getInstance()->multiQueryData($qry, 'myOpt', $params);

        if (count($result) > 0) {
            if (array_key_exists('PCSubstatus', $result)) $PCSubstatusInfo = $result['PCSubstatus'];
            if (array_key_exists('PCModuleInfo', $result)) $PCModuleInfo = $result['PCModuleInfo'];
            if (array_key_exists('PCSubstatusCategory', $result)) $PCSubstatusCategory = $result['PCSubstatusCategory'];
        }

        if ($keyNeeded == 'n') {
            $PCFileSubstatusArray = $PCSubstatusInfo;
        } elseif ($opt1 == 'popup') {
            for ($i = 0; $i < count($PCSubstatusInfo); $i++) {
                $PCFileSubstatusArray[trim($PCSubstatusInfo[$i]['PFSID'])] = $PCSubstatusInfo[$i];
            }
        } else {
            $newArray = [];
            $tempMC = '';
            for ($i = 0; $i < count($PCSubstatusInfo); $i++) {
                $moduleCode = trim($PCSubstatusInfo[$i]['moduleCode']);
                if ($tempMC != $moduleCode) $newArray = [];
                $newArray[] = $PCSubstatusInfo[$i];
                $PCFileSubstatusArray[$moduleCode] = $newArray;
                $tempMC = $moduleCode;
            }
        }


        for ($i = 0; $i < count($PCModuleInfo); $i++) {
            $PCModuleArray[trim($PCModuleInfo[$i]['moduleCode'])] = $PCModuleInfo[$i];
        }
        for ($i = 0; $i < count($PCSubstatusCategory); $i++) {
            $PCSubstatusCatInfo[trim($PCSubstatusCategory[$i]['categoryId'])] = $PCSubstatusCategory[$i]['category'];
        }

        $res['substatusInfo'] = $PCFileSubstatusArray;
        $res['moduleInfo'] = $PCModuleArray;
        $res['categoryInfo'] = $PCSubstatusCatInfo;

        return $res;
    }

}
