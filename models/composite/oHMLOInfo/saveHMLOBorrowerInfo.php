<?php

namespace models\composite\oHMLOInfo;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\Database2;
use models\lendingwise\db\tblFileHMLOBackGround_db;
use models\lendingwise\db\tblFileHMLOBusinessEntity_db;
use models\lendingwise\db\tblFileHMLOBusinessEntityRef_db;
use models\lendingwise\db\tblMemberOfficerInfo_db;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblFileHMLOBackGround;
use models\lendingwise\tblFileHMLOBusinessEntity;
use models\lendingwise\tblFileHMLOBusinessEntityRef;
use models\lendingwise\tblFileHMLOExperience;
use models\lendingwise\tblFileLoanOrigination;
use models\lendingwise\tblMemberOfficerInfo;
use models\Request;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\types\strongType;
use models\lendingwise\tblMembersOfficers;
use models\PageVariables;
use models\Controllers\LMRequest\entityMembers;
use models\lendingwise\tblEntityMembers;
use pages\backoffice\borrowerProfile\classes\borrowerProfileEntityInfo;

/**
 *
 */
class saveHMLOBorrowerInfo extends strongType
{
    public static array $HMLOBEFieldsArray = [
        'borrowerType',
        'entityName',
        'entityType',
        'ENINo',
        'naicsCode',
        'entityAddress',
        'entityCity',
        'entityState',
        'entityZip',
        'entityStateOfFormation',
        'entityNotes',
        'corporateSecretaryName',
        'sameAsEntityAddr',
        'borrowerUnderEntity',
        'entityWebsite',
        'entityBillAddress',
        'entityBillCity',
        'entityBillState',
        'entityBillZip',
        'entityLocation',
        'businessTypeEF',
        'tradeName',
        'noOfEmployees',
        'noOfEmployeesAfterLoan',
        'crossCorporateGuarantor',
        'grossAnnualRevenues',
        'grossIncomeLastYear',
        'netIncomeLastYear',
        'grossIncome2YearsAgo',
        'netIncome2YearsAgo',
        'averageBankBalance',
        'businessDescription',
        'merchantProcessingBankName',
        'businessEntityPhone',
        'businessEntityFax',
        'organizationalRef',
        'CBEID',
        'benBusinessHomeBased',
        'benCreditCardPayments',
        'benCardProcessorBank',
        'benChargeSalesTax',
        'benEmployeesPaid',
        'benBusinessLocation',
        'benHowManyLocation',
        'benOtherLocation',
        'benBusinessFranchise',
        'benNameOfFranchise',
        'benPointOfContact',
        'benPointOfContactPhone',
        'benPointOfContactEmail',
        'benWebsiteForFranchise',
        'isBusinessSeasonal',
        'isBusinessSeasonalPeakMonth',
        'businessReference',
        'recentNSFs',
        'hasBusinessBankruptcy',
        'businessBankruptcy',
        'statesLicIssued',
        'trustType',
        'retirementEntity',
        'businessOweTaxesPrior',
        'entityAddress2',
    ];

    public static array $HMLOBEReplaceCommaValuesArray = [
        'grossAnnualRevenues',
        'netIncomeLastYear',
        'netIncome2YearsAgo',
        'grossIncomeLastYear',
        'grossIncome2YearsAgo',
        'averageBankBalance'
    ];
    public static array $HMLOBEIntValArray = [
        'CBEID',
        'recentNSFs',
        'businessReference',
        'businessBankruptcy',
        'minTimeInBusiness',
        'sameAsEntityAddr',
        'noOfEmployees',
        'noOfEmployees1',
        'noOfEmployeesAfterLoan'
    ];

    public static array $HMLOBGFieldsArray = [
        'isBorUSCitizen',
        'isBorDecalredBankruptPastYears',
        'isAnyBorOutstandingJudgements',
        'hasBorAnyActiveLawsuits',
        'hasBorPropertyTaxLiens',
        'hasBorObligatedInForeclosure',
        'isBorPresenltyDelinquent',
        'isBorBorrowedDownPayment',
        'isBorIntendToOccupyPropAsPRI',
        'haveBorOtherFraudRelatedCrimes',
        'isBorPersonallyGuaranteeLoan',
        'borBackgroundExplanation',
        'borDesignatedBeneficiaryAgreement',

        'borDecalredBankruptExpln',
        'borOutstandingJudgementsExpln',
        'borActiveLawsuitsExpln',
        'borPropertyTaxLiensExpln',
        'borObligatedInForeclosureExpln',
        'borDelinquentExpln',
        'borOtherFraudRelatedCrimesExpln',
        'borBorrowedDownPaymentExpln',
        'borDesignatedBeneficiaryAgreementExpln',

        'isCoBorUSCitizen',
        'isCoBorDecalredBankruptPastYears',
        'isAnyCoBorOutstandingJudgements',
        'hasCoBorAnyActiveLawsuits',
        'hasCoBorPropertyTaxLiens',
        'hasCoBorObligatedInForeclosure',
        'isCoBorPresenltyDelinquent',
        'isCoBorBorrowedDownPayment',
        'isCoBorIntendToOccupyPropAsPRI',
        'haveCoBorOtherFraudRelatedCrimes',
        'isCoBorPersonallyGuaranteeLoan',
        'coBorBackgroundExplanation',
        'coBorDesignatedBeneficiaryAgreement',
        'marriedToBor',

        'coBorDecalredBankruptExpln',
        'coBorOutstandingJudgementsExpln',
        'coBorActiveLawsuitsExpln',
        'coBorPropertyTaxLiensExpln',
        'coBorObligatedInForeclosureExpln',
        'coBorDelinquentExpln',
        'coBorOtherFraudRelatedCrimesExpln',
        'coBorBorrowedDownPaymentExpln',
        'coBorDesignatedBeneficiaryAgreementExpln',
        'borOrigin',
        'borVisaStatus',

        'personalBankruptcy',
        'statusForeclosure',
        'completedPreForeclose',
        'hasBorBeenForeclosed',
        'bankruptcyTypes',
        'previouslyHadShortSale',
        'whenWasShortSale',
        'explnCircumstancesOfShortSales',
        'incomeTaxFiledDate',
        'anyReturnsAudited',
        'yearsAudited',
        'haveYouDrawnWill',
        'nameOfExecutorAndYearDrawn',
        'financialPlanPreparedForYou',
        'lineOfCreditOrUnusedCredit',
        'creditFacilityDetails',
        'substantialInheritances',
        'substantialInheritancesDetails',
        'coborOrigin',
        'coborVisaStatus',
    ];

    public static array $HMLOExpFieldsArray = [
        'haveBorREInvestmentExperience',
        'borNoOfFlippingExperience',
        'coBorNoOfFlippingExperience',
        'borNoOfREPropertiesCompleted',
        'haveBorRehabConstructionExperience',
        'borNoOfYearRehabExperience',
        'borRehabPropCompleted',
        'haveBorProjectCurrentlyInProgress',
        'borNoOfProjectCurrently',
        'haveBorOwnInvestmentProperties',
        'borNoOfOwnProp',
        'areBorMemberOfInvestmentClub',
        'borClubName',
        'liquidAssets',
        'haveBorProfLicences',
        'coBorliquidReserves',
        'borProfLicence',
        'fullTimeRealEstateInvestor',
        'areBuilderDeveloper',
        'doYouHireGC',
        'borPrimaryInvestmentStrategyExplain',

        'haveCoBorREInvestmentExperience',
        'coBorNoOfREPropertiesCompleted',
        'haveCoBorRehabConstructionExperience',
        'coBorNoOfYearRehabExperience',
        'coBorRehabPropCompleted',
        'haveCoBorProjectCurrentlyInProgress',
        'coBorNoOfProjectCurrently',
        'haveCoBorOwnInvestmentProperties',
        'coBorNoOfOwnProp',
        'areCoBorMemberOfInvestmentClub',
        'coBorClubName',
        'haveCoBorProfLicences',
        'coBorProfLicence',
        'coFullTimeRealEstateInvestor',

        'haveBorSellPropertie',
        'borNoOfProSellExperience',
        'borNoOfProSellCompleted',

        'haveCoBorSellPropertie',
        'coBorNoOfProSellExperience',
        'coBorNoOfProSellCompleted',
        'coBorPrimaryInvestmentStrategyExplain',

        'coBorREAddress1',
        'coBorOutcomeRE1',
        'coBorREAddress2',
        'coBorOutcomeRE2',
        'coBorREAddress3',
        'coBorOutcomeRE3',

        'coBorRCAddress1',
        'coBorRCOutcome1',
        'coBorRCAddress2',
        'coBorRCOutcome2',
        'coBorRCAddress3',
        'coBorRCOutcome3',

        'coBorSellAddress1',
        'coBorSellAddress2',
        'coBorSellAddress3',
        'coBorSellOutcome1',
        'coBorSellOutcome2',
        'coBorSellOutcome3',

        'amountOfFinancing',
        'amountOfFinancingTo',
        'typicalPurchasePrice',
        'typicalPurchasePriceTo',
        'typicalConstructionCosts',
        'typicalConstructionCostsTo',
        'typicalSalePrice',
        'typicalSalePriceTo',
        'constructionDrawsPerProject',
        'constructionDrawsPerProjectTo',
        'monthsPurchaseDateToFirstConst',
        'monthsPurchaseDateToFirstConstTo',
        'monthsPurchaseDateUntilConst',
        'monthsPurchaseDateUntilConstTo',
        'monthsPurchaseDateToSaleDate',
        'monthsPurchaseDateToSaleDateTo',
        'NoOfSuchProjects',
        'NoOfSuchProjectsTo',
        'borLicenseNo',
        'flipPropCompletedLifetime',
        'groundPropCompletedLifetime',
        'sellPropCompletedLifetime',
        'overallRealEstateInvesExp',
        'trackRecord'
    ];
    /** DIRECT Variables **/

    public static array $typTxnReplaceCommaValuesArray = [
        'coBorOutcomeRE1',
        'coBorOutcomeRE2',
        'coBorOutcomeRE3',
        'coBorRCOutcome1',
        'coBorRCOutcome2',
        'coBorRCOutcome3',
        'coBorSellOutcome1',
        'coBorSellOutcome2',
        'coBorSellOutcome3',
        'amountOfFinancing',
        'amountOfFinancingTo',
        'typicalPurchasePrice',
        'typicalPurchasePriceTo',
        'typicalConstructionCosts',
        'typicalConstructionCostsTo',
        'typicalSalePrice',
        'typicalSalePriceTo',
        'liquidAssets',
        'coBorliquidReserves'
    ];

    public static array $leadReceiveArray = [
        'borOccpPri'                     => 'isBorIntendToOccupyPropAsPRI',
        'hasBorBeenForeclosed'           => 'hasBorBeenForeclosed',
        'isBorDecalredBankruptPastYears' => 'isBorDecalredBankruptPastYears',
        'hasBorAnyActiveLawsuits'        => 'hasBorAnyActiveLawsuits',
        'haveBorOtherFraudRelatedCrimes' => 'haveBorOtherFraudRelatedCrimes',
    ];

    public static array $leadReceiveArrayB = [
        'noOfPropComp'   => 'borNoOfREPropertiesCompleted',
        'borNoOfOwnProp' => 'borNoOfOwnProp',
        'lAssets'        => 'liquidAssets',
        //'haveBorREInvestmentExperience' => 'haveBorREInvestmentExperience',  /* commented because leadposting makes error query specified this twice */
    ];

    public static array $leadReceiveArrayC = [
        'experianScore' => 'borExperianScore',
    ];

    public static array $HMLOArray = [
        'borCompanyName',
        'borCreditScoreRange',
        'borExperianScore',
        'borEquifaxScore',
        'borTransunionScore',
        'midFicoScoreCoBor',
        'coBorCreditScoreRange',
        'coBorExperianScore',
        'coBorEquifaxScore',
        'coBorTransunionScore', 'borrowerCitizenship', 'servicingMemberInfo',
        'isServicingMember', 'agesOfDependent', 'numberOfDependents',
        'maritalStatusCoBor',
        'coBorrowerCitizenship', 'serviceExpirationDate',
        'targetClosingDate'
    ];

    public static array $HMLODateFieldsArray = [
        'serviceExpirationDate',
        'targetClosingDate'
    ];

    public static array $LOFieldsArray = [
        'borResidedPresentAddr',
        'coBResidedPresentAddr',
        'borNoOfYrAtPrevAddr',
        'coBNoOfYrAtPrevAddr',
    ];


    public static array $fileHMLOPropInfo = [
        'propDetailsProcess',
        'isAdditionalGuarantors',
        'hereAbout',
        'referringParty', 'fundingDate',
    ];

    public static function tblFileHMLOBusinessEntityRef(
        $LMRId,
        $recordDate
    )
    {

        $items = tblFileHMLOBusinessEntityRef::GetAll([
            tblFileHMLOBusinessEntityRef_db::COLUMN_FILEID => $LMRId,
        ]);

        foreach ($items as $item) {
            $item->Delete();
        }

        if (isset($_REQUEST['businessReferenceName']) && $_REQUEST['businessReference'] == 1) {
            if (count($_REQUEST['businessReferenceName']) > 0) {
                foreach ($_REQUEST['businessReferenceName'] as $i => $businessReferenceName) {
                    $businessReferencePhone = $_REQUEST['businessReferencePhone'][$i];
                    $businessReferenceCompany = $_REQUEST['businessReferenceCompany'][$i];
                    $businessReferenceFax = $_REQUEST['businessReferenceFax'][$i];
                    $businessReferenceEmail = $_REQUEST['businessReferenceEmail'][$i];
                    if (trim($businessReferenceName) == ''
                        && trim($businessReferencePhone) == ''
                        && trim($businessReferenceCompany) == ''
                        && trim($businessReferenceFax) == ''
                        && trim($businessReferenceEmail) == '') {
                        continue;
                    }
                    $tblFileHMLOBusinessEntityRef = new tblFileHMLOBusinessEntityRef();
                    $tblFileHMLOBusinessEntityRef->fileID = $LMRId;
                    $tblFileHMLOBusinessEntityRef->recordDate = $recordDate;
                    $tblFileHMLOBusinessEntityRef->businessReferenceName = $businessReferenceName;
                    $tblFileHMLOBusinessEntityRef->businessReferencePhone = Strings::NumbersOnly($businessReferencePhone);
                    $tblFileHMLOBusinessEntityRef->businessReferenceCompany = $businessReferenceCompany;
                    $tblFileHMLOBusinessEntityRef->businessReferenceFax = Strings::NumbersOnly($businessReferenceFax);
                    $tblFileHMLOBusinessEntityRef->businessReferenceEmail = $businessReferenceEmail;
                    $tblFileHMLOBusinessEntityRef->Save();
                }
            }
        }
    }

    public static function tblFileHMLOBusinessEntity(
        $LMRId,
        $memberName,
        $memberTitle,
        $memberOwnership,
        $memberAnnualSalary,
        $memberAddress,
        $memberDriversLicense,
        $memberDriversLicenseState,
        $memberEmail,
        $memberPhone,
        $memberCell,
        $memberSSN,
        $memberDOB,
        $memberCreditScore,
        $clientId,
        $recordDate,
        $cliEntity,
        $entityMembersDetails
    )
    {
        /** Save the Business Entity Section **/

        $q = 'SELECT HMLOBEID FROM tblFileHMLOBusinessEntity WHERE fileID = :fileID  ';
        $sqlParams = [
            'fileID' => $LMRId,
        ];
        $resultArray = Database2::getInstance()->queryData($q, $sqlParams, null, true);
        $tblFileHMLOBusinessEntity = tblFileHMLOBusinessEntity::Get([tblFileHMLOBusinessEntity_db::COLUMN_FILEID => $LMRId]) ?? new tblFileHMLOBusinessEntity();
        /** DIRECT Variables **/

        $qu = '';
        $qs = ' ';
        $qc = '';
        $qv = '';
        $params = [];
        foreach (self::$HMLOBEFieldsArray as $column) {
            if (Request::isset($column)) {
                if (in_array($column, self::$HMLOBEReplaceCommaValuesArray)) {
                    $postVal = Strings::replaceCommaValues(Request::GetClean($column));
                } elseif (in_array($column, self::$HMLOBEIntValArray)) {
                    $postVal = intval(Request::GetClean($column));
                } else {
                    $postVal = trim(stripslashes(Request::GetClean($column)));
                }
                $tblFileHMLOBusinessEntity->$column = $postVal;
            } elseif (isset ($cliEntity[$column]) && trim($cliEntity[$column]) && $LMRId < 0) {
                if (in_array($column, self::$HMLOBEReplaceCommaValuesArray)) {
                    $postVal = Strings::replaceCommaValues(Request::GetClean($column));
                } elseif (in_array($column, self::$HMLOBEIntValArray)) {
                    $postVal = intval(Request::GetClean($column));
                } else {
                    $postVal = trim(stripslashes(Request::GetClean($column)));
                }
                $tblFileHMLOBusinessEntity->$column = $postVal;
            }
        }

        if (isset($_REQUEST['dateOfFormation'])) {
            $dateOfFormation = trim($_REQUEST['dateOfFormation']);
            if (Dates::IsEmpty($dateOfFormation)) {
                $dateOfFormation = '0000-00-00';
            } else {
                $dateOfFormation = trim(Dates::formatDateWithRE($dateOfFormation, 'MDY', 'Y-m-d'));
            }
            $tblFileHMLOBusinessEntity->dateOfFormation = $dateOfFormation;
        }

        if (Request::isset('minTimeInBusiness')) {
            $tblFileHMLOBusinessEntity->minTimeInBusiness = intval(Request::GetClean('minTimeInBusiness'));
        }

        if (Request::isset('estimatedAmountOwed')) {
            $tblFileHMLOBusinessEntity->estimatedAmountOwed = Strings::replaceCommaValues(Request::GetClean('estimatedAmountOwed'));
        }

        if (Request::isset('statesRegisterdIn') && Request::GetClean('statesRegisterdIn')) {
            $statesRegisterdIn = implode(',', Request::GetClean('statesRegisterdIn'));
            $tblFileHMLOBusinessEntity->statesRegisterdIn = $statesRegisterdIn;
        } elseif (Request::isset('statesRegisterdInHidden')) {
            $statesRegisterdIn = '';
            $tblFileHMLOBusinessEntity->statesRegisterdIn = $statesRegisterdIn;
        }

        if (Request::isset('businessCategory')) {
            $businessCategory = trim(Request::GetClean('businessCategory'));
            $tblFileHMLOBusinessEntity->businessCategory = $businessCategory;
        }
        if (Request::isset('businessType')) {
            $businessType = trim(Request::GetClean('businessType'));
            $tblFileHMLOBusinessEntity->businessType = $businessType;
        }
        if (Request::isset('productTypeOrServiceSold')) {
            $productTypeOrServiceSold = trim(Request::GetClean('productTypeOrServiceSold'));
            $tblFileHMLOBusinessEntity->productTypeOrServiceSold = $productTypeOrServiceSold;
        }
        if (Request::isset('terminalOrMakeModel')) {
            $terminalOrMakeModel = trim(Request::GetClean('terminalOrMakeModel'));
            $tblFileHMLOBusinessEntity->terminalOrMakeModel = $terminalOrMakeModel;
        }
        if (Request::isset('businessPhone')) {
            $businessPhone = trim(Request::GetClean('businessPhone'));
            $tblFileHMLOBusinessEntity->businessPhone = $businessPhone;
        }

        if (Request::isset('landlordMortagageContactName')) {
            $landlordMortagageContactName = trim(Request::GetClean('landlordMortagageContactName'));
            $tblFileHMLOBusinessEntity->landlordMortagageContactName = $landlordMortagageContactName;
        }
        if (Request::isset('landlordMortagagePhone')) {
            $landlordMortagagePhone = trim(Request::GetClean('landlordMortagagePhone'));
            $tblFileHMLOBusinessEntity->landlordMortagagePhone = $landlordMortagagePhone;
        }
        if (Request::isset('rentMortagagePayment')) {
            $rentMortagagePayment = Strings::replaceCommaValues(trim(Request::GetClean('rentMortagagePayment')));
            $tblFileHMLOBusinessEntity->rentMortagagePayment = $rentMortagagePayment;
        }

        if (Request::isset('avgMonthlyCreditcardSale')) {
            $avgMonthlyCreditcardSale = Strings::replaceCommaValues(trim(Request::GetClean('avgMonthlyCreditcardSale')));
            $tblFileHMLOBusinessEntity->avgMonthlyCreditcardSale = $avgMonthlyCreditcardSale;
        }

        if (Request::isset('avgTotalMonthlySale')) {
            $avgTotalMonthlySale = Strings::replaceCommaValues(trim(Request::GetClean('avgTotalMonthlySale')));
            $tblFileHMLOBusinessEntity->avgTotalMonthlySale = $avgTotalMonthlySale;
        }

        if (Request::isset('annualGrossSales')) {
            $annualGrossSales = Strings::replaceCommaValues(trim(Request::GetClean('annualGrossSales')));
            $tblFileHMLOBusinessEntity->annualGrossSales = $annualGrossSales;
        }

        if (Request::isset('annualGrossProfit')) {
            $annualGrossProfit = Strings::replaceCommaValues(trim(Request::GetClean('annualGrossProfit')));
            $tblFileHMLOBusinessEntity->annualGrossProfit = $annualGrossProfit;
        }
        if (Request::isset('ordinaryBusinessIncome')) {
            $ordinaryBusinessIncome = Strings::replaceCommaValues(trim(Request::GetClean('ordinaryBusinessIncome')));
            $tblFileHMLOBusinessEntity->ordinaryBusinessIncome = $ordinaryBusinessIncome;
        }
        if (Request::isset('entityPropertyOwnerShip')) {
            $entityPropertyOwnerShip = trim(Request::GetClean('entityPropertyOwnerShip'));
            $tblFileHMLOBusinessEntity->entityPropertyOwnerShip = $entityPropertyOwnerShip;
        }

        if (Request::isset('valueOfProperty')) {
            $valueOfProperty = Strings::replaceCommaValues(Request::GetClean('valueOfProperty'));
            $tblFileHMLOBusinessEntity->valueOfProperty = $valueOfProperty;
        }
        if (Request::isset('totalDebtOnProperty')) {
            $totalDebtOnProperty = Strings::replaceCommaValues(Request::GetClean('totalDebtOnProperty'));
            $tblFileHMLOBusinessEntity->totalDebtOnProperty = $totalDebtOnProperty;
        }
        if (Request::isset('nameOfLenders')) {
            $nameOfLenders = trim(Request::GetClean('nameOfLenders'));
            $tblFileHMLOBusinessEntity->nameOfLenders = $nameOfLenders;
        }


        if (isset($_REQUEST['startDateAtLocation'])) {
            $startDateAtLocation = trim($_REQUEST['startDateAtLocation']);
            if (Dates::IsEmpty($startDateAtLocation)) {
                $startDateAtLocation = '0000-00-00';
            } else {
                $startDateAtLocation = trim(Dates::formatDateWithRE($startDateAtLocation, 'MDY', 'Y-m-d'));
            }
            $tblFileHMLOBusinessEntity->startDateAtLocation = $startDateAtLocation;
        }
        if (isset($_REQUEST['dateOfOperatingAgreement'])) {
            $dateOfOperatingAgreement = trim($_REQUEST['dateOfOperatingAgreement']);
            if (Dates::IsEmpty($dateOfOperatingAgreement)) {
                $dateOfOperatingAgreement = '0000-00-00';
            } else {
                $dateOfOperatingAgreement = trim(Dates::formatDateWithRE($dateOfOperatingAgreement, 'MDY', 'Y-m-d'));
            }
            $tblFileHMLOBusinessEntity->dateOfOperatingAgreement = $dateOfOperatingAgreement;
        }

        if (Request::isset('borLicenseNumber')) {
            $borLicenseNumber = trim(Request::GetClean('borLicenseNumber'));
            $tblFileHMLOBusinessEntity->borLicenseNumber = $borLicenseNumber;
        }
        if (Request::isset('dbaNames')) {
            $dbaNames = trim(Request::GetClean('dbaNames'));
            $tblFileHMLOBusinessEntity->dbaNames = $dbaNames;
        }
        if (Request::isset('priorBusName')) {
            $priorBusName = trim(Request::GetClean('priorBusName'));
            $tblFileHMLOBusinessEntity->priorBusName = $priorBusName;
        }
        if (isset($_REQUEST['borLicenseIssuance'])) {
            $borLicenseIssuance = trim($_REQUEST['borLicenseIssuance']);
            if (Dates::IsEmpty($borLicenseIssuance)) {
                $borLicenseIssuance = '0000-00-00';
            } else {
                $borLicenseIssuance = trim(Dates::formatDateWithRE($borLicenseIssuance, 'MDY', 'Y-m-d'));
            }
            $tblFileHMLOBusinessEntity->borLicenseIssuance = $borLicenseIssuance;
        }
        if (isset($_REQUEST['borLicenseExpiration'])) {
            $borLicenseExpiration = trim($_REQUEST['borLicenseExpiration']);
            if (Dates::IsEmpty($borLicenseExpiration)) {
                $borLicenseExpiration = '0000-00-00';
            } else {
                $borLicenseExpiration = trim(Dates::formatDateWithRE($borLicenseExpiration, 'MDY', 'Y-m-d'));
            }
            $tblFileHMLOBusinessEntity->borLicenseExpiration = $borLicenseExpiration;
        }
        if (Request::isset('operatingStates') && Request::GetClean('operatingStates')) {
            $operatingStates = implode(',', Request::GetClean('operatingStates'));
            $tblFileHMLOBusinessEntity->operatingStates = $operatingStates;
        } elseif (Request::isset('operatingStatesHidden')) {
            $operatingStates = '';
            $tblFileHMLOBusinessEntity->operatingStates = $operatingStates;
        }

        //checkboxes
        $entityService = '';
        if (Request::isset('entityService')) {
            $entityService = stripslashes(Request::GetClean('entityService'));
            $tblFileHMLOBusinessEntity->entityService = $entityService;
        }


        $entityProduct = '';
        if (Request::isset('entityProduct')) {
            $entityProduct = Request::GetClean('entityProduct');
            $tblFileHMLOBusinessEntity->entityProduct = $entityProduct;
        }


        $entityB2B = '';
        if (Request::isset('entityB2B')) {
            $entityB2B = Request::GetClean('entityB2B');
            $tblFileHMLOBusinessEntity->entityB2B = $entityB2B;
        }

        $entityB2C = '';
        if (Request::isset('entityB2C')) {
            $entityB2C = Request::GetClean('entityB2C');
            $tblFileHMLOBusinessEntity->entityB2C = $entityB2C;
        }

        $tblFileHMLOBusinessEntity->fileID = $LMRId;
        $tblFileHMLOBusinessEntity->CID = $clientId;
        if (!$tblFileHMLOBusinessEntity->HMLOBEID) {
            $tblFileHMLOBusinessEntity->recordDate = $recordDate;
        }


        if ($clientId > 0) {                                     /*Reg: 159487380 >> DB error 4 - tblFileHMLOBusinessEntity*/
            $tblFileHMLOBusinessEntity->Save();

            $memberId = [];
            $memberTin = [];

            if (array_key_exists('memberId', $_REQUEST)) $memberId = $_REQUEST['memberId'];
            if (array_key_exists('memberTin', $_REQUEST)) $memberTin = $_REQUEST['memberTin'];
            $borrowerType = Request::isset('borrowerType') ? Request::GetClean('borrowerType') : '';
            if (PageVariables::$allowNestedEntityMembers) { // Nested Entity Members - enabled
                if (Request::GetClean('borrowerProfileEntityInfoSync')) {
                    //Save the data in the Loan File from Borrower Profile Entity Members Info
                    $CBEID = Request::GetClean('CBEID') ?? 0;
                    self::saveEntityMembersInfo($LMRId, $clientId, $CBEID);
                } else {
                    if ($borrowerType == 'Entity') {
                        self::processMemberData($LMRId, $entityMembersDetails);
                    }
                }
            } else { // Nested Entity Members - disabled | old code
                if (isset($_REQUEST['memberId']) && count($memberId) > 0) {
                    foreach ($memberId as $mo => $_memberId) {

                        $tblMemberOfficerInfo = null;

                        if ($_memberId) {
                            $tblMemberOfficerInfo = tblMemberOfficerInfo::Get([
                                tblMemberOfficerInfo_db::COLUMN_MEMBERID => $_memberId,
                            ]);
                        }

                        if (!$tblMemberOfficerInfo) {
                            $tblMemberOfficerInfo = new tblMemberOfficerInfo();
                            $tblMemberOfficerInfo->LMRID = $LMRId;
                        }

                        $tblMemberOfficerInfo->memberName = stripslashes($memberName[$mo]);
                        $tblMemberOfficerInfo->memberTitle = stripslashes($memberTitle[$mo]);
                        $tblMemberOfficerInfo->memberOwnership = intval($memberOwnership[$mo]);
                        $tblMemberOfficerInfo->memberAnnualSalary = Strings::replaceCommaValues($memberAnnualSalary[$mo]);
                        $tblMemberOfficerInfo->memberAddress = stripslashes($memberAddress[$mo]);
                        $tblMemberOfficerInfo->memberPhone = Strings::cleanPhoneNo($memberPhone[$mo]);
                        $tblMemberOfficerInfo->memberCell = Strings::cleanPhoneNo($memberCell[$mo]);
                        $tblMemberOfficerInfo->memberSSN = Strings::cleanPhoneNo($memberSSN[$mo]);
                        $tblMemberOfficerInfo->memberDOB = Dates::formatDateWithRE($memberDOB[$mo], 'MDY', 'Y-m-d');
                        $tblMemberOfficerInfo->memberCreditScore = $memberCreditScore[$mo];
                        $tblMemberOfficerInfo->memberEmail = $memberEmail[$mo];
                        $tblMemberOfficerInfo->memberDriversLicense = $memberDriversLicense[$mo];
                        $tblMemberOfficerInfo->memberDriversLicenseState = $memberDriversLicenseState[$mo];
                        $tblMemberOfficerInfo->memberTin = Strings::Numeric($memberTin[$mo]);
                        if (isset($_REQUEST['memberPersonalGuarantee_' . $mo]) && trim($_REQUEST['memberPersonalGuarantee_' . $mo][0]) != '') {
                            $tblMemberOfficerInfo->memberPersonalGuarantee = trim($_REQUEST['memberPersonalGuarantee_' . $mo]);
                        }
                        if (isset($_REQUEST['memberAuthorizedSigner_' . $mo]) && trim($_REQUEST['memberAuthorizedSigner_' . $mo][0]) != '') {
                            $tblMemberOfficerInfo->memberAuthorizedSigner = trim($_REQUEST['memberAuthorizedSigner_' . $mo]);
                        }
                        if (isset($_REQUEST['memberCitizenship_' . $mo]) && trim($_REQUEST['memberCitizenship_' . $mo][0]) != '') {
                            $tblMemberOfficerInfo->memberCitizenship = trim($_REQUEST['memberCitizenship_' . $mo]);
                        }
                        if (isset($_REQUEST['memberType_' . $mo]) && trim($_REQUEST['memberType_' . $mo][0]) != '') {
                            $tblMemberOfficerInfo->memberType = trim($_REQUEST['memberType_' . $mo]);
                        }
                        if (isset($_REQUEST['memberMaritalStatus_' . $mo]) && trim($_REQUEST['memberMaritalStatus_' . $mo][0]) != '') {
                            $tblMemberOfficerInfo->memberMaritalStatus = trim($_REQUEST['memberMaritalStatus_' . $mo]);
                        }
                        if (isset($_REQUEST['memberMarriageDate'])) {
                            $tblMemberOfficerInfo->memberMarriageDate = Dates::formatDateWithRE($_REQUEST['memberMarriageDate'][$mo], 'MDY', 'Y-m-d');
                        }
                        if (isset($_REQUEST['memberDivorceDate'])) {
                            $tblMemberOfficerInfo->memberDivorceDate = Dates::formatDateWithRE($_REQUEST['memberDivorceDate'][$mo], 'MDY', 'Y-m-d');
                        }
                        if (isset($_REQUEST['memberMaidenName'])) {
                            $tblMemberOfficerInfo->memberMaidenName = $_REQUEST['memberMaidenName'][$mo];
                        }
                        if (isset($_REQUEST['memberSpouseName'])) {
                            $tblMemberOfficerInfo->memberSpouseName = $_REQUEST['memberSpouseName'][$mo];
                        }
                        if (isset($_REQUEST['memberCreditScoreDate'])) {
                            $tblMemberOfficerInfo->memberCreditScoreDate = Dates::formatDateWithRE($_REQUEST['memberCreditScoreDate'][$mo], 'MDY', 'Y-m-d');
                        }
                        if (isset($_REQUEST['memberRentOrOwn'])) {
                            $tblMemberOfficerInfo->memberRentOrOwn = $_REQUEST['memberRentOrOwn'][$mo];
                        }
                        if (isset($_REQUEST['memberMonthlyRentOrMortgage'])) {
                            $tblMemberOfficerInfo->memberMonthlyRentOrMortgage = Strings::replaceCommaValues($_REQUEST['memberMonthlyRentOrMortgage'][$mo]);
                        }
                        if (isset($_REQUEST['memberDateMovedAddress'])) {
                            $tblMemberOfficerInfo->memberDateMovedAddress = Dates::formatDateWithRE($_REQUEST['memberDateMovedAddress'][$mo], 'MDY', 'Y-m-d');
                        }
                        if (isset($_REQUEST['memberRealEstateValue'])) {
                            $tblMemberOfficerInfo->memberRealEstateValue = Strings::replaceCommaValues($_REQUEST['memberRealEstateValue'][$mo]);
                        }
                        if (isset($_REQUEST['memberRetirementAccountBalance'])) {
                            $tblMemberOfficerInfo->memberRetirementAccountBalance = Strings::replaceCommaValues($_REQUEST['memberRetirementAccountBalance'][$mo]);
                        }
                        if (isset($_REQUEST['memberCashSavingsStocksBalance'])) {
                            $tblMemberOfficerInfo->memberCashSavingsStocksBalance = Strings::replaceCommaValues($_REQUEST['memberCashSavingsStocksBalance'][$mo]);
                        }
                        if (isset($_REQUEST['memberCreditCardBalance'])) {
                            $tblMemberOfficerInfo->memberCreditCardBalance = Strings::replaceCommaValues($_REQUEST['memberCreditCardBalance'][$mo]);
                        }
                        if (isset($_REQUEST['memberMortgageBalance'])) {
                            $tblMemberOfficerInfo->memberMortgageBalance = Strings::replaceCommaValues($_REQUEST['memberMortgageBalance'][$mo]);
                        }
                        if (isset($_REQUEST['memberAutoLoanBalance'])) {
                            $tblMemberOfficerInfo->memberAutoLoanBalance = Strings::replaceCommaValues($_REQUEST['memberAutoLoanBalance'][$mo]);
                        }
                        $tblMemberOfficerInfo->memberTotalNetWorth = ($tblMemberOfficerInfo->memberCashSavingsStocksBalance
                                + $tblMemberOfficerInfo->memberRealEstateValue + $tblMemberOfficerInfo->memberRetirementAccountBalance)
                            - ($tblMemberOfficerInfo->memberCreditCardBalance + $tblMemberOfficerInfo->memberMortgageBalance + $tblMemberOfficerInfo->memberAutoLoanBalance);

                        $tblMemberOfficerInfo->Save();

                    }

                }

                if ($_REQUEST['deletedMembersId']) {
                    $deletedMembersIdArr = explode(',', $_REQUEST['deletedMembersId']);
                    foreach ($deletedMembersIdArr as $deletedMemberId) {
                        $tblMemberOfficerInfo = tblMemberOfficerInfo::Get([
                            tblMemberOfficerInfo_db::COLUMN_MEMBERID => trim($deletedMemberId),
                        ]);

                        if ($tblMemberOfficerInfo) {
                            $tblMemberOfficerInfo->Delete();
                        }
                    }
                }
            }
        }
        //Delete Entity Members if the Borrower Type is not Entity
        if ($tblFileHMLOBusinessEntity->borrowerType != 'Entity') {
            self::deleteEntityMembers($LMRId);
        }
    }

    /**
     * @param $LMRId
     * @param $members
     * @return void
     */

    public static function processMemberData($LMRId, $members)
    {
        foreach ($members as $member) {
            $memberData = $member['entityMember'];
            foreach ($memberData as $parent) {
                self::saveMembers($LMRId, $parent);
            }
        }
    }

    public static function saveMembers($LMRId, $item)
    {
        $table = tblMemberOfficerInfo::Get([
            'memberId' => intval($item['memberId']),
            'LMRID'    => intval($LMRId)
        ]);
        if (!$table) {
            $table = new tblMemberOfficerInfo();
        }
        $table->parent_id = intval($item['parent_id']) ?: null;
        $table->LMRID = intval($LMRId);
        $memberType = $item['memberType'] ?? $table->memberType;
        $table->memberType = $memberType;
        $table->memberName = $item['memberName'] ?? $table->memberName;
        $table->memberCategory = $item['memberCategory'] ?? $table->memberCategory;
        $table->memberOwnership = intval($item['memberOwnership']) ?? $table->memberOwnership;
        $table->memberAddress = $item['memberAddress'] ?? $table->memberAddress;
        $table->memberPhone = Strings::cleanPhoneNo($item['memberPhone']) ?? $table->memberPhone;
        $table->memberCell = Strings::cleanPhoneNo($item['memberCell']) ?? $table->memberCell;

        if ($memberType == 'Individual') {
            //save individual member data
            $table->memberTitle = $item['memberTitle'] ?? $table->memberTitle;
            $table->memberAnnualSalary = Strings::replaceCommaValues($item['memberAnnualSalary']) ?? $table->memberAnnualSalary;
            $table->memberSSN = Strings::cleanPhoneNo($item['memberSSN']) ?? $table->memberSSN;
            $table->memberDOB = Dates::formatDateWithRE($item['memberDOB'], 'MDY', 'Y-m-d') ?? $table->memberDOB;
            $table->memberCreditScore = $item['memberCreditScore'] ?? $table->memberCreditScore;
            $table->memberCreditScoreDate = Dates::formatDateWithRE($item['memberCreditScoreDate'], 'MDY', 'Y-m-d') ?? $table->memberCreditScoreDate;
            $table->memberEmail = $item['memberEmail'] ?? $table->memberEmail;

            $table->memberRentOrOwn = $item['memberRentOrOwn'] ?? $table->memberRentOrOwn;
            $table->memberMonthlyRentOrMortgage = Strings::replaceCommaValues($item['memberMonthlyRentOrMortgage']) ?? $table->memberMonthlyRentOrMortgage;
            $table->memberDateMovedAddress = Dates::formatDateWithRE($item['memberDateMovedAddress'], 'MDY', 'Y-m-d') ?? $table->memberDateMovedAddress;
            $table->memberRealEstateValue = Strings::replaceCommaValues($item['memberRealEstateValue']) ?? $table->memberRealEstateValue;
            $table->memberRetirementAccountBalance = Strings::replaceCommaValues($item['memberRetirementAccountBalance']) ?? $table->memberRetirementAccountBalance;
            $table->memberCashSavingsStocksBalance = Strings::replaceCommaValues($item['memberCashSavingsStocksBalance']) ?? $table->memberCashSavingsStocksBalance;
            $table->memberCreditCardBalance = Strings::replaceCommaValues($item['memberCreditCardBalance']) ?? $table->memberCreditCardBalance;
            $table->memberMortgageBalance = Strings::replaceCommaValues($item['memberMortgageBalance']) ?? $table->memberMortgageBalance;
            $table->memberAutoLoanBalance = Strings::replaceCommaValues($item['memberAutoLoanBalance']) ?? $table->memberAutoLoanBalance;

            $table->memberTotalNetWorth = (Strings::replaceCommaValues($item['memberCashSavingsStocksBalance'])
                + Strings::replaceCommaValues($item['memberRealEstateValue'])
                + Strings::replaceCommaValues($item['memberRetirementAccountBalance']))
            - (Strings::replaceCommaValues($item['memberCreditCardBalance'])
                + Strings::replaceCommaValues($item['memberMortgageBalance'])
                + Strings::replaceCommaValues($item['memberAutoLoanBalance'])) ?? $table->memberTotalNetWorth;

            $table->memberDriversLicense = $item['memberDriversLicense'] ?? $table->memberDriversLicense;
            $table->memberDriversLicenseState = $item['memberDriversLicenseState'] ?? $table->memberDriversLicenseState;
            $table->memberPersonalGuarantee = $item['memberPersonalGuarantee'] ?? $table->memberPersonalGuarantee;
            $table->memberAuthorizedSigner = $item['memberAuthorizedSigner'] ?? $table->memberAuthorizedSigner;
            $table->memberCitizenship = $item['memberCitizenship'] ?? $table->memberCitizenship;
            $table->memberMaritalStatus = $item['memberMaritalStatus'] ?? $table->memberMaritalStatus;
            $table->memberMarriageDate = Dates::formatDateWithRE($item['memberMarriageDate'], 'MDY', 'Y-m-d') ?? $table->memberMarriageDate;
            $table->memberDivorceDate = Dates::formatDateWithRE($item['memberDivorceDate'], 'MDY', 'Y-m-d') ?? $table->memberDivorceDate;
            $table->memberMaidenName = $item['memberMaidenName'] ?? $table->memberMaidenName;
            $table->memberSpouseName = $item['memberSpouseName'] ?? $table->memberSpouseName;

            //clear entity member data
            $table->memberTin = null;
        } elseif ($memberType == 'Entity') {
            //save entity member data
            $table->memberTin = intval(Strings::numberOnly($item['memberTin'])) ?? $table->memberTin;

            //clear individual member data
            $table->memberTitle = '';
            $table->memberAnnualSalary = 0;
            $table->memberSSN = '';
            $table->memberDOB = null;
            $table->memberCreditScore = '';
            $table->memberCreditScoreDate = null;
            $table->memberEmail = '';
            $table->memberRentOrOwn = '';
            $table->memberMonthlyRentOrMortgage = 0;
            $table->memberDateMovedAddress = null;
            $table->memberRealEstateValue = 0;
            $table->memberRetirementAccountBalance = 0;
            $table->memberCashSavingsStocksBalance = 0;
            $table->memberCreditCardBalance = 0;
            $table->memberMortgageBalance = 0;
            $table->memberAutoLoanBalance = 0;
            $table->memberTotalNetWorth = 0;
            $table->memberDriversLicense = '';
            $table->memberDriversLicenseState = '';
            $table->memberPersonalGuarantee = '';
            $table->memberAuthorizedSigner = '';
            $table->memberCitizenship = '';
            $table->memberMaritalStatus = '';
            $table->memberMarriageDate = null;
            $table->memberDivorceDate = null;
            $table->memberMaidenName = '';
            $table->memberSpouseName = '';
        }
        $table->Save();
    }

    /**
     * @param $memberId
     * @param $LMRId
     * @param $entityMembersDetails
     * @return void
     */
    public static function saveEntityMembers($memberId, $LMRId, $entityMembersDetails)
    {
        foreach ($entityMembersDetails as $entityMember) {
            $entityMember = array_filter($entityMember); // remove empty values
            if ($entityMember) {
                $tblEntityMembers = tblEntityMembers::Get([
                    'id'       => intval($entityMember['entityMemberId']),
                    'memberId' => intval($memberId), //member id from tblMemberOfficerInfo
                    'lmrid'    => intval($LMRId)
                ]);
                if (!$tblEntityMembers) {
                    $tblEntityMembers = new tblEntityMembers();
                }
                $tblEntityMembers->memberId = intval($memberId) ?? $tblEntityMembers->memberId;
                $tblEntityMembers->lmrid = intval($LMRId) ?? $tblEntityMembers->lmrid;
                $tblEntityMembers->entityMemberName = $entityMember['entityMemberName'] ?? $tblEntityMembers->entityMemberName;
                $tblEntityMembers->entityMemberType = $entityMember['entityMemberType'] ?? $tblEntityMembers->entityMemberType;
                $tblEntityMembers->entityMemberTitle = $entityMember['entityMemberTitle'] ?? $tblEntityMembers->entityMemberTitle;
                $tblEntityMembers->entityMemberOwnership = intval($entityMember['entityMemberOwnership']) ?? $tblEntityMembers->entityMemberOwnership;
                $tblEntityMembers->entityMemberPhone = Strings::cleanPhoneNo($entityMember['entityMemberPhone']) ?? $tblEntityMembers->entityMemberPhone;
                $tblEntityMembers->entityMemberCell = Strings::cleanPhoneNo($entityMember['entityMemberCell']) ?? $tblEntityMembers->entityMemberCell;
                $tblEntityMembers->entityMemberSSN = Strings::cleanPhoneNo($entityMember['entityMemberSSN']) ?? $tblEntityMembers->entityMemberSSN;
                $tblEntityMembers->entityMemberDOB = Dates::formatDateWithRE($entityMember['entityMemberDOB'], 'MDY', 'Y-m-d') ?? $tblEntityMembers->entityMemberDOB;
                $tblEntityMembers->entityMemberEmail = $entityMember['entityMemberEmail'] ?? $tblEntityMembers->entityMemberEmail;
                $tblEntityMembers->entityMemberCitizenship = $entityMember['entityMemberCitizenship'] ?? $tblEntityMembers->entityMemberCitizenship;
                $tblEntityMembers->entityMemberPersonalGuarantee = $entityMember['entityMemberPersonalGuarantee'] ?? $tblEntityMembers->entityMemberPersonalGuarantee;
                $tblEntityMembers->entityMemberAuthorizedSigner = $entityMember['entityMemberAuthorizedSigner'] ?? $tblEntityMembers->entityMemberAuthorizedSigner;
                $tblEntityMembers->Save();
            }
        }
    }

    /**
     * @param int $memberId
     * @param int $LMRId
     * @return int|null
     */
    public static function checkMemberType(int $memberId, int $LMRId): ?int
    {
        if (!$memberId || !$LMRId) {
            return 0;
        }
        $tblMemberOfficerInfo = tblMemberOfficerInfo::Get([
            'memberId' => $memberId,
            'LMRID'    => $LMRId
        ]);
        if ($tblMemberOfficerInfo) {
            $memberId = $tblMemberOfficerInfo->memberType == 'Entity' ? $tblMemberOfficerInfo->memberId : 0;
        }
        return $memberId;
    }

    public static function saveEntityMembersInfo($LMRId, $CID, $CBEID, ?int $parent_id = null, ?int $newMemberId = null)
    {
        $parentMembers = borrowerProfileEntityInfo::getRootMembers($CID, $CBEID, $parent_id);
        $count = count($parentMembers);
        $i = 1;
        foreach ($parentMembers as $member) {
            if (!$member->parent_id) {
                $member->parent_id = null;
            } else {
                if ($i <= $count) {
                    $member->parent_id = $newMemberId;
                }
            }
            $memberId = self::saveMemberInfo($LMRId, $member);
            self::saveEntityMembersInfo($LMRId, $CID, $CBEID, $member->MOID, $memberId);
            $i++;
        }
    }

    public static function saveMemberInfo($LMRId, $member): ?int
    {
        $fileLevelData = new tblMemberOfficerInfo();
        $fileLevelData->parent_id = $member->parent_id;
        $fileLevelData->LMRID = $LMRId;
        $fileLevelData->memberType = $member->memberType;
        $fileLevelData->memberCategory = $member->memberCategory;
        $fileLevelData->memberName = $member->memberName;
        $fileLevelData->memberTitle = $member->memberTitle;
        $fileLevelData->memberOwnership = $member->memberOwnership;
        $fileLevelData->memberAnnualSalary = $member->memberAnnualSalary;
        $fileLevelData->memberAddress = $member->memberAddress;
        $fileLevelData->memberPhone = $member->memberPhone;
        $fileLevelData->memberCell = $member->memberCell;
        $fileLevelData->memberSSN = $member->memberSSN;
        $fileLevelData->memberDOB = $member->memberDOB;
        $fileLevelData->memberCreditScore = $member->memberCreditScore;
        $fileLevelData->memberEmail = $member->memberEmail;
        $fileLevelData->memberDriversLicense = $member->memberDriversLicense;
        $fileLevelData->memberDriversLicenseState = $member->memberDriversLicenseState;
        $fileLevelData->memberTin = $member->memberTin;
        $fileLevelData->memberPersonalGuarantee = $member->memberPersonalGuarantee;
        $fileLevelData->memberAuthorizedSigner = $member->memberAuthorizedSigner;
        $fileLevelData->memberCitizenship = $member->memberCitizenship;
        $fileLevelData->memberMaritalStatus = $member->memberMaritalStatus;
        $fileLevelData->memberMarriageDate = $member->memberMarriageDate;
        $fileLevelData->memberDivorceDate = $member->memberDivorceDate;
        $fileLevelData->memberMaidenName = $member->memberMaidenName;
        $fileLevelData->memberSpouseName = $member->memberSpouseName;
        $fileLevelData->memberCreditScoreDate = $member->memberCreditScoreDate;
        $fileLevelData->memberRentOrOwn = $member->memberRentOrOwn;
        $fileLevelData->memberMonthlyRentOrMortgage = $member->memberMonthlyRentOrMortgage;
        $fileLevelData->memberDateMovedAddress = $member->memberDateMovedAddress;
        $fileLevelData->memberRealEstateValue = $member->memberRealEstateValue;
        $fileLevelData->memberRetirementAccountBalance = $member->memberRetirementAccountBalance;
        $fileLevelData->memberCashSavingsStocksBalance = $member->memberCashSavingsStocksBalance;
        $fileLevelData->memberCreditCardBalance = $member->memberCreditCardBalance;
        $fileLevelData->memberMortgageBalance = $member->memberMortgageBalance;
        $fileLevelData->memberAutoLoanBalance = $member->memberAutoLoanBalance;
        $fileLevelData->memberTotalNetWorth = $member->memberTotalNetWorth;
        $fileLevelData->Save();
        return $fileLevelData->memberId;
    }

    public static function deleteEntityMembers($LMRId)
    {
        if (!$LMRId) {
            return null;
        }

        $table = tblMemberOfficerInfo::GetAll([tblMemberOfficerInfo_db::COLUMN_LMRID => $LMRId]);
        foreach ($table as $row) {
            $row->Delete();
        }
    }

    public static function tblFileHMLOBackGround(
        $cnt,
        $LMRId,
        $saveTab,
        $newLMRId,
        $clientId,
        $recordDate
    )
    {
        /** Save the Background Section **/

        $q = 'SELECT HMLOBGID FROM tblFileHMLOBackGround WHERE fileID = :fileID  ';
        $resultArray = Database2::getInstance()->queryData($q, [
            'fileID' => $LMRId,
        ], null, true);


        $qu = '';
        $qs = '';
        $qc = '';
        $qv = '';
        $params = [];

        if ($saveTab == 'LR') {
            foreach (self::$leadReceiveArray as $key => $column) {
                if (isset ($_REQUEST[$key])) {
                    $qc .= $qs . " $column ";

                    $postVal = trim($_REQUEST[$key]);
                    $params[$column] = $postVal;
                    $qv .= $qs . ' :' . $column . ' ';
                    $qu .= $qs . " $column = :" . $column . ' ';

                    $qs = ', ';
                }
            }
        } else {
            foreach (self::$HMLOBGFieldsArray as $column) {
                if (isset ($_REQUEST[$column])) {
                    $qc .= $qs . " $column ";

                    if ($column == 'bankruptcyTypes') {
                        $postVal = trim(implode(',', $_REQUEST[$column]));
                        $params[$column] = $postVal;
                    } else if (in_array($column, ['incomeTaxFiledDate', 'whenWasShortSale'])) {
                        $whenWasShortSale = trim($_REQUEST[$column]);
                        if (Dates::IsEmpty($whenWasShortSale)) {
                            $whenWasShortSale = '0000-00-00';
                        } else {
                            $whenWasShortSale = trim(Dates::formatDateWithRE($whenWasShortSale, 'MDY', 'Y-m-d'));
                        }
                        $params[$column] = $whenWasShortSale;
                    } else {
                        $postVal = trim($_REQUEST[$column]);
                        $params[$column] = $postVal;
                    }
                    $qv .= $qs . ' :' . $column;
                    $qu .= $qs . " $column = :" . $column;
                    $qs = ', ';
                } elseif (isset($clientInfo[$column]) && $newLMRId > 0) {
                    $qc .= $qs . " $column ";
                    $postVal = trim($clientInfo[$column]);
                    $params[$column] = $postVal;
                    $qv .= $qs . ' :' . $column;
                    $qu .= $qs . " $column = :" . $column;
                    $qs = ', ';
                }
            }

        }

        $params['fileID'] = $LMRId;
        $params['LMRId'] = $LMRId;
        $params['CID'] = $clientId;
        $params['recordDate'] = $recordDate;
        $params['personalBankruptcy'] = intval($params['personalBankruptcy']);
        $params['statusForeclosure'] = intval($params['statusForeclosure']);

        if (count($resultArray) > 0) {
            if ($qu) {
                $qry = ' UPDATE tblFileHMLOBackGround SET ' . $qu . ' WHERE fileID = :LMRId ;';
                $cnt = Database2::getInstance()->queryData($qry, $params);
            }
        } elseif ($qc && $qv) {
            $qry = ' INSERT INTO tblFileHMLOBackGround (fileID, CID, recordDate, ' . $qc . ') VALUES ( :fileID, :CID, :recordDate ,' . $qv . ');';
            $cnt = Database2::getInstance()->insert($qry, $params);
        }


        sbaQASave::getReport(['clientId' => $clientId, 'LMRId' => $LMRId, 'recordDate' => $recordDate]);

        return $cnt;
        /***** End of SBA Background Questions */
    }

    public static function tblFileHMLOExperience(
        $cnt,
        $LMRId,
        $saveTab,
        $clientInfo,
        $recordDate,
        $clientId
    )
    {
        /** Save the Experience Section **/

        $q = 'SELECT HMLOEID FROM tblFileHMLOExperience WHERE fileID = :fileID  ';
        $resultArray = Database2::getInstance()->queryData($q, [
            'fileID' => $LMRId,
        ], null, true);


        $qu = '';
        $qs = '';
        $qc = '';
        $qv = '';
        $params = [];


        if ($saveTab == 'LR') {
            foreach (self::$leadReceiveArrayB as $key => $column) {
                if (isset ($_REQUEST[$key])) {
                    if ($key == 'haveBorREInvestmentExperience') {
                        $params[$column] = $_REQUEST[$key];
                    } else {
                        $params[$column] = Strings::replaceCommaValues(trim($_REQUEST[$key]));
                    }

                    $qc .= $qs . " $column ";
                    $qv .= $qs . " :$column ";
                    $qu .= $qs . " $column = :$column ";
                    $qs = ', ';
                    if ($params[$column]) {
                        $fieldColumn = '';
                        if ($column == 'borNoOfREPropertiesCompleted') {
                            $fieldColumn = 'haveBorREInvestmentExperience';
                        } elseif ($column == 'borNoOfOwnProp') {
                            $fieldColumn = 'haveBorOwnInvestmentProperties';
                        }
                        if ($fieldColumn) {
                            $qc .= $qs . " $fieldColumn ";
                            $qv .= $qs . " 'Yes' ";
                            $qu .= $qs . " $fieldColumn = 'Yes' ";
                        }
                    }
                }
            }
        } else {

            /* Multi Select Value */
            $borPrimaryInvestmentStrategy = '';

            if (isset($_REQUEST['borPrimaryInvestmentStrategy']) && $_REQUEST['borPrimaryInvestmentStrategy']) {
                $borPrimaryInvestmentStrategy = is_array($_REQUEST['borPrimaryInvestmentStrategy']) ? implode(',', $_REQUEST['borPrimaryInvestmentStrategy']) : $_REQUEST['borPrimaryInvestmentStrategy'];
                $params['borPrimaryInvestmentStrategy'] = $borPrimaryInvestmentStrategy;

                $qc .= $qs . ' borPrimaryInvestmentStrategy ';
                $qv .= $qs . " :borPrimaryInvestmentStrategy ";
                $qu .= $qs . " borPrimaryInvestmentStrategy = :borPrimaryInvestmentStrategy ";
                $qs = ', ';
            } elseif (isset($_REQUEST['borPrimaryInvestmentStrategyHidden'])) {
                $borPrimaryInvestmentStrategy = '';
                $params['borPrimaryInvestmentStrategy'] = $borPrimaryInvestmentStrategy;

                $qc .= $qs . ' borPrimaryInvestmentStrategy ';
                $qv .= $qs . " :borPrimaryInvestmentStrategy ";
                $qu .= $qs . " borPrimaryInvestmentStrategy = :borPrimaryInvestmentStrategy ";
                $qs = ', ';
            }

            $coBorPrimaryInvestmentStrategy = '';
            if (isset($_REQUEST['coBorPrimaryInvestmentStrategy']) && $_REQUEST['coBorPrimaryInvestmentStrategy']) {
                $coBorPrimaryInvestmentStrategy = is_array($_REQUEST['coBorPrimaryInvestmentStrategy']) ? implode(',', $_REQUEST['coBorPrimaryInvestmentStrategy']) : $_REQUEST['coBorPrimaryInvestmentStrategy'];
                $params['coBorPrimaryInvestmentStrategy'] = $coBorPrimaryInvestmentStrategy;

                $qc .= $qs . ' coBorPrimaryInvestmentStrategy ';
                $qv .= $qs . " :coBorPrimaryInvestmentStrategy ";
                $qu .= $qs . " coBorPrimaryInvestmentStrategy = :coBorPrimaryInvestmentStrategy ";
                $qs = ', ';
            } elseif (isset($_REQUEST['coBorPrimaryInvestmentStrategyHidden'])) {
                $coBorPrimaryInvestmentStrategy = '';
                $params['coBorPrimaryInvestmentStrategy'] = $coBorPrimaryInvestmentStrategy;

                $qc .= $qs . ' coBorPrimaryInvestmentStrategy ';
                $qv .= $qs . " :coBorPrimaryInvestmentStrategy ";
                $qu .= $qs . " coBorPrimaryInvestmentStrategy = :coBorPrimaryInvestmentStrategy ";
                $qs = ', ';
            }

            if (isset($_REQUEST['geographicAreas'])) {
                $geographicAreas = implode(',', $_REQUEST['geographicAreas']);

                $params['geographicAreas'] = $geographicAreas;

                $qc .= $qs . ' geographicAreas ';
                $qv .= $qs . " :geographicAreas ";
                $qu .= $qs . " geographicAreas = :geographicAreas ";
                $qs = ', ';
            }

            if (isset($_REQUEST['coBorLicenseNo'])) {
                $coBorLicenseNo = $_REQUEST['coBorLicenseNo'];
                $params['coBorLicenseNo'] = $coBorLicenseNo;

                $qc .= $qs . ' coBorLicenseNo ';
                $qv .= $qs . " :coBorLicenseNo ";
                $qu .= $qs . " coBorLicenseNo = :coBorLicenseNo ";
                $qs = ', ';
            }


            foreach (self::$HMLOExpFieldsArray as $column) {

                if (isset ($_REQUEST[$column])) {
                    $qc .= $qs . " $column ";
                    if (in_array($column, self::$typTxnReplaceCommaValuesArray)) {
                        $postVal = trim(Strings::replaceCommaValues($_REQUEST[$column]));
                    } else {
                        $postVal = HTTP::escapeQuoteForPOST($_REQUEST[$column]);
                    }
                    $params[$column] = $postVal;

                    $qv .= $qs . " :$column ";
                    $qu .= $qs . " $column = :$column ";
                    $qs = ', ';
                } elseif (
                    array_key_exists(trim($column), $clientInfo)
                    && (trim($clientInfo[$column])
                        && trim($clientInfo[$column]) != 'NA'
                        && trim($clientInfo[$column]) != '0'
                        && $LMRId < 0)
                ) {
                    $postVal = trim($clientInfo[$column]);
                    $params[$column] = $postVal;

                    $qc .= $qs . " $column ";
                    $qv .= $qs . " :$column ";
                    $qu .= $qs . " $column = :$column ";
                    $qs = ', ';
                }
            }
        }

        if (isset($params['borNoOfFlippingExperience'])) {
            $params['borNoOfFlippingExperience'] = intval($params['borNoOfFlippingExperience']) ?: null;
        }

        if (isset($params['coBorNoOfFlippingExperience'])) {
            $params['coBorNoOfFlippingExperience'] = intval($params['coBorNoOfFlippingExperience']);
        }

        if (isset($params['borNoOfREPropertiesCompleted'])) {
            $params['borNoOfREPropertiesCompleted'] = intval($params['borNoOfREPropertiesCompleted']);
        }

        if (isset($params['coBorNoOfREPropertiesCompleted'])) {
            $params['coBorNoOfREPropertiesCompleted'] = intval($params['coBorNoOfREPropertiesCompleted']);
        }

        if (isset($params['coBorNoOfProSellCompleted'])) {
            $params['coBorNoOfProSellCompleted'] = intval($params['coBorNoOfProSellCompleted']);
        }

        if (isset($params['borNoOfYearRehabExperience'])) {
            $params['borNoOfYearRehabExperience'] = intval($params['borNoOfYearRehabExperience']);
        }

        if (isset($params['borRehabPropCompleted'])) {
            $params['borRehabPropCompleted'] = intval($params['borRehabPropCompleted']);
        }

        if (isset($params['borNoOfProjectCurrently'])) {
            $params['borNoOfProjectCurrently'] = intval($params['borNoOfProjectCurrently']);
        }

        if (isset($params['borNoOfOwnProp'])) {
            $params['borNoOfOwnProp'] = intval($params['borNoOfOwnProp']);
        }

        if (isset($params['coBorNoOfYearRehabExperience'])) {
            $params['coBorNoOfYearRehabExperience'] = intval($params['coBorNoOfYearRehabExperience']);
        }

        if (isset($params['coBorRehabPropCompleted'])) {
            $params['coBorRehabPropCompleted'] = intval($params['coBorRehabPropCompleted']);
        }

        if (isset($params['coBorNoOfProjectCurrently'])) {
            $params['coBorNoOfProjectCurrently'] = intval($params['coBorNoOfProjectCurrently']);
        }

        if (isset($params['coBorNoOfOwnProp'])) {
            $params['coBorNoOfOwnProp'] = intval($params['coBorNoOfOwnProp']);
        }

        if (isset($params['coBorNoOfProSellExperience'])) {
            $params['coBorNoOfProSellExperience'] = intval($params['coBorNoOfProSellExperience']);
        }

        if (isset($params['constructionDrawsPerProject'])) {
            $params['constructionDrawsPerProject'] = intval($params['constructionDrawsPerProject']);
        }

        if (isset($params['constructionDrawsPerProjectTo'])) {
            $params['constructionDrawsPerProjectTo'] = intval($params['constructionDrawsPerProjectTo']);
        }

        if (isset($params['monthsPurchaseDateToFirstConst'])) {
            $params['monthsPurchaseDateToFirstConst'] = intval($params['monthsPurchaseDateToFirstConst']);
        }

        if (isset($params['monthsPurchaseDateToFirstConstTo'])) {
            $params['monthsPurchaseDateToFirstConstTo'] = intval($params['monthsPurchaseDateToFirstConstTo']);
        }

        if (isset($params['monthsPurchaseDateUntilConst'])) {
            $params['monthsPurchaseDateUntilConst'] = intval($params['monthsPurchaseDateUntilConst']);
        }

        if (isset($params['monthsPurchaseDateUntilConstTo'])) {
            $params['monthsPurchaseDateUntilConstTo'] = intval($params['monthsPurchaseDateUntilConstTo']);
        }

        if (isset($params['monthsPurchaseDateToSaleDate'])) {
            $params['monthsPurchaseDateToSaleDate'] = intval($params['monthsPurchaseDateToSaleDate']);
        }

        if (isset($params['monthsPurchaseDateToSaleDateTo'])) {
            $params['monthsPurchaseDateToSaleDateTo'] = intval($params['monthsPurchaseDateToSaleDateTo']);
        }

        if (isset($params['borNoOfProSellExperience'])) {
            $params['borNoOfProSellExperience'] = intval($params['borNoOfProSellExperience']);
        }

        if (isset($params['borNoOfProSellCompleted'])) {
            $params['borNoOfProSellCompleted'] = intval($params['borNoOfProSellCompleted']);
        }

        if (isset($params['NoOfSuchProjects'])) {
            $params['NoOfSuchProjects'] = intval($params['NoOfSuchProjects']);
        }

        if (isset($params['NoOfSuchProjectsTo'])) {
            $params['NoOfSuchProjectsTo'] = intval($params['NoOfSuchProjectsTo']);
        }
        if (isset($params['trackRecord'])) {
            $params['trackRecord'] = $params['trackRecord'] ? intval($params['trackRecord']) : null;
        }
        $params['LMRId'] = $LMRId;
        $params['clientId'] = $clientId;
        $params['recordDate'] = $recordDate;

        if (count($resultArray) > 0) {
            if ($qu) {
                $qry = ' UPDATE tblFileHMLOExperience SET ' . $qu . ' WHERE fileID = :LMRId ;';
                $cnt = Database2::getInstance()->update($qry, $params);
            }
        } elseif ($qc && $qv) {
            $qry = ' INSERT INTO tblFileHMLOExperience (fileID, CID,  recordDate, ' . $qc . ") 
            VALUES ( :LMRId , :clientId , :recordDate ," . $qv . ');';
            $cnt = Database2::getInstance()->insert($qry, $params);
        }
        return $cnt;
    }

    public static function tblFileExpFilpGroundUp_Flip(
        $userName,
        $UGroup,
        $LMRId,
        $recordDate,
        $countOfPastDeals
    )
    {
        $qry = '';
        $tCnt = 0;
        $params = [];

        for ($ag = 0; $ag < $countOfPastDeals; $ag++) {
            $qc = '';
            $qv = '';
            $ap = '';
            if (isset($_REQUEST['borFlipPropType'])) {
                $params['borFlipPropType' . $ag] = trim($_REQUEST['borFlipPropType'][$ag]);
                $qc .= $ap . ' propertyType ';
                $qv .= $ap . " :" . 'borFlipPropType' . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipPurchaseDate'])) {
                $val = Dates::formatDateWithRE($_REQUEST['borFlipPurchaseDate'][$ag], 'MDY', 'Y-m-d');
                $val = $val ? Dates::Datestamp($val) : '0000-00-00';
                $params['borFlipPurchaseDate' . $ag] = $val;

                $qc .= $ap . ' purchaseDate ';
                $qv .= $ap . " :borFlipPurchaseDate" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipPurchasePrice'])) {
                $params['borFlipPurchasePrice' . $ag] = Strings::replaceCommaValues($_REQUEST['borFlipPurchasePrice'][$ag]);

                $qc .= $ap . ' purchasePrice ';
                $qv .= $ap . " :borFlipPurchasePrice" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipAmountFinanced'])) {
                $params['borFlipAmountFinanced' . $ag] = Strings::replaceCommaValues($_REQUEST['borFlipAmountFinanced'][$ag]);

                $qc .= $ap . ' amountFinanced ';
                $qv .= $ap . " :borFlipAmountFinanced" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipRehabBudget'])) {
                $params['borFlipRehabBudget' . $ag] = Strings::replaceCommaValues($_REQUEST['borFlipRehabBudget'][$ag]);

                $qc .= $ap . ' rehabBudget ';
                $qv .= $ap . " :borFlipRehabBudget" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipEntityName'])) {
                $params['borFlipEntityName' . $ag] = trim($_REQUEST['borFlipEntityName'][$ag]);

                $qc .= $ap . ' entityName ';
                $qv .= $ap . " :borFlipEntityName" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipOwnership'])) {
                $params['borFlipOwnership' . $ag] = Strings::replaceCommaValues($_REQUEST['borFlipOwnership'][$ag]);

                $qc .= $ap . ' ownership ';
                $qv .= $ap . " :borFlipOwnership" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borFlipOwnershipStatus'])) {
                $params['borFlipOwnershipStatus' . $ag] = $_REQUEST['borFlipOwnershipStatus'][$ag];

                $qc .= $ap . ' ownershipStatus ';
                $qv .= $ap . " :borFlipOwnershipStatus" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipExit'])) {
                $params['borFlipExit' . $ag] = trim($_REQUEST['borFlipExit'][$ag]);

                $qc .= $ap . ' exitValues ';
                $qv .= $ap . " :borFlipExit" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipSalePrice'])) {
                $params['borFlipSalePrice' . $ag] = Strings::replaceCommaValues($_REQUEST['borFlipSalePrice'][$ag]);

                $qc .= $ap . ' salePrice ';
                $qv .= $ap . " :borFlipSalePrice" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipSaleDate'])) {
                $val = Dates::formatDateWithRE($_REQUEST['borFlipSaleDate'][$ag], 'MDY', 'Y-m-d');
                $val = $val ? Dates::Datestamp($val) : '0000-00-00';
                $params['borFlipSaleDate' . $ag] = $val;

                $qc .= $ap . ' saleDate ';
                $qv .= $ap . " :borFlipSaleDate" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipMonthlyRent'])) {
                $params['borFlipMonthlyRent' . $ag] = Strings::replaceCommaValues($_REQUEST['borFlipMonthlyRent'][$ag]);

                $qc .= $ap . ' monthlyRent ';
                $qv .= $ap . " :borFlipMonthlyRent" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipAddress'])) {
                $params['borFlipAddress' . $ag] = trim($_REQUEST['borFlipAddress'][$ag]);

                $qc .= $ap . ' address ';
                $qv .= $ap . " :borFlipAddress" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipCity'])) {
                $params['borFlipCity' . $ag] = trim($_REQUEST['borFlipCity'][$ag]);

                $qc .= $ap . ' city ';
                $qv .= $ap . " :borFlipCity" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipState'])) {
                $params['borFlipState' . $ag] = trim($_REQUEST['borFlipState'][$ag]);

                $qc .= $ap . ' state ';
                $qv .= $ap . " :borFlipState" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borFlipZip'])) {
                $params['borFlipZip' . $ag] = substr(trim($_REQUEST['borFlipZip'][$ag]), 0, 10);

                $qc .= $ap . ' zip ';
                $qv .= $ap . " :borFlipZip" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borFlipUnit'])) {
                $params['borFlipUnit' . $ag] = trim($_REQUEST['borFlipUnit'][$ag]);

                $qc .= $ap . ' unit ';
                $qv .= $ap . " :borFlipUnit" . $ag . " ";
                $ap = ', ';
            }

            if (isset($_REQUEST['borOutcomeRE'])) {
                $params['borOutcomeRE' . $ag] = Strings::replaceCommaValues($_REQUEST['borOutcomeRE'][$ag]);

                $qc .= $ap . ' Outcome ';
                $qv .= $ap . " :borOutcomeRE" . $ag . " ";
                $ap = ', ';
            }

            if ($qc && $qv) {
                $tCnt++;
            }

            $qc .= $ap . ' createdBy ';
            if ($userName && $UGroup) {
                $params['createdBy'] = trim($userName . ' (' . $UGroup . ')');
                $qv .= $ap . " :createdBy ";
            } else {
                $qv .= $ap . " '' ";
            }

            $params['LMRId'] = $LMRId;
            $params['userType'] = 'B';
            $params['expType'] = 'Flip';
            $params['recordDate'] = $recordDate;
            if ($tCnt > 0) {
                $qry .= 'INSERT INTO tblFileExpFilpGroundUp (LMRId, userType, expType, recordDate, ' . $qc . ') 
                        VALUES ( :LMRId, :userType, :expType, :recordDate, ' . $qv . '); ';
            }
        }
        if ($tCnt > 0 && $qry) {
            $qryDel = " DELETE FROM tblFileExpFilpGroundUp WHERE LMRId = :LMRId AND expType = 'Flip'; ";
            Database2::getInstance()->update($qryDel, [
                'LMRId' => $LMRId,
            ]);
            Database2::getInstance()->executeQuery($qry, $params);
        }
    }

    public static function tblFileExpFilpGroundUp_Gup(
        $userName,
        $UGroup,
        $LMRId,
        $recordDate,
        $countOfPastDeals
    )
    {
        /**
         * Save Ground Up Experience Information
         */
        $qry = '';
        $tCnt = 0;
        $params = [];

        for ($ag = 0; $ag < $countOfPastDeals; $ag++) {
            $qc = '';
            $qv = '';
            $ap = '';
            if (isset($_REQUEST['borGUPropType'])) {
                $params['borGUPropType' . $ag] = trim($_REQUEST['borGUPropType'][$ag]);
                $qc .= $ap . ' propertyType ';
                $qv .= $ap . " :borGUPropType" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUPurchaseDate'])) {
                $val = Dates::formatDateWithRE($_REQUEST['borGUPurchaseDate'][$ag], 'MDY', 'Y-m-d');
                $val = $val ? Dates::Datestamp($val) : '0000-00-00';

                $params['borGUPurchaseDate' . $ag] = $val;
                $qc .= $ap . ' purchaseDate ';
                $qv .= $ap . " :borGUPurchaseDate" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUPurchasePrice'])) {
                $params['borGUPurchasePrice' . $ag] = Strings::replaceCommaValues($_REQUEST['borGUPurchasePrice'][$ag]);

                $qc .= $ap . ' purchasePrice ';
                $qv .= $ap . " :borGUPurchasePrice" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUAmountFinanced'])) {
                $params['borGUAmountFinanced' . $ag] = Strings::replaceCommaValues($_REQUEST['borGUAmountFinanced'][$ag]);

                $qc .= $ap . ' amountFinanced ';
                $qv .= $ap . " :borGUAmountFinanced" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGURehabBudget'])) {
                $params['borGURehabBudget' . $ag] = Strings::replaceCommaValues($_REQUEST['borGURehabBudget'][$ag]);

                $qc .= $ap . ' rehabBudget ';
                $qv .= $ap . " :borGURehabBudget" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUEntityName'])) {
                $params['borGUEntityName' . $ag] = trim($_REQUEST['borGUEntityName'][$ag]);
                $qc .= $ap . ' entityName ';
                $qv .= $ap . " :borGUEntityName" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUOwnership'])) {
                $params['borGUOwnership' . $ag] = Strings::replaceCommaValues($_REQUEST['borGUOwnership'][$ag]);
                $qc .= $ap . ' ownership ';
                $qv .= $ap . " :borGUOwnership" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUOwnershipStatus'])) {
                $params['borGUOwnershipStatus' . $ag] = $_REQUEST['borGUOwnershipStatus'][$ag];
                $qc .= $ap . ' ownershipStatus ';
                $qv .= $ap . " :borGUOwnershipStatus" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUExit'])) {
                $params['borGUExit' . $ag] = trim($_REQUEST['borGUExit'][$ag]);
                $qc .= $ap . ' exitValues ';
                $qv .= $ap . " :borGUExit" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUSalePrice'])) {
                $params['borGUSalePrice' . $ag] = Strings::replaceCommaValues($_REQUEST['borGUSalePrice'][$ag]);

                $qc .= $ap . ' salePrice ';
                $qv .= $ap . " :borGUSalePrice" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUSaleDate'])) {
                $val = Dates::formatDateWithRE($_REQUEST['borGUSaleDate'][$ag], 'MDY', 'Y-m-d');
                $val = $val ? Dates::Datestamp($val) : '0000-00-00';

                $params['borGUSaleDate' . $ag] = $val;

                $qc .= $ap . ' saleDate ';
                $qv .= $ap . " :borGUSaleDate" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUMonthlyRent'])) {
                $params['borGUMonthlyRent' . $ag] = Strings::replaceCommaValues($_REQUEST['borGUMonthlyRent'][$ag]);

                $qc .= $ap . ' monthlyRent ';
                $qv .= $ap . " :borGUMonthlyRent" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUAddress'])) {
                $params['borGUAddress' . $ag] = trim($_REQUEST['borGUAddress'][$ag]);
                $qc .= $ap . ' address ';
                $qv .= $ap . " :borGUAddress" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUCity'])) {
                $params['borGUCity' . $ag] = trim($_REQUEST['borGUCity'][$ag]);

                $qc .= $ap . ' city ';
                $qv .= $ap . " :borGUCity" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUState'])) {
                $params['borGUState' . $ag] = trim($_REQUEST['borGUState'][$ag]);

                $qc .= $ap . ' state ';
                $qv .= $ap . " :borGUState" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUZip'])) {
                $params['borGUZip' . $ag] = substr(trim($_REQUEST['borGUZip'][$ag]), 0, 10);

                $qc .= $ap . ' zip ';
                $qv .= $ap . " :borGUZip" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borGUUnit'])) {
                $params['borGUUnit' . $ag] = trim($_REQUEST['borGUUnit'][$ag]);

                $qc .= $ap . ' unit ';
                $qv .= $ap . " :borGUUnit" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borOutcomeRC'])) {
                $params['borOutcomeRC' . $ag] = Strings::replaceCommaValues($_REQUEST['borOutcomeRC'][$ag]);

                $qc .= $ap . ' Outcome ';
                $qv .= $ap . " :borOutcomeRC" . $ag . " ";
                $ap = ', ';
            }

            if ($qc && $qv) $tCnt++;

            $qc .= $ap . ' createdBy ';
            if ($userName && $UGroup) {
                $params['createdBy'] = trim($userName . ' (' . $UGroup . ')');
                $qv .= $ap . " :createdBy" . $ag . " ";
            } else {
                $qv .= $ap . " '' ";
            }

            if ($tCnt > 0) {
                $qry .= ' INSERT INTO tblFileExpFilpGroundUp (LMRId, userType, expType, recordDate, ' . $qc . ') 
                VALUES
                 (' . $LMRId . ", 'B', 'Gup', '" . $recordDate . "', " . $qv . ');
                 ';
            }
        }
        if ($tCnt > 0 && $qry) {
            $qryDel = " DELETE FROM tblFileExpFilpGroundUp WHERE LMRId = :LMRId AND expType = 'Gup'; ";
            Database2::getInstance()->update($qryDel, [
                'LMRId' => $LMRId,
            ]);
            Database2::getInstance()->executeQuery($qry, $params);
        }
    }

    public static function tblFileExpFilpGroundUp_Sell(
        $userName,
        $UGroup,
        $LMRId,
        $recordDate,
        $countOfPastDeals
    )
    {
        /**
         * Save Selling Properties Experience Information
         */
        $qry = '';
        $tCnt = 0;
        $params = [];
        for ($ag = 0; $ag < $countOfPastDeals; $ag++) {
            $qc = '';
            $qv = '';
            $ap = '';
            if (isset($_REQUEST['borSellPropType'])) {
                $params['borSellPropType' . $ag] = trim($_REQUEST['borSellPropType'][$ag]);
                $qc .= $ap . ' propertyType ';
                $qv .= $ap . " :borSellPropType" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellPurchaseDate'])) {
                $val = Dates::formatDateWithRE($_REQUEST['borSellPurchaseDate'][$ag], 'MDY', 'Y-m-d');
                $val = $val ? Dates::Datestamp($val) : '0000-00-00';
                $params['borSellPurchaseDate' . $ag] = $val;
                $qc .= $ap . ' purchaseDate ';
                $qv .= $ap . " :borSellPurchaseDate" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellPurchasePrice'])) {
                $params['borSellPurchasePrice' . $ag] = Strings::replaceCommaValues($_REQUEST['borSellPurchasePrice'][$ag]);
                $qc .= $ap . ' purchasePrice ';
                $qv .= $ap . " :borSellPurchasePrice" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellAmountFinanced'])) {
                $params['borSellAmountFinanced' . $ag] = Strings::replaceCommaValues($_REQUEST['borSellAmountFinanced'][$ag]);
                $qc .= $ap . ' amountFinanced ';
                $qv .= $ap . " :borSellAmountFinanced" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellRehabBudget'])) {
                $params['borSellRehabBudget' . $ag] = Strings::replaceCommaValues($_REQUEST['borSellRehabBudget'][$ag]);
                $qc .= $ap . ' rehabBudget ';
                $qv .= $ap . " :borSellRehabBudget" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellEntityName'])) {
                $params['borSellEntityName' . $ag] = trim($_REQUEST['borSellEntityName'][$ag]);
                $qc .= $ap . ' entityName ';
                $qv .= $ap . " :borSellEntityName" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellOwnership'])) {
                $params['borSellOwnership' . $ag] = Strings::replaceCommaValues($_REQUEST['borSellOwnership'][$ag]);
                $qc .= $ap . ' ownership ';
                $qv .= $ap . " :borSellOwnership" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellOwnershipStatus'])) {
                $params['borSellOwnershipStatus' . $ag] = $_REQUEST['borSellOwnershipStatus'][$ag];
                $qc .= $ap . ' ownershipStatus ';
                $qv .= $ap . " :borSellOwnershipStatus" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellExit'])) {
                $params['borSellExit' . $ag] = trim($_REQUEST['borSellExit'][$ag]);
                $qc .= $ap . ' exitValues ';
                $qv .= $ap . " :borSellExit" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellSalePrice'])) {
                $params['borSellSalePrice' . $ag] = Strings::replaceCommaValues($_REQUEST['borSellSalePrice'][$ag]);
                $qc .= $ap . ' salePrice ';
                $qv .= $ap . " :borSellSalePrice" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellSaleDate'])) {
                $val = Dates::formatDateWithRE($_REQUEST['borSellSaleDate'][$ag], 'MDY', 'Y-m-d');
                $val = $val ? Dates::Datestamp($val) : '0000-00-00';
                $params['borSellSaleDate' . $ag] = $val;

                $qc .= $ap . ' saleDate ';
                $qv .= $ap . " :borSellSaleDate" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellMonthlyRent'])) {
                $params['borSellMonthlyRent' . $ag] = Strings::replaceCommaValues($_REQUEST['borSellMonthlyRent'][$ag]);
                $qc .= $ap . ' monthlyRent ';
                $qv .= $ap . " :borSellMonthlyRent" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellAddress'])) {
                $params['borSellAddress' . $ag] = trim($_REQUEST['borSellAddress'][$ag]);
                $qc .= $ap . ' address ';
                $qv .= $ap . " :borSellAddress" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellCity'])) {
                $params['borSellCity' . $ag] = trim($_REQUEST['borSellCity'][$ag]);
                $qc .= $ap . ' city ';
                $qv .= $ap . " :borSellCity" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellState'])) {
                $params['borSellState' . $ag] = trim($_REQUEST['borSellState'][$ag]);
                $qc .= $ap . ' state ';
                $qv .= $ap . " :borSellState" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellZip'])) {
                $params['borSellZip' . $ag] = substr(trim($_REQUEST['borSellZip'][$ag]), 0, 10);
                $qc .= $ap . ' zip ';
                $qv .= $ap . " :borSellZip" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellUnit'])) {
                $params['borSellUnit' . $ag] = trim($_REQUEST['borSellUnit'][$ag]);
                $qc .= $ap . ' unit ';
                $qv .= $ap . " :borSellUnit" . $ag . " ";
                $ap = ', ';
            }
            if (isset($_REQUEST['borSellOutcomeRE'])) {
                $params['borSellOutcomeRE' . $ag] = Strings::replaceCommaValues($_REQUEST['borSellOutcomeRE'][$ag]);

                $qc .= $ap . ' Outcome ';
                $qv .= $ap . " :borSellOutcomeRE" . $ag . " ";
                $ap = ', ';
            }

            if ($qc && $qv) $tCnt++;

            $qc .= $ap . ' createdBy ';
            if ($userName && $UGroup) {
                $params['createdBy'] = trim($userName . ' (' . $UGroup . ')');
                $qv .= $ap . " :createdBy ";
            } else {
                $qv .= $ap . " '' ";
            }
            if ($tCnt > 0) {
                $qry .= ' INSERT INTO tblFileExpFilpGroundUp (LMRId, userType, expType, recordDate, ' . $qc . ')
                 VALUES 
                 (' . $LMRId . ", 'B', 'Sell', '" . $recordDate . "', " . $qv . ');';

            }
        }
        if ($tCnt > 0 && $qry) {
            $qryDel = " DELETE FROM tblFileExpFilpGroundUp WHERE LMRId = :LMRId AND expType = 'Sell'; ";
            Database2::getInstance()->update($qryDel, [
                'LMRId' => $LMRId,
            ]);
            Database2::getInstance()->executeQuery($qry, $params);
        }
    }

    public static function tblFileHMLO(
        $cnt,
        $LMRId,
        $saveTab,
        $recordDate
    )
    {
        /** Save the HMLO Modules Fields **/

        $tblFileHMLO = tblFileHMLO::get(['fileID' => $LMRId]) ?? new tblFileHMLO();
        if (!$tblFileHMLO->HMLOID) {
            $tblFileHMLO->recordDate = $recordDate;
        }
        $tblFileHMLO->fileID = $LMRId;
        if ($saveTab == 'LR') {
            foreach (self::$leadReceiveArrayC as $key => $column) {
                if (isset($_REQUEST[$key])) {
                    $postVal = trim($_REQUEST[$key]);
                    $tblFileHMLO->$column = $postVal;
                }
            }
        } else {
            /** DIRECT Variables **/
            $_REQUEST['borCompanyName'] = $_REQUEST['borCompanyName'] ?? '';
            foreach (self::$HMLOArray as $column) {
                if (isset($_REQUEST[$column])) {
                    if ($column == 'servicingMemberInfo') {
                        $postVal = implode(',', $_REQUEST[$column]);
                    } elseif (in_array($column, self::$HMLODateFieldsArray)) {
                        $postVal = $_REQUEST[$column] ? Dates::Datestamp($_REQUEST[$column]) : '0000-00-00';
                    } else if ($column == 'numberOfDependents') {
                        $postVal = intVal($_REQUEST[$column]);
                    } else {
                        $postVal = trim($_REQUEST[$column]);
                    }
                    $tblFileHMLO->$column = $postVal;
                }
            }
        }
        $tblFileHMLO->save();

        return $tblFileHMLO->HMLOID;
    }

    public static function tblFileLoanOrigination(
        $cnt,
        $LMRId,
        $recordDate
    ): ?int
    {
        if (!$LMRId) {
            return 0;
        }
        $table = tblFileLoanOrigination::Get(['fileID' => $LMRId]) ?? new tblFileLoanOrigination();
        $table->fileID = intval($LMRId);
        $table->borYearsOfSchool = Request::isset('borYearsOfSchool') ? intval(Request::GetClean('borYearsOfSchool')) : 0;
        $table->dependentsNotListed = Request::isset('dependentsNotListed') ? intval(Request::GetClean('dependentsNotListed')) : 0;
        $table->dependentsAges = Request::isset('dependentsAges') ? Request::GetClean('dependentsAges') : null;
        $table->borNoOfYrAtMailingAddr = Request::isset('borNoOfYrAtMailingAddr') ? intval(Request::GetClean('borNoOfYrAtMailingAddr')) : 0;
        $table->borResidedPresentAddr = Request::isset('borResidedPresentAddr') ? Request::GetClean('borResidedPresentAddr') : null;
        $table->borNoOfYrAtPrevAddr = Request::isset('borNoOfYrAtPrevAddr') ? intval(Request::GetClean('borNoOfYrAtPrevAddr')) : 0;
        $table->coBYearsOfSchool = Request::isset('coBYearsOfSchool') ? intval(Request::GetClean('coBYearsOfSchool')) : 0;
        $table->coBNoOfYrAtMailingAddr = Request::isset('coBNoOfYrAtMailingAddr') ? intval(Request::GetClean('coBNoOfYrAtMailingAddr')) : 0;
        $table->coBResidedPresentAddr = Request::isset('coBResidedPresentAddr') ? Request::GetClean('coBResidedPresentAddr') : null;
        $table->coBNoOfYrAtPrevAddr = Request::isset('coBNoOfYrAtPrevAddr') ? intval(Request::GetClean('coBNoOfYrAtPrevAddr')) : 0;
        $table->recordDate = Dates::Timestamp();
        $table->Save();
        return $table->LOID;
    }

    public static function tblFileHMLOPropInfo(
        $cnt,
        $LMRId,
        $recordDate
    )
    {
        /** Save the Where are you in the process? field in Borrower Contact Info Section **/

        $q = 'SELECT HMLOPID FROM tblFileHMLOPropInfo WHERE fileID = :fileID ';
        $resultArray = Database2::getInstance()->queryData($q, [
            'fileID' => $LMRId,
        ], null, true);

        /** DIRECT Variables **/

        $qu = '';
        $qs = '';
        $qc = '';
        $qv = '';
        $params = [];
        foreach (self::$fileHMLOPropInfo as $column) {
            if (isset ($_REQUEST[$column])) {
                if ($column == 'fundingDate') {
                    $fundingDate = trim($_REQUEST['fundingDate']);
                    if (Dates::IsEmpty($fundingDate)) {
                        $fundingDate = '0000-00-00';
                    } else {
                        $fundingDate = trim(Dates::formatDateWithRE($fundingDate, 'MDY', 'Y-m-d'));
                    }
                    $params['fundingDate'] = $fundingDate;
                    $qc .= $qs . ' fundingDate ';
                    $qv .= $qs . " :fundingDate ";

                    $qu .= $qs . " fundingDate = :fundingDate ";
                } else {
                    $qc .= $qs . " $column ";
                    $postVal = trim($_REQUEST[$column]);
                    $params[$column] = $postVal;

                    $qv .= $qs . " :" . $column . " ";
                    $qu .= $qs . " $column = :" . $column . " ";
                }
                $qs = ', ';
            }
        }

        if (count($resultArray) > 0) {
            if ($qu) {
                $qry = ' UPDATE tblFileHMLOPropInfo SET ' . $qu . ' WHERE fileID = ' . $LMRId . ';';
                $cnt = Database2::getInstance()->update($qry, $params);
            }
        } elseif ($qc && $qv) {
            $qry = ' INSERT INTO tblFileHMLOPropInfo (fileID, recordDate, ' . $qc . ") VALUES ('" . $LMRId . "', '" . $recordDate . "', " . $qv . ');';
            $cnt = Database2::getInstance()->insert($qry, $params);
        }
        return $cnt;
    }

    /**
     * @param $ip
     * @return array|int
     */
    public static function getReport($ip)
    {
        $memberAnnualSalary = [];
        $memberAddress = $memberDriversLicense = $memberDriversLicenseState = [];
        $memberPhone = [];
        $memberCell = [];
        $memberSSN = [];
        $memberDOB = [];
        $memberEmail = [];
        $memberCreditScore = [];
        $getClientInfo = $clientInfo = $entityInfo = [];
        /** POST Variables **/
        $LMRId = $ip['LMRId'];
        $saveTab = '';
        if (array_key_exists('saveTab', $ip)) $saveTab = $ip['saveTab'];
        $memberName = [];
        $memberTitle = [];
        $memberOwnership = [];
        $cnt = 0;
        $userName = '';
        $newLMRId = 0;
        $CBEID = 0;
        $recordDate = Dates::Timestamp();
        $cliEntity = [];
        $clientId = 0;
        $PCID = 0;
        $UGroup = '';
        $UID = 0;
        $entityMembersDetails = [];


        if (array_key_exists('UID', $ip)) $UID = $ip['UID'];
        if (array_key_exists('URole', $ip)) $UGroup = $ip['URole'];
        if (array_key_exists('PCID', $ip)) $PCID = $ip['PCID'];
        if (array_key_exists('clientId', $ip)) $clientId = $ip['clientId'];
        if (array_key_exists('userName', $_REQUEST)) $userName = $_REQUEST['userName'];

        if (array_key_exists('memberName', $_REQUEST)) $memberName = $_REQUEST['memberName'];
        if (array_key_exists('memberTitle', $_REQUEST)) $memberTitle = $_REQUEST['memberTitle'];
        if (array_key_exists('memberOwnership', $_REQUEST)) $memberOwnership = $_REQUEST['memberOwnership'];
        if (array_key_exists('memberAnnualSalary', $_REQUEST)) $memberAnnualSalary = $_REQUEST['memberAnnualSalary'];
        if (array_key_exists('memberAddress', $_REQUEST)) $memberAddress = $_REQUEST['memberAddress'];
        if (array_key_exists('memberDriversLicense', $_REQUEST)) $memberDriversLicense = $_REQUEST['memberDriversLicense'];
        if (array_key_exists('memberDriversLicenseState', $_REQUEST)) $memberDriversLicenseState = $_REQUEST['memberDriversLicenseState'];
        if (array_key_exists('memberPhone', $_REQUEST)) $memberPhone = $_REQUEST['memberPhone'];
        if (array_key_exists('memberCell', $_REQUEST)) $memberCell = $_REQUEST['memberCell'];
        if (array_key_exists('memberSSN', $_REQUEST)) $memberSSN = $_REQUEST['memberSSN'];
        if (array_key_exists('memberDOB', $_REQUEST)) $memberDOB = $_REQUEST['memberDOB'];
        if (array_key_exists('memberEmail', $_REQUEST)) $memberEmail = $_REQUEST['memberEmail'];
        if (array_key_exists('memberCreditScore', $_REQUEST)) $memberCreditScore = $_REQUEST['memberCreditScore'];
        if (array_key_exists('CBEID', $_REQUEST)) $CBEID = $_REQUEST['CBEID'];
        if (array_key_exists('newLMRId', $ip)) $newLMRId = $ip['newLMRId'];

        if (PageVariables::$allowNestedEntityMembers) { // Nested Entity Members - Enabled
            $entityMembersDetails = $ip['p']['members'] ?? [];
        }


        /* Get Client Information */
        if (array_key_exists('getClientInfo', $ip)) $getClientInfo = $ip['getClientInfo'];
        if (array_key_exists('clientInfo', $getClientInfo)) $clientInfo = $getClientInfo['clientInfo'][0];
        if (array_key_exists('entityInfo', $getClientInfo)) $entityInfo = $getClientInfo['entityInfo'];
        if (count($entityInfo) > 0) {
            for ($i = 0; $i < count($entityInfo); $i++) {
                if ($entityInfo[$i]['CBEID'] == $CBEID) {
                    $cliEntity = $entityInfo[$i];
                    break;
                }
            }
        }

        if ($LMRId == 0) {
            return $cnt;
        }

        self::tblFileHMLOBusinessEntity(
            $LMRId,
            $memberName,
            $memberTitle,
            $memberOwnership,
            $memberAnnualSalary,
            $memberAddress,
            $memberDriversLicense,
            $memberDriversLicenseState,
            $memberEmail,
            $memberPhone,
            $memberCell,
            $memberSSN,
            $memberDOB,
            $memberCreditScore,
            $clientId,
            $recordDate,
            $cliEntity,
            $entityMembersDetails
        );

        self::tblFileHMLOBusinessEntityRef(
            $LMRId,
            $recordDate
        );

        $cnt = self::tblFileHMLOBackGround(
            $cnt,
            $LMRId,
            $saveTab,
            $newLMRId,
            $clientId,
            $recordDate
        );

        $cnt = self::tblFileHMLOExperience(
            $cnt,
            $LMRId,
            $saveTab,
            $clientInfo,
            $recordDate,
            $clientId
        );

        /**
         * Save Flipping & Ground Up Experience Information
         */
        if ($LMRId > 0) {
            $countOfPastDeals = glCustomJobForProcessingCompany::getCountOfPastDeals($PCID) ?? 0;

            self::tblFileExpFilpGroundUp_Flip(
                $userName,
                $UGroup,
                $LMRId,
                $recordDate,
                $countOfPastDeals
            );

            self::tblFileExpFilpGroundUp_Gup(
                $userName,
                $UGroup,
                $LMRId,
                $recordDate,
                $countOfPastDeals
            );

            self::tblFileExpFilpGroundUp_Sell(
                $userName,
                $UGroup,
                $LMRId,
                $recordDate,
                $countOfPastDeals
            );
        }

        self::tblFileHMLO(
            $cnt,
            $LMRId,
            $saveTab,
            $recordDate
        );


        $cnt = self::tblFileLoanOrigination(
            $cnt,
            $LMRId,
            $recordDate
        );

        if ($saveTab == 'LR') { /* if the file from lead post stop here - Mar 28, 2017 */
            return $cnt;
        }

        $cnt = self::tblFileHMLOPropInfo(
            $cnt,
            $LMRId,
            $recordDate
        );

        recordFileTabUpdate::getReport([
            'fileID' => $LMRId,
            'UID'    => $UID,
            'UGroup' => $UGroup,
            'opt'    => 'HMLO Summary',
            'PCID'   => $PCID
        ]);

        saveHMLOFilePropertyInfo::getReport($ip);

        if ($saveTab == 'HMLOSummary') {
            saveHMLOClientInfo::getReport($ip);
        }
        return $cnt;
    }

    public static function savePropertiesUsedForExperienceValidated($LMRID, $borNoOfFlippingExperience, $fullTimeRealEstateInvestor, $borrowerId): void
    {
        if (!$LMRID) {
            return;
        }
        $tblFileHMLOExperience = tblFileHMLOExperience::Get(['fileID' => $LMRID]) ?? new tblFileHMLOExperience();
        if (!$tblFileHMLOExperience->fileID) {
            $tblFileHMLOExperience->fileID = $LMRID;
            $tblFileHMLOExperience->CID = $borrowerId;
        }
        $tblFileHMLOExperience->borNoOfFlippingExperience = $borNoOfFlippingExperience !== '' ? intval($borNoOfFlippingExperience) : null;
        $tblFileHMLOExperience->fullTimeRealEstateInvestor = $fullTimeRealEstateInvestor;
        $tblFileHMLOExperience->Save();
    }
}
