<?php

namespace models\composite\oHMLOInfo;

use models\Database2;
use models\lendingwise\tblFile2;
use models\Request;
use models\standard\Dates;
use models\types\strongType;

/**
 *
 */
class saveHMLOFile2Info extends strongType
{
    /**
     * @param $ip
     * @return int
     */
    public static function getReport($ip): int
    {

        $LMRId = $ip['LMRId'] ?? null;
        $cnt = 0;
        $PCID = 0;
        $UGroup = '';
        $UID = 0;

        if (array_key_exists('UID', $ip)) $UID = $ip['UID'];
        if (array_key_exists('URole', $ip)) $UGroup = $ip['URole'];
        if (array_key_exists('PCID', $ip)) $PCID = $ip['PCID'];

        if ($LMRId == 0) {
            return $cnt;
        }

        $tblFile2 = tblFile2::Get(['LMRID' => $LMRId]);
        if (!$tblFile2) {
            $tblFile2 = new tblFile2();
            $tblFile2->LMRID = $LMRId;
        }

        $HMLOFileInfoFieldsArray = [
            'presentAddress',
            'presentCity',
            'presentState',
            'presentZip',
            'presentCounty',
            'borPresentPropType',
            'presentPropLengthTime',
            'previousPropLengthTime',
            'mailingAddrAsPresent',
            'coBPresentAddress',
            'coBPresentCity',
            'coBPresentState',
            'coBPresentZip',
            'coBorMailingAddrAsPresent',
            'coBPresentPropType',
            'presentPropLengthTimeCoBor',
        ];
        /** DIRECT Variables **/

        foreach ($HMLOFileInfoFieldsArray as $column) {
            if (isset ($_REQUEST[$column])) {
                $postVal = trim($_REQUEST[$column]);
                $tblFile2->$column = $postVal;
            }
        }
        if (Request::isset('address')) {
            $tblFile2->presentAddress = Request::GetClean('address');
        }
        if (Request::isset('driverLicenseIssuanceDate')) {
            $driverLicenseIssuanceDate = Request::GetClean('driverLicenseIssuanceDate');
            if (Dates::IsEmpty($driverLicenseIssuanceDate)) {
                $driverLicenseIssuanceDate = null;
            } else {
                $driverLicenseIssuanceDate = trim(Dates::formatDateWithRE($driverLicenseIssuanceDate, 'MDY', 'Y-m-d'));
            }
            $tblFile2->driverLicenseIssuanceDate = $driverLicenseIssuanceDate;
        }
        if (Request::isset('driverLicenseExpirationDate')) {
            $driverLicenseExpirationDate = Request::GetClean('driverLicenseExpirationDate');
            if (Dates::IsEmpty($driverLicenseExpirationDate)) {
                $driverLicenseExpirationDate = null;
            } else {
                $driverLicenseExpirationDate = trim(Dates::formatDateWithRE($driverLicenseExpirationDate, 'MDY', 'Y-m-d'));
            }
            $tblFile2->driverLicenseExpirationDate = $driverLicenseExpirationDate;
        }

        $tblFile2->Save();

        recordFileTabUpdate::getReport([
            'fileID' => $LMRId,
            'UID'    => $UID,
            'UGroup' => $UGroup,
            'opt'    => 'HMLO Summary',
            'PCID'   => $PCID,
        ]);

        return $cnt;
    }
}
