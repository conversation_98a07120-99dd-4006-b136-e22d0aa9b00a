<?php

namespace models\composite\oHMLOInfo;

use models\composite\oLoanOrigination\saveExplanationInfo;
use models\composite\oLoanOrigination\saveLoanOriginatorInfo;
use models\cypher;
use models\Database2;
use models\lendingwise\tblFileHMLOBackGround;
use models\lendingwise\tblFileHMLOListOfRepairs;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblFileHMLORehabInfo;
use models\lendingwise\tblIncomeInfo;
use models\lendingwise\tblPrincipalPayDown;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;


/**
 *
 */
class saveHMLOPropertyInfo extends strongType
{
    /**
     * @param $ip
     * @return array|int|mixed|string|null
     */
    public static function getReport($ip)
    {
        global $repairs, $HMLOListOfRepairsDescFieldsArray, $HMLOListOfRepairsAmtArray;

        $saveTab = '';
        $recordDate = Dates::Timestamp();
        $saveOpt = '';
        $userName = $userGroup = '';
        $principalPayDownAmt = [];
        $LMRId = $ip['LMRId'];
        $clientId = 0;
        if (array_key_exists('saveTab', $ip)) $saveTab = $ip['saveTab'];
        if (array_key_exists('saveOpt', $ip)) $saveOpt = $ip['saveOpt'];
        if (array_key_exists('clientId', $ip)) $clientId = $ip['clientId'];
        if (!$clientId && array_key_exists('encryptedCId', $ip['p'])) {
            $clientId = cypher::myDecryption($ip['p']['encryptedCId']);
        }
        $cnt = 0;

        $PCID = 0;
        $UGroup = '';
        $UID = 0;

        $principalPayDownDate = [];

        if (array_key_exists('UID', $ip)) $UID = $ip['UID'];
        if (array_key_exists('URole', $ip)) $UGroup = $ip['URole'];
        if (array_key_exists('PCID', $ip)) $PCID = $ip['PCID'];
        if (array_key_exists('userName', $ip)) $userName = $ip['userName'];
        if (array_key_exists('userGroup', $ip)) $userGroup = $ip['userGroup'];
        if (isset($ip['p']['principalPayDownAmt'])) $principalPayDownAmt = $ip['p']['principalPayDownAmt'];
        if (isset($ip['p']['principalPayDownDate'])) $principalPayDownDate = $ip['p']['principalPayDownDate'];
        if (isset($ip['p']['principalPayDownNote'])) $principalPayDownNote = $ip['p']['principalPayDownNote'];

        if (count($principalPayDownAmt) > 0) {
            $tblPrincipalPayDown = tblPrincipalPayDown::GetAll(['LMRID' => $LMRId]);

            if (count($tblPrincipalPayDown) > 0) {
                foreach ($tblPrincipalPayDown as $item) {
                    $item->Delete();
                }
            }

            for ($s = 0; $s < count($principalPayDownAmt); $s++) {
                $paydownnote = '';
                if (isset($principalPayDownNote[$s])) {
                    $paydownnote = $principalPayDownNote[$s];
                }
                if (Dates::IsEmpty($principalPayDownDate[$s])) {
                    $principalPayDownDate[$s] = '0000-00-00';
                    continue; // don't insert garbage 5/22/2022
                } else {
                    $principalPayDownDate[$s] = trim(Dates::formatDateWithRE($principalPayDownDate[$s], 'MDY', 'Y-m-d'));
                }

                if (!trim($principalPayDownAmt[$s])) {
                    continue; // don't insert garbage 5/22/2022
                }

                $tblPrincipalPayDown = new tblPrincipalPayDown();
                $tblPrincipalPayDown->LMRID = $LMRId;
                $tblPrincipalPayDown->principalPayDownAmount = Strings::Numeric($principalPayDownAmt[$s]);
                $tblPrincipalPayDown->principalPayDownDate = $principalPayDownDate[$s] ? Dates::Datestamp($principalPayDownDate[$s]) : '0000-00-00';
                $tblPrincipalPayDown->principalPayDownNote = $paydownnote;
                $tblPrincipalPayDown->Save();

                $cnt++;
            }
        }

        if ($LMRId == 0) {
            return $cnt;
        }

        $tblFileHMLOPropInfo = tblFileHMLOPropInfo::Get(['fileID' => $LMRId]) ?? new tblFileHMLOPropInfo();
        $tblFileHMLOPropInfo->fileID = $LMRId;

        if ($saveTab == 'LR') {
            $leadReceiveArray = [
                'annualPremium'     => 'annualPremium',
                'HMLOLoanType'      => 'typeOfHMLOLoanRequesting',
                'exitStrategy'      => 'exitStrategy',
                'loanTerm'          => 'loanTerm',
                'acceptedPurchase'  => 'acceptedPurchase',
                'propEquity'        => 'propEquity',
                'maxAmt'            => 'maxAmtToPutDown',
                'rateIndex'         => 'rateIndex',
                'spread'            => 'spread',
                'isBlanketLoan'     => 'isBlanketLoan',
                'propertyNeedRehab' => 'propertyNeedRehab',
                'isThisGroundUpConstruction' => 'isThisGroundUpConstruction',
            ];

            $HMLOPropInfoReplaceCommaField = ['maxAmt',
                'spread',
            ];

            foreach ($leadReceiveArray as $val => $column) {
                if (isset ($_REQUEST[$val])) {
                    if ($column == 'rateIndex') {
                        $postVal = implode(',', $_REQUEST['rateIndex']);
                    } else if (in_array($column, $HMLOPropInfoReplaceCommaField)) {
                        $postVal = Strings::replaceCommaValues($_REQUEST[$val]);
                    } else {
                        $postVal = $_REQUEST[$val];
                    }

                    $tblFileHMLOPropInfo->{$column} = $postVal;
                }
            }
        } else {
            $HMLOPropertyInfoFieldsArray = [
                'typeOfHMLOLoanRequesting',
                'acceptedPurchase',
                'requiredLoanAmount',
                'exitStrategy',
                'loanGuaranteeType',
                'propertyNeedRehab',
                'isThisGroundUpConstruction',
                'rehabRepairDetails',
                'neededToCompleteRehab',
                'rehabCompanyName',
                'rehabContractorName',
                'rehabGCLicense',
                'rehabContractorEmail',
                'rehabStrategyPlans',
                'annualPremium',
                'loanTerm',
                'prePaymentPenalty',
                'lienPosition',
                'isHiredPerformRehab',
                'GCCompanyName',
                'GCFirstName',
                'GCLastName',
                'GCLicense',
                'approvedLoanAmt',
                'propIncNotes',
                'propEquity',
                'maxAmtToPutDown',
                'HMLOEstateHeldIn',
                'paymentReserves',
                'includeTaxesInsuranceHOA',
                'requiredConstruction',
                'contingencyReserve',
                'isBlanketLoan',
                'serviceLenderType',
                'typeOfRecordingJurisdiction',
                'nameOfRecordingJurisdiction',
                'typeOfSale',
                'latePayemntAppliedOn',
                'lateChargeAmt',
                'proInsPolicyNo',
                'propDetailsProcess',
                'assetCollateralized',
                'lenderNotes',
                'checkDisplayTermSheet',
                'involvedPurchase',
                'prePayExcessOf20percent',
                'loanMadeWholly',
                'limitedOrNot',
                'balloonPayment',
                'wholesaleFee',
                'seekingCashRefinance',
                'seekingCash',
                'seekingFund',
                'rentalIncomePerMonth',
                'minLiabilityCoverage',
                'serviceLenderStateOfFormation',
                'serviceLenderEntityType',
                'isTherePrePaymentPenalty',
                'defaultInterestRate',
                'madePursuantToCourtOrder',
                'loanSigning',
                'courtOrderNecessary',
                'loanPurpose',
                'lotStatus',
                'lotPurchasePrice',
                'currentLotMarket',
                'isAdditionalGuarantors',
                'expectForDueDiligence',
                'referringParty',
                'hereAbout',
                'paymentDue',
                'billing',
                'lenderLossPayableEndorsementInfo',
                'triggeredByDays',
                'minLateFeeAmt',
                'servicingNumber',
                'servicingSubStatus',
                'paymentBased',
                'accrualType',
                'taxInsuranceEscrowsIncluded',
                'requiredConstructionReservesFinanced',
                'proInsCarrier',
                'docType',
                'rateIndex',
                'spread',
                'famBizAffil',
                'applyOtherLoan',
                'applyNewCredit',
                'borrowingMoney',
                'borrowedAmt',
                'haveOwnershipInterest',
                'typePropOwned',
                'titleType',
                'mersNumber',
                'expectedTimelineToExit',
                'percentageTotalLoan',
                'successFeePayment',
                'extensionFee',
                'applicationLoanExitPlan',
                'accountExecutiveLoanExitNotes',
            ];
            /** DIRECT Variables **/
            $HMLOPropInfoReplaceCommaField = [
                'approvedLoanAmt',
                'annualPremium',
                'maxAmtToPutDown',
                'lateChargeAmt',
                'wholesaleFee',
                'rentalIncomePerMonth',
                'minLiabilityCoverage',
                'minLateFeeAmt',
                'spread',
                'borrowedAmt',
                'lotPurchasePrice',
                'currentLotMarket',
                'extensionFee',
            ];

            foreach ($HMLOPropertyInfoFieldsArray as $field) {

                if (isset ($_REQUEST[$field])) {
                    if ($field == 'propertyNeedRehab') {
                        if ($_REQUEST[$field] == 'No') {
                            $tblFileHMLOPropInfo->isThisGroundUpConstruction = '';
                            $tblFileHMLOPropInfo->propertyNeedRehab = $_REQUEST[$field];
                            continue;
                        }
                    }

                    if (in_array($field, $HMLOPropInfoReplaceCommaField)) {
                        if ($field == 'rateIndex') {
                            $postVal = implode(',', $_REQUEST['rateIndex']);

                        } else {
                            $postVal = trim(Strings::replaceCommaValues($_REQUEST["$field"]));
                        }
                    } elseif ($field == 'rateIndex') {
                        $postVal = implode(',', $_REQUEST['rateIndex']);
                    } else {
                        $postVal = trim($_REQUEST["$field"]);
                    }

                    if ($field == 'expectForDueDiligence') {
                        $postVal = urlencode(Strings::stripQuote($postVal));
                    }
                    if ($field == 'lenderNotes') {
                        $postVal = Strings::stripQuote($postVal);
                    }

                    if (in_array($field, [
                        'paymentReserves',
                        'includeTaxesInsuranceHOA',
                        'requiredConstruction',
                        'contingencyReserve',
                        'paymentDue',
                    ])) { // need proper numerical value for these columns
                        if ($postVal === '') {
                            $postVal = 0;
                        }
                    }

                    $tblFileHMLOPropInfo->safeSet($field, $postVal);

                } elseif ($field == 'isBlanketLoan') {
                    if (isset($_REQUEST['isBlanketLoanMirror'])) {
                        $postVal = trim($_REQUEST['isBlanketLoanMirror']);

                        $tblFileHMLOPropInfo->$field = $postVal;
                    }

                } elseif ($field == 'rateIndex') {
                    if (!isset($_REQUEST['rateIndex'])) {
                        $tblFileHMLOPropInfo->$field = '';
                    }
                } elseif ($field == 'includeTaxesInsuranceHOA') {
                    if (!Request::isset('includeTaxesInsuranceHOA')) {
                        $tblFileHMLOPropInfo->includeTaxesInsuranceHOA = 0;
                    }
                } elseif ($field == 'loanGuaranteeType') {
                    if (Request::isset('loanGuarantee')) {
                        $tblFileHMLOPropInfo->loanGuaranteeType = Request::GetClean('loanGuarantee');
                    }
                }
            }
        }

        /** Date Field value **/

        /** Phone Number Field value **/

        if (isset($_REQUEST['rehabContractorPh1'])
            || isset($_REQUEST['rehabContractorPh2'])
            || isset($_REQUEST['rehabContractorPh3'])
            || isset($_REQUEST['rehabContractorPhExt'])
        ) {
            $item = tblFileHMLORehabInfo::Get(['fileID' => $LMRId]);
            if (!$item) {
                $item = new tblFileHMLORehabInfo();
                $item->fileID = $LMRId;
            }
            $item->rehabContractorPhone = trim($_REQUEST['rehabContractorPh1'] . $_REQUEST['rehabContractorPh2'] . $_REQUEST['rehabContractorPh3'] . $_REQUEST['rehabContractorPhExt']);
            $item->Save();
        }

        if (isset($_REQUEST['dateNoteSigned'])) {
            $dateNoteSigned = trim($_REQUEST['dateNoteSigned']);
            if (Dates::IsEmpty($dateNoteSigned)) {
                $dateNoteSigned = '0000-00-00';
            } else {
                $dateNoteSigned = trim(Dates::formatDateWithRE($dateNoteSigned, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->dateNoteSigned = $dateNoteSigned;
        }

        if (isset($_REQUEST['maturityDate'])) {
            $maturityDate = trim($_REQUEST['maturityDate']);
            if (Dates::IsEmpty($maturityDate)) {
                $maturityDate = '0000-00-00';
            } else {
                $maturityDate = trim(Dates::formatDateWithRE($maturityDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->maturityDate = $maturityDate;
        }

        if (isset($_REQUEST['loanSaleDate'])) {
            $loanSaleDate = trim($_REQUEST['loanSaleDate']);
            if (Dates::IsEmpty($loanSaleDate)) {
                $loanSaleDate = '0000-00-00';
            } else {
                $loanSaleDate = trim(Dates::formatDateWithRE($loanSaleDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->loanSaleDate = $loanSaleDate;
        }

        if (isset($_REQUEST['masterLoanSaleDate'])) {
            $masterLoanSaleDate = trim($_REQUEST['masterLoanSaleDate']);
            if (Dates::IsEmpty($masterLoanSaleDate)) {
                $masterLoanSaleDate = '0000-00-00';
            } else {
                $masterLoanSaleDate = trim(Dates::formatDateWithRE($masterLoanSaleDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->masterLoanSaleDate = $masterLoanSaleDate;
        }

        if (isset($_REQUEST['recordingDate'])) {
            $recordingDate = trim($_REQUEST['recordingDate']);
            if (Dates::IsEmpty($recordingDate)) {
                $recordingDate = '0000-00-00';
            } else {
                $recordingDate = trim(Dates::formatDateWithRE($recordingDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->recordingDate = $recordingDate;
        }

        if (isset($_REQUEST['payOffDate'])) {
            $payOffDate = trim($_REQUEST['payOffDate']);
            if (Dates::IsEmpty($payOffDate)) {
                $payOffDate = '0000-00-00';
            } else {
                $payOffDate = trim(Dates::formatDateWithRE($payOffDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->payOffDate = $payOffDate;
        }

        if (isset($_REQUEST['extensionDate'])) {
            $extensionDate = trim($_REQUEST['extensionDate']);
            if (Dates::IsEmpty($extensionDate)) {
                $extensionDate = '0000-00-00';
            } else {
                $extensionDate = trim(Dates::formatDateWithRE($extensionDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->extensionDate = $extensionDate;
        }

        if (isset($_REQUEST['proInsPolicyExpDate'])) {
            $proInsPolicyExpDate = trim($_REQUEST['proInsPolicyExpDate']);
            if (Dates::IsEmpty($proInsPolicyExpDate)) {
                $proInsPolicyExpDate = null;
            } else {
                $proInsPolicyExpDate = trim(Dates::formatDateWithRE($proInsPolicyExpDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->proInsPolicyExpDate = $proInsPolicyExpDate;
        }

        if (isset($_REQUEST['proInsPolicyEffDate'])) {
            $proInsPolicyEffDate = trim($_REQUEST['proInsPolicyEffDate']);
            if (Dates::IsEmpty($proInsPolicyEffDate)) {
                $proInsPolicyEffDate = null;
            } else {
                $proInsPolicyEffDate = trim(Dates::formatDateWithRE($proInsPolicyEffDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->proInsPolicyEffDate = $proInsPolicyEffDate;
        }

        if (isset($_REQUEST['riders'])) {
            $riders = $_REQUEST['riders'];
            if (count($riders) > 0) {
                $riders = implode(',', $riders);
                $tblFileHMLOPropInfo->riders = $riders;
            }
        }

        if (isset($_REQUEST['propertyTaxDueDate'])) {
            $propertyTaxDueDate = trim($_REQUEST['propertyTaxDueDate']);
            if (Dates::IsEmpty($propertyTaxDueDate)) {
                $propertyTaxDueDate = '0000-00-00';
            } else {
                $propertyTaxDueDate = trim(Dates::formatDateWithRE($propertyTaxDueDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->propertyTaxDueDate = $propertyTaxDueDate;
        }

        if (isset($_REQUEST['foreclosureDate'])) {
            $foreclosureDate = trim($_REQUEST['foreclosureDate']);
            if (Dates::IsEmpty($foreclosureDate)) {
                $foreclosureDate = null;
            } else {
                $foreclosureDate = trim(Dates::formatDateWithRE($foreclosureDate, 'MDY', 'Y-m-d'));
            }

            $tblFileHMLOPropInfo->foreclosureDate = $foreclosureDate;
        }

        if ($saveTab == 'HMLOPI' || $saveTab == 'HMLOSummary' || $saveTab == 'HMLO') {
            if (isset($_REQUEST['proInsType']) && isset($_REQUEST['proInsType_CheckField'])) {
                $insTypesIds = implode(',', $_REQUEST['proInsType']);
                $tblFileHMLOPropInfo->proInsType = $insTypesIds;
            } else if (isset($_REQUEST['proInsType_CheckField'])) {
                $tblFileHMLOPropInfo->proInsType = '';
            }


            if (isset($_REQUEST['reqValuationMethod']) && isset($_REQUEST['reqValuationMethod_CheckField'])) {
                $reqValuationMethods = implode(',', $_REQUEST['reqValuationMethod']);
                $tblFileHMLOPropInfo->reqValuationMethod = $reqValuationMethods;
            } elseif (isset($_REQUEST['reqValuationMethod_CheckField'])) {
                $tblFileHMLOPropInfo->reqValuationMethod = '';
            }
        }

        if (isset($_REQUEST['amountPastDueOrOwed'])) {
            $amountPastDueOrOwed = Strings::Numeric($_REQUEST['amountPastDueOrOwed']);
            $tblFileHMLOPropInfo->amountPastDueOrOwed = $amountPastDueOrOwed;
        }

        if (isset($_REQUEST['noteSalePrice'])) {
            $tblFileHMLOPropInfo->noteSalePrice = trim(Strings::replaceCommaValues($_REQUEST['noteSalePrice']));
        }

        if (isset($_REQUEST['BPieceAmount'])) {
            $tblFileHMLOPropInfo->BPieceAmount = trim(Strings::replaceCommaValues($_REQUEST['BPieceAmount']));
        }

        if (isset($_REQUEST['securityInstrument'])) {
            $securityInstrument = $_REQUEST['securityInstrument'];
            $tblFileHMLOPropInfo->securityInstrument = trim($securityInstrument);
        }


        if (isset($_REQUEST['useOfFunds'])) {
            $useOfFunds = $_REQUEST['useOfFunds'];
            $tblFileHMLOPropInfo->useOfFunds = trim($useOfFunds);
        } elseif (isset($_REQUEST['useOfFundsLT'])) {
            $useOfFunds = $_REQUEST['useOfFundsLT'];
            $tblFileHMLOPropInfo->useOfFunds = trim($useOfFunds);
        }

        if (isset($_REQUEST['desiredFundingAmount'])) {
            $desiredFundingAmount = Strings::replaceCommaValues($_REQUEST['desiredFundingAmount']);
            $tblFileHMLOPropInfo->desiredFundingAmount = trim($desiredFundingAmount);
        }

        if (isset($_REQUEST['haveCurrentLoanBal'])) {
            $haveCurrentLoanBal = $_REQUEST['haveCurrentLoanBal'];
            $tblFileHMLOPropInfo->haveCurrentLoanBal = trim($haveCurrentLoanBal);
        }

        if (isset($_REQUEST['balance'])) {
            $balance = Strings::replaceCommaValues($_REQUEST['balance']);
            $tblFileHMLOPropInfo->balance = trim($balance);
        }

        if (isset($_REQUEST['heldWith'])) {
            $heldWith = $_REQUEST['heldWith'];
            $tblFileHMLOPropInfo->heldWith = trim($heldWith);
        }

        if (isset($_REQUEST['doYouHaveInvoiceToFactor'])) {
            $doYouHaveInvoiceToFactor = $_REQUEST['doYouHaveInvoiceToFactor'];
            $tblFileHMLOPropInfo->haveInvoiceToFactor = trim($doYouHaveInvoiceToFactor);
        }

        if (isset($_REQUEST['amount'])) {
            $invoiceFactorAmount = Strings::replaceCommaValues($_REQUEST['amount']);
            $tblFileHMLOPropInfo->amount = trim($invoiceFactorAmount);
        }

        if (isset($_REQUEST['displayNotes'])) {
            $displayNotes = $_REQUEST['displayNotes'];
            $tblFileHMLOPropInfo->displayNotes = trim($displayNotes);
        }

        //purposeOfLoan
        if (isset($_REQUEST['purposeOfLoan']) && $_REQUEST['purposeOfLoan']) {
            $purposeOfLoan = is_array($_REQUEST['purposeOfLoan']) ? implode('~', $_REQUEST['purposeOfLoan']) : $_REQUEST['purposeOfLoan'];
            $tblFileHMLOPropInfo->purposeOfLoan = trim($purposeOfLoan);
        } elseif (isset($_REQUEST['purposeOfLoanHidden'])) {
            $purposeOfLoan = $_REQUEST['purposeOfLoanHidden'];
            $tblFileHMLOPropInfo->purposeOfLoan = trim($purposeOfLoan);
        }

        if (isset($_REQUEST['fundingDate'])) {
            $fundingDate = trim($_REQUEST['fundingDate']);
            if (Dates::IsEmpty($fundingDate)) {
                $fundingDate = '0000-00-00';
            } else {
                $fundingDate = trim(Dates::formatDateWithRE($fundingDate, 'MDY', 'Y-m-d'));
            }
            $tblFileHMLOPropInfo->fundingDate = trim($fundingDate);
        }

        if (isset($_REQUEST['rehabToBeMade'])) {
            $rehabToBeMade = $_REQUEST['rehabToBeMade'];
            $tblFileHMLOPropInfo->rehabToBeMade = trim($rehabToBeMade);
        }

        if (isset($_REQUEST['rehabTime'])) {
            $rehabTime = $_REQUEST['rehabTime'];
            $tblFileHMLOPropInfo->safeSet('rehabTime', $rehabTime);
        }

        if (isset($_REQUEST['isSubjectUnderConst'])) {
            $isSubjectUnderConst = $_REQUEST['isSubjectUnderConst'];
            $tblFileHMLOPropInfo->isSubjectUnderConst = trim($isSubjectUnderConst);
        }

        if (isset($_REQUEST['areKnownHazards'])) {
            $areKnownHazards = $_REQUEST['areKnownHazards'];
            $tblFileHMLOPropInfo->areKnownHazards = trim($areKnownHazards);
        }

        if (isset($_REQUEST['areProReports'])) {
            $areProReports = $_REQUEST['areProReports'];
            $tblFileHMLOPropInfo->areProReports = trim($areProReports);
        }

        if (isset($_REQUEST['isSubjectSS'])) {
            $isSubjectSS = $_REQUEST['isSubjectSS'];
            $tblFileHMLOPropInfo->isSubjectSS = trim($isSubjectSS);
        }

        if (isset($_REQUEST['useOfProceeds'])) {
            $useOfProceeds = $_REQUEST['useOfProceeds'];
            $tblFileHMLOPropInfo->useOfProceeds = trim($useOfProceeds);
        }

        if (isset($_REQUEST['changeInCircumstance'])) {
            $changeInCircumstance = $_REQUEST['changeInCircumstance'];
            $tblFileHMLOPropInfo->changeInCircumstance = trim($changeInCircumstance);
        }

        if (isset($_REQUEST['changeDescription'])) {
            $changeInCircumstance = $_REQUEST['changeDescription'];
            $tblFileHMLOPropInfo->changeDescription = trim($changeInCircumstance);
        }

        if (isset($_REQUEST['purposeOfLoanExplanation'])) {
            $tblFileHMLOPropInfo->purposeOfLoanExplanation = trim($_REQUEST['purposeOfLoanExplanation']);
        }

        //to avoid conflict in names of field annualPremium
        if (isset($_REQUEST['annualPremiumInsInfo'])) {
            $tblFileHMLOPropInfo->annualPremium = Strings::replaceCommaValues($_REQUEST['annualPremiumInsInfo']);
        }

        $tblFileHMLOPropInfo->userName = Strings::processString($userName);
        $tblFileHMLOPropInfo->userType = $userGroup;

        if (isset($params['propertyTaxDueDate'])) {
            $tblFileHMLOPropInfo->propertyTaxDueDate = $tblFileHMLOPropInfo->propertyTaxDueDate ? Dates::Datestamp($tblFileHMLOPropInfo->propertyTaxDueDate) : null;
        }

        if (isset($params['latePayemntAppliedOn'])) {
            $tblFileHMLOPropInfo->latePayemntAppliedOn = intval($tblFileHMLOPropInfo->latePayemntAppliedOn);
        }
        if (isset($params['paymentDue'])) {
            $tblFileHMLOPropInfo->paymentDue = intval($tblFileHMLOPropInfo->paymentDue);
        }
        if (isset($params['triggeredByDays'])) {
            $tblFileHMLOPropInfo->triggeredByDays = intval($tblFileHMLOPropInfo->triggeredByDays);
        }

        $tblFileHMLOPropInfo->Save();

        $tblFileHMLOBackGround = tblFileHMLOBackGround::Get(['fileID' => $LMRId]);
        if (!$tblFileHMLOBackGround) {
            $tblFileHMLOBackGround = new tblFileHMLOBackGround();
            $tblFileHMLOBackGround->fileID = $LMRId;
            $tblFileHMLOBackGround->CID = $clientId;
        }


        $HMLOBGFieldsArray = [
            'isBorBorrowedDownPayment',
            'borBorrowedDownPaymentExpln',
            'isBorIntendToOccupyPropAsPRI',
            'isCoBorIntendToOccupyPropAsPRI',
            'isBorPersonallyGuaranteeLoan',
        ];
        /** DIRECT Variables **/

        foreach ($HMLOBGFieldsArray as $f => $column) {
            if (isset ($_REQUEST[$column])) {
                $postVal = trim($_REQUEST[$column]);
                $tblFileHMLOBackGround->$column = $postVal;
            } elseif (isset ($clientInfo[$column])) {
                $postVal = trim($clientInfo[$column]);
                $tblFileHMLOBackGround->$column = $postVal;
            }
        }

        $tblFileHMLOBackGround->Save();
        $cnt = $tblFileHMLOBackGround->HMLOBGID ? 1 : 0;

        //        ****************************************************

        /* Start SBA Questions */
        sbaQASave::getReport([
            'clientId'   => $clientId,
            'LMRId'      => $LMRId,
            'recordDate' => $recordDate,
        ]); // refactored
        /* End of SBA Questions */


        if (isset($_REQUEST['purchaseCloseDate']) || isset($_REQUEST['closingDate']) || isset($_REQUEST['hearingDate'])) { /* Merge closing Date in Admin tab with Purchase / Target Closing Date in Loan info tab on Jul 26, 2017 - Ticket Id: 148701649*/
            saveHMLOQAInfo::getReport($ip); // refactored
        }

        if (isset($_REQUEST['trialPaymentDate1']) || isset($_REQUEST['trialPaymentDay1'])) { /* Save the First Loan Payment Due on Jan 31, 2018 - Pivotal Id: #154635903*/
            saveHMLOFileResponseInfo::getReport($ip); // refactored
        }

        if ($saveTab == 'Admin') {
            return $cnt;
        } // Break and Return Other Process for File Admin Tab. (Pivotal - #154536669)

        if ($saveTab == 'HMLI') {
            /** Saved the "Loan Info" Tab, Saved the Exit Strategy Explanation notes field for "Loan Terms" section on May 10, 2017 **/
            saveExplanationInfo::getReport($ip); // refactored
            saveLoanOriginatorInfo::getReport($ip); // refactored
            if ($saveOpt != 'HMLOSummary') {
                saveHMLOClientInfo::getReport($ip); // refactored
            }
            saveHMLOInfo::getReport($ip); // refactored
            saveHMLOAppraiserAndRealtorInfo::getReport($ip); // refactored
            saveHMLONewLoanInfo::getReport($ip); // refactored
            saveHMLOFilePropertyInfo::getReport($ip); // refactored
            saveFileNewInfo::getReport($ip); // refactored
        }
        if ($saveTab == 'HMLI' || $saveTab == 'HMLOSummary') {
            saveEscrowIncomeInfo::getReport($ip); // refactored
        }

        if ($saveTab == 'HMLOPI') { /* Property Info New Field Save - July 24, 2017 */
            saveHMLONewLoanInfo::getReport($ip); // refactored
        }
        if ($saveTab == 'LR' || $saveTab == 'HMLI') { /* if the file from lead post stop here - Mar 28, 2017 */
            if ($saveTab == 'HMLI') {
                $UID = $ip['p']['createdBy'];
                $UGroup = $ip['p']['createdUserType'];
                $PCID = $ip['p']['PCID'];

                recordFileTabUpdate::getReport([
                    'fileID' => $LMRId,
                    'UID'    => $UID,
                    'UGroup' => $UGroup,
                    'opt'    => 'HMLO Summary',
                    'PCID'   => $PCID,
                ]);
            }
            return $cnt;
        }
        /** Added the Initial List of Repairs section save Functionality On Jan 31, 2017 **/


        $qu = '';
        $qs = '';
        $qc = '';
        $qv = '';

        $tblFileHMLOListOfRepairs = tblFileHMLOListOfRepairs::Get(['fileID' => $LMRId]);

        if (!$tblFileHMLOListOfRepairs) {
            $tblFileHMLOListOfRepairs = new tblFileHMLOListOfRepairs();
            $tblFileHMLOListOfRepairs->fileID = $LMRId;
            $tblFileHMLOListOfRepairs->recordDate = $recordDate;
        }

        /* Get Reb Construction data */
        $k1 = array_keys($repairs);
        for ($s = 0; $s < count($k1); $s++) {
            $s1 = $k1[$s];
            $fs = $repairs[$s1];
            $keys = array_keys($fs);

            foreach ($keys as $x => $rk) {
                $fld = $fs[$rk];
                if (isset ($_REQUEST[$fld])) {
                    $postVal = trim(Strings::replaceCommaValues($_REQUEST[$fld]));
                    $tblFileHMLOListOfRepairs->$fld = $postVal;
                }
            }
        }

        foreach ($HMLOListOfRepairsDescFieldsArray as $fld) {
            if (isset ($_REQUEST[$fld])) {
                $postVal = trim($_REQUEST[$fld]);
                $tblFileHMLOListOfRepairs->$fld = $postVal;
            }
        }

        foreach ($HMLOListOfRepairsAmtArray as $x => $fld) {
            if (isset ($_REQUEST[$fld])) {
                $postVal = trim(Strings::replaceCommaValues($_REQUEST[$fld]));
                $tblFileHMLOListOfRepairs->$fld = $postVal;
            }
        }


        $cnt = 1;

        if ($saveTab == 'HMLOPI') {
            /** Saved the "Loan & Property Info" Tab, Saved the Exit Strategy Explanation notes field for "Loan Info" section on Feb 01, 2017 **/


            saveExplanationInfo::getReport($ip); // refactored
            saveHMLOInfo::getReport($ip); // refactored
        }

        saveAdditionalInsuranceInfo::getReport($ip); // refactored

        /** Save the multi insurance details**/
        recordFileTabUpdate::getReport([
            'fileID' => $LMRId,
            'UID'    => $UID,
            'UGroup' => $UGroup,
            'opt'    => 'HMLO Summary',
            'PCID'   => $PCID,
        ]);

        return $cnt;
    }

    public static function saveFileHMLOPropInfoCashFlowWebform(?int $LMRId): ?array
    {
        if (!$LMRId) return [];

        if (self::checkRecord($LMRId)) {
            $qry = ' 
                    UPDATE tblFileHMLOPropInfo 
                    SET annualPremium = :annualPremium
                    WHERE fileID = :LMRId ;
';
            return Database2::getInstance()->executeQuery($qry, [
                'annualPremium' => Strings::replaceCommaValues($_REQUEST['spcf_annualPremium']),
                'LMRId'         => $LMRId,
            ]);
        } else {
            $qry = ' 
                    insert into tblFileHMLOPropInfo( fileID, annualPremium )  
                    values(  :fileID , :annualPremium ); ';

            return Database2::getInstance()->executeQuery($qry, [
                'annualPremium' => Strings::replaceCommaValues($_REQUEST['spcf_annualPremium']),
                'LMRId'         => $LMRId,
            ]);
        }
    }

    public static function checkRecord(int $LMRId)
    {
        $q = 'SELECT HMLOPID FROM tblFileHMLOPropInfo WHERE fileID = :fileID ';
        $sqlParams = [
            'fileID' => $LMRId,
        ];
        return Database2::getInstance()->queryData($q, $sqlParams, null, true);
    }

}
