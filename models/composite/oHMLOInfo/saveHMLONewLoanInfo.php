<?php

namespace models\composite\oHMLOInfo;

use models\Database2;
use models\composite\oFileUpdate\saveFileNotes;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\db\tblRefinanceMortgage_db;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblRefinanceMortgage;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;


/**
 *
 */
class saveHMLONewLoanInfo extends strongType
{
    /**
     * @param $ip
     * @return int|null
     */
    public static function getReport($ip): ?int
    {

        $LMRId = $ip['LMRId'];
        $cnt = 0;
        if (!$LMRId) {
            return $cnt;
        }

        $recordDate = Dates::Timestamp();
        $UGroup = $userName = '';
        $UID = 0;


        if (array_key_exists('userNumber', $_SESSION)) $UID = $_SESSION['userNumber'];
        if (array_key_exists('userGroup', $_SESSION)) $UGroup = $_SESSION['userGroup'];
        if (array_key_exists('firstName', $_SESSION)) $userName = $_SESSION['firstName'] . ' ' . $_SESSION['lastName'];

        $savingTab = $ip['p']['activeTab'];

        $oldOriginationPointsRate = $ip['p']['oldOriginationPointsRate'];
        $oldBrokerPointsRate = $ip['p']['oldBrokerPointsRate'];

        $originationPointsRate = $ip['p']['originationPointsRate'];
        $brokerPointsRate = $ip['p']['brokerPointsRate'];

        $tblFileHMLONewLoanInfo = tblFileHMLONewLoanInfo::Get(['fileID' => $LMRId]);

        if (!$tblFileHMLONewLoanInfo) {
            $tblFileHMLONewLoanInfo = new tblFileHMLONewLoanInfo();
            $tblFileHMLONewLoanInfo->fileID = $LMRId;
            $tblFileHMLONewLoanInfo->recordDate = $recordDate;
        }


        $HMLOFileNewLoanInfoFieldsArray = [
            'isTaxesInsEscrowed'                   => null,
            'assumability'                         => null,
            'rehabCostFinanced'                    => Database2::DATATYPE_MONEY,
            'processingFee'                        => Database2::DATATYPE_MONEY,
            'appraisalFee'                         => Database2::DATATYPE_MONEY,
            'applicationFee'                       => Database2::DATATYPE_MONEY,
            'drawsSetUpFee'                        => Database2::DATATYPE_MONEY,
            'miscellaneousFee'                     => Database2::DATATYPE_MONEY,
            'closingCostFinanced'                  => Database2::DATATYPE_MONEY,
            'extensionOption'                      => null,
            'HMLOLender'                           => null,
            'interestReserves'                     => null,
            'percentageOfBudget'                   => null,
            'drawsFee'                             => Database2::DATATYPE_MONEY,
            'originalPurchasePrice'                => Database2::DATATYPE_MONEY,
            'costOfImprovementsMade'               => Database2::DATATYPE_MONEY,
            'payOffMortgage1'                      => Database2::DATATYPE_MONEY,
            'payOffMortgage2'                      => Database2::DATATYPE_MONEY,
            'payOffOutstandingTaxes'               => Database2::DATATYPE_MONEY,
            'payOffOtherOutstandingAmounts'        => Database2::DATATYPE_MONEY,
            'CashOut'                              => null,
            'additionalPropertyRestrictions'       => null,
            'refinanceCurrentLender'               => null,
            'refinanceCurrentRate'                 => null,
            'refinanceMonthlyPayment'              => Database2::DATATYPE_MONEY,
            'restrictionsExplain'                  => null,
            'actualRentsInPlace'                   => Database2::DATATYPE_MONEY,
            'spcf_hoafees'                         => Database2::DATATYPE_MONEY,
            'lessActualExpenses'                   => Database2::DATATYPE_MONEY,
            'grossAnnualRentLargestTenant'         => Database2::DATATYPE_MONEY,
            'prePaymentPenaltyPercentage'          => Database2::DATATYPE_MONEY,
            'extensionOptionPercentage'            => Database2::DATATYPE_MONEY,
            'amortizationType'                     => null,
            'noUnitsOccupied'                      => Database2::DATATYPE_NUMBER,
//            'noOfParcels' => Database2::DATATYPE_NUMBER,
//            'pricePerDoor' => Database2::DATATYPE_MONEY,
//            'noOfBuildings' => Database2::DATATYPE_NUMBER,
//            'ownerOccupancyPercentage' => Database2::DATATYPE_MONEY,
// TODO:  THESE COLUMNS NEED DELETION - WE NEED TO REVIEW SAI'S CODE TO FIND ALL TO DROP
            'noOfPropertiesAcquiring'              => Database2::DATATYPE_NUMBER,
            'cashOutAmt'                           => Database2::DATATYPE_MONEY,
            'resalePrice'                          => Database2::DATATYPE_MONEY,
            'initialAdvance'                       => Database2::DATATYPE_MONEY,
            'secondaryHolderName'                  => null,
            'secondaryFinancingAmount'             => Database2::DATATYPE_MONEY,
            'valuationBPOFee'                      => Database2::DATATYPE_MONEY,
            'valuationCMAFee'                      => Database2::DATATYPE_MONEY,
            'valuationAVEFee'                      => Database2::DATATYPE_MONEY,
            'valuationAVMFee'                      => Database2::DATATYPE_MONEY,
            'creditReportFee'                      => Database2::DATATYPE_MONEY,
            'creditCheckFee'                       => Database2::DATATYPE_MONEY,
            'employmentVerificationFee'            => Database2::DATATYPE_MONEY,
            'backgroundCheckFee'                   => Database2::DATATYPE_MONEY,
            'taxReturnOrderFee'                    => Database2::DATATYPE_MONEY,
            'floodCertificateFee'                  => Database2::DATATYPE_MONEY,
            'loanOriginationFee'                   => Database2::DATATYPE_MONEY,
            'documentPreparationFee'               => Database2::DATATYPE_MONEY,
            'wireFee'                              => Database2::DATATYPE_MONEY,
            'servicingSetUpFee'                    => Database2::DATATYPE_MONEY,
            'taxServiceFee'                        => Database2::DATATYPE_MONEY,
            'floodServiceFee'                      => Database2::DATATYPE_MONEY,
            'constructionHoldbackFee'              => Database2::DATATYPE_MONEY,
            'sellerCreditsFee'                     => Database2::DATATYPE_MONEY,
            'thirdPartyFees'                       => Database2::DATATYPE_MONEY,
            'otherFee'                             => Database2::DATATYPE_MONEY,
            'taxImpoundsMonth'                     => Database2::DATATYPE_NUMBER,
            'taxImpoundsMonthAmt'                  => Database2::DATATYPE_MONEY,
            'taxImpoundsFee'                       => Database2::DATATYPE_MONEY,
            'insImpoundsMonth'                     => Database2::DATATYPE_NUMBER,
            'insImpoundsMonthAmt'                  => Database2::DATATYPE_MONEY,
            'insImpoundsFee'                       => Database2::DATATYPE_MONEY,
            'costOfImprovementsToBeMade'           => Database2::DATATYPE_MONEY,
//            'LOCTotalLoanAmt' => Database2::DATATYPE_MONEY,   //TODO: NOT SAVED HERE?
            'rehabCostPercentageFinanced'          => Database2::DATATYPE_MONEY,
            'downPaymentPercentage'                => Database2::DATATYPE_MONEY,
            'CORTotalLoanAmt'                      => Database2::DATATYPE_MONEY,
            'CORefiLTVPercentage'                  => null,
            'yieldSpread'                          => Database2::DATATYPE_MONEY,
            'costOfCapital'                        => Database2::DATATYPE_MONEY,
            'includeCCF'                           => Database2::DATATYPE_NUMBER,
//            'stabilizedRate' => Database2::DATATYPE_MONEY,   //TODO: NOT SAVED HERE?
            'landValue'                            => Database2::DATATYPE_MONEY,
            'aggregateDSCR'                        => Database2::DATATYPE_MONEY,
            'isOwnLand'                            => null,
            'isLoanPaymentAmt'                     => null,
            'ownedFreeAndClear'                    => null,
            'ownedSameEntity'                      => null,
            'totalLoanAmount'                      => Database2::DATATYPE_MONEY,
            'finalLoanAmt'                         => Database2::DATATYPE_MONEY,
            'inspectionFees'                       => Database2::DATATYPE_MONEY,
            'projectFeasibility'                   => Database2::DATATYPE_MONEY,
            'dueDiligence'                         => Database2::DATATYPE_MONEY,
            'UccLienSearch'                        => Database2::DATATYPE_MONEY,
            'isFeeUpdated'                         => null,
            'haveBorSquareFootage'                 => null,
            'borNoOfSquareFeet'                    => null,
            'prepaidInterestReserve'               => Database2::DATATYPE_MONEY,
            'noOfMonthsPrepaid'                    => Database2::DATATYPE_NUMBER,
            'haveInterestreserve'                  => null,
            'addToTotalProjectValue'               => null,
            'closingCostFinancingFee'              => Database2::DATATYPE_MONEY,
            'attorneyFee'                          => Database2::DATATYPE_MONEY,
            'lean1CurrentDefault'                  => null,
            'lean2CurrentDefault'                  => null,
            'ownPropertyFreeAndClear'              => null,
            'brokerDealPoints'                     => null,
            'brokerDealFee'                        => Database2::DATATYPE_MONEY,
            'brokerQuotedinterestRate'             => null,
            'accessWholesalePricing'               => null,
            'desiredLoanAmount'                    => Database2::DATATYPE_MONEY,
            'escrowFees'                           => Database2::DATATYPE_MONEY,
            'recordingFee'                         => Database2::DATATYPE_MONEY,
            'underwritingFees'                     => Database2::DATATYPE_MONEY,
            'propertyTax'                          => Database2::DATATYPE_MONEY,
            'bufferAndMessengerFee'                => Database2::DATATYPE_MONEY,
            'travelNotaryFee'                      => Database2::DATATYPE_MONEY,
            'prePaidInterest'                      => Database2::DATATYPE_MONEY,
            'realEstateTaxes'                      => Database2::DATATYPE_MONEY,
            'insurancePremium'                     => Database2::DATATYPE_MONEY,
            'payOffLiensCreditors'                 => Database2::DATATYPE_MONEY,
            'wireTransferFeeToTitle'               => Database2::DATATYPE_MONEY,
            'wireTransferFeeToEscrow'              => Database2::DATATYPE_MONEY,
            'pastDuePropertyTaxes'                 => Database2::DATATYPE_MONEY,
            'earnestDeposit'                       => Database2::DATATYPE_MONEY,
            'otherDownPayment'                     => Database2::DATATYPE_MONEY,
            'autoCalcTLAARV'                       => null,
            'maxArvPer'                            => Database2::DATATYPE_MONEY,
            'maxLTCPer'                            => null,
            'refinanceCurrentLoanBalance'          => Database2::DATATYPE_MONEY,
            'approvedAcquisition'                  => Database2::DATATYPE_MONEY,
            'extensionRatePercentage'              => Database2::DATATYPE_MONEY,
            'survey'                               => Database2::DATATYPE_MONEY,
            'wholeSaleAdminFee'                    => Database2::DATATYPE_MONEY,
            'cityCountyTaxStamps'                  => Database2::DATATYPE_MONEY,
            'origination_based_on_total_loan_amt'  => null,
            'broker_based_on_total_loan_amt'       => null,
            'calcInrBasedOnMonthlyPayment'         => null,
            'InrBasedOnMonthlyPayment'             => null,
            'paymentFrequency'                     => null,
            'vacancyFactor'                        => Database2::DATATYPE_MONEY,  //not saving in master, I let miguel know
            'reserveFactor'                        => Database2::DATATYPE_MONEY,
            'reserveFactoron'                      => null,
            'desiredInterestRateRangeFrom'         => null,
            'desiredInterestRateRangeTo'           => null,
            'principalRepayment'                   => null,
            'actualRentsInPlaceCommercial'         => Database2::DATATYPE_MONEY,
            'vacancyFactorCommercial'              => Database2::DATATYPE_MONEY,
            'waterSewer'                           => Database2::DATATYPE_MONEY,
            'electricity'                          => Database2::DATATYPE_MONEY,
            'gas'                                  => Database2::DATATYPE_MONEY,
            'repairsMaintenance'                   => Database2::DATATYPE_MONEY,
            'legal'                                => Database2::DATATYPE_MONEY,
            'payroll'                              => Database2::DATATYPE_MONEY,
            'misc'                                 => Database2::DATATYPE_MONEY,
            'tenantReimursements'                  => Database2::DATATYPE_MONEY,
            'managementExpense'                    => Database2::DATATYPE_MONEY,
            'managementExpensePercentage'          => Database2::DATATYPE_MONEY,
            'actualRentsInPlaceCheckbox'           => null,
            'actualRentsInPlaceCommercialCheckbox' => null,
            'exitFeePoints'                        => Database2::DATATYPE_MONEY,
            'exitFeeAmount'                        => Database2::DATATYPE_MONEY,
            'currentLoanBalance'                   => Database2::DATATYPE_MONEY,
            'initialLoanAmount'                    => Database2::DATATYPE_MONEY,
            'tenantContribution'                   => Database2::DATATYPE_MONEY,
            'otherIncome'                          => Database2::DATATYPE_MONEY,
            'lockOriginationValue'                 => Database2::DATATYPE_NUMBER,
            'lockBrokerValue'                      => Database2::DATATYPE_NUMBER,
            'otherIncomeVacancyRate'               => Database2::DATATYPE_MONEY,
            'commonAreaUtilities'                  => Database2::DATATYPE_MONEY,
            'elevatorMaintenance'                  => Database2::DATATYPE_MONEY,
            'replacementReserves'                  => Database2::DATATYPE_MONEY,
            'other'                                => Database2::DATATYPE_MONEY,
            'tenantContributionVacancyRate'        => Database2::DATATYPE_MONEY,
        ];
        if ($savingTab != 'DA') {
            $HMLOFileNewLoanInfoFieldsArray['estdTitleClosingFee'] = Database2::DATATYPE_MONEY;
        }

        if ($savingTab != 'HMLI') {
            unset($HMLOFileNewLoanInfoFieldsArray['pastDuePropertyTaxes']);
        }

        foreach ($HMLOFileNewLoanInfoFieldsArray as $column => $type) {

            if (isset($_REQUEST[$column])) {

                $postVal = trim($_REQUEST[$column]);

                if ($type) {
                    $postVal = Database2::strongTypeValue($postVal, $type);
                }
                if (in_array($column, [
                    'CORefiLTVPercentage',
                    'maxLTCPer',
                    'borNoOfSquareFeet',
                ])) {
                    $postVal = floatval($postVal);
                }
                if($column == 'HMLOLender') {
                    $postVal = Strings::stripQuote($postVal);
                }

                $tblFileHMLONewLoanInfo->safeSet($column, $postVal);

            } elseif ($column == 'noOfPropertiesAcquiring' && isset($_REQUEST['noOfPropertiesAcquiring'])) {
                $postVal = trim($_REQUEST['noOfPropertiesAcquiring_mirror']);
                $postVal = Database2::strongTypeValue($postVal, $type);
                if ($postVal > 100) {
                    $postVal = 100;
                }
                $tblFileHMLONewLoanInfo->$column = $postVal;

            } elseif (in_array($column, ['actualRentsInPlaceCheckbox', 'actualRentsInPlaceCommercialCheckbox'])) {
                $postVal = 0;
                $tblFileHMLONewLoanInfo->$column = $postVal;
            }
        }


        if (isset($_REQUEST['prePaymentSelectVal']) && count($_REQUEST['prePaymentSelectVal']) > 0) {
            //check for non-empty values
            $prePaymentSelectValues = [];
            foreach ($_REQUEST['prePaymentSelectVal'] as $prePaymentValue) {
                if (!empty($prePaymentValue)) {
                    $prePaymentSelectValues[] = $prePaymentValue;
                }
            }
            $prePaymentSelectValues = count($prePaymentSelectValues) > 0 ? implode(',', $prePaymentSelectValues) : '';
            $tblFileHMLONewLoanInfo->prePaymentSelectVal = trim($prePaymentSelectValues, ',');
        } elseif (isset($_REQUEST['prePaymentSelectValHidden'])) {
            $tblFileHMLONewLoanInfo->prePaymentSelectVal = '';
        }

        if (isset($_REQUEST['loanTermExpireDate'])) {
            $loanTermExpireDate = trim($_REQUEST['loanTermExpireDate']);
            $loanTermExpireDate = trim(Dates::formatDateWithRE($loanTermExpireDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->loanTermExpireDate = $loanTermExpireDate;
        }
        if (Request::isset('LOISentDate')) {
            $LOISentDate = Request::GetClean('LOISentDate');
            $LOISentDate = Dates::formatDateWithRE($LOISentDate, 'MDY', 'Y-m-d');
            $tblFileHMLONewLoanInfo->LOISentDate = $LOISentDate;
        }

        if (isset($_REQUEST['originalPurchaseDate'])) {
            $originalPurchaseDate = trim($_REQUEST['originalPurchaseDate']);
            $originalPurchaseDate = trim(Dates::formatDateWithRE($originalPurchaseDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->originalPurchaseDate = $originalPurchaseDate;
        }

        if (isset($_REQUEST['originalPurchaseDate_mirror'])) {
            $originalPurchaseDate = trim(Request::GetClean('originalPurchaseDate_mirror'));

            $originalPurchaseDate = trim(Dates::formatDateWithRE($originalPurchaseDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->originalPurchaseDate = $originalPurchaseDate;

            $refinanceMortgage = tblRefinanceMortgage::Get([
                tblRefinanceMortgage_db::COLUMN_LMRID => $LMRId,
            ]);

            if (!$refinanceMortgage) {
                $refinanceMortgage = new tblRefinanceMortgage();
            }

            $originalPurchasePrice_mirror = (Request::GetClean('originalPurchasePrice_mirror')) ?? $refinanceMortgage->originalPurchasePrice;
            if (!$originalPurchasePrice_mirror) {
                $originalPurchasePrice_mirror = 0;
            }
            $refinanceMonthlyPayment = $refinanceMortgage->refinanceMonthlyPayment;
            if (!$refinanceMonthlyPayment) {
                $refinanceMonthlyPayment = 0;
            }
            $refinanceMortgage->LMRId = $LMRId;
            $refinanceMortgage->originalPurchaseDate = $originalPurchaseDate;
            $refinanceMortgage->originalPurchasePrice = floatval($originalPurchasePrice_mirror);
            $refinanceMortgage->refinanceMonthlyPayment = floatval($refinanceMonthlyPayment);
            $refinanceMortgage->Save();
        }

        if (isset($_REQUEST['datesigned'])) {
            $datesigned = trim($_REQUEST['datesigned']);
            $datesigned = trim(Dates::formatDateWithRE($datesigned, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->datesigned = $datesigned;
        }

        if (isset($_REQUEST['resaleClosingDate'])) {
            $resaleClosingDate = trim($_REQUEST['resaleClosingDate']);
            $resaleClosingDate = trim(Dates::formatDateWithRE($resaleClosingDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->resaleClosingDate = $resaleClosingDate;
        }

        if (isset($_REQUEST['interestChargedFromDate'])) {
            $interestChargedFromDate = trim($_REQUEST['interestChargedFromDate']);
            $interestChargedFromDate = trim(Dates::formatDateWithRE($interestChargedFromDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->interestChargedFromDate = $interestChargedFromDate;
        }

        if (isset($_REQUEST['perDiemAccrualType'])) {
            $value = trim($_REQUEST['perDiemAccrualType']);
            $tblFileHMLONewLoanInfo->perDiemAccrualType = $value;
        }

        if (isset($_REQUEST['interestChargedEndDate'])) {
            $interestChargedEndDate = trim($_REQUEST['interestChargedEndDate']);
            $interestChargedEndDate = trim(Dates::formatDateWithRE($interestChargedEndDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->interestChargedEndDate = $interestChargedEndDate;
        }

        if (isset($_REQUEST['PAExpirationDate'])) {
            $PAExpirationDate = trim($_REQUEST['PAExpirationDate']);
            $PAExpirationDate = trim(Dates::formatDateWithRE($PAExpirationDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->PAExpirationDate = $PAExpirationDate;
        }

        if (isset($_REQUEST['lien1MaturityDate'])) {
            $lien1MaturityDate = trim($_REQUEST['lien1MaturityDate']);
            $lien1MaturityDate = trim(Dates::formatDateWithRE($lien1MaturityDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->lien1MaturityDate = $lien1MaturityDate;
        }

        if (isset($_REQUEST['lien2MaturityDate'])) {
            $lien2MaturityDate = trim($_REQUEST['lien2MaturityDate']);
            $lien2MaturityDate = trim(Dates::formatDateWithRE($lien2MaturityDate, 'MDY', 'Y-m-d'));
            $tblFileHMLONewLoanInfo->lien2MaturityDate = $lien2MaturityDate;
        }

        if (isset($_REQUEST['originationPointsRate']) ||
            ($_REQUEST['publicUser'] &&
                ($tblFileHMLONewLoanInfo->totalLoanAmount != Strings::replaceCommaValues($_REQUEST['totalLoanAmount'])) &&
                $tblFileHMLONewLoanInfo->lockOriginationValue)
        ) {

            $originationPointsRate = isset($_REQUEST['originationPointsRate']) ? trim(Strings::replaceCommaValues($_REQUEST['originationPointsRate'])) : HMLOLoanTermsCalculation::setOriginationBrokerPoints(Strings::replaceCommaValues($tblFileHMLONewLoanInfo->originationPointsValue),
                Strings::replaceCommaValues($_REQUEST['totalLoanAmount']),
                Strings::replaceCommaValues($tblFileHMLONewLoanInfo->closingCostFinanced),
                ($tblFileHMLONewLoanInfo->origination_based_on_total_loan_amt),
            );

            $oldOriginationPointsRate = $tblFileHMLONewLoanInfo->originationPointsRate;

            $tblFileHMLONewLoanInfo->originationPointsRate = $originationPointsRate;
        }


        if (isset($_REQUEST['originationPointsValue']) ||
            ($_REQUEST['publicUser'] &&
                ($tblFileHMLONewLoanInfo->totalLoanAmount != Strings::replaceCommaValues($_REQUEST['totalLoanAmount'])) &&
                !$tblFileHMLONewLoanInfo->lockOriginationValue)
        ) {

            $originationPointsValue = isset($_REQUEST['originationPointsValue']) ? trim(Strings::replaceCommaValues($_REQUEST['originationPointsValue'])) : HMLOLoanTermsCalculation::setOriginationBrokerValue(Strings::replaceCommaValues($tblFileHMLONewLoanInfo->originationPointsRate),
                Strings::replaceCommaValues($_REQUEST['totalLoanAmount']),
                Strings::replaceCommaValues($tblFileHMLONewLoanInfo->closingCostFinanced),
                ($tblFileHMLONewLoanInfo->origination_based_on_total_loan_amt),
            );

            $tblFileHMLONewLoanInfo->originationPointsValue = $originationPointsValue;
        }

        if (isset($_REQUEST['brokerPointsRate']) ||
            ($_REQUEST['publicUser'] && ($tblFileHMLONewLoanInfo->totalLoanAmount != Strings::replaceCommaValues($_REQUEST['totalLoanAmount'])) && $tblFileHMLONewLoanInfo->lockBrokerValue)) {

            $brokerPointsRate = isset($_REQUEST['brokerPointsRate']) ? trim(Strings::replaceCommaValues($_REQUEST['brokerPointsRate'])) : HMLOLoanTermsCalculation::setOriginationBrokerPoints(Strings::replaceCommaValues($tblFileHMLONewLoanInfo->brokerPointsValue),
                Strings::replaceCommaValues($_REQUEST['totalLoanAmount']),
                Strings::replaceCommaValues($tblFileHMLONewLoanInfo->closingCostFinanced),
                $tblFileHMLONewLoanInfo->broker_based_on_total_loan_amt,
            );

            $oldBrokerPointsRate = $tblFileHMLONewLoanInfo->brokerPointsRate;

            $tblFileHMLONewLoanInfo->brokerPointsRate = $brokerPointsRate;
        }

        if (isset($_REQUEST['brokerPointsValue']) ||
            ($_REQUEST['publicUser'] && ($tblFileHMLONewLoanInfo->totalLoanAmount != Strings::replaceCommaValues($_REQUEST['totalLoanAmount'])) && !$tblFileHMLONewLoanInfo->lockBrokerValue)) {

            $brokerPointsValue = isset($_REQUEST['brokerPointsValue']) ? trim(Strings::replaceCommaValues($_REQUEST['brokerPointsValue'])) : HMLOLoanTermsCalculation::setOriginationBrokerValue(Strings::replaceCommaValues($tblFileHMLONewLoanInfo->brokerPointsRate),
                Strings::replaceCommaValues($_REQUEST['totalLoanAmount']),
                Strings::replaceCommaValues($tblFileHMLONewLoanInfo->closingCostFinanced),
                $tblFileHMLONewLoanInfo->broker_based_on_total_loan_amt,
            );

            $tblFileHMLONewLoanInfo->brokerPointsValue = $brokerPointsValue;
        }

        //CV3 New Fields
        if (isset($_REQUEST['cv3OriginationPoint'])) {
            $cv3OriginationPoint = Strings::replaceCommaValues($_REQUEST['cv3OriginationPoint']);
            $tblFileHMLONewLoanInfo->cv3OriginationPoint = $cv3OriginationPoint;
        }

        if (isset($_REQUEST['cv3ReferralPoint'])) {
            $cv3ReferralPoint = Strings::replaceCommaValues($_REQUEST['cv3ReferralPoint']);
            $tblFileHMLONewLoanInfo->cv3ReferralPoint = $cv3ReferralPoint;
        }

        if (isset($_REQUEST['cv3OriginationAmount'])) {
            $cv3OriginationAmount = Strings::replaceCommaValues($_REQUEST['cv3OriginationAmount']);
            $tblFileHMLONewLoanInfo->cv3OriginationAmount = $cv3OriginationAmount;
        }

        if (isset($_REQUEST['cv3ReferralAmount'])) {
            $cv3ReferralAmount = Strings::replaceCommaValues($_REQUEST['cv3ReferralAmount']);
            $tblFileHMLONewLoanInfo->cv3ReferralAmount = $cv3ReferralAmount;
        }

        if (isset($_REQUEST['brokerProcessingFee'])) {
            $brokerProcessingFee = ($_REQUEST['brokerProcessingFee']) ? Strings::replaceCommaValues($_REQUEST['brokerProcessingFee']) : null;
            $tblFileHMLONewLoanInfo->brokerProcessingFee = $brokerProcessingFee;
        }

        $tblFileHMLONewLoanInfo->UID = $UID;
        $tblFileHMLONewLoanInfo->updatedUserType = $UGroup;
        $tblFileHMLONewLoanInfo->updatedBy = $userName;
        $tblFileHMLONewLoanInfo->recordDate = $recordDate;

        // convert to proper numeric value
        $tblFileHMLONewLoanInfo->extensionOption = $tblFileHMLONewLoanInfo->extensionOption ?: 0;
        $tblFileHMLONewLoanInfo->vacancyFactor = $tblFileHMLONewLoanInfo->vacancyFactor ?: 0;
        $tblFileHMLONewLoanInfo->reserveFactor = $tblFileHMLONewLoanInfo->reserveFactor ?: 0;
        $tblFileHMLONewLoanInfo->noOfMonthsPrepaid = intval($tblFileHMLONewLoanInfo->noOfMonthsPrepaid ?: 0);
        $tblFileHMLONewLoanInfo->refinanceCurrentRate = $tblFileHMLONewLoanInfo->refinanceCurrentRate ?: 0;

        if ($tblFileHMLONewLoanInfo->borNoOfSquareFeet) {
            $tblFileHMLONewLoanInfo->borNoOfSquareFeet = intval($tblFileHMLONewLoanInfo->borNoOfSquareFeet);
        }

//        if ($tblFileHMLONewLoanInfo->borNoOfREPropertiesCompleted)) {
//            $tblFileHMLONewLoanInfo->borNoOfREPropertiesCompleted = intval($tblFileHMLONewLoanInfo->borNoOfREPropertiesCompleted);
//        }

        if ($tblFileHMLONewLoanInfo->origination_based_on_total_loan_amt) {
            $tblFileHMLONewLoanInfo->origination_based_on_total_loan_amt = intval($tblFileHMLONewLoanInfo->origination_based_on_total_loan_amt);
        }

        if ($tblFileHMLONewLoanInfo->broker_based_on_total_loan_amt) {
            $tblFileHMLONewLoanInfo->broker_based_on_total_loan_amt = intval($tblFileHMLONewLoanInfo->broker_based_on_total_loan_amt);
        }

        if ($tblFileHMLONewLoanInfo->noUnitsOccupied) {
            $tblFileHMLONewLoanInfo->noUnitsOccupied = intval($tblFileHMLONewLoanInfo->noUnitsOccupied);
        }

//        if ($tblFileHMLONewLoanInfo->yrsEmployed) {
//            $tblFileHMLONewLoanInfo->yrsEmployed = intval($tblFileHMLONewLoanInfo->yrsEmployed);
//        }

        if ($tblFileHMLONewLoanInfo->noOfPropertiesAcquiring) {
            $tblFileHMLONewLoanInfo->noOfPropertiesAcquiring = intval($tblFileHMLONewLoanInfo->noOfPropertiesAcquiring);
            if ($tblFileHMLONewLoanInfo->noOfPropertiesAcquiring > 100) {
                $tblFileHMLONewLoanInfo->noOfPropertiesAcquiring = 100;
            }

        }

        if ($tblFileHMLONewLoanInfo->CORefiLTVPercentage) {
            $tblFileHMLONewLoanInfo->CORefiLTVPercentage = floatval($tblFileHMLONewLoanInfo->CORefiLTVPercentage);
        }

        if ($tblFileHMLONewLoanInfo->InrBasedOnMonthlyPayment) {
            $tblFileHMLONewLoanInfo->InrBasedOnMonthlyPayment = floatval($tblFileHMLONewLoanInfo->InrBasedOnMonthlyPayment);
        }

        if ($tblFileHMLONewLoanInfo->noOfBuildings) {
            $tblFileHMLONewLoanInfo->noOfBuildings = intval($tblFileHMLONewLoanInfo->noOfBuildings);
        }

        if ($tblFileHMLONewLoanInfo->ownerOccupancyPercentage) {
            $tblFileHMLONewLoanInfo->ownerOccupancyPercentage = floatval($tblFileHMLONewLoanInfo->ownerOccupancyPercentage);
        }

        if ($tblFileHMLONewLoanInfo->maxLTCPer) {
            $tblFileHMLONewLoanInfo->maxLTCPer = floatval($tblFileHMLONewLoanInfo->maxLTCPer);
        }


        if ($tblFileHMLONewLoanInfo->refinanceCurrentRate) {
            $tblFileHMLONewLoanInfo->refinanceCurrentRate = Strings::replaceCommaValues($tblFileHMLONewLoanInfo->refinanceCurrentRate);
        }

        if ($tblFileHMLONewLoanInfo->reserveFactor) {
            $tblFileHMLONewLoanInfo->reserveFactor = Strings::replaceCommaValues($tblFileHMLONewLoanInfo->reserveFactor);
        }

        if ($tblFileHMLONewLoanInfo->PAExpirationDate) {
            $tblFileHMLONewLoanInfo->PAExpirationDate = Dates::Datestamp($tblFileHMLONewLoanInfo->PAExpirationDate, '0000-00-00');
        }

//        tblBudgetAndDraws::initialAdvance(
//            Strings::Numeric($tblFileHMLONewLoanInfo->initialAdvance']),
//            $LMRId,
//            Dates::Datestamp($_REQUEST['fundingDate'])
//        );

        if ($tblFileHMLONewLoanInfo->HMLIID) {
            $tblFileHMLONewLoanInfo->initialLoanAmount = 0;
            $tblFileHMLONewLoanInfo->currentLoanBalance = 0;
        }

        //Loan Terms Save Rate Lock Data
        $tblFileHMLONewLoanInfo->rateLockDate = Request::isset('rateLockDate') ? Dates::formatDateWithRE(Request::GetClean('rateLockDate'), 'MDY', 'Y-m-d') : $tblFileHMLONewLoanInfo->rateLockDate;
        $tblFileHMLONewLoanInfo->rateLockExpirationDate = Request::isset('rateLockExpirationDate') ? Dates::formatDateWithRE(Request::GetClean('rateLockExpirationDate'), 'MDY', 'Y-m-d') : $tblFileHMLONewLoanInfo->rateLockExpirationDate;
        $tblFileHMLONewLoanInfo->rateLockExtension = Request::isset('rateLockExtension') ? Request::GetClean('rateLockExtension') : $tblFileHMLONewLoanInfo->rateLockExtension;
        $tblFileHMLONewLoanInfo->rateLockNotes = Request::isset('rateLockNotes') ? Request::GetClean('rateLockNotes') : $tblFileHMLONewLoanInfo->rateLockNotes;
        $tblFileHMLONewLoanInfo->rateLockPeriod = Request::isset('rateLockPeriod') ? intval(Request::GetClean('rateLockPeriod')) : $tblFileHMLONewLoanInfo->rateLockPeriod;

        $tblFileHMLONewLoanInfo->plansAndPermitsStatus = Request::isset('plansAndPermitsStatus') ? intval(Request::GetClean('plansAndPermitsStatus')) : $tblFileHMLONewLoanInfo->plansAndPermitsStatus;
        $tblFileHMLONewLoanInfo->isProjectRequireRezoning = Request::isset('isProjectRequireRezoning') ? intval(Request::GetClean('isProjectRequireRezoning')) : $tblFileHMLONewLoanInfo->isProjectRequireRezoning;
        $tblFileHMLONewLoanInfo->anticipatedHoldTime = Request::isset('anticipatedHoldTime') ? intval(Request::GetClean('anticipatedHoldTime')) : $tblFileHMLONewLoanInfo->anticipatedHoldTime;
        $tblFileHMLONewLoanInfo->anticipatedPlansPermitTimeline = Request::isset('anticipatedPlansPermitTimeline') ? intval(Request::GetClean('anticipatedPlansPermitTimeline')) : $tblFileHMLONewLoanInfo->anticipatedPlansPermitTimeline;
        $tblFileHMLONewLoanInfo->anticipatedConstructionTimeline = Request::isset('anticipatedConstructionTimeline') ? intval(Request::GetClean('anticipatedConstructionTimeline')) : $tblFileHMLONewLoanInfo->anticipatedConstructionTimeline;
        if (Request::isset('applicantConfirmed')) {
            $tblFileHMLONewLoanInfo->applicantConfirmed = intval(Request::GetClean('applicantConfirmed'));
        }
        $tblFileHMLONewLoanInfo->fundingAs = Request::GetClean('fundingAs') ?? $tblFileHMLONewLoanInfo->fundingAs;

        $tblFileHMLONewLoanInfo->PSAClosingDate = Request::isset('PSAClosingDate') ? Dates::Datestamp(Request::GetClean('PSAClosingDate')) : $tblFileHMLONewLoanInfo->PSAClosingDate;

        $tblFileHMLONewLoanInfo->buildingAnalysisOutstanding = Request::isset('buildingAnalysisOutstanding')
            ? (intval(Request::GetClean('buildingAnalysisOutstanding')) ?: null)
            : $tblFileHMLONewLoanInfo->buildingAnalysisOutstanding;

        $tblFileHMLONewLoanInfo->buildingAnalysisNeed = Request::isset('buildingAnalysisNeed')
            ? (intval(Request::GetClean('buildingAnalysisNeed')) ?: null)
            : $tblFileHMLONewLoanInfo->buildingAnalysisNeed;

        $tblFileHMLONewLoanInfo->buildingAnalysisDueDate = Request::isset('buildingAnalysisDueDate') ? Dates::Datestamp(Request::GetClean('buildingAnalysisDueDate')) : $tblFileHMLONewLoanInfo->buildingAnalysisDueDate;

        $tblFileHMLONewLoanInfo->Save();

        // Multiple Extension Option
        $extensionOptionFields = $ip['p']['extensionOptionFields'] ?? [];
        fileExtensionOptions::getReport($LMRId, $extensionOptionFields); // refactored


        //check if values are updated to save in the notes
        $userName = !$userName ? $ip['p']['borrowerFName'] . '(Client)' : $userName;

        if ($oldOriginationPointsRate != $originationPointsRate) { //updated values
            $notes = "Origination Points updated from $oldOriginationPointsRate to $originationPointsRate by $userName";
            $exeId = 0;
            $empId = 0;
            $brId = 0;
            $clientId = 0;
            //These notes are created by Admin

            $UGroup = '';
            $notesArray = [
                'fileID'         => $LMRId,
                'notesDate'      => $recordDate,
                'processorNotes' => $notes,
                'role'           => $UGroup,
                'privateNotes'   => 1,
                'isSysNotes'     => 1,
                'notesType'      => '',
                'employeeId'     => $empId,
                'executiveId'    => $exeId,
                'brokerNumber'   => $brId,
                'clientId'       => $clientId,
            ];
            saveFileNotes::getReport($notesArray); // refactored
        }

        if ($oldBrokerPointsRate != $brokerPointsRate) {
            $notes = "Broker Points updated from $oldBrokerPointsRate to $brokerPointsRate by $userName";
            $exeId = 0;
            $empId = 0;
            $brId = 0;
            $clientId = 0;
            //These notes are created by Admin

            $UGroup = '';
            $notesArray = [
                'fileID'         => $LMRId,
                'notesDate'      => $recordDate,
                'processorNotes' => $notes,
                'role'           => $UGroup,
                'privateNotes'   => 1,
                'isSysNotes'     => 1,
                'notesType'      => '',
                'employeeId'     => $empId,
                'executiveId'    => $exeId,
                'brokerNumber'   => $brId,
                'clientId'       => $clientId,
            ];
            saveFileNotes::getReport($notesArray); // refactored
        }
        return $cnt;
    }


}
