<?php

namespace models\composite\oHMLOInfo;

use models\Database2;
use models\standard\Arrays;
use models\types\strongType;

/**
 *
 */
class updateClientProfile extends strongType
{
    /**
     * @param $inputArray
     * @return void|null
     */
    public static function getReport($inputArray)
    {
        if (Arrays::getArrayValue('LMRId', $inputArray) > 0
            && (Arrays::getArrayValue('opt', $inputArray) == 'Update current loan file and borrower profile'
                || Arrays::getArrayValue('opt', $inputArray) == 'Update all file and profile')) {

            $sql = 'CALL SP_InsUpdateClientProfile (
                    :fileId,
                    :clientId,
                    :opt
            );';

            $params = [
                'fileId' => Arrays::getArrayValue('LMRId', $inputArray),
                'clientId' => Arrays::getArrayValue('CID', $inputArray),
                'opt' => Arrays::getArrayValue('opt', $inputArray),
            ];
            Database2::getInstance()->queryData($sql, $params);
        }

        return null;

    }
}