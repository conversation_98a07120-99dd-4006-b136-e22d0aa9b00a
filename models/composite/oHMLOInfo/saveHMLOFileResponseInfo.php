<?php

namespace models\composite\oHMLOInfo;

use models\Database2;
use models\lendingwise\tblFileResponse;
use models\standard\Dates;
use models\types\strongType;

/**
 *
 */
class saveHMLOFileResponseInfo extends strongType
{
    /**
     * @param $ip
     * @return int
     */
    public static function getReport($ip): int
    {

        $LMRId = $ip['LMRId'];
        $cnt = 0;
        $PCID = 0;
        $UGroup = '';
        $UID = 0;

        if (array_key_exists('UID', $ip)) $UID = $ip['UID'];
        if (array_key_exists('URole', $ip)) $UGroup = $ip['URole'];
        if (array_key_exists('PCID', $ip)) $PCID = $ip['PCID'];

        if ($LMRId == 0) {
            return $cnt;
        }

        $tblFileResponse = tblFileResponse::Get(['LMRId' => $LMRId]);

        $HMLOFileResponseInfoFieldsArray = [
            'leadSource',
            'projectName',
        ];
        /** DIRECT Variables **/

        foreach ($HMLOFileResponseInfoFieldsArray as $f => $column) {
            if (isset ($_REQUEST[$column])) {
                $postVal = trim($_REQUEST[$column]);
                $tblFileResponse->$column = $postVal;
            }
        }

        if (isset($_REQUEST['trialPaymentDate1'])) {
            $trialPaymentDate1 = trim($_REQUEST['trialPaymentDate1']);
            if (Dates::IsEmpty($trialPaymentDate1)) {
                $trialPaymentDate1 = '0000-00-00';
            } else {
                $trialPaymentDate1 = trim(Dates::formatDateWithRE($trialPaymentDate1, 'MDY', 'Y-m-d'));
            }
            $tblFileResponse->trialPaymentDate1 = $trialPaymentDate1;
        }

        if (isset($_REQUEST['trialPaymentDay1'])) {
            $trialPaymentDay1 = trim($_REQUEST['trialPaymentDay1']);
            if (Dates::IsEmpty($trialPaymentDay1)) {
                $trialPaymentDay1 = '0000-00-00';
            } else {
                $trialPaymentDay1 = trim(Dates::formatDateWithRE($trialPaymentDay1, 'MDY', 'Y-m-d'));
            }

            $tblFileResponse->trialPaymentDay1 = $trialPaymentDay1;
        }

        if (isset($_REQUEST['welcomeCallDate'])) {
            $welcomeCallDate = trim($_REQUEST['welcomeCallDate']);
            if (Dates::IsEmpty($welcomeCallDate)) {
                $welcomeCallDate = '0000-00-00';
            } else {
                $welcomeCallDate = trim(Dates::formatDateWithRE($welcomeCallDate, 'MDY', 'Y-m-d'));
            }

            $tblFileResponse->welcomeCallDate = $welcomeCallDate;
        }

        if (isset($_REQUEST['borrowerCallBack'])) {
            $borrowerCallBack = trim($_REQUEST['borrowerCallBack']);
            if (Dates::IsEmpty($borrowerCallBack)) {
                $borrowerCallBack = '0000-00-00';
            } else {
                $borrowerCallBack = trim(Dates::formatDateWithRE($borrowerCallBack, 'MDY', 'Y-m-d'));
            }

            $tblFileResponse->borrowerCallBack = $borrowerCallBack;
        }

        $tblFileResponse->Save();
        $cnt = 1;

        recordFileTabUpdate::getReport([
            'fileID' => $LMRId,
            'UID' => $UID,
            'UGroup' => $UGroup,
            'opt' => 'HMLO Summary',
            'PCID' => $PCID
        ]);

        return $cnt;
    }
}