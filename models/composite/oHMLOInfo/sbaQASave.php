<?php

namespace models\composite\oHMLOInfo;

use models\Database2;
use models\lendingwise\tblFileSBAQuestions;
use models\lendingwise\tblSbaOtherBusiness;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;
use Symfony\Component\VarDumper\Cloner\Data;

/**
 *
 */
class sbaQASave extends strongType
{
    /**
     * @param $ip
     * @return void
     */
    public static function getReport($ip)
    {
        $LMRId = $ip['LMRId'];
        $clientId = $ip['clientId'] ?? 0;
        $recordDate = $ip['recordDate'];

        /***** Start of SBA Background Questions */
        $tblFileSBAQuestions = tblFileSBAQuestions::Get(['fileID' => $LMRId]);
        if(!$tblFileSBAQuestions) {
            $tblFileSBAQuestions = new tblFileSBAQuestions();
            $tblFileSBAQuestions->fileID = $LMRId;
        }

        $SBAQaArray = [
            'areyouIndictmentCriminal' => null,
            'areyouIndictmentCriminalExpl' => null,

            'haveYouArrested' => null,
            'haveYouArrestedExpl' => null,

            'criminalOffense' => null,
            'criminalOffenseExpl' => null,

            'certifiedDevelopment' => null,
            'certifiedDevelopmentExpl' => null,

            'smallBusinessApplicant' => null,
            'smallBusinessApplicantExpl' => null,

            'smallBusinessApplicantAffil' => null,
            'smallBusinessApplicantAffilExpl' => null,

            'smallBusinessApplicantBankruptcy' => null,
            'smallBusinessApplicantBankruptcyExpl' => null,


            'smallBusinessApplicantLegal' => null,
            'smallBusinessApplicantLegalExpl' => null,


            'debarredSuspendedFederal' => null,
            'debarredSuspendedFederalExpl' => null,


            'childSupportEnforcementServices' => null,
            'childSupportEnforcementServicesExpl' => null,


            'smallBusinessApplicantLoan' => null,
            'anyFinancingDelinquent' => null,
            'anyFinancingDelinquentExpl' => null,
            'anyFinancingDefault' => null,
            'anyFinancingDefaultExpl' => null,


            'smallBusinessApplicantExport' => null,
            'smallBusinessApplicantExportExpl' => null,


            'smallBusinessApplicantPacker' => null,
            'smallBusinessApplicantPackerExpl' => null,


            'smallBusinessApplicantRevenue' => null,
            'smallBusinessApplicantRevenueExpl' => null,

            'noSBAempHouseHoldMember' => null,
            'noSBAempHouseHoldMemberExpl' => null,


            'noFormerSBAempSeparated' => null,
            'noFormerSBAempSeparatedExpl' => null,


            'noMemberSoleProprietor' => null,
            'noMemberSoleProprietorExpl' => null,

            'noGovEmpGS13' => null,
            'noGovEmpGS13Expl' => null,

            'noMemberSmallBuinessAdvisory' => null,
            'noMemberSmallBuinessAdvisoryExpl' => null,

            'haveOwnershipAffiliate' => null,
            'haveOwnershipAffiliateExpl' => null,


            'haveControlledBankruptcyProtection' => null,
            'haveControlledBankruptcyProtectionExpl' => null,

            'businessControlLegalAction' => null,
            'businessControlLegalActionExpl' => null,

            'sbaEconomicInjury' => null,
            'sbaEconomicInjuryExpl' => null,
            'sbaEconomicInjuryLoanAmt' => null,

            'purposeOfTheLoan' => null,
            'purposeOfTheLoanOtherExpl' => null,

            'avgMonthlyPayroll' => 'money',
            'monthlyLoanAmount' => 'money',

            'applicantsPayrollCalculation' => null,
            'applicantsPayrollCalculationExpl' => null,

            'estimatedMonthlyPayroll' => null,
            'estimatedMonthlyPayrollExpl' => null,

            'lossToTheGovernment' => null,
            'lossToTheGovernmentExpl' => null,

            'noOfw2Employee' => 'number',
            'noOfw2sIssue' => 'number',

            'operateSuspension' => null,
            'businessImpact' => null,

            'q12019' => null,
            'q22019' => null,
            'q32019' => null,
            'q42019' => null,

            'q12020' => null,
            'q22020' => null,
            'q32020' => null,
            'q42020' => null,

            'q12021' => null,
            'q22021' => null,
            'q32021' => null,
            'q42021' => null,

            'vendorOperateSuspension' => null,
            'vendorBusinessImpact' => null,

            'q12019v' => null,
            'q22019v' => null,
            'q32019v' => null,
            'q42019v' => null,

            'q12020v' => null,
            'q22020v' => null,
            'q32020v' => null,
            'q42020v' => null,

            'q12021v' => null,
            'q22021v' => null,
            'q32021v' => null,
            'q42021v' => null,

            'receivePPPLoanDraw1' => null,
            'receivePPPLoanDraw2' => null,

            'receivePPPLoanForgivenessDraw1' => null,
            'receivePPPLoanForgivenessDraw2' => null,

            'havePPPLoanForgivenessDraw1' => null,
            'havePPPLoanForgivenessDraw2' => null,

            'payrollServiceProvider' => null,
            'employeeRetentionCredit' => null,
            'employeeRetentionCreditCompany' => null,
            'employeeFamilyOwner' => null,
            'startedOtherBusiness' => null,
            'otherExistingBusiness' => null,

            'companyStarted' => null,
            'quarterEffected' => null,
            'quarterEffectedVendor' => null,

            'quarterExperiencedLoss2020' => null,
            'quarterExperiencedLoss2021' => null,
        ];

        /** DIRECT Variables **/

        $qu = '';
        $qs = '';
        $qc = '';
        $qv = '';
        foreach ($SBAQaArray as  $column => $type) {
            if (isset ($_REQUEST[$column])) {
                if (is_array($_REQUEST[$column])) {
                    $postVal = implode(',', $_REQUEST[$column]);
                } else {
                    $postVal = trim($_REQUEST[$column]);
                }

                $postVal = Database2::strongTypeValue($postVal, $type);

                $tblFileSBAQuestions->$column = $postVal;
            } else {
                $multiSelectFields = ['quarterEffected', 'quarterEffectedVendor', 'quarterExperiencedLoss2020', 'quarterExperiencedLoss2021'];
                if (in_array($column, $multiSelectFields)) {
                    $tblFileSBAQuestions->$column = '';
                }
            }
        }

        //$tblFileSBAQuestions->CID = $clientId;
        $tblFileSBAQuestions->safeSet('CID', $clientId);
        $tblFileSBAQuestions->Save();

        $tblSbaOtherBusiness = tblSbaOtherBusiness::GetAll(['lmrid' => $LMRId]);
        foreach($tblSbaOtherBusiness as $item) {
            $item->Delete();
        }

        for ($a = 0; $a < count($_POST['companyName'] ?? []); $a++) {

            $tblSbaOtherBusiness = new tblSbaOtherBusiness();
            $tblSbaOtherBusiness->lmrid = $LMRId;
            $tblSbaOtherBusiness->created_on = $recordDate;
            $tblSbaOtherBusiness->tblfilesbaquestionsid = $tblSbaOtherBusiness->id;
            $tblSbaOtherBusiness->created_by = $clientId;

            if (isset($_POST['companyName'][$a]) && trim($_POST['companyName'][$a]) != '') {
                $tblSbaOtherBusiness->companyName = trim($_POST['companyName'][$a]);
            }

            if (isset($_POST['taxId'][$a]) && trim($_POST['taxId'][$a]) != '') {
                $tblSbaOtherBusiness->taxId = trim($_POST['taxId'][$a]);
            }

            if (isset($_POST['legalOwners'][$a]) && trim($_POST['legalOwners'][$a]) != '') {
                $tblSbaOtherBusiness->legalOwners = trim($_POST['legalOwners'][$a]);
            }

            if (isset($_POST['noOfEmployes'][$a]) && trim($_POST['noOfEmployes'][$a]) != '') {
                $tblSbaOtherBusiness->noOfEmployees = trim($_POST['noOfEmployes'][$a]);
            }

            if (isset($_POST['yearsInBusiness'][$a]) && trim($_POST['yearsInBusiness'][$a]) != '') {
                $tblSbaOtherBusiness->yearsInBusiness = trim($_POST['yearsInBusiness'][$a]);
            }

            if (isset($_POST['sbaDesc'][$a]) && trim($_POST['sbaDesc'][$a]) != '') {
                $tblSbaOtherBusiness->sbaDesc = trim($_POST['sbaDesc'][$a]);
            }

            if (isset($_POST['sbaLoans_' . $a][0]) && trim($_POST['sbaLoans_' . $a][0]) != '') {
                $tblSbaOtherBusiness->sbaLoans = trim($_POST['sbaLoans_' . $a][0]);
            }
            $tblSbaOtherBusiness->Save();
        }
    }
}