<?php

namespace models\composite\oAutomatedRules;

use models\APIHelper;
use models\composite\oAutomatedRules\getFileWebhookFields\getFileWebhookFieldsEntities;
use models\composite\oAutomatedRules\getFileWebhookFields\getWebhookParameters;
use models\Controllers\backoffice\LMRequest;
use models\Database2;
use models\lendingwise\db\tblMemberOfficerInfo_db;
use models\lendingwise\tblFileLOChekingSavingInfo;
use models\lendingwise\tblMemberOfficerInfo;
use models\oFile\ActiveInvestors;
use models\standard\Strings;
use models\types\strongType;

class getFileWebhookFields extends strongType
{
    public ?string $PCName = null;
    public ?string $branchName = null;
    public ?string $branchEmail = null;
    public ?string $agentName = null;
    public ?string $agentEmail = null;
    public ?string $LMRID = null;
    public ?string $loanNumber1 = null;
    public ?string $LMRServiceType = null;
    public ?string $internalLoanProgram = null;
    public ?string $filestatus = null;
    public ?string $filessubtatus = null;
    public ?string $projectName = null;
    public ?string $receivedDate = null;
    public ?string $borrowerCallBack = null;
    public ?string $createdDate = null;
    public ?string $closingDate = null;
    public ?string $desiredClosingDate = null;
    public ?string $leadSource = null;
    public ?string $_3rdPartyFileId = null;
    public ?string $borrowerFName = null;
    public ?string $borrowerLName = null;
    public ?string $borrowerEmail = null;
    public ?string $borPhone = null;
    public ?string $borCell = null;
    public ?string $presentAddress = null;
    public ?string $presentCity = null;
    public ?string $presentState = null;
    public ?string $presentZip = null;
    public ?string $borPropAddress = null;
    public ?string $propertyCity = null;
    public ?string $propertyState = null;
    public ?string $propertyZip = null;
    public ?string $propertyType = null;
    public ?string $zillowValue = null;
    public ?string $estimatedPropValue = null;
    public ?string $desiredLoanAmount = null;
    public ?string $typeOfHMLOLoanRequesting = null;
    public ?string $lien1Rate = null;
    public ?string $propertyNeedRehab = null;
    public ?string $costBasis = null;
    public ?string $homeValue = null;
    public ?string $maxAmtToPutDown = null;
    public ?string $totalLoanAmount = null;
    public ?string $assessedValue = null;
    public ?string $rehabCost = null;
    public ?string $borrowerDOB = null;
    public ?string $entityName = null;
    public ?string $tradeName = null;
    public ?string $entityType = null;
    public ?string $dateOfFormation = null;
    public ?string $entityStateOfFormation = null;
    public ?string $entityAddress = null;
    public ?string $entityCity = null;
    public ?string $entityState = null;
    public ?string $entityZip = null;
    public ?string $isBorUSCitizen = null;
    public ?string $isBorDeclaredBankruptPastYears = null;
    public ?string $hasBorAnyActiveLawsuits = null;
    public ?string $hasBorObligatedInForeclosure = null;
    public ?string $haveBorOtherfraudRelatedCrimes = null;
    public ?string $isAnyBoroutstandingJudgements = null;
    public ?string $hasBorPropertyTaxLiens = null;
    public ?string $isBorPresenltyDelinquent = null;
    public ?string $borDesignatedBeneficiaryAgreement = null;
    public ?string $haveBorOwnInvestmentProperties = null;
    public ?string $borNoOfOwnProp = null;
    public ?string $areBorMemberOfInvestmentClub = null;
    public ?string $borClubName = null;
    public ?string $haveBorProfLicences = null;
    public ?string $fullTimeRealEstateInvestor = null;
    public ?string $liquidAssets = null;
    public ?string $borPrimaryInvestmentStrategy = null;
    public ?string $overallRealEstateInvesExp = null;
    public ?string $propertySqFt = null;
    public ?string $acres = null;
    public ?string $yearBuilt = null;
    public ?string $howManyBedRoom = null;
    public ?string $howManyBathRoom = null;
    public ?string $howManyHalfBathRoom = null;
    public ?string $basementHome = null;
    public ?string $garageHome = null;
    public ?string $presentOccupancy = null;
    public ?string $propertyCondition = null;
    public ?string $yearRenovated = null;
    public ?string $taxYear = null;
    public ?string $fundingDate = null;
    public ?string $isHouseProperty = null;
    public ?string $HMLOLender = null;
    public ?string $trialPaymentDate1 = null;
    public ?string $maturityDate = null;
    public ?string $loanTerm = null;
    public ?string $lien1Terms = null;
    public ?string $totalMonthlyPaymentAmt = null;
    public ?string $acquisitionPriceFinanced = null;
    public ?string $rehabCostFinanced = null;
    public ?string $prepaidInterestReserve = null;
    public ?string $closingCostFinanced = null;
    public ?string $payOffMortagage1 = null;
    public ?string $payOffMortagage2 = null;
    public ?string $payOffTaxes = null;
    public ?string $originalPurchaseDate = null;
    public ?string $originalPurchasePrice = null;
    public ?string $refinanceMonthlyPayment = null;
    public ?string $refinanceCurrentLender = null;
    public ?string $costOfImprovementsMade = null;
    public ?string $refinanceCurrentRate = null;
    public ?string $refinanceCurrentLoanBalance = null;
    public ?string $originationPointsValue = null;
    public ?string $originationPointsRate = null;
    public ?string $brokerPointsValue = null;
    public ?string $brokerPointsRate = null;
    public ?string $documentPreparationFee = null;
    public ?string $acceptedPurchase = null;
    public ?string $exitStrategy = null;
    public ?string $isBorPersonallyGuaranteeLoan = null;
    public ?string $borrowerMName = null;
    public ?string $borrowerCitizenship = null;
    public ?string $maritalStatus = null;
    public ?string $borrowerNumberDependents = null;
    public ?string $borrowerDependentAges = null;
    public ?string $workNumber = null;
    public ?string $borrowerUnit = null;
    public ?string $borrowerCountry = null;
    public ?string $presentPropLengthTime = null;
    public ?string $presentPropLengthMonths = null;
    public ?string $borrowerRentOrOwn = null;
    public ?string $borrowerPresentRent = null;
    public ?string $borrowerResideTwoYears = null;
    public ?string $borrowerPreviousAddress = null;
    public ?string $borrowerPreviousUnit = null;
    public ?string $borrowerPreviousCity = null;
    public ?string $borrowerPreviousState = null;
    public ?string $borrowerPreviousZip = null;
    public ?string $borrowerPreviousCountry = null;
    public ?string $previousPropLength = null;
    public ?string $previousPropLengthMonths = null;
    public ?string $previousRentOrOwn = null;
    public ?string $previousRent = null;
    public ?string $mailingAddrAsPresent = null;
    public ?string $bormailingAddress = null;
    public ?string $mailingUnit = null;
    public ?string $mailingCity = null;
    public ?string $mailingState = null;
    public ?string $mailingZip = null;
    public ?string $mailingCountry = null;
    public ?string $alternateFName1 = null;
    public ?string $alternateMName1 = null;
    public ?string $alternateLName1 = null;
    public ?string $guarantorFName1 = null;
    public ?string $guarantorMName1 = null;
    public ?string $guarantorLName1 = null;
    public ?string $employerName = null;
    public ?string $employerPhone = null;
    public ?string $employerStreetAddr = null;
    public ?string $employerCity = null;
    public ?string $employerState = null;
    public ?string $employerZip = null;
    public ?string $employerCountry = null;
    public ?string $occupation1 = null;
    public ?string $empStartDate = null;
    public ?string $yearsAtJob1 = null;
    public ?string $employedByOtherParty = null;
    public ?string $ownerOrSelfEmp = null;
    public ?string $ownershipLessOrMore = null;
    public ?string $empmonthlyincome1 = null;
    public ?string $grossIncome1 = null;
    public ?string $overtime1 = null;
    public ?string $commissionOrBonus1 = null;
    public ?string $militaryIncome1 = null;
    public ?string $otherHouseHold1 = null;
    public ?string $propertyUnit = null;
    public ?string $propertyCountry = null;
    public ?string $propertyValue = null;
    public ?string $loanOfficerName = null;
    public ?string $loanOfficerEmail = null;
    public ?string $lenderFullName = null;
    public ?string $lenderEmail = null;
    public ?string $lenderPhone = null;
    public ?string $serviceLenderAddress = null;
    public ?string $serviceLenderCity = null;
    public ?string $serviceLenderState = null;
    public ?string $serviceLenderZip = null;
    public ?string $titleSeller = null;
    public ?string $titleName = null;
    public ?string $titleCompany = null;
    public ?string $titleCompanyEmail = null;
    public ?string $titleCompanyPhoneNumber = null;
    public ?string $titleNotes = null;
    public ?string $titleRecordingNumber = null;
    public ?string $HMLOEstateHeldIn = null;
    public ?string $titleOrderNumber = null;
    public ?string $fileAttorneyName = null;
    public ?string $fileAttorneyCompany = null;
    public ?string $fileAttorneyPhone = null;
    public ?string $fileAttorneyEmail = null;
    public ?string $escrowCompany = null;
    public ?string $escrowEmail = null;
    public ?string $escrowPhone = null;
    public ?string $escrowNum = null;
    public ?string $sellerinfoFirstName = null;
    public ?string $sellerinfoLastName = null;
    public ?string $sellerinfoEmail = null;
    public ?string $sellerinfoPhone = null;
    public ?string $borrowerNotes = null;
    public ?string $lenderNotes = null;
    public ?string $sellerAttorneyFullName = null;
    public ?string $sellerAttorneyFirmName = null;
    public ?string $sellerAttorneyPhone = null;
    public ?string $sellerAttorneyEmail = null;
    public ?string $propertyCounty = null;
    public ?string $exitFeePoints = null;
    public ?string $exitFeeAmount = null;
    public ?string $escrowRepFirstName = null;
    public ?string $escrowRepLastName = null;
    public ?string $escrowTollFree = null;
    public ?string $escrowFax = null;
    public ?string $escrowCell = null;
    public ?string $escrowAddress = null;
    public ?string $escrowCity = null;
    public ?string $escrowState = null;
    public ?string $escrowZip = null;
    public ?string $escrowStateLong = null;
    public ?string $wireAmountRequested = null;
    public ?string $wireSentDate = null;
    public ?string $brokerProcessingFee = null;
    public ?string $constructionHoldbackFee = null;
    public ?string $creditReportFee = null;
    public ?string $underwritingFees = null;
    public ?string $bridgeCombinedLoanToValue = null;
    public ?string $totalPropertiesLTV = null;
    public ?string $initialAdvance = null;
    public ?string $lienPosition = null;
    public ?string $netFundsToBorrower = null;
    public ?string $prepaidInterestReserveMonth = null;
    public ?string $prePaymentPenaltyPercentage_prePaymentPenalty = null;
    public ?string $totalCashOut = null;
    public ?string $yieldSpread = null;
    public ?string $noOfUnits = null;
    public ?string $totalPropertiesLoanAmount = null;
    public ?string $formatCoBSsnNumber = null;
    public ?string $FormatssnLast4Number = null;
    public ?string $formatCoBSsnLast4Number = null;
    public ?string $PublishBInfo = null;
    public ?string $BEthnicity = null;
    public ?string $BRace = null;
    public ?string $BGender = null;
    public ?string $BVeteran = null;
    public ?string $bFiEthnicity = null;
    public ?string $bFiSex = null;
    public ?string $bFiRace = null;
    public ?string $bDemoInfo = null;
    public ?string $PublishCBInfo = null;
    public ?string $CBEthnicity = null;
    public ?string $CBRace = null;
    public ?string $CBGender = null;
    public ?string $CBVeteran = null;
    public ?string $CBFiEthnicity = null;
    public ?string $CBFiGender = null;
    public ?string $CBDDemoInfo = null;
    public ?string $CBFiRace = null;
    public ?string $midFicoScore = null;
    public ?string $midFicoScoreCoBor = null;
    public ?string $borExperianScore = null;
    public ?string $coBorExperianScore = null;
    public ?string $borEquifaxScore = null;
    public ?string $coBorEquifaxScore = null;
    public ?string $borTransunionScore = null;
    public ?string $coBorTransunionScore = null;
    public ?string $formatSsnNumber = null;
    public ?string $MERSID = null;
    public ?string $accountTitledAs = null;
    public ?string $paymentReservesAmt = null;
    public ?string $loanFileStage = null;

    public const ColumnDataTypes = [
        'BEthnicity'                                    => Database2::DATATYPE_ETHNICITY,
        'BGender'                                       => Database2::DATATYPE_GENDER,
        'BRace'                                         => Database2::DATATYPE_RACE,
        'BVeteran'                                      => Database2::DATATYPE_VETERAN,
        'CBDDemoInfo'                                   => Database2::DATATYPE_DEMOGRAPHIC_INFO_PROVIDED_BY,
        'CBEthnicity'                                   => Database2::DATATYPE_ETHNICITY,
        'CBFiEthnicity'                                 => Database2::DATATYPE_STRING,
        'CBFiGender'                                    => Database2::DATATYPE_STRING,
        'CBFiRace'                                      => Database2::DATATYPE_STRING,
        'CBGender'                                      => Database2::DATATYPE_GENDER,
        'CBRace'                                        => Database2::DATATYPE_RACE,
        'CBVeteran'                                     => Database2::DATATYPE_VETERAN,
        'FormatssnLast4Number'                          => Database2::DATATYPE_SSN4,
        'HMLOEstateHeldIn'                              => Database2::DATATYPE_STRING,
        'HMLOLender'                                    => Database2::DATATYPE_STRING,
        'LMRID'                                         => Database2::DATATYPE_NUMBER,
        'LMRServiceType'                                => Database2::DATATYPE_STRING,
        'MERSID'                                        => Database2::DATATYPE_STRING,
        'PCName'                                        => Database2::DATATYPE_STRING,
        'PublishBInfo'                                  => Database2::DATATYPE_STRING,
        'PublishCBInfo'                                 => Database2::DATATYPE_STRING,
        '3rdPartyFileId'                                => Database2::DATATYPE_STRING,
        'acceptedPurchase'                              => Database2::DATATYPE_STRING,
        'accountTitledAs'                               => Database2::DATATYPE_STRING,
        'acquisitionPriceFinanced'                      => Database2::DATATYPE_MONEY_STR,
        'acres'                                         => Database2::DATATYPE_STRING,
        'agentEmail'                                    => Database2::DATATYPE_EMAIL,
        'agentName'                                     => Database2::DATATYPE_STRING,
        'alternateFName1'                               => Database2::DATATYPE_STRING,
        'alternateLName1'                               => Database2::DATATYPE_STRING,
        'alternateMName1'                               => Database2::DATATYPE_STRING,
        'areBorMemberOfInvestmentClub'                  => Database2::DATATYPE_STRING,
        'assessedValue'                                 => Database2::DATATYPE_MONEY_STR,
        'bDemoInfo'                                     => Database2::DATATYPE_DEMOGRAPHIC_INFO_PROVIDED_BY,
        'bFiEthnicity'                                  => Database2::DATATYPE_STRING,
        'bFiRace'                                       => Database2::DATATYPE_STRING,
        'bFiSex'                                        => Database2::DATATYPE_STRING,
        'basementHome'                                  => Database2::DATATYPE_STRING,
        'borCell'                                       => Database2::DATATYPE_PHONE_NUMBER,
        'borClubName'                                   => Database2::DATATYPE_STRING,
        'borDesignatedBeneficiaryAgreement'             => Database2::DATATYPE_STRING,
        'borEquifaxScore'                               => Database2::DATATYPE_STRING,
        'borExperianScore'                              => Database2::DATATYPE_STRING,
        'borNoOfOwnProp'                                => Database2::DATATYPE_STRING,
        'borPhone'                                      => Database2::DATATYPE_PHONE_NUMBER,
        'borPrimaryInvestmentStrategy'                  => Database2::DATATYPE_STRING,
        'borPropAddress'                                => Database2::DATATYPE_STRING,
        'borTransunionScore'                            => Database2::DATATYPE_STRING,
        'bormailingAddress'                             => Database2::DATATYPE_STRING,
        'borrowerCallBack'                              => Database2::DATATYPE_DATE_STANDARD,
        'borrowerCitizenship'                           => Database2::DATATYPE_STRING,
        'borrowerCountry'                               => Database2::DATATYPE_STRING,
        'borrowerDOB'                                   => Database2::DATATYPE_DATE_STANDARD,
        'borrowerDependentAges'                         => Database2::DATATYPE_STRING,
        'borrowerEmail'                                 => Database2::DATATYPE_EMAIL,
        'borrowerFName'                                 => Database2::DATATYPE_STRING,
        'borrowerLName'                                 => Database2::DATATYPE_STRING,
        'borrowerMName'                                 => Database2::DATATYPE_STRING,
        'borrowerNotes'                                 => Database2::DATATYPE_STRING,
        'borrowerNumberDependents'                      => Database2::DATATYPE_STRING,
        'borrowerPresentRent'                           => Database2::DATATYPE_MONEY_STR,
        'borrowerPreviousAddress'                       => Database2::DATATYPE_STRING,
        'borrowerPreviousCity'                          => Database2::DATATYPE_STRING,
        'borrowerPreviousCountry'                       => Database2::DATATYPE_STRING,
        'borrowerPreviousState'                         => Database2::DATATYPE_STRING,
        'borrowerPreviousUnit'                          => Database2::DATATYPE_STRING,
        'borrowerPreviousZip'                           => Database2::DATATYPE_ZIP,
        'borrowerRentOrOwn'                             => Database2::DATATYPE_STRING,
        'borrowerResideTwoYears'                        => Database2::DATATYPE_STRING,
        'borrowerUnit'                                  => Database2::DATATYPE_STRING,
        'branchEmail'                                   => Database2::DATATYPE_EMAIL,
        'branchName'                                    => Database2::DATATYPE_STRING,
        'bridgeCombinedLoanToValue'                     => Database2::DATATYPE_PERCENT_STR,
        'brokerPointsRate'                              => Database2::DATATYPE_PERCENT_STR,
        'brokerPointsValue'                             => Database2::DATATYPE_MONEY_STR,
        'brokerProcessingFee'                           => Database2::DATATYPE_MONEY_STR,
        'closingCostFinanced'                           => Database2::DATATYPE_MONEY_STR,
        'closingDate'                                   => Database2::DATATYPE_DATE_STANDARD,
        'coBorEquifaxScore'                             => Database2::DATATYPE_NUMBER,
        'coBorExperianScore'                            => Database2::DATATYPE_NUMBER,
        'coBorTransunionScore'                          => Database2::DATATYPE_NUMBER,
        'commissionOrBonus1'                            => Database2::DATATYPE_MONEY_STR,
        'constructionHoldbackFee'                       => Database2::DATATYPE_MONEY_STR,
        'costBasis'                                     => Database2::DATATYPE_MONEY_STR,
        'costOfImprovementsMade'                        => Database2::DATATYPE_MONEY_STR,
        'createdDate'                                   => Database2::DATATYPE_DATE_STANDARD,
        'creditReportFee'                               => Database2::DATATYPE_MONEY_STR,
        'dateOfFormation'                               => Database2::DATATYPE_DATE_STANDARD,
        'desiredClosingDate'                            => Database2::DATATYPE_DATE_STANDARD,
        'desiredLoanAmount'                             => Database2::DATATYPE_MONEY_STR,
        'documentPreparationFee'                        => Database2::DATATYPE_MONEY_STR,
        'empStartDate'                                  => Database2::DATATYPE_DATE_STANDARD,
        'employedByOtherParty'                          => Database2::DATATYPE_STRING,
        'employerCity'                                  => Database2::DATATYPE_STRING,
        'employerCountry'                               => Database2::DATATYPE_STRING,
        'employerName'                                  => Database2::DATATYPE_STRING,
        'employerPhone'                                 => Database2::DATATYPE_STRING,
        'employerState'                                 => Database2::DATATYPE_STRING,
        'employerStreetAddr'                            => Database2::DATATYPE_STRING,
        'employerZip'                                   => Database2::DATATYPE_ZIP,
        'empmonthlyincome1'                             => Database2::DATATYPE_MONEY_STR,
        'entityAddress'                                 => Database2::DATATYPE_STRING,
        'entityCity'                                    => Database2::DATATYPE_STRING,
        'entityName'                                    => Database2::DATATYPE_STRING,
        'entityState'                                   => Database2::DATATYPE_STATE,
        'entityStateOfFormation'                        => Database2::DATATYPE_STATE,
        'entityType'                                    => Database2::DATATYPE_STRING,
        'entityZip'                                     => Database2::DATATYPE_ZIP,
        'escrowAddress'                                 => Database2::DATATYPE_STRING,
        'escrowCell'                                    => Database2::DATATYPE_PHONE_NUMBER,
        'escrowCity'                                    => Database2::DATATYPE_STRING,
        'escrowCompany'                                 => Database2::DATATYPE_STRING,
        'escrowEmail'                                   => Database2::DATATYPE_EMAIL,
        'escrowFax'                                     => Database2::DATATYPE_PHONE_NUMBER,
        'escrowNum'                                     => Database2::DATATYPE_STRING,
        'escrowPhone'                                   => Database2::DATATYPE_PHONE_NUMBER,
        'escrowRepFirstName'                            => Database2::DATATYPE_STRING,
        'escrowRepLastName'                             => Database2::DATATYPE_STRING,
        'escrowState'                                   => Database2::DATATYPE_STRING,
        'escrowStateLong'                               => Database2::DATATYPE_STATE,
        'escrowTollFree'                                => Database2::DATATYPE_PHONE_NUMBER,
        'escrowZip'                                     => Database2::DATATYPE_ZIP,
        'estimatedPropValue'                            => Database2::DATATYPE_MONEY_STR,
        'exitFeeAmount'                                 => Database2::DATATYPE_MONEY_STR,
        'exitFeePoints'                                 => Database2::DATATYPE_PERCENT_STR,
        'exitStrategy'                                  => Database2::DATATYPE_STRING,
        'fileAttorneyCompany'                           => Database2::DATATYPE_STRING,
        'fileAttorneyEmail'                             => Database2::DATATYPE_EMAIL,
        'fileAttorneyName'                              => Database2::DATATYPE_STRING,
        'fileAttorneyPhone'                             => Database2::DATATYPE_PHONE_NUMBER,
        'filessubtatus'                                 => Database2::DATATYPE_STRING,
        'filestatus'                                    => Database2::DATATYPE_STRING,
        'formatCoBSsnLast4Number'                       => Database2::DATATYPE_SSN4,
        'formatCoBSsnNumber'                            => Database2::DATATYPE_SSN,
        'formatSsnNumber'                               => Database2::DATATYPE_SSN,
        'fullTimeRealEstateInvestor'                    => Database2::DATATYPE_STRING,
        'fundingDate'                                   => Database2::DATATYPE_DATE_STANDARD,
        'garageHome'                                    => Database2::DATATYPE_STRING,
        'grossIncome1'                                  => Database2::DATATYPE_MONEY_STR,
        'guarantorFName1'                               => Database2::DATATYPE_STRING,
        'guarantorLName1'                               => Database2::DATATYPE_STRING,
        'guarantorMName1'                               => Database2::DATATYPE_STRING,
        'hasBorAnyActiveLawsuits'                       => Database2::DATATYPE_STRING,
        'hasBorObligatedInForeclosure'                  => Database2::DATATYPE_STRING,
        'hasBorPropertyTaxLiens'                        => Database2::DATATYPE_STRING,
        'haveBorOtherfraudRelatedCrimes'                => Database2::DATATYPE_STRING,
        'haveBorOwnInvestmentProperties'                => Database2::DATATYPE_STRING,
        'haveBorProfLicences'                           => Database2::DATATYPE_STRING,
        'homeValue'                                     => Database2::DATATYPE_MONEY_STR,
        'howManyBathRoom'                               => Database2::DATATYPE_NUMBER,
        'howManyBedRoom'                                => Database2::DATATYPE_NUMBER,
        'howManyHalfBathRoom'                           => Database2::DATATYPE_NUMBER,
        'initialAdvance'                                => Database2::DATATYPE_MONEY_STR,
        'internalLoanProgram'                           => Database2::DATATYPE_STRING,
        'isAnyBoroutstandingJudgements'                 => Database2::DATATYPE_STRING,
        'isBorDeclaredBankruptPastYears'                => Database2::DATATYPE_STRING,
        'isBorPersonallyGuaranteeLoan'                  => Database2::DATATYPE_STRING,
        'isBorPresenltyDelinquent'                      => Database2::DATATYPE_STRING,
        'isBorUSCitizen'                                => Database2::DATATYPE_STRING,
        'isHouseProperty'                               => Database2::DATATYPE_STRING,
        'leadSource'                                    => Database2::DATATYPE_STRING,
        'lenderEmail'                                   => Database2::DATATYPE_EMAIL,
        'lenderFullName'                                => Database2::DATATYPE_STRING,
        'lenderNotes'                                   => Database2::DATATYPE_STRING,
        'lenderPhone'                                   => Database2::DATATYPE_PHONE_NUMBER,
        'lien1Rate'                                     => Database2::DATATYPE_PERCENT_STR,
        'lien1Terms'                                    => Database2::DATATYPE_STRING,
        'lienPosition'                                  => Database2::DATATYPE_NUMBER,
        'liquidAssets'                                  => Database2::DATATYPE_MONEY_STR,
        'loanNumber1'                                   => Database2::DATATYPE_STRING,
        'loanOfficerEmail'                              => Database2::DATATYPE_EMAIL,
        'loanOfficerName'                               => Database2::DATATYPE_STRING,
        'loanTerm'                                      => Database2::DATATYPE_STRING,
        'mailingAddrAsPresent'                          => Database2::DATATYPE_STRING,
        'mailingCity'                                   => Database2::DATATYPE_STRING,
        'mailingCountry'                                => Database2::DATATYPE_STRING,
        'mailingState'                                  => Database2::DATATYPE_STATE,
        'mailingUnit'                                   => Database2::DATATYPE_STRING,
        'mailingZip'                                    => Database2::DATATYPE_ZIP,
        'maritalStatus'                                 => Database2::DATATYPE_STRING,
        'maturityDate'                                  => Database2::DATATYPE_DATE_STANDARD,
        'maxAmtToPutDown'                               => Database2::DATATYPE_MONEY_STR,
        'midFicoScore'                                  => Database2::DATATYPE_NUMBER,
        'midFicoScoreCoBor'                             => Database2::DATATYPE_NUMBER,
        'militaryIncome1'                               => Database2::DATATYPE_MONEY_STR,
        'netFundsToBorrower'                            => Database2::DATATYPE_MONEY_STR,
        'noOfUnits'                                     => Database2::DATATYPE_NUMBER,
        'occupation1'                                   => Database2::DATATYPE_STRING,
        'originalPurchaseDate'                          => Database2::DATATYPE_DATE_STANDARD,
        'originalPurchasePrice'                         => Database2::DATATYPE_MONEY_STR,
        'originationPointsRate'                         => Database2::DATATYPE_PERCENT_STR,
        'originationPointsValue'                        => Database2::DATATYPE_MONEY_STR,
        'otherHouseHold1'                               => Database2::DATATYPE_MONEY_STR,
        'overallRealEstateInvesExp'                     => Database2::DATATYPE_STRING,
        'overtime1'                                     => Database2::DATATYPE_MONEY_STR,
        'ownerOrSelfEmp'                                => Database2::DATATYPE_STRING,
        'ownershipLessOrMore'                           => Database2::DATATYPE_STRING,
        'payOffMortagage1'                              => Database2::DATATYPE_MONEY_STR,
        'payOffMortagage2'                              => Database2::DATATYPE_MONEY_STR,
        'payOffTaxes'                                   => Database2::DATATYPE_MONEY_STR,
        'paymentReservesAmt'                            => Database2::DATATYPE_MONEY_STR,
        'prePaymentPenaltyPercentage_prePaymentPenalty' => Database2::DATATYPE_STRING,
        'prepaidInterestReserve'                        => Database2::DATATYPE_MONEY_STR,
        'prepaidInterestReserveMonth'                   => Database2::DATATYPE_STRING,
        'presentAddress'                                => Database2::DATATYPE_STRING,
        'presentCity'                                   => Database2::DATATYPE_STRING,
        'presentOccupancy'                              => Database2::DATATYPE_STRING,
        'presentPropLengthMonths'                       => Database2::DATATYPE_STRING,
        'presentPropLengthTime'                         => Database2::DATATYPE_STRING,
        'presentState'                                  => Database2::DATATYPE_STRING,
        'presentZip'                                    => Database2::DATATYPE_ZIP,
        'previousPropLength'                            => Database2::DATATYPE_STRING,
        'previousPropLengthMonths'                      => Database2::DATATYPE_STRING,
        'previousRent'                                  => Database2::DATATYPE_MONEY_STR,
        'previousRentOrOwn'                             => Database2::DATATYPE_STRING,
        'projectName'                                   => Database2::DATATYPE_STRING,
        'propertyCity'                                  => Database2::DATATYPE_STRING,
        'propertyCondition'                             => Database2::DATATYPE_STRING,
        'propertyCountry'                               => Database2::DATATYPE_STRING,
        'propertyCounty'                                => Database2::DATATYPE_STRING,
        'propertyNeedRehab'                             => Database2::DATATYPE_STRING,
        'propertySqFt'                                  => Database2::DATATYPE_NUMBER,
        'propertyState'                                 => Database2::DATATYPE_STATE,
        'propertyType'                                  => Database2::DATATYPE_STRING,
        'propertyUnit'                                  => Database2::DATATYPE_STRING,
        'propertyValue'                                 => Database2::DATATYPE_MONEY,
        'propertyZip'                                   => Database2::DATATYPE_ZIP,
        'receivedDate'                                  => Database2::DATATYPE_DATE_STANDARD,
        'refinanceCurrentLender'                        => Database2::DATATYPE_STRING,
        'refinanceCurrentLoanBalance'                   => Database2::DATATYPE_MONEY_STR,
        'refinanceCurrentRate'                          => Database2::DATATYPE_PERCENT_STR,
        'refinanceMonthlyPayment'                       => Database2::DATATYPE_MONEY_STR,
        'rehabCost'                                     => Database2::DATATYPE_MONEY_STR,
        'rehabCostFinanced'                             => Database2::DATATYPE_MONEY_STR,
        'sellerAttorneyEmail'                           => Database2::DATATYPE_EMAIL,
        'sellerAttorneyFirmName'                        => Database2::DATATYPE_STRING,
        'sellerAttorneyFullName'                        => Database2::DATATYPE_STRING,
        'sellerAttorneyPhone'                           => Database2::DATATYPE_PHONE_NUMBER,
        'sellerinfoEmail'                               => Database2::DATATYPE_EMAIL,
        'sellerinfoFirstName'                           => Database2::DATATYPE_STRING,
        'sellerinfoLastName'                            => Database2::DATATYPE_STRING,
        'sellerinfoPhone'                               => Database2::DATATYPE_PHONE_NUMBER,
        'serviceLenderAddress'                          => Database2::DATATYPE_STRING,
        'serviceLenderCity'                             => Database2::DATATYPE_STRING,
        'serviceLenderState'                            => Database2::DATATYPE_STRING,
        'serviceLenderZip'                              => Database2::DATATYPE_ZIP,
        'taxYear'                                       => Database2::DATATYPE_STRING,
        'titleCompany'                                  => Database2::DATATYPE_STRING,
        'titleCompanyEmail'                             => Database2::DATATYPE_EMAIL,
        'titleCompanyPhoneNumber'                       => Database2::DATATYPE_PHONE_NUMBER,
        'titleName'                                     => Database2::DATATYPE_STRING,
        'titleNotes'                                    => Database2::DATATYPE_STRING,
        'titleOrderNumber'                              => Database2::DATATYPE_STRING,
        'titleRecordingNumber'                          => Database2::DATATYPE_STRING,
        'titleSeller'                                   => Database2::DATATYPE_STRING,
        'totalCashOut'                                  => Database2::DATATYPE_MONEY_STR,
        'totalLoanAmount'                               => Database2::DATATYPE_MONEY_STR,
        'totalMonthlyPaymentAmt'                        => Database2::DATATYPE_MONEY_STR,
        'totalPropertiesLTV'                            => Database2::DATATYPE_PERCENT_STR,
        'totalPropertiesLoanAmount'                     => Database2::DATATYPE_MONEY_STR,
        'tradeName'                                     => Database2::DATATYPE_STRING,
        'trialPaymentDate1'                             => Database2::DATATYPE_DATE_STANDARD,
        'typeOfHMLOLoanRequesting'                      => Database2::DATATYPE_STRING,
        'underwritingFees'                              => Database2::DATATYPE_MONEY_STR,
        'wireAmountRequested'                           => Database2::DATATYPE_MONEY_STR,
        'wireSentDate'                                  => Database2::DATATYPE_DATE_STANDARD,
        'workNumber'                                    => Database2::DATATYPE_PHONE_NUMBER,
        'yearBuilt'                                     => Database2::DATATYPE_STRING,
        'yearRenovated'                                 => Database2::DATATYPE_STRING,
        'yearsAtJob1'                                   => Database2::DATATYPE_STRING,
        'yieldSpread'                                   => Database2::DATATYPE_PERCENT_STR,
        'zillowValue'                                   => Database2::DATATYPE_MONEY_STR,
        'loanFileStage'                                 => Database2::DATATYPE_STRING,

        // calculated
        'account'                                       => Database2::DATATYPE_STRING,
        'accountType'                                   => Database2::DATATYPE_STRING,
        'amountBorrowed'                                => Database2::DATATYPE_MONEY_STR,
        'balanceValue'                                  => Database2::DATATYPE_MONEY_STR,
        'beneficiaries'                                 => Database2::DATATYPE_STRING,
        'companyName'                                   => Database2::DATATYPE_STRING,
        'cost'                                          => Database2::DATATYPE_MONEY_STR,
        'dateofQuote'                                   => Database2::DATATYPE_DATE_STANDARD,
        'description'                                   => Database2::DATATYPE_STRING,
        'faceAmountFinance'                             => Database2::DATATYPE_MONEY_STR,
        'investorYield'                                 => Database2::DATATYPE_PERCENT_STR,
        'marketValueQuote'                              => Database2::DATATYPE_MONEY_STR,
        'memberFullName'                                => Database2::DATATYPE_STRING,
        'memberTitle'                                   => Database2::DATATYPE_STRING,
        'memberDOB'                                     => Database2::DATATYPE_DATE_STANDARD,
        'memberSSN'                                     => Database2::DATATYPE_SSN,
        'memberCreditScore'                             => Database2::DATATYPE_NUMBER,
        'memberAddress'                                 => Database2::DATATYPE_STRING,
        'memberPhone'                                   => Database2::DATATYPE_PHONE_NUMBER,
        'memberEmail'                                   => Database2::DATATYPE_EMAIL,
        'memberOwnership'                               => Database2::DATATYPE_STRING,
        'memberPersonalGuarantee'                       => Database2::DATATYPE_STRING,
        'memberAuthorizedSigner'                        => Database2::DATATYPE_STRING,
        'nameofInstitution'                             => Database2::DATATYPE_STRING,
        'nameofSecurity'                                => Database2::DATATYPE_STRING,
        'owners'                                        => Database2::DATATYPE_STRING,
        'pledged'                                       => Database2::DATATYPE_STRING,
        'shareValueInBond'                              => Database2::DATATYPE_MONEY_STR,
        'statementDate'                                 => Database2::DATATYPE_DATE_STANDARD,
        'typeofPolicy'                                  => Database2::DATATYPE_STRING,
    ];

    /* @var getFileWebhookFieldsEntities[] $entities */
    public ?array $entities = null;

    /* @var tblMemberOfficerInfo[] $memberOfficers */
    public ?array $memberOfficers = null;

    public function __get($name)
    {
        switch ($name) {
            case '3rdPartyFileId':
                return $this->_3rdPartyFileId;
        }

        return parent::__get($name); // TODO: Change the autogenerated stub
    }

    /**
     * @param int $LMRId
     * @param bool $debug
     * @return getFileWebhookFields|null
     */
    public static function getReport(int $LMRId, bool $debug = false): ?self
    {
        $sql = APIHelper::getSQL(__DIR__ . '/getFileWebhookFields/sql/getFileWebhookFields.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId' => $LMRId,
        ], function ($row) use ($debug) {
            if ($debug) {
                // when debugging, set all returned values to non-empty
                // so it's easy to see which are not coming back from the database
                $k = 100;
                foreach ($row as $field => $value) {
                    $row[$field] = $k;
                    $k += 10;
                }
            }
            return new self($row);
        });

        if (sizeof($res) > 1) {
            Debug([
                'error' => 'multiple rows',
                'sql'   => Database2::getInstance()->safeQuery($sql, ['LMRId' => $LMRId]),
            ]);
        }

        $final = $res[0] ?? null;

        if (!$final) {
            return null;
        }

        $sql = APIHelper::getSQL(__DIR__ . '/getFileWebhookFields/sql/getFileWebhookFieldsEntities.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId' => $LMRId,
        ], function ($row) {
            return new getFileWebhookFieldsEntities($row);
        });

        $final->entities = $res;

        return $final;
    }

    private ?array $_ActiveInvestors = null;

    /**
     * @return ActiveInvestors[]|null
     */
    public function ActiveInvestors(): ?array
    {
        if (is_null($this->_ActiveInvestors)) {
            LMRequest::setLMRId($this->LMRID, true); // don't muck with file data, just get what's there

            $this->_ActiveInvestors = LMRequest::File()->ActiveInvestors();

        }
        return $this->_ActiveInvestors;
    }

    /* @var tblFileLOChekingSavingInfo[] $_fileLOChekingSavingInfo */
    private ?array $_fileLOChekingSavingInfo = null;

    public function fileLOChekingSavingInfo(): ?array
    {
        if (is_null($this->_fileLOChekingSavingInfo) && $this->LMRID) {
            $this->_fileLOChekingSavingInfo = tblFileLOChekingSavingInfo::GetAll([
                'fileID' => $this->LMRID,
            ]);
        }
        return $this->_fileLOChekingSavingInfo;
    }

    /**
     * @return tblMemberOfficerInfo[]|null
     */
    public function MemberOfficers(): ?array
    {
        if (is_null($this->memberOfficers)) {
            $this->memberOfficers = tblMemberOfficerInfo::GetAll([
                tblMemberOfficerInfo_db::COLUMN_LMRID => $this->LMRID,
            ]);
        }
        return $this->memberOfficers;
    }

    /**
     * @param getFileWebhookFields|null $res
     * @param getWebhookParameters[] $params
     * @param bool $parseData
     * @return array
     */
    public static function getValueArray(
        ?getFileWebhookFields $res,
        array                 $params,
        bool                  $parseData = true
    ): array {
        $list = [];

        foreach ($params as $param) {
            if (property_exists($res, $param->paramValue)) {
                $list[$param->paramValue] = $res->{$param->paramValue};
                continue;
            }

            if (is_numeric($param->paramValue[0])) {
                $list[$param->paramValue] = $res->{'_' . $param->paramValue};
                continue;
            }

            $matches = [];
            if (preg_match('/^accountType(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->accType : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^shareValueInBond(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->shareValueInBond : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^nameofSecurity(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->nameofSecurity : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^cost(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->cost : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^dateofQuote(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->dateofQuote : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^typeofPolicy(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->typeofPolicy : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^faceAmountFinance(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->faceAmount : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^amountBorrowed(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->amountBorrowed : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^beneficiaries(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->beneficiaries : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^nameofInstitution(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->nameAddrOfBank : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^accountTitledAs(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->accountTitledAs : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^accountTitledAs/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[0] ?? null;
                $list[$param->paramValue] = $investor ? $investor->accountTitledAs : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^account(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->accountNumber : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^balanceValue(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->balance : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^owners(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->owners : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^description(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->description : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^statementDate(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->statementDate : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^pledged(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->pledged : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^marketValueQuote(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->fileLOChekingSavingInfo()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->marketValueQuote : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^companyName(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->ActiveInvestors()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->companyName : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^investorYield(\d+)/si', $param->paramValue, $matches)) {
                $investor = $res->ActiveInvestors()[$matches[1] - 1] ?? null;
                $list[$param->paramValue] = $investor ? $investor->investorYield : null;
                continue;
            }

            $matches = [];
            if (preg_match('/^member(\d+)(\w+)/si', $param->paramValue, $matches)) {
                $member = $res->memberOfficers()[$matches[1] - 1] ?? null;
                switch ($matches[2]) {
                    case 'FullName':
                        $list[$param->paramValue] = $member ? $member->memberName : null;
                        break;
                    case 'Title':
                        $list[$param->paramValue] = $member ? $member->memberTitle : null;
                        break;
                    case 'DOB':
                        $list[$param->paramValue] = $member ? $member->memberDOB : null;
                        break;
                    case 'SSN':
                        $list[$param->paramValue] = $member ? $member->memberSSN : null;
                        break;
                    case 'CreditScore':
                        $list[$param->paramValue] = $member ? $member->memberCreditScore : null;
                        break;
                    case 'Address':
                        $list[$param->paramValue] = $member ? $member->memberAddress : null;
                        break;
                    case 'Phone':
                        $list[$param->paramValue] = $member ? $member->memberPhone : null;
                        break;
                    case 'Email':
                        $list[$param->paramValue] = $member ? $member->memberEmail : null;
                        break;
                    case 'Ownership':
                        $list[$param->paramValue] = $member ? $member->memberOwnership : null;
                        break;
                    case 'PersonalGuarantee':
                        $list[$param->paramValue] = $member ? $member->memberPersonalGuarantee : null;
                        break;
                    case 'AuthorizedSigner':
                        $list[$param->paramValue] = $member ? $member->memberAuthorizedSigner : null;
                        break;
                    default:
                        Debug($matches);
                }

                continue;
            }

            Debug($param->paramValue . ' not found for webhooks');
        }

        ksort($list);
        if ($parseData) {
            foreach ($list as $k => $v) {

                $type = self::ColumnDataTypes[$k] ?? null;

                if (!$type) {
                    $matches = [];
                    if (
                        preg_match('/^(accountType)(\d+)/si', $k, $matches)
                        || preg_match('/^(shareValueInBond)(\d+)/si', $k, $matches)
                        || preg_match('/^(nameofSecurity)(\d+)/si', $k, $matches)
                        || preg_match('/^(cost)(\d+)/si', $k, $matches)
                        || preg_match('/^(dateofQuote)(\d+)/si', $k, $matches)
                        || preg_match('/^(typeofPolicy)(\d+)/si', $k, $matches)
                        || preg_match('/^(faceAmountFinance)(\d+)/si', $k, $matches)
                        || preg_match('/^(amountBorrowed)(\d+)/si', $k, $matches)
                        || preg_match('/^(beneficiaries)(\d+)/si', $k, $matches)
                        || preg_match('/^(nameofInstitution)(\d+)/si', $k, $matches)
                        || preg_match('/^(accountTitledAs)(\d+)/si', $k, $matches)
                        || preg_match('/^(accountTitledAs)/si', $k, $matches)
                        || preg_match('/^(account)(\d+)/si', $k, $matches)
                        || preg_match('/^(balanceValue)(\d+)/si', $k, $matches)
                        || preg_match('/^(owners)(\d+)/si', $k, $matches)
                        || preg_match('/^(description)(\d+)/si', $k, $matches)
                        || preg_match('/^(statementDate)(\d+)/si', $k, $matches)
                        || preg_match('/^(pledged)(\d+)/si', $k, $matches)
                        || preg_match('/^(marketValueQuote)(\d+)/si', $k, $matches)
                        || preg_match('/^(companyName)(\d+)/si', $k, $matches)
                        || preg_match('/^(investorYield)(\d+)/si', $k, $matches)
                    ) {
                        $type = self::ColumnDataTypes[$matches[1]] ?? null;
                    }
                }

                if (!$type) {
                    if (preg_match('/^(member)(\d+)(\w+)/si', $k, $matches)) {
                        $type = self::ColumnDataTypes[$matches[1] . $matches[3]] ?? null;
                    }
                }


                if (!$type) {
                    print_r(['data type not set', $k, $v, $list]);
                    exit;
                }

                $list[$k] = Database2::strongTypeValue($v, $type);
                // pr($list);

                //  exit;
            }
        }
        // pr($list);
        // exit;
        return $list;
    }
}
