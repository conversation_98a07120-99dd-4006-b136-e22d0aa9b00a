SELECT t270.processingCompanyName                       AS PCName
     , t250.LMRExecutive                                AS branchName
     , t250.executiveEmail                              AS branchEmail
     , CONCAT(t280.firstName, ' ', t280.lastName)       AS agentName
     , t280.email                                       AS agentEmail
     , t100.LMRID
     , t100.loanNumber                                  AS loanNumber1
     , (SELECT GROUP_CONCAT(tls.serviceType SEPARATOR ', ')
        FROM tblLMRClientType tl
                 JOIN tblLibServiceTypes tls ON tls.STCode = tl.ClientType
        WHERE tl.LMRID = @LMRId
          AND tls.activeStatus = 1
        ORDER BY tl.LMRId, tl.ClientType)               AS LMRServiceType
     , (SELECT GROUP_CONCAT(t1000.internalLoanProgram SEPARATOR ', ')
        FROM tblFileInternalLoanPrograms t1000
        WHERE t1000.LMRID = @LMRId
          AND t1000.activeStatus = 1)                   AS internalLoanProgram
     , t340.primaryStatus                               AS filestatus
     , (SELECT GROUP_CONCAT(t370.substatus, ', ')
        FROM tblFileSubstatus t360
                 LEFT JOIN tblPCFileSubstatus t370
                           ON t360.substatusId = t370.PFSID AND t370.activeStatus = 1
        WHERE t360.dateUnchecked IS NULL
          AND t360.fileID = @LMRId)                     AS filessubtatus
     , t110.projectName
     , t100.receivedDate
     , t110.borrowerCallBack
     , t100.recordDate                                  AS createdDate
     , t210.closingDate
     , t210.desiredClosingDate
     , t110.leadSource
     , t110.3rdPartyFileId
     , t100.borrowerName                                AS borrowerFName
     , t100.borrowerLName
     , t100.borrowerEmail
     , t100.phoneNumber                                 AS borPhone
     , t100.cellNumber                                  AS borCell
     , t120.presentAddress
     , t120.presentCity
     , t120.presentState
     , t120.presentZip
     , t1000.propertyAddress                             AS borPropAddress
     , t1000.propertyCity
     , t1000.propertyState
     , t1000.propertyZipCode as propertyZip
     , t1011.propType                                   as propertyType
     , t1020.propertyZillowValue                        as zillowValue
     , t1020.propertyEstimatedValue                     as estimatedPropValue
     , t140.desiredLoanAmount
     , t140.typeOfHMLOLoanRequesting
     , t100.lien1Rate
     , t150.propertyNeedRehab
     , t140.costBasis
     , t100.homeValue
     , t140.maxAmtToPutDown
     , t700.TotalLoanAmount                             as totalLoanAmount
     , t220.assessedValue
     , t130.rehabCost
     , t100.borrowerDOB
     , t230.isBorUSCitizen
     , t230.isBorDecalredBankruptPastYears              AS isBorDeclaredBankruptPastYears
     , t230.hasBorAnyActiveLawsuits
     , t230.hasBorObligatedInForeclosure
     , t230.haveBorOtherfraudRelatedCrimes
     , t230.isAnyBoroutstandingJudgements
     , t230.hasBorPropertyTaxLiens
     , t230.isBorPresenltyDelinquent
     , t230.borDesignatedBeneficiaryAgreement
     , t240.haveBorOwnInvestmentProperties
     , t240.borNoOfOwnProp
     , t240.areBorMemberOfInvestmentClub
     , t240.borClubName
     , t240.haveBorProfLicences
     , t240.fullTimeRealEstateInvestor
     , t240.liquidAssets
     , t240.borPrimaryInvestmentStrategy
     , t240.overallRealEstateInvesExp
     , t1010.propertySqFt                               as propertySqFt
     , t1010.propertyAcres                              as acres
     , t1010.propertyYearBuilt                          as yearBuilt
     , t1010.propertyNumberOfBedRooms                   as howManyBedRoom
     , t1010.propertyNumberOfBathRooms                  as howManyBathRoom
     , t1010.propertyNumberOfHalfBathRooms              as howManyHalfBathRoom
     , t1010.propertyIsHomeHaveBasement                 as basementHome
     , t1010.propertyIsHomeHaveGarage                   as garageHome
     , t1020.propertyPresentOccupancy                   as presentOccupancy
     , t1020.propertyCondition
     , t1020.propertyTaxYear                            as taxYear
     , t1020.propertyRenovatedYear                      as yearRenovated
     , t150.fundingDate
     , t160.isHouseProperty
     , t140.HMLOLender
     , t210.closingDate
     , t110.trialPaymentDate1
     , t150.maturityDate
     , t150.loanTerm
     , t100.lien1Terms
     , t100.lien1Payment                                AS totalMonthlyPaymentAmt -- calculated
     , t700.initialLoanAmount                           AS acquisitionPriceFinanced
     , t140.rehabCostFinanced
     , t140.prepaidInterestReserve
     , t140.closingCostFinanced
     , t140.payOffMortgage1                             AS payOffMortagage1
     , t140.payOffMortgage1                             AS payOffMortagage2
     , t140.payOffOutstandingTaxes                      AS payOffTaxes
     , t140.originalPurchaseDate
     , t140.originalPurchasePrice
     , t140.refinanceMonthlyPayment
     , t140.refinanceCurrentLender
     , t140.costOfImprovementsMade
     , t140.refinanceCurrentRate
     , t140.refinanceCurrentLoanBalance
     , t140.originationPointsValue
     , t140.originationPointsRate
     , t140.brokerPointsValue
     , t140.brokerPointsRate
     , t140.documentPreparationFee
     , t150.acceptedPurchase
     , t150.exitStrategy
     , t230.isBorPersonallyGuaranteeLoan
     , t100.borrowerMName
     , t130.borrowerCitizenship
     , t100.maritalStatus
     , t130.numberOfDependents                          AS borrowerNumberDependents
     , t130.agesOfDependent                             AS borrowerDependentAges
     , t100.workNumber
     , t120.presentUnit                                 AS borrowerUnit
     , t120.presentCountry                              AS borrowerCountry
     , t120.presentPropLengthTime
     , t120.presentPropLengthMonths
     , t120.borPresentPropType                          AS borrowerRentOrOwn
     , t120.currentRPM                                  AS borrowerPresentRent
     , t290.borResidedPresentAddr                       AS borrowerResideTwoYears
     , t100.previousAddress                             AS borrowerPreviousAddress
     , t120.previousUnit                                AS borrowerPreviousUnit
     , t100.previousCity                                AS borrowerPreviousCity
     , t100.previousState                               AS borrowerPreviousState
     , t100.previousZip                                 AS borrowerPreviousZip
     , t120.previousCountry                             AS borrowerPreviousCountry
     , t120.previousPropLengthTime                      AS previousPropLength
     , t120.previousPropLengthMonths
     , t120.borFormerPropType                           AS previousRentOrOwn
     , t120.previousRPM                                 AS previousRent
     , t120.mailingAddrAsPresent
     , t100.mailingAddress                              AS bormailingAddress
     , t100.mailingUnit
     , t100.mailingCity
     , t100.mailingState
     , t100.mailingZip
     , t100.mailingCountry
     , (SELECT t1000.alternateFName
        FROM tblBorrowerAlternateNames t1000
        WHERE t1000.LMRID = t100.LMRId
        ORDER BY t1000.nameID DESC LIMIT 1)                                        AS alternateFName1
     , (SELECT t1000.alternateMName
        FROM tblBorrowerAlternateNames t1000
        WHERE t1000.LMRID = t100.LMRId
        ORDER BY t1000.nameID DESC LIMIT 1)                                        AS alternateMName1
     , (SELECT t1000.alternateLName
        FROM tblBorrowerAlternateNames t1000
        WHERE t1000.LMRID = t100.LMRId
        ORDER BY t1000.nameID DESC LIMIT 1)                                        AS alternateLName1
     , (SELECT t1000.guarantorFName
        FROM tblFileAdditionalGuarantors t1000
        WHERE t1000.LMRID = t100.LMRId
        ORDER BY t1000.AGID DESC LIMIT 1)                                        AS guarantorFName1
     , (SELECT t1000.guarantorMName
        FROM tblFileAdditionalGuarantors t1000
        WHERE t1000.LMRID = t100.LMRId
        ORDER BY t1000.AGID DESC LIMIT 1)                                        AS guarantorMName1
     , (SELECT t1000.guarantorLName
        FROM tblFileAdditionalGuarantors t1000
        WHERE t1000.LMRID = t100.LMRId
        ORDER BY t1000.AGID DESC LIMIT 1)                                        AS guarantorLName1
     , t190.employer1                                   AS employerName
     , t190.employer1Phone                              AS employerPhone
     , t190.employer1Add                                AS employerStreetAddr
     , t190.employer1City                               AS employerCity
     , t190.employer1State                              AS employerState
     , t190.employer1Zip                                AS employerZip
     , t190.employer1Country                            AS employerCountry
     , t190.occupation1
     , t190.borrowerHireDate                            AS empStartDate
     , t190.yearsAtJob1
     , t190.employedByOtherParty
     , t190.ownerOrSelfEmpoyed                          AS ownerOrSelfEmp
     , IF(t190.emptypeshare1 = 'lessthan25', 'I have ownership share of less than 25%',
          IF(t190.emptypeshare1 = 'eqmorethan25', 'I have ownership share of 25% or more',
             ''))                                       AS ownershipLessOrMore
     , t190.empmonthlyincome1
     , t190.grossIncome1
     , t190.overtime1
     , t190.commissionOrBonus1
     , t190.militaryIncome1
     , t190.otherHouseHold1
     , t1000.propertyUnit
     , t1000.propertyCountry
     , t1010.propertyValue
     , t700.TotalLoanAmount                             AS totalLoanAmount
     , CONCAT(t285.firstName, ' ', t285.lastName)       AS loanOfficerName
     , t285.email                                       AS loanOfficerEmail
     , CONCAT(t601.contactName, ' ', t601.contactLName) AS lenderFullName
     , t601.email                                       AS lenderEmail
     , t601.phone                                       AS lenderPhone
     , t601.address                                     AS serviceLenderAddress
     , t601.city                                        AS serviceLenderCity
     , t601.state                                       AS serviceLenderState
     , t601.zip                                         AS serviceLenderZip
     , t160.titleSeller
     , t160.titleName
     , t401.companyName                                 as titleCompany
     , t401.email                                       as titleCompanyEmail
     , t401.phone                                       as titleCompanyPhoneNumber
     , t401.description                                 AS titleNotes
     , t160.recordingNo                                 AS titleRecordingNumber
     , t150.HMLOEstateHeldIn
     , t160.titleOrderNumber
     , t210.attorneyName                                AS fileAttorneyName
     , t210.attorneyFirmName                            AS fileAttorneyCompany
     , t210.attorneyPhone                               AS fileAttorneyPhone
     , t210.attorneyEmail                               AS fileAttorneyEmail
     , t501.companyName                                 AS escrowCompany
     , t501.email                                       AS escrowEmail
     , t501.phone                                       AS escrowPhone
     , t501.barNo                                       AS escrowNum
     , t200.sellerinfoFirstName
     , t200.sellerinfoLastName
     , t200.sellerinfoEmail
     , t200.sellerinfoPhone
     , t100.mortgageNotes                               AS borrowerNotes
     , t150.lenderNotes
     , CONCAT(t701.contactName, ' ', t701.contactLName) AS sellerAttorneyFullName
     , t701.companyName                                 AS sellerAttorneyFirmName
     , t701.phone                                       AS sellerAttorneyPhone
     , t701.email                                       AS sellerAttorneyEmail
     , t1000.propertyCounty                             as propertyCounty
     , t140.exitFeePoints
     , t140.exitFeeAmount
     , t501.contactName                                 AS escrowRepFirstName
     , t501.contactLName                                AS escrowRepLastName
     , t501.companyName                                 AS escrowCompany
     , t501.email                                       AS escrowEmail
     , t501.phone                                       AS escrowPhone
     , t501.tollFree                                    AS escrowTollFree
     , t501.fax                                         AS escrowFax
     , t501.cell                                        AS escrowCell
     , t501.barNo                                       AS escrowNum
     , t501.address                                     AS escrowAddress
     , t501.city                                        AS escrowCity
     , t501.state                                       AS escrowState
     , t501.zip                                         AS escrowZip
     , (SELECT stateName
        FROM tblStates
        WHERE tblStates.stateCode = t501.state)         AS escrowStateLong

     , t400.wireAmountRequested
     , t400.wireSentDate
     , t140.brokerProcessingFee
     , t140.constructionHoldbackFee
     , t140.creditReportFee
     , t140.underwritingFees
     , t600.bridgeCombinedLoanToValue
     , t600.totalPropertiesLTV
     , t140.initialAdvance
     , t150.lienPosition
     , t140.totalLoanAmount
    - t700.totalFeesAndCost
    - t140.rehabCostFinanced
    - t140.prepaidInterestReserve                       AS netFundsToBorrower
     , t140.noOfMonthsPrepaid                           AS prepaidInterestReserveMonth
     , IF(t150.prePaymentPenalty = 'No Pre-Pay Penalty', t150.prePaymentPenalty,
          CONCAT(t140.prePaymentPenaltyPercentage, ' % for ',
                 t150.prePaymentPenalty))               AS prePaymentPenaltyPercentage_prePaymentPenalty
     , t700.totalCashOutAmt                             AS totalCashOut
     , t140.yieldSpread
     , t1010.propertyNumberOfUnits                      AS noOfUnits
     , t600.totalPropertiesLoanAmount
     , t100.ssnNumber                                   AS formatSsnNumber
     , t100.coBSsnNumber                                AS formatCoBSsnNumber
     , t100.ssnNumber                                   AS FormatssnLast4Number
     , t100.coBSsnNumber                                AS formatCoBSsnLast4Number
     , t210.PublishBInfo
     , t210.BEthnicity
     , t210.BRace
     , t210.BGender
     , t210.BVeteran
     , t210.bFiEthnicity
     , t210.bFiSex
     , t210.bFiRace
     , t210.bDemoInfo
     , t210.PublishCBInfo
     , t210.CBEthnicity
     , t210.CBRace
     , t210.CBGender
     , t210.CBVeteran
     , t210.CBFiEthnicity
     , t210.CBFiGender
     , t210.CBDDemoInfo
     , t210.CBFiRace

     , t130.midFicoScore
     , t130.midFicoScoreCoBor
     , t130.borExperianScore
     , t130.coBorExperianScore
     , t130.borEquifaxScore
     , t130.coBorEquifaxScore
     , t130.borTransunionScore
     , t130.coBorTransunionScore
     , t140.MERSID

     , t700.paymentReservesAmt
     , t710.entityAddress
     , t710.tradeName
     , t710.entityStateOfFormation
     , t710.dateOfFormation
     , t710.entityType
     , t710.entityName
     , t710.entityCity
     , t710.entityState
     , t710.entityZip
     , t702.accountTitledAs
     , tls.stageDisplayName                        AS loanFileStage


FROM tblFile t100
    LEFT JOIN tblFileResponse t110 ON t110.LMRId = t100.LMRId
    LEFT JOIN tblFile2 AS t120 ON t120.LMRID = t100.LMRId
    LEFT JOIN tblFileHMLO t130 ON t130.fileID = t100.LMRId
    LEFT JOIN tblFileHMLONewLoanInfo t140 ON t140.fileID = t100.LMRId
    LEFT JOIN tblFileHMLOPropInfo t150 ON t150.fileID = t100.LMRId
    LEFT JOIN tblFilePropertyInfo t160 ON t160.LMRId = t100.LMRId
    LEFT JOIN tblFileResponse t170 ON t170.LMRId = t100.LMRId
    LEFT JOIN tblHomeReport t180 ON t180.fileID = t100.LMRId
    LEFT JOIN tblIncomeInfo t190 ON t190.LMRId = t100.LMRId
    LEFT JOIN tblPropSellerInfo t200 ON t200.LMRId = t100.LMRId
    LEFT JOIN tblQAInfo t210 ON t210.LMRId = t100.LMRId
    LEFT JOIN tblShortSale t220 ON t220.LMRId = t100.LMRId
    LEFT JOIN tblFileHMLOBackGround t230 ON t100.LMRId = t230.fileID
    LEFT JOIN tblFileHMLOExperience t240 ON t100.LMRId = t240.fileID
    LEFT JOIN tblBranch t250 ON t250.executiveId = t100.FBRID
    LEFT JOIN tblProcessingCompany t270 ON t100.FPCID = t270.PCID
    LEFT JOIN tblAgent t280 ON t280.userNumber = t100.brokerNumber
    LEFT JOIN tblAgent t285 ON t285.userNumber = t100.secondaryBrokerNumber
    LEFT JOIN tblFileLoanOrigination t290 ON t290.fileID = t100.LMRId
    LEFT JOIN tblFundingClosing t400 ON t100.LMRId = t400.LMRId
    LEFT JOIN tblLoanPropertySummary t600 ON t100.LMRId = t600.LMRId
    LEFT JOIN tblFileCalculatedValues t700 ON t100.LMRId = t700.LMRId

    LEFT JOIN tblFileHMLOBusinessEntity t710 ON t100.LMRId = t710.fileID

    LEFT JOIN tblProperties t1000 ON t100.LMRId = t1000.LMRId AND t1000.isPrimary = 1
    LEFT JOIN tblPropertiesCharacteristics t1010 ON t1000.propertyId = t1010.propertyId
    LEFT JOIN tblHMLOPropertyType t1011 ON t1010.propertyType = t1011.propTypeId
    LEFT JOIN tblPropertiesDetails t1020 ON t1000.propertyId = t1020.propertyId

    LEFT JOIN tblPCPrimeStatus t340 ON t170.primeStatusId = t340.PSID AND t340.activeStatus = 1

    LEFT JOIN (SELECT t400.fileID
    , t401.*
    FROM tblFileContacts t400
    LEFT JOIN tblContacts t401 ON t401.CID = t400.CID
    WHERE t400.fileID = @LMRId
    AND t400.cRole = 'Title Rep' LIMIT 1) t401 ON t401.fileID = t100.LMRId

    LEFT JOIN (SELECT t500.fileID
    , t501.*
    FROM tblFileContacts t500
    LEFT JOIN tblContacts t501 ON t501.CID = t500.CID
    WHERE t500.fileID = @LMRId
    AND t500.cRole = 'Escrow' LIMIT 1) t501 ON t501.fileID = t100.LMRId

    LEFT JOIN (SELECT t600.fileID
    , t601.*
    FROM tblFileContacts t600
    LEFT JOIN tblContacts t601 ON t601.CID = t600.CID
    WHERE t600.fileID = @LMRId
    AND t600.cRole = 'Lender' LIMIT 1) t601 ON t601.fileID = t100.LMRId


    LEFT JOIN (SELECT t700.fileID
    , t701.*
    FROM tblFileContacts t700
    LEFT JOIN tblContacts t701 ON t701.CID = t700.CID
    WHERE t700.fileID = @LMRId
    AND t700.cRole = 'Seller Attorney' LIMIT 1) t701 ON t701.fileID = t100.LMRId

    LEFT JOIN (SELECT t1000.fileID, t1000.accountTitledAs
    FROM tblFileLOChekingSavingInfo t1000
    WHERE t1000.fileID = @LMRId  LIMIT 1) t702 ON t100.LMRId = t702.fileID

    LEFT JOIN tblLoanFileStages tlfs ON t100.LMRId = tlfs.LMRId
    LEFT JOIN tblLoanStages tls ON tlfs.stageId = tls.stageId AND tls.isActive = 1
WHERE t100.LMRId = @LMRId
