<?php

namespace models\composite;


use models\APIHelper;
use models\composite\LoanServicing\getVested;
use models\constants\accrualTypes;
use models\constants\amortizationType;
use models\Database2;
use models\lendingwise\tblCharges;
use models\lendingwise\tblEscrow;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblFileInterestRateHistory;
use models\lendingwise\tblIncomeInfo;
use models\lendingwise\tblInvestorFunding;
use models\lendingwise\tblInvestorHistory;
use models\lendingwise\tblLedger;
use models\lendingwise\tblPayment;
use models\servicing\LoanTerms;
use models\standard\Dates;
use models\standard\Strings;
use models\types\compositeType;

/**
 *
 */
class LoanServicing extends compositeType
{
    private ?int $LMRId;
    private ?tblFile $file = null;

    /**
     * @var tblCharges[]|void
     */
    private ?array $ChargesOwed = null;

    /**
     * @var tblFileInterestRateHistory[]|null
     */
    private ?array $_InterestRateHistory = null;

    /**
     * @var tblInvestorHistory[]|void
     */
    private ?array $InvestorHistoryRecords = null;

    public ?bool $isInvalid = null;

    /**
     * @param int $LMRId
     * @param tblFile|null $file
     */
    public function __construct(
        int      $LMRId,
        ?tblFile $file = null
    )
    {
        $this->LMRId = $LMRId;
        if ($file) {
            $this->file = $file;
        }
        $this->getFile();
    }

    public function checkValid(): array
    {

        $invalidReason = [];
        if (!$this->isInvalid) {

            if (stristr($this->getFile()->getTblFileHMLOPropInfo_by_fileID()->loanTerm, 'ARM') !== false) {
                $invalidReason [] = 'ARM Loan Term Not Supported';
            }


            if (!$this->getFile()->getAmortization()) {
                $invalidReason [] = 'Amortization is not Set';
            } elseif ($this->getFile()->getAmortization() !== LoanTerms::INTEREST_ONLY) {
                if (stristr($this->getFile()->getAmortization(), 'Years') === false) {
                    $invalidReason [] = 'Amortization must be Years or Interest Only';
                }
            }


            if (!in_array($this->getFile()->getPaymentMethod(), [LoanTerms::ILA, LoanTerms::TLA])) {
                $invalidReason [] = 'Payment Method is not ILA or TLA';
            }

            if (!in_array($this->getFile()->getAccrualType(), [
                accrualTypes::ACCRUAL_TYPE_30_360,
                accrualTypes::ACCRUAL_TYPE_30_365,
                accrualTypes::ACCRUAL_TYPE_ACTUAL_360,
                accrualTypes::ACCRUAL_TYPE_ACTUAL_365,
            ])) {
                $valid_types = [
                    accrualTypes::$accrualTypes[accrualTypes::ACCRUAL_TYPE_30_360],
                    accrualTypes::$accrualTypes[accrualTypes::ACCRUAL_TYPE_30_365],
                    accrualTypes::$accrualTypes[accrualTypes::ACCRUAL_TYPE_ACTUAL_360],
                    accrualTypes::$accrualTypes[accrualTypes::ACCRUAL_TYPE_ACTUAL_365],
                ];
                $invalidReason [] = 'Accrual Type is not ' . implode(' or ', $valid_types);
            }

            if (Dates::IsEmpty($this->getFile()->getFirstPaymentDueDate())) {
                $invalidReason [] = 'First Payment Date is not Set';
            }

            if (Dates::IsEmpty($this->getFile()->getFundingDate())) {
                $invalidReason [] = 'Funding Date is not Set';
            }

            if ($this->getFile()->getAmortizationType() === amortizationType::ADJUSTABLE) {
                $invalidReason [] = 'ARM Loans Not Supported';
            }

            if (!$this->getFile()->getAmortization()) {
                $invalidReason [] = 'Amortization Not Supported';
            }

            if (!$this->getFile()->getAccrualDays()) {
                $invalidReason [] = 'Accrual Days Not Set';
            }
        }

        if (sizeof($invalidReason)) {
            $this->isInvalid = true;
        }

        return $invalidReason;
    }

    /**
     * @return int|null
     */
    public function getLMRId(): ?int
    {
        return $this->LMRId;
    }

    /**
     * @return tblFileInterestRateHistory|null
     */
    public function getInterestRateHistory(): ?array
    {
        if (is_null($this->_InterestRateHistory)) {
            $this->_InterestRateHistory = tblFileInterestRateHistory::GetAll([
                'LMRId' => $this->getFile()->LMRId
            ], [
                'apply_date' => 'desc'
            ]);
        }
        return $this->_InterestRateHistory;
    }

    /**
     * @param string|null $date
     * @return float
     */
    public function getRehabLimit(string $date = null): float
    {
        $sql = '
            SELECT 
                SUM(amount) AS amount
            FROM (
                SELECT
                    -amount AS amount
                FROM 
                    tblLedger
                WHERE debit_source_name = :debit_source_name
                AND LMRId = :LMRId
                ' . ($date ? 'AND transactionDatetime <= :date' : '') . '
            
            UNION
            
                SELECT
                    amount AS amount
                FROM 
                    tblLedger
                WHERE credit_source_name = :credit_source_name
                AND LMRId = :LMRId
                ' . ($date ? 'AND transactionDatetime <= :date' : '') . '
            ) t100
                    
            ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'              => $this->LMRId,
            'debit_source_name'  => LoanAmortization::LEDGER_ESCROW_REHAB,
            'credit_source_name' => LoanAmortization::LEDGER_ESCROW_REHAB,
            'date'               => $date != '0000-00-00' ? Dates::Datestamp($date) : null,
        ]);
        return floatval($res[0]['amount'] ?? 0);
    }

    /**
     * @return float
     */
    public function getRehabBalance(): float
    {
        return $this->getFile()->getRehabCostFinanced() - $this->getRehabLimit();
    }

    /**
     * @param string|null $date
     * @return int|mixed
     */
    public function getPerDiemPaid(string $date = null)
    {
        $date = $date ? Dates::Datestamp($date) : null;
        $sql = '
                    SELECT 
                           SUM(amount) AS amount
                    FROM (
                          
                    SELECT
                           SUM(amount) AS amount
                    FROM 
                         tblLedger
                    WHERE transactionType = :transactionTypeDebit
                      AND credit_source_name = :credit_source_name
                    AND LMRId = :LMRId
                     ' . ($date ? 'AND DATE(transactionDatetime) <= :date' : '') . '
                    
                    UNION
                    
                    SELECT
                           -SUM(amount) AS amount
                    FROM tblLedger
                    WHERE transactionType = :transactionTypeCredit
                      AND debit_source_name = :debit_source_name
                    AND LMRId = :LMRId
                     ' . ($date ? 'AND DATE(transactionDatetime) <= :date' : '') . '
                        ) t100
                    
            ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'                 => $this->LMRId,
            'date'                  => $date,
            'transactionTypeDebit'  => LoanAmortization::LEDGER_TRANSACTION_TYPE_PAYMENT,
            'transactionTypeCredit' => LoanAmortization::LEDGER_TRANSACTION_TYPE_PAYMENT,
            'credit_source_name'    => LoanAmortization::LEDGER_PER_DIEM,
            'debit_source_name'     => LoanAmortization::LEDGER_PER_DIEM,
        ]);
        return $res[0]['amount'] ?? 0;
    }

    /**
     * @param string|null $date
     * @param string $type
     * @return float
     */
    public function getPrepaidBalance(?string $date, string $type): float
    {
        $date = Dates::Datestamp($date);
        $sql = '
                    SELECT 
                           SUM(amount) AS amount
                    FROM (
                          
                    SELECT
                           -SUM(amount) AS amount
                    FROM 
                         tblLedger
                    WHERE debit_source_name = :DebitSourceName
                    AND LMRId = :LMRId
                    AND DATE(transactionDatetime) <= :date
                    GROUP BY debit_source_id
                    
                    UNION
                    
                    SELECT
                           SUM(amount) AS amount
                    FROM tblLedger
                    WHERE credit_source_name = :CreditSourceName
                    AND LMRId = :LMRId
                    AND DATE(transactionDatetime) <= :date
                    GROUP BY credit_source_id
                        ) t100
                    
            ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'            => $this->LMRId,
            'date'             => $date,
            'DebitSourceName'  => $type,
            'CreditSourceName' => $type,
        ]);
        return floatval($res[0]['amount'] ?? 0);
    }

    /**
     * @param string|null $date
     * @return float
     */
    public function getPrepaidEscrowReserveBalance(?string $date): float
    {
        return $this->getPrepaidBalance($date, LoanAmortization::LEDGER_ESCROW_PREPAID_ESCROW);
    }

    /**
     * @param string|null $date
     * @return float
     */
    public function getPrepaidInterestReserveBalance(?string $date): float
    {
        return $this->getPrepaidBalance($date, LoanAmortization::LEDGER_ESCROW_PREPAID_INTEREST);
    }

    /**
     * @param string $date
     * @return int|mixed
     */
    public function getDrawBalance(string $date)
    {
        $date = Dates::Datestamp($date);
        $sql = '
                    SELECT 
                           SUM(amount) AS amount
                    FROM (
                          
                    SELECT
                           SUM(amount) AS amount
                    FROM 
                         tblLedger
                    WHERE transactionType = :transactionTypeDebit
                    AND LMRId = :LMRId
                    AND DATE(transactionDatetime) <= :date
                    GROUP BY debit_source_id
                    
                    UNION
                    
                    SELECT
                           -SUM(amount) AS amount
                    FROM tblLedger
                    WHERE transactionType = :transactionTypeCredit
                    AND LMRId = :LMRId
                    AND DATE(transactionDatetime) <= :date
                    GROUP BY credit_source_id
                        ) t100
                    
            ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'                 => $this->LMRId,
            'date'                  => $date,
            'transactionTypeDebit'  => LoanAmortization::LEDGER_TRANSACTION_TYPE_DRAW,
            'transactionTypeCredit' => LoanAmortization::LEDGER_TRANSACTION_TYPE_PAY_DOWN,
        ]);
        return $res[0]['amount'] ?? 0;
    }

    /**
     * @return tblPayment[]
     */
    public function getPaymentHistory(): array
    {
        return Database2::getInstance()->queryData('
            SELECT
                *
            FROM
                tblPayment
            WHERE
                LMRId = :LMRId
            ORDER BY
                paymentDate
        ', ['LMRId' => $this->getLMRId()], function ($row) {
            return new tblPayment($row);
        });
    }

    /**
     * @param int $InID
     * @param string|null $date
     * @return float
     */
    public function getInvestorTotal(int $InID, ?string $date): float
    {
        $sql = '
SELECT 
    SUM(t100.FundingAmount) AS invested
FROM 
    tblInvestorFunding t100
JOIN tblInvestorInfo t110 ON t110.InID = t100.InID
WHERE t110.LMRId = :LMRId
AND t110.InID = :InID
AND t100.FundingDate <= :date
            ';

//        Debug(Database2::getInstance()->safeQuery($sql, [
//            'LMRId' => $this->LMRId,
//            'InID' => $InID,
//            'date' => Dates::Datestamp($date),
//        ]));

        $res = Database2::getInstance()->queryData($sql, [
            'LMRId' => $this->LMRId,
            'InID'  => $InID,
            'date'  => Dates::Datestamp($date),
        ]);
        return $res[0]['invested'] ?? 0;
    }

    /**
     * @return array|tblInvestorHistory[]|void
     */
    public function getInvestorHistory(?string $PaymentDueDate = null)
    {
        $sql = '
SELECT 
    t100.*
FROM 
    tblInvestorHistory t100
JOIN tblInvestorInfo t110 ON t110.InID = t100.InID
WHERE t110.LMRId = :LMRId
AND IF(:dateApplied_check IS NULL, true, t100.dateApplied <= :dateApplied)

            ';
        $this->InvestorHistoryRecords = Database2::getInstance()->queryData($sql, [
            'LMRId'             => $this->LMRId,
            'dateApplied'       => $PaymentDueDate ? Dates::Datestamp($PaymentDueDate) : null,
            'dateApplied_check' => $PaymentDueDate ? Dates::Datestamp($PaymentDueDate) : null,
        ], function ($row) {
            return new tblInvestorHistory($row);
        });
        return $this->InvestorHistoryRecords;
    }

    /**
     * @return array|tblInvestorFunding[]|void
     */
    public function getInvestorFunding(?string $PaymentDueDate = null)
    {
        $sql = '
SELECT 
    t100.*
FROM 
    tblInvestorFunding t100
JOIN tblInvestorInfo t110 ON t110.InID = t100.InID
WHERE t110.LMRId = :LMRId
AND IF(:dateApplied_check IS NULL, true, t100.FundingDate <= :dateApplied)

            ';
        $this->InvestorHistoryRecords = Database2::getInstance()->queryData($sql, [
            'LMRId'             => $this->LMRId,
            'dateApplied'       => $PaymentDueDate ? Dates::Datestamp($PaymentDueDate) : null,
            'dateApplied_check' => $PaymentDueDate ? Dates::Datestamp($PaymentDueDate) : null,
        ], function ($row) {
            return new tblInvestorFunding($row);
        });
        return $this->InvestorHistoryRecords;
    }

    /**
     * @return tblEscrow[]|null
     */
    public function getEscrowRecords(): ?array
    {
        $sql = '
                SELECT
                    t100.*,
                    t110.amount AS _owed
                FROM
                    tblEscrow t100
                LEFT JOIN (
                    SELECT 
                    id, 
                           SUM(amount) AS amount
                    FROM (
                          
                    SELECT
                        debit_source_id AS id,
                           -SUM(amount) AS amount
                    FROM 
                         tblLedger
                    WHERE transactionType IN ( :transactionTypeA, :transactionTypeB )
                    GROUP BY debit_source_id
                    
                    UNION
                    
                    SELECT
                        credit_source_id AS id,
                           SUM(amount) AS amount
                    FROM tblLedger
                    WHERE transactionType IN ( :transactionTypeA, :transactionTypeB )
                    GROUP BY credit_source_id
                        ) t100
                    GROUP BY id
                    
                ) AS t110 ON t110.id = t100.tblEscrowId
                WHERE t100.LMRId = :LMRId
                ORDER BY t100.dueDate
            ';
        return Database2::getInstance()->queryData($sql, [
            'LMRId'            => $this->LMRId,
            'transactionTypeA' => LoanAmortization::LEDGER_TRANSACTION_TYPE_PAYMENT,
            'transactionTypeB' => LoanAmortization::LEDGER_TRANSACTION_TYPE_ESCROW,
        ], function ($row) {
            return new tblEscrow($row);
        });
    }

    /**
     * @return tblCharges[]|null
     */
    public function getChargesRecords(): ?array
    {
        $sql = '
            SELECT
              t100.*,
                   t110.amount AS _owed
            FROM
                tblCharges t100
            LEFT JOIN (
                SELECT 
                    id, 
                   SUM(amount) AS amount
               FROM (
                SELECT
                    debit_source_id AS id,
                       -SUM(amount) AS amount
                FROM 
                     tblLedger
                WHERE debit_source_name = :DebitSourceName
                GROUP BY debit_source_id
                
                UNION
                
                SELECT
                    credit_source_id AS id,
                       SUM(amount) AS amount
                FROM tblLedger
                WHERE credit_source_name = :CreditSourceName
                GROUP BY credit_source_id
                           ) t100
                GROUP BY id
                
            ) AS t110 ON t110.id = t100.tblChargesId
            WHERE t100.LMRId = :LMRId
            ORDER BY t100.dueDate
        ';
        return Database2::getInstance()->queryData($sql, [
            'LMRId'            => $this->LMRId,
            'DebitSourceName'  => LoanAmortization::LEDGER_CHARGES,
            'CreditSourceName' => LoanAmortization::LEDGER_CHARGES,
        ], function ($row) {
            return new tblCharges($row);
        });
    }

    /**
     * @param string|null $date
     * @return float
     */
    public function getCurrentLoanBalance(string $date = null): float
    {
        $sql = '
        SELECT
        SUM(alter_loan_balance) AS total
        FROM
tblLedger
WHERE
LMRId = :LMRId
AND IF(:date_check IS NULL, true, transactionDatetime <= :date)
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'      => $this->getLMRId(),
            'date_check' => $date ? Dates::Datestamp($date) : null,
            'date'       => Dates::Datestamp($date),
        ]);
        return round($res[0]['total'] ?? 0, 2);
    }

    /**
     * @return tblFile|null
     */
    public function getFile(): ?tblFile
    {
        $this->isInvalid = true;
        if (!$this->LMRId) {
            return null;
        }
        if (is_null($this->file)) {
            $this->file = tblFile::Get(['LMRId' => $this->LMRId]);
            if ($this->file) {
                $this->isInvalid = false;
            } else {
                $this->LMRId = null;
            }
        } else {
            $this->isInvalid = false;
        }
        return $this->file;
    }


    /**
     * @param string|null $PaymentDate
     * @return tblPayment
     */
    public function getLastPayment(?string $PaymentDate = null): tblPayment
    {
        // a payment must include principal and/or interest
        // per diem only payments screw up the calculations
        $sql = '
            SELECT
                *
            FROM
                tblPayment
            WHERE 
                LMRId = :LMRId
                AND principalInterest > 0
                ' . ($PaymentDate ? 'AND paymentDate < :paymentDate' : '') . '
            ORDER BY 
               paymentDate DESC
        ';
        /* @var tblPayment[] $res */
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'       => $this->LMRId,
            'paymentDate' => Dates::Datestamp($PaymentDate),
        ], function ($row) {
            return new tblPayment($row);
        });
        return $res[0] ?? new tblPayment();
    }

    /**
     * @param string|null $PaymentDueDate
     * @return tblLedger[]
     */
    public function getPrincipalAdjustments(?string $PaymentDueDate): array
    {
        $sql = '
            SELECT
                *
            FROM
                tblLedger
            WHERE 
                LMRId = :LMRId
            AND alter_loan_balance <> 0
            AND transactionDatetime <= :PaymentDueDate
            ORDER BY 
               transactionDatetime
        ';
        return Database2::getInstance()->queryData($sql, [
            'LMRId'          => $this->LMRId,
            'PaymentDueDate' => Dates::Datestamp($PaymentDueDate),
        ], function ($row) {
            return new tblLedger($row);
        });
    }

    /**
     * @param string|null $date
     * @return getVested[]
     */
    public function getVested(?string $date): array
    {
//SET @debit_source_name = 'investor%';
//SET @credit_source_name = 'investor%';
//SET @date = '2023-09-19';
//SET @LMRId = 7463955;


        $sql = APIHelper::getSQL(__DIR__ . '/LoanServicing/sql/ActiveInvestors.getVested.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'              => $this->LMRId,
            'debit_source_name'  => 'investor%',
            'credit_source_name' => 'investor%',
            'date'               => Dates::Datestamp($date),
        ]);
        $list = [];
        foreach ($res as $row) {
            $list[$row['InID']] = new getVested($row);
        }
        return $list;
    }

    /**
     * @param string|null $PaymentDueDate
     * @return array
     */
    public function getInvestorInterest(?string $PaymentDueDate): array
    {

        $total_interest = [];
        $total_investment = [];
        foreach ($this->getVested($PaymentDueDate) as $vestment) {
            $daily_rate = $vestment->Yield / 100.0 / 360.0;
            $periodic_rate = $daily_rate * 30;

            $months_days = Dates::GetMonthsDays($vestment->dateApplied, $PaymentDueDate);
            $months = $months_days['months'];
            $days = $months_days['days'];

            if (!$months && !$days) {
                $months = 1; // if they vest on the due date, they get a month of interest ?
            }

            if (!isset($total_interest[$vestment->InID])) {
                $total_interest[$vestment->InID] = 0;
            }

            if (!isset($total_investment[$vestment->InID])) {
                $total_investment[$vestment->InID] = 0;
            }

            $total_interest[$vestment->InID] += round($vestment->Amount * ($periodic_rate * $months + $daily_rate * $days), 2);
            $total_investment[$vestment->InID] += $vestment->Amount;
        }

        $sql = '
            SELECT
                credit_source_id,
                SUM(amount) AS total_interest
            FROM
                tblLedger
            WHERE 
                LMRId = :LMRId
                AND credit_source_name = :credit_source_name
                AND transactionDatetime < :PaymentDueDate
            GROUP BY
                credit_source_id
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'              => $this->getLMRId(),
            'credit_source_name' => LoanAmortization::LEDGER_INVESTOR_INTEREST,
            'PaymentDueDate'     => Dates::Datestamp($PaymentDueDate),
        ]);

        if (sizeof($res)) {
            foreach ($res as $item) {
                if (isset($total_interest[$item['credit_source_id']])) {
                    $total_interest[$item['credit_source_id']] -= $item['total_interest'];
                }
            }
        }
        return [
            'interest' => $total_interest,
            'invested' => $total_investment,
        ];
    }

    /**
     * @param string|null $PaymentDate
     * @param string|null $PaymentDueDate
     * @param float|null $given_interest_rate
     * @return float|int|void
     */
    public function getTotalInterestOwed(
        ?string $PaymentDate,
        ?string $PaymentDueDate = null,
        ?float  $given_interest_rate = null
    ): ?float
    {
        $amort = $this->getFile()->getAmortization();
        $term = 0;
        if (stristr(substr($amort, -5), 'years') !== false) {
            $term = intval(trim(str_ireplace('years', '', $amort)));
            $amort = 'Years';
        }


//        $log = [];

        switch ($amort) {
            case 'Years':
                $FirstPayment = strtotime(Dates::Datestamp($this->getFile()->getFirstPaymentDueDate()));
                $start = strtotime($PaymentDueDate ?? $this->getFile()->getFirstPaymentDueDate());
                $end = strtotime($PaymentDate);

                $total_interest = 0;
                while ($start <= $end) {
                    $interest_rate = ($given_interest_rate ?? $this->getFile()->getInterestRate($start)) / 100.0 / 12.0;
                    $i = $start == $FirstPayment ? 0 : 3600;
                    $loan_amount = $this->getCurrentLoanBalance($start - $i); // we need to go back an hour because the query includes "today" which we don't want here
                    $monthly_interest = round($loan_amount * (pow(1 + $interest_rate, 1) - 1), 2);
                    $total_interest += $monthly_interest;

                    $start = strtotime('+1 month', $start);
                }
                return $total_interest;

            case LoanTerms::INTEREST_ONLY:
                switch ($this->getFile()->getPaymentMethod()) {
                    case LoanTerms::TLA: // total loan amount
                        switch ($this->getFile()->getAccrualType()) {
                            case accrualTypes::ACCRUAL_TYPE_30_360: // most common
                            case accrualTypes::ACCRUAL_TYPE_30_365:
                                $daily_rate = $this->getFile()->getInterestRate(null) / 100.0 / $this->getFile()->getAccrualDays();

                                $periodic_rate = $daily_rate * 30;

                                $getOriginalBalance = $this->getCurrentLoanBalance($PaymentDate);

                                // don't use inclusive, because same date for TLA needs to be the first month owed, not the first day
                                $months_days = Dates::GetMonthsDays($this->getFile()->getFirstPaymentDueDate(), $PaymentDate);
                                $months = $months_days['months'] + 1;
                                $days = $months_days['days'];

                                if ($months < 0) {
                                    return 0;
                                }

                                $total = round($getOriginalBalance * $daily_rate * $days, 2);
                                for ($j = 0; $j < $months; $j++) {
                                    $total += round($getOriginalBalance * $periodic_rate, 2);
                                }

                                return $total;

                            case accrualTypes::ACCRUAL_TYPE_ACTUAL_360:
                            case accrualTypes::ACCRUAL_TYPE_ACTUAL_365:
                                $daily_rate = $this->getFile()->getInterestRate(null) / 100.0 / $this->getFile()->getAccrualDays();

                                $getOriginalBalance = $this->getFile()->getRehabCostFinanced() + $this->getFile()->getOriginalBalance();


                                $start = strtotime($this->getFile()->getFundingDate());
                                $months_days = Dates::GetMonthsDays($start, $PaymentDate);
                                $months = $months_days['months'];
                                $days = $months_days['days'];

                                if ($months < 0) {
                                    return 0;
                                }

                                $total = round($getOriginalBalance * $daily_rate * $days, 2);
                                for ($j = 1; $j <= $months; $j++) {
                                    $days = date('t', strtotime('+' . $j . ' months', $start));
                                    $periodic_rate = $daily_rate * $days;
                                    $total += round($getOriginalBalance * $periodic_rate, 2);
                                }

                                return $total;

                            default:
                                Debug('getTotalInterestOwed - Unknown Accrual Type:' . $this->getFile()->getAccrualType());
                        }
                        break;
                    case LoanTerms::ILA: // current loan amount
                        switch ($this->getFile()->getAccrualType()) {
                            case accrualTypes::ACCRUAL_TYPE_30_360: // most common
                            case accrualTypes::ACCRUAL_TYPE_30_365:
                                $daily_rate = $this->getFile()->getInterestRate(null) / 100.0 / $this->getFile()->getAccrualDays();

                                // Debug($daily_rate); // 0.00033333333333333

                                $periodic_rate = $daily_rate * 30; // 0.0099999999999999 x 12 = 12%

                                // every month of interest has to be rounded to calculate total interest owed over period of time
                                $principalAdjustments = $this->getPrincipalAdjustments($PaymentDate);
                                $report = [];
                                foreach ($principalAdjustments as $item) {

                                    $months_days = Dates::GetMonthsDays($item->transactionDatetime, $PaymentDate);
                                    $months = $months_days['months'];
                                    $days = $months_days['days'];

                                    if ($days > 30) {
                                        $days = 0;
                                        $months++;
                                    }

                                    $report[$item->transactionDatetime][] = [
                                        'months'              => $months,
                                        'days'                => $days,
                                        'amount'              => $item->alter_loan_balance,
                                        'months_days'         => $months_days,
                                        'transactionDateTime' => $item->transactionDatetime,
                                        'paymentDueDate'      => $PaymentDate,
                                        'interest_daily'      => $item->alter_loan_balance * $days * $daily_rate,
                                        'interest_monthly'    => $item->alter_loan_balance * $months * $periodic_rate,
                                    ];
                                }
                                $total_interest = 0;

                                foreach ($report as $items) {
                                    foreach ($items as $item) {
                                        $total_interest += round($item['interest_daily'] + $item['interest_monthly'], 2);
                                    }
                                }

                                // Debug($principalAdjustments, $report, $total_interest);

                                return $total_interest;

                            case accrualTypes::ACCRUAL_TYPE_ACTUAL_360:
                            case accrualTypes::ACCRUAL_TYPE_ACTUAL_365:
                                $daily_rate = $this->getFile()->getInterestRate(null) / 100.0 / $this->getFile()->getAccrualDays();

                                $principalAdjustments = $this->getPrincipalAdjustments($PaymentDate);
                                $total_interest = 0;
                                foreach ($principalAdjustments as $item) {

                                    $days = Dates::DaysDiff($item->transactionDatetime, $PaymentDate);
                                    $total_interest += $item->alter_loan_balance * ($daily_rate * $days);
                                }
                                return $total_interest;

                            default:
                                return 0;
                        }

                    default:
                        return 0;

                }
                break;

            default:
                return 0;
        }
    }

    /**
     * @param LoanServicing $loan
     * @param string|null $PaymentDueDate
     * @param string|null $PaymentDate
     * @return float
     */
    public function getDefaultInterestOwed(LoanServicing $loan, ?string $PaymentDueDate, ?string $PaymentDate): float
    {
        $amort = $loan->getFile()->getAmortization();
        $term = 0;
        if (stristr(substr($amort, -5), 'years') !== false) {
            $term = intval(trim(str_ireplace('years', '', $amort)));
            $amort = 'Years';
        }

        switch ($amort) {
            case 'Years':
                return $loan->getTotalInterestOwed($PaymentDate, $PaymentDueDate, $loan->getFile()->getDefaultInterestRate());

            case LoanTerms::INTEREST_ONLY:
                $defaultStartDate = Dates::Datestamp(strtotime('+ ' . $this->getFile()->getDefaultTriggeredByDays() . ' days', strtotime(Dates::Datestamp($PaymentDueDate))));

                $default_rate = 0;
                $regular_rate = 0;

                switch ($loan->getFile()->getAccrualType()) {
                    case accrualTypes::ACCRUAL_TYPE_30_360:
                    case accrualTypes::ACCRUAL_TYPE_30_365:

                        $daily_rate = $this->getFile()->getInterestRate(null) / 100.0 / $this->getFile()->getAccrualDays();
                        $periodic_rate = $daily_rate * 30;
                        $months_days = Dates::GetMonthsDays($PaymentDueDate, $defaultStartDate);
                        $months = $months_days['months'];
                        $days = $months_days['days'];
                        $regular_rate = ($periodic_rate * $months + $daily_rate * $days);


                        $default_daily_rate = $this->getFile()->getDefaultInterestRate() / 100.0 / $this->getFile()->getAccrualDays();
                        $default_periodic_rate = $default_daily_rate * 30;
                        $months_days = Dates::GetMonthsDays($defaultStartDate, $PaymentDate);
                        $months = $months_days['months'];
                        $days = $months_days['days'];
                        $default_rate = ($default_periodic_rate * $months + $default_daily_rate * $days);
                        break;

                    case accrualTypes::ACCRUAL_TYPE_ACTUAL_360:
                    case accrualTypes::ACCRUAL_TYPE_ACTUAL_365:
                        $daily_rate = $this->getFile()->getInterestRate(null) / 100.0 / $this->getFile()->getAccrualDays();
                        $days = Dates::DaysDiff($PaymentDueDate, $defaultStartDate);
                        $regular_rate = ($daily_rate * $days);


                        $default_daily_rate = $this->getFile()->getDefaultInterestRate() / 100.0 / $this->getFile()->getAccrualDays();
                        $days = Dates::DaysDiff($defaultStartDate, $PaymentDate);
                        $default_rate = ($default_daily_rate * $days);
                        break;

                    default:
                        Debug('getDefaultInterestOwed - Unknown Accrual Type:' . $this->getFile()->getAccrualType());
                }


                switch ($this->getFile()->getPaymentMethod()) {
                    case LoanTerms::TLA: // Total Loan Balance
                        switch ($this->getFile()->getAccrualType()) {
                            case accrualTypes::ACCRUAL_TYPE_30_360: // most common
                            case accrualTypes::ACCRUAL_TYPE_30_365:
                            case accrualTypes::ACCRUAL_TYPE_ACTUAL_360:
                            case accrualTypes::ACCRUAL_TYPE_ACTUAL_365:

                                $getOriginalBalance = $this->getFile()->getOriginalBalance();
                                return round($getOriginalBalance * ($default_rate + $regular_rate), 2);

                            default:
                                Debug('getDefaultInterestOwed - Unknown Accrual Type:' . $this->getFile()->getAccrualType());
                        }
                        break;

                    case LoanTerms::ILA: // Current Loan Balance
                        switch ($this->getFile()->getAccrualType()) {
                            case accrualTypes::ACCRUAL_TYPE_30_360: // most common
                            case accrualTypes::ACCRUAL_TYPE_30_365:
                            case accrualTypes::ACCRUAL_TYPE_ACTUAL_360:
                            case accrualTypes::ACCRUAL_TYPE_ACTUAL_365:
                                $principalAdjustments = $this->getPrincipalAdjustments($PaymentDate);
                                $total_interest = 0;
                                foreach ($principalAdjustments as $item) {
                                    $total_interest += round($item->alter_loan_balance * ($default_rate + $regular_rate), 2);
                                }
                                return $total_interest;

                            default:
                                return 0;
                        }

                    default:
                        return 0;

                }
                break;

            default:
                return 0;
        }
        return 0;
    }

    /**
     * @param string|null $date
     * @return float
     */
    public function getTotalInterestPayments(?string $date): float
    {
        $date = !$date ? null : Dates::Datestamp($date);


        $sql = '
SELECT
    SUM(amount) AS total
FROM
    tblLedger
WHERE
    debit_source_name IN ( 
                          :debit_source_name
                          ,:debit_source_name_alt 
                         )
    AND credit_source_name = :credit_source_name
    AND LMRId = :LMRId
    AND IF(:date IS NULL, true, DATE(transactionDatetime) < :date)
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'                 => $this->LMRId,
            'debit_source_name'     => LoanAmortization::LEDGER_REGULAR_PAYMENT,
            'debit_source_name_alt' => LoanAmortization::LEDGER_ESCROW_PREPAID_INTEREST,
            'credit_source_name'    => LoanAmortization::LEDGER_INTEREST,
            'date'                  => $date,
        ]);
        $total = 0;
        foreach ($res as $row) {
            $total += floatval($row['total']);
        }
        return round($total, 2);
    }

    /**
     * @return float
     */
    public function getTotalPayments(): float
    {
        $sql = '
SELECT
    SUM(amount) AS total
FROM
    tblLedger
WHERE
    transactionType = :transactionType
    AND credit_source_name IN (
                               :credit_source_name_a
                               , :credit_source_name_b
    )
    AND LMRId = :LMRId
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'transactionType'      => LoanAmortization::LEDGER_TRANSACTION_TYPE_PAYMENT,
            'credit_source_name_a' => LoanAmortization::LEDGER_INTEREST,
            'credit_source_name_b' => LoanAmortization::LEDGER_PRINCIPAL,
            'LMRId'                => $this->LMRId,
        ]);
        $total = 0;
        foreach ($res as $row) {
            $total += floatval($row['total']);
        }
        return round($total, 2);
    }

    /**
     * @param string $PaymentDate
     * @return float
     */
    public function getTotalEscrowOwed(string $PaymentDate): float
    {
        $total = 0;
        foreach ($this->getEscrowRecords() as $item) {
            $total += $item->getMonthlyPayment($PaymentDate);
        }
        return round($total, 2);
    }

    /**
     * @return string|null
     */
    public function getNextEscrowDueDate(): ?string
    {
        $sql = '
SELECT MIN(dueDate) AS dueDate
FROM (SELECT t100.dueDate
             -- , t110.amount AS owed
      FROM tblEscrow t100
               JOIN (SELECT id,
                            SUM(amount) AS amount
                     FROM (SELECT debit_source_id AS id,
                                  amount          AS amount
                           FROM tblLedger
                           WHERE debit_source_name LIKE \'escrow_%\'
                             AND LMRId = :LMRId

                           UNION

                           SELECT credit_source_id AS id,
                                  -amount          AS amount
                           FROM tblLedger
                           WHERE credit_source_name LIKE \'escrow_%\'
                             AND LMRId = :LMRId) t100
                     GROUP BY id) AS t110 ON t110.id = t100.tblEscrowId
      WHERE t100.LMRId = :LMRId
        AND t110.amount <> 0) t100


        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId' => $this->LMRId
        ]);
        $dueDate = null;
        foreach ($res as $row) {
            $dueDate = $row['dueDate'];
        }
        return $dueDate;
    }

    /**
     * @return string|null
     */
    public function getLastEscrowDueDate(): ?string
    {
        $sql = '
SELECT MAX(dueDate) AS dueDate
FROM (SELECT t100.dueDate
             -- , t110.amount AS owed
      FROM tblEscrow t100
               JOIN (SELECT id,
                            SUM(amount) AS amount
                     FROM (SELECT debit_source_id AS id,
                                  amount          AS amount
                           FROM tblLedger
                           WHERE debit_source_name LIKE \'escrow_%\'
                             AND LMRId = :LMRId

                           UNION

                           SELECT credit_source_id AS id,
                                  -amount          AS amount
                           FROM tblLedger
                           WHERE credit_source_name LIKE \'escrow_%\'
                             AND LMRId = :LMRId) t100
                     GROUP BY id) AS t110 ON t110.id = t100.tblEscrowId
      WHERE t100.LMRId = :LMRId
        AND t110.amount <> 0) t100


        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId' => $this->LMRId
        ]);
        $dueDate = null;
        foreach ($res as $row) {
            $dueDate = $row['dueDate'];
        }
        return $dueDate;
    }

    /**
     * @return float
     */
    public function getEscrowBalance(): float
    {
//        $l->debit_source_name = 'Borrower';
//        $l->debit_source_id = null;
//        $l->credit_source_name = 'Escrow';

        $sql = '
SELECT SUM(total) AS total
FROM (SELECT amount AS total
      FROM tblLedger
      WHERE LMRId = :LMRId
        AND debit_source_name = \'Borrower\'
        AND credit_source_name LIKE \'escrow_%\'
        AND credit_source_name NOT LIKE \'escrow_prepaid_%\'

      UNION

      SELECT -amount AS total
      FROM tblLedger
      WHERE LMRId = :LMRId
        AND credit_source_name LIKE \'escrow_%\'
        AND credit_source_name NOT LIKE \'escrow_prepaid_%\'
        AND credit_source_id IS NOT NULL
        AND debit_source_name LIKE \'escrow_%\'
        AND debit_source_name NOT LIKE \'escrow_prepaid_%\'
      ) t100
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId' => $this->LMRId
        ]);
        $total = 0;
        foreach ($res as $row) {
            $total += floatval($row['total']);
        }
        return round($total, 2);
    }

    /**
     * @return float
     */
    public function getTotalEscrowPaid(): float
    {
//        $l->debit_source_name = 'Borrower';
//        $l->debit_source_id = null;
//        $l->credit_source_name = 'Escrow';

        $sql = '
SELECT
SUM(amount) AS total
FROM
tblLedger
WHERE
LMRId = :LMRId     
AND credit_source_name = \'escrow_prepaid_escrow\'
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId' => $this->LMRId
        ]);
        $total = 0;
        foreach ($res as $row) {
            $total += floatval($row['total']);
        }
        return round($total, 2);
    }

    /**
     * @return tblCharges[]|null
     */
    public function getChargesOwed(?string $dueDate): ?array
    { // 6214303

        // don't use cache for this - 3/31/2022
        $sql = '
        SELECT
            t100.*,
            t110.amount AS _owed
        FROM
            tblCharges t100
        LEFT JOIN (
                SELECT 
                    id, 
                   SUM(amount) AS amount
               FROM (
                SELECT
                    debit_source_id AS id,
                       -SUM(amount) AS amount
                FROM 
                     tblLedger
                WHERE debit_source_name = :DebitSourcesName
                GROUP BY debit_source_id
                
                UNION
                
                SELECT
                    credit_source_id AS id,
                       SUM(amount) AS amount
                FROM tblLedger
                WHERE credit_source_name = :CreditSourcesName
                GROUP BY credit_source_id
                           ) t100
                GROUP BY id
        ) t110 ON t110.id = t100.tblChargesId
        WHERE t100.LMRId = :LMRId
        AND t110.amount <> 0
        AND t100.dueDate <= :dueDate
        ORDER BY t100.dueDate
    ';
        $this->ChargesOwed = Database2::getInstance()->queryData($sql, [
            'LMRId'             => $this->LMRId,
            'DebitSourcesName'  => LoanAmortization::LEDGER_CHARGES,
            'CreditSourcesName' => LoanAmortization::LEDGER_CHARGES,
            'dueDate'           => Dates::Datestamp($dueDate),
        ], function ($row) {
            return new tblCharges($row);
        });
        return $this->ChargesOwed;
    }

    /**
     * @param string|null $PaymentDate
     * @return float
     */
    public function getTotalChargesOwed(?string $PaymentDate): float
    {
        $sql = '
SELECT
SUM(amount) AS total
FROM tblLedger
WHERE credit_source_name = :CreditSourceName
AND LMRId = :LMRId
AND transactionDatetime <= :transactionDatetime
        ';
        $res = Database2::getInstance()->queryData($sql, [
            'LMRId'               => $this->LMRId,
            'CreditSourceName'    => LoanAmortization::LEDGER_CHARGES,
            'transactionDatetime' => Dates::Datestamp($PaymentDate),

        ]);
        return round($res[0]['total'] ?? 0, 2);
    }

    /**
     * @param string|null $PaymentDate
     * @return float
     */
    public function getTotalChargesPaid(?string $PaymentDate): float
    {
        $sql = '
SELECT
SUM(amount) AS total
FROM tblLedger
WHERE debit_source_name = :DebitSourceName
AND LMRId = :LMRId
AND transactionDatetime <= :transactionDatetime
        ';
        $params = [
            'LMRId'               => $this->LMRId,
            'DebitSourceName'     => LoanAmortization::LEDGER_CHARGES,
            'transactionDatetime' => Dates::Datestamp($PaymentDate),
        ];
        $res = Database2::getInstance()->queryData($sql, $params);
        return round($res[0]['total'] ?? 0, 2);
    }

    /**
     * @param float|null $prepaid_escrow_amount
     * @param float|null $escrow_cushion_amount
     * @param float|null $annual_property_taxes
     * @param string|null $annual_property_taxes_paid_date
     * @param float|null $annual_property_taxes_paid_amount
     * @param float|null $annual_insurance_premium
     * @param string|null $annual_insurance_premium_paid_date
     * @param float|null $annual_insurance_premium_paid_amount
     * @return array|null
     */
    public function saveEscrowSettings(
        ?float  $prepaid_escrow_amount,
        ?float  $escrow_cushion_amount,
        ?float  $annual_property_taxes,
        ?string $annual_property_taxes_paid_date,
        ?float  $annual_property_taxes_paid_amount,
        ?float  $annual_insurance_premium,
        ?string $annual_insurance_premium_paid_date,
        ?float  $annual_insurance_premium_paid_amount
    ): ?array
    {
        $TblFileHMLONewLoanInfo = $this->getFile()->getTblFileHMLONewLoanInfo_by_fileID();
        if (!$TblFileHMLONewLoanInfo) {
            $TblFileHMLONewLoanInfo = new tblFileHMLONewLoanInfo();
            $TblFileHMLONewLoanInfo->fileID = $this->getLMRId();
        }
        $TblFileHMLONewLoanInfo->prepaidEscrow = Strings::Numeric($prepaid_escrow_amount);
        $TblFileHMLONewLoanInfo->escrowCushion = Strings::Numeric($escrow_cushion_amount);

        $TblFileHMLONewLoanInfo->Save();

        $TblIncomeInfo = $this->getFile()->getTblIncomeInfo_by_LMRId();
        if (!$TblIncomeInfo) {
            $TblIncomeInfo = new tblIncomeInfo();
            $TblIncomeInfo->LMRId = $this->getLMRId();
        }

        $TblIncomeInfo->taxes1 = Strings::Numeric($annual_property_taxes);
        $TblIncomeInfo->taxes1LastAmount = Strings::Numeric($annual_property_taxes_paid_amount);
        $TblIncomeInfo->taxes1LastPaid = Dates::Datestamp($annual_property_taxes_paid_date, '');

        $TblIncomeInfo->Save();

        $TblFileHMLOPropInfo = $this->getFile()->getTblFileHMLOPropInfo_by_fileID();
        if (!$TblFileHMLOPropInfo) {
            $TblFileHMLOPropInfo = new tblFileHMLOPropInfo();
            $TblFileHMLOPropInfo->fileID = $this->getLMRId();
        }

        $TblFileHMLOPropInfo->annualPremium = Strings::Numeric($annual_insurance_premium);
        $TblFileHMLOPropInfo->annualPremiumLastPaid = Dates::Datestamp($annual_insurance_premium_paid_date, '');
        $TblFileHMLOPropInfo->annualPremiumLastAmount = Strings::Numeric($annual_insurance_premium_paid_amount);

        $TblFileHMLOPropInfo->Save();

        return null;
    }
}
