<?php

namespace models\composite\oFileUpdate2;


use models\Database2;
use models\composite\oFileUpdate\saveFileNotesWhenSaleDateChange;
use models\lendingwise\db\tblFile_db;
use models\lendingwise\db\tblProposalInfo_db;
use models\lendingwise\db\tblQAInfo_db;
use models\lendingwise\db\tblShortSale_db;
use models\lendingwise\tblFile;
use models\lendingwise\tblProposalInfo;
use models\lendingwise\tblQAInfo;
use models\lendingwise\tblShortSale;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\storedProc\SP_RecordFileTabUpdate;
use models\types\strongType;
use pages\backoffice\loan\hmda\classes\HMDAController;

/**
 *
 */
class saveQAInfo extends strongType
{
    /**
     * @param $ip
     * @return int
     */
    public static function getReport($ip): int
    {
        $rentPayment = '';
        $propertyForRent = '';
        $monthlyRentAmt = '';
        $summonDate = null;
        $isHomeownerServed = '';
        $noOfProperties = '';
        $noOfMortgages = '';
        $bankruptcyDischargeDate = '';
        $leaseExpiresDate = '';
        $coBorrowerConvicted = 0;
        $repaymentOrMortAssistance = '';
        $monthlyLastPaidAmt = '';
        $delinquentOnPropTax = '';
        $delinquentTaxYear = '';
        $delinquentTaxAmount = '';
        $delinquencySavedAmount = '';
        $attorneyFax = '';
        $attorneyFirmName = '';
        $attorneyName = '';
        $attorneyPhone = '';
        $isPaymentsPlanBehind = '';
        $bankruptcyDispositionStatus = '';
        $offerPropertyAgencyName = '';
        $doneAppraisal = '';
        $appraisalListValue = '';
        $BGender = 0;
        $BVeteran = 0;
        $bFiEthnicity = '';
        $bFiEthnicitySub = '';
        $bFiEthnicitySubOther = '';
        $bFiSex = '';
        $bFiRace = '';
        $bFiRaceSub = '';
        $bFiRaceAsianOther = '';
        $bFiRacePacificOther = '';
        $bDemoInfo = '';
        $listingPrice = '';
        $propertyListedDate = '';
        $saleForHowLong = '';
        $OPAPhone = '';
        $attorneyEmail = '';
        $PublishBInfo = 0;
        $BEthnicity = 0;
        $BRace = 0;
        $PublishCBInfo = 0;
        $CBEthnicity = 0;
        $CBEthnicitySub = '';
        $CBEthnicitySubOther = '';
        $CBRace = 0;
        $CBRaceSub = '';
        $CBRaceAsianOther = '';
        $CBRacePacificOther = '';
        $CBGender = 0;
        $CBVeteran = '';
        $CBFiEthnicity = '';
        $CBFiGender = '';
        $CBFiRace = '';
        $CBDDemoInfo = '';
        $creditCounselorAgency = '';
        $attorneyNum = '';
        $policyNumber = '';
        $attorneyAddress = '';
        $attorneyState = '';
        $attorneyZip = '';
        $attorneyCity = '';
        $noticeReceivedDate = '';
        $dateOfOffer = '';
        $bankruptcyFilingDate = '';
        $LMRId = 0;
        $jurisDiction = '';
        $indexNo = '';
        $QAID = 0;
        $appliedModification = 'No';
        $appliedModificationDate = '';
        $serviceMemberDeceased = '';
        $receivedModificationDate = '';
        $repaymentOrMortAssistanceDate = '';
        $serviceMemberServing = '';
        $notOnMortgageNote = '';
        $borrowerNatural = '';
        $occupiedBy = '';
        $loanModType = '';
        $HAMPTier1Payment = '';
        $loanModPlanStanding = 0;
        $creditCounselStartedDate = '';
        $creditCounselFinisheddDate = '';
        $tabOpt = '';
        $serviceMemberSurvivor = '';
        $isMtgReaffirmed = '';

        $stateHFAEntity = '';
        $stateHFAContactName = '';
        $stateHFAPhone = '';
        $stateHFAEmailAddress = '';
        $hazardPolicyCurrent = '';

        $isInsuranceDelinquent = '';
        $delinquentInsuranceAmount = '';
        $isTaxReturn = 0;
        $QABorName = '';
        $QABorSpouseName = '';
        $QABorSpouseSSN = '';
        $QACoBorName = '';
        $QACoBorSSN = '';
        $LMRInfo = [];
        $shortSaleInfo = [];
        $proposalInfo = [];

        $receiveNotice = '';
        $loanSalesDate = '';
        $receiveModification = '';
        $mortgageLates = '';
        $checkingAndSavings = '';
        $noOfPeopleInProperty = '';
        $receiveModificationNotes = '';
        $PID = 0;
        $SSID = 0;
        $leaseStartedDate = '';
        $QACoBorSpouseSSN = '';
        $closingDate = '';
        $noOfvehiclesOwned = '';
        $opt = '';
        $QACoBorSpouseName = '';
        $QABorSSN = '';
        $YRF4506TDate1 = '';
        $YRF4506TDate2 = '';
        $YRF4506TDate3 = '';
        $YRF4506TDate4 = '';

        $missedMonthOfMortPay = '';

        if (array_key_exists('LMRId', $ip)) $LMRId = $ip['LMRId'];
        if (array_key_exists('QAInfo', $ip)) $QAInfoArray = $ip['QAInfo'];
        if (array_key_exists('tabOpt', $ip)) $tabOpt = trim($ip['tabOpt']);
        if (array_key_exists('LMRInfo', $ip)) $LMRInfo = $ip['LMRInfo'];
        if (array_key_exists('shortSaleInfo', $ip)) $shortSaleInfo = $ip['shortSaleInfo'];
        if (array_key_exists('proposalInfo', $ip)) $proposalInfo = $ip['proposalInfo'];

        if (array_key_exists('receiveNotice', $LMRInfo)) $receiveNotice = $LMRInfo['receiveNotice'];
        if (array_key_exists('loanSalesDate', $LMRInfo)) $loanSalesDate = $LMRInfo['loanSalesDate'];
        if (array_key_exists('receiveModification', $LMRInfo)) $receiveModification = $LMRInfo['receiveModification'];
        if (array_key_exists('receiveModificationNotes', $LMRInfo)) $receiveModificationNotes = $LMRInfo['receiveModificationNotes'];
        if (array_key_exists('mortgageLates', $LMRInfo)) $mortgageLates = $LMRInfo['mortgageLates'];
        if (array_key_exists('checkingAndSavings', $LMRInfo)) $checkingAndSavings = $LMRInfo['checkingAndSavings'];
        if (array_key_exists('noOfPeopleInProperty', $LMRInfo)) $noOfPeopleInProperty = $LMRInfo['noOfPeopleInProperty'];
        if (array_key_exists('missedMonthOfMortPay', $LMRInfo)) $missedMonthOfMortPay = $LMRInfo['missedMonthOfMortPay'];

        if (trim($noOfPeopleInProperty) == '') $noOfPeopleInProperty = 0;
        if (Dates::IsEmpty($loanSalesDate)) {
            $loanSalesDate = null;
        } else {
            $loanSalesDate = Dates::formatDateWithRE($loanSalesDate, 'MDY', 'Y-m-d');
            if (trim($loanSalesDate) == '') {
                $loanSalesDate = null;
            }
        }

        if (array_key_exists('opt', $LMRInfo)) $opt = $LMRInfo['opt'];

        if (array_key_exists('noOfPeopleDependent', $QAInfoArray)) $noOfPeopleDependent = $QAInfoArray['noOfPeopleDependent'];
        if (array_key_exists('stayInHome', $QAInfoArray)) $stayInHome = $QAInfoArray['stayInHome'];
        if (array_key_exists('homeListedForSale', $QAInfoArray)) $homeListedForSale = $QAInfoArray['homeListedForSale'];
        if (array_key_exists('seriousNeedRepair', $QAInfoArray)) $seriousNeedRepair = $QAInfoArray['seriousNeedRepair'];
        if (array_key_exists('repairExplanation', $QAInfoArray)) $repairExplanation = $QAInfoArray['repairExplanation'];
        if (array_key_exists('homeMortgageClosed', $QAInfoArray)) $homeMortgageClosed = $QAInfoArray['homeMortgageClosed'];
        if (array_key_exists('employmentExplanation', $QAInfoArray)) $employmentExplanation = $QAInfoArray['employmentExplanation'];
        if (array_key_exists('moreThanOneProperty', $QAInfoArray)) $moreThanOneProperty = $QAInfoArray['moreThanOneProperty'];
        if (array_key_exists('propertyExlpanation', $QAInfoArray)) $propertyExlpanation = $QAInfoArray['propertyExlpanation'];
        if (array_key_exists('bankruptcy', $QAInfoArray)) $bankruptcy = $QAInfoArray['bankruptcy'];
        if (array_key_exists('bankruptcyExlpanation', $QAInfoArray)) $bankruptcyExlpanation = $QAInfoArray['bankruptcyExlpanation'];
        if (array_key_exists('creditCounselling', $QAInfoArray)) $creditCounselling = $QAInfoArray['creditCounselling'];
        if (array_key_exists('propertyIncludedBankruptcy', $QAInfoArray)) $propertyIncludedBankruptcy = $QAInfoArray['propertyIncludedBankruptcy'];
        if (array_key_exists('thirtyDaysLate', $QAInfoArray)) $thirtyDaysLate = $QAInfoArray['thirtyDaysLate'];
        if (array_key_exists('foreClosureNotes', $QAInfoArray)) $foreClosureNotes = $QAInfoArray['foreClosureNotes'];
        if (array_key_exists('noticeReceivedDate', $QAInfoArray)) $noticeReceivedDate = $QAInfoArray['noticeReceivedDate'];
        if (array_key_exists('saleByOwner', $QAInfoArray)) $saleByOwner = $QAInfoArray['saleByOwner'];
        if (array_key_exists('receiveOfferOnProperty', $QAInfoArray)) $receiveOfferOnProperty = $QAInfoArray['receiveOfferOnProperty'];
        if (array_key_exists('dateOfOffer', $QAInfoArray)) $dateOfOffer = $QAInfoArray['dateOfOffer'];
        if (array_key_exists('amountOfOffer', $QAInfoArray)) $amountOfOffer = $QAInfoArray['amountOfOffer'];
        if (array_key_exists('RETBillPayer', $QAInfoArray)) $RETBillPayer = $QAInfoArray['RETBillPayer'];
        if (array_key_exists('taxesCurrent', $QAInfoArray)) $taxesCurrent = $QAInfoArray['taxesCurrent'];
        if (array_key_exists('hazardInsurancePolicy', $QAInfoArray)) $hazardInsurancePolicy = $QAInfoArray['hazardInsurancePolicy'];
        if (array_key_exists('policyCurrent', $QAInfoArray)) $policyCurrent = $QAInfoArray['policyCurrent'];
        if (array_key_exists('insuranceCompName', $QAInfoArray)) $insuranceCompName = $QAInfoArray['insuranceCompName'];
        if (array_key_exists('insuranceCompPhone', $QAInfoArray)) $insuranceCompPhone = $QAInfoArray['insuranceCompPhone'];
        if (array_key_exists('filedBankruptcy', $QAInfoArray)) $filedBankruptcy = $QAInfoArray['filedBankruptcy'];
        if (array_key_exists('bankruptcyChapter', $QAInfoArray)) $bankruptcyChapter = $QAInfoArray['bankruptcyChapter'];
        if (array_key_exists('bankruptcyFilingDate', $QAInfoArray)) $bankruptcyFilingDate = $QAInfoArray['bankruptcyFilingDate'];
        if (array_key_exists('bankruptcyDischarged', $QAInfoArray)) $bankruptcyDischarged = $QAInfoArray['bankruptcyDischarged'];
        if (array_key_exists('bankruptcyCaseNumb', $QAInfoArray)) $bankruptcyCaseNumb = $QAInfoArray['bankruptcyCaseNumb'];
        if (array_key_exists('creditCounselorName', $QAInfoArray)) $creditCounselorName = $QAInfoArray['creditCounselorName'];
        if (array_key_exists('creditCounselorPhone', $QAInfoArray)) $creditCounselorPhone = $QAInfoArray['creditCounselorPhone'];
        if (array_key_exists('creditCounselorEmail', $QAInfoArray)) $creditCounselorEmail = $QAInfoArray['creditCounselorEmail'];
        if (array_key_exists('serviceMemberServing', $QAInfoArray)) $serviceMemberServing = $QAInfoArray['serviceMemberServing'];

        if (array_key_exists('isMtgReaffirmed', $QAInfoArray)) $isMtgReaffirmed = $QAInfoArray['isMtgReaffirmed'];
        if (array_key_exists('isMtgReaffirmed', $QAInfoArray)) $isMtgReaffirmed = $QAInfoArray['isMtgReaffirmed'];
        if (array_key_exists('isInsuranceDelinquent', $QAInfoArray)) $isInsuranceDelinquent = $QAInfoArray['isInsuranceDelinquent'];
        if (array_key_exists('delinquentInsuranceAmount', $QAInfoArray)) $delinquentInsuranceAmount = $QAInfoArray['delinquentInsuranceAmount'];

        if (array_key_exists('summonDate', $QAInfoArray)) $summonDate = $QAInfoArray['summonDate'];
        if (array_key_exists('rentPayment', $QAInfoArray)) $rentPayment = $QAInfoArray['rentPayment'];
        if (array_key_exists('propertyForRent', $QAInfoArray)) $propertyForRent = $QAInfoArray['propertyForRent'];
        if (array_key_exists('monthlyRentAmt', $QAInfoArray)) $monthlyRentAmt = $QAInfoArray['monthlyRentAmt'];
        if (array_key_exists('monthlyLastPaidAmt', $QAInfoArray)) $monthlyLastPaidAmt = $QAInfoArray['monthlyLastPaidAmt'];
        if (array_key_exists('leaseExpiresDate', $QAInfoArray)) $leaseExpiresDate = $QAInfoArray['leaseExpiresDate'];
        if (array_key_exists('leaseStartedDate', $QAInfoArray)) $leaseStartedDate = $QAInfoArray['leaseStartedDate'];


        if (array_key_exists('isHomeownerServed', $QAInfoArray)) $isHomeownerServed = $QAInfoArray['isHomeownerServed'];
        if (array_key_exists('noOfProperties', $QAInfoArray)) $noOfProperties = $QAInfoArray['noOfProperties'];
        if (array_key_exists('noOfMortgages', $QAInfoArray)) $noOfMortgages = $QAInfoArray['noOfMortgages'];
        if (array_key_exists('bankruptcyDischargeDate', $QAInfoArray)) $bankruptcyDischargeDate = $QAInfoArray['bankruptcyDischargeDate'];
        if (array_key_exists('creditCounselStartedDate', $QAInfoArray)) $creditCounselStartedDate = $QAInfoArray['creditCounselStartedDate'];
        if (array_key_exists('creditCounselFinisheddDate', $QAInfoArray)) $creditCounselFinisheddDate = $QAInfoArray['creditCounselFinisheddDate'];

        if (array_key_exists('borrowerConvicted', $QAInfoArray)) $borrowerConvicted = $QAInfoArray['borrowerConvicted'];
        if (array_key_exists('coBorrowerConvicted', $QAInfoArray)) $coBorrowerConvicted = $QAInfoArray['coBorrowerConvicted'];
        if (array_key_exists('repaymentOrMortAssistance', $QAInfoArray)) $repaymentOrMortAssistance = $QAInfoArray['repaymentOrMortAssistance'];
        if (array_key_exists('delinquentOnPropTax', $QAInfoArray)) $delinquentOnPropTax = $QAInfoArray['delinquentOnPropTax'];
        if (array_key_exists('delinquentTaxYear', $QAInfoArray)) $delinquentTaxYear = $QAInfoArray['delinquentTaxYear'];
        if (array_key_exists('delinquentTaxAmount', $QAInfoArray)) $delinquentTaxAmount = $QAInfoArray['delinquentTaxAmount'];
        if (array_key_exists('delinquencySavedAmount', $QAInfoArray)) $delinquencySavedAmount = $QAInfoArray['delinquencySavedAmount'];
        if (array_key_exists('attorneyFirmName', $QAInfoArray)) $attorneyFirmName = $QAInfoArray['attorneyFirmName'];
        if (array_key_exists('attorneyName', $QAInfoArray)) $attorneyName = $QAInfoArray['attorneyName'];
        if (array_key_exists('attorneyPhone', $QAInfoArray)) $attorneyPhone = $QAInfoArray['attorneyPhone'];
        if (array_key_exists('attorneyFax', $QAInfoArray)) $attorneyFax = $QAInfoArray['attorneyFax'];
        if (array_key_exists('attorneyCell', $QAInfoArray)) $attorneyCell = $QAInfoArray['attorneyCell'];

        if (array_key_exists('isPaymentsPlanBehind', $QAInfoArray)) $isPaymentsPlanBehind = $QAInfoArray['isPaymentsPlanBehind'];
        if (array_key_exists('bankruptcyDispositionStatus', $QAInfoArray)) $bankruptcyDispositionStatus = $QAInfoArray['bankruptcyDispositionStatus'];
        if (array_key_exists('doneAppraisal', $QAInfoArray)) $doneAppraisal = $QAInfoArray['doneAppraisal'];
        if (array_key_exists('appraisalListValue', $QAInfoArray)) $appraisalListValue = $QAInfoArray['appraisalListValue'];
        if (array_key_exists('offerPropertyAgencyName', $QAInfoArray)) $offerPropertyAgencyName = $QAInfoArray['offerPropertyAgencyName'];
        if (array_key_exists('listingPrice', $QAInfoArray)) $listingPrice = $QAInfoArray['listingPrice'];
        if (array_key_exists('propertyListedDate', $QAInfoArray)) $propertyListedDate = $QAInfoArray['propertyListedDate'];
        if (array_key_exists('saleForHowLong', $QAInfoArray)) $saleForHowLong = $QAInfoArray['saleForHowLong'];
        if (array_key_exists('OPAPhone', $QAInfoArray)) $OPAPhone = $QAInfoArray['OPAPhone'];
        if (array_key_exists('attorneyEmail', $QAInfoArray)) $attorneyEmail = $QAInfoArray['attorneyEmail'];
        if (array_key_exists('PublishBInfo', $QAInfoArray)) $PublishBInfo = $QAInfoArray['PublishBInfo'];
        if (array_key_exists('BEthnicity', $QAInfoArray)) $BEthnicity = $QAInfoArray['BEthnicity'] ?? 0;
        if (array_key_exists('BRace', $QAInfoArray)) $BRace = $QAInfoArray['BRace'];
        if (array_key_exists('BGender', $QAInfoArray)) $BGender = intval($QAInfoArray['BGender']);
        if (array_key_exists('BVeteran', $QAInfoArray)) $BVeteran = intval($QAInfoArray['BVeteran']);
        if (array_key_exists('bFiEthnicity', $QAInfoArray)) $bFiEthnicity = $QAInfoArray['bFiEthnicity'];
        if (array_key_exists('bFiEthnicitySub', $QAInfoArray)) $bFiEthnicitySub = $QAInfoArray['bFiEthnicitySub'];
        if (array_key_exists('bFiEthnicitySubOther', $QAInfoArray)) $bFiEthnicitySubOther = $QAInfoArray['bFiEthnicitySubOther'];
        if (array_key_exists('bFiSex', $QAInfoArray)) $bFiSex = $QAInfoArray['bFiSex'];
        if (array_key_exists('bFiRace', $QAInfoArray)) $bFiRace = $QAInfoArray['bFiRace'];
        if (array_key_exists('bFiRaceSub', $QAInfoArray)) $bFiRaceSub = $QAInfoArray['bFiRaceSub'];
        if (array_key_exists('bFiRaceAsianOther', $QAInfoArray)) $bFiRaceAsianOther = $QAInfoArray['bFiRaceAsianOther'];
        if (array_key_exists('bFiRacePacificOther', $QAInfoArray)) $bFiRacePacificOther = $QAInfoArray['bFiRacePacificOther'];
        if (array_key_exists('bDemoInfo', $QAInfoArray)) $bDemoInfo = $QAInfoArray['bDemoInfo'];
        if (array_key_exists('PublishCBInfo', $QAInfoArray)) $PublishCBInfo = $QAInfoArray['PublishCBInfo'];
        if (array_key_exists('CBEthnicity', $QAInfoArray)) $CBEthnicity = $QAInfoArray['CBEthnicity'];
        if (array_key_exists('CBEthnicitySub', $QAInfoArray)) $CBEthnicitySub = $QAInfoArray['CBEthnicitySub'];
        if (array_key_exists('CBEthnicitySubOther', $QAInfoArray)) $CBEthnicitySubOther = $QAInfoArray['CBEthnicitySubOther'];
        if (array_key_exists('CBRace', $QAInfoArray)) $CBRace = $QAInfoArray['CBRace'];
        if (array_key_exists('CBRaceSub', $QAInfoArray)) $CBRaceSub = $QAInfoArray['CBRaceSub'];
        if (array_key_exists('CBRaceAsianOther', $QAInfoArray)) $CBRaceAsianOther = $QAInfoArray['CBRaceAsianOther'];
        if (array_key_exists('CBRacePacificOther', $QAInfoArray)) $CBRacePacificOther = $QAInfoArray['CBRacePacificOther'];
        if (array_key_exists('CBGender', $QAInfoArray)) $CBGender = $QAInfoArray['CBGender'];
        if (array_key_exists('CBVeteran', $QAInfoArray)) $CBVeteran = $QAInfoArray['CBVeteran'];
        if (array_key_exists('CBFiEthnicity', $QAInfoArray)) $CBFiEthnicity = $QAInfoArray['CBFiEthnicity'];
        if (array_key_exists('CBFiGender', $QAInfoArray)) $CBFiGender = $QAInfoArray['CBFiGender'];
        if (array_key_exists('CBFiRace', $QAInfoArray)) $CBFiRace = $QAInfoArray['CBFiRace'];
        if (array_key_exists('CBDDemoInfo', $QAInfoArray)) $CBDDemoInfo = $QAInfoArray['CBDDemoInfo'];
        if (array_key_exists('HAMPPrincipalResidence', $QAInfoArray)) $HAMPPrincipalResidence = $QAInfoArray['HAMPPrincipalResidence'];
        if (array_key_exists('permanentHAMPModification', $QAInfoArray)) $permanentHAMPModification = $QAInfoArray['permanentHAMPModification'];
        if (array_key_exists('howManyPermanentHAMP', $QAInfoArray)) $howManyPermanentHAMP = $QAInfoArray['howManyPermanentHAMP'];
        if (array_key_exists('HAMPTrialPrincipal', $QAInfoArray)) $HAMPTrialPrincipal = $QAInfoArray['HAMPTrialPrincipal'];
        if (array_key_exists('serviceMember', $QAInfoArray)) $serviceMember = $QAInfoArray['serviceMember'];
        if (array_key_exists('serviceMemberOrder', $QAInfoArray)) $serviceMemberOrder = $QAInfoArray['serviceMemberOrder'];
        if (array_key_exists('serviceMemberSurvivor', $QAInfoArray)) $serviceMemberSurvivor = $QAInfoArray['serviceMemberSurvivor'];

        if (array_key_exists('serviceMemberDeceased', $QAInfoArray)) $serviceMemberDeceased = $QAInfoArray['serviceMemberDeceased'];

        if (array_key_exists('hazardPolicyCurrent', $QAInfoArray)) $hazardPolicyCurrent = $QAInfoArray['hazardPolicyCurrent'];

        if (array_key_exists('creditCounselorAgency', $QAInfoArray)) $creditCounselorAgency = $QAInfoArray['creditCounselorAgency'];
        if (array_key_exists('policyNumber', $QAInfoArray)) $policyNumber = $QAInfoArray['policyNumber'];
        if (array_key_exists('attorneyAddress', $QAInfoArray)) $attorneyAddress = $QAInfoArray['attorneyAddress'];
        if (array_key_exists('attorneyCity', $QAInfoArray)) $attorneyCity = $QAInfoArray['attorneyCity'];
        if (array_key_exists('attorneyState', $QAInfoArray)) $attorneyState = $QAInfoArray['attorneyState'];
        if (array_key_exists('attorneyZip', $QAInfoArray)) $attorneyZip = $QAInfoArray['attorneyZip'];
        if (array_key_exists('attorneyNum', $QAInfoArray)) $attorneyNum = $QAInfoArray['attorneyNum'];
        if (array_key_exists('jurisDiction', $QAInfoArray)) $jurisDiction = $QAInfoArray['jurisDiction'];
        if (array_key_exists('indexNo', $QAInfoArray)) $indexNo = $QAInfoArray['indexNo'];

        if (array_key_exists('stateHFAEntity', $QAInfoArray)) $stateHFAEntity = $QAInfoArray['stateHFAEntity'];
        if (array_key_exists('stateHFAContactName', $QAInfoArray)) $stateHFAContactName = $QAInfoArray['stateHFAContactName'];
        if (array_key_exists('stateHFAPhone', $QAInfoArray)) $stateHFAPhone = $QAInfoArray['stateHFAPhone'];
        if (array_key_exists('stateHFAEmailAddress', $QAInfoArray)) $stateHFAEmailAddress = $QAInfoArray['stateHFAEmailAddress'];

        if (array_key_exists('appliedModification', $QAInfoArray)) $appliedModification = $QAInfoArray['appliedModification'];
        if (array_key_exists('appliedModificationDate', $QAInfoArray)) $appliedModificationDate = $QAInfoArray['appliedModificationDate'];
        if (array_key_exists('receivedModificationDate', $QAInfoArray)) $receivedModificationDate = $QAInfoArray['receivedModificationDate'];
        if (array_key_exists('repaymentOrMortAssistanceDate', $QAInfoArray)) $repaymentOrMortAssistanceDate = $QAInfoArray['repaymentOrMortAssistanceDate'];

        if (array_key_exists('occupiedBy', $QAInfoArray)) $occupiedBy = $QAInfoArray['occupiedBy'];
        if (array_key_exists('borrowerNatural', $QAInfoArray)) $borrowerNatural = $QAInfoArray['borrowerNatural'];
        if (array_key_exists('loanModType', $QAInfoArray)) {
            $loanModType = trim($QAInfoArray['loanModType']);
        }
        if (array_key_exists('HAMPTier1Payment', $QAInfoArray)) {
            $HAMPTier1Payment = trim($QAInfoArray['HAMPTier1Payment']);
        }
        if (array_key_exists('loanModPlanStanding', $QAInfoArray)) {
            $loanModPlanStanding = trim($QAInfoArray['loanModPlanStanding']);
        }
        if (array_key_exists('closingDate', $QAInfoArray)) {
            $closingDate = trim($QAInfoArray['closingDate']);
        }
        if (array_key_exists('noOfvehiclesOwned', $QAInfoArray)) $noOfvehiclesOwned = $QAInfoArray['noOfvehiclesOwned'];
        if (array_key_exists('notOnMortgageNote', $QAInfoArray)) $notOnMortgageNote = $QAInfoArray['notOnMortgageNote'];

        /** New section added for Tax Returns on Aug 22, 2016 Start **/

        if (array_key_exists('QACoBorSpouseSSN', $QAInfoArray)) $QACoBorSpouseSSN = $QAInfoArray['QACoBorSpouseSSN'];
        if (array_key_exists('isTaxReturn', $QAInfoArray)) $isTaxReturn = $QAInfoArray['isTaxReturn'];
        if (array_key_exists('QABorName', $QAInfoArray)) $QABorName = $QAInfoArray['QABorName'];
        if (array_key_exists('QABorSpouseName', $QAInfoArray)) $QABorSpouseName = $QAInfoArray['QABorSpouseName'];
        if (array_key_exists('QABorSpouseSSN', $QAInfoArray)) $QABorSpouseSSN = $QAInfoArray['QABorSpouseSSN'];
        if (array_key_exists('QACoBorName', $QAInfoArray)) $QACoBorName = $QAInfoArray['QACoBorName'];
        if (array_key_exists('QACoBorSSN', $QAInfoArray)) $QACoBorSSN = $QAInfoArray['QACoBorSSN'];
        if (array_key_exists('QACoBorSpouseName', $QAInfoArray)) $QACoBorSpouseName = $QAInfoArray['QACoBorSpouseName'];
        if (array_key_exists('QABorSSN', $QAInfoArray)) $QABorSSN = $QAInfoArray['QABorSSN'];

        /** New section added for Tax Returns on Aug 22, 2016 End **/
        if (array_key_exists('YRF4506TDate1', $QAInfoArray)) $YRF4506TDate1 = trim($QAInfoArray['YRF4506TDate1']); // YRF - Year requested For 4506T
        if (array_key_exists('YRF4506TDate2', $QAInfoArray)) $YRF4506TDate2 = trim($QAInfoArray['YRF4506TDate2']);
        if (array_key_exists('YRF4506TDate3', $QAInfoArray)) $YRF4506TDate3 = trim($QAInfoArray['YRF4506TDate3']);
        if (array_key_exists('YRF4506TDate4', $QAInfoArray)) $YRF4506TDate4 = trim($QAInfoArray['YRF4506TDate4']);


        if (Dates::IsEmpty($YRF4506TDate1)) {
            $YRF4506TDate1 = null;
        } else {
            $YRF4506TDate1 = Dates::formatDateWithRE($YRF4506TDate1, 'MDY', 'Y-m-d');
            if (trim($YRF4506TDate1) == '') {
                $YRF4506TDate1 = null;
            }
        }
        if (Dates::IsEmpty($YRF4506TDate2)) {
            $YRF4506TDate2 = null;
        } else {
            $YRF4506TDate2 = Dates::formatDateWithRE($YRF4506TDate2, 'MDY', 'Y-m-d');
            if (trim($YRF4506TDate2) == '') {
                $YRF4506TDate2 = null;
            }
        }
        if (Dates::IsEmpty($YRF4506TDate3)) {
            $YRF4506TDate3 = null;
        } else {
            $YRF4506TDate3 = Dates::formatDateWithRE($YRF4506TDate3, 'MDY', 'Y-m-d');
            if (trim($YRF4506TDate3) == '') {
                $YRF4506TDate3 = null;
            }
        }
        if (Dates::IsEmpty($YRF4506TDate4)) {
            $YRF4506TDate4 = null;
        } else {
            $YRF4506TDate4 = Dates::formatDateWithRE($YRF4506TDate4, 'MDY', 'Y-m-d');
            if (trim($YRF4506TDate4) == '') {
                $YRF4506TDate4 = null;
            }
        }

        if (Dates::IsEmpty($appliedModificationDate)) {
            $appliedModificationDate = null;
        } else {
            $appliedModificationDate = Dates::formatDateWithRE($appliedModificationDate, 'MDY', 'Y-m-d');
            if (trim($appliedModificationDate) == '') {
                $appliedModificationDate = null;
            }
        }
        if (Dates::IsEmpty($receivedModificationDate)) {
            $receivedModificationDate = null;
        } else {
            $receivedModificationDate = Dates::formatDateWithRE($receivedModificationDate, 'MDY', 'Y-m-d');
            if (trim($receivedModificationDate) == '') {
                $receivedModificationDate = null;
            }
        }
        if (Dates::IsEmpty($repaymentOrMortAssistanceDate)) {
            $repaymentOrMortAssistanceDate = null;
        } else {
            $repaymentOrMortAssistanceDate = Dates::formatDateWithRE($repaymentOrMortAssistanceDate, 'MDY', 'Y-m-d');
            if (trim($repaymentOrMortAssistanceDate) == '') {
                $repaymentOrMortAssistanceDate = null;
            }
        }

        if (Dates::IsEmpty($noticeReceivedDate)) {
            $noticeReceivedDate = null;
        } else {
            $noticeReceivedDate = Dates::formatDateWithRE($noticeReceivedDate, 'MDY', 'Y-m-d');
            if (trim($noticeReceivedDate) == '') {
                $noticeReceivedDate = null;
            }
        }
        if (Dates::IsEmpty($summonDate)) {
            $summonDate = null;
        } else {
            $summonDate = Dates::formatDateWithRE($summonDate, 'MDY', 'Y-m-d');
            if (trim($summonDate) == '') {
                $summonDate = null;
            }
        }

        if (Dates::IsEmpty($dateOfOffer)) {
            $dateOfOffer = null;
        } else {
            $dateOfOffer = Dates::formatDateWithRE($dateOfOffer, 'MDY', 'Y-m-d');
            if (trim($dateOfOffer) == '') {
                $dateOfOffer = null;
            }
        }
        if (Dates::IsEmpty($bankruptcyFilingDate)) {
            $bankruptcyFilingDate = null;
        } else {
            $bankruptcyFilingDate = Dates::formatDateWithRE($bankruptcyFilingDate, 'MDY', 'Y-m-d');
            if (trim($bankruptcyFilingDate) == '') {
                $bankruptcyFilingDate = null;
            }
        }
        if (Dates::IsEmpty($leaseExpiresDate)) {
            $leaseExpiresDate = null;
        } else {
            $leaseExpiresDate = Dates::formatDateWithRE($leaseExpiresDate, 'MDY', 'Y-m-d');
            if (trim($leaseExpiresDate) == '') {
                $leaseExpiresDate = null;
            }
        }

        if (($leaseStartedDate == NULL) || ($leaseStartedDate == '') || ($leaseStartedDate == null) || trim($leaseStartedDate) == '') {
            $leaseStartedDate = null;
        } else {
            $leaseStartedDate = Dates::formatDateWithRE($leaseStartedDate, 'MDY', 'Y-m-d');
        }

        if (Dates::IsEmpty($bankruptcyDischargeDate)) {
            $bankruptcyDischargeDate = null;
        } else {
            $bankruptcyDischargeDate = Dates::formatDateWithRE($bankruptcyDischargeDate, 'MDY', 'Y-m-d');
            if (trim($bankruptcyDischargeDate) == '') {
                $bankruptcyDischargeDate = null;
            }
        }

        if (Dates::IsEmpty($creditCounselStartedDate)) {
            $creditCounselStartedDate = null;
        } else {
            $creditCounselStartedDate = Dates::formatDateWithRE($creditCounselStartedDate, 'MDY', 'Y-m-d');
            if (trim($creditCounselStartedDate) == '') {
                $creditCounselStartedDate = null;
            }
        }

        if (Dates::IsEmpty($creditCounselFinisheddDate)) {
            $creditCounselFinisheddDate = null;
        } else {
            $creditCounselFinisheddDate = Dates::formatDateWithRE($creditCounselFinisheddDate, 'MDY', 'Y-m-d');
            if (trim($creditCounselFinisheddDate) == '') {
                $creditCounselFinisheddDate = null;
            }
        }
        if (Dates::IsEmpty($propertyListedDate)) {
            $propertyListedDate = null;
        } else {
            $propertyListedDate = Dates::formatDateWithRE($propertyListedDate, 'MDY', 'Y-m-d');
            if (trim($propertyListedDate) == '') {
                $propertyListedDate = null;
            }
        }
        if (Dates::IsEmpty($closingDate)) {
            $closingDate = null;
        } else {
            $closingDate = Dates::formatDateWithRE($closingDate, 'MDY', 'Y-m-d');
            if (trim($closingDate) == '') {
                $closingDate = null;
            }
        }

        //HMDA NEW Fields
        $hmdaFormPostData = $ip['p'] ?? [];
        $excludeFromHMDAReport = isset($hmdaFormPostData['excludeFromHMDAReport']) && $hmdaFormPostData['excludeFromHMDAReport'] == 'Yes' ? $hmdaFormPostData['excludeFromHMDAReport'] : ''; //string
        $legalEntityIdentifier = array_key_exists('legalEntityIdentifier', $hmdaFormPostData) ? $hmdaFormPostData['legalEntityIdentifier'] : ''; //string
        $introductoryRatePeriod = array_key_exists('introductoryRatePeriod', $hmdaFormPostData) ? $hmdaFormPostData['introductoryRatePeriod'] : 0; //int
        $universalLoanIdentifier = array_key_exists('universalLoanIdentifier', $hmdaFormPostData) ? $hmdaFormPostData['universalLoanIdentifier'] : ''; //string
        $interestOnlyPayment = array_key_exists('interestOnlyPayment', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['interestOnlyPayment']) : null; //money
        $actionTaken = array_key_exists('actionTaken', $hmdaFormPostData) ? $hmdaFormPostData['actionTaken'] : 0; //int
        $propertyValue = array_key_exists('propertyValue', $hmdaFormPostData) ? intval(Strings::replaceCommaValues($hmdaFormPostData['propertyValue'])) : 0; //int
        $typeOfPurchaser = array_key_exists('typeOfPurchaser', $hmdaFormPostData) ? $hmdaFormPostData['typeOfPurchaser'] : 0; //int
        $totalUnits = array_key_exists('totalUnits', $hmdaFormPostData) ? intval($hmdaFormPostData['totalUnits']) : 0; //int
        $reasonForDenial = array_key_exists('reasonForDenial', $hmdaFormPostData) ? implode('~', $hmdaFormPostData['reasonForDenial'] ?? []) : []; //array
        $reasonForDenialOther = array_key_exists('reasonForDenialOther', $hmdaFormPostData) ? $hmdaFormPostData['reasonForDenialOther'] : ''; //string
        $submissionOfApplicationFrom = array_key_exists('submissionOfApplicationFrom', $hmdaFormPostData) ? $hmdaFormPostData['submissionOfApplicationFrom'] : ''; //string
        $combinedLoanToValueRatio = array_key_exists('combinedLoanToValueRatio', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['combinedLoanToValueRatio']) : ''; //decimal
        $censusTract = array_key_exists('censusTract', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['censusTract']) : ''; //decimal

        $HMDALoanPurpose = array_key_exists('HMDALoanPurpose', $hmdaFormPostData) ? $hmdaFormPostData['HMDALoanPurpose'] : 0; //int
        $HMDALoanAmount = array_key_exists('HMDALoanAmount', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['HMDALoanAmount']) : ''; //money


        $borrowerRaceOfApplicant = array_key_exists('borrowerRaceOfApplicant', $hmdaFormPostData) ? $hmdaFormPostData['borrowerRaceOfApplicant'] : ''; //string
        $borrowerAgeOfApplicant = array_key_exists('borrowerAgeOfApplicant', $hmdaFormPostData) ? intval($hmdaFormPostData['borrowerAgeOfApplicant']) : ''; //int
        $borrowerCreditScoreOfApplicant = array_key_exists('borrowerCreditScoreOfApplicant', $hmdaFormPostData) ? intval($hmdaFormPostData['borrowerCreditScoreOfApplicant']) : ''; //int
        $borrowerCreditScoringModelOfApplicant = array_key_exists('borrowerCreditScoringModelOfApplicant', $hmdaFormPostData) ? $hmdaFormPostData['borrowerCreditScoringModelOfApplicant'] : ''; //string
        $borrowerCreditScoringModelOfApplicantOther = array_key_exists('borrowerCreditScoringModelOfApplicantOther', $hmdaFormPostData) ? $hmdaFormPostData['borrowerCreditScoringModelOfApplicantOther'] : ''; //string
        $borrowerCreditScoringModelConditionalFreeOfApplicant = array_key_exists('borrowerCreditScoringModelConditionalFreeOfApplicant', $hmdaFormPostData) ? $hmdaFormPostData['borrowerCreditScoringModelConditionalFreeOfApplicant'] : ''; //string

        $coBorrowerRaceOfApplicant = array_key_exists('coBorrowerRaceOfApplicant', $hmdaFormPostData) ? $hmdaFormPostData['coBorrowerRaceOfApplicant'] : ''; //string
        $coBorrowerAgeOfApplicant = array_key_exists('coBorrowerAgeOfApplicant', $hmdaFormPostData) ? intval($hmdaFormPostData['coBorrowerAgeOfApplicant']) : ''; //int
        $coBorrowerCreditScoreOfApplicant = array_key_exists('coBorrowerCreditScoreOfApplicant', $hmdaFormPostData) ? intval($hmdaFormPostData['coBorrowerCreditScoreOfApplicant']) : ''; //int
        $coBorrowerCreditScoringModelOfApplicant = array_key_exists('coBorrowerCreditScoringModelOfApplicant', $hmdaFormPostData) ? $hmdaFormPostData['coBorrowerCreditScoringModelOfApplicant'] : ''; //string
        $coBorrowerCreditScoringModelOfApplicantOther = array_key_exists('coBorrowerCreditScoringModelOfApplicantOther', $hmdaFormPostData) ? $hmdaFormPostData['coBorrowerCreditScoringModelOfApplicantOther'] : ''; //string
        $coBorrowerCreditScoringModelConditionalFreeOfApplicant = array_key_exists('coBorrowerCreditScoringModelConditionalFreeOfApplicant', $hmdaFormPostData) ? $hmdaFormPostData['coBorrowerCreditScoringModelConditionalFreeOfApplicant'] : ''; //string

        $borEnrolledPrincipalTribe = array_key_exists('borEnrolledPrincipalTribe', $hmdaFormPostData) ? $hmdaFormPostData['borEnrolledPrincipalTribe'] : ''; //string
        $coBorEnrolledPrincipalTribe = array_key_exists('coBorEnrolledPrincipalTribe', $hmdaFormPostData) ? $hmdaFormPostData['coBorEnrolledPrincipalTribe'] : ''; //string

        //CU-868brycx9
        $HMDALoanType = array_key_exists('HMDALoanType', $hmdaFormPostData) ? intval($hmdaFormPostData['HMDALoanType']) : null; //int
        $manufacturedHomeSecuredPropertyType = array_key_exists('manufacturedHomeSecuredPropertyType', $hmdaFormPostData) ? intval($hmdaFormPostData['manufacturedHomeSecuredPropertyType']) : null; //int
        $manufacturedHomeLandPropertyInterest = array_key_exists('manufacturedHomeLandPropertyInterest', $hmdaFormPostData) ? intval($hmdaFormPostData['manufacturedHomeLandPropertyInterest']) : null; //int
        $preapproval = array_key_exists('preapproval', $hmdaFormPostData) ? intval($hmdaFormPostData['preapproval']) : null; //int
        $multifamilyAffordableUnits = array_key_exists('multifamilyAffordableUnits', $hmdaFormPostData) ? intval($hmdaFormPostData['multifamilyAffordableUnits']) : null; //int
        $constructionMethod = array_key_exists('constructionMethod', $hmdaFormPostData) ? intval($hmdaFormPostData['constructionMethod']) : null; //int
        $applicationChannel = array_key_exists('applicationChannel', $hmdaFormPostData) ? intval($hmdaFormPostData['applicationChannel']) : null; //int
        $initiallyPayableToYourInstitution = array_key_exists('initiallyPayableToYourInstitution', $hmdaFormPostData) ? intval($hmdaFormPostData['initiallyPayableToYourInstitution']) : null; //int
        $rateSpread = array_key_exists('rateSpread', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['rateSpread']) : null; //decimal
        $HOEPAStatus = array_key_exists('HOEPAStatus', $hmdaFormPostData) ? intval($hmdaFormPostData['HOEPAStatus']) : null; //int
        $totalLoanCostOrTotalPointsAndFees = array_key_exists('totalLoanCostOrTotalPointsAndFees', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['totalLoanCostOrTotalPointsAndFees']) : null; //money
        $originationCharges = array_key_exists('originationCharges', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['originationCharges']) : null; //money
        $discountPoints = array_key_exists('discountPoints', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['discountPoints']) : null; //money
        $lenderCredits = array_key_exists('lenderCredits', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['lenderCredits']) : null; //money
        $HMDAPrepaymentPenalty = array_key_exists('HMDAPrepaymentPenalty', $hmdaFormPostData) ? intval($hmdaFormPostData['HMDAPrepaymentPenalty']) : null; //int
        $debtToIncomeRatio = array_key_exists('debtToIncomeRatio', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['debtToIncomeRatio']) : null; //decimal
        $balloonPayment = array_key_exists('balloonPayment', $hmdaFormPostData) ? intval($hmdaFormPostData['balloonPayment']) : null; //int
        $automatedUnderwritingSystem = array_key_exists('automatedUnderwritingSystem', $hmdaFormPostData)
            ? implode(',', $hmdaFormPostData['automatedUnderwritingSystem'] ?? [])
            : []; //string
        $AUSFreeFormTextFieldForCode5 = array_key_exists('AUSFreeFormTextFieldForCode5', $hmdaFormPostData) ? $hmdaFormPostData['AUSFreeFormTextFieldForCode5'] : ''; //string
        $automatedUnderwritingSystemResult = array_key_exists('automatedUnderwritingSystemResult', $hmdaFormPostData) ? intval($hmdaFormPostData['automatedUnderwritingSystemResult']) : null; //string
        $AUSResultFreeFormTextFieldForCode16 = array_key_exists('AUSResultFreeFormTextFieldForCode16', $hmdaFormPostData) ? $hmdaFormPostData['AUSResultFreeFormTextFieldForCode16'] : ''; //string
        $reverseMortgage = array_key_exists('reverseMortgage', $hmdaFormPostData) ? intval($hmdaFormPostData['reverseMortgage']) : null; //int
        $openEndLineOfCredit = array_key_exists('openEndLineOfCredit', $hmdaFormPostData) ? intval($hmdaFormPostData['openEndLineOfCredit']) : null; //int
        $businessOrCommercialPurpose = array_key_exists('businessOrCommercialPurpose', $hmdaFormPostData) ? intval($hmdaFormPostData['businessOrCommercialPurpose']) : null; //int
        $areThereInterestOnlyPayment = array_key_exists('areThereInterestOnlyPayment', $hmdaFormPostData) ? intval($hmdaFormPostData['areThereInterestOnlyPayment']) : null; //int
        $negativeAmortization = array_key_exists('negativeAmortization', $hmdaFormPostData) ? intval($hmdaFormPostData['negativeAmortization']) : null; //int
        $otherNonAmortizingFeatures = array_key_exists('otherNonAmortizingFeatures', $hmdaFormPostData) ? intval($hmdaFormPostData['otherNonAmortizingFeatures']) : null; //int
        $countyCode = array_key_exists('countyCode', $hmdaFormPostData) ? intval($hmdaFormPostData['countyCode']) : null; //int
        $incomeBorrowerCoBorrower = array_key_exists('incomeBorrowerCoBorrower', $hmdaFormPostData) ? Strings::replaceCommaValues($hmdaFormPostData['incomeBorrowerCoBorrower']) : null; //money


        $qrySel = 'SELECT QAID, \'QAInfo\' as myOpt FROM tblQAInfo WHERE LMRId = :LMRId ;';
        $qrySel .= 'SELECT PID, \'proposalInfo\' as myOpt FROM tblProposalInfo WHERE LMRId = :LMRId ;';
        $qrySel .= 'SELECT SSID, \'shortSaleInfo\' as myOpt FROM tblShortSale WHERE LMRId = :LMRId ;';
        $sqlParams = [
            'LMRId' => $LMRId,
        ];

        $resultArray = Database2::getInstance()->multiQueryData($qrySel, 'myOpt', $sqlParams, null, true);

        if (array_key_exists('QAInfo', $resultArray)) $QAID = trim($resultArray['QAInfo']['QAID']);
        if (array_key_exists('proposalInfo', $resultArray)) $PID = trim($resultArray['proposalInfo']['PID']);
        if (array_key_exists('shortSaleInfo', $resultArray)) $SSID = trim($resultArray['shortSaleInfo']['SSID']);

        $tblQAInfo = null;

        if ($LMRId > 0) {
            $tblQAInfo = $QAID ? tblQAInfo::Get([
                tblQAInfo_db::COLUMN_QAID => $QAID,
            ]) : new tblQAInfo();

            $tblQAInfo->LMRId = $LMRId;

            if (array_key_exists('noOfPeopleDependent', $QAInfoArray)) {
                $tblQAInfo->noOfPeopleDependent = $noOfPeopleDependent;
            }
            if (array_key_exists('stayInHome', $QAInfoArray)) {
                $tblQAInfo->stayInHome = $stayInHome;
            }
            if (array_key_exists('homeListedForSale', $QAInfoArray)) {
                $tblQAInfo->homeListedForSale = $homeListedForSale;
            }
            if (array_key_exists('seriousNeedRepair', $QAInfoArray)) {
                $tblQAInfo->seriousNeedRepair = $seriousNeedRepair;
            }
            if (array_key_exists('repairExplanation', $QAInfoArray)) {
                $tblQAInfo->repairExplanation = HTTP::escapeQuoteForPOST($repairExplanation);
            }
            if (array_key_exists('homeMortgageClosed', $QAInfoArray)) {
                $tblQAInfo->homeMortgageClosed = $homeMortgageClosed;
            }
            if (array_key_exists('employmentExplanation', $QAInfoArray)) {
                $tblQAInfo->employmentExplanation = HTTP::escapeQuoteForPOST($employmentExplanation);
            }
            if (array_key_exists('moreThanOneProperty', $QAInfoArray)) {
                $tblQAInfo->moreThanOneProperty = $moreThanOneProperty;
            }
            if (array_key_exists('propertyExlpanation', $QAInfoArray)) {
                $tblQAInfo->propertyExlpanation = HTTP::escapeQuoteForPOST($propertyExlpanation);
            }
            if (array_key_exists('bankruptcy', $QAInfoArray)) {
                $tblQAInfo->bankruptcy = $bankruptcy;
            }
            if (array_key_exists('desiredClosingDate', $QAInfoArray)) {
                $desiredClosingDate = $QAInfoArray['desiredClosingDate'];
                if (Dates::IsEmpty($desiredClosingDate)) {
                    $desiredClosingDate = null;
                } else {
                    $desiredClosingDate = trim(Dates::formatDateWithRE($desiredClosingDate, 'MDY', 'Y-m-d'));
                }
                $tblQAInfo->desiredClosingDate = $desiredClosingDate;
            }

            if ($QAID > 0) {

                if (array_key_exists('creditCounselling', $QAInfoArray)) {
                    $tblQAInfo->creditCounselling = $creditCounselling;
                }

                if (array_key_exists('propertyIncludedBankruptcy', $QAInfoArray)) {
                    $tblQAInfo->propertyIncludedBankruptcy = $propertyIncludedBankruptcy;
                }

                if (array_key_exists('thirtyDaysLate', $QAInfoArray)) {
                    $tblQAInfo->thirtyDaysLate = intval($thirtyDaysLate);
                }

                if (array_key_exists('foreClosureNotes', $QAInfoArray)) {
                    $tblQAInfo->foreClosureNotes = $foreClosureNotes;
                }

                if (array_key_exists('noticeReceivedDate', $QAInfoArray)) {
                    $tblQAInfo->noticeReceivedDate = $noticeReceivedDate;
                }

                if (array_key_exists('summonDate', $QAInfoArray)) {
                    $tblQAInfo->summonDate = $summonDate;
                }

                if (array_key_exists('saleByOwner', $QAInfoArray)) {
                    $tblQAInfo->saleByOwner = $saleByOwner;
                }

                if (array_key_exists('receiveOfferOnProperty', $QAInfoArray)) {
                    $tblQAInfo->receiveOfferOnProperty = $receiveOfferOnProperty;
                }

                if (array_key_exists('dateOfOffer', $QAInfoArray)) {
                    $tblQAInfo->dateOfOffer = $dateOfOffer;
                }

                if (array_key_exists('amountOfOffer', $QAInfoArray)) {
                    $tblQAInfo->amountOfOffer = $amountOfOffer;
                }

                if (array_key_exists('RETBillPayer', $QAInfoArray)) {
                    $tblQAInfo->RETBillPayer = $RETBillPayer;
                }

                if (array_key_exists('taxesCurrent', $QAInfoArray)) {
                    $tblQAInfo->taxesCurrent = $taxesCurrent;
                }

                if (array_key_exists('hazardInsurancePolicy', $QAInfoArray)) {
                    $tblQAInfo->hazardInsurancePolicy = $hazardInsurancePolicy;
                }

                if (array_key_exists('policyCurrent', $QAInfoArray)) {
                    $tblQAInfo->policyCurrent = $policyCurrent;
                }

                if (array_key_exists('insuranceCompName', $QAInfoArray)) {
                    $tblQAInfo->insuranceCompName = HTTP::escapeQuoteForPOST($insuranceCompName);
                }

                if (array_key_exists('insuranceCompPhone', $QAInfoArray)) {
                    $tblQAInfo->insuranceCompPhone = $insuranceCompPhone;
                }

                if (array_key_exists('filedBankruptcy', $QAInfoArray)) {
                    $tblQAInfo->filedBankruptcy = $filedBankruptcy;
                }

                if (array_key_exists('bankruptcyChapter', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyChapter = $bankruptcyChapter;
                }

                if (array_key_exists('bankruptcyFilingDate', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyFilingDate = $bankruptcyFilingDate;
                }

                if (array_key_exists('bankruptcyDischarged', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyDischarged = $bankruptcyDischarged;
                }

                if (array_key_exists('bankruptcyCaseNumb', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyCaseNumb = $bankruptcyCaseNumb;
                }

                if (array_key_exists('creditCounselorName', $QAInfoArray)) {
                    $tblQAInfo->creditCounselorName = HTTP::escapeQuoteForPOST($creditCounselorName);
                }

                if (array_key_exists('creditCounselorPhone', $QAInfoArray)) {
                    $tblQAInfo->creditCounselorPhone = $creditCounselorPhone;
                }

                if (array_key_exists('creditCounselorEmail', $QAInfoArray)) {
                    $tblQAInfo->creditCounselorEmail = $creditCounselorEmail;
                }

                if (array_key_exists('rentPayment', $QAInfoArray)) {
                    $tblQAInfo->rentPayment = $rentPayment;
                }

                if (array_key_exists('propertyForRent', $QAInfoArray)) {
                    $tblQAInfo->propertyForRent = $propertyForRent;
                }

                if (array_key_exists('monthlyRentAmt', $QAInfoArray)) {
                    $tblQAInfo->monthlyRentAmt = $monthlyRentAmt;
                }

                if (array_key_exists('monthlyLastPaidAmt', $QAInfoArray)) {
                    $tblQAInfo->monthlyLastPaidAmt = $monthlyLastPaidAmt;
                }

                if (array_key_exists('leaseExpiresDate', $QAInfoArray)) {
                    $tblQAInfo->leaseExpiresDate = $leaseExpiresDate;
                }

                if (array_key_exists('isHomeownerServed', $QAInfoArray)) {
                    $tblQAInfo->isHomeownerServed = $isHomeownerServed;
                }

                if (array_key_exists('noOfProperties', $QAInfoArray)) {
                    $tblQAInfo->noOfProperties = $noOfProperties;
                }

                if (array_key_exists('noOfMortgages', $QAInfoArray)) {
                    $tblQAInfo->noOfMortgages = $noOfMortgages;
                }

                if (array_key_exists('bankruptcyDischargeDate', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyDischargeDate = $bankruptcyDischargeDate;
                }

                if (array_key_exists('creditCounselStartedDate', $QAInfoArray)) {
                    $tblQAInfo->creditCounselStartedDate = $creditCounselStartedDate;
                }

                if (array_key_exists('creditCounselFinisheddDate', $QAInfoArray)) {
                    $tblQAInfo->creditCounselFinisheddDate = $creditCounselFinisheddDate;
                }

                if (array_key_exists('borrowerConvicted', $QAInfoArray)) {
                    $tblQAInfo->borrowerConvicted = $borrowerConvicted;
                }

                if (array_key_exists('coBorrowerConvicted', $QAInfoArray)) {
                    $tblQAInfo->coBorrowerConvicted = $coBorrowerConvicted;
                }

                if (array_key_exists('repaymentOrMortAssistance', $QAInfoArray)) {
                    $tblQAInfo->repaymentOrMortAssistance = $repaymentOrMortAssistance;
                }

                if (array_key_exists('delinquentOnPropTax', $QAInfoArray)) {
                    $tblQAInfo->delinquentOnPropTax = $delinquentOnPropTax;
                }

                if (array_key_exists('delinquentTaxYear', $QAInfoArray)) {
                    $tblQAInfo->delinquentTaxYear = $delinquentTaxYear;
                }

                if (array_key_exists('delinquentTaxAmount', $QAInfoArray)) {
                    $tblQAInfo->delinquentTaxAmount = $delinquentTaxAmount;
                }

                if (array_key_exists('delinquencySavedAmount', $QAInfoArray)) {
                    $tblQAInfo->delinquencySavedAmount = $delinquencySavedAmount;
                }

                if (array_key_exists('attorneyFirmName', $QAInfoArray)) {
                    $tblQAInfo->attorneyFirmName = HTTP::escapeQuoteForPOST($attorneyFirmName);
                }

                if (array_key_exists('attorneyName', $QAInfoArray)) {
                    $tblQAInfo->attorneyName = HTTP::escapeQuoteForPOST($attorneyName);
                }

                if (array_key_exists('attorneyPhone', $QAInfoArray)) {
                    $tblQAInfo->attorneyPhone = $attorneyPhone;
                }

                if (array_key_exists('attorneyFax', $QAInfoArray)) {
                    $tblQAInfo->attorneyFax = $attorneyFax;
                }

                if (array_key_exists('attorneyCell', $QAInfoArray)) {
                    $tblQAInfo->attorneyCell = $attorneyCell;
                }

                if (array_key_exists('isPaymentsPlanBehind', $QAInfoArray)) {
                    $tblQAInfo->isPaymentsPlanBehind = $isPaymentsPlanBehind;
                }

                if (array_key_exists('bankruptcyDispositionStatus', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyDispositionStatus = $bankruptcyDispositionStatus;
                }

                if (array_key_exists('doneAppraisal', $QAInfoArray)) {
                    $tblQAInfo->doneAppraisal = $doneAppraisal;
                }

                if (array_key_exists('appraisalListValue', $QAInfoArray)) {
                    $tblQAInfo->appraisalListValue = $appraisalListValue;
                }

                if (array_key_exists('offerPropertyAgencyName', $QAInfoArray)) {
                    $tblQAInfo->offerPropertyAgencyName = HTTP::escapeQuoteForPOST($offerPropertyAgencyName);
                }

                if (array_key_exists('listingPrice', $QAInfoArray)) {
                    $tblQAInfo->listingPrice = HTTP::escapeQuoteForPOST($listingPrice);
                }

                if (array_key_exists('propertyListedDate', $QAInfoArray)) {
                    $tblQAInfo->propertyListedDate = $propertyListedDate;
                }

                if (array_key_exists('saleForHowLong', $QAInfoArray)) {
                    $tblQAInfo->saleForHowLong = $saleForHowLong;
                }

                if (array_key_exists('OPAPhone', $QAInfoArray)) {
                    $tblQAInfo->OPAPhone = $OPAPhone;
                }

                if (array_key_exists('attorneyEmail', $QAInfoArray)) {
                    $tblQAInfo->attorneyEmail = $attorneyEmail;
                }

                if (array_key_exists('PublishBInfo', $QAInfoArray)) {
                    $tblQAInfo->PublishBInfo = $PublishBInfo;
                }

                if (array_key_exists('BEthnicity', $QAInfoArray)) {
                    $tblQAInfo->BEthnicity = $BEthnicity;
                }

                if (array_key_exists('BRace', $QAInfoArray)) {
                    $tblQAInfo->BRace = $BRace;
                }

                if (array_key_exists('BGender', $QAInfoArray)) {
                    $tblQAInfo->BGender = $BGender;
                }

                if (array_key_exists('BVeteran', $QAInfoArray)) {
                    $tblQAInfo->BVeteran = $BVeteran;
                }

                if (array_key_exists('bFiEthnicity', $QAInfoArray)) {
                    $tblQAInfo->bFiEthnicity = $bFiEthnicity;
                }

                if (array_key_exists('bFiEthnicitySub', $QAInfoArray)) {
                    $tblQAInfo->bFiEthnicitySub = $bFiEthnicitySub;
                }

                if (array_key_exists('bFiEthnicitySubOther', $QAInfoArray)) {
                    $tblQAInfo->bFiEthnicitySubOther = $bFiEthnicitySubOther;
                }

                if (array_key_exists('bFiSex', $QAInfoArray)) {
                    $tblQAInfo->bFiSex = $bFiSex;
                }

                if (array_key_exists('bFiRace', $QAInfoArray)) {
                    $tblQAInfo->bFiRace = $bFiRace;
                }

                if (array_key_exists('bFiRaceSub', $QAInfoArray)) {
                    $tblQAInfo->bFiRaceSub = $bFiRaceSub;
                }

                if (array_key_exists('bFiRaceAsianOther', $QAInfoArray)) {
                    $tblQAInfo->bFiRaceAsianOther = $bFiRaceAsianOther;
                }

                if (array_key_exists('bFiRacePacificOther', $QAInfoArray)) {
                    $tblQAInfo->bFiRacePacificOther = $bFiRacePacificOther;
                }

                if (array_key_exists('bDemoInfo', $QAInfoArray)) {
                    $tblQAInfo->bDemoInfo = $bDemoInfo;
                }

                if (array_key_exists('PublishCBInfo', $QAInfoArray)) {
                    $tblQAInfo->PublishCBInfo = intval($PublishCBInfo);
                }

                if (array_key_exists('CBEthnicity', $QAInfoArray)) {
                    $tblQAInfo->CBEthnicity = intval($CBEthnicity);
                }

                if (array_key_exists('CBEthnicitySub', $QAInfoArray)) {
                    $tblQAInfo->CBEthnicitySub = $CBEthnicitySub;
                }

                if (array_key_exists('CBEthnicitySubOther', $QAInfoArray)) {
                    $tblQAInfo->CBEthnicitySubOther = $CBEthnicitySubOther;
                }

                if (array_key_exists('CBRace', $QAInfoArray)) {
                    $tblQAInfo->CBRace = intval($CBRace);
                }

                if (array_key_exists('CBRaceSub', $QAInfoArray)) {
                    $tblQAInfo->CBRaceSub = $CBRaceSub;
                }

                if (array_key_exists('CBRaceAsianOther', $QAInfoArray)) {
                    $tblQAInfo->CBRaceAsianOther = $CBRaceAsianOther;
                }

                if (array_key_exists('CBRacePacificOther', $QAInfoArray)) {
                    $tblQAInfo->CBRacePacificOther = $CBRacePacificOther;
                }

                if (array_key_exists('CBGender', $QAInfoArray)) {
                    $tblQAInfo->CBGender = intval($CBGender);
                }

                if (array_key_exists('CBVeteran', $QAInfoArray)) {
                    $tblQAInfo->CBVeteran = $CBVeteran;
                }

                if (array_key_exists('CBFiEthnicity', $QAInfoArray)) {
                    $tblQAInfo->CBFiEthnicity = $CBFiEthnicity;
                }

                if (array_key_exists('CBFiGender', $QAInfoArray)) {
                    $tblQAInfo->CBFiGender = $CBFiGender;
                }

                if (array_key_exists('CBFiRace', $QAInfoArray)) {
                    $tblQAInfo->CBFiRace = $CBFiRace;
                }

                if (array_key_exists('CBDDemoInfo', $QAInfoArray)) {
                    $tblQAInfo->CBDDemoInfo = $CBDDemoInfo;
                }

                if (array_key_exists('HAMPPrincipalResidence', $QAInfoArray)) {
                    $tblQAInfo->HAMPPrincipalResidence = $HAMPPrincipalResidence;
                }

                if (array_key_exists('permanentHAMPModification', $QAInfoArray)) {
                    $tblQAInfo->permanentHAMPModification = $permanentHAMPModification;
                }

                if (array_key_exists('howManyPermanentHAMP', $QAInfoArray)) {
                    $tblQAInfo->howManyPermanentHAMP = $howManyPermanentHAMP;
                }

                if (array_key_exists('HAMPTrialPrincipal', $QAInfoArray)) {
                    $tblQAInfo->HAMPTrialPrincipal = $HAMPTrialPrincipal;
                }

                if (array_key_exists('serviceMember', $QAInfoArray)) {
                    $tblQAInfo->serviceMember = $serviceMember;
                }

                if (array_key_exists('serviceMemberOrder', $QAInfoArray)) {
                    $tblQAInfo->serviceMemberOrder = $serviceMemberOrder;
                }

                if (array_key_exists('serviceMemberSurvivor', $QAInfoArray)) {
                    $tblQAInfo->serviceMemberSurvivor = $serviceMemberSurvivor;
                }

                if (array_key_exists('creditCounselorAgency', $QAInfoArray)) {
                    $tblQAInfo->creditCounselorAgency = HTTP::escapeQuoteForPOST($creditCounselorAgency);
                }

                if (array_key_exists('attorneyNum', $QAInfoArray)) {
                    $tblQAInfo->attorneyNumber = $attorneyNum;
                }

                if (array_key_exists('policyNumber', $QAInfoArray)) {
                    $tblQAInfo->policyNumber = $policyNumber;
                }

                if (array_key_exists('attorneyAddress', $QAInfoArray)) {
                    $tblQAInfo->attorneyAddress = HTTP::escapeQuoteForPOST($attorneyAddress);
                }

                if (array_key_exists('attorneyCity', $QAInfoArray)) {
                    $tblQAInfo->attorneyCity = $attorneyCity;
                }

                if (array_key_exists('attorneyState', $QAInfoArray)) {
                    $tblQAInfo->attorneyState = $attorneyState;
                }

                if (array_key_exists('attorneyZip', $QAInfoArray)) {
                    $tblQAInfo->attorneyZip = $attorneyZip;
                }

                if (array_key_exists('jurisDiction', $QAInfoArray)) {
                    $tblQAInfo->jurisDiction = $jurisDiction;
                }

                if (array_key_exists('indexNo', $QAInfoArray)) {
                    $tblQAInfo->indexNo = $indexNo;
                }

                if (array_key_exists('appliedModification', $QAInfoArray)) {
                    $tblQAInfo->appliedModification = HTTP::escapeQuoteForPOST($appliedModification);
                }

                if (array_key_exists('appliedModificationDate', $QAInfoArray)) {
                    $tblQAInfo->appliedModificationDate = $appliedModificationDate;
                }

                if (array_key_exists('receivedModificationDate', $QAInfoArray)) {
                    $tblQAInfo->receivedModificationOfferDate = $receivedModificationDate;
                }

                if (array_key_exists('repaymentOrMortAssistanceDate', $QAInfoArray)) {
                    $tblQAInfo->repaymentOrMortAssistanceDate = $repaymentOrMortAssistanceDate;
                }

                if (array_key_exists('borrowerNatural', $QAInfoArray)) {
                    $tblQAInfo->borrowerNatural = $borrowerNatural;
                }

                if (array_key_exists('occupiedBy', $QAInfoArray)) {
                    $tblQAInfo->occupiedBy = HTTP::escapeQuoteForPOST($occupiedBy);
                }

                if (array_key_exists('loanModType', $QAInfoArray)) {
                    $tblQAInfo->loanModType = $loanModType;
                }

                if (array_key_exists('HAMPTier1Payment', $QAInfoArray)) {
                    $tblQAInfo->HAMPTier1Payment = $HAMPTier1Payment;
                }

                if (array_key_exists('loanModPlanStanding', $QAInfoArray)) {
                    $tblQAInfo->loanModPlanStanding = $loanModPlanStanding;
                }

                if (array_key_exists('stateHFAEntity', $QAInfoArray)) {
                    $tblQAInfo->stateHFAEntity = HTTP::escapeQuoteForPOST($stateHFAEntity);
                }

                if (array_key_exists('stateHFAContactName', $QAInfoArray)) {
                    $tblQAInfo->stateHFAContactName = HTTP::escapeQuoteForPOST($stateHFAContactName);
                }

                if (array_key_exists('stateHFAPhone', $QAInfoArray)) {
                    $tblQAInfo->stateHFAPhone = $stateHFAPhone;
                }

                if (array_key_exists('stateHFAEmailAddress', $QAInfoArray)) {
                    $tblQAInfo->stateHFAEmailAddress = $stateHFAEmailAddress;
                }

                if (array_key_exists('serviceMemberServing', $QAInfoArray)) {
                    $tblQAInfo->serviceMemberServing = $serviceMemberServing;
                }

                if (array_key_exists('isMtgReaffirmed', $QAInfoArray)) {
                    $tblQAInfo->isMtgReaffirmed = $isMtgReaffirmed;
                }

                if (array_key_exists('isInsuranceDelinquent', $QAInfoArray)) {
                    $tblQAInfo->isInsuranceDelinquent = $isInsuranceDelinquent;
                }

                if (array_key_exists('delinquentInsuranceAmount', $QAInfoArray)) {
                    $tblQAInfo->delinquentInsuranceAmount = $delinquentInsuranceAmount;
                }

                if (array_key_exists('hazardPolicyCurrent', $QAInfoArray)) {
                    $tblQAInfo->hazardPolicyCurrent = $hazardPolicyCurrent;
                }

                if (array_key_exists('leaseStartedDate', $QAInfoArray)) {
                    $tblQAInfo->leaseStartedDate = $leaseStartedDate;
                }

                if (array_key_exists('closingDate', $QAInfoArray)) {
                    $tblQAInfo->closingDate = $closingDate;
                }

                if (array_key_exists('serviceMemberDeceased', $QAInfoArray)) {
                    $tblQAInfo->serviceMemberDeceased = $serviceMemberDeceased;
                }

                if (array_key_exists('noOfvehiclesOwned', $QAInfoArray)) {
                    $tblQAInfo->noOfvehiclesOwned = $noOfvehiclesOwned;
                }

                if (array_key_exists('notOnMortgageNote', $QAInfoArray)) {
                    $tblQAInfo->notOnMortgageNote = $notOnMortgageNote;
                }

                /** New section added for Tax Returns on Aug 22, 2016 Start **/

                if (array_key_exists('isTaxReturn', $QAInfoArray)) {
                    $tblQAInfo->isTaxReturn = $isTaxReturn;
                }

                if (array_key_exists('QABorName', $QAInfoArray)) {
                    $tblQAInfo->QABorName = $QABorName;
                }

                if (array_key_exists('QABorSpouseName', $QAInfoArray)) {
                    $tblQAInfo->QABorSpouseName = $QABorSpouseName;
                }

                if (array_key_exists('QABorSpouseSSN', $QAInfoArray)) {
                    $tblQAInfo->QABorSpouseSSN = $QABorSpouseSSN;
                }

                if (array_key_exists('QACoBorName', $QAInfoArray)) {
                    $tblQAInfo->QACoBorName = $QACoBorName;
                }

                if (array_key_exists('QACoBorSSN', $QAInfoArray)) {
                    $tblQAInfo->QACoBorSSN = $QACoBorSSN;
                }

                if (array_key_exists('QACoBorSpouseName', $QAInfoArray)) {
                    $tblQAInfo->QACoBorSpouseName = $QACoBorSpouseName;
                }

                if (array_key_exists('QABorSSN', $QAInfoArray)) {
                    $tblQAInfo->QABorSSN = $QABorSSN;
                }

                if (array_key_exists('QACoBorSpouseSSN', $QAInfoArray)) {
                    $tblQAInfo->QACoBorSpouseSSN = $QACoBorSpouseSSN;
                }

                if (array_key_exists('YRF4506TDate1', $QAInfoArray)) {
                    $tblQAInfo->YRF4506TDate1 = $YRF4506TDate1;
                }

                if (array_key_exists('YRF4506TDate2', $QAInfoArray)) {
                    $tblQAInfo->YRF4506TDate2 = $YRF4506TDate2;
                }

                if (array_key_exists('YRF4506TDate3', $QAInfoArray)) {
                    $tblQAInfo->YRF4506TDate3 = $YRF4506TDate3;
                }

                if (array_key_exists('YRF4506TDate4', $QAInfoArray)) {
                    $tblQAInfo->YRF4506TDate4 = $YRF4506TDate4;
                }

                $tblQAInfo->excludeFromHMDAReport = $excludeFromHMDAReport;

                if (array_key_exists('legalEntityIdentifier', $hmdaFormPostData)) {
                    $tblQAInfo->legalEntityIdentifier = $legalEntityIdentifier;
                }

                if (array_key_exists('introductoryRatePeriod', $hmdaFormPostData)) {
                    $tblQAInfo->introductoryRatePeriod = intval($introductoryRatePeriod);
                }

                if (array_key_exists('universalLoanIdentifier', $hmdaFormPostData)) {
                    $tblQAInfo->universalLoanIdentifier = $universalLoanIdentifier;
                }

                if (array_key_exists('HMDALoanPurpose', $hmdaFormPostData)) {
                    $tblQAInfo->HMDALoanPurpose = intval($HMDALoanPurpose);
                }

                if (array_key_exists('HMDALoanAmount', $hmdaFormPostData)) { //last column
                    $tblQAInfo->HMDALoanAmount = $HMDALoanAmount;
                }

                if (array_key_exists('interestOnlyPayment', $hmdaFormPostData)) {
                    $tblQAInfo->interestOnlyPayment = $interestOnlyPayment;
                }

                if (array_key_exists('actionTaken', $hmdaFormPostData)) {
                    $tblQAInfo->actionTaken = intval($actionTaken);
                }

                if (array_key_exists('propertyValue', $hmdaFormPostData)) {
                    $tblQAInfo->propertyValue = Strings::replaceCommaValues($propertyValue);
                }

                if (array_key_exists('typeOfPurchaser', $hmdaFormPostData)) {
                    $tblQAInfo->typeOfPurchaser = $typeOfPurchaser;
                }

                if (array_key_exists('totalUnits', $hmdaFormPostData)) {
                    $tblQAInfo->totalUnits = $totalUnits;
                }

                if (array_key_exists('reasonForDenial', $hmdaFormPostData)) {
                    $tblQAInfo->reasonForDenial = $reasonForDenial;
                }

                if (array_key_exists('reasonForDenialOther', $hmdaFormPostData)) {
                    $tblQAInfo->reasonForDenialOther = $reasonForDenialOther;
                }

                if (array_key_exists('submissionOfApplicationFrom', $hmdaFormPostData)) {
                    $tblQAInfo->submissionOfApplicationFrom = $submissionOfApplicationFrom;
                }

                if (array_key_exists('combinedLoanToValueRatio', $hmdaFormPostData)) {
                    $tblQAInfo->combinedLoanToValueRatio = $combinedLoanToValueRatio;
                }

                if (array_key_exists('censusTract', $hmdaFormPostData)) {
                    $tblQAInfo->censusTract = $censusTract;
                }

                if (array_key_exists('borrowerRaceOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerRaceOfApplicant = $borrowerRaceOfApplicant;
                }

                if (array_key_exists('borrowerAgeOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerAgeOfApplicant = $borrowerAgeOfApplicant;
                }

                if (array_key_exists('borrowerCreditScoreOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerCreditScoreOfApplicant = $borrowerCreditScoreOfApplicant;
                }

                if (array_key_exists('borrowerCreditScoringModelOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerCreditScoringModelOfApplicant = $borrowerCreditScoringModelOfApplicant;
                }

                if (array_key_exists('borrowerCreditScoringModelOfApplicantOther', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerCreditScoringModelOfApplicantOther = $borrowerCreditScoringModelOfApplicantOther;
                }

                if (array_key_exists('borrowerCreditScoringModelConditionalFreeOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerCreditScoringModelConditionalFreeOfApplicant = $borrowerCreditScoringModelConditionalFreeOfApplicant;
                }

                if (array_key_exists('coBorrowerRaceOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerRaceOfApplicant = $coBorrowerRaceOfApplicant;
                }

                if (array_key_exists('coBorrowerAgeOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerAgeOfApplicant = $coBorrowerAgeOfApplicant;
                }

                if (array_key_exists('coBorrowerCreditScoreOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerCreditScoreOfApplicant = $coBorrowerCreditScoreOfApplicant;
                }

                if (array_key_exists('coBorrowerCreditScoringModelOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerCreditScoringModelOfApplicant = $coBorrowerCreditScoringModelOfApplicant;
                }

                if (array_key_exists('coBorrowerCreditScoringModelOfApplicantOther', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerCreditScoringModelOfApplicantOther = $coBorrowerCreditScoringModelOfApplicantOther;
                }

            } else {

                if (array_key_exists('bankruptcyExlpanation', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyExlpanation = $bankruptcyExlpanation;
                }
                if (array_key_exists('creditCounselling', $QAInfoArray)) {
                    $tblQAInfo->creditCounselling = $creditCounselling;
                }

                if (array_key_exists('propertyIncludedBankruptcy', $QAInfoArray)) {
                    $tblQAInfo->propertyIncludedBankruptcy = $propertyIncludedBankruptcy;
                }

                if (array_key_exists('thirtyDaysLate', $QAInfoArray)) {
                    $tblQAInfo->thirtyDaysLate = intval($thirtyDaysLate);
                }

                if (array_key_exists('foreClosureNotes', $QAInfoArray)) {
                    $tblQAInfo->foreClosureNotes = $foreClosureNotes;
                }

                if (array_key_exists('noticeReceivedDate', $QAInfoArray)) {
                    $tblQAInfo->noticeReceivedDate = $noticeReceivedDate;
                }

                if (array_key_exists('summonDate', $QAInfoArray)) {
                    $tblQAInfo->summonDate = $summonDate;
                }

                if (array_key_exists('saleByOwner', $QAInfoArray)) {
                    $tblQAInfo->saleByOwner = $saleByOwner;
                }

                if (array_key_exists('receiveOfferOnProperty', $QAInfoArray)) {
                    $tblQAInfo->receiveOfferOnProperty = $receiveOfferOnProperty;
                }

                if (array_key_exists('dateOfOffer', $QAInfoArray)) {
                    $tblQAInfo->dateOfOffer = $dateOfOffer;
                }

                if (array_key_exists('amountOfOffer', $QAInfoArray)) {
                    $tblQAInfo->amountOfOffer = $amountOfOffer;
                }

                if (array_key_exists('RETBillPayer', $QAInfoArray)) {
                    $tblQAInfo->RETBillPayer = $RETBillPayer;
                }

                if (array_key_exists('taxesCurrent', $QAInfoArray)) {
                    $tblQAInfo->taxesCurrent = $taxesCurrent;
                }

                if (array_key_exists('hazardInsurancePolicy', $QAInfoArray)) {
                    $tblQAInfo->hazardInsurancePolicy = $hazardInsurancePolicy;
                }

                if (array_key_exists('policyCurrent', $QAInfoArray)) {
                    $tblQAInfo->policyCurrent = $policyCurrent;
                }

                if (array_key_exists('insuranceCompName', $QAInfoArray)) {
                    $tblQAInfo->insuranceCompName = HTTP::escapeQuoteForPOST($insuranceCompName);
                }

                if (array_key_exists('insuranceCompPhone', $QAInfoArray)) {
                    $tblQAInfo->insuranceCompPhone = HTTP::escapeQuoteForPOST($insuranceCompPhone);
                }

                if (array_key_exists('filedBankruptcy', $QAInfoArray)) {
                    $tblQAInfo->filedBankruptcy = $filedBankruptcy;
                }

                if (array_key_exists('bankruptcyChapter', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyChapter = $bankruptcyChapter;
                }

                if (array_key_exists('bankruptcyFilingDate', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyFilingDate = $bankruptcyFilingDate;
                }

                if (array_key_exists('bankruptcyDischarged', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyDischarged = $bankruptcyDischarged;
                }

                if (array_key_exists('bankruptcyCaseNumb', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyCaseNumb = $bankruptcyCaseNumb;
                }

                if (array_key_exists('creditCounselorName', $QAInfoArray)) {
                    $tblQAInfo->creditCounselorName = HTTP::escapeQuoteForPOST($creditCounselorName);
                }

                if (array_key_exists('creditCounselorPhone', $QAInfoArray)) {
                    $tblQAInfo->creditCounselorPhone = HTTP::escapeQuoteForPOST($creditCounselorPhone);
                }

                if (array_key_exists('creditCounselorEmail', $QAInfoArray)) {
                    $tblQAInfo->creditCounselorEmail = $creditCounselorEmail;
                }

                if (array_key_exists('rentPayment', $QAInfoArray)) {
                    $tblQAInfo->rentPayment = $rentPayment;
                }

                if (array_key_exists('propertyForRent', $QAInfoArray)) {
                    $tblQAInfo->propertyForRent = $propertyForRent;
                }

                if (array_key_exists('monthlyRentAmt', $QAInfoArray)) {
                    $tblQAInfo->monthlyRentAmt = $monthlyRentAmt;
                }

                if (array_key_exists('monthlyLastPaidAmt', $QAInfoArray)) {
                    $tblQAInfo->monthlyLastPaidAmt = $monthlyLastPaidAmt;
                }

                if (array_key_exists('leaseExpiresDate', $QAInfoArray)) {
                    $tblQAInfo->leaseExpiresDate = $leaseExpiresDate;
                }

                if (array_key_exists('isHomeownerServed', $QAInfoArray)) {
                    $tblQAInfo->isHomeownerServed = $isHomeownerServed;
                }

                if (array_key_exists('noOfProperties', $QAInfoArray)) {
                    $tblQAInfo->noOfProperties = $noOfProperties;
                }

                if (array_key_exists('noOfMortgages', $QAInfoArray)) {
                    $tblQAInfo->noOfMortgages = $noOfMortgages;
                }

                if (array_key_exists('bankruptcyDischargeDate', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyDischargeDate = $bankruptcyDischargeDate;
                }

                if (array_key_exists('creditCounselStartedDate', $QAInfoArray)) {
                    $tblQAInfo->creditCounselStartedDate = $creditCounselStartedDate;
                }

                if (array_key_exists('creditCounselFinisheddDate', $QAInfoArray)) {
                    $tblQAInfo->creditCounselFinisheddDate = $creditCounselFinisheddDate;
                }

                if (array_key_exists('borrowerConvicted', $QAInfoArray)) {
                    $tblQAInfo->borrowerConvicted = $borrowerConvicted;
                }

                if (array_key_exists('coBorrowerConvicted', $QAInfoArray)) {
                    $tblQAInfo->coBorrowerConvicted = $coBorrowerConvicted;
                }

                if (array_key_exists('repaymentOrMortAssistance', $QAInfoArray)) {
                    $tblQAInfo->repaymentOrMortAssistance = $repaymentOrMortAssistance;
                }

                if (array_key_exists('delinquentOnPropTax', $QAInfoArray)) {
                    $tblQAInfo->delinquentOnPropTax = $delinquentOnPropTax;
                }

                if (array_key_exists('delinquentTaxYear', $QAInfoArray)) {
                    $tblQAInfo->delinquentTaxYear = $delinquentTaxYear;
                }

                if (array_key_exists('delinquentTaxAmount', $QAInfoArray)) {
                    $tblQAInfo->delinquentTaxAmount = $delinquentTaxAmount;
                }

                if (array_key_exists('delinquencySavedAmount', $QAInfoArray)) {
                    $tblQAInfo->delinquencySavedAmount = $delinquencySavedAmount;
                }

                if (array_key_exists('attorneyFirmName', $QAInfoArray)) {
                    $tblQAInfo->attorneyFirmName = HTTP::escapeQuoteForPOST($attorneyFirmName);
                }

                if (array_key_exists('attorneyName', $QAInfoArray)) {
                    $tblQAInfo->attorneyName = HTTP::escapeQuoteForPOST($attorneyName);
                }

                if (array_key_exists('attorneyPhone', $QAInfoArray)) {
                    $tblQAInfo->attorneyPhone = $attorneyPhone;
                }

                if (array_key_exists('attorneyFax', $QAInfoArray)) {
                    $tblQAInfo->attorneyFax = $attorneyFax;
                }

                if (array_key_exists('attorneyCell', $QAInfoArray)) {
                    $tblQAInfo->attorneyCell = $attorneyCell;
                }

                if (array_key_exists('isPaymentsPlanBehind', $QAInfoArray)) {
                    $tblQAInfo->isPaymentsPlanBehind = $isPaymentsPlanBehind;
                }

                if (array_key_exists('bankruptcyDispositionStatus', $QAInfoArray)) {
                    $tblQAInfo->bankruptcyDispositionStatus = $bankruptcyDispositionStatus;
                }

                if (array_key_exists('doneAppraisal', $QAInfoArray)) {
                    $tblQAInfo->doneAppraisal = $doneAppraisal;
                }

                if (array_key_exists('appraisalListValue', $QAInfoArray)) {
                    $tblQAInfo->appraisalListValue = $appraisalListValue;
                }

                if (array_key_exists('offerPropertyAgencyName', $QAInfoArray)) {
                    $tblQAInfo->offerPropertyAgencyName = HTTP::escapeQuoteForPOST($offerPropertyAgencyName);
                }

                if (array_key_exists('listingPrice', $QAInfoArray)) {
                    $tblQAInfo->listingPrice = $listingPrice;
                }

                if (array_key_exists('propertyListedDate', $QAInfoArray)) {
                    $tblQAInfo->propertyListedDate = $propertyListedDate;
                }

                if (array_key_exists('saleForHowLong', $QAInfoArray)) {
                    $tblQAInfo->saleForHowLong = $saleForHowLong;
                }

                if (array_key_exists('OPAPhone', $QAInfoArray)) {
                    $tblQAInfo->OPAPhone = $OPAPhone;
                }

                if (array_key_exists('attorneyEmail', $QAInfoArray)) {
                    $tblQAInfo->attorneyEmail = $attorneyEmail;
                }

                if (array_key_exists('PublishBInfo', $QAInfoArray)) {
                    $tblQAInfo->PublishBInfo = $PublishBInfo;
                }

                if (array_key_exists('BEthnicity', $QAInfoArray)) {
                    $tblQAInfo->BEthnicity = $BEthnicity;
                }

                if (array_key_exists('BRace', $QAInfoArray)) {
                    $tblQAInfo->BRace = $BRace;
                }

                if (array_key_exists('BGender', $QAInfoArray)) {
                    $tblQAInfo->BGender = $BGender;
                }

                if (array_key_exists('BVeteran', $QAInfoArray)) {
                    $tblQAInfo->BVeteran = $BVeteran;
                }

                if (array_key_exists('bFiEthnicity', $QAInfoArray)) {
                    $tblQAInfo->bFiEthnicity = $bFiEthnicity;
                }

                if (array_key_exists('bFiSex', $QAInfoArray)) {
                    $tblQAInfo->bFiSex = $bFiSex;
                }

                if (array_key_exists('bFiRace', $QAInfoArray)) {
                    $tblQAInfo->bFiRace = $bFiRace;
                }

                if (array_key_exists('bDemoInfo', $QAInfoArray)) {
                    $tblQAInfo->bDemoInfo = $bDemoInfo;
                }

                if (array_key_exists('PublishCBInfo', $QAInfoArray)) {
                    $tblQAInfo->PublishCBInfo = $PublishCBInfo;
                }

                if (array_key_exists('CBEthnicity', $QAInfoArray)) {
                    $tblQAInfo->CBEthnicity = $CBEthnicity;
                }

                if (array_key_exists('CBEthnicitySub', $QAInfoArray)) {
                    $tblQAInfo->CBEthnicitySub = $CBEthnicitySub;
                }

                if (array_key_exists('CBEthnicitySubOther', $QAInfoArray)) {
                    $tblQAInfo->CBEthnicitySubOther = $CBEthnicitySubOther;
                }

                if (array_key_exists('CBRace', $QAInfoArray)) {
                    $tblQAInfo->CBRace = $CBRace;
                }

                if (array_key_exists('CBRaceSub', $QAInfoArray)) {
                    $tblQAInfo->CBRaceSub = $CBRaceSub;
                }

                if (array_key_exists('CBRaceAsianOther', $QAInfoArray)) {
                    $tblQAInfo->CBRaceAsianOther = $CBRaceAsianOther;
                }

                if (array_key_exists('CBRacePacificOther', $QAInfoArray)) {
                    $tblQAInfo->CBRacePacificOther = $CBRacePacificOther;
                }

                if (array_key_exists('CBGender', $QAInfoArray)) {
                    $tblQAInfo->CBGender = $CBGender;
                }

                if (array_key_exists('CBVeteran', $QAInfoArray)) {
                    $tblQAInfo->CBVeteran = $CBVeteran;
                }

                if (array_key_exists('CBFiEthnicity', $QAInfoArray)) {
                    $tblQAInfo->CBFiEthnicity = $CBFiEthnicity;
                }

                if (array_key_exists('CBFiGender', $QAInfoArray)) {
                    $tblQAInfo->CBFiGender = $CBFiGender;
                }

                if (array_key_exists('CBFiRace', $QAInfoArray)) {
                    $tblQAInfo->CBFiRace = $CBFiRace;
                }

                if (array_key_exists('CBDDemoInfo', $QAInfoArray)) {
                    $tblQAInfo->CBDDemoInfo = $CBDDemoInfo;
                }

                if (array_key_exists('HAMPPrincipalResidence', $QAInfoArray)) {
                    $tblQAInfo->HAMPPrincipalResidence = $HAMPPrincipalResidence;
                }

                if (array_key_exists('permanentHAMPModification', $QAInfoArray)) {
                    $tblQAInfo->permanentHAMPModification = $permanentHAMPModification;
                }

                if (array_key_exists('howManyPermanentHAMP', $QAInfoArray)) {
                    $tblQAInfo->howManyPermanentHAMP = $howManyPermanentHAMP;
                }

                if (array_key_exists('HAMPTrialPrincipal', $QAInfoArray)) {
                    $tblQAInfo->HAMPTrialPrincipal = $HAMPTrialPrincipal;
                }

                if (array_key_exists('serviceMember', $QAInfoArray)) {
                    $tblQAInfo->serviceMember = $serviceMember;
                }

                if (array_key_exists('serviceMemberOrder', $QAInfoArray)) {
                    $tblQAInfo->serviceMemberOrder = $serviceMemberOrder;
                }

                if (array_key_exists('serviceMemberSurvivor', $QAInfoArray)) {
                    $tblQAInfo->serviceMemberSurvivor = HTTP::escapeQuoteForPOST($serviceMemberSurvivor);
                }

                if (array_key_exists('creditCounselorAgency', $QAInfoArray)) {
                    $tblQAInfo->creditCounselorAgency = HTTP::escapeQuoteForPOST($creditCounselorAgency);
                }

                if (array_key_exists('attorneyNum', $QAInfoArray)) {
                    $tblQAInfo->attorneyNumber = $attorneyNum;
                }

                if (array_key_exists('policyNumber', $QAInfoArray)) {
                    $tblQAInfo->policyNumber = $policyNumber;
                }

                if (array_key_exists('attorneyAddress', $QAInfoArray)) {
                    $tblQAInfo->attorneyAddress = HTTP::escapeQuoteForPOST($attorneyAddress);
                }

                if (array_key_exists('attorneyCity', $QAInfoArray)) {
                    $tblQAInfo->attorneyCity = HTTP::escapeQuoteForPOST($attorneyCity);
                }

                if (array_key_exists('attorneyState', $QAInfoArray)) {
                    $tblQAInfo->attorneyState = $attorneyState;
                }

                if (array_key_exists('attorneyZip', $QAInfoArray)) {
                    $tblQAInfo->attorneyZip = $attorneyZip;
                }

                if (array_key_exists('jurisDiction', $QAInfoArray)) {
                    $tblQAInfo->jurisDiction = $jurisDiction;
                }

                if (array_key_exists('indexNo', $QAInfoArray)) {
                    $tblQAInfo->indexNo = $indexNo;
                }

                if (array_key_exists('appliedModification', $QAInfoArray)) {
                    $tblQAInfo->appliedModification = $appliedModification;
                }

                if (array_key_exists('appliedModificationDate', $QAInfoArray)) {
                    $tblQAInfo->appliedModificationDate = $appliedModificationDate;
                }

                if (array_key_exists('receivedModificationDate', $QAInfoArray)) {
                    $tblQAInfo->receivedModificationOfferDate = $receivedModificationDate;
                }

                if (array_key_exists('repaymentOrMortAssistanceDate', $QAInfoArray)) {
                    $tblQAInfo->repaymentOrMortAssistanceDate = $repaymentOrMortAssistanceDate;
                }

                if (array_key_exists('borrowerNatural', $QAInfoArray)) {
                    $tblQAInfo->borrowerNatural = $borrowerNatural;
                }

                if (array_key_exists('occupiedBy', $QAInfoArray)) {
                    $tblQAInfo->occupiedBy = $occupiedBy;
                }

                if (array_key_exists('loanModType', $QAInfoArray)) {
                    $tblQAInfo->loanModType = $loanModType;
                }

                if (array_key_exists('HAMPTier1Payment', $QAInfoArray)) {
                    $tblQAInfo->HAMPTier1Payment = $HAMPTier1Payment;
                }

                if (array_key_exists('loanModPlanStanding', $QAInfoArray)) {
                    $tblQAInfo->loanModPlanStanding = $loanModPlanStanding;
                }

                if (array_key_exists('stateHFAEntity', $QAInfoArray)) {
                    $tblQAInfo->stateHFAEntity = $stateHFAEntity;
                }

                if (array_key_exists('stateHFAContactName', $QAInfoArray)) {
                    $tblQAInfo->stateHFAContactName = $stateHFAContactName;
                }

                if (array_key_exists('stateHFAPhone', $QAInfoArray)) {
                    $tblQAInfo->stateHFAPhone = $QAInfoArray;
                }

                if (array_key_exists('stateHFAEmailAddress', $QAInfoArray)) {
                    $tblQAInfo->stateHFAEmailAddress = $stateHFAEmailAddress;
                }

                if (array_key_exists('serviceMemberServing', $QAInfoArray)) {
                    $tblQAInfo->serviceMemberServing = $serviceMemberServing;
                }

                if (array_key_exists('isMtgReaffirmed', $QAInfoArray)) {
                    $tblQAInfo->isMtgReaffirmed = $isMtgReaffirmed;
                }

                if (array_key_exists('isInsuranceDelinquent', $QAInfoArray)) {
                    $tblQAInfo->isInsuranceDelinquent = $isInsuranceDelinquent;
                }

                if (array_key_exists('delinquentInsuranceAmount', $QAInfoArray)) {
                    $tblQAInfo->delinquentInsuranceAmount = $delinquentInsuranceAmount;
                }

                if (array_key_exists('hazardPolicyCurrent', $QAInfoArray)) {
                    $tblQAInfo->hazardPolicyCurrent = $hazardPolicyCurrent;
                }

                if (array_key_exists('leaseStartedDate', $QAInfoArray)) {
                    $tblQAInfo->leaseStartedDate = $leaseStartedDate;
                }

                if (array_key_exists('closingDate', $QAInfoArray)) {
                    $tblQAInfo->closingDate = $closingDate;
                }

                if (array_key_exists('serviceMemberDeceased', $QAInfoArray)) {
                    $tblQAInfo->serviceMemberDeceased = $serviceMemberDeceased;
                }

                if (array_key_exists('noOfvehiclesOwned', $QAInfoArray)) {
                    $tblQAInfo->noOfvehiclesOwned = $noOfvehiclesOwned;
                }

                if (array_key_exists('notOnMortgageNote', $QAInfoArray)) {
                    $tblQAInfo->notOnMortgageNote = $notOnMortgageNote;
                }

                if (array_key_exists('QACoBorSpouseSSN', $QAInfoArray)) {
                    $tblQAInfo->QACoBorSpouseSSN = $QACoBorSpouseSSN;
                }

                if (array_key_exists('isTaxReturn', $QAInfoArray)) {
                    $tblQAInfo->isTaxReturn = $isTaxReturn;
                }

                if (array_key_exists('QABorName', $QAInfoArray)) {
                    $tblQAInfo->QABorName = $QABorName;
                }

                if (array_key_exists('QABorSpouseName', $QAInfoArray)) {
                    $tblQAInfo->QABorSpouseName = $QABorSpouseName;
                }

                if (array_key_exists('QABorSpouseSSN', $QAInfoArray)) {
                    $tblQAInfo->QABorSpouseSSN = $QABorSpouseSSN;
                }

                if (array_key_exists('QACoBorName', $QAInfoArray)) {
                    $tblQAInfo->QACoBorName = $QACoBorName;
                }

                if (array_key_exists('QACoBorSSN', $QAInfoArray)) {
                    $tblQAInfo->QACoBorSSN = $QACoBorSSN;
                }

                if (array_key_exists('QACoBorSpouseName', $QAInfoArray)) {
                    $tblQAInfo->QACoBorSpouseName = $QACoBorSpouseName;
                }

                if (array_key_exists('QABorSSN', $QAInfoArray)) {
                    $tblQAInfo->QABorSSN = $QABorSSN;
                }

                if (array_key_exists('YRF4506TDate1', $QAInfoArray)) {
                    $tblQAInfo->YRF4506TDate1 = $YRF4506TDate1;
                }

                if (array_key_exists('YRF4506TDate2', $QAInfoArray)) {
                    $tblQAInfo->YRF4506TDate2 = $YRF4506TDate2;
                }

                if (array_key_exists('YRF4506TDate3', $QAInfoArray)) {
                    $tblQAInfo->YRF4506TDate3 = $YRF4506TDate3;
                }

                if (array_key_exists('YRF4506TDate4', $QAInfoArray)) {
                    $tblQAInfo->YRF4506TDate4 = $YRF4506TDate4;
                }


                $tblQAInfo->excludeFromHMDAReport = $excludeFromHMDAReport;

                if (array_key_exists('legalEntityIdentifier', $hmdaFormPostData)) {
                    $tblQAInfo->legalEntityIdentifier = $legalEntityIdentifier;
                }

                if (array_key_exists('introductoryRatePeriod', $hmdaFormPostData)) {
                    $tblQAInfo->introductoryRatePeriod = intval($introductoryRatePeriod);
                }

                if (array_key_exists('universalLoanIdentifier', $hmdaFormPostData)) {
                    $tblQAInfo->universalLoanIdentifier = $universalLoanIdentifier;
                }

                if (array_key_exists('HMDALoanPurpose', $hmdaFormPostData)) {
                    $tblQAInfo->HMDALoanPurpose = intval($HMDALoanPurpose);
                }

                if (array_key_exists('HMDALoanAmount', $hmdaFormPostData)) {
                    $tblQAInfo->HMDALoanAmount = $HMDALoanAmount;
                }

                if (array_key_exists('interestOnlyPayment', $hmdaFormPostData)) {
                    $tblQAInfo->interestOnlyPayment = $interestOnlyPayment;
                }

                if (array_key_exists('actionTaken', $hmdaFormPostData)) {
                    $tblQAInfo->actionTaken = $actionTaken;
                }

                if (array_key_exists('propertyValue', $hmdaFormPostData)) {
                    $tblQAInfo->propertyValue = Strings::replaceCommaValues($propertyValue);
                }

                if (array_key_exists('typeOfPurchaser', $hmdaFormPostData)) {
                    $tblQAInfo->typeOfPurchaser = $typeOfPurchaser;
                }

                if (array_key_exists('totalUnits', $hmdaFormPostData)) {
                    $tblQAInfo->totalUnits = $totalUnits;
                }

                if (array_key_exists('reasonForDenial', $hmdaFormPostData)) {
                    $tblQAInfo->reasonForDenial = $reasonForDenial;
                }

                if (array_key_exists('reasonForDenialOther', $hmdaFormPostData)) {
                    $tblQAInfo->reasonForDenialOther = $reasonForDenialOther;
                }

                if (array_key_exists('submissionOfApplicationFrom', $hmdaFormPostData)) {
                    $tblQAInfo->submissionOfApplicationFrom = $submissionOfApplicationFrom;
                }

                if (array_key_exists('combinedLoanToValueRatio', $hmdaFormPostData)) {
                    $tblQAInfo->combinedLoanToValueRatio = $combinedLoanToValueRatio;
                }

                if (array_key_exists('censusTract', $hmdaFormPostData)) {
                    $tblQAInfo->censusTract = $censusTract;
                }

                if (array_key_exists('borrowerRaceOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerRaceOfApplicant = $borrowerRaceOfApplicant;
                }

                if (array_key_exists('coBorrowerRaceOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerRaceOfApplicant = $coBorrowerRaceOfApplicant;
                }

                if (array_key_exists('borrowerAgeOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerAgeOfApplicant = $borrowerAgeOfApplicant;
                }

                if (array_key_exists('coBorrowerAgeOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerAgeOfApplicant = $coBorrowerAgeOfApplicant;
                }

                if (array_key_exists('coBorrowerCreditScoreOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerCreditScoreOfApplicant = $coBorrowerCreditScoreOfApplicant;
                }

                if (array_key_exists('borrowerCreditScoreOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerCreditScoreOfApplicant = $borrowerCreditScoreOfApplicant;
                }

                if (array_key_exists('borrowerCreditScoringModelOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerCreditScoringModelOfApplicant = $borrowerCreditScoringModelOfApplicant;
                }

                if (array_key_exists('coBorrowerCreditScoringModelOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerCreditScoringModelOfApplicant = $coBorrowerCreditScoringModelOfApplicant;
                }

                if (array_key_exists('borrowerCreditScoringModelOfApplicantOther', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerCreditScoringModelOfApplicantOther = $borrowerCreditScoringModelOfApplicantOther;
                }

                if (array_key_exists('coBorrowerCreditScoringModelOfApplicantOther', $hmdaFormPostData)) {
                    $tblQAInfo->coBorrowerCreditScoringModelOfApplicantOther = $coBorrowerCreditScoringModelOfApplicantOther;
                }

                if (array_key_exists('borrowerCreditScoringModelConditionalFreeOfApplicant', $hmdaFormPostData)) {
                    $tblQAInfo->borrowerCreditScoringModelConditionalFreeOfApplicant = $borrowerCreditScoringModelConditionalFreeOfApplicant;
                }

            }
            if (array_key_exists('coBorrowerCreditScoringModelConditionalFreeOfApplicant', $hmdaFormPostData)) {
                $tblQAInfo->coBorrowerCreditScoringModelConditionalFreeOfApplicant = $coBorrowerCreditScoringModelConditionalFreeOfApplicant;
            }
            if (array_key_exists('borEnrolledPrincipalTribe', $hmdaFormPostData)) {
                $tblQAInfo->borEnrolledPrincipalTribe = $borEnrolledPrincipalTribe;
            }
            if (array_key_exists('coBorEnrolledPrincipalTribe', $hmdaFormPostData)) {
                $tblQAInfo->coBorEnrolledPrincipalTribe = $coBorEnrolledPrincipalTribe;
            }

            if (array_key_exists('HMDALoanType', $hmdaFormPostData)) {
                $tblQAInfo->HMDALoanType = $HMDALoanType;
            }
            if (array_key_exists('manufacturedHomeSecuredPropertyType', $hmdaFormPostData)) {
                $tblQAInfo->manufacturedHomeSecuredPropertyType = $manufacturedHomeSecuredPropertyType;
            }
            if (array_key_exists('manufacturedHomeLandPropertyInterest', $hmdaFormPostData)) {
                $tblQAInfo->manufacturedHomeLandPropertyInterest = $manufacturedHomeLandPropertyInterest;
            }
            if (array_key_exists('preapproval', $hmdaFormPostData)) {
                $tblQAInfo->preapproval = $preapproval;
            }
            if (array_key_exists('multifamilyAffordableUnits', $hmdaFormPostData)) {
                $tblQAInfo->multifamilyAffordableUnits = $multifamilyAffordableUnits;
            }
            if (array_key_exists('constructionMethod', $hmdaFormPostData)) {
                $tblQAInfo->constructionMethod = $constructionMethod;
            }
            if (array_key_exists('applicationChannel', $hmdaFormPostData)) {
                $tblQAInfo->applicationChannel = $applicationChannel;
            }
            if (array_key_exists('initiallyPayableToYourInstitution', $hmdaFormPostData)) {
                $tblQAInfo->initiallyPayableToYourInstitution = $initiallyPayableToYourInstitution;
            }
            if (array_key_exists('rateSpread', $hmdaFormPostData)) {
                $tblQAInfo->rateSpread = $rateSpread;
            }
            if (array_key_exists('HOEPAStatus', $hmdaFormPostData)) {
                $tblQAInfo->HOEPAStatus = $HOEPAStatus;
            }
            if (array_key_exists('totalLoanCostOrTotalPointsAndFees', $hmdaFormPostData)) {
                $tblQAInfo->totalLoanCostOrTotalPointsAndFees = $totalLoanCostOrTotalPointsAndFees;
            }
            if (array_key_exists('originationCharges', $hmdaFormPostData)) {
                $tblQAInfo->originationCharges = $originationCharges;
            }
            if (array_key_exists('discountPoints', $hmdaFormPostData)) {
                $tblQAInfo->discountPoints = $discountPoints;
            }
            if (array_key_exists('lenderCredits', $hmdaFormPostData)) {
                $tblQAInfo->lenderCredits = $lenderCredits;
            }
            if (array_key_exists('HMDAPrepaymentPenalty', $hmdaFormPostData)) {
                $tblQAInfo->HMDAPrepaymentPenalty = $HMDAPrepaymentPenalty;
            }
            if (array_key_exists('debtToIncomeRatio', $hmdaFormPostData)) {
                $tblQAInfo->debtToIncomeRatio = $debtToIncomeRatio;
            }
            if (array_key_exists('balloonPayment', $hmdaFormPostData)) {
                $tblQAInfo->balloonPayment = $balloonPayment;
            }
            if (array_key_exists('automatedUnderwritingSystem', $hmdaFormPostData)) {
                $tblQAInfo->automatedUnderwritingSystem = $automatedUnderwritingSystem;
            }
            if (array_key_exists('AUSFreeFormTextFieldForCode5', $hmdaFormPostData)) {
                $tblQAInfo->AUSFreeFormTextFieldForCode5 = $AUSFreeFormTextFieldForCode5;
            }
            if (array_key_exists('automatedUnderwritingSystemResult', $hmdaFormPostData)) {
                $tblQAInfo->automatedUnderwritingSystemResult = $automatedUnderwritingSystemResult;
            }
            if (array_key_exists('AUSResultFreeFormTextFieldForCode16', $hmdaFormPostData)) {
                $tblQAInfo->AUSResultFreeFormTextFieldForCode16 = $AUSResultFreeFormTextFieldForCode16;
            }
            if (array_key_exists('reverseMortgage', $hmdaFormPostData)) {
                $tblQAInfo->reverseMortgage = $reverseMortgage;
            }
            if (array_key_exists('openEndLineOfCredit', $hmdaFormPostData)) {
                $tblQAInfo->openEndLineOfCredit = $openEndLineOfCredit;
            }
            if (array_key_exists('businessOrCommercialPurpose', $hmdaFormPostData)) {
                $tblQAInfo->businessOrCommercialPurpose = $businessOrCommercialPurpose;
            }
            if (array_key_exists('areThereInterestOnlyPayment', $hmdaFormPostData)) {
                $tblQAInfo->areThereInterestOnlyPayment = $areThereInterestOnlyPayment;
            }
            if (array_key_exists('negativeAmortization', $hmdaFormPostData)) {
                $tblQAInfo->negativeAmortization = $negativeAmortization;
            }
            if (array_key_exists('otherNonAmortizingFeatures', $hmdaFormPostData)) {
                $tblQAInfo->otherNonAmortizingFeatures = $otherNonAmortizingFeatures;
            }
            if (array_key_exists('countyCode', $hmdaFormPostData)) {
                $tblQAInfo->countyCode = $countyCode;
            }
            if (array_key_exists('incomeBorrowerCoBorrower', $hmdaFormPostData)) {
                $tblQAInfo->incomeBorrowerCoBorrower = $incomeBorrowerCoBorrower;
            }

            $tblQAInfo->Save();

            $tblFile = tblFile::Get([
                tblFile_db::COLUMN_LMRID => $LMRId,
            ]);

            if ($tabOpt == 'HR' || $tabOpt == 'CON' || $tabOpt == 'LE' || $tabOpt == 'FS' || $tabOpt == 'INT' || $tabOpt == 'CFPB') {
                if ($opt == 'newIntake') {
                    $tblFile->salesDate = $loanSalesDate;
                    $tblFile->mortgageLates = $mortgageLates;
                    $tblFile->Save();
                } elseif ($tabOpt == 'INT') {
                    if (array_key_exists('mortgageLates', $LMRInfo)) {
                        $tblFile->mortgageLates = $mortgageLates;
                        $tblFile->Save();
                    }
                }
            } else {

                $tblFile->receiveNotice = $receiveNotice;
                $tblFile->receiveModification = $receiveModification;
                $tblFile->receiveModificationNotes = HTTP::escapeQuoteForPOST($receiveModificationNotes);


                if (array_key_exists('mortgageLates', $LMRInfo)) {
                    $tblFile->mortgageLates = $mortgageLates;
                }

                if (array_key_exists('checkingAndSavings', $LMRInfo)) {
                    $tblFile->checkingAndSavings = $checkingAndSavings;
                }

                if (array_key_exists('noOfPeopleInProperty', $LMRInfo)) {
                    $tblFile->noOfPeopleInProperty = $noOfPeopleInProperty;
                }

                if (array_key_exists('missedMonthOfMortPay', $LMRInfo)) {
                    $tblFile->missedMonthOfMortPay = $missedMonthOfMortPay;
                }

                $tblFile->Save();


                if (array_key_exists('yearsInProp', $proposalInfo)) {
                    if ($PID > 0) {
                        $tblProposalInfo = tblProposalInfo::Get([
                            tblProposalInfo_db::COLUMN_LMRID => $LMRId,
                            tblProposalInfo_db::COLUMN_PID   => $PID,
                        ]);

                        $tblProposalInfo->yearsInProp = trim($proposalInfo['yearsInProp']);
                        $tblProposalInfo->Save();

                    } else {
                        $tblProposalInfo = new tblProposalInfo();
                        $tblProposalInfo->LMRId = $LMRId;
                        $tblProposalInfo->yearsInProp = trim($proposalInfo['yearsInProp']);
                        $tblProposalInfo->Save();
                    }
                }

                $propertyListedDate = '';
                if (array_key_exists('propertyListedDate', $shortSaleInfo)) {
                    $propertyListedDate = trim($shortSaleInfo['propertyListedDate']);
                }
                if (Dates::IsEmpty($propertyListedDate)) {
                    $propertyListedDate = null;
                } else {
                    $propertyListedDate = Dates::formatDateWithRE($propertyListedDate, 'MDY', 'Y-m-d');
                }
                if ((array_key_exists('listingPrice', $shortSaleInfo)
                    || array_key_exists('propertyListedDate', $shortSaleInfo)
                    || array_key_exists('OPAPhone', $shortSaleInfo)
                    || array_key_exists('offerPropertyAgentName', $shortSaleInfo)
                    || array_key_exists('offerPropertyAgencyName', $shortSaleInfo))
                ) {
                    $tblShortSale = $SSID ? tblShortSale::Get([
                        tblShortSale_db::COLUMN_SSID => $SSID,
                    ]) : new tblShortSale();

                    $tblShortSale->LMRId = $LMRId;

                    if ($SSID > 0) {

                        if (array_key_exists('listingPrice', $shortSaleInfo)) {
                            $tblShortSale->listingPrice = $shortSaleInfo['listingPrice'];
                        }

                        if (array_key_exists('propertyListedDate', $shortSaleInfo)) {
                            $tblShortSale->listingDate = $propertyListedDate;
                        }

                        if (array_key_exists('OPAPhone', $shortSaleInfo)) {
                            $tblShortSale->realtorPhoneNumber = $shortSaleInfo['OPAPhone'];
                        }

                    } else {

                        if (array_key_exists('listingPrice', $shortSaleInfo)) {
                            $tblShortSale->listingPrice = HTTP::escapeQuoteForPOST($shortSaleInfo['listingPrice']);
                        }

                        if (array_key_exists('propertyListedDate', $shortSaleInfo)) {
                            $tblShortSale->listingDate = $propertyListedDate;
                        }

                        if (array_key_exists('OPAPhone', $shortSaleInfo)) {
                            $tblShortSale->realtorPhoneNumber = trim($shortSaleInfo['OPAPhone']);
                        }

                    }
                    if (array_key_exists('offerPropertyAgentName', $shortSaleInfo)) {
                        $tblShortSale->realtor = HTTP::escapeQuoteForPOST($shortSaleInfo['offerPropertyAgentName']);
                    }
                    if (array_key_exists('offerPropertyAgencyName', $shortSaleInfo)) {
                        $tblShortSale->agency = HTTP::escapeQuoteForPOST($shortSaleInfo['offerPropertyAgencyName']);
                    }

                    $tblShortSale->Save();
                }
            }

            if ($tabOpt == 'FS') {
                $propertyListedDate = '';
                if (array_key_exists('propertyListedDate', $shortSaleInfo)) $propertyListedDate = trim($shortSaleInfo['propertyListedDate']);
                if (Dates::IsEmpty($propertyListedDate)) {
                    $propertyListedDate = null;
                } else {
                    $propertyListedDate = Dates::formatDateWithRE($propertyListedDate, 'MDY', 'Y-m-d');
                }

                $tblShortSale = $SSID ? tblShortSale::Get([
                    tblShortSale_db::COLUMN_SSID => $SSID,
                ]) : new tblShortSale();

                $tblShortSale->LMRId = $LMRId;

                $tblShortSale->listingPrice = HTTP::escapeQuoteForPOST($shortSaleInfo['listingPrice']);

                if (array_key_exists('propertyListedDate', $shortSaleInfo)) {
                    $tblShortSale->listingDate = $propertyListedDate;
                }

                if (array_key_exists('OPAPhone', $shortSaleInfo)) {
                    $tblShortSale->realtorPhoneNumber = $shortSaleInfo['OPAPhone'];
                }

                if (array_key_exists('offerPropertyAgentName', $shortSaleInfo)) {
                    $tblShortSale->realtor = HTTP::escapeQuoteForPOST($shortSaleInfo['offerPropertyAgentName']);
                }

                if (array_key_exists('offerPropertyAgencyName', $shortSaleInfo)) {
                    $tblShortSale->agency = HTTP::escapeQuoteForPOST($shortSaleInfo['offerPropertyAgencyName']);
                }

                $tblShortSale->Save();

            }

            if (array_key_exists('PCID', $ip)) {
                $PCID = trim($ip['PCID']);
            }
            if (array_key_exists('UID', $ip)) {
                $UID = trim($ip['UID']);
            }
            if (array_key_exists('UGroup', $ip)) {
                $UGroup = trim($ip['UGroup']);
            }

            if (!Dates::IsEmpty($loanSalesDate)) {
                saveFileNotesWhenSaleDateChange::getReport([
                    'loanSalesDate' => $loanSalesDate,
                    'PCID'          => $PCID,
                    'LMRId'         => $LMRId,
                    'UID'           => $UID,
                    'UGroup'        => $UGroup,
                ]);
            }
        }

        //HMDA Multiple values save
        $tblQAInfo = tblQAInfo::Get(['LMRId' => $LMRId]);
        if (!$tblQAInfo) {
            $tblQAInfo = new tblQAInfo();
            $tblQAInfo->LMRId = $LMRId;
        }
        foreach (HMDAController::$ethnicityRaceData as $column) {
            HMDAController::setBorrowerEthnicityRaceData($column, $tblQAInfo);
        }
        $tblQAInfo->Save();

        SP_RecordFileTabUpdate::getReport(
            'U',
            'QA Info',
            Dates::Timestamp(),
            $ip['PCID'],
            $LMRId,
            $ip['UID'],
            $ip['UGroup'],
            1
        );
        return $tblQAInfo->QAID;
    }
}
