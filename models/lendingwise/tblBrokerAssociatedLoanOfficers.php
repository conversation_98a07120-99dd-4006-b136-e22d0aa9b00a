<?php

namespace models\lendingwise;

use models\Database2;
use models\lendingwise\db\tblBrokerAssociatedLoanOfficers_db;
use models\standard\Dates;
use models\standard\Strings;

/**
 *
 */
class tblBrokerAssociatedLoanOfficers extends tblBrokerAssociatedLoanOfficers_db
{
    public static function getData(int $AID,int $lonOfficerId = 0): ?array
    {
        $sql = '
        select * from (
    SELECT bls.ID,
          bls.AID ,
          bls.loanOfficerId
        FROM
        tblBrokerAssociatedLoanOfficers as bls 
        WHERE  bls.AID = :AID ';
        if($lonOfficerId > 0){
            $sql .= ' AND bls.loanOfficerId = :loanOfficerId ';
        }
        $sql .= '
        UNION
        
        SELECT bl.ID,
          bl.brokerId AID ,
          bl.loanofficerId as loanOfficerId
        FROM
        tblBrokerLoanofficer as bl 
        WHERE  bl.brokerId = :AID ';
        if($lonOfficerId > 0){
            $sql .= ' AND bl.loanofficerId = :loanOfficerId ';
        }
        $sql .= '
        ) as AssignedLOdata order by loanOfficerId
        ';

        return self::queryData($sql, [
            'AID' => $AID,
            'loanOfficerId' => $lonOfficerId
        ]);
    }

    public function saveData($loanOfficerArray, $brokerNumber): int
    {
        $cnt = 0;
        $qryDel = 'DELETE FROM  tblBrokerAssociatedLoanOfficers WHERE AID = :AID  ;';
        $res = self::executeQuery($qryDel, ['AID' => $brokerNumber]);
        $cnt = $res['affected_rows'];
        $qry = ' 
                delete from 
                tblBrokerLoanofficer  
                where brokerId = :brokerId
            ';
        self::executeQuery($qry, [
            'brokerId' => $brokerNumber]);
        $sqlParams = [];
        $qryIns = ' INSERT INTO tblBrokerAssociatedLoanOfficers ( AID, 
        loanOfficerId,createdBy,createdOn
        ) VALUES (
        :AID , 
        :loanOfficerId,
        :createdBy,
                  :createdOn
        ) ';
        for ($f = 0; $f < count($loanOfficerArray); $f++) {
            if ($loanOfficerArray[$f]) {

                $sqlParams[] = [
                    'AID' => $brokerNumber,
                    'loanOfficerId' => $loanOfficerArray[$f],
                    'createdBy' => Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null,
                    'createdOn' => Dates::Timestamp() ?? null,

                ];
            }
        }
        if (count($loanOfficerArray) > 0) {
            $cnt = self::insertMulti($qryIns, $sqlParams);
        }
        return $cnt;
    }
}