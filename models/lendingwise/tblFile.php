<?php

namespace models\lendingwise;

use models\Controllers\backoffice\LMRequest;
use models\cypher;
use DateTime;
use Exception;
use models\composite\LoanServicing;
use models\constants\accrualTypes;
use models\lendingwise\db\collateral_db;
use models\lendingwise\db\estimatedProjectCost_db;
use models\lendingwise\db\tblACHInfo_db;
use models\lendingwise\db\tblAssetsInfo_db;
use models\lendingwise\db\tblBorrowerAlternateNames_db;
use models\lendingwise\db\tblEquipmentInformation_db;
use models\lendingwise\db\tblFeeSchedule_db;
use models\lendingwise\db\tblFile2_db;
use models\lendingwise\db\tblFile_db;
use models\lendingwise\db\tblFileCalculatedValues_db;
use models\lendingwise\db\tblFileHMLO_db;
use models\lendingwise\db\tblFileHMLOBackGround_db;
use models\lendingwise\db\tblFileHMLOBusinessEntity_db;
use models\lendingwise\db\tblFileHMLOExperience_db;
use models\lendingwise\db\tblFileHMLONewLoanInfo_db;
use models\lendingwise\db\tblFileHMLOPropInfo_db;
use models\lendingwise\db\tblFileHUDBasicInfo_db;
use models\lendingwise\db\tblFileLoanOrigination_db;
use models\lendingwise\db\tblFileLOAssetsInfo_db;
use models\lendingwise\db\tblFileLOExplanation_db;
use models\lendingwise\db\tblFileLOLiabilitiesInfo_db;
use models\lendingwise\db\tblFileLOScheduleRealInfo_db;
use models\lendingwise\db\tblFilePropertyInfo_db;
use models\lendingwise\db\tblFileResponse_db;
use models\lendingwise\db\tblFileSBAQuestions_db;
use models\lendingwise\db\tblFileServicing_db;
use models\lendingwise\db\tblFileStreetData_db;
use models\lendingwise\db\tblFundingClosing_db;
use models\lendingwise\db\tblHomeReport_db;
use models\lendingwise\db\tblIncomeInfo_db;
use models\lendingwise\db\tblLMRCCInfo_db;
use models\lendingwise\db\tblLoanOriginatorInfo_db;
use models\lendingwise\db\tblLoanSetting_db;
use models\lendingwise\db\tblProcessingCompany_db;
use models\lendingwise\db\tblProperties_db;
use models\lendingwise\db\tblPropertiesCharacteristics_db;
use models\lendingwise\db\tblPropertyManagement_db;
use models\lendingwise\db\tblProposalInfo_db;
use models\lendingwise\db\tblPropSellerInfo_db;
use models\lendingwise\db\tblQAInfo_db;
use models\lendingwise\db\tblRemoteUrl_db;
use models\lendingwise\db\tblSalesMethod_db;
use models\lendingwise\db\tblShortSale_db;
use models\loan_math\per_diem;
use models\myFileInfo\FileHUD;
use models\oFile\ActiveInvestors;
use models\servicing\LoanTerms;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;
use models\tblFile\Accountant;
use models\tblFile\Attorney;
use models\tblFile\Escrow;
use models\tblFile\FinancialAdvisor;
use models\tblFile\getTitleAttorney;
use models\tblFile\HOA1Contact;
use models\tblFile\HOA2Contact;
use models\tblFile\InsuranceRep;
use models\tblFile\Lender;
use models\tblFile\PM;
use models\tblFile\Servicer;
use models\tblFile\TitleRep;
use models\tblFile\Trustee;

/**
 *
 */
class tblFile extends tblFile_db
{
    const COLUMN_HUDLOANTYPE = 'HUDLoanType';

    /**
     * @var ActiveInvestors[]
     */
    private ?array $_ActiveInvestors = null;
    private ?LoanServicing $LoanServicing = null;

    public function getNewLinkedTable(string $tableName)
    {
        return $this->getLinkedTable($tableName, true);
    }

    public function getLinkedTable(string $tableName, bool $create = false)
    {
        if(substr($tableName, 0, 4) == 'HUD_') {
            return $this->FileHUD()->$tableName;
        }

        switch ($tableName) {
            case tblFile_db::TABLE:
                return $this;

            case tblProcessingCompany_db::TABLE:
                $res = $this->getTblProcessingCompany_by_FPCID();
                if ($create && !$res) {
                    $this->_tblProcessingCompany_by_FPCID = new tblProcessingCompany();
                    $this->_tblProcessingCompany_by_FPCID->PCID = $this->FPCID;
                    return $this->_tblProcessingCompany_by_FPCID;
                }
                return $res;


            case tblFileHUDBasicInfo_db::TABLE:
                $res = $this->getTblFileHUDBasicInfo_by_LMRID();
                if ($create && !$res) {
                    $this->_tblFileHUDBasicInfo_by_LMRID = new tblFileHUDBasicInfo();
                    $this->_tblFileHUDBasicInfo_by_LMRID->LMRID = $this->LMRId;
                    return $this->_tblFileHUDBasicInfo_by_LMRID;
                }
                return $res;

            case tblFundingClosing_db::TABLE:
                $res = $this->getTblFundingClosing_by_LMRId();
                if ($create && !$res) {
                    $this->_tblFundingClosing_by_LMRId = new tblFundingClosing();
                    $this->_tblFundingClosing_by_LMRId->LMRId = $this->LMRId;
                    return $this->_tblFundingClosing_by_LMRId;
                }
                return $res;

            case tblLMRCCInfo_db::TABLE:
                $res = $this->getTblLMRCCInfo_by_LMRID();
                if ($create && !$res) {
                    $this->_tblLMRCCInfo_by_LMRID = new tblLMRCCInfo();
                    $this->_tblLMRCCInfo_by_LMRID->LMRID = $this->LMRId;
                    return $this->_tblLMRCCInfo_by_LMRID;
                }
                return $res;

            case tblACHInfo_db::TABLE:
                $res = $this->getTblACHInfo_by_LMRID();
                if ($create && !$res) {
                    $this->_tblACHInfo_by_LMRID = new tblACHInfo();
                    $this->_tblACHInfo_by_LMRID->LMRID = $this->LMRId;
                    return $this->_tblACHInfo_by_LMRID;
                }
                return $res;

            case tblRemoteUrl_db::TABLE:
                $res = $this->getTblRemoteUrl_by_LMRId()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblRemoteUrl_by_LMRId[0] = new tblRemoteUrl();
                    $this->_tblRemoteUrl_by_LMRId[0]->LMRId = $this->LMRId;
                    return $this->_tblRemoteUrl_by_LMRId[0];
                }
                return $res;

            case tblFileLOExplanation_db::TABLE:
                $res = $this->getTblFileLOExplanation_by_fileID()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblFileLOExplanation_by_fileID[0] = new tblFileLOExplanation();
                    $this->_tblFileLOExplanation_by_fileID[0]->fileID = $this->LMRId;
                    return $this->_tblFileLOExplanation_by_fileID[0];
                }
                return $res;


            case tblProposalInfo_db::TABLE:
                $res = $this->getTblProposalInfo_by_LMRId()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblProposalInfo_by_LMRId[0] = new tblProposalInfo();
                    $this->_tblProposalInfo_by_LMRId[0]->LMRId = $this->LMRId;
                    return $this->_tblProposalInfo_by_LMRId[0];
                }
                return $res;

            case 'Broker':
                $res = tblAgent::Get(['userNumber' => $this->brokerNumber]);
                if ($create && !$res) {
                    $res = new tblAgent();
                }
                return $res;

            case 'SecondaryBroker': // Loan Officer
                $res = tblAgent::Get(['userNumber' => $this->secondaryBrokerNumber]);
                if ($create && !$res) {
                    $res = new tblAgent();
                }
                return $res;

            case collateral_db::TABLE:
                $res = $this->getCollateral_by_lmrid()[0] ?? null;
                if ($create && !$res) {
                    $this->_collateral_by_lmrid[0] = new collateral();
                    $this->_collateral_by_lmrid[0]->lmrid = $this->LMRId;
                    return $this->_collateral_by_lmrid[0];
                }
                return $res;

            case tblPropertyManagement_db::TABLE:
                $res = $this->getTblPropertyManagement_by_LMRId()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblPropertyManagement_by_LMRId[0] = new tblShortSale();
                    $this->_tblPropertyManagement_by_LMRId[0]->LMRId = $this->LMRId;
                    return $this->_tblPropertyManagement_by_LMRId[0];
                }
                return $res;

            case estimatedProjectCost_db::TABLE:
                $res = $this->getEstimatedProjectCost_by_lmrid()[0] ?? null;
                if ($create && !$res) {
                    $this->_estimatedProjectCost_by_lmrid[0] = new tblShortSale();
                    $this->_estimatedProjectCost_by_lmrid[0]->LMRId = $this->LMRId;
                    return $this->_estimatedProjectCost_by_lmrid[0];
                }
                return $res;

            case tblPropSellerInfo_db::TABLE:
                $res = $this->getTblPropSellerInfo_by_LMRId()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblPropSellerInfo_by_LMRId[0] = new tblPropSellerInfo();
                    $this->_tblPropSellerInfo_by_LMRId[0]->LMRId = $this->LMRId;
                    return $this->_tblPropSellerInfo_by_LMRId[0];
                }
                return $res;

            case tblShortSale_db::TABLE:
                $res = $this->getTblShortSale_by_LMRId();
                if ($create && !$res) {
                    $this->_tblShortSale_by_LMRId = new tblShortSale();
                    $this->_tblShortSale_by_LMRId->LMRId = $this->LMRId;
                    return $this->_tblShortSale_by_LMRId;
                }
                return $res;

            case tblFileLOAssetsInfo_db::TABLE:
                $res = $this->getTblFileLOAssetsInfo_by_fileID();
                if ($create && !$res) {
                    $this->_tblFileLOAssetsInfo_by_fileID = new tblFileLOAssetsInfo();
                    $this->_tblFileLOAssetsInfo_by_fileID->fileID = $this->LMRId;
                    return $this->_tblFileLOAssetsInfo_by_fileID;
                }
                return $res;

            case tblFileSBAQuestions_db::TABLE:
                $res = $this->getTblFileSBAQuestions_by_fileID();
                if ($create && !$res) {
                    $this->_tblFileSBAQuestions_by_fileID = new tblFileSBAQuestions();
                    $this->_tblFileSBAQuestions_by_fileID->fileID = $this->LMRId;
                    return $this->_tblFileSBAQuestions_by_fileID;
                }
                return $res;

            case tblFileHMLOExperience_db::TABLE:
                $res = $this->getTblFileHMLOExperience_by_fileID();
                if ($create && !$res) {
                    $this->_tblFileHMLOExperience_by_fileID = new tblFileHMLOExperience();
                    $this->_tblFileHMLOExperience_by_fileID->fileID = $this->LMRId;
                    return $this->_tblFileHMLOExperience_by_fileID;
                }
                return $res;

            case tblEquipmentInformation_db::TABLE:
                $res = $this->getTblEquipmentInformation_by_LMRID()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblEquipmentInformation_by_LMRID[0] = new tblEquipmentInformation();
                    $this->_tblEquipmentInformation_by_LMRID[0]->LMRID = $this->LMRId;
                    return $this->_tblEquipmentInformation_by_LMRID[0];
                }
                return $res;

            case tblFeeSchedule_db::TABLE:
                $res = $this->getTblFeeSchedule_by_LMRID()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblFeeSchedule_by_LMRID[0] = new tblFeeSchedule();
                    $this->_tblFeeSchedule_by_LMRID[0]->LMRID = $this->LMRId;
                    return $this->_tblFeeSchedule_by_LMRID[0];
                }
                return $res;

            case tblSalesMethod_db::TABLE:
                $res = $this->getTblSalesMethod_by_LMRID()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblSalesMethod_by_LMRID[0] = new tblSalesMethod();
                    $this->_tblSalesMethod_by_LMRID[0]->LMRID = $this->LMRId;
                    return $this->_tblSalesMethod_by_LMRID[0];
                }
                return $res;

            case tblFileLoanOrigination_db::TABLE:
                $res = $this->getTblFileLoanOrigination_by_fileID();
                if ($create && !$res) {
                    $this->_tblFileLoanOrigination_by_fileID = new tblFileLoanOrigination();
                    $this->_tblFileLoanOrigination_by_fileID->fileID = $this->LMRId;
                    return $this->_tblFileLoanOrigination_by_fileID;
                }
                return $res;

            case tblFileStreetData_db::TABLE:
                $res = $this->getTblFileStreetData_by_LMRID();
                if ($create && !$res) {
                    $this->_tblFileStreetData_by_LMRID = new tblFileStreetData();
                    $this->_tblFileStreetData_by_LMRID->LMRID = $this->LMRId;
                    return $this->_tblFileStreetData_by_LMRID;
                }
                return $res;

            case tblFileServicing_db::TABLE:
                $res = $this->getTblFileServicing_by_LMRId();
                if ($create && !$res) {
                    $this->_tblFileServicing_by_LMRId = new tblFileServicing();
                    $this->_tblFileServicing_by_LMRId->LMRId = $this->LMRId;
                    return $this->_tblFileServicing_by_LMRId;
                }
                return $res;

            case tblFile2_db::TABLE:
                $res = $this->getTblFile2_by_LMRID();
                if ($create && !$res) {
                    $this->_tblFile2_by_LMRID = new tblFile2();
                    $this->_tblFile2_by_LMRID->LMRID = $this->LMRId;
                    return $this->_tblFile2_by_LMRID;
                }
                return $res;

            case tblQAInfo_db::TABLE:
                $res = $this->getTblQAInfo_by_LMRId()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblQAInfo_by_LMRId = [];
                    $this->_tblQAInfo_by_LMRId[0] = new tblQAInfo();
                    $this->_tblQAInfo_by_LMRId[0]->LMRId = $this->LMRId;
                }
                return $this->getTblQAInfo_by_LMRId()[0] ?? null;

            case tblIncomeInfo_db::TABLE:
                return $this->getTblIncomeInfo_by_LMRId();

            case tblFileHMLONewLoanInfo_db::TABLE:
                $res = $this->getTblFileHMLONewLoanInfo_by_fileID();
                if ($create && !$res) {
                    $this->_tblFileHMLONewLoanInfo_by_fileID = new tblFileHMLONewLoanInfo();
                    $this->_tblFileHMLONewLoanInfo_by_fileID->fileID = $this->LMRId;
                }
                return $this->getTblFileHMLONewLoanInfo_by_fileID();

            case tblFileResponse_db::TABLE:
                $res = $this->getTblFileResponse_by_LMRId();
                if ($create && !$res) {
                    $this->_tblFileResponse_by_LMRId = new tblFileResponse();
                    $this->_tblFileResponse_by_LMRId->LMRId = $this->LMRId;
                }
                return $this->getTblFileResponse_by_LMRId();

            case tblFileHMLOPropInfo_db::TABLE:
                $res = $this->getTblFileHMLOPropInfo_by_fileID();
                if ($create && !$res) {
                    $this->_tblFileHMLOPropInfo_by_fileID = new tblFileHMLOPropInfo();
                    $this->_tblFileHMLOPropInfo_by_fileID->fileID = $this->LMRId;
                }
                return $this->getTblFileHMLOPropInfo_by_fileID();

            case tblFileCalculatedValues_db::TABLE:
                return $this->getTblFileCalculatedValues_by_LMRId();

            case tblHomeReport_db::TABLE:
                return $this->getTblHomeReport_by_fileID()[0] ?? null;

            case tblBorrowerAlternateNames_db::TABLE:
                return $this->getTblBorrowerAlternateNames_by_LMRID()[0] ?? null;

            case tblFileHMLO_db::TABLE:
                return $this->getTblFileHMLO_by_fileID()[0] ?? null;

            case tblFileLOScheduleRealInfo_db::TABLE:
                return $this->getTblFileLOScheduleRealInfo_by_fileID()[0] ?? null;

            case tblFileLOLiabilitiesInfo_db::TABLE:
                return $this->getTblFileLOLiabilitiesInfo_by_fileID()[0] ?? null;

            case tblFileHMLOBackGround_db::TABLE:
                return $this->getTblFileHMLOBackGround_by_fileID();

            case 'Lender':
                return $this->getLender();

            case 'Servicer':
                return $this->getServicer();

            case 'Trustee':
                return $this->getTrustee();

            case 'propertyManagement':
                return $this->getPropertyManagement();

            case 'HOA1Contact':
                return $this->getHOA1Contact();

            case 'HOA2Contact':
                return $this->getHOA2Contact();

            case 'titleAttorney':
                return $this->getTitleAttorney();

            case 'Attorney':
                return $this->getAttorney();

            case 'Escrow':
                return $this->getEscrow();

            case 'FinancialAdvisor':
                return $this->getFinancialAdvisor();

            case 'Accountant':
                return $this->getAccountant();

            case 'InsuranceRep':
                return $this->getInsuranceRep();

            case tblFileHMLOBusinessEntity_db::TABLE:
                $res = $this->getTblFileHMLOBusinessEntity_by_fileID();
                if ($create && !$res) {
                    $this->_tblFileHMLOBusinessEntity_by_fileID = new tblFileHMLOBusinessEntity();
                    $this->_tblFileHMLOBusinessEntity_by_fileID->fileID = $this->LMRId;
                    return $this->_tblFileHMLOBusinessEntity_by_fileID;
                }
                return $res;

            case tblLoanOriginatorInfo_db::TABLE:
                $res = $this->getTblLoanOriginatorInfo_by_LMRID()[0] ?? null;
                if ($create && !$res) {
                    $this->_tblLoanOriginatorInfo_by_LMRID = [
                        new tblLoanOriginatorInfo(),
                    ];
                    $this->_tblLoanOriginatorInfo_by_LMRID[0]->LMRID = $this->LMRId;
                }
                return $this->getTblLoanOriginatorInfo_by_LMRID()[0] ?? null;

            case tblProperties_db::TABLE:
                return $this->getPrimaryProperty();

            case tblPropertiesCharacteristics_db::TABLE:
                if (!$this->getPrimaryProperty()) {
                    return null;
                }
                return $this->getPrimaryProperty()->getTblPropertiesCharacteristics_by_propertyId();

            case tblAssetsInfo_db::TABLE:
                return $this->getTblAssetsInfo_by_LMRID()[0] ?? null;

            case tblFilePropertyInfo_db::TABLE:
                return $this->getTblFilePropertyInfo_by_LMRId();

            case tblLoanSetting_db::TABLE:
                if (is_null($this->getTblLoanSetting_by_LMRId())) {
                    $this->_tblLoanSetting_by_LMRId = new tblLoanSetting();
                    $this->_tblLoanSetting_by_LMRId->LMRId = $this->LMRId;
                }
                return $this->getTblLoanSetting_by_LMRId();

        }
        Debug('Unknown: ' . $tableName);
        return null;
    }

    public function Save(): array
    {
        $this->borrowerLoanRate = Strings::replaceCommaValues($this->borrowerLoanRate);
        $this->brokerNumber = $this->brokerNumber ?: null;

        return parent::Save();
    }

    /**
     * @return LoanServicing
     */
    public function getLoanServicing(): LoanServicing
    {
        if (is_null($this->LoanServicing)) {
            $this->LoanServicing = new LoanServicing($this->LMRId, $this);

        }
        return $this->LoanServicing;
    }

    /**
     * @return int|string|null
     */
    public function getPublicLoanNumber()
    {
        // tblFileHMLOPropInfo
        if ($this->getTblFileHMLOPropInfo_by_fileID() ?? null) {
            if ($this->getTblFileHMLOPropInfo_by_fileID()->servicingNumber) {
                return $this->getTblFileHMLOPropInfo_by_fileID()->servicingNumber;
            }
        }
        if ($this->loanNumber) {
            return $this->loanNumber;
        }
        if ($this->loanNumber2) {
            return $this->loanNumber2;
        }
        if ($this->loanNumber3) {
            return $this->loanNumber3;
        }
        return $this->LMRId;
    }

    /**
     * @return string|null
     */
    public function interestChargedFromDate(): ?string
    {
        $date = $this->getTblFileHMLONewLoanInfo_by_fileID()->interestChargedFromDate;
        return !Dates::IsEmpty($date) ? $date : null;
    }

    /**
     * @return string|null
     */
    public function interestChargedEndDate(): ?string
    {
        $date = $this->getTblFileHMLONewLoanInfo_by_fileID()->interestChargedEndDate;
        return !Dates::IsEmpty($date) ? $date : null;
    }

    private ?per_diem $_perDiem = null;

    public function getPerDiem(): ?per_diem
    {
        if(is_null($this->_perDiem)) {
            $this->_perDiem = per_diem::calc(
                $this->interestChargedFromDate(),
                $this->interestChargedEndDate(),
                $this->getTblFileHMLOPropInfo_by_fileID()->accrualType,
                $this->getTblFileHMLONewLoanInfo_by_fileID()->perDiemAccrualType,
                $this->getTblFileHMLONewLoanInfo_by_fileID()->isLoanPaymentAmt ?: 'TLA',
                $this->getTblFileCalculatedValues_by_LMRId()->CurrentLoanBalance,
                $this->getTotalLoanAmount(),
                $this->getInterestRate($this->interestChargedFromDate()),
                !$this->getTblProcessingCompany_by_PCID()->nonInclusivePerDiem
            );
        }
        return $this->_perDiem;
    }

   /**
     * @return float
     */
    public function getMonthlyPayment(): float
    {
        // from the origination side, not calculated, only use in loan info section
        return round(floatval(str_replace(',', '', $this->lien1Payment)), 2);
    }

    /**
     * @param string|null $paymentDate
     * @return float
     */
    public function getInterestRate(?string $paymentDate): float
    {
        $sql = '
            SELECT
                interest_rate
                FROM tblFileInterestRateHistory
WHERE LMRId = :LMRId
AND apply_date <= :paymentDate
ORDER BY apply_date DESC
LIMIT 1
        ';
        $res = self::queryData($sql, [
            'LMRId'       => $this->LMRId,
            'paymentDate' => Dates::Datestamp($paymentDate),
        ]);
        $interestRate = floatval($res[0]['interest_rate'] ?? 0);
        if (!$interestRate) {
            return (float)$this->lien1Rate;
        }
        return $interestRate;
    }

    /**
     * @return string|null
     */
    public function getAmortizationType(): ?string
    {
        return $this->getTblFileHMLONewLoanInfo_by_fileID()->amortizationType;
    }

    /**
     * @return string|null
     */
    public function getAmortization(): ?string
    {
        // $this->lien1Terms = null;
        return $this->lien1Terms;
    }

    /**
     * @return tblPayment[]
     */
    public function getPayments(): array
    {
        if (is_null($this->_tblPayment_by_LMRId)) {
            $this->_tblPayment_by_LMRId = $this->getLinkedRecords(
                'models\lendingwise',
                'tblPayment', [
                'LMRId' => $this->LMRId,
            ], 'paymentDate ASC');
        }
        return $this->_tblPayment_by_LMRId;
    }

    /* @var tblBudgetAndDraws[] */
    private ?array $_draws = null;

    /**
     * @return tblBudgetAndDraws[]|null
     */
    public function getDraws(): ?array
    {
        if (is_null($this->_draws)) {
            $this->_draws = [];
            foreach ($this->getTblBudgetAndDraws_by_LMRId() as $item) {
                if (!Dates::IsEmpty($item->dateFunded)) {
                    $this->_draws[] = $item;
                }
            }
            usort($this->_draws, function (tblBudgetAndDraws $a, tblBudgetAndDraws $b) {
                $date_a = strtotime($a->dateFunded);
                $date_b = strtotime($b->dateFunded);
                return $date_a > $date_b;
            });
        }
        return $this->_draws;
    }

    /**
     * @return null
     */
    public function getPaymentMethod(): ?string
    {
        return $this->getTblFileHMLONewLoanInfo_by_fileID()->isLoanPaymentAmt;
    }

    /**
     * @return null
     */
    public function getLoanTerm(): ?string
    {
        return $this->getTblFileHMLOPropInfo_by_fileID()->loanTerm;
    }

    /**
     * @return float
     */
    public function getOriginationBalance(): float
    {
        return (float)$this->getTblFileHMLONewLoanInfo_by_fileID()->totalLoanAmount
            + (float)$this->getTblFileHMLONewLoanInfo_by_fileID()->closingCostFinanced;
    }

    /**
     * @return float|null
     */
    public function getRehabCostFinanced(): ?float
    {
        $orig = floatval(
                $this->getTblFileHMLONewLoanInfo_by_fileID()->rehabCost)
            * floatval($this->getTblFileHMLONewLoanInfo_by_fileID()->rehabCostPercentageFinanced
            ) / 100.0;

        $new = tblFileServicingHistory::GetForTableColumn('tblFileHMLONewLoanInfo', 'rehabCost', $this->LMRId);
        return $new ?: $orig;
    }

    /**
     * @return float|null
     */
    public function getTotalLoanAmount(): ?float
    {
        return $this->getTblFileCalculatedValues_by_LMRId()->TotalLoanAmount;
    }

    /**
     * @return float
     */
    public function getClosingCostsFinanced(): float
    {
        return floatval(str_replace(',', '', $this->getTblFileHMLONewLoanInfo_by_fileID()->closingCostFinanced));
    }

    /**
     * @return float
     */
    public function getAnnualPropertyTaxes(): float
    {
        return floatval(str_replace(',', '', $this->getTblIncomeInfo_by_LMRId()->taxes1));
    }

    /**
     * @return string
     */
    public function getAnnualPropertyTaxesPaidDate(): ?string
    {
        return $this->getTblIncomeInfo_by_LMRId() && $this->getTblIncomeInfo_by_LMRId()->taxes1LastPaid ? Dates::Datestamp($this->getTblIncomeInfo_by_LMRId()->taxes1LastPaid) : null;
    }

    /**
     * @return float
     */
    public function getAnnualPropertyTaxesPaidAmount(): float
    {
        return floatval(str_replace(',', '', $this->getTblIncomeInfo_by_LMRId()->taxes1LastAmount));
    }

    /**
     * @return float
     */
    public function getAnnualInsurancePremium(): float
    {
        return floatval(str_replace(',', '', $this->getTblFileHMLOPropInfo_by_fileID()->annualPremium));
    }

    /**
     * @return string
     */
    public function getAnnualInsurancePremiumLastPaidDate(): ?string
    {
        return Dates::Datestamp($this->getTblFileHMLOPropInfo_by_fileID()->annualPremiumLastPaid, '');
    }

    /**
     * @return float
     */
    public function getAnnualInsurancePremiumLastAmount(): float
    {
        return floatval(str_replace(',', '', $this->getTblFileHMLOPropInfo_by_fileID()->annualPremiumLastAmount));
    }

    /**
     * @return float
     */
    public function getPrepaidInterestReserve(): float
    {
        return floatval(str_replace(',', '', $this->getTblFileHMLONewLoanInfo_by_fileID()->prepaidInterestReserve));
    }

    /**
     * @return float
     */
    public function getPrepaidEscrow(): float
    {
        return floatval(str_replace(',', '', $this->getTblFileHMLONewLoanInfo_by_fileID()->prepaidEscrow));
    }

    /**
     * @return float
     */
    public function getEscrowCushion(): float
    {
        return floatval(str_replace(',', '', $this->getTblFileHMLONewLoanInfo_by_fileID()->escrowCushion));
    }

    private ?string $_firstPaymentDueDate = null;

    /**
     * @return string|null
     */
    public function getFirstPaymentDueDate(): ?string
    {
        if (is_null($this->_firstPaymentDueDate)) {
            $this->_firstPaymentDueDate = $this->getTblFileResponse_by_LMRId()->trialPaymentDate1;
        }
        return $this->_firstPaymentDueDate;
    }

    private $_fundingDate = null;

    /**
     * @return null
     */
    public function getFundingDate()
    {
        if (is_null($this->_fundingDate)) {
            $this->_fundingDate = $this->getTblFileHMLOPropInfo_by_fileID()->fundingDate;
        }
        return $this->_fundingDate;
    }

    private ?string $_actualCloseDate = null;

    /**
     * @return null
     */
    public function getActualCloseDate(): ?string
    {
        if (is_null($this->_actualCloseDate)) {
            $this->_actualCloseDate = $this->getTblQAInfo_by_LMRId()[0]->closingDate;
        }
        return $this->_actualCloseDate;
    }

    private ?string $_accrualType = null;

    /**
     * @return int
     */
    public function getAccrualDays(): int
    {
        return accrualTypes::getYearDays($this->getAccrualType());
    }

    /**
     * @return string|null
     */
    public function getAccrualTypeName(): ?string
    {
        return accrualTypes::$accrualTypes[$this->getAccrualType()] ?? $this->getAccrualType();
    }

    /**
     * @return string|null
     */
    public function getAccrualType(): ?string
    {
        if (is_null($this->_accrualType)) {
            $this->_accrualType = $this->getTblFileHMLOPropInfo_by_fileID()->accrualType;
        }
        return $this->_accrualType;
    }

    private ?string $_maturityDate = null;

    /**
     * @return string|null
     */
    public function getMaturityDate(): ?string
    {
        if (!Dates::IsEmpty($this->getTblFileHMLOPropInfo_by_fileID()->maturityDate)) {
            return $this->getTblFileHMLOPropInfo_by_fileID()->maturityDate;
        }

        if (is_null($this->_maturityDate)) {
            $loanTerm = $this->getTblFileHMLOPropInfo_by_fileID()->loanTerm;
            if ($loanTerm) {
                $pattern = '/(\d+) Months/i';
                $matches = [];
                preg_match($pattern, $loanTerm, $matches);
                $months = $matches[1] ?? null;
                if ($months) {
                    $this->_maturityDate = Dates::Datestamp(strtotime('+' . ($months - 1) . ' months', strtotime($this->getFirstPaymentDueDate())));
                }
            } else {
                $this->_maturityDate = $this->getTblFileHMLOPropInfo_by_fileID()->maturityDate;
            }
        }
        return $this->_maturityDate;
    }

    private ?float $_defaultInterestRate = null;

    /**
     * @return float
     */
    public function getDefaultInterestRate(): float
    {
        if (is_null($this->_defaultInterestRate)) {
            $this->_defaultInterestRate = floatval($this->getTblFileHMLOPropInfo_by_fileID()->defaultInterestRate);
        }
        return $this->_defaultInterestRate;
    }

    private ?int $_triggeredByDays = null;

    /**
     * @return int
     */
    public function getDefaultTriggeredByDays(): int
    {
        if (is_null($this->_triggeredByDays)) {
            $this->_triggeredByDays = intval($this->getTblFileHMLOPropInfo_by_fileID()->triggeredByDays);
        }
        return $this->_triggeredByDays;
    }

    private ?int $_latePaymentAppliedOn = null;

    /**
     * @return int
     */
    public function getLatePaymentAppliedOn(): int
    {
        if (is_null($this->_latePaymentAppliedOn)) {
            $this->_latePaymentAppliedOn = intval($this->getTblFileHMLOPropInfo_by_fileID()->latePayemntAppliedOn);
        }
        return $this->_latePaymentAppliedOn;
    }

    private ?float $_lateChargePercent = null;

    /**
     * @return float
     */
    public function getLateChargePercent(): float
    {
        if (is_null($this->_lateChargePercent)) {
            $this->_lateChargePercent = floatval($this->getTblFileHMLOPropInfo_by_fileID()->lateChargeAmt);
        }
        return $this->_lateChargePercent;
    }

    private ?float $_lateChargeMinAmount = null;

    /**
     * @return float
     */
    public function getLateChargeMinAmount(): float
    {
        if (is_null($this->_lateChargeMinAmount)) {
            $this->_lateChargeMinAmount = floatval($this->getTblFileHMLOPropInfo_by_fileID()->minLateFeeAmt);
        }
        return $this->_lateChargeMinAmount;
    }

    /**
     * @throws Exception
     */
    public function calculateRemainingMonths()
    {
        $loanOriginationDate = $this->getTblFileResponse_by_LMRId()->trialPaymentDate1;
        $terms = $this->lien1Terms;

        $termsInMonths = 0;
        $remainingMonths = '';
        $crntDate = Dates::Datestamp();

        $loanOriginationDate = trim($loanOriginationDate);
        $term = trim($terms);

        switch ($term) {
            case LoanTerms::INTEREST_ONLY:
                $termsInMonths = 12;
                break;
            case 'Remaining Months':
                break;
            default:
                $pos = strpos($term, '/');
                if (($pos == 0) || ($pos == -1)) {
                    $pos = strpos($term, ' ');
                }
                $terms = substr($term, 0, $pos);
                if ($terms <= 0) {
                    $terms = 1;
                }
                if ($terms > 0) {
                    $termsInMonths = $terms * 12;
                }
        }

        if (!Dates::IsEmpty($loanOriginationDate)) {
            $date1 = new DateTime($loanOriginationDate);
            $date2 = new DateTime($crntDate);
            $interval = date_diff($date1, $date2);
            if ($termsInMonths > 0) {
                $remainingMonths = $termsInMonths - (($interval->y * 12) + $interval->m);
            }
        }
        return $remainingMonths;
    }

    /**
     * @return float
     */
    public function getOriginalBalance(): float
    {
        // verified 2022-03-25
        $principalPayDown = $funDraw = 0;

        $rehabCostFinanced = $this->getTblFileHMLONewLoanInfo_by_fileID()->rehabCostFinanced;
        if (!$rehabCostFinanced && $this->getTblFileHMLONewLoanInfo_by_fileID()->rehabCost) {
            $rehabCostFinanced = round($this->getTblFileHMLONewLoanInfo_by_fileID()->rehabCost * $this->getTblFileHMLONewLoanInfo_by_fileID()->rehabCostPercentageFinanced / 100.0, 2);
        }


        $initLAmt = $this->getTblFileHMLONewLoanInfo_by_fileID()->totalLoanAmount - (
                $rehabCostFinanced + // given, not calculated
                $this->getTblFileHMLONewLoanInfo_by_fileID()->closingCostFinanced +
                $this->getTblFileHMLONewLoanInfo_by_fileID()->prepaidInterestReserve
            );

        $prePIR = floatval($this->getTblFileHMLONewLoanInfo_by_fileID()->prepaidInterestReserve);
        $closingCost = $this->getClosingCostsFinanced(); // need financed closing costs as part of original balance

        return $initLAmt + $prePIR + $closingCost + $funDraw - $principalPayDown;
    }

    private $_InvestorInfo = null;

    /**
     * @param int $InID
     * @return mixed|tblInvestorInfo
     */
    public function getInvestorInfo(int $InID)
    {
        if (!isset($this->_InvestorInfo[$InID])) {
            $item = tblInvestorInfo::Get([
                'InID'  => $InID,
                'LMRId' => $this->LMRId,
            ]);
            $this->_InvestorInfo[$InID] = $item ?? new tblInvestorInfo();
        }
        return $this->_InvestorInfo[$InID];
    }

    /**
     * @return string|null
     */
    public function isTaxesInsuranceEscrowed(): ?string
    {
        return $this->getTblFileHMLONewLoanInfo_by_fileID()->isTaxesInsEscrowed;
    }

    /**
     * @return array|tblPrincipalPayDown[]
     */
    public function getTblPrincipalPayDown_by_LMRID(): array
    {
        if (is_null($this->_tblPrincipalPayDown_by_LMRID)) {
            $this->_tblPrincipalPayDown_by_LMRID = $this->getLinkedRecords(
                'models\lendingwise',
                'tblPrincipalPayDown', [
                'LMRID' => $this->LMRId,
            ], 'principalPayDownDate ASC');
        }
        return $this->_tblPrincipalPayDown_by_LMRID;
    }

    /**
     * @return string
     */
    public function getPaymentMethodDesc(): string
    {
        $basedOn = $this->getPaymentMethod();
        $str = $basedOn . ': ';
        switch ($basedOn) {
            case LoanTerms::ILA:
                $str .= 'Current Loan Balance - Non-Dutch';
                break;
            case LoanTerms::TLA:
                $str .= 'Total Loan Amount - Dutch';
                break;
            case 'SMP':
                $str .= 'Manual Payment Amount Set';
                break;
            default:
                $str .= 'Unknown';
        }
        return $str;
    }

    /**
     * @param string|null $PaymentDate
     * @return float
     */
    public function getFundingInterest(string $PaymentDate = null): float
    {
        return $this->getPerDiem()->totalEstPerDiem
            - $this->getPerDiemPaid($PaymentDate);
    }

    /**
     * @param string|null $PaymentDate
     * @return float
     */
    public function getPerDiemPaid(string $PaymentDate = null): float
    {
        return $this->getLoanServicing()->getPerDiemPaid($PaymentDate);
    }

    public function getFractionalPayments(?string $PaymentDueDate): float
    {
        $sql = '
            SELECT
                SUM(fractionalPenny) AS fractionalPenny
            FROM tblPayment
            WHERE LMRId = :LMRId
                AND dueDate < :dueDate
        ';
        $res = self::queryData($sql, [
            'LMRId'   => $this->LMRId,
            'dueDate' => Dates::Datestamp($PaymentDueDate),
        ]);
        return $res[0]['fractionalPenny'] ?? 0.0;
    }

    private ?tblBranch $tblBranch_by_FBRID = null;

    /**
     * @return tblBranch|null
     */
    public function getTblBranch_by_FBRID(): tblBranch
    {
        if (is_null($this->tblBranch_by_FBRID)) {
            if (!$this->LMRId) {
                $this->tblBranch_by_FBRID = new tblBranch();
            } else {
                $this->tblBranch_by_FBRID = $this->getLinkedRecord(
                    'models\lendingwise',
                    'tblBranch', [
                    'executiveId' => $this->FBRID,
                ]);
            }
        }
        return $this->tblBranch_by_FBRID;
    }

    private ?tblAgent $tblFileAgent_by_secondaryBrokerNumber = null;

    public function getTblFileAgent_by_secondaryBrokerNumber(): tblAgent
    {
        if (is_null($this->tblFileAgent_by_secondaryBrokerNumber)) {
            if (!$this->LMRId || !$this->secondaryBrokerNumber) {
                $this->tblFileAgent_by_secondaryBrokerNumber = new tblAgent();
            } else {
                $this->tblFileAgent_by_secondaryBrokerNumber = $this->getLinkedRecord(
                    'models\lendingwise',
                    'tblAgent', [
                    'userNumber' => $this->secondaryBrokerNumber,
                ]);
            }
        }
        return $this->tblFileAgent_by_secondaryBrokerNumber;
    }

    /**
     * @return tblOffers[]
     */
    public function getTblOffers_by_LMRId(): array
    {
        // SELECT * FROM tblOffers WHERE activeStatus = 1 AND LMRId = :LMRId ORDER  BY id DESC;

        if (is_null($this->_tblOffers_by_LMRId)) {
            if (!$this->LMRId) {
                $this->_tblOffers_by_LMRId = [];
            } else {
                $this->_tblOffers_by_LMRId = $this->getLinkedRecords(
                    'models\lendingwise',
                    'tblOffers', [
                    'LMRId'        => $this->LMRId,
                    'activeStatus' => 1,
                ], 'id DESC');
            }
        }
        return $this->_tblOffers_by_LMRId;
    }

    // SELECT * FROM tblOfferDocs WHERE activeStatus = 1 AND LMRId = :LMRId

    /**
     * @return tblOfferDocs[]
     */
    public function getTblOfferDocs_by_LMRId(): array
    {
        // SELECT * FROM tblOffers WHERE activeStatus = 1 AND LMRId = :LMRId ORDER  BY id DESC;

        if (is_null($this->_tblOfferDocs_by_LMRId)) {
            if (!$this->LMRId) {
                $this->_tblOfferDocs_by_LMRId = [];
            } else {
                $this->_tblOfferDocs_by_LMRId = $this->getLinkedRecords(
                    'models\lendingwise',
                    'tblOfferDocs', [
                    'LMRId'        => $this->LMRId,
                    'activeStatus' => 1,
                ], 'id DESC');
            }
        }
        return $this->_tblOfferDocs_by_LMRId;
    }

    /**
     * @return ActiveInvestors[]|null
     */
    public function ActiveInvestors(): ?array
    {
        if (is_null($this->_ActiveInvestors) && $this->LMRId) {
            $this->_ActiveInvestors = ActiveInvestors::getForLMRId($this->LMRId);
        }

        return $this->_ActiveInvestors;
    }

    public function getPrimaryProperty(): ?tblProperties
    {
        return tblProperties::Get([
            'LMRId'     => $this->LMRId,
            'isPrimary' => 1,
        ]);
    }

    public function publicURL(string $tab, bool $origination): string
    {
        if(
            !$this->FBRID
            || !$this->LMRId
            || !$this->getTblFileResponse_by_LMRId()
        ) {
            return '';
        }

        return self::GetURL(
            $this->FBRID,
            $this->LMRId,
            $this->getTblFileResponse_by_LMRId()->LMRResponseId,
            $tab,
            $origination
        );
    }

    public static function GetURL(
        int    $FBRID,
        int    $LMRId,
        int    $LMRResponseId,
        string $tab = null,
        ?bool $origination = false

    ): string
    {
        // pipeline - http://dev.theloanpost.com/backoffice/LMRequest.php?eId=4f6b2eb66a9101aa&lId=94007a8414c3864c&rId=682cab3767ea4083&op=a72f9e967052513d
        // this -     http://dev.theloanpost.com/backoffice/LMRequest.php?eId=4f6b2eb66a9101aa&lId=5ab23d1c6fe7ba2c&rId=4de699ec2e453cdb&op=a72f9e967052513d&tabOpt=SER2
        $eId = cypher::myEncryption($FBRID);
        $lId = cypher::myEncryption($LMRId);
        $rId = cypher::myEncryption($LMRResponseId);
        $op = cypher::myEncryption('edit');

        if($origination) {
            return '/backoffice/LMRequest.php?eId=' . $eId . '&lId=' . $lId . '&rId=' . $rId . '&op=' . $op . '&tabOpt=' . $tab;
        }

        return '/backoffice/loan/' . $tab . '?eId=' . $eId . '&lId=' . $lId . '&rId=' . $rId . '&op=' . $op;
    }

    private ?tblContacts $_Lender = null;

    public function getLender(): ?tblContacts
    {
        if (is_null($this->_Lender) && $this->LMRId) {
            $this->_Lender = Lender::getReport($this->LMRId);
        }
        return $this->_Lender;
    }

    private ?tblContacts $_Servicer = null;

    public function getServicer(): ?tblContacts
    {
        if (is_null($this->_Servicer) && $this->LMRId) {
            $this->_Servicer = Servicer::getReport($this->LMRId);
        }
        return $this->_Servicer;
    }

    private ?tblContacts $_Trustee = null;

    public function getTrustee(): ?tblContacts
    {
        if (is_null($this->_Trustee) && $this->LMRId) {
            $this->_Trustee = Trustee::getReport($this->LMRId);
        }
        return $this->_Trustee;
    }

    private ?tblContacts $getPropertyManagement = null;

    public function getPropertyManagement(): ?tblContacts
    {
        if (is_null($this->getPropertyManagement) && $this->LMRId) {
            $this->getPropertyManagement = PM::getReport($this->LMRId);
        }
        return $this->getPropertyManagement;
    }

    private ?tblContacts $getHOA1Contact = null;

    public function getHOA1Contact(): ?tblContacts
    {
        if (is_null($this->getHOA1Contact) && $this->LMRId) {
            $this->getHOA1Contact = HOA1Contact::getReport($this->LMRId);
        }
        return $this->getHOA1Contact;
    }

    private ?tblContacts $getHOA2Contact = null;

    public function getHOA2Contact(): ?tblContacts
    {
        if (is_null($this->getHOA2Contact) && $this->LMRId) {
            $this->getHOA2Contact = HOA2Contact::getReport($this->LMRId);
        }
        return $this->getHOA2Contact;
    }

    /* @var tblContacts[] */
    private ?array $getTitleReps = null;

    /**
     * @return tblContacts[]
     */
    public function getTitleReps(): array
    {
        if (is_null($this->getTitleReps) && $this->LMRId) {
            $this->getTitleReps = TitleRep::getReport($this->LMRId);
        }
        return $this->getTitleReps ?? [];
    }

    /* @var tblContacts[] */
    private ?array $getInsuranceRep = null;

    /**
     * @return tblContacts[]
     */
    public function getInsuranceRep(): array
    {
        if (is_null($this->getInsuranceRep) && $this->LMRId) {
            $this->getInsuranceRep = InsuranceRep::getReport($this->LMRId);
        }
        return $this->getInsuranceRep ?? [];
    }


    /* @var tblContacts[] */
    private ?array $getAccountant = null;

    /**
     * @return tblContacts[]
     */
    public function getAccountant(): array
    {
        if (is_null($this->getAccountant) && $this->LMRId) {
            $this->getAccountant = Accountant::getReport($this->LMRId);
        }
        return $this->getAccountant ?? [];
    }

    /* @var tblContacts[] */
    private ?array $getFinancialAdvisor = null;

    /**
     * @return tblContacts[]
     */
    public function getFinancialAdvisor(): array
    {
        if (is_null($this->getFinancialAdvisor) && $this->LMRId) {
            $this->getFinancialAdvisor = FinancialAdvisor::getReport($this->LMRId);
        }
        return $this->getFinancialAdvisor ?? [];
    }

    /* @var tblContacts[] */
    private ?array $getEscrow = null;

    /**
     * @return tblContacts[]
     */
    public function getEscrow(): array
    {
        if (is_null($this->getEscrow) && $this->LMRId) {
            $this->getEscrow = Escrow::getReport($this->LMRId);
        }
        return $this->getEscrow ?? [];
    }

    /* @var tblContacts[] */
    private ?array $getAttorney = null;

    /**
     * @return tblContacts[]
     */
    public function getAttorney(): array
    {
        if (is_null($this->getAttorney) && $this->LMRId) {
            $this->getAttorney = Attorney::getReport($this->LMRId);
        }
        return $this->getAttorney ?? [];
    }

    private ?tblContacts $getTitleAttorney = null;

    public function getTitleAttorney(): ?tblContacts
    {
        if (is_null($this->getTitleAttorney) && $this->LMRId) {
            $this->getTitleAttorney = getTitleAttorney::getReport($this->LMRId);
        }
        return $this->getTitleAttorney;
    }

    public function noOfDaysBehind1()
    {
        $inDBArray['lastPaymentMade'] = $this->lien1LPMade;
        return Integers::calculateNoOfDaysBehind($inDBArray);
    }

    public function noOfDaysBehind2()
    {
        $inDBArray['lastPaymentMade'] = $this->lien2LPMade;
        return Integers::calculateNoOfDaysBehind($inDBArray);
    }

    public function totalPayment(): float
    {
        LMRequest::setLMRId($this->LMRId);
        $lien1Payment = $this->lien1Payment;
        $taxes1 = LMRequest::File()->getTblIncomeInfo_by_LMRId()->taxes1;
        $insurance1 = LMRequest::File()->getTblIncomeInfo_by_LMRId()->insurance1;
        $HOAFees1 = LMRequest::File()->getTblIncomeInfo_by_LMRId()->HOAFees1;
        $mortgageInsurance1 = LMRequest::File()->getTblIncomeInfo_by_LMRId()->mortgageInsurance1;

        return calculateTotalPayment($lien1Payment, $taxes1, $insurance1, $mortgageInsurance1, $HOAFees1);
    }

    private ?array $_HUDLoanType = null;

    public function HUDLoanType(): array
    {
        if(is_null($this->_HUDLoanType)) {
            $this->_HUDLoanType = [];
            foreach($this->getTblFileHUDType_by_LMRID() as $item) {
                $this->_HUDLoanType[] = $item->loanType;
            }
        }
        return $this->_HUDLoanType;
    }

    private ?FileHUD $_FileHUD = null;

    public function FileHUD(): ?FileHUD
    {
        if(is_null($this->_FileHUD)) {
            $this->_FileHUD = FileHUD::getReport($this->LMRId);
        }
        return $this->_FileHUD;
    }

    private ?tblClient $tblClient_by_clientId = null;

    public function getTblFileClients_by_clientId(): tblClient
    {
        if (is_null($this->tblClient_by_clientId)) {
            if (!$this->LMRId || !$this->clientId) {
                $this->tblClient_by_clientId = new tblClient();
            } else {
                $this->tblClient_by_clientId = $this->getLinkedRecord(
                    'models\lendingwise',
                    'tblClient', [
                    'CID' => $this->clientId,
                ]);
            }
        }
        return $this->tblClient_by_clientId;
    }
}
