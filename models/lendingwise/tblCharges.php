<?php

namespace models\lendingwise;

use models\Database2;
use models\composite\LoanAmortization;
use models\lendingwise\db\tblCharges_db;
use models\standard\Dates;
use PhpOffice\PhpSpreadsheet\Calculation\Financial\Amortization;

/**
 *
 */
class tblCharges extends tblCharges_db
{
    public ?float $_owed = null;

    /**
     * @param int $tblPaymentId
     * @param string $description
     * @return tblCharges
     */
    public static function getForPaymentDescription(int $tblPaymentId, string $description): tblCharges
    {
        if(!$tblPaymentId) {
            return new self();
        }

        $sql = '
        SELECT
            *
        FROM
            tblCharges
        WHERE 
            tblPaymentId = :tblPaymentId
            AND description = :description
        ';
        $res = self::queryData($sql, [
            'tblPaymentId' => $tblPaymentId,
            'description' => $description,
        ]);
        $charge = new self();
        $charge->fromData($res[0] ?? []);
        return $charge;
    }

    /**
     * @param int $tblChargeId
     * @param int $LMRId
     * @return tblLedger[]
     */
    public static function getHistoryForChargeId(int $tblChargeId, int $LMRId): ?array
    {
        if(!$tblChargeId) {
            return null;
        }

        $sql = '
        SELECT
            *
        FROM
            tblLedger
        WHERE 
            (credit_source_id = :source_id
            OR debit_source_id = :source_id)
          AND (
              credit_source_name = :source
              OR debit_source_name = :source
          )
        AND LMRId = :LMRId
        ORDER BY 
            ledgerId
        ';
        return self::queryData($sql, [
            'source_id' => $tblChargeId,
            'LMRId' => $LMRId,
            'source' => LoanAmortization::LEDGER_CHARGES,
        ], function ($row) {
            $t = new tblLedger();
            $t->fromData($row);
            return $t;
        });
    }
}